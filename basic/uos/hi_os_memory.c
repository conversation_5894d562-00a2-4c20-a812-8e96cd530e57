/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_os_memory.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_08_02
  最近修改   :

******************************************************************************/
#include <sys/sysinfo.h>
#include "securec.h"
#include "hi_errno.h"
#include "hi_typedef.h"
#include "os/hi_os_fileio.h"
#include "os/hi_os_string.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

hi_void* hi_os_malloc( const hi_uint32 ui_size )
{
    if(ui_size > 0)
    {
        return malloc( ui_size );
    }
    else
    {
        return NULL;
    }
}

hi_void* hi_os_calloc( const hi_uint32 ui_size )
{
	return (ui_size != 0) ? calloc(ui_size, 1) : NULL;
}

hi_void* hi_os_realloc( hi_void *pv_buf, hi_uint32 ui_size)
{
    if(ui_size > 0)
    {
        return realloc(pv_buf, ui_size);
    }
    else
    {
        return NULL;
    }
}

hi_void hi_os_free( hi_void *pv_buf)
{
    free(pv_buf);
}

hi_void* hi_os_memset( hi_void *pv_toset, hi_uchar8 uc_setchar, hi_uint32 ui_count )
{
    return memset(pv_toset, uc_setchar, ui_count);
}

hi_int32 hi_os_memcmp( const hi_void *pv_buf1, const hi_void *pv_buf2, hi_uint32 ui_count)
{
    return memcmp(pv_buf1, pv_buf2, ui_count);
}

hi_void* hi_os_memmove(hi_void *pv_dest, const hi_void *pv_src, hi_uint32 ui_n)
{
    return memmove(pv_dest, pv_src, ui_n);
}

hi_void* hi_os_memcpy( hi_void *pv_buf1, const hi_void *pv_buf2, hi_uint32 ui_count)
{
    return memcpy(pv_buf1, pv_buf2, ui_count);
}

hi_void* hi_os_memset_s( hi_void *pv_toset, hi_uint32 ui_tosetmax, hi_uchar8 uc_setchar, hi_uint32 ui_count )
{
    return (hi_void*)memset_s(pv_toset, ui_tosetmax, uc_setchar, ui_count);
}

hi_void* hi_os_memcpy_s( hi_void *pv_buf1, hi_uint32 ui_buf1max, const hi_void *pv_buf2, hi_uint32 ui_count)
{
    return (hi_void*)memcpy_s(pv_buf1, ui_buf1max, pv_buf2, ui_count);
}

hi_void* hi_os_memmove_s(hi_void *pv_dest, hi_uint32 ui_destmax, const hi_void *pv_src, hi_uint32 ui_n)
{
    return (hi_void*)memmove_s(pv_dest, ui_destmax, pv_src, ui_n);
}
/*****************************************************************
***函数: hi_os_get_total_mem
***输入: pui_mem
***输出: pui_mem 系统的Memory总大小：单位kB
*****************************************************************/
hi_int32 hi_os_get_total_mem(hi_uint32 *pui_mem)
{
	hi_int32 iRet = HI_RET_SUCC;;
	struct sysinfo s_info;
	iRet = sysinfo(&s_info);
	if(!iRet)
	{
		*pui_mem = s_info.totalram/1024;
	}
	return iRet;
}

hi_int32 hi_os_memsize(hi_uint32 *pui_total, hi_uint32 *pui_free)
{
#define PROC_MEMINFO "/proc/meminfo"
    HI_FILE_S  *pf = NULL;
    hi_char8 readBuf[256];
    hi_char8 *pos, *pRem;
    hi_int32  totoMem, availMem;

    pf = hi_os_fopen(PROC_MEMINFO, "r");
    if (NULL == pf) {
        return -1;
    }
    if (0 > hi_os_fread(readBuf, 1, sizeof(readBuf)-1, pf)) //lint !e568 !e685
	{
        hi_os_fclose(pf);
        return -1;
    }
    hi_os_fclose(pf);

    pos = hi_os_strstr(readBuf, "MemTotal:");
    if (NULL == pos) {
        return -1;
    }
    pos += hi_os_strlen("MemTotal:");
    totoMem = hi_os_strtol(pos, &pRem, 0);
    if (0 == totoMem) {
        return -1;
    }
    pos = hi_os_strstr(pos, "MemAvailable:");
    if (NULL == pos) {
        return -1;
    }
    pos += hi_os_strlen("MemAvailable:");
    availMem = hi_os_strtol(pos, &pRem, 0);

    *pui_total = totoMem;
    *pui_free = availMem;
    return 0;
}

#define PROC_STAT_RSS_PAGES_IDX 24
#define PROC_RSS_PAGE_IN_KB 4

hi_int32 hi_os_getmem_pid(hi_int32 i_pid, hi_int32 *pi_mem)
{
    int i = PROC_STAT_RSS_PAGES_IDX;
    hi_char8 ac_name[128];
    hi_char8 ac_buf[1280] = {0};
    HI_FILE_S *fp = NULL;
    hi_char8 *pos, *sep ;

    sprintf_s(ac_name, sizeof(ac_name)-1, "/proc/%d/stat", i_pid);
    if (NULL == (fp = hi_os_fopen(ac_name, "r")))
	{
        return -1;
    }
    if (0 > hi_os_fread(ac_buf, 1, sizeof(ac_buf)-1, fp)) //lint !e568 !e685
	{
        hi_os_fclose(fp);
        return -1;
    }
    hi_os_fclose(fp);
    pos = ac_buf;
    while (i && (sep = hi_os_strsep(&pos, " ")) != NULL)
        i--;

    if (sep != NULL)
        *pi_mem = hi_os_strtol(sep, NULL, 0) * PROC_RSS_PAGE_IN_KB;
    else
        *pi_mem = 0;

    return 0;
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
