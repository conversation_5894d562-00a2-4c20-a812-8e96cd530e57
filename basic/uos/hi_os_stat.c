/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_os_stat.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_08_03
  最近修改   :

******************************************************************************/
#include <sys/stat.h>
#include "securec.h"
#include "hi_typedef.h"
#include "hi_errno.h"
#include "os/hi_os_stat.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

hi_int32 hi_os_stat(const hi_char8 *pc_filename, hi_os_fstat_s *pst_buf)
{
    hi_int32 i_ret;
    hi_os_fstat_s st_temp_stat;

    if ( ( NULL == pc_filename ) || ( NULL == pst_buf ))
    {
        return HI_RET_NULLPTR;
    }

    i_ret = stat(pc_filename,(hi_void*)&st_temp_stat);
    if( HI_RET_SUCC != i_ret )
    {
        return i_ret;
    }

    (void)memcpy_s(pst_buf, sizeof(*pst_buf), &st_temp_stat, sizeof(hi_os_fstat_s) );

    return i_ret;
}

hi_int32 hi_os_fstat(const hi_int32 i_fd, hi_os_fstat_s *pst_buf)
{
    hi_int32 i_ret;
    struct stat st_temp_stat;

    if ( NULL == pst_buf )
    {
        return HI_RET_NULLPTR;
    }

    i_ret = fstat(i_fd, &st_temp_stat);
    if( HI_RET_SUCC != i_ret )
    {
        return i_ret;
    }

    pst_buf->st_dev = (hi_uint32)st_temp_stat.st_dev;
    pst_buf->st_ino = (hi_uint32)st_temp_stat.st_ino;
    pst_buf->st_mode = (hi_ushort16)st_temp_stat.st_mode;
    pst_buf->st_nlink = (hi_ushort16)st_temp_stat.st_nlink;
    pst_buf->st_uid = (hi_ushort16)st_temp_stat.st_uid;
    pst_buf->st_gid = (hi_ushort16)st_temp_stat.st_gid;
    pst_buf->st_rdev = (hi_uint32)st_temp_stat.st_rdev;
    pst_buf->st_size = (hi_uint32)st_temp_stat.st_size;
    pst_buf->st_blksize = (hi_uint32)st_temp_stat.st_blksize;
    pst_buf->st_blocks = (hi_uint32)st_temp_stat.st_blocks;

    return i_ret;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
