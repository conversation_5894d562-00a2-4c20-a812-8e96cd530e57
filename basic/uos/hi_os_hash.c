/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_os_hash.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_07_21
  最近修改   :

******************************************************************************/
#include "securec.h"
#include "hi_errno.h"
#include "os/hi_os_hash.h"
#include "os/hi_os_err.h"
#include "os/hi_os_mem.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

/* output: ui_value / pv_object */
hi_uint32 hi_os_hash_find( hi_os_hash_head_s *pst_hashtable,
                               hi_void *pv_keyobject, hi_void **pv_dataobject )
{
    hi_uint32   ui_ret;
    hi_uint32   ui_itemindx;
    hi_list_head *pst_list_pos    = NULL;
    hi_list_head *pst_list_next   = NULL;
    hi_os_hash_item_s *pst_hashitem = NULL;
    hi_os_hash_item_s *pst_conflictitem = NULL;

    if ( ( NULL == pst_hashtable )
        || ( NULL == pst_hashtable->pv_matchcallback )
        || ( NULL == pst_hashtable->pv_comparecallback )
        || ( NULL == pv_dataobject ) )
    {
        return HI_RET_NULLPTR;
    }

    /*calc key index*/
    ui_itemindx = ((hi_os_hash_matchcallback)pst_hashtable->pv_matchcallback)( pv_keyobject );/*lint !e611*/
    if ( ui_itemindx >= pst_hashtable->ui_hashsize )
    {
        return HI_RET_ITEM_EXCEPT;
    }

    /*get first ptr*/
    pst_hashitem = (hi_os_hash_item_s*)pst_hashtable->pv_hashitems;
    /*offset ptr*/
    pst_hashitem += ui_itemindx;
    if ( ( NULL == pst_hashitem ) || ( HI_TRUE != pst_hashitem->ui_state ) )
    {
        return HI_RET_ITEM_NOTEXIST;
    }

    /*check first item */
    ui_ret = ((hi_os_hash_comparecallback)pst_hashtable->pv_comparecallback)( pst_hashitem->pv_keyobject, pv_keyobject );/*lint !e611*/
    if( HI_TRUE == ui_ret )
    {
        *pv_dataobject = pst_hashitem->pv_dataobject;
        return HI_RET_SUCC;
    }

    /*process collision items,check if exist*/
    hi_list_for_each_safe(pst_list_pos, pst_list_next, &pst_hashitem->st_listhead)
    {
        pst_conflictitem = hi_list_entry(pst_list_pos, hi_os_hash_item_s, st_listhead);
        if ( NULL != pst_conflictitem )
        {
            ui_ret = ((hi_os_hash_comparecallback)pst_hashtable->pv_comparecallback)( pst_conflictitem->pv_keyobject, pv_keyobject );/*lint !e611*/
            if( HI_TRUE == ui_ret )
            {
                *pv_dataobject = pst_conflictitem->pv_dataobject;
                return HI_RET_SUCC;
            }
        }
    }

    return HI_RET_ITEM_NOTEXIST;
}

hi_uint32 hi_os_hash_remove(  hi_os_hash_head_s *pst_hashtable, hi_void *pv_keyobject )
{
    hi_uint32   ui_ret;
    hi_uint32   ui_itemindx;
    hi_list_head *pst_list_pos    = NULL;
    hi_list_head *pst_list_next   = NULL;
    hi_os_hash_item_s *pst_hashitem = NULL;
    hi_os_hash_item_s *pst_conflictitem = NULL;

    if ( ( NULL == pst_hashtable )
        || ( NULL == pst_hashtable->pv_matchcallback )
        || ( NULL == pst_hashtable->pv_comparecallback ))
    {
        return HI_RET_NULLPTR;
    }

    /*calc key index*/
    ui_itemindx = ((hi_os_hash_matchcallback)pst_hashtable->pv_matchcallback)( pv_keyobject );/*lint !e611*/
    if ( ui_itemindx >= pst_hashtable->ui_hashsize )
    {
        return HI_RET_ITEM_EXCEPT;
    }

    /*get first ptr*/
    pst_hashitem = (hi_os_hash_item_s*)pst_hashtable->pv_hashitems;
    /*offset ptr*/
    pst_hashitem += ui_itemindx;
    if ( HI_TRUE != pst_hashitem->ui_state )
    {
        return HI_RET_ITEM_NOTEXIST;
    }

    /*check first item,if have conflict need to move this position */
    ui_ret = ((hi_os_hash_comparecallback)pst_hashtable->pv_comparecallback)( pst_hashitem->pv_keyobject, pv_keyobject );/*lint !e611*/
    if( HI_TRUE == ui_ret )
    {
        if ( hi_list_empty( &pst_hashitem->st_listhead ) )
        {
            if ( NULL != pst_hashtable->pv_freekeycallback )
            {
                ((hi_os_hash_freecallback)pst_hashtable->pv_freekeycallback)( pst_hashitem->pv_keyobject );/*lint !e611*/
            }
            if ( NULL != pst_hashtable->pv_freedatacallback )
            {
                ((hi_os_hash_freecallback)pst_hashtable->pv_freedatacallback)( pst_hashitem->pv_dataobject );/*lint !e611*/
            }

            pst_hashitem->ui_state      = HI_FALSE;
            pst_hashitem->pv_keyobject  = NULL;
            pst_hashitem->pv_dataobject = NULL;
            hi_list_init_head( &pst_hashitem->st_listhead );
            pst_hashtable->ui_currnum --;

            return HI_RET_SUCC;
        }

        /*如果下面有冲突项需要将后一个移到当前未知,再删除当前内容,释放后一个*/
        pst_list_pos = hi_list_getnext( &pst_hashitem->st_listhead );
        if ( NULL == pst_list_pos )
        {
            return HI_RET_ITEM_EXCEPT;
        }
        pst_conflictitem = hi_list_entry(pst_list_pos, hi_os_hash_item_s, st_listhead);
        if ( NULL == pst_conflictitem )
        {
            return HI_RET_ITEM_EXCEPT;
        }

        /*delete from used*/
        hi_list_del( &pst_conflictitem->st_listhead );
        /*free to idle item*/
        hi_list_add_tail( &pst_conflictitem->st_listhead, &pst_hashtable->st_idleconflict );

        /*release item context*/
        if ( NULL != pst_hashtable->pv_freekeycallback )
        {
            ((hi_os_hash_freecallback)pst_hashtable->pv_freekeycallback)( pst_hashitem->pv_keyobject );/*lint !e611*/
        }
        if ( NULL != pst_hashtable->pv_freedatacallback )
        {
            ((hi_os_hash_freecallback)pst_hashtable->pv_freedatacallback)( pst_hashitem->pv_dataobject );/*lint !e611*/
        }

        /*move conflict */
        pst_hashitem->pv_keyobject  = pst_conflictitem->pv_keyobject;
        pst_hashitem->pv_dataobject = pst_conflictitem->pv_dataobject;

        /*clear conflict context*/
        pst_conflictitem->ui_state      = HI_FALSE;
        pst_conflictitem->pv_keyobject  = NULL;
        pst_conflictitem->pv_dataobject = NULL;

        pst_hashtable->ui_conflictnum --;

        return HI_RET_SUCC;
    }

    /*process collision items,check if exist*/
    hi_list_for_each_safe(pst_list_pos, pst_list_next, &pst_hashitem->st_listhead)
    {
        pst_conflictitem = hi_list_entry(pst_list_pos, hi_os_hash_item_s, st_listhead);
        if ( NULL != pst_conflictitem )
        {
            ui_ret = ((hi_os_hash_comparecallback)pst_hashtable->pv_comparecallback)( pst_conflictitem->pv_keyobject, pv_keyobject );/*lint !e611*/
            if( HI_TRUE == ui_ret )
            {
                if ( NULL != pst_hashtable->pv_freekeycallback )
                {
                    ((hi_os_hash_freecallback)pst_hashtable->pv_freekeycallback)( pst_conflictitem->pv_keyobject );/*lint !e611*/
                }
                if ( NULL != pst_hashtable->pv_freedatacallback )
                {
                    ((hi_os_hash_freecallback)pst_hashtable->pv_freedatacallback)( pst_conflictitem->pv_dataobject );/*lint !e611*/
                }
                pst_conflictitem->ui_state      = HI_FALSE;
                pst_conflictitem->pv_keyobject  = NULL;
                pst_conflictitem->pv_dataobject = NULL;

                /*delete from used*/
                hi_list_del( &pst_conflictitem->st_listhead );
                /*free to idle item*/
                hi_list_add_tail( &pst_conflictitem->st_listhead, &pst_hashtable->st_idleconflict );

                pst_hashtable->ui_conflictnum --;

                return HI_RET_SUCC;
            }
        }
    }

    return HI_RET_ITEM_NOTEXIST;
}

hi_uint32 hi_os_hash_insert( hi_os_hash_head_s *pst_hashtable,hi_void *pv_keyobject, hi_void *pv_dataobject )
{
    hi_uint32   ui_ret;
    hi_uint32   ui_itemindx;
    hi_uint32   ui_cnt;
    hi_list_head *pst_list_pos    = NULL;
    hi_list_head *pst_list_next   = NULL;
    hi_os_hash_item_s *pst_hashitem = NULL;
    hi_os_hash_item_s *pst_conflictitem = NULL;

    if ( ( NULL == pst_hashtable )
        || ( NULL == pst_hashtable->pv_matchcallback )
        || ( NULL == pst_hashtable->pv_comparecallback ))
    {
        return HI_RET_NULLPTR;
    }

    /*calc key index*/
    ui_itemindx = ((hi_os_hash_matchcallback)pst_hashtable->pv_matchcallback)( pv_keyobject );/*lint !e611*/
    if ( ui_itemindx >= pst_hashtable->ui_hashsize )
    {
        return HI_RET_ITEM_EXCEPT;
    }

    /*get first ptr*/
    pst_hashitem = (hi_os_hash_item_s*)pst_hashtable->pv_hashitems;
    /*offset ptr*/
    pst_hashitem += ui_itemindx;
    /*if hash item idle */
    if ( HI_FALSE == pst_hashitem->ui_state )
    {
        pst_hashitem->ui_state      = HI_TRUE;
        pst_hashitem->pv_keyobject  = pv_keyobject;
        pst_hashitem->pv_dataobject = pv_dataobject;

        hi_list_init_head( &pst_hashitem->st_listhead );
        pst_hashtable->ui_currnum ++;
        return HI_RET_SUCC;
    }

    /*check first item */
    ui_ret = ((hi_os_hash_comparecallback)pst_hashtable->pv_comparecallback)( pst_hashitem->pv_keyobject, pv_keyobject );/*lint !e611*/
    if( HI_TRUE == ui_ret )
    {
        return HI_RET_ITEM_EXIST;
    }

    /*check if conflict item has cur item */
    ui_cnt = 0;
    if ( ! hi_list_empty( &pst_hashitem->st_listhead) )
    {
        /*process collision items,check if exist*/
        hi_list_for_each_safe(pst_list_pos, pst_list_next, &pst_hashitem->st_listhead)
        {
            pst_conflictitem = hi_list_entry(pst_list_pos, hi_os_hash_item_s, st_listhead);
            if ( NULL != pst_conflictitem )
            {
                ui_ret = ((hi_os_hash_comparecallback)pst_hashtable->pv_comparecallback)( pst_conflictitem->pv_keyobject, pv_keyobject );/*lint !e611*/
                if( HI_TRUE == ui_ret )
                {
                    return HI_RET_ITEM_EXIST;
                }
            }
            ui_cnt++;
        }
    }

    /*check conflict max of per item */
    if ( ui_cnt >= pst_hashtable->ui_conflictitemsize )
    {
        return HI_RET_ITEM_FULL;
    }

    /*get new item from idle*/
    pst_list_pos = hi_list_getnext( &pst_hashtable->st_idleconflict );
    if ( NULL == pst_list_pos )
    {
        return HI_RET_ITEM_FULL;
    }

    /*get new item*/
    pst_conflictitem = hi_list_entry(pst_list_pos, hi_os_hash_item_s, st_listhead);
    if ( NULL == pst_conflictitem )
    {
        return HI_RET_ITEM_EXCEPT;
    }

    pst_conflictitem->ui_state  = HI_TRUE;
    pst_conflictitem->pv_keyobject  = pv_keyobject;
    pst_conflictitem->pv_dataobject = pv_dataobject;

    /*delete from idle*/
    hi_list_del( &pst_conflictitem->st_listhead );
    /*add to cur hash item*/
    hi_list_add_tail( &pst_conflictitem->st_listhead, &pst_hashitem->st_listhead );

    pst_hashtable->ui_conflictnum ++;

    return HI_RET_SUCC;
}

/*ui_hashsize : HASH最大深度*/
/*ui_conflictitemsize : HASH每个ITEM最大许可冲突深度*/
/*ui_conflictmaxsize : HASH最大冲突表项深度*/
hi_uint32 hi_os_hash_init( hi_uint32 ui_hashsize, hi_uint32 ui_conflictitemsize, hi_uint32 ui_conflictmaxsize,
                               hi_void **pv_hashtable, hi_void *pv_matchcallback, hi_void *pv_comparecallback,
                               hi_void *pv_freekeycallback,hi_void *pv_freedatacallback )
{
    hi_uint32   ui_cnt = 0;
    hi_os_hash_head_s *pst_hashhead = NULL;
    hi_os_hash_item_s *pst_hashitem = NULL;
    hi_os_hash_item_s *pv_conflictitems = NULL;

    if ( ( 0 == ui_hashsize )
        || ( NULL == pv_hashtable )
        || ( NULL == pv_matchcallback )
        || ( NULL == pv_comparecallback ) )
    {
        return HI_RET_INVALID_PARA;
    }
    /*alloc hash head*/
    pst_hashhead = (hi_os_hash_head_s*)hi_os_malloc( sizeof(hi_os_hash_head_s) );
    if ( NULL == pst_hashhead )
    {
        return HI_RET_MALLOC_FAIL;
    }
    memset_s((hi_void*)pst_hashhead,sizeof(hi_os_hash_head_s),0,sizeof(hi_os_hash_head_s));

    /*alloc hash item*/
    pst_hashitem = (hi_os_hash_item_s*)hi_os_malloc( ui_hashsize*sizeof(hi_os_hash_item_s));
    if ( NULL == pst_hashitem )
    {
        hi_os_free( pst_hashhead );
        return HI_RET_MALLOC_FAIL;
    }
    memset_s((hi_void*)pst_hashitem,ui_hashsize*sizeof(hi_os_hash_item_s),0,ui_hashsize*sizeof(hi_os_hash_item_s));
    for ( ui_cnt = 0; ui_cnt < ui_hashsize; ui_cnt++ )
    {
        hi_list_init_head( &pst_hashitem[ui_cnt].st_listhead );
    }

    /*alloc hash conflict item*/
    hi_list_init_head( &pst_hashhead->st_idleconflict );
    if ( 0 != ui_conflictmaxsize )
    {
        pv_conflictitems = (hi_os_hash_item_s*)hi_os_malloc( ui_conflictmaxsize*sizeof(hi_os_hash_item_s));
        if ( NULL == pv_conflictitems )
        {
            hi_os_free( pst_hashitem );
            hi_os_free( pst_hashhead );
            return HI_RET_MALLOC_FAIL;
        }
        memset_s((hi_void*)pv_conflictitems,ui_conflictmaxsize*sizeof(hi_os_hash_item_s),0,ui_conflictmaxsize*sizeof(hi_os_hash_item_s));
        for ( ui_cnt = 0; ui_cnt < ui_conflictmaxsize; ui_cnt++ )
        {
            hi_list_add_tail( &pv_conflictitems[ui_cnt].st_listhead, &pst_hashhead->st_idleconflict);
        }
    }

    pst_hashhead->ui_state              = HI_TRUE;
    pst_hashhead->ui_hashsize           = ui_hashsize;
    pst_hashhead->ui_conflictitemsize   = ui_conflictitemsize;
    pst_hashhead->ui_conflictmaxsize    = ui_conflictmaxsize;
    pst_hashhead->pv_hashitems          = (hi_void*)pst_hashitem;
    pst_hashhead->pv_conflictitems      = (hi_void*)pv_conflictitems;
    pst_hashhead->pv_matchcallback      = (hi_void*)pv_matchcallback;
    pst_hashhead->pv_comparecallback    = (hi_void*)pv_comparecallback;
    pst_hashhead->pv_freekeycallback    = (hi_void*)pv_freekeycallback;
    pst_hashhead->pv_freedatacallback   = (hi_void*)pv_freedatacallback;

    /* output hash table mem point */
    *pv_hashtable = (hi_void*)pst_hashhead;

    return HI_RET_SUCC;
}

hi_uint32 hi_os_hash_free( hi_void **pv_hashtable )
{
    hi_uint32   ui_cnt = 0;
    hi_os_hash_head_s *pst_hashhead;
    hi_os_hash_item_s *pst_hashitems;
    hi_os_hash_item_s *pv_conflictitems;

    if ( NULL == *pv_hashtable )
    {
        return HI_RET_NULLPTR;
    }

    pst_hashhead  = (hi_os_hash_head_s *)*pv_hashtable;

    /* release hash item */
    pst_hashitems = pst_hashhead->pv_hashitems;
    if ( NULL != pst_hashitems )
    {
        for ( ui_cnt = 0; ui_cnt < pst_hashhead->ui_hashsize; ui_cnt++,pst_hashitems++ )
        {
            if ( HI_FALSE == pst_hashitems->ui_state )
            {
               continue;
            }
            if ( NULL != pst_hashhead->pv_freekeycallback )
            {
                ((hi_os_hash_freecallback)pst_hashhead->pv_freekeycallback)( pst_hashitems->pv_keyobject );/*lint !e611*/
            }
            if ( NULL != pst_hashhead->pv_freedatacallback )
            {
                ((hi_os_hash_freecallback)pst_hashhead->pv_freedatacallback)( pst_hashitems->pv_dataobject );/*lint !e611*/
            }
        }
        hi_os_free( pst_hashhead->pv_hashitems );
    }

    /* release hash conflict item */
    pv_conflictitems = pst_hashhead->pv_conflictitems;
    if ( NULL != pv_conflictitems )
    {
        for ( ui_cnt = 0; ui_cnt < pst_hashhead->ui_conflictmaxsize; ui_cnt++,pv_conflictitems++ )
        {
            if ( HI_FALSE == pv_conflictitems->ui_state )
            {
                continue;
            }
            if ( NULL != pst_hashhead->pv_freekeycallback )
            {
                ((hi_os_hash_freecallback)pst_hashhead->pv_freekeycallback)( pv_conflictitems->pv_keyobject );/*lint !e611*/
            }
            if ( NULL != pst_hashhead->pv_freedatacallback )
            {
                ((hi_os_hash_freecallback)pst_hashhead->pv_freedatacallback)( pv_conflictitems->pv_dataobject );/*lint !e611*/
            }
        }
        hi_os_free( pst_hashhead->pv_conflictitems );
    }

    /* release hash head */
    hi_os_free( *pv_hashtable );
    *pv_hashtable = NULL;

    return HI_RET_SUCC;
}

hi_uint32 hi_os_hash_dump( hi_void *pv_hashtable )
{
    hi_uint32 ui_cnt=0;
    hi_os_hash_head_s *pst_hashhead;
    hi_os_hash_item_s *pst_item;

    if ( NULL == pv_hashtable )
    {
        return HI_RET_NULLPTR;
    }

    pst_hashhead = (hi_os_hash_head_s *)pv_hashtable;
    pst_item = (hi_os_hash_item_s*)pst_hashhead->pv_hashitems;
    if ( NULL == pst_item )
    {
        return HI_RET_SUCC;
    }
    for ( ui_cnt=0; ui_cnt<pst_hashhead->ui_hashsize; ui_cnt++ )
    {
        if ( HI_TRUE != pst_hashhead->ui_state )
        {
            continue;
        }

        pst_item++;
    }

    pst_item = (hi_os_hash_item_s*)pst_hashhead->pv_conflictitems;
    if ( NULL == pst_item )
    {
        return HI_RET_SUCC;
    }
    for ( ui_cnt=0; ui_cnt<pst_hashhead->ui_conflictmaxsize; ui_cnt++ )
    {
        if ( HI_TRUE != pst_hashhead->ui_state )
        {
            continue;
        }

        pst_item++;
    }

    return HI_RET_SUCC;
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
