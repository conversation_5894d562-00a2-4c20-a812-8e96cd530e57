/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_os_fileio.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_08_04
  最近修改   :

******************************************************************************/
#include <sys/stat.h>
#include <termios.h>
#include "securec.h"
#include "hi_errno.h"
#include "os/hi_os_thread.h"
#include "os/hi_os_fileio.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif /* __cpluscplus */
#endif /* __cpluscplus */

/*****************************************************************************
 函 数 名  : hi_os_opendir
 功能描述  : 打开参数name指定的目录，并返回DIR*类型的目录流，
             接下来对目录的读取和搜寻都要使用这个返回值。
 输入参数  : pc_name  : 要打开的文件夹名字
 返 回 值  : HI_P_DIR: 执行成功DIR*类型的目录流
             执行失败返回NULL_PTR
*****************************************************************************/
hi_os_dir hi_os_opendir(const hi_char8 *pc_name)
{
    return opendir(pc_name);
}

/*****************************************************************************
 函 数 名  : hi_os_closedir
 功能描述  : 关闭目录
 输入参数  : HI_P_DIR dir  : 已经打开的目录
*****************************************************************************/
hi_int32 hi_os_closedir(hi_os_dir p_dir)
{
    return closedir(p_dir);
}

/*****************************************************************************
 函 数 名  : hi_os_readdir
 功能描述  : 读取目录
 输入参数  : HI_P_DIR dir  : 已经打开的目录
*****************************************************************************/
hi_os_dirent_s * hi_os_readdir(hi_os_dir p_dir)
{
    return (hi_os_dirent_s *)(hi_void *)readdir(p_dir);
}

/*****************************************************************************
 函 数 名  : hi_os_scandir
 功能描述  : 读取特定的目录数据
 输入参数  : HI_P_DIR dir  : 目录
 返 回 值  :
             0: 执行成功返回复制到namelist数组中的数据结构数目
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_scandir(const hi_char8 *p_dir,
                    hi_os_dirent_s ***pst_namelist,
                    hi_int32(*select)(const hi_os_dirent_s *),
                    hi_int32(*cmp)(const struct dirent **, const struct dirent **))
{
    return scandir(p_dir,(struct dirent***)(hi_void ***)pst_namelist,(hi_os_scandir_selectcallback)select,cmp);
}

/*****************************************************************************
 函 数 名  : hi_os_mkdir
 功能描述  : 创建目录
 输入参数  : const hi_uchar8 *dirname ,hi_uint32 mode
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_mkdir( const hi_char8 *c_dirname ,hi_uint32 mode)
{
    return mkdir(c_dirname,mode);
}

/*****************************************************************************
 函 数 名  : hi_os_access
 功能描述  : 判断是否具有访问文件的权限
 输入参数  : pc_pathname, hi_int32 mode
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_access(const hi_char8 *pc_pathname, hi_int32 i_mode)
{
    return access(pc_pathname,i_mode);
}

/*****************************************************************************
 函 数 名  : hi_os_remove
 功能描述  : 删除文件
 输入参数  : pc_filename, hi_int32 mode
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_remove(const hi_char8*pc_filename)
{
    return remove(pc_filename);
}

/*****************************************************************************
 函 数 名  : hi_os_fcopy
 功能描述  : 把源文件pcSrcFile拷贝到目的文件pcDestFile
 输入参数  : pcSrcFile
             pcDestFile
*****************************************************************************/
hi_uint32 hi_os_fcopy(hi_char8 *pc_srcfile, hi_char8 *pc_destfile)
{
    hi_uint32   ui_ret       = HI_RET_SUCC;
    hi_char8    c_accmd[256]  = {0};

    /* 先把目的文件删除掉 */
    (hi_void)hi_os_sprintf(c_accmd, "rm -f %s", pc_destfile);
    (hi_void)hi_os_execcmd(c_accmd);

    /* 然后把源文件拷贝到目的文件 */
    (hi_void)hi_os_sprintf(c_accmd,"cp -f %s %s", pc_srcfile, pc_destfile);
    ui_ret= (hi_uint32)hi_os_execcmd(c_accmd);

    return ui_ret;
}

int hi_os_filecopy(const char *src_file, const char *dest_file)
{
#define MAX_COPY_BUFF_SIZE (4 * 1024)
    ssize_t nread;
    int src_fd, dest_fd, rc = -1;
    char buf[MAX_COPY_BUFF_SIZE] = {0};

    src_fd = open(src_file, O_RDONLY);
    if (src_fd < 0) {
        return rc;
    }

    dest_fd = open(dest_file, O_WRONLY | O_CREAT | O_TRUNC, 0666);
    if (dest_fd == -1) {
        close(src_fd);
        return rc;
    }

    while ((nread = read(src_fd, buf, MAX_COPY_BUFF_SIZE)) > 0) {
        if (write(dest_fd, buf, nread) != nread) {
            break;
        }
    }
    if (nread == 0)
        rc = 0;
    close(src_fd);
    close(dest_fd);
    return rc;
}

/*****************************************************************************
 函 数 名  : hi_os_fdelete
 功能描述  : 调用shell命令rm删除指定的文件
 输入参数  : pcFile
*****************************************************************************/
hi_uint32 hi_os_fdelete(hi_char8 *pc_file)
{
    hi_uint32   ui_ret       = HI_RET_SUCC;
    hi_char8    c_cmd[256]  = {0};

    (hi_void)hi_os_sprintf(c_cmd, "rm -f %s", pc_file);
    ui_ret = (hi_uint32)hi_os_execcmd(c_cmd);

    return ui_ret;
}


/*****************************************************************************
 函 数 名  : hi_os_fopen
 功能描述  : 打开文件流
 输入参数  : const hi_char8 * filename,const hi_char8 *modes
 返 回 值  : 文件描述符
*****************************************************************************/
HI_FILE_S *hi_os_fopen (const hi_char8 * pc_filename,const hi_char8 *c_modes)
{
    return (HI_FILE_S*)fopen(pc_filename,c_modes);
}

/*****************************************************************************
 函 数 名  : hi_os_fflush
 功能描述  : flush 转储清除
 输入参数  : HI_P_DIR dir  : 目录
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32  hi_os_fflush(HI_FILE_S *p_stream)
{
    return fflush((FILE *)p_stream);
}

/*****************************************************************************
 函 数 名  : hi_os_fclose
 功能描述  : 关闭文件
 输入参数  : HI_P_DIR dir  : 目录
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
 hi_int32 hi_os_fclose(HI_FILE_S *p_stream)
{
    return fclose((FILE *)p_stream);
}

/*****************************************************************************
 函 数 名  : hi_os_tmpfile
 功能描述  : 创建临时文件
 返 回 值  : 文件描述符
*****************************************************************************/
HI_FILE_S *hi_os_tmpfile (hi_void)
{
    return (HI_FILE_S *)tmpfile();
}

/*****************************************************************************
 函 数 名  : hi_os_ftruncate
 功能描述  : 改变文件大小
 输入参数  : HI_P_DIR dir  : 目录
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_ftruncate(hi_int32 i_fd, hi_int32 i_length)
{
    return ftruncate(i_fd,i_length);
}

/*****************************************************************************
 函 数 名  : hi_os_unlink
 功能描述  : 删除文件
 输入参数  : 文件路径和名
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_unlink(const hi_char8 * pc_pathnames)
{
    return unlink(pc_pathnames);
}

/*****************************************************************************
 函 数 名  : hi_os_fsize
 功能描述  : 获取文件大小
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_fsize(const hi_char8 *pc_filename, hi_uint32 *pui_filesize)
{
    hi_uint32 uiLen = 0;
    HI_FILE_S *fp;
    hi_int32 i_ret;

    if (NULL == pc_filename) {
        return -1;
    }

    fp = fopen(pc_filename, "r");
    if (NULL == fp) {
        return -1;
    }

    i_ret = fseek(fp,0,HI_SEEK_END);
    if(HI_RET_SUCC != i_ret)
    {
        fclose(fp);
        return i_ret;
    }
    uiLen = (hi_uint32)ftell(fp);
    *pui_filesize = uiLen;
    fclose(fp);

    return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_os_fpathconf
 功能描述  : 获取文件的配置选项
 输入参数  : hi_int32 iFiledes
             hi_int32 iName
*****************************************************************************/
hi_int32 hi_os_fpathconf(hi_int32 i_filedes,hi_int32 i_name)
{
    return fpathconf(i_filedes, i_name);
}

/*****************************************************************************
 函 数 名  : hi_os_fprintf
 功能描述  : 格式化输出数据到文件
 输入参数  : HI_FILE_S *p_stream, const hi_uchar8 *pc_format, ...
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_fprintf (HI_FILE_S *p_stream, const hi_char8 *pc_format, ...)
{
    hi_int32 i_dwbuflen;
    HI_VA_LIST marker;

    va_start( marker, pc_format );
    i_dwbuflen = vfprintf((FILE *)p_stream, pc_format, marker);
    va_end(marker);

    return i_dwbuflen;
}

/*****************************************************************************
 函 数 名  : hi_os_sprintf
 功能描述  : 格式化字符串复制
 输入参数  : hi_uchar8 *puc_str, const hi_uchar8 *pc_format, ...
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_sprintf(hi_char8 *pc_str, const hi_char8 *pc_format, ...)
{
    hi_int32 i_dwbuflen;
    HI_VA_LIST marker;

    va_start( marker, pc_format );
    i_dwbuflen = vsprintf(pc_str, pc_format, marker);
    va_end(marker);

    return i_dwbuflen;
}

/*****************************************************************************
 函 数 名  : hi_os_sprintf_s
 功能描述  : 格式化字符串复制,若是字符串则必须含有结束符
 输入参数  : hi_uchar8 *puc_str
                           hi_uint32 ui_destmax :目的缓冲区大小（包括'\0'）
                           cons hi_uchar8 t*pc_format, ...
 返 回 值  :
             x: 执行成功,返回写入的字符个数
            -1:执行失败返回-1
*****************************************************************************/
hi_int32 hi_os_sprintf_s(hi_char8 *pc_str, hi_uint32 ui_destmax, const hi_char8 *pc_format, ...)
{
    hi_int32 i_dwbuflen;
	HI_VA_LIST marker;

	va_start( marker, pc_format );
    i_dwbuflen = vsprintf_s( pc_str, ui_destmax, pc_format, marker);
    va_end(marker);
    return i_dwbuflen;
}

/*****************************************************************************
 函 数 名  : hi_os_snprintf
 功能描述  : 格式化字符串复制
 输入参数  : hi_uchar8 *puc_str, hi_uint32 size, const hi_uchar8 *pc_format, ...
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_snprintf(hi_char8 *pc_str, hi_uint32 ui_size, const hi_char8 *pc_format, ...)
{
    hi_int32 i_dwbuflen;
    HI_VA_LIST marker;

    va_start( marker, pc_format );
    i_dwbuflen = vsnprintf(pc_str, ui_size,pc_format, marker);
    va_end(marker);

    return i_dwbuflen;
}

/*****************************************************************************
 函 数 名  : hi_os_snprintf_s
 功能描述  : 格式化字符串复制,若是字符串则必须含有结束符.
                           建议传入的参数destMax 大于 count以保证有截断功能
 输入参数  : hi_uchar8 *puc_str
                           hi_uint32 ui_destmax :目的缓冲区大小（包括'\0'）
                           hi_uint32 size
                           const hi_uchar8 *pc_format, ...
 返 回 值  :
             x: 执行成功,返回写入的字符个数
            -1:执行失败返回-1
*****************************************************************************/
hi_int32 hi_os_snprintf_s(hi_char8 *pc_str, hi_uint32 ui_destmax, hi_uint32 ui_size, const hi_char8 *pc_format, ...)
{
    hi_int32 i_dwbuflen;
	HI_VA_LIST marker;

    va_start( marker, pc_format );
    i_dwbuflen = vsnprintf_s( pc_str, ui_destmax, ui_size,pc_format, marker);
	va_end(marker);

    return i_dwbuflen;
}

/*****************************************************************************
 函 数 名  : hi_os_vsprintf
 功能描述  : 格式化字符串复制
 输入参数  : hi_uchar8 *puc_str, const hi_uchar8 *pc_format, HI_VA_LIST argList
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_vsprintf(hi_char8 *pc_str, const hi_char8 *pc_format, HI_VA_LIST argList)
{
    return  vsprintf(pc_str, pc_format, argList);
}

/*****************************************************************************
 函 数 名  : hi_os_vsprintf_s
 功能描述  : 格式化字符串复制, 若是字符串则必须含有结束符
 输入参数  : hi_uchar8 *puc_str
                           hi_uint32 ui_destmax :目的缓冲区大小（包括'\0'）
                           const hi_uchar8 *pc_format, ...
 返 回 值  :
             x: 执行成功,返回写入的字符个数
            -1:执行失败返回-1
*****************************************************************************/
hi_int32 hi_os_vsprintf_s(hi_char8 *pc_str, hi_uint32 ui_destmax, const hi_char8 *pc_format, ...)
{
    hi_int32 i_dwbuflen;
    HI_VA_LIST marker;

    va_start( marker, pc_format );
    i_dwbuflen = vsprintf_s(pc_str, ui_destmax, pc_format, marker);
    va_end(marker);

    return i_dwbuflen;
}


/*****************************************************************************
 函 数 名  : hi_os_vsnprintf
 功能描述  : 格式化字符串复制
 输入参数  : hi_uchar8 *puc_str, hi_uint32 ui_size, const hi_uchar8 *pc_format, HI_VA_LIST argList
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_vsnprintf(hi_char8 *pc_str, hi_uint32 ui_size, const hi_char8 *pc_format, HI_VA_LIST arglist)
{
    return vsnprintf(pc_str,ui_size, pc_format, arglist);
}

/*****************************************************************************
 函 数 名  : hi_os_vsnprintf_s
 功能描述  : 格式化字符串复制, 若是字符串则必须含有结束符
                           建议传入的参数destMax 大于 count以保证有截断功能
 输入参数  : hi_uchar8 *puc_str
                           hi_uint32 ui_destmax :目的缓冲区大小（包括'\0'）
                           hi_uint32 size
                           const hi_uchar8 *pc_format, ...
 返 回 值  :
             x: 执行成功,返回写入的字符个数
            -1:执行失败返回-1
*****************************************************************************/
hi_int32 hi_os_vsnprintf_s(hi_char8 *pc_str, hi_uint32 ui_destmax, hi_uint32 ui_size, const hi_char8 *pc_format, ...)
{
    hi_int32 i_dwbuflen;
    HI_VA_LIST marker;

    va_start( marker, pc_format );
    i_dwbuflen = vsnprintf_s(pc_str, ui_destmax, ui_size, pc_format, marker);
    va_end(marker);

    return i_dwbuflen;
}

/*****************************************************************************
 函 数 名  : hi_os_vfprintf
 功能描述  : 格式化字符串到文件
 输入参数  : hi_uchar8 *puc_str, hi_uint32 size, const hi_uchar8 *pc_format, HI_VA_LIST argList
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_vfprintf(HI_FILE_S *p_f, const hi_char8 *pc_format, HI_VA_LIST arglist)
{
    return vfprintf(p_f, pc_format, arglist);
}

/*****************************************************************************
 函 数 名  : hi_os_printf
 功能描述  : 格式化输出数据到标准输出
 输入参数  : const hi_uchar8 *pc_format, ...
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32  hi_os_printf (const hi_char8 *pc_format, ...)
{
    hi_int32 i_dwbuflen;
    HI_VA_LIST marker;

    va_start( marker, pc_format );
    i_dwbuflen = vprintf( pc_format, marker);
    va_end(marker);

    return i_dwbuflen;
}

/*****************************************************************************
 函 数 名  : hi_os_fscanf
 功能描述  : 格式化字符串输入
 输入参数  : HI_FILE_S * p_stream,const hi_uchar8 * pc_format, ...
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_fscanf (HI_FILE_S * p_stream,const hi_char8 * pc_format, ...)
{
    hi_int32 i_dwbuflen;
    HI_VA_LIST marker;

    va_start( marker, pc_format );
    i_dwbuflen = vfscanf( (FILE *)p_stream,pc_format, marker);
    va_end(marker);
    return i_dwbuflen;
}

/*****************************************************************************
 函 数 名  : hi_os_fscanf_s
 功能描述  : 格式化字符串输入
 输入参数  : HI_FILE_S * p_stream,const hi_uchar8 * pc_format, ...
 返 回 值  :
             执行成功返回读取字段数
             执行失败返回-1
*****************************************************************************/
hi_int32 hi_os_fscanf_s (HI_FILE_S * p_stream,const hi_char8 * pc_format, ...)
{
    hi_int32 i_dwbuflen;
	HI_VA_LIST marker;

    va_start( marker, pc_format );
    i_dwbuflen = vfscanf_s( (FILE *)p_stream,pc_format, marker);
	va_end(marker);
    return i_dwbuflen;
}

/*****************************************************************************
 函 数 名  : hi_os_vfscanf_s
 功能描述  : 格式化字符串输入
 输入参数  : HI_FILE_S * p_stream,const hi_uchar8 * pc_format, ...
 返 回 值  :
             执行成功返回读取字段数
             执行失败返回-1
*****************************************************************************/
hi_int32 hi_os_vfscanf_s (HI_FILE_S * p_stream,const hi_char8 * pc_format, ...)
{
    hi_int32 i_dwbuflen;
    HI_VA_LIST marker;

    va_start( marker, pc_format );
    i_dwbuflen = vfscanf_s( (FILE *)p_stream,pc_format, marker);
    va_end(marker);
    return i_dwbuflen;
}

/*****************************************************************************
 函 数 名  : hi_os_scanf
 功能描述  : 格式化字符串输入
 输入参数  : const hi_uchar8 * pc_format, ...
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_scanf( hi_char8 * pc_format, ...)
{
    hi_int32 i_dwbuflen;
    HI_VA_LIST marker;

    va_start( marker, pc_format );
    i_dwbuflen = vscanf( pc_format, marker);
    va_end(marker);
    return i_dwbuflen;
}

/*****************************************************************************
 函 数 名  : hi_os_scanf_s
 功能描述  : 格式化字符串输入
 输入参数  : const hi_uchar8 * pc_format, ...
 返 回 值  :
             执行成功返回读取字段数
             执行失败返回-1
*****************************************************************************/
hi_int32 hi_os_scanf_s( hi_char8 * pc_format, ...)
{
    hi_int32 i_dwbuflen;
	HI_VA_LIST marker;

    va_start( marker, pc_format );
    i_dwbuflen = vscanf_s( pc_format, marker);
	va_end(marker);
    return i_dwbuflen;
}

/*****************************************************************************
 函 数 名  : hi_os_vscanf_s
 功能描述  : 格式化字符串输入
 输入参数  : const hi_uchar8 * pc_format, ...
 返 回 值  :
              执行成功返回读取字段数
              执行失败返回-1
*****************************************************************************/
hi_int32 hi_os_vscanf_s( hi_char8 * pc_format, ...)
{
    hi_int32 i_dwbuflen;
    HI_VA_LIST marker;

    va_start( marker, pc_format );
    i_dwbuflen = vscanf_s( pc_format, marker);
    va_end(marker);
    return i_dwbuflen;
}

/*****************************************************************************
 函 数 名  : hi_os_sscanf
 功能描述  : 格式化字符串输入
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_sscanf( hi_char8 *pc_str, const hi_char8 *pc_format, ...)
{
    hi_int32 i_dwbuflen;
    HI_VA_LIST marker;

    va_start( marker, pc_format );
    i_dwbuflen = vsscanf(pc_str,pc_format, marker);
    va_end(marker);

    return i_dwbuflen;
}

/*****************************************************************************
 函 数 名  : hi_os_sscanf_s
 功能描述  : 格式化字符串输入
                           源缓冲区含有结束符
 返 回 值  :
             执行成功返回读取字段数
             执行失败返回-1
*****************************************************************************/
hi_int32 hi_os_sscanf_s( hi_char8 *pc_str, const hi_char8 *pc_format, ...)
{
    hi_int32 i_dwbuflen;
	HI_VA_LIST marker;

    va_start( marker, pc_format );
    i_dwbuflen = vsscanf_s( pc_str, pc_format, marker);
	va_end(marker);

    return i_dwbuflen;
}

/*****************************************************************************
 函 数 名  : hi_os_vsscanf_s
 功能描述  : 格式化字符串输入
                           源缓冲区含有结束符
 返 回 值  :
             执行成功返回读取字段数
             执行失败返回-1
*****************************************************************************/
hi_int32 hi_os_vsscanf_s( hi_char8 *pc_str, const hi_char8 *pc_format, ...)
{
    hi_int32 i_dwbuflen;
    HI_VA_LIST marker;

    va_start( marker, pc_format );
    i_dwbuflen = vsscanf_s(pc_str,pc_format, marker);
    va_end(marker);

    return i_dwbuflen;
}


/*****************************************************************************
 函 数 名  : hi_os_fgetc
 功能描述  : 从文件中读取一个字符
 输入参数  : HI_FILE_S *p_stream
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_fgetc (HI_FILE_S *p_stream)
{
    return fgetc((FILE *)p_stream);
}

/*****************************************************************************
 函 数 名  : hi_os_fputc
 功能描述  : 将一个指定字符写入到文件流中
 输入参数  : int c, HI_FILE_S *p_stream
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_fputc (int i_c, HI_FILE_S *p_stream)
{
    return fputc(i_c,(FILE *)p_stream);
}

/*****************************************************************************
 函 数 名  : hi_os_fgets
 功能描述  : 从文件中读取一个字符串
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_char8 *hi_os_fgets (hi_char8 * pc_s, hi_int32 i_n, HI_FILE_S * p_stream)
{
    return fgets(pc_s,i_n,(FILE *)p_stream);
}

/*****************************************************************************
 函 数 名  : hi_os_fputs
 功能描述  : 将一个指定字符串写入到文件流中
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_fputs (const hi_char8 * pc_s, HI_FILE_S * p_stream)
{
    return fputs(pc_s,(FILE *)p_stream);
}

/*****************************************************************************
 函 数 名  : hi_os_fread
 功能描述  : 从文件流读取数据
 返 回 值  : 返回实际读到的数目
*****************************************************************************/
hi_uint32 hi_os_fread (hi_void * p_tr, hi_uint32 ui_size,
             hi_uint32 ui_n, HI_FILE_S * p_stream)
{
    return fread(p_tr,ui_size,ui_n,(FILE *)p_stream);
}

/*****************************************************************************
 函 数 名  : hi_os_fread
 功能描述  : 将数据写入文件流
 返 回 值  : 返回实际写入的数目
*****************************************************************************/
hi_uint32 hi_os_fwrite (const hi_void * p_tr, hi_uint32 ui_size,
              hi_uint32 ui_n, HI_FILE_S * p_s)
{
    return fwrite(p_tr,ui_size,ui_n,(FILE *)p_s);
}

/*****************************************************************************
 函 数 名  : hi_os_fseek
 功能描述  : 移动文件流的读写位置
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_fseek (HI_FILE_S *p_stream, hi_int32 i_off, hi_int32 i_whence)
{
    return fseek((FILE *)p_stream,i_off,i_whence);
}

/*****************************************************************************
 函 数 名  : hi_os_ftell
 功能描述  : 取得文件流的读写位置
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_ftell (HI_FILE_S *p_stream)
{
    return ftell((FILE *)p_stream);
}

/*****************************************************************************
 函 数 名  : hi_os_rewind
 功能描述  : 重设文件流的读写位置为文件开头
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_void hi_os_rewind (HI_FILE_S *p_stream)
{
    rewind((FILE *)p_stream) ;
}

/*****************************************************************************
 函 数 名  : hi_os_fgetpos
 功能描述  : 取得文件流的读写位置
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_fgetpos (HI_FILE_S * p_stream, hi_os_fpos_s * pst_pos)
{
    return fgetpos(p_stream,(fpos_t *)(hi_void *)pst_pos);
}

/*****************************************************************************
 函 数 名  : hi_os_fsetpos
 功能描述  : 设置文件流的读写位置
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_fsetpos (HI_FILE_S *p_stream, const hi_os_fpos_s *pst_pos)
{
    return fsetpos((FILE *)p_stream,(fpos_t *)(hi_void *)pst_pos);
}

/*****************************************************************************
 函 数 名  : hi_os_clearerr
 功能描述  : 清除文件流的错误标志
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_void hi_os_clearerr (HI_FILE_S *p_stream)
{
    clearerr((FILE *)p_stream)  ;
}

/*****************************************************************************
 函 数 名  : hi_os_feof
 功能描述  : 检查文件流是否读到了文件尾
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_feof (const HI_FILE_S *p_stream)
{
    return feof((FILE *)(hi_void *)p_stream);
}

/*****************************************************************************
 函 数 名  : hi_os_ferror
 功能描述  : 获取文件流的错误
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_ferror (const HI_FILE_S *p_stream)
{
    return ferror((FILE *)(hi_void *)p_stream);
}

/*****************************************************************************
 函 数 名  : hi_os_ferrorhi_os_fileno
 功能描述  : 返回文件流所使用的文件描述符
 返 回 值  :
             0: 执行成功
            -1:执行失败返回NULL
*****************************************************************************/
hi_int32 hi_os_fileno (const HI_FILE_S *p_stream)
{
    return fileno((FILE *)(hi_void *)p_stream);
}

/*****************************************************************************
 函 数 名  : hi_os_tcgetattr
 功能描述  : 得到终端属性
 输入参数  : hi_int32 i_fd
             hi_os_termios_s* pstTermios
*****************************************************************************/
hi_int32 hi_os_tcgetattr(hi_int32 i_fd, hi_os_termios_s* pst_termios)
{
    return tcgetattr(i_fd, (struct termios *)(hi_void *)pst_termios);
}

/*****************************************************************************
 函 数 名  : hi_os_tcsetattr
 功能描述  : 设置终端属性
 输入参数  : hi_int32 i_fd
             hi_int32 iOptional
             hi_os_termios_s* pstTermios
*****************************************************************************/
hi_int32 hi_os_tcsetattr(hi_int32 i_fd,hi_int32 i_optional,hi_os_termios_s* pst_termios)
{
    return tcsetattr(i_fd,i_optional, (const struct termios *)(hi_void *)pst_termios);
}

/*****************************************************************************
 函 数 名  : hi_os_setsid
 功能描述  : 建立新会话
*****************************************************************************/
hi_int32 hi_os_setsid(hi_void)
{
    return setsid();
}

/*****************************************************************************
 函 数 名  : hi_os_dup2
 功能描述  : 复制现存描述符
 输入参数  : iOldFd: 旧描述符
             iNewFd: 新描述符
*****************************************************************************/
hi_int32 hi_os_dup2(hi_int32 i_oldfd, hi_int32 i_newfd)
{
    return dup2(i_oldfd, i_newfd);
}

/*****************************************************************************
 函 数 名  : hi_os_chmod
 功能描述  : 修改文件访问权限
 输入参数  : pcPath: 文件路径
             iMode : 设置模式
*****************************************************************************/
hi_int32 hi_os_chmod(hi_char8 *pc_path, hi_uint32 ui_mode)
{
    return chmod(pc_path, ui_mode);
}

/*****************************************************************************
 函 数 名  : hi_os_grantpt
 功能描述  : 修改伪终端设备访问权限
 输入参数  : i_fd: 终端设备描述符
*****************************************************************************/
hi_int32 hi_os_grantpt(hi_int32 i_fd)
{
    return grantpt(i_fd);
}

/*****************************************************************************
 函 数 名  : hi_os_unlockpt
 功能描述  : 解锁伪终端从设备
 输入参数  : i_fd: 终端设备描述符
*****************************************************************************/
hi_int32 hi_os_unlockpt(hi_int32 i_fd)
{
    return unlockpt(i_fd);
}

/*获取当前绝对路径*/
hi_char8 *hi_os_getcwd( hi_char8 *pc_buf, hi_int32 ui_size )
{
    return getcwd(pc_buf,ui_size);
}

#ifdef __cplusplus
#if __cplusplus
}
#endif /* __cpluscplus */
#endif /* __cpluscplus */
