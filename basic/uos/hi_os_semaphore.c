/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_os_semaphore.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_08_02
  最近修改   :

******************************************************************************/
#include "securec.h"
#include "hi_errno.h"
#include "hi_typedef.h"
#include "os/hi_os_sem.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

/*****************************************************************************
 函 数 名  : hi_os_sem_init
 功能描述  : 初始化信号量。用于进程内。与hi_os_sem_wait和
                          hi_os_sem_post配合使用
 输入参数  : pstSem：信号量
                           iShared：信号量的类型，如果它等于0，
                                          表示它是当前进程的局部信号量；否则，
                                          其他进程就能够共享这个信号量。
                           ui_value:信号量的值
                           uiMaxValue：信号量的最大值
*****************************************************************************/

hi_int32 hi_os_sem_init(hi_os_sem_s *pst_sem, hi_int32 i_shared, hi_uint32 ui_value, hi_uint32 ui_maxvalue)
{
    return sem_init((sem_t *)pst_sem,i_shared,ui_value);
}

/*****************************************************************************
 函 数 名  : hi_os_sem_open
 功能描述  : 打开信号量。如果没有就创建一个新的。
                          用于进程间。与hi_os_sem_wait和hi_os_sem_post、
                          hi_os_sem_close配合使用
 输入参数  :
                         hi_uchar8 *pc_name：信号量的名字
                         hi_int32 iOFlag：操作控制标志。
                         hi_uint32 mode：信号量的权限。
                         hi_uint32 ui_value：信号量的初始值。
                         hi_uint32 uiMaxValue：信号量的最大值（兼容WIN32）。
                         hi_uint32 uiInheritHandle：指定所返回的句柄在新进程创建时能否继承。TRUE值表示创建的新进程可以继承该句柄。（兼容WIN32）
 返 回 值  :     成功：返回一个新信号量的地址
                 失败：返回返回空指针，并将错误保存在errno中。
*****************************************************************************/
hi_os_sem_s *hi_os_sem_open(const hi_char8 *pc_name, hi_int32 i_operateflag, hi_uint32 ui_mode,hi_uint32 ui_value,hi_uint32 ui_maxvalue,hi_uint32 ui_inherithandle)
{
    return (hi_os_sem_s *)sem_open(pc_name,i_operateflag,ui_mode,ui_value);
}

/*****************************************************************************
 函 数 名  : hi_os_sem_close
 功能描述  : 关闭一个有名的信号量。用于进程间。
 输入参数  : pstSem：信号量
 返 回 值  :
                         成功：返回HI_OK
                         失败：返回-1，并将错误保存在errno中
*****************************************************************************/
hi_int32 hi_os_sem_close(const hi_os_sem_s *pst_sem)
{
    return sem_close((sem_t *)pst_sem);
}

/*****************************************************************************
 函 数 名  : hi_os_sem_unlink
 功能描述  : 删除一个有名的信号量。信号量的名字会被立刻删除，
                          信号量会在所有用到这个信号量的进程关闭了该信号量后才被销毁。
 输入参数  : pc_name：信号量的名字
 返 回 值  :
                         成功：返回HI_OK
                         失败：返回-1，并将错误保存在errno中
*****************************************************************************/
hi_int32 hi_os_sem_unlink(const hi_char8 * pc_name)
{
    return sem_unlink(pc_name);
}

/*****************************************************************************
 函 数 名  : hi_os_sem_destroy
 功能描述  : 在用完信号量后对它进行清理，归还信号量占用的一切资源。
                          在清理信号量的时候如果还有线程在等待它，用户会收到一个错误。
 输入参数  : pstSem：信号量
 输出参数  :
 返 回 值  :
                         成功：返回HI_OK
                         失败：返回-1，并将错误保存在errno中
*****************************************************************************/
hi_int32 hi_os_sem_destroy(hi_os_sem_s *pst_sem)
{
    return sem_destroy((sem_t *)pst_sem);
}

/*****************************************************************************
 函 数 名  : hi_os_sem_wait
 功能描述  : 函数是一个原子操作，它的作用是从信号量的值减去一个"1"，
                          但是它永远会先等待该信号量为一个非零值才开始做减法。
 输入参数  : pstSem：信号量
 输出参数  :
 返 回 值  :
                         成功：返回HI_OK
                         失败：返回-1，并将错误保存在errno中
*****************************************************************************/
hi_int32 hi_os_sem_wait(const hi_os_sem_s *pst_sem)
{
    return sem_wait((sem_t *)pst_sem);
}

/*****************************************************************************
 函 数 名  : hi_os_sem_trywait
 功能描述  : hi_os_sem_trywait是hi_os_sem_wait的非阻塞搭档。
                          它只有在信号量大于0的时候减1并返回0，否则立即返回-1。
                          它不用象hi_os_sem_wait等待信号量大于0。
 输入参数  : pstSem：信号量
 输出参数  :
 返 回 值  :
                         成功：返回HI_OK
                         失败：返回-1，并将错误保存在errno中
*****************************************************************************/
hi_int32 hi_os_sem_trywait(const hi_os_sem_s *pst_sem)
{
    return sem_trywait((sem_t *)pst_sem);
}

/*****************************************************************************
 函 数 名  : hi_os_sem_trywait
 功能描述  : 信号量的V操作。
                          函数的作用是给信号量的值加上一个1，它是一个原子操作。
 输入参数  :
                          hi_os_sem_s *sem：信号量
                          hi_uint32 ulSemNum：信号量的释放计数,该值必须大于0，并且小于为信号量指定的最大值。释放信号量时，信号量的当前计数会增加ulSemNum指定的值。任何在该信号量上等待的线程都会检查当前的计数值是否满足它们的等待条件，如果满足，它们将等待返回（兼容WIN32）
 返 回 值  :
                         成功：返回HI_OK
                         失败：返回-1，并将错误保存在errno中
*****************************************************************************/
hi_int32 hi_os_sem_post(const hi_os_sem_s *pst_sem , hi_uint32 ui_semnum)
{
    return sem_post((sem_t *)pst_sem);
}

/*****************************************************************************
 函 数 名  : hi_os_semctl
 功能描述  : 控制信号队列的运作
 输入参数  : hi_int32 i_semid   :信号量标识符
             hi_int32 i_semnum  :信号量编号，当需要用到成组的信号量时，
                                           用到该参数。一般取值为0，表示唯一的一个
                                           信号量。
             hi_int32 i_cmd     :采取的动作
             hi_os_semun_u u_arg   :参数
*****************************************************************************/
hi_int32 hi_os_semctl(hi_int32 i_semid, hi_int32 i_semnum, hi_int32 i_cmd, hi_os_semun_u u_arg)
{
    return semctl(i_semid,i_semnum,i_cmd,u_arg);
}

/*****************************************************************************
 Function name: hi_os_semget
 Description  : wrap for hi_os_semget in Linux only，用于进程间或者进程内。
                    与hi_os_i_semop和hi_os_semctl配合使用。
*****************************************************************************/
hi_int32 hi_os_semget(hi_int32 i_key,hi_int32 i_nsems, hi_int32 i_semflg)
{
    return semget(i_key, i_nsems, i_semflg);
}

/*****************************************************************************
 Function name: hi_os_i_semop
 Description  : wrap for hi_os_i_semop in Linux only
*****************************************************************************/
hi_int32 hi_os_semop(hi_int32 i_semid, hi_os_sembuf_s *sops, hi_int32 i_ops)
{
    return semop(i_semid, (struct sembuf *)(hi_void *)sops ,(hi_uint32)i_ops);
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
