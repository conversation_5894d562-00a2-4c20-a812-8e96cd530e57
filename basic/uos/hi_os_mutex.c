/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_os_mutex.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_08_02
  最近修改   :

******************************************************************************/
#include "securec.h"
#include "hi_errno.h"
#include "hi_typedef.h"
#include "os/hi_os_thread.h"
#include "os/hi_os_mutex.h"

#ifdef  __cplusplus
#if  __cplusplus
    extern "C"{
#endif
#endif

/*****************************************************************************
 函 数 名  : hi_os_pthread_mutex_init
 功能描述  : 初始化互斥量。
*****************************************************************************/
hi_int32 hi_os_pthread_mutex_init (hi_os_pthread_mutex_s *pst_mutex, const hi_os_pthread_mutex_attr_s *pst_mutexattr)
{
    return pthread_mutex_init( (pthread_mutex_t *)pst_mutex, (pthread_mutexattr_t *)pst_mutexattr);
}

/*****************************************************************************
 函 数 名  : hi_os_pthread_mutex_destroy
 功能描述  : 删除指定的互斥量。
*****************************************************************************/
hi_int32 hi_os_pthread_mutex_destroy (hi_os_pthread_mutex_s *pst_mutex)
{
    return pthread_mutex_destroy((pthread_mutex_t *)pst_mutex);
}


/*****************************************************************************
 函 数 名  : hi_os_pthread_mutex_trylock
 功能描述  : 非阻塞锁定互斥量。
*****************************************************************************/
hi_int32 hi_os_pthread_mutex_trylock (hi_os_pthread_mutex_s *pst_mutex)
{
    return pthread_mutex_trylock((pthread_mutex_t *)pst_mutex);
}

/*****************************************************************************
 函 数 名  : hi_os_pthread_mutex_lock
 功能描述  :将互斥量锁定
*****************************************************************************/
hi_int32 hi_os_pthread_mutex_lock (hi_os_pthread_mutex_s *pst_mutex)
{
    return pthread_mutex_lock((pthread_mutex_t *)pst_mutex);
}

/*****************************************************************************
 函 数 名  : hi_os_pthread_mutex_unlock
 功能描述  :将互斥量解锁
*****************************************************************************/
hi_int32 hi_os_pthread_mutex_unlock (hi_os_pthread_mutex_s *pst_mutex)
{
    return pthread_mutex_unlock((pthread_mutex_t *)pst_mutex);
}


#ifdef __cplusplus
#if __cplusplus
    }
#endif
#endif  /* end of __cplusplus */
