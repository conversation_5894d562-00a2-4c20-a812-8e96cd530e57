/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_os_time.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_08_03
  最近修改   :

******************************************************************************/
#include <stdlib.h>
#include "securec.h"
#include "hi_errno.h"
#include "hi_typedef.h"
#include "os/hi_os_err.h"
#include "os/hi_os_time.h"
#include <unistd.h>

#ifdef  __cplusplus
#if  __cplusplus
extern "C"{
#endif
#endif


/*****************************************************************************
 函 数 名  : hi_os_srand
 功能描述  : 设置随机数种子
*****************************************************************************/
hi_void hi_os_srand(hi_uint32 ui_seed)
{
    srand(ui_seed);
}

/*****************************************************************************
 函 数 名  : hi_os_random
 功能描述  : 获取随机数。
*****************************************************************************/
hi_uint32 hi_os_random(hi_void)
{
    return (hi_uint32)random();
}

#define MAX_BUF_SIZE 256
static void timezone_sync(hi_void)
{
    char cur_timezone[MAX_BUF_SIZE] = {0};
    int ret = readlink("/etc/localtime", cur_timezone, MAX_BUF_SIZE);
    if (ret < 0 || ret >= MAX_BUF_SIZE) {
        printf("read /etc/localtime error!\n");
        return;
    }
    setenv("TZ", cur_timezone, 1);
    return;
}

/*****************************************************************************
 函 数 名  : hi_os_localtime
 功能描述  : 获取系统的本地时间和日期，时间和日期已经转换成当地时区。
 输入参数  : ptime：表示系统的相对时间秒数的指针
*****************************************************************************/
hi_os_tm_s *hi_os_localtime(const time_t *pi_time)
{
    timezone_sync();
    return (hi_os_tm_s*)(hi_void *)localtime(pi_time);
}

/*****************************************************************************
 函 数 名  : hi_os_settimeofday
 功能描述  : 设置目前时间
 输入参数  : HI_OS_TIMEVAL_S *pstTimeVal时间数据
             hi_os_time_zone_s* pstTimeZone当地时区
 返 回 值  :  成功则返回0，失败则返回-1，
              错误原因存于errno中。
*****************************************************************************/
hi_uint32 hi_os_settimeofday(hi_os_timeval_s *pst_time_val,hi_os_time_zone_s* pst_time_zone)
{
    struct timeval tmval;
    tmval.tv_sec = pst_time_val->tv_sec;
    tmval.tv_usec = pst_time_val->tv_usec;
    return settimeofday(&tmval,(struct timezone*)pst_time_zone);
}

/*****************************************************************************
 函 数 名  : hi_os_gettime
 功能描述  : 函数获取的是目前UTC的时间日期，即格林尼治时间，
                          是没有经过时区转换的时间日期。
 输出参数  :pstCurdate： 保存时间日期数据
*****************************************************************************/
hi_void hi_os_gettime(hi_os_tm_s * pst_curdate)
{
    time_t  st_timenow;
    if ( NULL == pst_curdate )
    {
        return;
    }
    (hi_void)time(&st_timenow);
    (void)memcpy_s(pst_curdate, sizeof(hi_os_tm_s),gmtime(&st_timenow),sizeof(hi_os_tm_s));
}

/*****************************************************************************
 函 数 名  : hi_os_getsystemtime
 功能描述  : 获取系统的当前时间。
             linux下为当前系统时间自00:00:00 UTC, January 1, 1970 以来经过的时间，
             保存在hi_os_timeVal_S结构中，秒和微秒。
 输出参数  :pstTv：当前的时间
*****************************************************************************/
hi_int32 hi_os_gettimeofday(hi_os_timeval_s* pst_tv)
{
    int ret;
    struct timeval tv;
    ret = gettimeofday(&tv, NULL);
    pst_tv->tv_sec = tv.tv_sec;
    pst_tv->tv_usec = tv.tv_usec;
    return ret;
}

/*****************************************************************************
 函 数 名  : hi_os_gettimeofday_ex
 功能描述  : 获取系统的当前时间。
             linux下为当前系统时间自00:00:00 UTC, January 1, 1970 以来经过的时间，
             保存在hi_os_timeVal_S结构中，秒和微秒。支持时区；
 输出参数  :pstTv：当前的时间
*****************************************************************************/
hi_int32 hi_os_gettimeofday_ex(hi_os_timeval_s* pst_tv, hi_os_time_zone_s *pst_zone)
{
    int ret;
    struct timeval tv;
    ret = gettimeofday(&tv, (struct timezone *)pst_zone);
    pst_tv->tv_sec = tv.tv_sec;
    pst_tv->tv_usec = tv.tv_usec;
    return ret;
}

/*****************************************************************************
 函 数 名  : hi_os_getsystemtime
 功能描述  : 获取系统的相对时间。
             linux下为当前系统时间自00:00:00 UTC, January 1, 1970 以来经过毫秒数。
             注意：这里由于毫秒数会溢出，在10000000毫秒后会重新计算
             所以本函数主要是用于记录相对时间（兼容以前的，两次相减可以得到运行了多长时间），
             并不是真正的当前系统时间
             WIN32下为开机以来的毫秒数。
*****************************************************************************/
hi_uint32 hi_os_getsystemtime(hi_void)
{
    struct timespec now;
    clock_gettime(CLOCK_MONOTONIC, &now);
    return (unsigned int)(now.tv_sec%10000000*1000+now.tv_nsec/1000000);
}

/*****************************************************************************
 函 数 名  : hi_os_gettimespan(us)
 功能描述  : 获取系统的相对时间(微秒)。
             用于3600秒内的时间间隔计算，超过3600秒间隔计算会出错。
             ui_anchor 锚点时间;
             ui_anchor 为0 用于获取锚点时间
*****************************************************************************/
hi_uint32 hi_os_gettimespan(hi_uint32 ui_anchor)
{
    hi_uint32 ui_cur;
    struct timespec now;
    clock_gettime(CLOCK_MONOTONIC, &now);

    ui_cur = ((now.tv_sec&0x7FF)*1000000 + now.tv_nsec/1000);

    return (ui_cur>=ui_anchor)?(ui_cur-ui_anchor):(0x800*1000000-ui_anchor+ui_cur);
}

/*
    获取系统启动后的执行时间
*/
time_t hi_os_sysuptime(void)
{
    struct timespec now;
    clock_gettime(CLOCK_MONOTONIC, &now);

    return now.tv_sec;
}

time_t hi_os_sysup_militime(void)
{
    struct timespec now;
    clock_gettime(CLOCK_MONOTONIC, &now);

    return now.tv_sec * 1000 + now.tv_nsec / 1000000;
}

/*****************************************************************************
 函 数 名  : hi_os_ctime
 功能描述  : 将时间和日期转换成字符串。
 输入参数  : pstTime：时间和日期的详细信息
 返 回 值  :  返回时间日期的字符串。返回后字符串如下格式：
                        Sat Oct 28 10:12:05 2009
*****************************************************************************/
hi_char8 *hi_os_ctime(const time_t *pi_time)
{
    return ctime(pi_time);
}

/*****************************************************************************
 函 数 名  : hi_os_strftime
 功能描述  : 将时间和日期格式化成字符串。
 输入参数  : uiLen：保存结果的字符串的长度。
                         pcFormat：输出格式，具体格式说明参见上述详细说明。
                         pstTime：要格式化时间日期结构。

 输出参数  :pcStrResult：保存结果的字符串。

 返 回 值  :  返回复制到参数pcStrResult所指的字符串数组的总字符数，
                        不包括字符串结束符号。如果返回0，表示未复制字符串
                        到参数pcStrResult内。
*****************************************************************************/
hi_uint32 hi_os_strftime(hi_char8*pc_result,hi_uint32 ui_len,
                         const hi_char8 *pc_format,  const hi_os_tm_s *pst_time)
{
    return strftime(pc_result,ui_len,pc_format,(const struct tm *)(hi_void *)pst_time);
}

/*****************************************************************************
 函 数 名  : hi_os_time
 功能描述  : 此函数会返回从公元1970 年1 月1 日的UTC时间从0 时0 分0 秒
                         算起到现在所经过的秒数。如果t 并非空指针的话，
                         此函数也会将返回值存到t指针所指的内存。
 输入参数  : hi_uint32 *time   如果t 并非空指针的话，
                          此函数也会将返回值存到t指针所指的内存。
 返 回 值  :  成功则返回秒数，失败则返回（（time_t）-1）值，
                        错误原因存于errno中。
*****************************************************************************/
time_t hi_os_time(time_t *ui_time)
{
	time_t tm = {0};
	time(&tm);
	if (ui_time != NULL) {
		*ui_time = tm;
	}
    return tm;
}

/*****************************************************************************
 函 数 名  : hi_os_gmtime
 功能描述  : 取得目前时间和日期。
                          将参数timep 所指的time_t 结构中的信息转换成真实世界
                 所使用的时间日期表示方法，然后将结果返回。
 输入参数  : const hi_uint32 *pi_time  :要转换的时间
 返 回 值  :   返回目前UTC时间
*****************************************************************************/
hi_os_tm_s *hi_os_gmtime(const time_t *pi_time)
{
    return (hi_os_tm_s *)(hi_void *)gmtime(pi_time);
}

/*****************************************************************************
 函 数 名  : hi_os_mktime
 功能描述  : 将时间结构数据转换成经过的秒数
 返 回 值  :  成功则返回秒数，失败则返回（（time_t）-1）值，
*****************************************************************************/
hi_uint32 hi_os_mktime(hi_os_tm_s *pst_time)
{
    return mktime((struct tm*)(hi_void *)pst_time);
}

hi_uint32 hi_os_timetogm(const hi_os_tm_s * pst_timer, hi_os_tm_s * pst_timerout)
{
    hi_uint32 i_timesec;

    struct timeb st_systime;
    struct tm * pst_systm;

    if ((NULL == pst_timer) || (NULL == pst_timerout))
    {
        return (hi_uint32)HI_ERR_OS_EINVAL;
    }

    (hi_void)ftime(&st_systime); /* 取得系统时区 */

    i_timesec = mktime((struct tm *)(hi_void *)pst_timer);  /* 将输入时间转化为秒 */
    if (0 == i_timesec)
    {
        return (hi_uint32)HI_ERR_OS_EINVAL;
    }

    i_timesec += (st_systime.timezone * HI_TIME_SEC_PER_MIN);    /* 与系统时区进行差计算 */
    pst_systm = localtime((time_t *)&i_timesec); /* 将计算结果再转化为TM结构 */
    if (NULL == pst_systm)
    {
        return (hi_uint32)HI_ERR_OS_EINVAL;
    }

    /* 输出结果 */
    pst_timerout->ui_sec = (hi_uint32)pst_systm->tm_sec;
    pst_timerout->ui_min = (hi_uint32)pst_systm->tm_min;
    pst_timerout->ui_hour = (hi_uint32)pst_systm->tm_hour;
    pst_timerout->ui_mday = (hi_uint32)pst_systm->tm_mday;
    pst_timerout->ui_mon = (hi_uint32)pst_systm->tm_mon;
    pst_timerout->ui_year = (hi_uint32)pst_systm->tm_year;
    pst_timerout->ui_wday = (hi_uint32)pst_systm->tm_wday;
    pst_timerout->ui_yday = (hi_uint32)pst_systm->tm_yday;
    pst_timerout->ui_isdst = (hi_uint32)pst_systm->tm_isdst;

    return HI_RET_SUCC;
}

hi_uint32 hi_os_timetolocal(const hi_os_tm_s * pst_timer, hi_os_tm_s * pst_timerout)
{
    time_t timesec;

    struct timeb st_systime;
    struct tm * pst_systm;

    if ((NULL == pst_timer) || (NULL == pst_timerout))
    {
        return (hi_uint32)HI_ERR_OS_EINVAL;
    }

    (hi_void)ftime(&st_systime);         /* 取得系统时区 */

    timesec = mktime((struct tm *)(hi_void *)pst_timer);       /* 将输入时间转化为秒 */
    if (0 == timesec)
    {
        return (hi_uint32)HI_ERR_OS_EINVAL;
    }

    timesec -= (st_systime.timezone * HI_TIME_SEC_PER_MIN);    /* 与系统时区进行差计算 */
    pst_systm = localtime(&timesec);      /* 将计算结果再转化为TM结构 */
    if (NULL == pst_systm)
    {
        return (hi_uint32)HI_ERR_OS_EINVAL;
    }

    /* 输出结果 */
    pst_timerout->ui_sec = (hi_uint32)pst_systm->tm_sec;
    pst_timerout->ui_min = (hi_uint32)pst_systm->tm_min;
    pst_timerout->ui_hour = (hi_uint32)pst_systm->tm_hour;
    pst_timerout->ui_mday = (hi_uint32)pst_systm->tm_mday;
    pst_timerout->ui_mon = (hi_uint32)pst_systm->tm_mon;
    pst_timerout->ui_year = (hi_uint32)pst_systm->tm_year;
    pst_timerout->ui_wday = (hi_uint32)pst_systm->tm_wday;
    pst_timerout->ui_yday = (hi_uint32)pst_systm->tm_yday;
    pst_timerout->ui_isdst = (hi_uint32)pst_systm->tm_isdst;

    return HI_RET_SUCC;
}

/*****************************************************************************
 Function name: hi_os_timetodst
 Description  : 将给定时间转化为DST时间，即加一个小时
*****************************************************************************/
hi_uint32 hi_os_timetodst(const hi_os_tm_s * pst_timer, hi_os_tm_s * pst_timerout)
{
    time_t timesec;
    struct tm * pst_systm;

    if ((NULL == pst_timer) || ((NULL == pst_timerout)))
    {
        return (hi_uint32)HI_ERR_OS_EINVAL;
    }

    timesec = mktime((struct tm *)(hi_void *)pst_timer);       /* 将输入时间转化为秒 */
    if (0 == timesec)
    {
        return (hi_uint32)HI_ERR_OS_EINVAL;
    }

    timesec += HI_SEC_IN_HOUR;

    pst_systm = localtime(&timesec);      /* 将计算结果再转化为TM结构 */
    if (NULL == pst_systm)
    {
        return (hi_uint32)HI_ERR_OS_EINVAL;
    }

    /* 输出结果 */
    pst_timerout->ui_sec = (hi_uint32)pst_systm->tm_sec;
    pst_timerout->ui_min = (hi_uint32)pst_systm->tm_min;
    pst_timerout->ui_hour = (hi_uint32)pst_systm->tm_hour;
    pst_timerout->ui_mday = (hi_uint32)pst_systm->tm_mday;
    pst_timerout->ui_mon = (hi_uint32)pst_systm->tm_mon;
    pst_timerout->ui_year = (hi_uint32)pst_systm->tm_year;
    pst_timerout->ui_wday = (hi_uint32)pst_systm->tm_wday;
    pst_timerout->ui_yday = (hi_uint32)pst_systm->tm_yday;
    pst_timerout->ui_isdst = (hi_uint32)pst_systm->tm_isdst;

    return HI_RET_SUCC;
}

/*****************************************************************************
 Function name: hi_os_dsttotime
 Description  : 将给定时间转化为非DST时间，即减一个小时
*****************************************************************************/
hi_uint32 hi_os_dsttotime(const hi_os_tm_s * pst_timer, hi_os_tm_s * pst_timerout)
{

    time_t timesec;
    struct tm * pst_systm;

    if ((NULL == pst_timer) || ((NULL == pst_timerout)))
    {
        return (hi_uint32)HI_ERR_OS_EINVAL;
    }

    timesec = mktime((struct tm *)(hi_void *)pst_timer);       /* 将输入时间转化为秒 */
    if (0 == timesec)
    {
        return (hi_uint32)HI_ERR_OS_EINVAL;
    }

    timesec -= HI_SEC_IN_HOUR;

    pst_systm = localtime(&timesec);      /* 将计算结果再转化为TM结构 */
    if (NULL == pst_systm)
    {
        return (hi_uint32)HI_ERR_OS_EINVAL;
    }

    /* 输出结果 */
    pst_timerout->ui_sec = (hi_uint32)pst_systm->tm_sec;
    pst_timerout->ui_min = (hi_uint32)pst_systm->tm_min;
    pst_timerout->ui_hour = (hi_uint32)pst_systm->tm_hour;
    pst_timerout->ui_mday = (hi_uint32)pst_systm->tm_mday;
    pst_timerout->ui_mon = (hi_uint32)pst_systm->tm_mon;
    pst_timerout->ui_year = (hi_uint32)pst_systm->tm_year;
    pst_timerout->ui_wday = (hi_uint32)pst_systm->tm_wday;
    pst_timerout->ui_yday = (hi_uint32)pst_systm->tm_yday;
    pst_timerout->ui_isdst = (hi_uint32)pst_systm->tm_isdst;

    return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
    }
#endif
#endif  /* end of __cplusplus */
