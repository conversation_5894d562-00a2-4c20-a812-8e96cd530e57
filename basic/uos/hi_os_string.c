/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_os_string.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_08_04
  最近修改   :

******************************************************************************/
#include <ctype.h>
#include <string.h>
#include "securec.h"
#include "hi_errno.h"
#include "hi_comdef.h"
#include "hi_typedef.h"
#include "os/hi_os_mem.h"
#include "os/hi_os_string.h"

#ifdef  __cplusplus
#if  __cplusplus
extern "C"{
#endif
#endif


/*****************************************************************************
 函 数 名  : hi_os_strlen
 功能描述  : 获取字符串长度，不包括结束字符“\0”
 输入参数  : 指向字符串的指针
 返 回 值  : hi_uint32: 返回字符串的长度；指针为空，则返回0
*****************************************************************************/
hi_uint32  hi_os_strlen( const hi_char8 *pc_str )
{
    return strlen(pc_str);
}

/*****************************************************************************
 函 数 名  : hi_os_strcpy
 功能描述  : 字符串拷贝，会将源字符串拷贝至目的地址。
 输入参数  : 指向拷贝的源字符串和目的字符串的指针
 返 回 值  :返回参数dest的字符串起始地址。
*****************************************************************************/
hi_char8*  hi_os_strcpy( hi_char8 *pc_dest, const hi_char8 *pc_src )
{
    return (hi_char8*)strcpy(pc_dest,pc_src);
}

/*****************************************************************************
 函 数 名  : hi_os_strcpy_s
 功能描述  : 字符串拷贝，会将源字符串拷贝至目的地址。
                           源字符串必须要有结束符。
 输入参数  : 指向拷贝的源字符串和目的字符串的指针
                           destmax: 目的缓冲区可用大小(包括结束符)
 返 回 值  : 0       ok
                        34     destmax == 0 or destmax out range
                        22     pc_dest == null
                        150   strsrc == null'
                        162   strSrc length + 1 is bigger than destMax
                        182   dest, src overlap
*****************************************************************************/
hi_int32 hi_os_strcpy_s( hi_char8 *pc_dest, hi_uint32 ui_destmax, const hi_char8 *pc_src )
{
    return strcpy_s(pc_dest, ui_destmax, pc_src);
}


/*****************************************************************************
 函 数 名  : hi_os_strncpy
 功能描述  : 字符串拷贝，将参数pcSrc 字符串拷贝前uiNum个字符至参数pcDest所指的地址。
 输入参数  : 指向拷贝的源字符串和目的字符串的指针，拷贝的数目
 返 回 值  : 返回参数pcDest的字符串起始地址
*****************************************************************************/
hi_char8* hi_os_strncpy( hi_char8 *pc_dest, const hi_char8 *pc_src,
                                                          hi_uint32 ui_num)
{
    return (hi_char8*)strncpy(pc_dest,pc_src,ui_num);
}

/*****************************************************************************
 函 数 名  : hi_os_strncpy_s
 功能描述  : 字符串拷贝，将参数pcSrc 字符串拷贝前uiNum个字符至参数pcDest所指的地址。
                           源字符串必须要有结束符。
 输入参数  : 指向拷贝的源字符串和目的字符串的指针，拷贝的数目
                           destmax: 目的缓冲区可用大小(包括结束符)
 返 回 值  : 0       ok
                        34     destmax == 0 or destmax out range
                        22     pc_dest == null
                        150   strsrc == null'
                        162   strSrc length + 1 is bigger than destMax
                        182   dest, src overlap
*****************************************************************************/
hi_int32 hi_os_strncpy_s( hi_char8 *pc_dest, hi_uint32 ui_destmax, const hi_char8 *pc_src,hi_uint32 ui_num)
{
    return strncpy_s(pc_dest, ui_destmax, pc_src, ui_num);
}

/*****************************************************************************
 函 数 名  : hi_os_strcmp
 功能描述  :用来比较参数pcStr1 和pcStr2 字符串,大小写敏感。字符串大小的比较是以
            ASCII 码表上的顺序来决定，此顺序亦为字符 的值。首先将两个字符串的第
            一个字符值，若差值为0，则再继续比较下个字符，直到找到不相等的字符为
            止。
 输入参数  : 需要比较的两个字符串的指针
 返 回 值  : 若参数pucStr1 和pucStr2字符串相同则返回0。pucStr1若大于pucStr2则
             返回大于0的值。pucStr1若小于pucStr2则返回小于0 的值。
*****************************************************************************/
hi_int32 hi_os_strcmp( const hi_char8 *pc_str1, const hi_char8 *pc_str2 )
{
    return strcmp(pc_str1,pc_str2);
}

/*****************************************************************************
 函 数 名  : hi_os_strncmp
 功能描述  : 字符串的前uiNum个字符比较，大小写敏感。字符串大小的比较是以ASCII 码
             表上的顺序来决定，此顺序亦为字符的值。首先将两个字符串的第一个字符值，
             若差值为0，则再继续比较下个字符，直到找到不相等的字符为止。
 输入参数  : 需要比较的两个字符串的指针和个数
 返 回 值  :  若参数pucStr1 和pucStr2字符串的前i_count个字符相同则返回0。pucStr1
              若大于pucStr2则返回大于0的值。pucStr1若小于pucStr2则返回小于0 的值。
*****************************************************************************/
hi_int32 hi_os_strncmp(const hi_char8 *pc_str1, const hi_char8 *pc_str2, hi_uint32 ui_num)
{
    return strncmp(pc_str1,pc_str2,ui_num);
}

/*****************************************************************************
  函 数 名  : hi_os_strcasecmp
  功能描述  :用来比较参数pcstr1 和pcstr2 字符串,大小写不敏感。字符串大小的比较
             是以ASCII 码表上的顺序来决定，此顺序亦为字符 的值。首先将两个字符串
             的第一个字符值，若差值为0，则再继续比较下个字符，直到找到不相等的
             字符为止。
  输入参数  : 需要比较的两个字符串的指针
  返 回 值  : 若参数pcstr1 和pcstr2字符串相同则返回0。pcstr1若大于pcstr2则返回
              大于0的值。pcStr1若小于pcStr2则返回小于0的值。
*****************************************************************************/
hi_int32 hi_os_strcasecmp(const hi_char8 *pc_str1, const hi_char8 *pc_str2)
{
    return strcasecmp(pc_str1,pc_str2);
}

/*****************************************************************************
 函 数 名  : hi_os_strncasecmp
 功能描述  : 字符串的前uiNum个字符比较，大小写不敏感。字符串大小的比较是以ASCII
             码表上的顺序来决定，此顺序亦为字符的值。首先将两个字符串的第一个字
             符值，若差值为0，则再继续比较下个字符，直到找到不相等的字符为止。
 输入参数  : 需要比较的两个字符串的指针和个数
 返 回 值  :  若参数pcStr1 和pcStr2字符串的前uiNum个字符相同则返回0，pcStr1，
              若大于pcStr2则返回大于0的值。pcStr1若小于pcStr2则返回小于0 的值。
*****************************************************************************/
hi_int32 hi_os_strncasecmp(const hi_char8 *pc_str1, const hi_char8 *pc_str2,
                                                    hi_uint32 ui_num)
{
    return strncasecmp(pc_str1,pc_str2,ui_num);
}

/*****************************************************************************
 函 数 名  : hi_os_strstr
 功能描述  : 在字符串中查询子字符串，区分大小写，会从字符串pucString 中搜寻字符串
             puc_key，并将第一次出现的地址返回。
 输入参数  : 字符串和子字符串的指针
 返 回 值  : 返回指定字符串第一次出现的地址，否则返回0。
*****************************************************************************/
hi_char8* hi_os_strstr(const hi_char8 *pc_str, const hi_char8 *pc_key)
{
    return  (hi_char8*)strstr( pc_str, pc_key );
}

hi_char8 *hi_os_strcasestr(const hi_char8 *s1, const hi_char8 *s2)
{
	size_t l1, l2;

	l2 = strlen(s2);
	if (!l2)
		return (char *)s1;
	l1 = strlen(s1);
	while (l1 >= l2) {
		l1--;
		if (!strncasecmp(s1, s2, l2))
			return (char *)s1;
		s1++;
	}
	return NULL;
}
/*****************************************************************************
 函 数 名  : hi_os_strcat
 功能描述  : 字符串合并,将参数pucStrSource字符串拷贝到参数pucStrDestination
                            所指的字符串尾。第一个参数pucStrDestination要有足够的空间
                            来容纳要拷贝的字符串。
 输入参数  :指向两个需要合并的字符串的指针
 返 回 值  :返回参数pucStrDestination的字符串起始地址
*****************************************************************************/
hi_char8* hi_os_strcat( hi_char8* pc_dest, const hi_char8* pc_src)
{
    return (hi_char8 *)strcat(pc_dest,pc_src);
}

/*****************************************************************************
 函 数 名  : hi_os_strcat_s
 功能描述  : 字符串合并,将参数pucStrSource字符串拷贝到参数pucStrDestination
                            所指的字符串尾。第一个参数pucStrDestination要有足够的空间
                            来容纳要拷贝的字符串。第三个参数指向的字符串一定
                            要有结束符。
 输入参数  :指向两个需要合并的字符串的指针
                          destmax: 目的缓冲区可用大小(包括结束符)
 返 回 值  : 0       ok
                        34     destmax == 0 or destmax out range
                        22     pc_dest == null
                        150   strdest in destMax don't have '\0'
                        162   destmax isn't enough for strSrc
                        182   dest, src overlap
*****************************************************************************/
hi_int32 hi_os_strcat_s( hi_char8* pc_dest, hi_uint32 ui_destmax, const hi_char8* pc_src)
{
    return strcat_s(pc_dest, ui_destmax, pc_src);
}

/*****************************************************************************
 函 数 名  : hi_os_strncat
 功能描述  : 将参数pcSrc 字符串拷贝uiNum个字符到参数pcDest 所指的字符串尾。
             第一个参数pcDest要有足够的空间来容纳要拷贝的字符串。
 输入参数  :指向两个需要合并的字符串的指针
 返 回 值  :返回参数pcDest的字符串起始地址。
*****************************************************************************/
hi_char8* hi_os_strncat( hi_char8* pc_dest, const hi_char8* pc_src,hi_uint32 ui_num)
{
    return (hi_char8 *)strncat(pc_dest,pc_src,ui_num);
}

/*****************************************************************************
 函 数 名  : hi_os_strncat_s
 功能描述  : 将参数pcSrc 字符串拷贝uiNum个字符到参数pcDest 所指的字符串尾。
             第一个参数pcDest要有足够的空间来容纳要拷贝的字符串。第三个参数
             指向的字符串一定要有结束符。
 输入参数  :指向两个需要合并的字符串的指针
                          destmax: 目的缓冲区可用大小(包括结束符)
 返 回 值  : 0       ok
                        34     destmax == 0 or destmax out range
                        22     pc_dest == null
                        150   strdest in destMax don't have '\0'
                        162   destmax isn't enough for strSrc
                        182   dest, src overlap
*****************************************************************************/
hi_int32 hi_os_strncat_s( hi_char8* pc_dest, hi_uint32 ui_destmax, const hi_char8* pc_src,hi_uint32 ui_num)
{
    return strncat_s(pc_dest, ui_destmax, pc_src, ui_num);
}


/*****************************************************************************
 函 数 名  : hi_os_strconcat
 功能描述  : 串接多个字符串到指定字符串。
 返 回 值  : 返回参数pcStr1的字符串起始地址
*****************************************************************************/
hi_char8 *hi_os_strconcat(const hi_char8 *pc_buf, ...)
{
    hi_uint32 ui_len;
    va_list args;
    hi_char8 *pc_str;
    hi_char8 *pc_concat;
    hi_char8 *pc_strtmp;

    ui_len = 1 + strlen(pc_buf);
    HI_VA_START(args, pc_buf);
    pc_str = HI_VA_ARG(args, hi_char8 *);
    while (0 != strlen(pc_str)) {
        ui_len += strlen(pc_str);
        pc_str = HI_VA_ARG(args, hi_char8 *);
    }
    HI_VA_END(args);

    pc_concat = (hi_char8 *)hi_os_malloc(ui_len);
    if (NULL == pc_concat) {
        return NULL;
    }

    memset_s(pc_concat, ui_len, 0, ui_len);
    pc_strtmp = pc_concat;
    if (strcat_s(pc_strtmp, ui_len, pc_buf) != EOK) {
        hi_os_free(pc_concat);
        return NULL;
    }
    HI_VA_START(args, pc_buf);
    pc_str = HI_VA_ARG(args, hi_char8 *);

    while (0 != hi_os_strlen(pc_str)) {
        if (strcat_s(pc_strtmp, ui_len, pc_str) != EOK) {
            hi_os_free(pc_concat);
            return NULL;
        }
        pc_str = HI_VA_ARG(args, hi_char8 *);
    }

    HI_VA_END(args);

    return pc_concat;
}

/*****************************************************************************
 函 数 名  : hi_os_toupper
 功能描述  : 将字符串转为全大写
*****************************************************************************/
hi_char8* hi_os_toupper(hi_char8 *pc_str)
{
    hi_char8* pc_tmp;

    if ( NULL == pc_str)
    {
       return NULL;
    }

    pc_tmp = pc_str;

    while (*pc_tmp != '\0')
    {
       *pc_tmp = (hi_char8)toupper(*pc_tmp);
       pc_tmp++;
    }

    return pc_str;
}

hi_char8 *hi_os_tolower(hi_char8 *str)
{
    hi_char8 *tmp = str;
    if (NULL == str) {
       return NULL;
    }

    while (*tmp != '\0') {
       *tmp = (hi_char8)tolower(*tmp);
       tmp++;
    }

    return str;
}

/*****************************************************************************
 函 数 名  : hi_os_strrepeat
 功能描述  : 字符串复制
*****************************************************************************/
hi_char8* hi_os_strrepeat(const hi_char8* pc_str)
{
    hi_uint32   ui_len;
    hi_char8   *pcDst = NULL;

    if (NULL == pc_str)
    {
        return 0;
    }

    ui_len = hi_os_strlen(pc_str);

    /*  如果为空字符串，也分配空间复制 */
    pcDst = (hi_char8 *)hi_os_malloc(ui_len + 1);
    if (NULL == pcDst)
    {
        return 0;
    }

    hi_os_strcpy(pcDst, pc_str);

    return pcDst;
}

/*****************************************************************************
 函 数 名  : hi_os_atoi
 功能描述  : 字符串转换为整数,扫描参数pcStr 字符串，跳过前面的空格字符，直到遇上数字
             或正负符号才开始做转换，而再遇到非数字或字符串结束时（’\0’）才结束转换，
             并将结果返回。
*****************************************************************************/
hi_uint32 hi_os_atoi(const hi_char8* pc_str, hi_int32 *pi_value)
{
    if ((NULL == pc_str) || (NULL == pi_value))
    {
        return (hi_uint32)HI_RET_FAIL;
    }

    *pi_value = atoi(pc_str);

    return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_os_strtol
 功能描述  :参数pcStr 字符串根据参数uibase 来转换成有符长整型数。参数uibase 范围
            从2 至36，或0。参数uibase 代表采用的进制方式，如uibase值为10 则采用
            10 进制，若uibase值为16 则采用16 进制等。当uibase 值为0 时则是采用10
            进制做转换，但遇到如’0x’前置字符则会使用16进制做转换。一开始会
            扫描参数pcStr 字符串，跳过前面的空格字符，直到遇上数字或正负符号才
            开始做转换，再遇到非数字或字符串结束时(‘\0’)结束转换，并将结果
            返回。
 输出参数  : 转化uibase进制后的有符整型:pi_value
*****************************************************************************/
hi_int32 hi_os_strtol(const hi_char8* pc_str, hi_char8 **ppc_end, hi_int32 i_base)
{
    return strtol(pc_str, ppc_end, i_base);
}

/*****************************************************************************
 函 数 名  : hi_os_strtoul
 功能描述  :参数pcStr 字符串根据参数base 来转换成无符长整型数。参数uibase 范围从2
            至36，或0。参数uibase 代表采用的进制方式，如uibase值为10 则采用10
            进制，若uibase值为16 则采用16 进制等。当uibase 值为0 时则是采用10
            进制做转换，但遇到如’0x’前置字符则会使用16进制做转换。一开始会
            扫描参数pcStr 字符串，跳过前面的空格字符，直到遇上数字或正负符号才
            开始做转换，再遇到非数字或字符串结束时(‘\0’)结束转换，并将结果
            返回。
 返 回 值  :  转化uibase进制后的无符整型
*****************************************************************************/
hi_uint32 hi_os_strtoul(const hi_char8* pc_str, hi_char8 **ppc_end, hi_int32 i_base)
{
    return strtoul(pc_str, ppc_end, i_base);
}

hi_ulong64 hi_os_strtoull(const hi_char8* pc_str, hi_char8 **ppc_end, hi_int32 i_base)
{
    return strtoull(pc_str, ppc_end, i_base);
}

/*****************************************************************************
 函 数 名  : hi_os_strchr
 功能描述  : 查找字符串中字符cChr的位置，并将第一次出现的地址返回
 输入参数  : ULONG  ulUserID  : 用户ID
 输出参数  : 无
 返 回 值  : 返回指定字符串第一次出现的地址，否则返回0。

 修改历史      :
  1.日    期   : 2009年5月7日
    作    者   : l00132877
    修改内容   : 新生成函数

*****************************************************************************/
hi_char8* hi_os_strchr(const hi_char8 *pc_str, hi_char8 c_chr)
{
    return (hi_char8*)strchr(pc_str,c_chr);
}

/*****************************************************************************
 函 数 名  : hi_os_strrchr
 功能描述  : 字符串中最后一个出现的参数cChr地址
 输入参数  : 用来找出参数pcStr字符串中最后一个出现的参数cChr地址，然后将该字符
             出现的地址返回。
 返 回 值  : 如果找到指定的字符则返回该字符所在地址，否则返回0。
*****************************************************************************/
 hi_char8* hi_os_strrchr(const hi_char8* pc_str, hi_char8 c_chr)
{
    return (hi_char8*)strrchr(pc_str,c_chr);
}

/*****************************************************************************
 函 数 名  : hi_os_strtok
 功能描述  : 用来将字符串分割成一个个片段。参数pcStr指向欲分割的字符串，参数
             pcDelim则为分割字符串，当在参数pcStr的字符串中发现到参数pcDelim
             的分割字符时则会将该字符改为\0 字符。在第一次调用时，必需给予参
             数pcStr字符串，往后的调用则将参数pcStr设置成NULL。每次调用成功则
             返回下一个分割后的字符串指针。
 输入参数  : hi_char8* pc_str
             const hi_char8* pc_delim
 返 回 值  : 返回下一个分割后的字符串指针，如果已无从分割则返回NULL。
*****************************************************************************/
hi_char8* hi_os_strtok( hi_char8* pc_str,const hi_char8* pc_delim )
{
    return strtok( pc_str,pc_delim);
}

/*****************************************************************************
 函 数 名  : hi_os_strtok_s
 功能描述  : 用来将字符串分割成一个个片段。参数pcStr指向欲分割的字符串，参数
             pcDelim则为分割字符串，当在参数pcStr的字符串中发现到参数pcDelim
             的分割字符时则会将该字符改为\0 字符。在第一次调用时，必需给予参
             数pcStr字符串，往后的调用则将参数pcStr设置成NULL。每次调用成功则
             返回下一个分割后的字符串指针。pc_context保存调用strtok_s后的位置信息。
 输入参数  : hi_char8* pc_str
                           const hi_char8* pc_delim
                           hi_char8 **pc_context
 返 回 值  : 返回下一个分割后的字符串指针，如果已无从分割则返回NULL。
*****************************************************************************/
hi_char8* hi_os_strtok_s( hi_char8* pc_str,const hi_char8* pc_delim, hi_char8 **pc_context)
{
    return strtok_s( pc_str, pc_delim, pc_context);
}


/*****************************************************************************
 函 数 名  : hi_os_strtok_r
 功能描述  : 用来将字符串分割成一个个片段。参数pcStr指向欲分割的字符串，参数
             pcDelim则为分割字符串，当在参数pcStr的字符串中发现到参数pcDelim
             的分割字符时则会将该字符改为\0 字符。在第一次调用时，必需给予参
             数pcStr字符串，往后的调用则将参数pcStr设置成NULL。每次调用成功则
             返回下一个分割后的字符串指针。
 输入参数  : hi_char8* pc_str
             const hi_char8* pc_delim
 返 回 值  : 返回下一个分割后的字符串指针，如果已无从分割则返回NULL。
*****************************************************************************/
hi_char8* hi_os_strtok_r( hi_char8* pc_str,const hi_char8* pc_delim, hi_char8** ppc_save_ptr )
{
    return strtok_r(pc_str, pc_delim, ppc_save_ptr );
}

/*****************************************************************************
 函 数 名  : hi_os_strsep
 功能描述  : 用来将字符串分割成一个个片段。参数ppcStr指向欲分割的字符串，参数
             pcDelim则为分割字符串，当在参数pcStr的字符串中发现到参数pcDelim
             的分割字符时则会将该字符改为\0 字符。与hi_os_strtok不同的是，此函数
             调用后*ppcStr将指向分割字符串后边的字符串
 输入参数  : hi_char8 **ppc_str
             const hi_char8* pc_delim
 输出参数  : 无
 返 回 值  : hi_char8*
*****************************************************************************/
hi_char8* hi_os_strsep( hi_char8 **ppc_str,const hi_char8* pc_delim )
{
    return strsep( ppc_str,pc_delim);
}

/*****************************************************************************
 函 数 名  : hi_os_strpbrk
 功能描述  : 用来找出参数pcStr字符串中最先出现存在参数pcStrSet字符串中的任意字符。
 输入参数  : const hi_char8* pc_str
             const hi_char8* pc_str_set
 返 回 值  : hi_char8*:如果找到指定的字符则返回该字符所在地址，否则返回0。
*****************************************************************************/
hi_char8* hi_os_strpbrk( const hi_char8* pc_str,const hi_char8* pc_str_set )
{
    return strpbrk((hi_char8 *)pc_str,pc_str_set);
}

/*****************************************************************************
 函 数 名  : hi_os_strcspn
 功能描述  :从参数pcStr字符串的开头计算连续的字符，而这些字符都完全不在参数
            pc_reject 所指的字符串中。返回的数值为n，则代表字符串pcStr开头连续有
            n 个字符都不含字符串pcReject内的字符
 输入参数  : const hi_char8 *pc_str
             const hi_char8 *pc_reject
 返 回 值  : hi_uint32:返回字符串pcStr开头连续不含字符串pcReject内的字符数目。
*****************************************************************************/
hi_uint32 hi_os_strcspn(const hi_char8 *pc_str, const hi_char8 *pc_reject)
{
    return strcspn(pc_str,pc_reject);
}

/*****************************************************************************
 函 数 名  : hi_os_isalpha
 功能描述  : 检查是否为英文字母
 输入参数  : i_value:需要判断的参数值
 返 回 值  : 若参数为英文字母，则返回TRUE，否则返回NULL( 0 )。
*****************************************************************************/
hi_int32 hi_os_isalpha(hi_int32 i_value)
{
    return isalpha(i_value);
}

/*****************************************************************************
 函 数 名  : hi_os_isalnum
 功能描述  : 检查是否为英文字母或阿拉伯数字
 输入参数  : i_value:需要检查的参数值
 返 回 值  : 若参数为字母或数字，则返回TRUE，否则返回NULL( 0 )。
*****************************************************************************/
hi_int32 hi_os_isalnum(hi_int32 i_value)
{
    return isalnum(i_value);
}

/*****************************************************************************
 函 数 名  : hi_os_islower
 功能描述  : 检查参数c是否为小写英文字母。
 输入参数  : hi_int32 i_ch
 返 回 值  : hi_int32:若参数c为小写英文字母，则返回TRUE，否则返回NULL（0）。
*****************************************************************************/
hi_int32 hi_os_islower(hi_int32 i_ch)
{
    return islower(i_ch);
}

/*****************************************************************************
 函 数 名  : hi_os_isupper
 功能描述  : 检查参数c是否为大写英文字母。
 输入参数  : hi_int32 i_ch
 返 回 值  : hi_int32:若参数c为大写英文字母，则返回TRUE，否则返回NULL（0）。
*****************************************************************************/
hi_int32 hi_os_isupper(hi_int32 i_ch)
{
    return isupper(i_ch);
}
/*****************************************************************************
 函 数 名  : hi_os_isdigit
 功能描述  : 检查是否为阿拉伯数字
 输入参数  : i_value:需要检查的参数值
 返 回 值  : 若参数为数字，则返回TRUE，否则返回NULL( 0 )。
*****************************************************************************/
hi_int32 hi_os_isdigit(hi_int32 i_value)
{
    return isdigit(i_value);
}

/*****************************************************************************
 函 数 名  : hi_os_isdigitstr
 功能描述  : 判断字符串是否由数字组成
 输入参数  : hi_char8 *pc_str
*****************************************************************************/
hi_uint32 hi_os_isdigitstr(hi_char8 *pc_str)
{
    hi_char8 *pc_tmp = pc_str;

    if ( NULL == pc_str ) {
        return HI_FALSE;
    }

    if( '\0' == *pc_tmp ) {
        return HI_FALSE;
    }

    while ( '\0' != *pc_tmp ) {
        if ( !HI_ISDIGIT(*pc_tmp) ) {
            return HI_FALSE;
        }
        pc_tmp++;
    }

    return HI_TRUE;
}

/*****************************************************************************
 函 数 名  : hi_os_isspace
 功能描述  : 检查参数c是否为空格字符，也就是判断是否为空格（’’）、定位字
                符（’\t’）、CR（’\r’）、换行（’\n’）、垂直定位字符（’\v’）
                或翻页（’\f’）的情况。此为宏定义，非真正函数
 输入参数  : hi_int32 i_ch
 返 回 值  : hi_int32:若参数c为空格字符，则返回TRUE，否则返回NULL（0）。
*****************************************************************************/
hi_int32 hi_os_isspace(hi_int32 i_ch)
{
    return isspace(i_ch);
}

/*****************************************************************************
 函 数 名  : hi_os_isspacestr
 功能描述  : 判断字符串是否由空字符串组成
 输入参数  : hi_char8 *pc_str
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_os_isspacestr(const hi_char8 *pc_str)
{
    const hi_char8 *pc_tmp = pc_str;

    if ( NULL == pc_str )
    {
        return HI_FALSE;
    }

    while ( '\0' != *pc_tmp )
    {
        if ( !hi_os_isspace(*pc_tmp) )
        {
            return HI_FALSE;
        }
        pc_tmp++;
    }

    return HI_TRUE;
}

char *hi_os_skip_space(char *str)
{
	char *p = str;
	if (str == NULL)
		return str;
	while (isspace(*p)) {
		p++;
	}
	return p;
}

char *hi_os_strim(char *str)
{
	size_t size;
	char *p = str;
    if (str == NULL) {
		return str;
	}
    size = strlen(str);
	if (size == 0) {
		return str;
	}
	p += (size - 1);
	while (p >= str && isspace(*p)) {
		--p;
	}
	p[1] = '\0';

	return hi_os_skip_space(str);
}

static hi_char8 hi_os_hex_to_char(hi_uchar8 src_hex)
{
	switch (src_hex) {
		case 0x0:
			return '0';
		case 0x1:
			return '1';
		case 0x2:
			return '2';
		case 0x3:
			return '3';
		case 0x4:
			return '4';
		case 0x5:
			return '5';
		case 0x6:
			return '6';
		case 0x7:
			return '7';
		case 0x8:
			return '8';
		case 0x9:
			return '9';
		case 0xa:
			return 'A';
		case 0xb:
			return 'B';
		case 0xc:
			return 'C';
		case 0xd:
			return 'D';
		case 0xe:
			return 'E';
		case 0xf:
			return 'F';
		default:
			return ' ';
	}
}

/* *****************************************************************************
description  : convert digital mac(6B) to char(17B) mac
input params : param1: mac address(6B) param2: char string of mac(17B)
***************************************************************************** */
hi_int32 hi_os_mac_to_char(unsigned char *mac_digital, char *mac_char)
{
	hi_uint32 i;

	if ((mac_char == NULL) || (mac_digital == NULL))
		return 1;

	for (i = 0x0; i < 0x6; i++) {
		mac_char[i * 0x3 + 0] = hi_os_hex_to_char(mac_digital[i] / 0x10);
		mac_char[i * 0x3 + 1] = hi_os_hex_to_char(mac_digital[i] % 0x10);
		if ((i * 0x3 + 0x2) < 0x11)
			mac_char[i * 0x3 + 0x2] = ':';
		else
			mac_char[i * 0x3 + 0x2] = '\0';
	}

	return 0;
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif  /* end of __cplusplus */
