/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_os_pipe.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_08_02
  最近修改   :

******************************************************************************/
#include "os/hi_os_fileio.h"
#include "os/hi_os_pipe.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

HI_FILE_S * hi_os_popen(const hi_char8* pc_command,const hi_char8* pc_openmode)
{
    return (HI_FILE_S *)popen(pc_command, pc_openmode);
}

hi_int32 hi_os_mkfifo(const hi_char8* pc_pathname,hi_int32 i_mode)
{
    return mkfifo(pc_pathname, i_mode);
}

hi_int32 hi_os_pipe(hi_int32 a_i_filedes[2])
{
    return pipe(a_i_filedes);
}

hi_int32 hi_os_pclose(HI_FILE_S* pst_stream)
{
    return pclose((FILE*)pst_stream);
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
