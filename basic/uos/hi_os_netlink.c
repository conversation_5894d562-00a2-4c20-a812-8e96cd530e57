/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_os_netlink.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_07_25
  最近修改   :

******************************************************************************/
#include <unistd.h>
#include <sys/socket.h>
#include "securec.h"
#include "hi_errno.h"
#include "hi_typedef.h"
#include "os/hi_os_mem.h"
#include "os/hi_os_netlink.h"

#ifdef  __cplusplus
#if  __cplusplus
extern "C"{
#endif
#endif

hi_int32 hi_os_netlink_senddata(hi_int32 i_sock, hi_ushort16 us_msgtype,
                                hi_uint32 us_msgpid, hi_uchar8 uc_genlcmd,
                                hi_ushort16 us_nlatype, hi_void *pv_nladata,
                                hi_ushort16 us_nlalen)
{
    struct nlattr *pst_nla;
    struct sockaddr_nl st_nladdr;
    hi_int32 i_recvlen, i_bufflen;
    hi_uchar8 *puc_buff;
    hi_int32 ret;

    hi_netlink_s st_msg;

    st_msg.n.nlmsg_len = NLMSG_LENGTH(GENL_HDRLEN);
    st_msg.n.nlmsg_type = us_msgtype;
    st_msg.n.nlmsg_flags = NLM_F_REQUEST;
    st_msg.n.nlmsg_seq = 0;
    st_msg.n.nlmsg_pid = us_msgpid;
    st_msg.g.cmd = uc_genlcmd;
    st_msg.g.version = 0x1;
    pst_nla = (struct nlattr *) GENLMSG_DATA(&st_msg);
    pst_nla->nla_type = us_nlatype;
    pst_nla->nla_len = (hi_short16)(us_nlalen + NLA_HDRLEN);
    ret = memcpy_s(NLA_DATA(pst_nla), NETLINK_MAX_MSG_SIZE, pv_nladata, us_nlalen);
    if (ret != EOK) {
        return -HI_RET_MEMCPY_FAIL;
    }

    st_msg.n.nlmsg_len += NLMSG_ALIGN(pst_nla->nla_len);

    puc_buff = (hi_uchar8 *) &st_msg;
    i_bufflen = (hi_int32)(st_msg.n.nlmsg_len) ;
    (void)memset_s(&st_nladdr, sizeof(st_nladdr), 0, sizeof(st_nladdr));
    st_nladdr.nl_family = AF_NETLINK;

    while ( (i_recvlen = sendto(i_sock, puc_buff, (hi_uint32)i_bufflen, 0,
                               (struct sockaddr *)(void *)&st_nladdr, sizeof(st_nladdr)) )
             < i_bufflen )
    {
        if (i_recvlen > 0)
        {
            puc_buff += i_recvlen;
            i_bufflen -= i_recvlen;
        }
        else if (errno != EAGAIN)
        {
            return -HI_RET_FAIL;
        }
    }

    return HI_RET_SUCC;
}

hi_int32 hi_os_netlink_getid(hi_int32 i_sock)
{
    hi_uint32   ui_tid;
    hi_int32    i_id = 0;
    hi_int32    i_ret;
    hi_int32    i_recvlen;
    struct nlattr *pst_nla;
    hi_char8   a_c_name[32];
    hi_netlink_s st_netlink;

    // for namespace
    ui_tid = (i_sock  << 16) + getpid();
    strncpy_s(a_c_name, sizeof(a_c_name), HI_NETLINK_GENLNAME, sizeof(a_c_name)-1);

    i_ret = hi_os_netlink_senddata( i_sock, GENL_ID_CTRL, ui_tid,
                                   CTRL_CMD_GETFAMILY, CTRL_ATTR_FAMILY_NAME,
                                   (hi_void *)a_c_name, (hi_int32)(strlen(a_c_name)+1));
    if ( i_ret < 0 )
    {
        return -HI_RET_FAIL;
    }

    i_recvlen = recv(i_sock, &st_netlink, sizeof(hi_netlink_s), 0);
    if ( ( st_netlink.n.nlmsg_type == NLMSG_ERROR )
     ||  (i_recvlen < 0 )
     ||  ( !NLMSG_OK((&st_netlink.n), (hi_uint32)i_recvlen)) )
    {
        return -HI_RET_FAIL;
    }

    pst_nla = (struct nlattr *) GENLMSG_DATA(&st_netlink);
    pst_nla = (struct nlattr *) ((hi_uchar8 *) pst_nla + NLA_ALIGN(pst_nla->nla_len));
    if ( CTRL_ATTR_FAMILY_ID == pst_nla->nla_type)
    {
        i_id = *(hi_ushort16 *) NLA_DATA(pst_nla);
    }

    return i_id;
}

hi_int32 hi_os_netlink_recvdata(hi_int32 i_sock, hi_void *pv_buff, hi_int32 i_len, hi_int32 i_flags)
{
    return recv(i_sock, pv_buff, (hi_uint32)i_len, i_flags);
}

hi_int32 hi_os_netlink_close(hi_int32 i_sock)
{
    close(i_sock);
    return HI_RET_SUCC;
}

hi_int32 hi_os_netlink_create(hi_void)
{
    hi_int32 i_fd;
    struct sockaddr_nl st_local;

    i_fd = socket(AF_NETLINK, SOCK_RAW, NETLINK_GENERIC);
    if ( i_fd < 0 )
    {
        return -HI_RET_FAIL;
    }

    memset_s(&st_local, sizeof(st_local), 0, sizeof(st_local));
    st_local.nl_family = AF_NETLINK;
	st_local.nl_pid = (i_fd << 16) + getpid(); //  for namespace

    if (bind(i_fd, (struct sockaddr *)(void *) &st_local, sizeof(st_local)) < 0)
    {
        hi_os_netlink_close(i_fd);
        return -HI_RET_FAIL;
    }

    return i_fd;
}

hi_int32 hi_os_netlink_connect(hi_int32 i_sock, hi_uint32 ui_dstmod, hi_uint32 ui_type, hi_uchar8 genlcmd)
{
    hi_int32 i_id;
    hi_uint32 ui_tid;
    hi_int32 i_ret;
	hi_int32 i_pid = 0;

    i_id  = hi_os_netlink_getid(i_sock);
    if ( i_id < 0)
    {
        return -HI_RET_FAIL;
    }

    //  for namespace
	i_pid = getpid();
	ui_tid = (i_sock << 16) + i_pid;

    i_ret = hi_os_netlink_senddata(i_sock, (hi_ushort16)i_id, ui_tid, genlcmd,
                                      ui_type, &ui_dstmod, sizeof(hi_uint32) );
    if ( i_ret < 0 )
    {
        return -HI_RET_FAIL;
    }

    return i_ret;
}


hi_int32 hi_os_netlink_init( hi_int32 *pi_sock, hi_int32 ui_destmodule )
{
    hi_int32  i_ret;
    hi_int32  i_socket;

    if ( NULL == pi_sock )
    {
        return HI_RET_NULLPTR;
    }

    /* 创建NETLINK相关Socket */
    i_socket = hi_os_netlink_create();
    if ( i_socket < 0 )
    {
        return i_socket;
    }

    /* 将socket和对应的模块绑定 */
    i_ret = hi_os_netlink_connect( i_socket, ui_destmodule, HI_NETLINK_ATTR_MODULE, HI_NETLINK_CMD_REG);
    if ( i_ret < 0)
    {
        hi_os_netlink_close(i_socket);
        return i_ret;
    }

    *pi_sock = i_socket;

    return HI_RET_SUCC;
}

hi_int32 hi_os_netlink_exit( hi_int32 i_socket, hi_int32 ui_destmodule )
{
    hi_int32  i_ret    = HI_RET_SUCC;

    if ( i_socket < 0 )
    {
        return HI_RET_INVALID_PARA;
    }

    /* 将socket和对应的模块绑定 */
    i_ret = hi_os_netlink_connect( i_socket, ui_destmodule, HI_NETLINK_ATTR_MODULE, HI_NETLINK_CMD_UNREG);
    if ( i_ret < 0 )
    {
        return i_ret;
    }

    /* 创建NETLINK相关Socket */
    i_ret = hi_os_netlink_close(i_socket);

    return i_ret;
}

hi_int32 hi_os_netlink_recv( hi_int32 i_sock, hi_void *pv_buff, hi_int32 i_len, hi_int32 i_flags )
{
    hi_int32  i_ret;
    hi_uchar8 *puc_buf = (hi_uchar8 *)hi_os_malloc(NETLINK_MAX_MSG_SIZE);

    if(NULL == puc_buf)
    {
        return -1;
    }
    /*clear buffer*/
    memset_s( puc_buf, NETLINK_MAX_MSG_SIZE, 0, NETLINK_MAX_MSG_SIZE);
    /*recv packet*/
    i_ret = hi_os_netlink_recvdata(i_sock, puc_buf, NETLINK_MAX_MSG_SIZE, i_flags );
    if ( 24 > i_ret )
    {
        hi_os_free((hi_void *)puc_buf);
        return i_ret;
    }
    /*offset netlink head*/
    i_ret -= 24;
    /*protect over mem*/
    if ( i_ret > i_len )
    {
        hi_os_free((hi_void *)puc_buf);
        return -1;
    }

    /*mem offset 24 byte,skip netlink head*/
    if (memmove_s(pv_buff, i_len, puc_buf+24, i_ret) != EOK) {
		i_ret = -1;
	}

    hi_os_free((hi_void *)puc_buf);
    return i_ret;
}

#ifdef  __cplusplus
#if  __cplusplus
}
#endif
#endif
