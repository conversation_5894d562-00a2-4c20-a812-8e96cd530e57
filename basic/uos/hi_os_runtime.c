/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_os_runtime.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_08_02
  最近修改   :

******************************************************************************/
#include "securec.h"
#include "hi_errno.h"
#include "hi_typedef.h"
#include "os/hi_os_fileio.h"
#include "os/hi_os_runtime.h"
#include "os/hi_os_thread.h"
#include "os/hi_os_string.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

/*****************************************************************************
 函 数 名  : hi_os_runtime_read_cpu_time
 功能描述  : 读取各状态下CPU运行时间
 输入参数  : HI_P_DIR dir  : 目录
 输出参数  : cputimes，各状态下的cpu时间，单位jiffies(10ms)。依次分别是：
             用户态、低优先级用户态、系统态、空闲态
*****************************************************************************/
static hi_uint32 hi_os_runtime_read_cpu_time(hi_os_runtime_cputime_s *pst_cputime)
{
    HI_FILE_S* pfd = NULL;
    hi_uchar8   uc_buff[HI_OS_RUNTIME_MAX_LEN];
    hi_uint32   *pui_data;
    hi_uint32   ui_cnt = 0;

    if ( NULL == pst_cputime )
    {
        return HI_RET_NULLPTR;
    }

    /* 打开文件/proc/stat */
    pfd = hi_os_fopen("/proc/stat", "rb");
    if ( NULL == pfd )
    {
        return HI_RET_FILE_READ_FAIL;
    }

    memset_s(uc_buff, HI_OS_RUNTIME_MAX_LEN, 0, HI_OS_RUNTIME_MAX_LEN);

    ui_cnt = hi_os_fread( uc_buff, 1, HI_OS_RUNTIME_MAX_LEN, pfd);

    hi_os_fclose(pfd);

	if(0 == ui_cnt)
	{
		return HI_RET_FILE_READ_FAIL;
	}

    /*读取前四个整数（用户态运行时间，nice，内核态运行时间，系统空闲时间） */
    pui_data = (hi_uint32*)&uc_buff[0];

    pst_cputime->ui_usertime    = pui_data[0];
    pst_cputime->ui_nicetime    = pui_data[1];
    pst_cputime->ui_kerneltime  = pui_data[2];
    pst_cputime->ui_idletime    = pui_data[3];

    return HI_RET_SUCC;
}

/******************************************************************************
 函 数 名  : hi_os_runtime_get_cpu_busy_rate
 功能描述  : 查询当前的CPU占用率，为计算占用率，需等待1秒。
 输出参数  : ulRate，CPU占用率乘以10000，防止值太小
*****************************************************************************/
hi_uint32 hi_os_runtime_get_cpu_busy_rate(hi_uint32 *pui_rate)
{
    hi_uint32 ui_totaldifftime;
    hi_uint32 ui_ret;
    hi_os_runtime_cputime_s st_begincputime;
    hi_os_runtime_cputime_s st_endcputime;

    if ( NULL == pui_rate )
    {
        return HI_RET_NULLPTR;
    }

    *pui_rate = 0;

    /* 读取CPU时间 */
    ui_ret = hi_os_runtime_read_cpu_time(&st_begincputime);
    if ( HI_RET_SUCC != ui_ret )
    {
        return ui_ret;
    }

    /* 休眠1秒钟 */
    hi_os_sleep(HI_OS_RUNTIME_INTERVAL);

    /* 重新读取CPU时间 */
    ui_ret = hi_os_runtime_read_cpu_time(&st_endcputime);
    if ( HI_RET_SUCC != ui_ret )
    {
        return ui_ret;
    }

    /* 计算CPU占用率（1 － 系统空闲时间的差值/所有时间之差的和）。 */
    ui_totaldifftime =  (st_endcputime.ui_usertime + st_endcputime.ui_nicetime +
                      st_endcputime.ui_kerneltime + st_endcputime.ui_idletime )
                   - (st_begincputime.ui_usertime + st_begincputime.ui_nicetime +
                      st_begincputime.ui_kerneltime + st_begincputime.ui_idletime );

    /* /proc/stat中的数据有可能没有刷新导致两次读出时间相同，使得totaldifftime为0 */
    if ( 0 == ui_totaldifftime )
    {
        *pui_rate = 0;
    }
    else
    {
        /*a_ui_endtime[3] - a_ui_starttime[3] 空闲时间*/
        //*pui_rate = ((ui_totaldifftime - (st_endcputime.ui_kerneltime - st_begincputime.ui_kerneltime))*10000) / ui_totaldifftime;
    }
    return HI_RET_SUCC;

}

hi_int32 hi_os_cpuusage(hi_int32 *pi_total, hi_int32 *pi_idle)
{
#define PROC_STAT "/proc/stat"
    hi_int32 stat[8]; /* user , nice , sys, idle, iowait, irq, sirq, steal, guest */
    HI_FILE_S *fp = NULL;
    if (NULL == (fp = hi_os_fopen(PROC_STAT, "r"))) {
        return -1;
    }
    if (fscanf_s(fp, "cpu %d %d %d %d %d %d %d %d",
            &stat[0], &stat[1], &stat[2], &stat[3], &stat[4], &stat[5], &stat[6], &stat[7]) < 0) {
        hi_os_fclose(fp);
        return -1;
    }
    hi_os_fclose(fp);

    *pi_total = stat[0]+stat[1]+stat[2]+stat[3]+stat[4]+stat[5]+stat[6];
    *pi_idle = stat[3];

	return 0;
}

hi_int32 hi_os_getcputime(hi_int32 i_pid, hi_int32 *pi_time)
{
#define PROC_PID_UTIME_OFF (14) /* proc/%d/stat 文件内ctime位置为14 */
    hi_char8 ac_name[128];
    hi_char8 ac_buf[512] = {0};
    HI_FILE_S *fp = NULL;
    hi_char8 *pos, *sep ;
    hi_int32 i_num;
    hi_int32 i_utime = 0, i_stime = 0;

    sprintf_s(ac_name, sizeof(ac_name)-1, "/proc/%d/stat", i_pid);
    if (NULL == (fp = hi_os_fopen(ac_name, "r"))) {
        return -1;
    }
    hi_os_fgets(ac_buf, sizeof(ac_buf)-1, fp);
    hi_os_fclose(fp);
    pos = ac_buf;
    i_num = PROC_PID_UTIME_OFF;
    while (i_num && (NULL != (sep = hi_os_strsep(&pos, " ")))) {
        i_num--;
    }
    if (sep == NULL || i_num ) {
        return -1;
    }
    i_utime = hi_os_strtol(sep, &sep, 0);
    if (sep && *sep) {
        sep++;
        i_stime = hi_os_strtol(sep, NULL, 0);
	}

    *pi_time = i_utime + i_stime;
	return 0;
}

#ifdef __cplusplus
#if __cplusplus
}
#endif /* __cpluscplus */
#endif /* __cpluscplus */
