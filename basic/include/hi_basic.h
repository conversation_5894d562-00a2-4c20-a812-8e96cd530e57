/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_basic.h
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_09_23

******************************************************************************/
#ifndef __HI_BASIC_H__
#define __HI_BASIC_H__
#include "hi_typedef.h"
#include "hi_errno.h"
#include "hi_sysdef.h"
#include "os/hi_os.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

#ifndef ARRAY_SIZE
#define ARRAY_SIZE(x) (sizeof(x) / sizeof((x)[0]))
#endif

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif /* __HI_BASIC_H__ */
