/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_os.h
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_09_21

******************************************************************************/
#ifndef __HI_OS_H__
#define __HI_OS_H__


#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

#include "hi_os_runtime.h"
#include "hi_os_time.h"
#include "hi_os_mem.h"
#include "hi_os_ipc.h"
#include "hi_os_thread.h"
#include "hi_os_sem.h"
#include "hi_os_shm.h"
#include "hi_os_mutex.h"
#include "hi_os_signal.h"

#include "hi_os_pipe.h"
#include "hi_os_array.h"
#include "hi_os_msg.h"
#include "hi_os_math.h"

#include "hi_os_stat.h"
#include "hi_os_string.h"
#include "hi_os_unistdio.h"

#include "hi_os_crc.h"
#include "hi_os_err.h"
#include "hi_os_socket.h"
#include "hi_os_fileio.h"

#include "hi_os_netlink.h"
#include "hi_os_hash.h"
#include "hi_os_hlp.h"
#include "hi_os_time.h"

#include "hi_os_securec.h"

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif /* __HI_OS_H__ */
