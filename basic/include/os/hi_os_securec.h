/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_os_securec.h
  版 本 号   : 初稿
  作    者   : t00404924
  生成日期   : D2018_02_13
  最近修改   :

******************************************************************************/

#ifndef __HI_OS_SECUREC_H__
#define __HI_OS_SECUREC_H__
#include "securec.h"


#if 1
#define HI_OS_MEMSET_S(d,max,c,size)      \
do{                                                           \
    int nRet = 0;  \
    nRet =  memset_s((d), (max), (c), (size));  \
    if(EOK != nRet) \
    {                                       \
        printf("%s,%d :memset_s return[%d] failed \r\n",__func__,__LINE__,nRet); \
    } \
}while(0)

#define HI_OS_MEMCPY_S(d,max,s,size)           \
do{  \
    int nRet = 0;  \
    nRet =  memcpy_s((d), (max), (s), (size));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%d :memcpy_s return[%d] failed \r\n",__func__,__LINE__,nRet); \
    } \
}while(0)

#define HI_OS_WMEMCPY_S(d,max,s,size)           \
do{  \
    int nRet = 0;  \
    nRet =  wmemcpy_s((d), (max), (s), (size));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%d :wmemcpy_s return[%d] failed \r\n",__func__,__LINE__,nRet); \
    } \
}while(0)


#define HI_OS_MEMMOVE_S(d,max,s,size)           \
do{  \
    int nRet = 0;  \
    nRet =  memmove_s((d), (max), (s), (size));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%d :memmove_s return[%d] failed \r\n",__func__,__LINE__,nRet); \
    } \
}while(0)

#define HI_OS_WMEMMOVE_S(d,max,s,size)           \
do{  \
    int nRet = 0;  \
    nRet =  wmemmove_s((d), (max), (s), (size));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%d :wmemmove_s return[%d] failed \r\n",__func__,__LINE__,nRet); \
    } \
}while(0)

#define HI_OS_STRCPY_S(d,max,s)           \
do{  \
    int nRet = 0;  \
    nRet =  strcpy_s((d),(max),(s));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%d :strcpy_s return[%d] failed \r\n",__func__,__LINE__,nRet); \
    } \
}while(0)

#define HI_OS_WCSCPY_S(d,max,s)           \
do{  \
    int nRet = 0;  \
    nRet =  wcscpy_s((d),(max),(s));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%d :wcscpy_s return[%d] failed \r\n",__func__,__LINE__,nRet); \
    } \
}while(0)

#define HI_OS_STRNCPY_S(d,max,s,size)           \
do{  \
    int nRet = 0;  \
    nRet =  strncpy_s((d),(max),(s),(size));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%d :strncpy_s return[%d] failed \r\n",__func__,__LINE__,nRet); \
    } \
}while(0)

#define HI_OS_WCSNCPY_S(d,max,s,size)           \
do{  \
    int nRet = 0;  \
    nRet =  wcsncpy_s((d),(max),(s),(size));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%d :wcsncpy_s return[%d] failed \r\n",__func__,__LINE__,nRet); \
    } \
}while(0)

#define HI_OS_STRCAT_S(d,max,s)           \
do{  \
    int nRet = 0;  \
    nRet =  strcat_s((d),(max),(s));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%d :strcat_s return[%d] failed \r\n",__func__,__LINE__,nRet); \
    } \
}while(0)

#define HI_OS_WCSCAT_S(d,max,s)           \
do{  \
    int nRet = 0;  \
    nRet =  wcscat_s((d),(max),(s));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%d :wcscat_s return[%d] failed \r\n",__func__,__LINE__,nRet); \
    } \
}while(0)

#define HI_OS_STRNCAT_S(d,max,s,size)           \
do{  \
    int nRet = 0;  \
    nRet =  strncat_s((d),(max),(s),(size));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%d :strncat_s return[%d] failed \r\n",__func__,__LINE__,nRet); \
    } \
}while(0)

#define HI_OS_WCSNCAT_S(d,max,s,size)           \
do{  \
    int nRet = 0;  \
    nRet =  wcsncat_s((d),(max),(s),(size));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%d :wcsncat_s return[%d] failed \r\n",__func__,__LINE__,nRet); \
    } \
}while(0)
#else   //下面else 中的宏为临时宏，正式宏使用上面#if 0

#define HI_OS_MEMSET_S4(d,max,c,size)           \
do{  \
    int nRet = 0;  \
    nRet =  memset_s((d), (max), (c), (size));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%s,%d :memset_s return[%d] failed \r\n",__FILE__,__func__,__LINE__,nRet); \
    } \
}while(0)

#define HI_OS_MEMSET_S3(d,c,size)           \
do{  \
    int nRet = 0;  \
    nRet =  memset_s((d),(size),(c),(size));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%s,%d :memset_s return[%d] failed \r\n",__FILE__,__func__,__LINE__,nRet); \
    } \
}while(0)


#define HI_OS_MEMSET_MID2(count, ...) HI_OS_MEMSET_S##count( __VA_ARGS__ )
#define HI_OS_MEMSET_MID(count, ...)  HI_OS_MEMSET_MID2(count, __VA_ARGS__ )
//临时宏，正式使用上面#if 0中的定义，需传入max参数。
#define HI_OS_MEMSET_S(...)           HI_OS_MEMSET_MID(HI_VA_NARGS(__VA_ARGS__), __VA_ARGS__ )


#define HI_OS_MEMCPY_S4(d,max,s,size)           \
do{  \
    int nRet = 0;  \
    nRet =  memcpy_s((d), (max), (s), (size));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%s,%d :memcpy_s return[%d] failed \r\n",__FILE__,__func__,__LINE__,nRet); \
    } \
}while(0)

#define HI_OS_MEMCPY_S3(d,s,size)           \
do{  \
    int nRet = 0;  \
    nRet =  memcpy_s((d),(size),(s),(size));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%s,%d :memcpy_s return[%d] failed \r\n",__FILE__,__func__,__LINE__,nRet); \
    } \
}while(0)


#define HI_OS_MEMCPY_MID2(count, ...) HI_OS_MEMCPY_S##count( __VA_ARGS__ )
#define HI_OS_MEMCPY_MID(count, ...)  HI_OS_MEMCPY_MID2(count, __VA_ARGS__ )
//临时宏，正式使用上面#if 0中的定义，需传入max参数。
#define HI_OS_MEMCPY_S(...)              HI_OS_MEMCPY_MID(HI_VA_NARGS(__VA_ARGS__), __VA_ARGS__ )


#define HI_OS_MEMMOVE_S4(d,max,s,size)           \
do{  \
    int nRet = 0;  \
    nRet =  memmove_s((d), (max), (s), (size));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%s,%d :memmove_s return[%d] failed \r\n",__FILE__,__func__,__LINE__,nRet); \
    } \
}while(0)

#define HI_OS_MEMMOVE_S3(d,s,size)           \
do{  \
    int nRet = 0;  \
    nRet =  memmove_s((d),(size),(s),(size));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%s,%d :memmove_s return[%d] failed \r\n",__FILE__,__func__,__LINE__,nRet); \
    } \
}while(0)


#define HI_OS_MEMMOVE_MID2(count, ...) HI_OS_MEMMOVE_S##count( __VA_ARGS__ )
#define HI_OS_MEMMOVE_MID(count, ...)  HI_OS_MEMMOVE_MID2(count, __VA_ARGS__ )
//临时宏，正式使用上面#if 0中的定义，需传入max参数。
#define HI_OS_MEMMOVE_S(...)              HI_OS_MEMMOVE_MID(HI_VA_NARGS(__VA_ARGS__), __VA_ARGS__ )


/*安全函数宏*/
#define HI_OS_STRCPY_S2(d,s)           \
do{  \
    int nRet = 0;  \
    nRet =  strcpy_s(((char *)d), sizeof(d), (s));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%s,%d :strcpy_s return[%d] failed \r\n",__FILE__,__func__,__LINE__,nRet); \
    } \
}while(0)

#define HI_OS_STRCPY_S3(d,max,s)           \
do{  \
    int nRet = 0;  \
    nRet =  strcpy_s((d),(max),(s));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%s,%d :strcpy_s return[%d] failed \r\n",__FILE__,__func__,__LINE__,nRet); \
    } \
}while(0)


#define HI_OS_STRCPY_MID2(count, ...) HI_OS_STRCPY_S##count( __VA_ARGS__ )
#define HI_OS_STRCPY_MID(count, ...)  HI_OS_STRCPY_MID2(count, __VA_ARGS__ )
//临时宏，正式使用上面#if 0中的定义，需传入max参数。
#define HI_OS_STRCPY_S(...)              HI_OS_STRCPY_MID(HI_VA_NARGS(__VA_ARGS__), __VA_ARGS__ )


/*安全函数宏*/
#define HI_OS_STRNCPY_S3(d,s,size)           \
do{  \
    int nRet = 0;  \
    nRet =  strncpy_s(((char *)d), sizeof(d), (s),(size));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%s,%d :strncpy_s return[%d] failed \r\n",__FILE__,__func__,__LINE__,nRet); \
    } \
}while(0)

#define HI_OS_STRNCPY_S4(d,max,s,size)           \
do{  \
    int nRet = 0;  \
    nRet =  strncpy_s((d),(max),(s),(size));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%s,%d :strncpy_s return[%d] failed \r\n",__FILE__,__func__,__LINE__,nRet); \
    } \
}while(0)

#define HI_OS_STRNCPY_MID2(count, ...) HI_OS_STRNCPY_S##count( __VA_ARGS__ )
#define HI_OS_STRNCPY_MID(count, ...)  HI_OS_STRNCPY_MID2(count, __VA_ARGS__ )
//临时宏，正式使用上面#if 0中的定义，需传入max参数。
#define HI_OS_STRNCPY_S(...)              HI_OS_STRNCPY_MID(HI_VA_NARGS(__VA_ARGS__), __VA_ARGS__ )

/*安全函数宏*/
#define HI_OS_STRCAT_S2(d,s)           \
do{  \
    int nRet = 0;  \
    nRet =  strcat_s(((char *)d), sizeof(d), (s));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%s,%d :strcat_s return[%d] failed \r\n",__FILE__,__func__,__LINE__,nRet); \
    } \
}while(0)

#define HI_OS_STRCAT_S3(d,max,s)           \
do{  \
    int nRet = 0;  \
    nRet =  strcat_s((d),(max),(s));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%s,%d :strcat_s return[%d] failed \r\n",__FILE__,__func__,__LINE__,nRet); \
    } \
}while(0)


#define HI_OS_STRCAT_MID2(count, ...) HI_OS_STRCAT_S##count( __VA_ARGS__ )
#define HI_OS_STRCAT_MID(count, ...)  HI_OS_STRCAT_MID2(count, __VA_ARGS__ )
//临时宏，正式使用上面#if 0中的定义，需传入max参数。
#define HI_OS_STRCAT_S(...)              HI_OS_STRCAT_MID(HI_VA_NARGS(__VA_ARGS__), __VA_ARGS__ )


/*安全函数宏*/
#define HI_OS_STRNCAT_S3(d,s,size)           \
do{  \
    int nRet = 0;  \
    nRet =  strncat_s(((char *)d), sizeof(d), (s),(size));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%s,%d :strncat_s return[%d] failed \r\n",__FILE__,__func__,__LINE__,nRet); \
    } \
}while(0)

#define HI_OS_STRNCAT_S4(d,max,s,size)           \
do{  \
    int nRet = 0;  \
    nRet =  strncat_s((d),(max),(s),(size));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%s,%d :strncat_s return[%d] failed \r\n",__FILE__,__func__,__LINE__,nRet); \
    } \
}while(0)


#define HI_OS_STRNCAT_MID2(count, ...) HI_OS_STRNCAT_S##count( __VA_ARGS__ )
#define HI_OS_STRNCAT_MID(count, ...)  HI_OS_STRNCAT_MID2(count, __VA_ARGS__ )
//临时宏，正式使用上面#if 0中的定义，需传入max参数。
#define HI_OS_STRNCAT_S(...)              HI_OS_STRNCAT_MID(HI_VA_NARGS(__VA_ARGS__), __VA_ARGS__ )

#define HI_OS_WMEMMOVE_S(d,max,s,size)           \
do{  \
    int nRet = 0;  \
    nRet =  wmemmove_s((d), (max), (s), (size));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%d :wmemmove_s return[%d] failed \r\n",__func__,__LINE__,nRet); \
    } \
}while(0)

#define HI_OS_WCSCPY_S(d,max,s)           \
do{  \
    int nRet = 0;  \
    nRet =  wcscpy_s((d),(max),(s));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%d :wcscpy_s return[%d] failed \r\n",__func__,__LINE__,nRet); \
    } \
}while(0)


#define HI_OS_WCSNCPY_S(d,max,s,size)           \
do{  \
    int nRet = 0;  \
    nRet =  wcsncpy_s((d),(max),(s),(size));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%d :wcsncpy_s return[%d] failed \r\n",__func__,__LINE__,nRet); \
    } \
}while(0)

#define HI_OS_WCSCAT_S(d,max,s)           \
do{  \
    int nRet = 0;  \
    nRet =  wcscat_s((d),(max),(s));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%d :wcscat_s return[%d] failed \r\n",__func__,__LINE__,nRet); \
    } \
}while(0)

#define HI_OS_WCSNCAT_S(d,max,s,size)           \
do{  \
    int nRet = 0;  \
    nRet =  wcsncat_s((d),(max),(s),(size));  \
    if(EOK != nRet ) \
    { \
        printf("%s,%d :wcsncat_s return[%d] failed \r\n",__func__,__LINE__,nRet); \
    } \
}while(0)

#endif

#define HI_OS_SPRINTF_S               sprintf_s
#define HI_OS_SWPRINTF_S              swprintf_s
#define HI_OS_VSPRINTF_S              vsprintf_s
#define HI_OS_VSWPRINTF_S             vswprintf_s
#define HI_OS_SNPRINTF_S              snprintf_s
#define HI_OS_VSNPRINTF_S             vsnprintf_s
#define HI_OS_SCANF_S                 scanf_s
#define HI_OS_WSCANF_S                wscanf_s
#define HI_OS_VSCANF_S                vscanf_s
#define HI_OS_VWSCANF_S               vwscanf_s
#define HI_OS_FSCANF_S                fscanf_s
#define HI_OS_FWSCANF_S               fwscanf_s
#define HI_OS_VFSCANF_S               vfscanf_s
#define HI_OS_VFWSCANF_S              vfwscanf_s
#define HI_OS_SSCANF_S                sscanf_s
#define HI_OS_SWSCANF_S               swscanf_s
#define HI_OS_VSSCANF_S               vsscanf_s
#define HI_OS_VSWSCANF_S              vswscanf_s
#define HI_OS_GETS_S                  gets_s

#endif    /*__HI_OS_SECUREC_H__*/
