/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_os_err.h
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_08_02
  最近修改   :

******************************************************************************/
#ifndef __HI_OS_ERR_H__
#define __HI_OS_ERR_H__

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif

typedef enum {
	HI_ERR_OS_BASE = 0,
    HI_ERR_OS_EPERM              = (HI_ERR_OS_BASE + 1  ),    /* Operation not permitted */
    HI_ERR_OS_ENOENT             = (HI_ERR_OS_BASE + 2  ),    /* No such file or directory */
    HI_ERR_OS_ESRCH              = (HI_ERR_OS_BASE + 3  ),    /* No such process */
    HI_ERR_OS_EINTR              = (HI_ERR_OS_BASE + 4  ),    /* Interrupted system call */
    HI_ERR_OS_EIO                = (HI_ERR_OS_BASE + 5  ),    /* I/O error */
    HI_ERR_OS_ENXIO              = (HI_ERR_OS_BASE + 6  ),    /* No such device or address */
    HI_ERR_OS_E2BIG              = (HI_ERR_OS_BASE + 7  ),    /* Arg list too long */
    HI_ERR_OS_ENOEXEC            = (HI_ERR_OS_BASE + 8  ),    /* Exec format error */
    HI_ERR_OS_EBADF              = (HI_ERR_OS_BASE + 9  ),    /* Bad file number */
    HI_ERR_OS_ECHILD             = (HI_ERR_OS_BASE + 10 ),   /* No child processes */
    HI_ERR_OS_EAGAIN             = (HI_ERR_OS_BASE + 11 ),   /* Try again */
    HI_ERR_OS_ENOMEM             = (HI_ERR_OS_BASE + 12 ),   /* Out of memory */
    HI_ERR_OS_EACCES             = (HI_ERR_OS_BASE + 13 ),   /* Permission denied */
    HI_ERR_OS_EFAULT             = (HI_ERR_OS_BASE + 14 ),   /* Bad address */
    HI_ERR_OS_ENOTBLK            = (HI_ERR_OS_BASE + 15 ),   /* Block device required */
    HI_ERR_OS_EBUSY              = (HI_ERR_OS_BASE + 16 ),   /* Device or resource busy */
    HI_ERR_OS_EEXIST             = (HI_ERR_OS_BASE + 17 ),   /* File exists */
    HI_ERR_OS_EXDEV              = (HI_ERR_OS_BASE + 18 ),   /* Cross-device link */
    HI_ERR_OS_ENODEV             = (HI_ERR_OS_BASE + 19 ),   /* No such device */
    HI_ERR_OS_ENOTDIR            = (HI_ERR_OS_BASE + 20 ),   /* Not a directory */
    HI_ERR_OS_EISDIR             = (HI_ERR_OS_BASE + 21 ),   /* Is a directory */
    HI_ERR_OS_EINVAL             = (HI_ERR_OS_BASE + 22 ),   /* Invalid argument */
    HI_ERR_OS_ENFILE             = (HI_ERR_OS_BASE + 23 ),   /* File table overflow */
    HI_ERR_OS_EMFILE             = (HI_ERR_OS_BASE + 24 ),   /* Too many open files */
    HI_ERR_OS_ENOTTY             = (HI_ERR_OS_BASE + 25 ),   /* Not a typewriter */
    HI_ERR_OS_ETXTBSY            = (HI_ERR_OS_BASE + 26 ),   /* Text file busy */
    HI_ERR_OS_EFBIG              = (HI_ERR_OS_BASE + 27 ),   /* File too large */
    HI_ERR_OS_ENOSPC             = (HI_ERR_OS_BASE + 28 ),   /* No space left on device */
    HI_ERR_OS_ESPIPE             = (HI_ERR_OS_BASE + 29 ),   /* Illegal seek */
    HI_ERR_OS_EROFS              = (HI_ERR_OS_BASE + 30 ),   /* Read-only file system */
    HI_ERR_OS_EMLINK             = (HI_ERR_OS_BASE + 31 ),   /* Too many links */
    HI_ERR_OS_EPIPE              = (HI_ERR_OS_BASE + 32 ),   /* Broken pipe */
    HI_ERR_OS_EDOM               = (HI_ERR_OS_BASE + 33 ),   /* Math argument out of domain of func */
    HI_ERR_OS_ERANGE             = (HI_ERR_OS_BASE + 34 ),   /* Math result not representable */
    HI_ERR_OS_EDEADLK            = (HI_ERR_OS_BASE + 35 ),   /* Resource deadlock would occur */
    HI_ERR_OS_ENAMETOOLONG       = (HI_ERR_OS_BASE + 36 ),   /* File name too long */
    HI_ERR_OS_ENOLCK             = (HI_ERR_OS_BASE + 37 ),   /* No record locks available */
    HI_ERR_OS_ENOSYS             = (HI_ERR_OS_BASE + 38 ),   /* Function not implemented */
    HI_ERR_OS_ENOTEMPTY          = (HI_ERR_OS_BASE + 39 ),   /* Directory not empty */
    HI_ERR_OS_ELOOP              = (HI_ERR_OS_BASE + 40 ),   /* Too many symbolic links encountered */
    HI_ERR_OS_ENOMSG             = (HI_ERR_OS_BASE + 42 ),   /* No message of desired type */
    HI_ERR_OS_EIDRM              = (HI_ERR_OS_BASE + 43 ),   /* Identifier removed */
    HI_ERR_OS_ECHRNG             = (HI_ERR_OS_BASE + 44 ),   /* Channel number out of range */
    HI_ERR_OS_EL2NSYNC           = (HI_ERR_OS_BASE + 45 ),   /* Level 2 not synchronized */
    HI_ERR_OS_EL3HLT             = (HI_ERR_OS_BASE + 46 ),   /* Level 3 halted */
    HI_ERR_OS_EL3RST             = (HI_ERR_OS_BASE + 47 ),   /* Level 3 reset */
    HI_ERR_OS_ELNRNG             = (HI_ERR_OS_BASE + 48 ),   /* Link number out of range */
    HI_ERR_OS_EUNATCH            = (HI_ERR_OS_BASE + 49 ),   /* Protocol driver not attached */
    HI_ERR_OS_ENOCSI             = (HI_ERR_OS_BASE + 50 ),   /* No CSI structure available */
    HI_ERR_OS_EL2HLT             = (HI_ERR_OS_BASE + 51 ),   /* Level 2 halted */
    HI_ERR_OS_EBADE              = (HI_ERR_OS_BASE + 52 ),   /* Invalid exchange */
    HI_ERR_OS_EBADR              = (HI_ERR_OS_BASE + 53 ),   /* Invalid request descriptor */
    HI_ERR_OS_EXFULL             = (HI_ERR_OS_BASE + 54 ),   /* Exchange full */
    HI_ERR_OS_ENOANO             = (HI_ERR_OS_BASE + 55 ),   /* No anode */
    HI_ERR_OS_EBADRQC            = (HI_ERR_OS_BASE + 56 ),   /* Invalid request code */
    HI_ERR_OS_EBADSLT            = (HI_ERR_OS_BASE + 57 ),   /* Invalid slot */
    HI_ERR_OS_EBFONT             = (HI_ERR_OS_BASE + 59 ),   /* Bad font file format */
    HI_ERR_OS_ENOSTR             = (HI_ERR_OS_BASE + 60 ),   /* Device not a stream */
    HI_ERR_OS_ENODATA            = (HI_ERR_OS_BASE + 61 ),   /* No data available */
    HI_ERR_OS_ETIME              = (HI_ERR_OS_BASE + 62 ),   /* Timer expired */
    HI_ERR_OS_ENOSR              = (HI_ERR_OS_BASE + 63 ),   /* Out of streams resources */
    HI_ERR_OS_ENONET             = (HI_ERR_OS_BASE + 64 ),   /* Machine is not on the network */
    HI_ERR_OS_ENOPKG             = (HI_ERR_OS_BASE + 65 ),   /* Package not installed */
    HI_ERR_OS_EREMOTE            = (HI_ERR_OS_BASE + 66 ),   /* Object is remote */
    HI_ERR_OS_ENOLINK            = (HI_ERR_OS_BASE + 67 ),   /* Link has been severed */
    HI_ERR_OS_EADV               = (HI_ERR_OS_BASE + 68 ),   /* Advertise error */
    HI_ERR_OS_ESRMNT             = (HI_ERR_OS_BASE + 69 ),   /* Srmount error */
    HI_ERR_OS_ECOMM              = (HI_ERR_OS_BASE + 70 ),   /* Communication error on send */
    HI_ERR_OS_EPROTO             = (HI_ERR_OS_BASE + 71 ),   /* Protocol error */
    HI_ERR_OS_EMULTIHOP          = (HI_ERR_OS_BASE + 72 ),   /* Multihop attempted */
    HI_ERR_OS_EDOTDOT            = (HI_ERR_OS_BASE + 73 ),   /* RFS specific error */
    HI_ERR_OS_EBADMSG            = (HI_ERR_OS_BASE + 74 ),   /* Not a data message */
    HI_ERR_OS_EOVERFLOW          = (HI_ERR_OS_BASE + 75 ),   /* Value too large for defined data type */
    HI_ERR_OS_ENOTUNIQ           = (HI_ERR_OS_BASE + 76 ),   /* Name not unique on network */
    HI_ERR_OS_EBADFD             = (HI_ERR_OS_BASE + 77 ),   /* File descriptor in bad state */
    HI_ERR_OS_EREMCHG            = (HI_ERR_OS_BASE + 78 ),   /* Remote address changed */
    HI_ERR_OS_ELIBACC            = (HI_ERR_OS_BASE + 79 ),   /* Can not access a needed shared library */
    HI_ERR_OS_ELIBBAD            = (HI_ERR_OS_BASE + 80 ),   /* Accessing a corrupted shared library */
    HI_ERR_OS_ELIBSCN            = (HI_ERR_OS_BASE + 81 ),   /* .lib section in a.out corrupted */
    HI_ERR_OS_ELIBMAX            = (HI_ERR_OS_BASE + 82 ),   /* Attempting to link in too many shared libraries */
    HI_ERR_OS_ELIBEXEC           = (HI_ERR_OS_BASE + 83 ),   /* Cannot exec a shared library directly */
    HI_ERR_OS_EILSEQ             = (HI_ERR_OS_BASE + 84 ),   /* Illegal byte sequence */
    HI_ERR_OS_ERESTART           = (HI_ERR_OS_BASE + 85 ),   /* Interrupted system call should be restarted */
    HI_ERR_OS_ESTRPIPE           = (HI_ERR_OS_BASE + 86 ),   /* Streams pipe error */
    HI_ERR_OS_EUSERS             = (HI_ERR_OS_BASE + 87 ),   /* Too many users */
    HI_ERR_OS_ENOTSOCK           = (HI_ERR_OS_BASE + 88 ),   /* Socket operation on non-socket */
    HI_ERR_OS_EDESTADDRREQ       = (HI_ERR_OS_BASE + 89 ),   /* Destination address required */
    HI_ERR_OS_EMSGSIZE           = (HI_ERR_OS_BASE + 90 ),   /* Message too long */
    HI_ERR_OS_EPROTOTYPE         = (HI_ERR_OS_BASE + 91 ),   /* Protocol wrong type for socket */
    HI_ERR_OS_ENOPROTOOPT        = (HI_ERR_OS_BASE + 92 ),   /* Protocol not available */
    HI_ERR_OS_EPROTONOSUPPORT    = (HI_ERR_OS_BASE + 93 ),   /* Protocol not supported */
    HI_ERR_OS_ESOCKTNOSUPPORT    = (HI_ERR_OS_BASE + 94 ),   /* Socket type not supported */
    HI_ERR_OS_EOPNOTSUPP         = (HI_ERR_OS_BASE + 95 ),   /* Operation not supported on transport endpoint */
    HI_ERR_OS_EPFNOSUPPORT       = (HI_ERR_OS_BASE + 96 ),   /* Protocol family not supported */
    HI_ERR_OS_EAFNOSUPPORT       = (HI_ERR_OS_BASE + 97 ),   /* Address family not supported by protocol */
    HI_ERR_OS_EADDRINUSE         = (HI_ERR_OS_BASE + 98 ),   /* Address already in use */
    HI_ERR_OS_EADDRNOTAVAIL      = (HI_ERR_OS_BASE + 99 ),   /* Cannot assign requested address */
    HI_ERR_OS_ENETDOWN           = (HI_ERR_OS_BASE + 100),  /* Network is down */
    HI_ERR_OS_ENETUNREACH        = (HI_ERR_OS_BASE + 101),  /* Network is unreachable */
    HI_ERR_OS_ENETRESET          = (HI_ERR_OS_BASE + 102),  /* Network dropped connection because of reset */
    HI_ERR_OS_ECONNABORTED       = (HI_ERR_OS_BASE + 103),  /* Software caused connection abort */
    HI_ERR_OS_ECONNRESET         = (HI_ERR_OS_BASE + 104),  /* Connection reset by peer */
    HI_ERR_OS_ENOBUFS            = (HI_ERR_OS_BASE + 105),  /* No buffer space available */
    HI_ERR_OS_EISCONN            = (HI_ERR_OS_BASE + 106),  /* Transport endpoint is already connected */
    HI_ERR_OS_ENOTCONN           = (HI_ERR_OS_BASE + 107),  /* Transport endpoint is not connected */
    HI_ERR_OS_ESHUTDOWN          = (HI_ERR_OS_BASE + 108),  /* Cannot send after transport endpoint shutdown */
    HI_ERR_OS_ETOOMANYREFS       = (HI_ERR_OS_BASE + 109),  /* Too many references: cannot splice */
    HI_ERR_OS_ETIMEDOUT          = (HI_ERR_OS_BASE + 110),  /* Connection timed out */
    HI_ERR_OS_ECONNREFUSED       = (HI_ERR_OS_BASE + 111),  /* Connection refused */
    HI_ERR_OS_EHOSTDOWN          = (HI_ERR_OS_BASE + 112),  /* Host is down */
    HI_ERR_OS_EHOSTUNREACH       = (HI_ERR_OS_BASE + 113),  /* No route to host */
    HI_ERR_OS_EALREADY           = (HI_ERR_OS_BASE + 114),  /* Operation already in progress */
    HI_ERR_OS_EINPROGRESS        = (HI_ERR_OS_BASE + 115),  /* Operation now in progress */
    HI_ERR_OS_ESTALE             = (HI_ERR_OS_BASE + 116),  /* Stale NFS file handle */
    HI_ERR_OS_EUCLEAN            = (HI_ERR_OS_BASE + 117),  /* Structure needs cleaning */
    HI_ERR_OS_ENOTNAM            = (HI_ERR_OS_BASE + 118),  /* Not a XENIX named type file */
    HI_ERR_OS_ENAVAIL            = (HI_ERR_OS_BASE + 119),  /* No XENIX semaphores available */
    HI_ERR_OS_EISNAM             = (HI_ERR_OS_BASE + 120),  /* Is a named type file */
    HI_ERR_OS_EREMOTEIO          = (HI_ERR_OS_BASE + 121),  /* Remote I/O error */
    HI_ERR_OS_EDQUOT             = (HI_ERR_OS_BASE + 122),  /* Quota exceeded */
    HI_ERR_OS_ENOMEDIUM          = (HI_ERR_OS_BASE + 123),  /* No medium found */
    HI_ERR_OS_EMEDIUMTYPE        = (HI_ERR_OS_BASE + 124),  /* Wrong medium type */
    HI_ERR_OS_CUSTOMBASEERR      = (HI_ERR_OS_BASE + 500),
    HI_ERR_OS_NOT_IMPLEMENT      = (HI_ERR_OS_BASE + 501),  /* Operation not implement */
    HI_ERR_OS_ARRAY_FULL         = (HI_ERR_OS_BASE + 502),  /* Array is full */
}hi_os_errcode_e;


/*****************************************************************************
 函 数 名  : hi_os_errno
 功能描述  : 函数获取系统全局变量errno的值
*****************************************************************************/
hi_uint32 hi_os_errno(hi_void);

/*****************************************************************************
 函 数 名  : hi_os_errset
 功能描述  : 设置全局变量errno的值
*****************************************************************************/
hi_void hi_os_errset(hi_uint32 ui_new_err_no);

/*****************************************************************************
 函 数 名  : hi_os_errset
 功能描述  : 获取全局变量errno的值对应的字符串说明信息
*****************************************************************************/
hi_char8 *hi_os_strerror(hi_uint32 ui_err_no);

hi_char8 *hi_os_errnostr(void);


#ifdef __cplusplus
#if __cplusplus
}

#endif /* __cpluscplus */
#endif /* __cpluscplus */

#endif /* __HI_OS_ERR_H__ */
