#===============================================================================
# export sub dir
#===============================================================================
HI_SUB_DIR :=
HI_SUB_DIR += Huawei_Secure_C_Library/src
HI_SUB_DIR += uos

#===============================================================================
# include
#===============================================================================
HI_LOC_INCLUDE += -I$(HI_LOC_CUR)/include
HI_LOC_INCLUDE += -I$(HI_LOC_CUR)/include/os

#===============================================================================
# export lib
#===============================================================================
HI_LOC_LIB += -lpthread -ldl

#
#===============================================================================
# target
#===============================================================================
TARGET 		= libhi_basic.so
TARGET_TYPE	= so

include $(HI_HGW_SCRIPT_DIR)/app.mk
