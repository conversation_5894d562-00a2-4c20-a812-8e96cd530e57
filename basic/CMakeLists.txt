project(GATEWAY_BASIC)

include(${CONFIG_CMAKE_DIR}/toolchains/${CONFIG_TOOLCHAIN_LINUX_APP}.cmake)

# 组件名称
set(USERAPP_NAME hi_basic)

# 组件类型
set(USERAPP_TARGET_TYPE so)

# 依赖的源文件
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/uos BASIC_SOURCE_DIR)
set(USERAPP_PRIVATE_SRC
    ${BASIC_SOURCE_DIR}
)

# 依赖的头文件
set(USERAPP_PRIVATE_INC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/include/os
)

set(BUILD_LINK_FLAGS
    -lpthread -ldl
)

# 自定义宏
set(USERAPP_PRIVATE_DEFINE)

# 自定义编译选项
set(USERAPP_PRIVATE_COMPILE)

build_app_feature()
