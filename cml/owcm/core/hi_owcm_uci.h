/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Create: 2024-11-02
 */

#ifndef __HI_OWCM_UCI_H__
#define __HI_OWCM_UCI_H__

#include <stdint.h>

int32_t hi_owcm_uci_make_file(const char *file, char *options[], char *values[]);
uint32_t hi_owcm_uci_get_section_tatol(void *fp, const char *section);
int32_t hi_owcm_uci_get_section(void *fp, const char *section, char *options[], char *values[]);
void hi_owcm_uci_put_section(char *options[], char *values[]);
int32_t hi_owcm_uci_get_file(const char *file, void **fp);
void hi_owcm_uci_put_file(void *fp);
int32_t hi_owcm_uci_init(void);
void hi_owcm_uci_exit(void);

#endif /* __HI_OWCM_UCI_H__ */
