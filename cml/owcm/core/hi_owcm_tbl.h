/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Create: 2024-11-02
 */

#ifndef __HI_OWCM_TBL_H__
#define __HI_OWCM_TBL_H__

#include <stddef.h>
#include "hi_typedef.h"
#include "os/hi_os_list.h"
#include "hi_owcm_data.h"

struct hi_owcm_tbl_uci {
	hi_list_head list;
	hi_list_head tbl_head;
	const char *file;
	const char *section;
	uint32_t uci_flag;
};

struct hi_owcm_tbl_para {
	struct hi_owcm_tbl_uci *uci;
	const char *name;
	uint16_t type; /* enum hi_owcm_data_type */
	uint16_t init;
	uint32_t size;
	uint32_t offset;
};

struct hi_owcm_tbl_op {
	int32_t (*get)(void *);
	int32_t (*set)(void *);
	int32_t (*init)(void);
};

struct hi_owcm_tbl {
	const char *name;
	struct hi_owcm_tbl_uci *uci;
	struct hi_owcm_tbl_para *para;
	struct hi_owcm_tbl_op *op;
	uint32_t size;
	uint32_t idx;
	void *data;
};

#define OWCM_SECTION "hi_owcm"
#define HI_OWCM_TBL_DEF(name) \
	static struct hi_owcm_tbl __attribute__((section(OWCM_SECTION), used)) g_##name##_tbl

int32_t hi_owcm_tbl_update(const char *file, const char *section, char *o[], char *v[]);
int32_t hi_owcm_tbl_commit(const char *file, const char *section, char *o[], char *v[]);
hi_list_head *hi_owcm_tbl_get_head(void);
int32_t hi_owcm_tbl_init(void);
void hi_owcm_tbl_exit(void);

#endif /* __HI_OWCM_TBL_H__ */
