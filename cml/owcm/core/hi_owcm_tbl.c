/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Create: 2024-11-02
 */

#include "hi_owcm_tbl.h"
#include <stdbool.h>
#include <ulog.h>
#include "hi_basic.h"
#include "hi_owcm_common.h"
#include "hi_owcm_data.h"

struct hi_owcm_tbl_node {
	hi_list_head list;
	void *tbl;
};

extern int __start_hi_owcm;
extern int __stop_hi_owcm;

static hi_list_head g_tbl_uci_head;

static inline bool hi_owcm_tbl_match_uci(struct hi_owcm_tbl_uci *uci, const char *file,
	const char *section)
{
	if (strcmp(file, uci->file) == 0 && strcmp(section, uci->section) == 0)
		return true;

	return false;
}

static struct hi_owcm_tbl_uci *hi_owcm_tbl_get_uci(const char *file, const char *section)
{
	struct hi_owcm_tbl_uci *pos = NULL, *n = NULL;

	hi_list_for_each_entry_safe(pos, n, &g_tbl_uci_head, list)
		if (hi_owcm_tbl_match_uci(pos, file, section) == true)
			return pos;

	return NULL;
}

static struct hi_owcm_tbl_uci *hi_owcm_tbl_add_uci(struct hi_owcm_tbl_uci *uci)
{
	struct hi_owcm_tbl_uci *node;

	node = malloc(sizeof(struct hi_owcm_tbl_uci));
	if (node == NULL) {
		ULOG_ERR("uci node alloc fail\n");
		return NULL;
	}

	hi_list_init_head(&node->list);
	hi_list_init_head(&node->tbl_head);
	node->file = uci->file;
	node->section = uci->section;
	node->uci_flag = uci->uci_flag;
	hi_list_add(&node->list, &g_tbl_uci_head);
	return node;
}

static int32_t hi_owcm_tbl_bind_uci(struct hi_owcm_tbl_uci *uci, struct hi_owcm_tbl *tbl)
{
	struct hi_owcm_tbl_node *node;

	node = malloc(sizeof(struct hi_owcm_tbl_node));
	if (node == NULL) {
		ULOG_ERR("tbl node alloc fail\n");
		return HI_RET_FAIL;
	}

	node->tbl = tbl;
	hi_list_add(&node->list, &uci->tbl_head);
	return HI_RET_SUCC;
}

static int32_t hi_owcm_tbl_reg_uci(struct hi_owcm_tbl *tbl)
{
	struct hi_owcm_tbl_uci *tbl_uci = NULL;
	struct hi_owcm_tbl_uci *uci = NULL;

	for (tbl_uci = tbl->uci; tbl_uci->file != NULL; tbl_uci++) {
		uci = hi_owcm_tbl_get_uci(tbl_uci->file, tbl_uci->section);
		if (uci == NULL) {
			ULOG_INFO("## Add uci node ## file[%s], setion[%s]\n", tbl_uci->file, tbl_uci->section);
			uci = hi_owcm_tbl_add_uci(tbl_uci);
		}
		if (uci == NULL) {
			ULOG_ERR("add uci node fail\n");
			return HI_RET_FAIL;
		}
		hi_owcm_tbl_bind_uci(uci, tbl);
	}

	return HI_RET_SUCC;
}

static uint32_t hi_owcm_tbl_init_para(struct hi_owcm_tbl *tbl)
{
	uint32_t i;
	uint32_t size = 0;

	for (i = 0; tbl->para[i].name != NULL; i++) {
		tbl->para[i].offset = size;
		size += tbl->para[i].size;
	}

	return size;
}

static int32_t hi_owcm_tbl_reg(struct hi_owcm_tbl *tbl)
{
	if (hi_owcm_tbl_reg_uci(tbl) != HI_RET_SUCC) {
		ULOG_ERR("reg uci list fail\n");
		return HI_RET_FAIL;
	}

	tbl->size = hi_owcm_tbl_init_para(tbl);
	if (tbl->size == 0 || (tbl->data = malloc(tbl->size)) == NULL) {
		ULOG_ERR("tbl data malloc fail, size = %d\n", tbl->size);
		return HI_RET_FAIL;
	}
	(void)memset_s(tbl->data, tbl->size, 0, tbl->size);

	if (tbl->op->init != NULL)
		return tbl->op->init();

	return HI_RET_SUCC;
}

static void hi_owcm_tbl_destroy(void)
{
	struct hi_owcm_tbl *start = (void *)&__start_hi_owcm;
	struct hi_owcm_tbl *end = (void *)&__stop_hi_owcm;
	struct hi_owcm_tbl *tbl;
	struct hi_owcm_tbl_uci *pos = NULL, *n = NULL;

	hi_list_for_each_entry_safe(pos, n, &g_tbl_uci_head, list) {
		hi_list_del(&pos->list);
		free(pos);
	}

	for (tbl = start; start < end; tbl++) {
		if (tbl->data != NULL) {
			free(tbl->data);
			tbl->data = NULL;
		}
	}
}

static uint32_t hi_owcm_tbl_paser_section(const char *section, char *tmp, uint32_t size)
{
	char *idx;

	if (strcpy_s(tmp, size, section) != EOK) {
		ULOG_ERR("paser section fail\n");
		return 0;
	}

	idx = strstr(tmp, "_");
	if (idx == NULL)
		return 0;

	*idx = 0;
	idx++;
	return strtoul(idx, NULL, 0);
}

#define TBL_VAL_MAXLEN 4096

static int32_t hi_owcm_tbl_fill_string(struct hi_owcm_tbl_para *para, char *o[], char *v[], char *data)
{
	uint32_t i, j = 0;
	char value[TBL_VAL_MAXLEN + 1] = {0};

	for (i = 0; i < UCI_OPT_MAXNUM; i++)
		if (o[i])
			j++;

	for (i = 0; para[i].uci != NULL && j < UCI_OPT_MAXNUM; i++, j++) {
		hi_owcm_data_convert(para[i].type, DATA_TO_STRING, &data[para[i].offset], value, TBL_VAL_MAXLEN);
		o[j] = strdup(para[i].name);
		v[j] = strdup(value);
		if (o[j] == NULL || v[j] == NULL)
			goto exit;
	}

	return HI_RET_SUCC;

exit:
	ULOG_ERR("malloc fail\n");
	for (i = 0; i < UCI_OPT_MAXNUM; i++) {
		if (o[i] != NULL)
			free(o[i]);
		if (v[i] != NULL)
			free(v[i]);
		o[i] = NULL;
		v[i] = NULL;
	}
	return HI_RET_FAIL;
}

static int32_t hi_owcm_tbl_fill_data(struct hi_owcm_tbl_para *para, char *o[], char *v[], char *data)
{
	uint32_t i;

	for (i = 0; o[i] != NULL && v[i] != NULL; i++) {
		if (strcmp(para->name, o[i]) != 0)
			continue;
		if (hi_owcm_data_convert(para->type, STRING_TO_DATA, &data[para->offset], v[i],
			para->size) != HI_RET_SUCC) {
			ULOG_ERR("convert data fail, name[%s], type[%d], val[%s]\n",
				para->name, para->type, v[i]);
			return HI_RET_FAIL;
		}
		break;
	}
	if (para->init == 0)
		para->init = 1;

	if (o[i] == NULL) {
		(void)memset_s(&data[para->offset], para->size, 0, para->size);
		ULOG_WARN("cannot find option[%s]\n", para->name);
	}
	return HI_RET_SUCC;
}

static int32_t hi_owcm_tbl_distribute(struct hi_owcm_tbl *tbl, const char *file,
	const char *section, char *o[], char *v[])
{
	uint32_t i;

	for (i = 0; tbl->para[i].name != NULL && tbl->para[i].uci != NULL; i++) {
		if (hi_owcm_tbl_match_uci(tbl->para[i].uci, file, section) == false)
			continue;
		if (hi_owcm_tbl_fill_data(&tbl->para[i], o, v, tbl->data) != HI_RET_SUCC)
			return HI_RET_FAIL;
	}
	for (i = 0; tbl->para[i].name != NULL; i++)
		if (tbl->para[i].init == 0) {
			ULOG_INFO("para init incompletely");
			return HI_RET_SUCC;
		}

	return tbl->op->set(tbl->data);
}

int32_t hi_owcm_tbl_commit(const char *file, const char *section, char *o[], char *v[])
{
	int32_t ret = HI_RET_SUCC;
	struct hi_owcm_tbl *tbl;
	struct hi_owcm_tbl_node *pos = NULL, *n = NULL;
	struct hi_owcm_tbl_uci *uci;
	char sec_type[OWCM_TMP_BUFLEN] = {0};
	uint32_t idx = hi_owcm_tbl_paser_section(section, sec_type, sizeof(sec_type));

	uci = hi_owcm_tbl_get_uci(file, sec_type);
	if (uci == NULL) {
		ULOG_ERR("find uci tbl fail, name = %s.%s\n", file, section);
		return HI_RET_FAIL;
	}

	hi_list_for_each_entry_safe(pos, n, &uci->tbl_head, list) {
		tbl = pos->tbl;
		if (tbl->op->set == NULL || idx != tbl->idx)
			continue;
		if (hi_owcm_tbl_distribute(tbl, file, sec_type, o, v) != HI_RET_SUCC) {
			ULOG_ERR("distribute tbl[%s] fail", tbl->name);
			ret = HI_RET_FAIL;
		}
	}

	return ret;
}

int32_t hi_owcm_tbl_update(const char *file, const char *section, char *o[], char *v[])
{
	struct hi_owcm_tbl *tbl;
	struct hi_owcm_tbl_node *pos = NULL, *n = NULL;
	struct hi_owcm_tbl_uci *uci = hi_owcm_tbl_get_uci(file, section);

	if (uci == NULL) {
		ULOG_ERR("find uci tbl fail, name = %s.%s\n", file, section);
		return HI_RET_FAIL;
	}

	hi_list_for_each_entry_safe(pos, n, &uci->tbl_head, list) {
		tbl = pos->tbl;
		if (tbl->op->get == NULL)
			continue;
		if (tbl->op->get(tbl->data)) {
			ULOG_ERR("Get tbl[%s] fail\n", tbl->name);
			continue;
		}
		if (hi_owcm_tbl_fill_string(tbl->para, o, v, tbl->data) != HI_RET_SUCC)
			return HI_RET_FAIL;
	}

	return HI_RET_SUCC;
}

hi_list_head *hi_owcm_tbl_get_head(void)
{
	return &g_tbl_uci_head;
}

int32_t hi_owcm_tbl_init(void)
{
	struct hi_owcm_tbl *start = (void *)&__start_hi_owcm;
	struct hi_owcm_tbl *end = (void *)&__stop_hi_owcm;
	struct hi_owcm_tbl *tbl;

	hi_list_init_head(&g_tbl_uci_head);

	for (tbl = start; tbl < end; tbl++) {
		if (hi_owcm_tbl_reg(tbl) != HI_RET_SUCC) {
			ULOG_ERR("config tbl init fail\n");
			hi_owcm_tbl_destroy();
			return HI_RET_FAIL;
		}
	}

	return HI_RET_SUCC;
}

void hi_owcm_tbl_exit(void)
{
	hi_owcm_tbl_destroy();
}
