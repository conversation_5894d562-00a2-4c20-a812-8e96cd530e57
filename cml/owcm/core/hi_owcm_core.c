/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Create: 2024-11-02
 */

#include "hi_owcm_core.h"
#include <ulog.h>
#include "hi_basic.h"
#include "hi_owcm_common.h"
#include "hi_owcm_tbl.h"
#include "hi_owcm_uci.h"

static int hi_owcm_splits_path(char *path, char **file, char **section)
{
	*file = path;
	*section = strchr(path, '.');
	if (*section == NULL)
		return HI_RET_INVALID_PARA;

	*(*section)++ = 0;
	return HI_RET_SUCC;
}

static int32_t hi_owcm_commit_all(const char *file, const char *section)
{
	uint32_t total, i;
	void *fp;
	char section_name[OWCM_TMP_BUFLEN];
	char *option[UCI_OPT_MAXNUM];
	char *value[UCI_OPT_MAXNUM];

	ULOG_INFO("CM [%s.%s] init, start to distribute\n", file, section);

	if (hi_owcm_uci_get_file(file, &fp) != HI_RET_SUCC) {
		ULOG_ERR("invalid uci config [%s.%s]\n", file, section);
		return HI_RET_FAIL;
	}

	total = hi_owcm_uci_get_section_tatol(fp, section);
	for (i = 1; i <= total; i++) {
		if (total > 1) {
			if (sprintf_s(section_name, sizeof(section_name), "%s_%u", section, i) == 0)
				continue;
		} else {
			if (strcpy_s(section_name, sizeof(section_name), section) != EOK)
				continue;
		}

		(void)memset_s(option, sizeof(option), 0, sizeof(option));
		(void)memset_s(value, sizeof(value), 0, sizeof(value));
		if (hi_owcm_uci_get_section(fp, section_name, option, value) != HI_RET_SUCC)
			continue;
		hi_owcm_tbl_commit(file, section_name, option, value);
		hi_owcm_uci_put_section(option, value);
	}

	hi_owcm_uci_put_file(fp);
	return HI_RET_SUCC;
}

static int32_t hi_owcm_commit_cfg(const char *file, const char *section)
{
	int32_t ret;
	void *fp;
	char *option[UCI_OPT_MAXNUM] = {NULL};
	char *value[UCI_OPT_MAXNUM] = {NULL};

	ULOG_INFO("uci [%s.%s] changed, start to distribute\n", file, section);

	if (hi_owcm_uci_get_file(file, &fp) != HI_RET_SUCC) {
		ULOG_ERR("invalid uci config [%s.%s]\n", file, section);
		return HI_RET_FAIL;
	}
	if (hi_owcm_uci_get_section(fp, section, option, value) != HI_RET_SUCC) {
		hi_owcm_uci_put_file(fp);
		return HI_RET_FAIL;
	}

	ret = hi_owcm_tbl_commit(file, section, option, value);

	hi_owcm_uci_put_section(option, value);
	hi_owcm_uci_put_file(fp);
	return ret;
}

static int32_t hi_owcm_update_cfg(const char *file, const char *section)
{
	char *option[UCI_OPT_MAXNUM] = {NULL};
	char *value[UCI_OPT_MAXNUM] = {NULL};

	if (hi_owcm_tbl_update(file, section, option, value) != HI_RET_SUCC) {
		ULOG_ERR("Get tbl[%s.%s] fail!\n", file, section);
		return HI_RET_FAIL;
	}

	return hi_owcm_uci_make_file(file, option, value);
}

int32_t hi_owcm_commit(char *path)
{
	char tmp[OWCM_TMP_BUFLEN] = {0};
	char *file = NULL;
	char *section = NULL;

	if (strcpy_s(tmp, sizeof(tmp), path) != EOK) {
		ULOG_ERR("owcm copy fail, input len = %d\n", strlen(path));
		return HI_RET_FAIL;
	}

	if (hi_owcm_splits_path(tmp, &file, &section)) {
		ULOG_ERR("owcm splits path fail\n");
		return HI_RET_FAIL;
	}

	return hi_owcm_commit_cfg(file, section);
}

int32_t hi_owcm_update(char *path)
{
	char tmp[OWCM_TMP_BUFLEN] = {0};
	char *file = NULL;
	char *section = NULL;

	if (strcpy_s(tmp, sizeof(tmp), path) != EOK) {
		ULOG_ERR("owcm copy fail, input len = %d\n", strlen(path));
		return HI_RET_FAIL;
	}

	if (hi_owcm_splits_path(tmp, &file, &section)) {
		ULOG_ERR("owcm splits path fail\n");
		return HI_RET_FAIL;
	}

	return hi_owcm_update_cfg(file, section);
}

static int32_t hi_owcm_modules_init(void)
{
	struct hi_owcm_tbl_uci *pos = NULL, *n = NULL;
	hi_list_head *uci_head = hi_owcm_tbl_get_head();

	hi_list_for_each_entry_safe(pos, n, uci_head, list) {
		if (pos->uci_flag == 0)
			continue;
		ULOG_INFO("## Init ## [%s.%s] start\n", pos->file, pos->section);
		if (hi_owcm_commit_all(pos->file, pos->section) != HI_RET_SUCC)
			ULOG_ERR("init [%s.%s] fail\n", pos->file, pos->section);
	}
	return HI_RET_SUCC;
}

int32_t hi_owcm_init(void)
{
	if (hi_owcm_uci_init() != HI_RET_SUCC)
		return HI_RET_FAIL;

	if (hi_owcm_tbl_init() != HI_RET_SUCC) {
		hi_owcm_uci_exit();
		return HI_RET_FAIL;
	}

	return hi_owcm_modules_init();
}

void hi_owcm_exit(void)
{
	hi_owcm_tbl_exit();
	hi_owcm_uci_exit();
	ULOG_ERR("owcm exit\n");
}
