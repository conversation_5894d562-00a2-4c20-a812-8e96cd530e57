/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Create: 2024-12-03
 */

#include <ulog.h>
#include "hi_basic.h"
#include "hi_ipc.h"
#include "hi_owcm_tbl.h"
#include "voip_api.h"

static void hi_owcm_voice_set_nft(uint32_t port)
{
	FILE *fp = NULL;
	char *rule = NULL, *dport = NULL;
	char tmp[256] = {0};
	uint32_t index, old_port;

	fp = popen("uci show firewall |grep Allow-Voice", "r");
	if (fp == NULL)
		return;
	(void)fgets(tmp, sizeof(tmp), fp);
	pclose(fp);
	rule = strstr(tmp, "rule[");
	if (rule == NULL)
		return;
	rule += strlen("rule[");
	index = strtoul(rule, NULL, 0);

	(void)sprintf_s(tmp, sizeof(tmp), "uci show firewall |grep \"rule\\[%u\\]\" |grep port", index);
	fp = popen(tmp, "r");
	if (fp == NULL)
		return;
	(void)fgets(tmp, sizeof(tmp), fp);
	pclose(fp);
	dport = strstr(tmp, "port='");
	if (dport == NULL)
		return;

	dport += strlen("port='");
	old_port = strtoul(dport, NULL, 0);
	if (old_port != port) {
		hi_os_vcmd("uci set firewall.@rule[%d].dest_port=%d", index, port);
		hi_os_vcmd("uci commit");
		hi_os_vcmd("fw4 -q reload");
	}
}

/******************************VOICE PROTOCOL TBL*******************************/
static int32_t hi_owcm_voice_set_protocol(void *data)
{
	uint32_t *protocol = data;
	voip_procotol_type protocol_type = {0};

	protocol_type.tab_id = VOIP_IPC_CALL_PROTOCOL_CFG_ID;
	protocol_type.voip_protocol = *protocol;
	if (HI_IPC_CALL("hi_voip_cfg_app", &protocol_type) != 0) {
		ULOG_ERR("=====Set protocol fail.\n");
		return HI_RET_FAIL;
	}

	return HI_RET_SUCC;
}

struct hi_owcm_tbl_uci g_protocol_uci[] = {
	{
		.file = "voice",
		.section = "Protocol",
		.uci_flag = 1,
	},
	{
		.file = NULL
	},
};

struct hi_owcm_tbl_para g_protocol_para[] = {
	{
		.uci = &g_protocol_uci[0],
		.name = "protocol",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{NULL},
};

static struct hi_owcm_tbl_op g_protocol_op = {
	.set = hi_owcm_voice_set_protocol,
};

HI_OWCM_TBL_DEF(voice_protocol) = {
	.name = "voiceProtocol",
	.uci = g_protocol_uci,
	.para = g_protocol_para,
	.op = &g_protocol_op,
};

/******************************SIP BASIC ATTR TBL*******************************/
#define BASIC_ARRT_TBL_BITMAP 0x301B6DB
struct hi_owcm_voice_basic_attr {
	char proxy_server[VOIP_SIP_SERVER_ADDRESS];
	uint32_t proxy_server_port;
	char registrar_server[VOIP_SIP_SERVER_ADDRESS];
	uint32_t registrar_server_port;
	char outbound_proxy_server[VOIP_SIP_SERVER_ADDRESS];
	uint32_t outbound_proxy_server_port;
	char standby_proxy_server[VOIP_SIP_SERVER_ADDRESS];
	uint32_t standby_proxy_server_port;
	char standby_registrar_server[VOIP_SIP_SERVER_ADDRESS];
	uint32_t standby_registrar_server_port;
	char standby_outbound_proxy_server[VOIP_SIP_SERVER_ADDRESS];
	uint32_t standby_outbound_proxy_server_port;
	uint32_t heart_beat_switch;
	uint32_t heart_beat_cycle;
};

static int32_t hi_owcm_voice_set_basic_attr(void *data)
{
	struct hi_owcm_voice_basic_attr *basic_attr = data;
	VOIP_SIP_PARAMETER sip_para = {0};

	sip_para.tab_id = VOIP_IPC_CALL_SIP_CFG_ID;
	sip_para.sip_cfg_bitmap = BASIC_ARRT_TBL_BITMAP;
	(void)memcpy_s(sip_para.sip_param_config.server_ip, VOIP_SIP_SERVER_ADDRESS,
				basic_attr->proxy_server, VOIP_SIP_SERVER_ADDRESS);
	sip_para.sip_param_config.serv_com_port = basic_attr->proxy_server_port;
	(void)memcpy_s(sip_para.sip_param_config.reg_server_ip, VOIP_SIP_SERVER_ADDRESS,
				basic_attr->registrar_server, VOIP_SIP_SERVER_ADDRESS);
	sip_para.sip_param_config.reg_serv_com_port = basic_attr->registrar_server_port;
	(void)memcpy_s(sip_para.sip_param_config.outbound_server_ip, VOIP_SIP_SERVER_ADDRESS,
				basic_attr->outbound_proxy_server, VOIP_SIP_SERVER_ADDRESS);
	sip_para.sip_param_config.outbound_serv_com_port = basic_attr->outbound_proxy_server_port;
	(void)memcpy_s(sip_para.sip_param_config.back_server_ip, VOIP_SIP_SERVER_ADDRESS,
				basic_attr->standby_proxy_server, VOIP_SIP_SERVER_ADDRESS);
	sip_para.sip_param_config.back_serv_com_port = basic_attr->standby_proxy_server_port;
	(void)memcpy_s(sip_para.sip_param_config.back_reg_server_ip, VOIP_SIP_SERVER_ADDRESS,
				basic_attr->standby_registrar_server, VOIP_SIP_SERVER_ADDRESS);
	sip_para.sip_param_config.back_reg_serv_com_port = basic_attr->standby_registrar_server_port;
	(void)memcpy_s(sip_para.sip_Standby_OutboundProxy_ip, VOIP_SIP_SERVER_ADDRESS,
				basic_attr->standby_outbound_proxy_server, VOIP_SIP_SERVER_ADDRESS);
	sip_para.sip_Standby_OutboundProxy_port = basic_attr->standby_outbound_proxy_server_port;
	sip_para.sip_param_config.heart_beat_switch = basic_attr->heart_beat_switch;
	sip_para.sip_param_config.heart_beat_cycle = basic_attr->heart_beat_cycle;
	if (HI_IPC_CALL("hi_voip_cfg_app", &sip_para) != 0) {
		ULOG_ERR("=====Set sip basic attr fail.\n");
		return HI_RET_FAIL;
	}

	hi_owcm_voice_set_nft(sip_para.sip_param_config.reg_serv_com_port);
	return HI_RET_SUCC;
}

struct hi_owcm_tbl_uci g_sip_basic_attr_uci[] = {
	{
		.file = "voice",
		.section = "SipBasicAttr",
		.uci_flag = 1,
	},
	{
		.file = NULL
	},
};

struct hi_owcm_tbl_para g_sip_basic_attr_para[] = {
	{
		.uci = &g_sip_basic_attr_uci[0],
		.name = "proxy_server",
		.type = HI_OWCM_PARA_STRING,
		.size = VOIP_SIP_SERVER_ADDRESS,
	},
	{
		.uci = &g_sip_basic_attr_uci[0],
		.name = "proxy_server_port",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_sip_basic_attr_uci[0],
		.name = "registrar_server",
		.type = HI_OWCM_PARA_STRING,
		.size = VOIP_SIP_SERVER_ADDRESS,
	},
	{
		.uci = &g_sip_basic_attr_uci[0],
		.name = "registrar_server_port",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_sip_basic_attr_uci[0],
		.name = "outbound_proxy_server",
		.type = HI_OWCM_PARA_STRING,
		.size = VOIP_SIP_SERVER_ADDRESS,
	},
	{
		.uci = &g_sip_basic_attr_uci[0],
		.name = "outbound_proxy_server_port",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_sip_basic_attr_uci[0],
		.name = "standby_proxy_server",
		.type = HI_OWCM_PARA_STRING,
		.size = VOIP_SIP_SERVER_ADDRESS,
	},
	{
		.uci = &g_sip_basic_attr_uci[0],
		.name = "standby_proxy_server_port",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_sip_basic_attr_uci[0],
		.name = "standby_registrar_server",
		.type = HI_OWCM_PARA_STRING,
		.size = VOIP_SIP_SERVER_ADDRESS,
	},
	{
		.uci = &g_sip_basic_attr_uci[0],
		.name = "standby_registrar_server_port",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_sip_basic_attr_uci[0],
		.name = "standby_outbound_proxy_server",
		.type = HI_OWCM_PARA_STRING,
		.size = VOIP_SIP_SERVER_ADDRESS,
	},
	{
		.uci = &g_sip_basic_attr_uci[0],
		.name = "standby_outbound_proxy_server_port",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_sip_basic_attr_uci[0],
		.name = "heart_beat_switch",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_sip_basic_attr_uci[0],
		.name = "heart_beat_cycle",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{NULL},
};

static struct hi_owcm_tbl_op g_sip_basic_attr_op = {
	.set = hi_owcm_voice_set_basic_attr,
};

HI_OWCM_TBL_DEF(voice_sipbasicattr) = {
	.name = "SipBasicAttrTable",
	.uci = g_sip_basic_attr_uci,
	.para = g_sip_basic_attr_para,
	.op = &g_sip_basic_attr_op,
};

/******************************VOICE SIP ADV USER TBL*******************************/
#define MINUTE_TO_SECOND 60
#define SIP_ADVANCED_BITMAP 0x80800000
struct hi_owcm_voice_sip_adv_user {
	uint32_t session_update_time;
	uint32_t require_timer;
};

static int32_t hi_owcm_voice_set_sip_adv_user(void *data)
{
	struct hi_owcm_voice_sip_adv_user *sip_adv_user = data;
	VOIP_SIP_PARAMETER sip_para = {0};

	sip_para.tab_id = VOIP_IPC_CALL_SIP_CFG_ID;
	sip_para.sip_cfg_bitmap = SIP_ADVANCED_BITMAP;
	sip_para.session_update_time = sip_adv_user->session_update_time * MINUTE_TO_SECOND;
	sip_para.require_timer = sip_adv_user->require_timer;
	if (HI_IPC_CALL("hi_voip_cfg_app", &sip_para) != 0) {
		ULOG_ERR("=====Set sip advanced fail.\n");
		return HI_RET_FAIL;
	}

	return HI_RET_SUCC;
}

struct hi_owcm_tbl_uci g_sip_adv_user_uci[] = {
	{
		.file = "voice",
		.section = "SipAdvanced",
		.uci_flag = 1,
	},
	{
		.file = NULL
	},
};

struct hi_owcm_tbl_para g_sip_adv_user_para[] = {
	{
		.uci = &g_sip_adv_user_uci[0],
		.name = "session_update_time",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_sip_adv_user_uci[0],
		.name = "require_timer",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{NULL},
};

static struct hi_owcm_tbl_op g_sip_adv_user_op = {
	.set = hi_owcm_voice_set_sip_adv_user,
};

HI_OWCM_TBL_DEF(voice_sip_adv_user) = {
	.name = "SipAdvUserTable",
	.uci = g_sip_adv_user_uci,
	.para = g_sip_adv_user_para,
	.op = &g_sip_adv_user_op,
};

/******************************SIP USER TBL*******************************/
#define	SIP_USER_TBL_BITMAP 0xf
struct hi_owcm_voice_sip_user {
	uint32_t enable;
	char auth_username[VOIP_AUTHENTICATION_USER_SIZE];
	char auth_pwd[VOIP_AUTHENTICATION_PAWD_SIZE];
	char number_url[VOIP_AUTHENTICATION_NUMBER_SIZE];
};

static int32_t hi_owcm_voice_set_sip_user(void *data)
{
	struct hi_owcm_voice_sip_user *sip_user = data;
	VOIP_AUTHENTICATION user_auth = {0};

	user_auth.tab_id = VOIP_IPC_CALL_SIP_USERINFO_ID;
	user_auth.line_id = 1;
	user_auth.user_mask = SIP_USER_TBL_BITMAP;
	user_auth.voip_user_status = sip_user->enable;
	(void)memcpy_s(user_auth.voip_user, VOIP_AUTHENTICATION_USER_SIZE,
		sip_user->auth_username, VOIP_AUTHENTICATION_USER_SIZE);
	(void)memcpy_s(user_auth.voip_passwd, VOIP_AUTHENTICATION_PAWD_SIZE,
		sip_user->auth_pwd, VOIP_AUTHENTICATION_PAWD_SIZE);
	(void)memcpy_s(user_auth.voip_number, VOIP_AUTHENTICATION_NUMBER_SIZE,
		sip_user->number_url, VOIP_AUTHENTICATION_NUMBER_SIZE);
	if (HI_IPC_CALL("hi_voip_cfg_app", &user_auth) != HI_RET_SUCC) {
		ULOG_ERR("IPC_CALL_SIP_USERINFO fail\n");
		return HI_RET_FAIL;
	}

	return HI_RET_SUCC;
}

struct hi_owcm_tbl_uci g_sip_user_uci[] = {
	{
		.file = "voice",
		.section = "SipUserInfo",
		.uci_flag = 1,
	},
	{
		.file = NULL
	},
};

struct hi_owcm_tbl_para g_sip_user_para[] = {
	{
		.uci = &g_sip_user_uci[0],
		.name = "enable",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_sip_user_uci[0],
		.name = "auth_username",
		.type = HI_OWCM_PARA_STRING,
		.size = VOIP_AUTHENTICATION_USER_SIZE,
	},
	{
		.uci = &g_sip_user_uci[0],
		.name = "auth_pwd",
		.type = HI_OWCM_PARA_STRING,
		.size = VOIP_AUTHENTICATION_PAWD_SIZE,
	},
	{
		.uci = &g_sip_user_uci[0],
		.name = "number_url",
		.type = HI_OWCM_PARA_STRING,
		.size = VOIP_AUTHENTICATION_NUMBER_SIZE,
	},
	{NULL},
};

static struct hi_owcm_tbl_op g_sip_user_op = {
	.set = hi_owcm_voice_set_sip_user,
};

HI_OWCM_TBL_DEF(voice_sip_user) = {
	.name = "SipUsrTable",
	.uci = g_sip_user_uci,
	.para = g_sip_user_para,
	.op = &g_sip_user_op,
};

/******************************REG STATUS TBL*******************************/
static int32_t hi_owcm_voice_get_regstatus(void *data)
{
	uint32_t *status = data;
	VOIP_USER_STATUS user_status = {0};

	user_status.line_id = 1;
	user_status.tab_id = VOIP_IPC_CALL_GET_USER_STATUS;

	if (HI_IPC_CALL("hi_voip_get_cfg_app", &user_status)) {
		ULOG_ERR("=====Get register status fail\n");
		return HI_RET_FAIL;
	}

	*status = user_status.use_register_result;
	return HI_RET_SUCC;
}

struct hi_owcm_tbl_uci g_reg_status_path[] = {
	{
		.file = "voice",
		.section = "RegStatus",
		.uci_flag = 0,
	},
	{
		.file = NULL
	},
};

struct hi_owcm_tbl_para g_reg_status_para[] = {
	{
		.uci = &g_reg_status_path[0],
		.name = "reg_status",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{NULL},
};

static struct hi_owcm_tbl_op g_reg_status_op = {
	.get = hi_owcm_voice_get_regstatus,
};

HI_OWCM_TBL_DEF(voice_reg_status) = {
	.name = "RegStatus",
	.uci = g_reg_status_path,
	.para = g_reg_status_para,
	.op = &g_reg_status_op,
};

/******************************VOICE ADVANCED ATTR TBL*******************************/
#define ADVANCED_ATTR_TBL_BITMAP (VOICE_ADV_ATTR_MASK_T_BIT0_URI_TYPE | \
	VOICE_ADV_ATTR_MASK_T_BIT1_DTMF_METHOD | \
	VOICE_ADV_ATTR_MASK_T_BIT2_SIP_DSCP_MARK | \
	VOICE_ADV_ATTR_MASK_T_BIT3_RTP_DSCP_MARK | \
	VOICE_ADV_ATTR_MASK_T_BIT4_SIP_LOCAL_PORT | \
	VOICE_ADV_ATTR_MASK_T_BIT5_REG_EXPIRE_TIME | \
	VOICE_ADV_ATTR_MASK_T_BIT6_REG_RETRY_INTERVAL | \
	VOICE_ADV_ATTR_MASK_T_BIT8_RTP_PORT_START | \
	VOICE_ADV_ATTR_MASK_T_BIT9_RTP_PORT_END | \
	VOICE_ADV_ATTR_MASK_T_BIT10_PRACK_ENABLE | \
	VOICE_ADV_ATTR_MASK_T_BIT11_FLASH_MIN_TIME | \
	VOICE_ADV_ATTR_MASK_T_BIT12_FLASH_MAX_TIME | \
	VOICE_ADV_ATTR_MASK_T_BIT13_BOOT_DEREG | \
	VOICE_ADV_ATTR_MASK_T_BIT14_VAD_CNG_ENABLE)

struct hi_owcm_voice_advanced {
	uint32_t uri_type;
	uint32_t dtmf_method;
	uint32_t sip_local_port;
	uint32_t sip_dscp_mark;
	uint32_t rtp_dscp_mark;
	uint32_t register_expire_time;
	uint32_t register_retry_interval;
	uint32_t rtp_port_start;
	uint32_t rtp_port_end;
	uint32_t prack_enable;
	uint32_t boot_deregister_enable;
	uint32_t flash_min_time;
	uint32_t flash_max_time;
	uint32_t vadcng_enable;
};

static int32_t hi_owcm_voice_set_advanced(void *data)
{
	struct hi_owcm_voice_advanced *advanced_attr = data;
	voice_advanced_cfg advanced_config = {0};

	advanced_config.tab_id = VOIP_IPC_CALL_VOICE_ADVICE_CFG_ID;
	advanced_config.voip_advice_bitmap = ADVANCED_ATTR_TBL_BITMAP;
	advanced_config.sip_head_type = advanced_attr->uri_type;
	advanced_config.voice_dtmf_type = advanced_attr->dtmf_method;
	advanced_config.ulSipLocalPort = advanced_attr->sip_local_port;
	advanced_config.ucSipDscpMark = advanced_attr->sip_dscp_mark;
	advanced_config.ucRtpDscpMark = advanced_attr->rtp_dscp_mark;
	advanced_config.ulRegisterExpireTime = advanced_attr->register_expire_time;
	advanced_config.ulRegisterRetryInterval = advanced_attr->register_retry_interval;
	advanced_config.ulRtpPortStart = advanced_attr->rtp_port_start;
	advanced_config.ulRtpPortEnd = advanced_attr->rtp_port_end;
	advanced_config.sip_prack_type = advanced_attr->prack_enable;
	advanced_config.voice_boot_regiseter = advanced_attr->boot_deregister_enable;
	advanced_config.ucFlashMinTime = advanced_attr->flash_min_time;
	advanced_config.ucFlashMaxTime = advanced_attr->flash_max_time;
	advanced_config.silence_type = advanced_attr->vadcng_enable;
	if (HI_IPC_CALL("hi_voip_cfg_app", &advanced_config)) {
		ULOG_ERR("=====Set advanced attr fail.\n");
		return HI_RET_FAIL;
	}

	return HI_RET_SUCC;
}

struct hi_owcm_tbl_uci g_advanced_attr_uci[] = {
	{
		.file = "voice",
		.section = "AdvancedAttr",
		.uci_flag = 1,
	},
	{
		.file = NULL
	},
};

struct hi_owcm_tbl_para g_advanced_para[] = {
	{
		.uci = &g_advanced_attr_uci[0],
		.name = "uri_type",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_advanced_attr_uci[0],
		.name = "dtmf_method",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_advanced_attr_uci[0],
		.name = "sip_local_port",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_advanced_attr_uci[0],
		.name = "sip_dscp_mark",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_advanced_attr_uci[0],
		.name = "rtp_dscp_mark",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_advanced_attr_uci[0],
		.name = "register_expire_time",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_advanced_attr_uci[0],
		.name = "register_retry_interval",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_advanced_attr_uci[0],
		.name = "rtp_port_start",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_advanced_attr_uci[0],
		.name = "rtp_port_end",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_advanced_attr_uci[0],
		.name = "prack_enable",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_advanced_attr_uci[0],
		.name = "boot_deregister_enable",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_advanced_attr_uci[0],
		.name = "flash_min_time",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_advanced_attr_uci[0],
		.name = "flash_max_time",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_advanced_attr_uci[0],
		.name = "vadcng_enable",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{NULL},
};

static struct hi_owcm_tbl_op g_advanced_op = {
	.set = hi_owcm_voice_set_advanced,
};

HI_OWCM_TBL_DEF(voice_advanced) = {
	.name = "AdvancedTable",
	.uci = g_advanced_attr_uci,
	.para = g_advanced_para,
	.op = &g_advanced_op,
};

/******************************VOICE ADVANCED SUPPLEMENT TBL*******************************/
#define ADVANCED_SUPPLEMENT_BITMAP (VOICE_ADV_ATTR_MASK_T_BIT15_POLA_REVERSE | \
	VOICE_ADV_ATTR_MASK_T_BIT16_TIME_SYNC_MODE | \
	VOICE_ADV_ATTR_MASK_T_BIT17_CALLID_SHOW_MODE | \
	VOICE_ADV_ATTR_MASK_T_BIT18_CALLING_SUBSCIB_MODE)
struct hi_owcm_voice_adv_sup {
	uint32_t time_sync_mode;
	uint32_t callid_show_mode;
	uint32_t subscribe_enable;
	uint32_t polarity_reverse_enable;
};

static int32_t hi_owcm_voice_set_adv_sup(void *data)
{
	struct hi_owcm_voice_adv_sup *supplement = data;
	voice_advanced_cfg advanced_config = {0};

	advanced_config.tab_id = VOIP_IPC_CALL_VOICE_ADVICE_CFG_ID;
	advanced_config.voip_advice_bitmap = ADVANCED_SUPPLEMENT_BITMAP;
	advanced_config.voice_polary_type = supplement->polarity_reverse_enable;
	advanced_config.voice_time_sync_type = supplement->time_sync_mode;
	advanced_config.voice_callshow_type = supplement->callid_show_mode;
	advanced_config.subscribe_type = supplement->subscribe_enable;
	if (HI_IPC_CALL("hi_voip_cfg_app", &advanced_config)) {
		ULOG_ERR("=====Set advanced supplement fail.\n");
		return HI_RET_FAIL;
	}

	return HI_RET_SUCC;
}

struct hi_owcm_tbl_uci g_adv_sup_uci[] = {
	{
		.file = "voice",
		.section = "AdvSup",
		.uci_flag = 1,
	},
	{
		.file = "voice",
		.section = "HotlineService",
		.uci_flag = 1,
	},
	{
		.file = NULL
	},
};

struct hi_owcm_tbl_para g_adv_sup_para[] = {
	{
		.uci = &g_adv_sup_uci[0],
		.name = "time_sync_mode",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_adv_sup_uci[0],
		.name = "callid_show_mode",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_adv_sup_uci[0],
		.name = "subscribe_enable",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_adv_sup_uci[1],
		.name = "polarity_reverse_enable",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{NULL},
};

static struct hi_owcm_tbl_op g_adv_sup_op = {
	.set = hi_owcm_voice_set_adv_sup,
};

HI_OWCM_TBL_DEF(voice_adv_sup) = {
	.name = "AdvSupTable",
	.uci = g_adv_sup_uci,
	.para = g_adv_sup_para,
	.op = &g_adv_sup_op,
};

/******************************VOICE DIGIT ATTR TBL*******************************/
#define DIGIT_ARRT_BIRMAP (VOICE_GENTRAL_ATTR_MASK_T_BIT1_DIGIT_MAP_ENABLE | \
	VOICE_GENTRAL_ATTR_MASK_T_BIT2_DIGIT_MAP_MATCH_MODE | \
	VOICE_GENTRAL_ATTR_MASK_T_BIT3_IMMEDIATLY_DIGIT | \
	VOICE_GENTRAL_ATTR_MASK_T_BIT4_OFF_HOOK_TIME | \
	VOICE_GENTRAL_ATTR_MASK_T_BIT5_DIGIT_TIME_SHORT | \
	VOICE_GENTRAL_ATTR_MASK_T_BIT6_DIGIT_TIME_LONG | \
	VOICE_GENTRAL_ATTR_MASK_T_BIT10_T_TIMEOUT_WAITING_TIME | \
	VOICE_GENTRAL_ATTR_MASK_T_BIT11_DOT_TIMEOUT_WAITING_TIME | \
	VOICE_GENTRAL_ATTR_MASK_T_BIT12_DOT_DIGIT_MAP)

struct hi_owcm_voice_digit_attr {
	uint32_t digitmap_enable;
	uint32_t digitmap_match_mode;
	uint32_t immediatly_digit;
	uint32_t offhook_start_digit_time;
	uint32_t digit_time_short;
	uint32_t digit_time_long;
	uint32_t t_timeout_waiting_time;
	uint32_t d_timeout_waiting_time;
	char digitmap[VOICE_DIGIG_MAP_STRING_LENTH];
};

static int32_t hi_owcm_voice_set_digit_attr(void *data)
{
	struct hi_owcm_voice_digit_attr *digit_attr = data;
	VOIP_DIGIT_VOICE_PROFILE voice_profile = {0};

	voice_profile.tab_id = VOIP_IPC_CALL_DIGIT_SERVICE_ID;
	voice_profile.attr_time_bitmap = DIGIT_ARRT_BIRMAP;
	voice_profile.digit_map_enable = digit_attr->digitmap_enable;
	voice_profile.digit_match_type = digit_attr->digitmap_match_mode;
	voice_profile.immediaty_dial = digit_attr->immediatly_digit;
	voice_profile.startDigitTimer = digit_attr->offhook_start_digit_time;
	voice_profile.startDigitTimer_short = digit_attr->digit_time_short;
	voice_profile.startDigitTimer_long = digit_attr->digit_time_long;
	voice_profile.t_timeoutTimer = digit_attr->t_timeout_waiting_time;
	voice_profile.d_timeoutTimer = digit_attr->d_timeout_waiting_time;
	(void)memcpy_s(voice_profile.digitmap, VOICE_DIGIG_MAP_STRING_LENTH,
				digit_attr->digitmap, VOICE_DIGIG_MAP_STRING_LENTH);
	if (HI_IPC_CALL("hi_voip_cfg_app", &voice_profile) != 0) {
		ULOG_ERR("=====Set general attr fail.\n");
		return HI_RET_FAIL;
	}

	return HI_RET_SUCC;
}

struct hi_owcm_tbl_uci g_digit_attr_uci[] = {
	{
		.file = "voice",
		.section = "DigitAttr",
		.uci_flag = 1,
	},
	{
		.file = NULL
	},
};

struct hi_owcm_tbl_para g_digit_attr_para[] = {
	{
		.uci = &g_digit_attr_uci[0],
		.name = "digitmap_enable",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_digit_attr_uci[0],
		.name = "digitmap_match_mode",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_digit_attr_uci[0],
		.name = "immediatly_digit",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_digit_attr_uci[0],
		.name = "offhook_start_digit_time",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_digit_attr_uci[0],
		.name = "digit_time_short",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_digit_attr_uci[0],
		.name = "digit_time_long",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_digit_attr_uci[0],
		.name = "t_timeout_waiting_time",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_digit_attr_uci[0],
		.name = "d_timeout_waiting_time",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_digit_attr_uci[0],
		.name = "digitmap",
		.type = HI_OWCM_PARA_STRING,
		.size = VOICE_DIGIG_MAP_STRING_LENTH,
	},
	{NULL},
};

static struct hi_owcm_tbl_op g_digit_attr_op = {
	.set = hi_owcm_voice_set_digit_attr,
};

HI_OWCM_TBL_DEF(voice_digit_attr) = {
	.name = "DigitAttrTable",
	.uci = g_digit_attr_uci,
	.para = g_digit_attr_para,
	.op = &g_digit_attr_op,
};

/******************************VOICE FAX ATTR TBL*******************************/
#define FAX_G711_PT_STATIC 0
#define FAX_G711_PT_DYNAMIC 1

#define FAX_ATTR_BITMAP (VOICE_FAX_ATTR_MASK_T_BIT0_FAX_MODE | \
	VOICE_FAX_ATTR_MASK_T_BIT2_FAX_CONTROL_TYPE | \
	VOICE_FAX_ATTR_MASK_T_BIT8_FAX_ENCODEING | \
	VOICE_FAX_ATTR_MASK_T_BIT9_FAX_PT)

struct hi_owcm_voice_fax_attr {
	uint32_t fax_mode;
	uint32_t encoding;
	uint32_t pt_type;
	uint32_t pt;
	uint32_t fax_control_type;
};

static int32_t hi_owcm_voice_set_fax_attr(void *data)
{
	struct hi_owcm_voice_fax_attr *fax_attr = data;
	VoiceFaxConf fax_conf = {0};

	fax_conf.tab_id = VOIP_IPC_CALL_VOICE_FAX_ID;
	fax_conf.fax_bitmap = FAX_ATTR_BITMAP;
	fax_conf.fax_mode = fax_attr->fax_mode;
	fax_conf.fax_encoding = fax_attr->encoding;
	fax_conf.fax_pt = fax_attr->pt_type == FAX_G711_PT_DYNAMIC ? fax_attr->pt : 0;
	fax_conf.g711_control = fax_attr->fax_control_type;
	if (HI_IPC_CALL("hi_voip_cfg_app", &fax_conf)) {
		ULOG_ERR("=====Set fax attr fail.\n");
		return HI_RET_FAIL;
	}
	return HI_RET_SUCC;
}

struct hi_owcm_tbl_uci g_fax_attr_uci[] = {
	{
		.file = "voice",
		.section = "FaxAttr",
		.uci_flag = 1,
	},
	{
		.file = NULL
	},
};

struct hi_owcm_tbl_para g_fax_attr_para[] = {
	{
		.uci = &g_fax_attr_uci[0],
		.name = "fax_mode",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_fax_attr_uci[0],
		.name = "encoding",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_fax_attr_uci[0],
		.name = "pt_type",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_fax_attr_uci[0],
		.name = "pt",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_fax_attr_uci[0],
		.name = "fax_control_type",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{NULL},
};

static struct hi_owcm_tbl_op g_fax_attr_op = {
	.set = hi_owcm_voice_set_fax_attr,
};

HI_OWCM_TBL_DEF(voice_fax_attr) = {
	.name = "FaxAttrTable",
	.uci = g_fax_attr_uci,
	.para = g_fax_attr_para,
	.op = &g_fax_attr_op,
};

/******************************VOICE EXHALE GAIN TBL*******************************/
#define VOICE_EXHALE_GAIN_BITMAP 0x7
struct hi_owcm_voice_exhale_gain {
	int32_t transmit_gain;
	int32_t receive_gain;
	uint32_t echo_enable;
};

static int32_t hi_owcm_voice_set_exhale_gain(void *data)
{
	struct hi_owcm_voice_exhale_gain *gain_attr = data;
	VOIP_EXHALE_GAIN exhale_gain = {0};

	exhale_gain.tab_id = VOIP_IPC_CALL_ECHO_GAIN_CFG_ID;
	exhale_gain.line_id = 1;
	exhale_gain.echo_bitmap = VOICE_EXHALE_GAIN_BITMAP;
	exhale_gain.transmit_Gain = gain_attr->transmit_gain;
	exhale_gain.receive_Gain = gain_attr->receive_gain;
	exhale_gain.echo_enable = gain_attr->echo_enable;
	if (HI_IPC_CALL("hi_voip_cfg_app", &exhale_gain)) {
		ULOG_ERR("=====Set exhale gain fail.\n");
		return HI_RET_FAIL;
	}

	return HI_RET_SUCC;
}

struct hi_owcm_tbl_uci g_exhale_gain_uci[] = {
	{
		.file = "voice",
		.section = "ExhaleGain",
		.uci_flag = 1,
	},
	{
		.file = NULL
	},
};

struct hi_owcm_tbl_para g_exhale_gain_para[] = {
	{
		.uci = &g_exhale_gain_uci[0],
		.name = "transmit_gain",
		.type = HI_OWCM_PARA_INT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_exhale_gain_uci[0],
		.name = "receive_gain",
		.type = HI_OWCM_PARA_INT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_exhale_gain_uci[0],
		.name = "echo_enable",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{NULL},
};

static struct hi_owcm_tbl_op g_exhale_gain_op = {
	.set = hi_owcm_voice_set_exhale_gain,
};

HI_OWCM_TBL_DEF(voice_exhale_gain) = {
	.name = "ExhaleGainTable",
	.uci = g_exhale_gain_uci,
	.para = g_exhale_gain_para,
	.op = &g_exhale_gain_op,
};

/******************************VOICE HOTLINE SERVICE TBL*******************************/
#define HOTLINE_SERVICE_BITMAP (HOTLINE_ATTR_MASK_T_BIT0_ENABLE | \
	HOTLINE_ATTR_MASK_T_BIT2_CALL_WAITING_ENABLE | \
	HOTLINE_ATTR_MASK_T_BIT4_HOTLINE_NUMBER | \
	HOTLINE_ATTR_MASK_T_BIT5_HOTLINE_TIMER | \
	HOTLINE_ATTR_MASK_T_BIT6_THREE_PARTY_CONFERENCE_MODE | \
	HOTLINE_ATTR_MASK_T_BIT7_CONFERENCE_URI)
struct hi_owcm_voice_hotline_service {
	uint32_t hotline_enable;
	uint32_t hotline_timer;
	char hotline_number[VOICE_HOTLINE_NUMBER_LEN];
	uint32_t call_waiting_enable;
	uint32_t three_way_call;
	char conference_url[VOICE_CONFERENCE_URI_LEN];
};

static int32_t hi_owcm_voice_set_hotline_service(void *data)
{
	struct hi_owcm_voice_hotline_service *hotline_service = data;
	voice_hotline_service hotline_serv = {0};

	hotline_serv.tab_id = VOIP_IPC_CALL_HOTLINE_SERVICE_ID;
	hotline_serv.line_id = 1;
	hotline_serv.hotline_bitmap = HOTLINE_SERVICE_BITMAP;
	hotline_serv.hotline_enable = hotline_service->hotline_enable;
	hotline_serv.ulHotlineTimer = hotline_service->hotline_timer;
	(void)memcpy_s(hotline_serv.hotlineNumber, VOICE_HOTLINE_NUMBER_LEN,
				hotline_service->hotline_number, VOICE_HOTLINE_NUMBER_LEN);
	hotline_serv.call_wating_enable = hotline_service->call_waiting_enable;
	hotline_serv.three_way_call = hotline_service->three_way_call;
	(void)memcpy_s(hotline_serv.aucConferenceUrl, VOICE_CONFERENCE_URI_LEN,
				       hotline_service->conference_url, VOICE_CONFERENCE_URI_LEN);
	if (HI_IPC_CALL("hi_voip_cfg_app", &hotline_serv)) {
		ULOG_ERR("=====Set hotline service fail.\n");
		return HI_RET_FAIL;
	}

	return HI_RET_SUCC;
}

struct hi_owcm_tbl_uci g_hotline_service_uci[] = {
	{
		.file = "voice",
		.section = "HotlineService",
		.uci_flag = 1,
	},
	{
		.file = NULL
	},
};

struct hi_owcm_tbl_para g_hotline_service_para[] = {
	{
		.uci = &g_hotline_service_uci[0],
		.name = "hotline_enable",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_hotline_service_uci[0],
		.name = "hotline_timer",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_hotline_service_uci[0],
		.name = "hotline_number",
		.type = HI_OWCM_PARA_STRING,
		.size = VOICE_HOTLINE_NUMBER_LEN,
	},
	{
		.uci = &g_hotline_service_uci[0],
		.name = "call_waiting_enable",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_hotline_service_uci[0],
		.name = "three_way_call",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_hotline_service_uci[0],
		.name = "conference_url",
		.type = HI_OWCM_PARA_STRING,
		.size = VOICE_CONFERENCE_URI_LEN,
	},
	{NULL},
};

static struct hi_owcm_tbl_op g_hotline_service_op = {
	.set = hi_owcm_voice_set_hotline_service,
};

HI_OWCM_TBL_DEF(voice_hotline_service) = {
	.name = "HotlineServiceTable",
	.uci = g_hotline_service_uci,
	.para = g_hotline_service_para,
	.op = &g_hotline_service_op,
};

/******************************VOICE CODEC CAPBILITY TBL*******************************/
struct hi_owcm_voice_codec {
	uint32_t index;
	uint32_t enable;
	uint32_t priority;
	char packetization_period[VOIP_CODEC_PACKT_SIZE];
};

static int32_t hi_owcm_voice_set_codec(void *data)
{
	struct hi_owcm_voice_codec *codec_attr = data;
	VOIP_CODEC voice_codec = {0};

	voice_codec.tab_id = VOIP_IPC_CALL_CODEC_ID;
	voice_codec.codec_bitmap = (VOIP_CODEC_PRIORITY | VOIP_CODEC_ENTRY_ID |
		VOIP_CODEC_PACKETIZATIONPERIOD | VOIP_CODEC_SWITCH);
	voice_codec.line_id = 1;
	voice_codec.codec = codec_attr->index;
	voice_codec.entryID = codec_attr->index;
	voice_codec.codec_switch = codec_attr->enable;
	voice_codec.priority = codec_attr->priority;
	(void)memcpy_s(voice_codec.packetizationPeriod, VOIP_CODEC_PACKT_SIZE,
				codec_attr->packetization_period, VOIP_CODEC_PACKT_SIZE);
	if (HI_IPC_CALL("hi_voip_cfg_app", &voice_codec)) {
		ULOG_ERR("=====Set codec entry%d fail.\n", codec_attr->index);
		return HI_RET_FAIL;
	}

	return HI_RET_SUCC;
}

struct hi_owcm_tbl_uci g_codec_uci[] = {
	{
		.file = "voice",
		.section = "Codec",
		.uci_flag = 1,
	},
	{
		.file = NULL
	},
};

struct hi_owcm_tbl_para g_codec_para[] = {
	{
		.uci = &g_codec_uci[0],
		.name = "index",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_codec_uci[0],
		.name = "switch",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_codec_uci[0],
		.name = "priority",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_codec_uci[0],
		.name = "packetization_period",
		.type = HI_OWCM_PARA_STRING,
		.size = VOIP_CODEC_PACKT_SIZE,
	},
	{NULL},
};

static struct hi_owcm_tbl_op g_codec_op = {
	.set = hi_owcm_voice_set_codec,
};

HI_OWCM_TBL_DEF(voice_codec_g711a) = {
	.name = "G711ACodecTable",
	.idx = 1,
	.uci = g_codec_uci,
	.para = g_codec_para,
	.op = &g_codec_op,
};

HI_OWCM_TBL_DEF(voice_codec_g711u) = {
	.name = "G711UCodecTable",
	.idx = 2,
	.uci = g_codec_uci,
	.para = g_codec_para,
	.op = &g_codec_op,
};

HI_OWCM_TBL_DEF(voice_codec_g729) = {
	.name = "G729CodecTable",
	.idx = 3,
	.uci = g_codec_uci,
	.para = g_codec_para,
	.op = &g_codec_op,
};

HI_OWCM_TBL_DEF(voice_codec_g722) = {
	.name = "G722CodecTable",
	.idx = 4,
	.uci = g_codec_uci,
	.para = g_codec_para,
	.op = &g_codec_op,
};
