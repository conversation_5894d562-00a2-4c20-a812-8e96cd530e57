/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Create: 2024-11-02
 */

#include <math.h>
#include <ulog.h>
#include "hi_basic.h"
#include "hi_pon_gpon_def.h"
#include "hi_pon_epon_def.h"
#include "hi_pon_glb_def.h"
#include "hi_omci_tapi_sysinfo.h"
#include "hi_ctcoam_proc.h"
#include "hi_ipc.h"
#include "hi_optical.h"
#include "hi_owcm_tbl.h"

#define PON_SN_LEN HI_PON_SN_LEN
#define PON_PWD_LEN (HI_PON_PWD_LEN + 1)
#define PON_LOID_LEN (HI_PON_LOID_LEN + 1)
#define PON_LOIDPWD_LEN (HI_PON_LOIDPWD_LEN + 1)
#define PON_MAC_LEN HI_MAC_LEN

enum hi_ow_mpcp_regstate {
	HI_OW_MPCP_STATE_INITIAL = 0,
	HI_OW_MPCP_STATE_REGISTER_REQ,
	HI_OW_MPCP_STATE_REGISTERED,
	HI_OW_MPCP_STATE_HANGUP,
	HI_OW_MPCP_STATE_MAX,
	HI_OW_MPCP_STATE_LOS,
	HI_OW_MPCP_STATE_SD
};

enum hi_sal_pon_uplink_status_e {
    HI_OW_PON_UPLINK_DISABLED = 0,       /**< pon模块故障 Disabled */
    HI_OW_PON_UPLINK_INITIALIZING,       /**< ploam O1状态 Initializing */
    HI_OW_PON_UPLINK_STANDBY,            /**< ploam O2状态 EstablishingLink */
    HI_OW_PON_UPLINK_SN,                 /**< ploam O3状态 EstablishingLink */
    HI_OW_PON_UPLINK_RANGING,            /**< ploam O4状态 EstablishingLink */
    HI_OW_PON_UPLINK_UP,                 /**< ploam O5状态 Up */
    HI_OW_PON_UPLINK_POPUP,              /**< ploam O6状态 Error */
    HI_OW_PON_UPLINK_EMERGENCY,          /**< ploam O7状态 Error */
    HI_OW_PON_UPLINK_NOSIGNAL,           /**< 断纤 NoSignal */
};

enum {
	PON_REG_MODE_PWD,
	PON_REG_MODE_LOID,
};

/******************************PON INIT TBL*******************************/
#define PON_MODE_LEN 12

struct hi_owcm_pon_init_info {
	char pon_mode[PON_MODE_LEN];
};

static int32_t hi_pon_mode_get(const char *upname, enum hi_pon_mode *pon_mode)
{
    if (hi_os_strcasecmp(upname, "gpon") == 0) {
        *pon_mode = HI_PON_MODE_GPON;
    } else if (hi_os_strcasecmp(upname, "epon") == 0) {
        *pon_mode = HI_PON_MODE_EPON;
    } else if (hi_os_strcasecmp(upname, "xgpon_u2d5") == 0) {
        *pon_mode = HI_PON_MODE_10GGPON_U2DOT5G;
    } else if (hi_os_strcasecmp(upname, "xgpon_sym") == 0) {
        *pon_mode = HI_PON_MODE_10GGPON_SYM;
    } else if (hi_os_strcasecmp(upname, "xepon_u2d5") == 0) {
        *pon_mode = HI_PON_MODE_10GEPON_U2DOT5G;
    } else if (hi_os_strcasecmp(upname, "xepon_u1") == 0) {
        *pon_mode = HI_PON_MODE_10GEPON_1G;
    } else if (hi_os_strcasecmp(upname, "xepon_sym") == 0) {
        *pon_mode = HI_PON_MODE_10GEPON_SYM;
    } else if (hi_os_strcasecmp(upname, "gpon_s") == 0) {
        *pon_mode = HI_PON_MODE_GPON_S;
    } else {
		ULOG_ERR("pon mode str invalid : [%s]\n", upname);
		return HI_RET_FAIL;
	}
    return HI_RET_SUCC;
}

static int32_t hi_owcm_pon_init(void *data)
{
	int32_t ret;
	enum hi_pon_mode pon_mode;
	struct hi_owcm_pon_init_info *adapt = data;

	ret = hi_pon_mode_get(adapt->pon_mode, &pon_mode);
	if (ret != HI_RET_SUCC)
		return HI_RET_FAIL;
	ret = HI_IPC_CALL("hi_pon_set_mode", &pon_mode, sizeof(pon_mode));
	if (ret != HI_RET_SUCC) {
		ULOG_ERR("set pon mode failed ,ret[%d]\n", ret);
		return HI_RET_FAIL;
	}
    switch (pon_mode) {
        case HI_PON_MODE_GPON:
        case HI_PON_MODE_GPON_S:
        case HI_PON_MODE_10GGPON_U2DOT5G:
        case HI_PON_MODE_10GGPON_SYM:
			ULOG_INFO("start omci\n");
            ret = HI_IPC_CALL("hi_omci_start");
            break;
        case HI_PON_MODE_EPON:
        case HI_PON_MODE_10GEPON_1G:
        case HI_PON_MODE_10GEPON_U2DOT5G:
        case HI_PON_MODE_10GEPON_SYM:
			ULOG_INFO("start oam\n");
            ret = HI_IPC_CALL("hi_oam_start");
            break;
        default:
			ULOG_ERR("pon mode[%d] not support \n", pon_mode);
            break;
    }
	return ret;
}

struct hi_owcm_tbl_uci g_pon_init_uci[] = {
	{
		.file = "pon",
		.section = "Adapt",
		.uci_flag = 1,
	},
	{
		.file = NULL
	},
};

static struct hi_owcm_tbl_op g_pon_init_op = {
	.set = hi_owcm_pon_init,
};

struct hi_owcm_tbl_para g_pon_init_para[] = {
	{
		.uci = &g_pon_init_uci[0],
		.name = "mode",
		.type = HI_OWCM_PARA_STRING,
		.size = PON_MODE_LEN,
	},
	{NULL},
};

HI_OWCM_TBL_DEF(pon_init) = {
	.name = "PonInit",
	.uci = g_pon_init_uci,
	.op = &g_pon_init_op,
	.para = g_pon_init_para,
};

/******************************PON REG TBL*******************************/
struct hi_owcm_pon_reginfo {
	uint32_t mode;
	char sn[PON_SN_LEN];
	char pwd[PON_PWD_LEN];
	char loid[PON_LOID_LEN];
	char loidpwd[PON_LOIDPWD_LEN];
	uint8_t mac[PON_MAC_LEN];
};

static int32_t gpon_set_loid(char *loid, char *loidpwd)
{
	hi_omci_tapi_sn_loid_s sn_loid = {0};

	if (memcpy_s((char *)sn_loid.auc_loid, sizeof(sn_loid.auc_loid), loid, strlen(loid)) != EOK) {
		ULOG_ERR("memcpy_s fail, loid[%s]\n", loid);
		return HI_RET_FAIL;
	}
	if (memcpy_s((char *)sn_loid.auc_lopwd, sizeof(sn_loid.auc_lopwd), loidpwd, strlen(loidpwd)) != EOK) {
		ULOG_ERR("memcpy_s fail, loidpwd[%s]\n", loidpwd);
		return HI_RET_FAIL;
	}

	return HI_IPC_CALL("hi_omci_set_loid", &sn_loid, sizeof(sn_loid));
}

static int32_t epon_set_loid(char *loid, char *loidpwd)
{
	hi_ctcoam_onu_loid_s loid_info = {0};

	if (strcpy_s((char *)loid_info.loid, sizeof(loid_info.loid), loid) != EOK) {
		ULOG_ERR("strcpy_s fail, loid[%s]\n", loid);
		return HI_RET_FAIL;
	}
	if (strcpy_s((char *)loid_info.password, sizeof(loid_info.password), loidpwd) != EOK) {
		ULOG_ERR("strcpy_s fail, loidpwd[%s]\n", loidpwd);
		return HI_RET_FAIL;
	}

	return HI_IPC_CALL("hi_oam_set_loid", &loid_info, sizeof(loid_info));
}

static int32_t hi_owcm_gpon_start_reg(struct hi_owcm_pon_reginfo *reg)
{
	struct hi_gpon_para gpon_para = {0};
	if (memcpy_s(gpon_para.sn, sizeof(gpon_para.sn), reg->sn, sizeof(reg->sn)) != EOK)
		return HI_RET_FAIL;

	if (reg->mode == PON_REG_MODE_PWD) {
        if (memcpy_s(gpon_para.pwd, sizeof(gpon_para.pwd), reg->pwd, strlen(reg->pwd)) != EOK)
            return HI_RET_FAIL;
	} else {
		if (gpon_set_loid(reg->loid, reg->loidpwd) != HI_RET_SUCC)
			return HI_RET_FAIL;
	}

	HI_IPC_CALL("hi_gpon_stop");
	HI_IPC_CALL("hi_gpon_start", &gpon_para);
	return HI_RET_SUCC;
}

static int32_t hi_owcm_epon_start_reg(struct hi_owcm_pon_reginfo *reg)
{
	struct hi_epon_para epon_para = { 0 };
	char loid_tmp[PON_LOID_LEN] = {0};
	char loidpwd_tmp[PON_LOIDPWD_LEN] = {0};

	if (memcpy_s(epon_para.mac, sizeof(epon_para.mac), reg->mac, sizeof(reg->mac)) != EOK)
		return HI_RET_FAIL;

	if (reg->mode == PON_REG_MODE_LOID) {
		if (epon_set_loid(reg->loid, reg->loidpwd) != HI_RET_SUCC)
			return HI_RET_FAIL;
	} else {
		if (epon_set_loid(loid_tmp, loidpwd_tmp) != HI_RET_SUCC)
			return HI_RET_FAIL;
	}

	HI_IPC_CALL("hi_epon_stop");
	HI_IPC_CALL("hi_epon_start", &epon_para);
	return HI_RET_SUCC;
}

static int32_t hi_owcm_pon_set_reginfo(void *data)
{
	int32_t ret;
	struct hi_owcm_pon_reginfo *reg = data;
	enum hi_pon_mode pon_mode;

	ret = HI_IPC_CALL("hi_pon_get_mode", &pon_mode);
	if (ret != HI_RET_SUCC) {
		ULOG_ERR("Get pon mode fail ,ret[%u]\n", ret);
		return ret;
	}
	
	ULOG_INFO("=====Distribute pon reginfo,pon mode [%d], reg mode [%d]\n", pon_mode, reg->mode);
    switch (pon_mode) {
        case HI_PON_MODE_GPON:
        case HI_PON_MODE_GPON_S:
        case HI_PON_MODE_10GGPON_U2DOT5G:
        case HI_PON_MODE_10GGPON_SYM:
            ret = hi_owcm_gpon_start_reg(reg);
            break;
        case HI_PON_MODE_EPON:
        case HI_PON_MODE_10GEPON_1G:
        case HI_PON_MODE_10GEPON_U2DOT5G:
        case HI_PON_MODE_10GEPON_SYM:
            ret = hi_owcm_epon_start_reg(reg);
            break;
        default:
            ULOG_ERR("pon mode[%d] not support \n", pon_mode);
            break;
    }
	return ret;
}

struct hi_owcm_tbl_uci g_pon_reg_uci[] = {
	{
		.file = "pon",
		.section = "RegInfo",
		.uci_flag = 1,
	},
	{
		.file = "/usr/local/factory/sysinfo",
		.section = "sysinfo",
		.uci_flag = 1,
	},
	{
		.file = NULL
	},
};

struct hi_owcm_tbl_para g_pon_reg_para[] = {
	{
		.uci = &g_pon_reg_uci[0],
		.name = "mode",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_pon_reg_uci[1],
		.name = "auid_sn",
		.type = HI_OWCM_PARA_HEX,
		.size = PON_SN_LEN,
	},
	{
		.uci = &g_pon_reg_uci[0],
		.name = "pwd",
		.type = HI_OWCM_PARA_STRING,
		.size = PON_PWD_LEN,
	},
	{
		.uci = &g_pon_reg_uci[0],
		.name = "loid",
		.type = HI_OWCM_PARA_STRING,
		.size = PON_LOID_LEN,
	},
	{
		.uci = &g_pon_reg_uci[0],
		.name = "loidpwd",
		.type = HI_OWCM_PARA_STRING,
		.size = PON_LOIDPWD_LEN,
	},
	{
		.uci = &g_pon_reg_uci[1],
		.name = "llid0_mac",
		.type = HI_OWCM_PARA_MAC,
		.size = PON_MAC_LEN,
	},
	{NULL},
};

static struct hi_owcm_tbl_op g_pon_reg_op = {
	.set = hi_owcm_pon_set_reginfo,
};

HI_OWCM_TBL_DEF(pon_reg) = {
	.name = "PonRegTable",
	.uci = g_pon_reg_uci,
	.op = &g_pon_reg_op,
	.para = g_pon_reg_para,
};


/*******************************PON STATUS TBL*******************************/
#define PON_MODE_LEN 8
#define PON_DBM_MUL 10

struct hi_owcm_pon_status {
	uint32_t linkstatus;
	uint16_t fec_cap;
	uint16_t fec_enable;
	float temprature;
	float voltage;
	float ibias;
	float txpower;
	float rxpower;
	uint32_t wavelength;
	char pon_mode[PON_MODE_LEN];
};


static int32_t gpon_get_status(struct hi_owcm_pon_status *status)
{
	struct hi_gpon_fec gpon_fec = {0};

	if (HI_IPC_CALL("hi_gpon_get_fec", &gpon_fec, sizeof(gpon_fec)) != HI_RET_SUCC) {
		ULOG_ERR("=====Get gpon fec fail\n");
		return HI_RET_FAIL;
	}
	status->fec_enable = gpon_fec.tx_fec;
	(void)strcpy_s(status->pon_mode, sizeof(status->pon_mode), "GPON");
	if (HI_IPC_CALL("hi_gpon_get_reg_state", &status->linkstatus, sizeof(status->linkstatus)) != HI_RET_SUCC) {
		ULOG_ERR("=====Get gpon status fail\n");
		return HI_RET_FAIL;
	}
	return HI_RET_SUCC;
}

static int32_t epon_get_status(struct hi_owcm_pon_status *status)
{
	struct hi_epon_fec epon_fec = {0};
	struct hi_epon_llidstate epon_state = {0};
	uint32_t los_sta = 0xffffffff;
	if (HI_IPC_CALL("hi_epon_get_fec", &epon_fec, sizeof(epon_fec)) != HI_RET_SUCC) {
		ULOG_ERR("=====Get epon fec fail\n");
		return HI_RET_FAIL;
	}
	status->fec_enable = epon_fec.tx_fec_en;
	(void)strcpy_s(status->pon_mode, sizeof(status->pon_mode), "EPON");

	if (HI_IPC_CALL("hi_epon_get_los_state", &los_sta, sizeof(los_sta)) != HI_RET_SUCC) {
		ULOG_ERR("=====Get epon los state fail\n");
		return HI_RET_FAIL;
	}
	if (HI_IPC_CALL("hi_epon_get_llidstate", &epon_state, sizeof(epon_state)) != HI_RET_SUCC) {
		ULOG_ERR("=====Get epon llidstate fail\n");
		return HI_RET_FAIL;
	}
	if (los_sta)
		status->linkstatus = HI_OW_PON_UPLINK_NOSIGNAL;
	else if (epon_state.state == (HI_OW_MPCP_STATE_REGISTERED + 1))
		status->linkstatus = HI_OW_PON_UPLINK_UP;
	else if (epon_state.state == (HI_OW_MPCP_STATE_HANGUP + 1))
		status->linkstatus = HI_OW_PON_UPLINK_POPUP;
	else
		status->linkstatus = HI_OW_PON_UPLINK_INITIALIZING;
	
	return HI_RET_SUCC;
}

static int32_t hi_owcm_pon_get_status(void *data)
{
	int32_t ret;
	struct hi_optical_state optical = {0};
	enum hi_pon_mode pon_mode;
	struct hi_owcm_pon_status *status = data;

	if (hi_optical_get_state(&optical) != HI_RET_SUCC) {
		ULOG_ERR("=====Get pon status fail\n");
		return HI_RET_FAIL;
	}

	status->fec_cap = 1;
	status->temprature = optical.temprature;
	status->voltage = optical.voltage;
	status->ibias = optical.ibias;
	status->txpower = PON_DBM_MUL * log10(optical.txpower);
	status->rxpower = PON_DBM_MUL * log10(optical.rxpower);
	status->wavelength = optical.wavelength;

	ret = HI_IPC_CALL("hi_pon_get_mode", &pon_mode);
	if (ret != HI_RET_SUCC) {
		ULOG_ERR("Get pon mode fail ,ret[%u]\n", ret);
		return ret;
	}

    switch (pon_mode) {
        case HI_PON_MODE_GPON:
        case HI_PON_MODE_GPON_S:
        case HI_PON_MODE_10GGPON_U2DOT5G:
        case HI_PON_MODE_10GGPON_SYM:
            ret = gpon_get_status(status);
            break;
        case HI_PON_MODE_EPON:
        case HI_PON_MODE_10GEPON_1G:
        case HI_PON_MODE_10GEPON_U2DOT5G:
        case HI_PON_MODE_10GEPON_SYM:
            ret = epon_get_status(status);
            break;
        default:
            ULOG_ERR("pon mode[%d] not support \n", pon_mode);
            break;
    }

	return ret;
}

struct hi_owcm_tbl_uci g_pon_status_path[] = {
	{
		.file = "pon",
		.section = "Status",
		.uci_flag = 0,
	},
	{
		.file = NULL
	},
};

struct hi_owcm_tbl_para g_pon_status_para[] = {
	{
		.uci = &g_pon_status_path[0],
		.name = "linkstatus",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_pon_status_path[0],
		.name = "fec_cap",
		.type = HI_OWCM_PARA_UCHAR,
		.size = sizeof(uint16_t),
	},
	{
		.uci = &g_pon_status_path[0],
		.name = "fec_enable",
		.type = HI_OWCM_PARA_UCHAR,
		.size = sizeof(uint16_t),
	},
	{
		.uci = &g_pon_status_path[0],
		.name = "temprature",
		.type = HI_OWCM_PARA_FLOAT,
		.size = sizeof(float),
	},
	{
		.uci = &g_pon_status_path[0],
		.name = "voltage",
		.type = HI_OWCM_PARA_FLOAT,
		.size = sizeof(float),
	},
	{
		.uci = &g_pon_status_path[0],
		.name = "ibias",
		.type = HI_OWCM_PARA_FLOAT,
		.size = sizeof(float),
	},
	{
		.uci = &g_pon_status_path[0],
		.name = "txpower",
		.type = HI_OWCM_PARA_FLOAT,
		.size = sizeof(float),
	},
	{
		.uci = &g_pon_status_path[0],
		.name = "rxpower",
		.type = HI_OWCM_PARA_FLOAT,
		.size = sizeof(float),
	},
	{
		.uci = &g_pon_status_path[0],
		.name = "wavelength",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_pon_status_path[0],
		.name = "pon_mode",
		.type = HI_OWCM_PARA_STRING,
		.size = PON_MODE_LEN,
	},
	{NULL},
};

static struct hi_owcm_tbl_op g_pon_status_op = {
	.get = hi_owcm_pon_get_status,
};

HI_OWCM_TBL_DEF(pon_status) = {
	.name = "PonStatus",
	.uci = g_pon_status_path,
	.para = g_pon_status_para,
	.op = &g_pon_status_op,
};
