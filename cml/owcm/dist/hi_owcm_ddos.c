/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: ddos for openwrt
 * Author: HSAN
 * Create: 2025-03-03
 */

#include <ulog.h>
#include "hi_basic.h"
#include "hi_netif_ipc.h"
#include "hi_ipc.h"
#include "hi_owcm_tbl.h"

#define HI_OWCM_QOS_LAN_PROTO_PQ 7

struct hi_owcm_netif_ddos_rule {
	uint32_t proto_rate;
	uint32_t cpu_rate;
};

static int32_t hi_owcm_set_cpu_rate(uint32_t rate)
{
	return HI_IPC_CALL("hi_netif_cpu_rate_set", &rate);
}

static int32_t hi_netif_set_proto_pq_rate(const char *ifname, uint32_t type, uint32_t rate_kbps, uint32_t pq)
{
	struct hi_netif_ddos_rule rule = {0};
	if (strcpy_s(rule.devname, sizeof(rule.devname), ifname) != EOK)
		return -HI_RET_FAIL;

	rule.ptype = type;
	rule.rate = rate_kbps;
	rule.unit = HI_NETIF_RATE_UNIT_KBPS;
	rule.pq = pq;
	return HI_IPC_CALL("hi_netif_set_ddos_rule", &rule);
}

static void hi_owcm_set_all_ddos(uint32_t rate)
{
	hi_netif_set_proto_pq_rate("all", HI_NETIF_PROTO_TYPE_IGMP, rate, HI_OWCM_QOS_LAN_PROTO_PQ);
	hi_netif_set_proto_pq_rate("all", HI_NETIF_PROTO_TYPE_MLD, rate, HI_OWCM_QOS_LAN_PROTO_PQ);
}

static int32_t hi_owcm_set_ddos(void *data)
{
	int32_t ret;
	struct hi_owcm_netif_ddos_rule *rule = data;

	ret = hi_owcm_set_cpu_rate(rule->cpu_rate);
	if (ret != HI_RET_SUCC) {
		ULOG_ERR("failed to hi_owcm_set_cpu_rate, ret = %x\n", ret);
		return HI_RET_FAIL;
	}
	hi_owcm_set_all_ddos(rule->proto_rate);
	return HI_RET_SUCC;
}

struct hi_owcm_tbl_uci g_ddos_rule_uci[] = {
	{
		.file = "ddos",
		.section = "rule",
		.uci_flag = 1,
	},
	{
		.file = NULL
	},
};

struct hi_owcm_tbl_para g_ddos_rule_para[] = {
	{
		.uci = &g_ddos_rule_uci[0],
		.name = "proto_rate",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{
		.uci = &g_ddos_rule_uci[0],
		.name = "cpu_rate",
		.type = HI_OWCM_PARA_UINT,
		.size = sizeof(uint32_t),
	},
	{NULL},
};

static struct hi_owcm_tbl_op g_ddos_rule_op = {
	.set = hi_owcm_set_ddos,
};

HI_OWCM_TBL_DEF(netif_reg) = {
	.name = "ddos_rule",
	.uci = g_ddos_rule_uci,
	.para = g_ddos_rule_para,
	.op = &g_ddos_rule_op,
};
