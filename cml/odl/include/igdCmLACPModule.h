/*
 * Copyright (c) Hisilicon(Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: link aggregation cfg
 * Author: hsan
 * Create: 2023-03-06
 * History:
 */

#ifndef IGD_CM_LACP_MODULE_H
#define IGD_CM_LACP_MODULE_H

#include <igdGlobalTypeDef.h>
#include "hi_odl_tab_lacp.h"

word32 igdCmLACPDevCfgAdd(uword8 *pucInfo, uword32 len);
word32 igdCmLACPDevCfgDel(uword8 *pucInfo, uword32 len);
word32 igdCmLACPDevCfgSet(uword8 *pucInfo, uword32 len);
word32 igdCmLACPDevCfgGet(uword8 *pucInfo, uword32 len);
word32 igdCmLACPDevCfgEntryNumGet(uword32 *entrynum);
word32 igdCmLACPDevCfgGetAllentry(uword8 *pucInfo, uword32 len);

uword32 igdCmLACPModuleInit(void);
#ifdef CONFIG_WITH_LACP
void cm_lacp_oper_init(void);
#else
static inline void cm_lacp_oper_init(void) { }
#endif

#endif /*IGD_CM_LACP_MODULE_H*/
