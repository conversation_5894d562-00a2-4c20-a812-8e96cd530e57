#ifndef IGD_CM_SECURITY_MODULE_PUB_H
#define IGD_CM_SECURITY_MODULE_PUB_H

#include <igdGlobalTypeDef.h>
#include "hi_odl_tab_url_filter.h"
#include "hi_odl_tab_dns_filter.h"
#include "hi_odl_tab_ip_filter.h"
#include "hi_odl_tab_mac_filter.h"
#include "hi_odl_tab_proto_limit.h"
#include "hi_odl_tab_parent_ctrl.h"
#include "hi_odl_tab_firewall.h"
#include "hi_odl_tab_static_arp.h"
#include "hi_odl_tab_userlimit.h"
#include "hi_odl_tab_portal.h"
#include "hi_odl_asyn_rt.h"

#define SERVICE_NETWORK_ACCELERATION	0
#define SERVICE_L2TP			1

#define SERVICE_IPSET_IPV4		1
#define SERVICE_IPSET_IPV6		2

#define SERVICE_OPERATE_OR		0
#define SERVICE_OPERATE_AND		1

#define SERVICE_IPSET_TARGET_ACCEPT	0
#define SERVICE_IPSET_TARGET_DROP	1
#define SERVICE_IPSET_TARGET_REJECT	2

#define SERVICE_IPSET_MATCH_NUM_1	1
#define SERVICE_IPSET_MATCH_NUM_2	2
#define SERVICE_IPSET_MATCH_NUM_3	3

#define SERVICE_IPSET_DIRECTION_SRC	1
#define SERVICE_IPSET_DIRECTION_DST	2

#define MAX_IPSET_NUM			8
#define WAN_NAME_STR_LEN		256
#define MAX_SET_NAME_STR_LEN		64
typedef struct {
	uint8_t enable;
	uint8_t service_type;
	uint8_t index;
	uint8_t operate;
	char  wan_name[WAN_NAME_STR_LEN];
	char  set_name[MAX_IPSET_NUM][MAX_SET_NAME_STR_LEN];
	uint32_t ipset_num;
} service_rules;

/***************************URL过滤基本属性表*********************************/
word32 igdCmSecureUrlFilterAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureUrlFilterAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureUrlFilterAttrInit(void);


/***************************URL过滤名单*********************************/
word32 igdCmSecureUrlFilterListAdd(uword8 *pucInfo, uword32 len);
word32 igdCmSecureUrlFilterListDel(uword8 *pucInfo, uword32 len);
word32 igdCmSecureUrlFilterListSet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureUrlFilterListGet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureUrlFilterListEntryNumGet(uword32 *entrynum);
word32 igdCmSecureUrlFilterListGetallIndex(uword8 *pucInfo, uword32 len);
word32 igdCmSecureUrlFilterListInit(void);

/***************************Dbus URL过滤名单*********************************/
word32 igdCmSecureDbusUrlFilterListAdd(uword8 *pucInfo, uword32 len);
word32 igdCmSecureDbusUrlFilterListDel(uword8 *pucInfo, uword32 len);
word32 igdCmSecureDbusUrlFilterListGet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureDbusUrlFilterListSet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureDbusUrlFilterListEntryNumGet(uword32 *entrynum);
word32 igdCmSecureDbusUrlFilterListGetallIndex(uword8 *pucInfo, uword32 len);
word32 igdCmSecureDbusUrlFilterListInit(void);


/***************************防火墙基本属性表*********************************/
word32 igdCmSecureFirewallAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureFirewallAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureFirewallAttrInit(void);

word32 igdCmSecureFirewallAttrFillEntryImpl(IgdSecurFirewallAttrConfTab *attr_cfg, IgdSecurFirewallAttrConfTab *target_entry,
	uint32_t entry_index, cm_attr_op_type cfg_attr_op);

/***************************IPv6 Session Whitelist*********************************/
word32 igdCmSecureIPv6SessionWhiteListAdd(uword8 *pucInfo, uword32 len);
word32 igdCmSecureIPv6SessionWhiteListDel(uword8 *pucInfo, uword32 len);
word32 igdCmSecureIPv6SessionWhiteListSet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureIPv6SessionWhiteListGet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureIPv6SessionWhiteListEntryNumGet(uword32 *entrynum);
word32 igdCmSecureIPv6SessionWhiteListGetallIndex(uword8 *pucInfo, uword32 len);
word32 igdCmSecureIPv6SessionWhiteListGetallConfigInfo(uword8 *pucInfo, uword32 len);
word32 igdCmSecureIPv6SessionWhiteListInit(void);

/***************************Mac过滤基本属性表*********************************/
word32 igdCmSecureMacFilterAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureMacFilterAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureMacFilterAttrInit(void);


/***************************Mac过滤名单*********************************/
word32 igdCmSecureMacFilterListAdd(uword8 *pucInfo, uword32 len);
word32 igdCmSecureMacFilterListDel(uword8 *pucInfo, uword32 len);
word32 igdCmSecureMacFilterListGet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureMacFilterListSet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureMacFilterListEntryNumGet(uword32 *entrynum);
word32 igdCmSecureMacFilterListGetallIndex(uword8 *pucInfo, uword32 len);
word32 igdCmSecureMacFilterListInit(void);

/***************************Mac过滤名单*********************************/

/***************************IP过滤基本属性表*********************************/
word32 igdCmSecureIPFilterAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureIPFilterAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureIPFilterAttrInit(void);

/***************************IP过滤表*********************************/
word32 igdCmSecureIPFilterListAdd(uword8 *pucInfo, uword32 len);
word32 igdCmSecureIPFilterListDel(uword8 *pucInfo, uword32 len);
word32 igdCmSecureIPFilterListGet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureIPFilterListEntryNumGet(uword32 *entrynum);
word32 igdCmSecureIPFilterListGetallIndex(uword8 *pucInfo, uword32 len);
word32 igdCmSecureIPFilterListInit(void);

/***************************接入用户限制*********************************/
word32 igdCmSecureUserLimitAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureUserLimitAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureUserLimitAttrInit(void);

/***************************强制门户***************************************/
word32 igdCmSecurePortalMgtAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSecurePortalMgtAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmSecurePortalMgtAttrInit(void);

/***************************LAN IP过滤基本属性表*********************************/
word32 igdCmSecureLANIPFilterListAdd(uword8 *pucInfo, uword32 len);
word32 igdCmSecureLANIPFilterListSet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureLANIPFilterListDel(uword8 *pucInfo, uword32 len);
word32 igdCmSecureLANIPFilterListGet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureLANIPFilterListGetallIndex(uword8 *pucInfo, uword32 len);
word32 igdCmSecureLANIPFilterListEntryNumGet(uword32 *entrynum);
word32 igdCmSecureLANIPFilterListGetallConfigInfo(uword8 *pucInfo, uword32 len);
word32 igdCmSecureLANIPFilterListInit(void);

/***************************wan IP过滤表*********************************/
word32 igdCmSecureWANIPFilterListAdd(uword8 *pucInfo, uword32 len);
word32 igdCmSecureWANIPFilterListSet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureWANIPFilterListDel(uword8 *pucInfo, uword32 len);
word32 igdCmSecureWANIPFilterListGet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureWANIPFilterListEntryNumGet(uword32 *entrynum);
word32 igdCmSecureWANIPFilterListGetallIndex(uword8 *pucInfo, uword32 len);
word32 igdCmSecureWANIPFilterListGetallConfigInfo(uword8 *pucInfo, uword32 len);
word32 igdCmSecureWANIPFilterListInit(void);

/***************************ARP绑定基本属性表*********************************/
word32 igdCmSecureStaticArpAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureStaticArpAttrGet(uword8 *pucInfo, uword32 len);

/***************************静态ARP基本属性表*********************************/
word32 igdCmSecureStaticArpListAdd(uword8 *pucInfo, uword32 len);
word32 igdCmSecureStaticArpListDel(uword8 *pucInfo, uword32 len);
word32 igdCmSecureStaticArpListSet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureStaticArpListGet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureStaticArpAllGet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureStaticArpListEntryNumGet(uword32 *entrynum);
word32 igdCmSecureRemoteControlListSet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureRemoteControlListGet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureRemoteControlListAdd(uword8 *pucInfo, uword32 len);
word32 igdCmSecureRemoteControlListDel(uword8 *pucInfo, uword32 len);
word32 igdCmSecureRemoteControlListAllGet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureRemoteControlListEntryNumGet(uword32 *entrynum);

/***************************家长控制儿童设备MAC地址列表*********************************/
word32 igdCmParentalCtrlBASICSet(uword8 *pucInfo, uword32 len);
word32 igdCmParentalCtrlBASICGet(uword8 *pucInfo, uword32 len);

/***************************家长控制儿童设备MAC地址列表*********************************/
word32 igdCmParentalCtrlMACSet(uword8 *pucInfo, uword32 len);
word32 igdCmParentalCtrlMACGet(uword8 *pucInfo, uword32 len);
word32 igdCmParentalCtrlMACInit(void);
word32 igdCmParentalCtrlMACAdd(uword8 *pucInfo, uword32 len);
word32 igdCmParentalCtrlMACDel(uword8 *pucInfo, uword32 len);
word32 igdCmParentalCtrlMACAllIndexGet(uword8 *pucInfo, uword32 len);
word32 igdCmParentalCtrlMACEntryNumGet(uword32 *entrynum);


/***************************家长控制过滤策略模板*********************************/
word32 igdCmParentalCtrlTemplatesSet(uword8 *pucInfo, uword32 len);
word32 igdCmParentalCtrlTemplatesGet(uword8 *pucInfo, uword32 len);
word32 igdCmParentalCtrlTemplatesInit(void);
word32 igdCmParentalCtrlTemplatesAdd(uword8 *pucInfo, uword32 len);
word32 igdCmParentalCtrlTemplatesDel(uword8 *pucInfo, uword32 len);
word32 igdCmParentalCtrlTemplatesAllIndexGet(uword8 *pucInfo, uword32 len);
word32 igdCmParentalCtrlTemplatesEntryNumGet(uword32 *entrynum);
int32_t igd_cm_parental_ctrl_template_get_all_entry(uint8_t *pucInfo, uint32_t len);



/***************************家长控制上网时间段列表*********************************/
word32 igdCmParentalCtrlDuraSet(uword8 *pucInfo, uword32 len);
word32 igdCmParentalCtrlDuraGet(uword8 *pucInfo, uword32 len);
word32 igdCmParentalCtrlDuraInit(void);
word32 igdCmParentalCtrlDuraAdd(uword8 *pucInfo, uword32 len);
word32 igdCmParentalCtrlDuraDel(uword8 *pucInfo, uword32 len);
word32 igdCmParentalCtrlDuraAllIndexGet(uword8 *pucInfo, uword32 len);
word32 igdCmParentalCtrlDuraEntryNumGet(uword32 *entrynum);

/***************************家长控制URL过滤*********************************/
word32 igdCmParentalCtrlUrlSet(uword8 *pucInfo, uword32 len);
word32 igdCmParentalCtrlUrlGet(uword8 *pucInfo, uword32 len);
word32 igdCmParentalCtrlUrlInit(void);
word32 igdCmParentalCtrlUrlAdd(uword8 *pucInfo, uword32 len);
word32 igdCmParentalCtrlUrlDel(uword8 *pucInfo, uword32 len);
word32 igdCmParentalCtrlUrlAllIndexGet(uword8 *pucInfo, uword32 len);
word32 igdCmParentalCtrlUrlEntryNumGet(uword32 *entrynum);


/***************************协议报文流量控制*********************************/
word32 igdCmSecureProtoCarInit(void);
word32 igdCmSecureProtoCarGet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureProtoCarEntryNumGet(uint32_t *entrynum);
word32 igdCmSecureProtoCarSet(uword8 *pucInfo, uword32 len);
word32 igdCmSecureProtoGetAllEntry(uword8 *pucInfo, uword32 len);

/********************************域名过滤*******************************************/
word32 igdCmDnsFilterConfigAdd(uword8 *pucInfo, uword32 len);
word32 igdCmDnsFilterConfigSet(uword8 *pucInfo, uword32 len);
word32 igdCmDnsFilterConfigDel(uword8 *pucInfo, uword32 len);
word32 igdCmDnsFilterConfigGet(uword8 *pucInfo, uword32 len);
word32 igdCmDnsFilterConfigEntryNumGet(uword32 *entrynum);
word32 igdCmDnsFilterConfigGetallIndex(uword8 *pucInfo, uword32 len);
word32 igdCmDnsFilterConfigInit(void);

/********************************域名过滤*******************************************/
int32_t igd_cm_user_management_attr_add(uint8_t *info, uint32_t len);
int32_t igd_cm_user_management_attr_del(uint8_t *info, uint32_t len);
int32_t igd_cm_user_management_attr_entry_num_get(uint32_t *entry_num);
int32_t igd_cm_user_management_attr_all_index_get(uint32_t *info, uint32_t len);
int32_t igd_cm_user_management_attr_all_entry_get(uint8_t *info, uint32_t len);
int32_t igd_cm_user_management_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_user_management_attr_get(uint8_t *info, uint32_t len);

int32_t igd_cm_firewall_traffic_control_attr_add(uint8_t *info, uint32_t len);
int32_t igd_cm_firewall_traffic_control_attr_del(uint8_t *info, uint32_t len);
int32_t igd_cm_firewall_traffic_control_attr_entry_num_get(uint32_t *entry_num);
int32_t igd_cm_firewall_traffic_control_attr_all_index_get(uint32_t *info, uint32_t len);
int32_t igd_cm_firewall_traffic_control_attr_all_entry_get(uint8_t *info, uint32_t len);
int32_t igd_cm_firewall_traffic_control_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_firewall_traffic_control_attr_get(uint8_t *info, uint32_t len);
int32_t igd_cm_firewall_traffic_control_attr_init(void);

int32_t igd_cm_firewall_network_acceleration_attr_add(uint8_t *info, uint32_t len);
int32_t igd_cm_firewall_network_acceleration_attr_del(uint8_t *info, uint32_t len);
int32_t igd_cm_firewall_network_acceleration_attr_entry_num_get(uint32_t *entry_num);
int32_t igd_cm_firewall_network_acceleration_attr_all_index_get(uint32_t *info, uint32_t len);
int32_t igd_cm_firewall_network_acceleration_attr_all_entry_get(uint8_t *info, uint32_t len);
int32_t igd_cm_firewall_network_acceleration_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_firewall_network_acceleration_attr_get(uint8_t *info, uint32_t len);
int32_t igd_cm_firewall_network_acceleration_attr_init(void);

int32_t igd_cm_firewall_ipset_config_attr_add(uint8_t *info, uint32_t len);
int32_t igd_cm_firewall_ipset_config_attr_del(uint8_t *info, uint32_t len);
int32_t igd_cm_firewall_ipset_config_attr_entry_num_get(uint32_t *entry_num);
int32_t igd_cm_firewall_ipset_config_attr_all_index_get(uint32_t *info, uint32_t len);
int32_t igd_cm_firewall_ipset_config_attr_all_entry_get(uint8_t *info, uint32_t len);
int32_t igd_cm_firewall_ipset_config_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_firewall_ipset_config_attr_get(uint8_t *info, uint32_t len);
int32_t igd_cm_firewall_ipset_attr_init(void);

int32_t igd_cm_firewall_ipset_entry_attr_add(uint8_t *info, uint32_t len);
int32_t igd_cm_firewall_ipset_entry_attr_del(uint8_t *info, uint32_t len);
int32_t igd_cm_firewall_ipset_entry_attr_entry_num_get(uint32_t *entry_num);
int32_t igd_cm_firewall_ipset_entry_attr_all_index_get(uint32_t *info, uint32_t len);
int32_t igd_cm_firewall_ipset_entry_attr_all_entry_get(uint8_t *info, uint32_t len);
int32_t igd_cm_firewall_ipset_entry_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_firewall_ipset_entry_attr_get(uint8_t *info, uint32_t len);

int8_t igd_cm_api_split_ipset_name(char *src, int32_t src_size, char *des, int32_t des_2d_size, int32_t des_1d_size);
void igd_cm_firewall_flush_service_rules(service_rules *prules);
void igd_cm_firewall_init_service_rules(service_rules *prules);
int32_t igd_cm_traffic_control_construct_mod_by_ipset_name(const char *ipset_name, char *module, uint32_t module_len,
							   uint32_t *protocol);

uword32 igdCmSecureModuleInit(void);
void  igdCmSecureModuleExit(void);
void cm_security_oper_init(void);

#endif/*IGD_CM_SECURITY_MODULE_PUB_H*/
