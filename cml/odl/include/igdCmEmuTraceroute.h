/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: igdCmEmuTraceroute.h
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef IGD_CM_EMU_TRACEROUTE_H
#define IGD_CM_EMU_TRACEROUTE_H
#include "hi_odl_tab_emu_tracert.h"

extern word32 igdCmSysmngTraceRouteConfigSet(uword8 *pucInfo, uword32 len);

extern word32 igdCmSysmngTraceRouteConfigGet(uword8 *pucInfo, uword32 len);

extern word32 igdCmSysmngTraceRouteStart(void);

extern word32 igdCmSysmngTraceRouteStop(void);

extern word32 igdCmSysmngTraceRouteStaticsGet(uword8 *pucInfo, uword32 len);

extern word32 igdCmSysmngTraceRouteHopGet(uword8 *pucInfo, uword32 len);

extern word32 igdCmSysmngTraceRouteInit(void);

extern word32 igdEmuTraceRTProcNotify(uword8 *pv_data);

void cm_emu_tracert_oper_init(void);

#endif /* SRC_ODL_INCLUDE_IGDCMEMUTRACEROUTE_H_ */
