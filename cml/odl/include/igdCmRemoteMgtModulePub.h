#ifndef IGD_CM_REMOTEMGT_MODULE_PUB_H
#define IGD_CM_REMOTEMGT_MODULE_PUB_H

#include <igdGlobalTypeDef.h>

#define LOID_TMP_FILE_PATH "/tmp/pon"

/***************************Tr069属性表*********************************/
word32 igdCmRemoteMgtTr069AttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmRemoteMgtTr069AttrGet(uword8 *pucInfo, uword32 len);


/*操作库函数声明*/

/***************************Tr069属性表*********************************/

/***************************Tr069信息表*********************************/
word32 igdCmRemoteMgtTr069InfoGet(uword8 *pucInfo, uword32 len);


/*操作库函数声明*/

/***************************Tr069信息表*********************************/

/***************************中间键属性表*********************************/
word32 igdCmRemoteMgtMiddwareAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmRemoteMgtMiddwareAttrGet(uword8 *pucInfo, uword32 len);


/***************************中间键属性表*********************************/

/***************************LOID注册信息表*********************************/
word32 igdCmRmtMgtLoidRegAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmRmtMgtLoidRegAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmRmtMgtLoidRegAttrInit(void);
word32 igdCmRmtMgtDataUpdateSerName(uword32 SeviceName);
word32 igdCmRmtMgtSerNameGet(word8 *pserName, uword32 *serNum, uword32 len);
word32 igdCmRmtMgtSerNameClear(void);

int loid_reg_tab_get(uint32_t inst, IgdRmtMgtLoidRegAttrConfTab *entry);
int loid_reg_tab_update(IgdRmtMgtLoidRegAttrConfTab *entry, uint32_t inst);
/*操作库函数声明*/

/***************************LOID注册信息表*********************************/

void igd_rmt_update_inform_result(int32_t inform_result);

/***************************ITMS注册状态*********************************/
word32 igdCmRmtMgtITMSRegStateGet(uword8 *pucInfo, uword32 len);

/**********************************
*interface for dbus Register LOID*
************************************/
word32 igdCmRmtSetLOID(uword8 *pucInfo, uword32 len);/*仅保存数据，不发起注册流程*/
word32 igdCmRmtRegisterLOID(void);/*从mib中读取数据发起注册流程*/


/*操作库函数声明*/

/***************************ITMS注册状态*********************************/


/***************************PASSWORD 认证信息表*********************************/
word32 igdCmRmtMgtPasswordRegAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmRmtMgtPasswordRegAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmRmtMgtPasswordRegAttrInit(void);
/***************************PASSWORD 认证信息表*********************************/

/*************************** 远程访问web *********************************/
word32 igdCmRmtMgtWebAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmRmtMgtWebAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmRmtMgtWebAttrInit(void);

/*************************** 用户和性能数据上传*********************************/
word32 igdCmRmtMgtDataUploadAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmRmtMgtDataUploadAttrGet(uword8 *pucInfo, uword32 len);

word32 igdCmRmtMgtDataUploadAttrInit(void);


word32 igdCmRmtRestoreAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmRmtRestoreAttrGet(uword8 *pucInfo, uword32 len);

word32 igdCmRmtMgtDataUploadResultGet(uword8 *pucInfo, uword32 len);

uword32 igdCmRemoteMgtModuleInit(void);
void  igdCmRemoteMgtModuleExit(void);
#ifdef CONFIG_WITH_DBUS
word32 igd_cm_dbus_restore_nonkey_attr_set();
#endif
void cm_remote_mgt_oper_init(void);

#endif/*IGD_CM_REMOTEMGT_MODULE_PUB_H*/
