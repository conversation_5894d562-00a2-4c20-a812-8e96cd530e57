/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: igdCmIPoEEmulatorPub.h
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef IGD_CM_IPOE_EMULATOR_PUB_H
#define IGD_CM_IPOE_EMULATOR_PUB_H

#include <igdGlobalTypeDef.h>
#include "hi_odl_tab_emu_ipoe.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

extern word32  igdCmIPOEEmulatorAttrSet(uword8 *pucInfo, uword32 len);

extern word32  igdCmIPOEEmulatorAttrGet(uword8 *pucInfo, uword32 len);

extern word32  igdCmIPOEEmulatorStaticsGet(uword8 *pucInfo, uword32 len);

extern word32 igdCmIPOEEmulatorStart(void);

extern word32 igdCmIPOEEmulatorStop(void);

extern word32 igdCmIPOEEmulatorInit(void);

extern word32 igdEmuIPoEProcNotify(uword8 *pv_data);

void cm_emu_ipoe_oper_init(void);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif