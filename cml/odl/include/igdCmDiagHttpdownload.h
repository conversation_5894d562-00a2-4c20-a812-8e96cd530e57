/*
 * Copyright (c) Hisilicon(Shanghai) Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author: hsan
 * Create: 2024-02-08
 * History:
 */
#ifndef IGD_CM_DIAG_HTTPDOWNLOAD_H
#define IGD_CM_DIAG_HTTPDOWNLOAD_H

#include <igdGlobalTypeDef.h>
#include "hi_odl_tab_diag_httpdownload.h"

extern int32_t igd_cm_diag_speedtest_httpdownload_attr_set(uword8 *info, uword32 len);
extern int32_t igd_cm_diag_speedtest_httpdownload_attr_get(uword8 *info, uword32 len);
extern int32_t igd_cm_diag_speedtest_httpdownload_attr_start(void);
extern int32_t igd_cm_diag_speedtest_httpdownload_attr_stop(void);

extern int32_t igd_cm_diag_speedtest_ff_attr_set(uword8 *info, uword32 len);
extern int32_t igd_cm_diag_speedtest_ff_attr_get(uword8 *info, uword32 len);
extern int32_t igd_cm_diag_speedtest_ff_attr_start(void);
extern int32_t igd_cm_diag_speedtest_ff_attr_stop(void);
extern int32_t igd_cm_diag_speedtest_ff_attr_notify(uword8 *pv_data);
extern int32_t igd_cm_diag_speedtest_ff_attr_init(void);

extern int32_t igd_cm_diag_speedtest_local_attr_set(uword8 *info, uword32 len);
extern int32_t igd_cm_diag_speedtest_local_attr_get(uword8 *info, uword32 len);
extern int32_t igd_cm_diag_speedtest_local_attr_start(void);
extern int32_t igd_cm_diag_speedtest_local_attr_stop(void);

extern void cm_diag_speedtest_oper_init(void);

#endif /* IGD_CM_DIAG_HTTPDOWNLOAD_H */