#ifndef IGD_CM_VPN_MODULE_PUB_H
#define IGD_CM_VPN_MODULE_PUB_H
#include <igdGlobalTypeDef.h>
#include "hi_odl_tab_vpn.h"

word32 igdCmVpnAttrCfgAdd(uword8 *pucInfo, uword32 len);
word32 igdCmVpnAttrCfgSet(uword8 *pucInfo, uword32 len);
word32 igdCmVpnAttrCfgDel(uword8 *pucInfo, uword32 len);
word32 igdCmVpnAttrCfgGet(uword8 *pucInfo, uword32 len);
word32 igdCmVpnAttrCfgEntryNumGet(uword32 *entrynum);
word32 igdCmVpnAttrCfgGetall(uword8 *pucInfo, uword32 len);
word32 igdCmVpnAttrCfgGetallIndex(uword8 *pucInfo, uword32 len);
word32 igdCmVpnAttrCfgInit();

word32 igdCmVpnAttachCfgDelByTunlIndex(uword8 *pucInfo, uword32 len);
word32 igdCmVpnAttachCfgAdd(uword8 *pucInfo, uword32 len);
word32 igdCmVpnAttachCfgSet(uword8 *pucInfo, uword32 len);
word32 igdCmVpnAttachCfgDel(uword8 *pucInfo, uword32 len);
word32 igdCmVpnAttachCfgGet(uword8 *pucInfo, uword32 len);
word32 igdCmVpnAttachCfgEntryNumGet(uword32 *entrynum);
word32 igdCmVpnAttachCfgGetall(uword8 *pucInfo, uword32 len);
word32 igdCmVpnAttachCfgGetallIndex(uword8 *pucInfo, uword32 len);
word32 igdCmVpnAttachCfgInit();

word32 igdCmVpnAttrStateGet(uword8 *pucInfo, uword32 len);

uword32 igdCmVpnModuleInit(void);
void  igdCmVpnModuleExit(void);

void cm_vpn_oper_init(void);

#endif /* IGD_CM_VPN_MODULE_PUB_H */
