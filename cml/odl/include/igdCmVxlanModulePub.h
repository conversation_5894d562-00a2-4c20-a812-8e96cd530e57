/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm vxlan module
 * Author: HSAN
 * Create: 2022-10-27
 */


#ifndef IGD_CM_VXLAN_MODULE_PUB_H
#define IGD_CM_VXLAN_MODULE_PUB_H

#include <igdGlobalTypeDef.h>
#include "hi_odl_tab_vxlan.h"


/***************************VXLAN信息表*********************************/
typedef enum
{
    HI_VXLAN_ADD_E  = 1,
    HI_VXLAN_DEL_E,
    HI_VXLAN_ADD_IP_E,
    HI_VXLAN_ADD_IPV6_E,
    HI_VXLAN_DEL_IP_E,
    HI_VXLAN_DEL_IPV6_E,
    HI_VXLAN_M_VLAN_E,
    HI_VXLAN_M_BIND_LAN_E,
    HI_VXLAN_M_UNBIND_LAN_E,
    HI_VXLAN_ADD_GATEWAY_E,
    HI_VXLAN_ADD_IPV6_GATEWAY_E,
    HI_VXLAN_DEL_GATEWAY_E,
    HI_VXLAN_DEL_IPV6_GATEWAY_E,
    HI_VXLAN_M_MTU_E,
    HI_VXLAN_M_SWITCH_E,
    HI_VXLAN_M_IPV6_DNS_MASTER_E,
    HI_VXLAN_M_DNS_MASTER_E,
    HI_VXLAN_M_IPV6_DNS_SLAVE_E,
    HI_VXLAN_M_DNS_SLAVE_E,
    HI_VXLAN_M_ADDR_MODE_E,
    HI_VXLAN_M_IPV6_ADDR_MODE_E,
    HI_VXLAN_M_NAT_E,
    HI_VXLAN_M_BIND_WAN_E,
    HI_VXLAN_M_FWD_E,
}hi_vxlan_op_e;

typedef enum
{
	HI_VXLAN_VNI_FWD_TYPE_EMPTY=0,
	HI_VXLAN_VNI_FWD_TYPE_L2=1,
	HI_VXLAN_VNI_FWD_TYPE_L3=2,
}hi_vxlan_vni_fwd_type_e;

/*  */
typedef enum
{
	HI_VXLAN_VNI_CON_EMPTY=0,
	HI_VXLAN_VNI_CON_P2P,         /* 一个VNI里只有一个对端VTEP */
	HI_VXLAN_VNI_CON_P2MP,        /* 一个VNI里有多个对端VTEP */
}hi_vxlan_vni_con_type_e;


typedef enum
{
    HI_VXLAN_ADDR_CFG_TYPE_DYN=0,
	HI_VXLAN_ADDR_CFG_TYPE_STATIC=1,
}hi_vxlan_addr_cfg_type_e;


typedef enum
{
    HI_VXLAN_FALSE_E  = 0,
    HI_VXLAN_TRUE_E  = 1,
}hi_vxlan_bool_e;

typedef enum
{
    HI_VXLAN_IPV4_E  = 1,
    HI_VXLAN_IPV6_E,
    HI_VXLAN_DAUL_E,
}hi_vxlan_ip_mode_e;

typedef struct {
/* 通用配置 */
	hi_vxlan_op_e                   em_op;
	uword32                         ui_vni;
	hi_vxlan_vni_fwd_type_e         em_fwd;
	hi_vxlan_vni_con_type_e         em_con;
	uword32                         ui_vlan;
	uword32                         ui_mtu;
	word8                           ac_local_if[64];
	uword32                         ui_bind_lan;
	uword32                         ui_index;
	hi_vxlan_bool_e                 em_switch;

/* 外层配置 */
	hi_vxlan_ip_mode_e              outer_ip_mode;
	uword32                         ui_peer_ip;
	word8                           ui_ipv6_peer_addr[CM_IPV6_ADDR_LEN_MAX];
/* 内层配置 */
	hi_vxlan_ip_mode_e              inner_ip_mode;
	/* ipv4 */
	uword32                         ui_ip;
	uword32                         ui_ip_mask;
	hi_vxlan_addr_cfg_type_e        em_ip_cfg_type;
	hi_vxlan_bool_e                 em_nat;
	uword32                         ui_gateway;
	uword32                         ui_DNS_master;
	uword32                         ui_DNS_slave;
	/* ipv6 */
	word8                           ui_ipv6_addr[CM_IPV6_ADDR_LEN_MAX];
	uword32                         ui_ipv6_prefix;
	hi_vxlan_addr_cfg_type_e        em_ipv6_cfg_type;
	word8                           ui_ipv6_gateway[CM_IPV6_ADDR_LEN_MAX];
	word8                           ui_ipv6_DNS_master[CM_IPV6_ADDR_LEN_MAX];
	word8                           ui_ipv6_DNS_slave[CM_IPV6_ADDR_LEN_MAX];
} hi_vxlan_vni_info_s;

word32 igdCmVxlanVniAdd(uword8 *pucInfo, uword32 len);
word32 igdCmVxlanVniDel(uword8 *pucInfo, uword32 len);
word32 igdCmVxlanVniSet(uword8 *pucInfo, uword32 len);
word32 igdCmVxlanVniGet(uword8 *pucInfo, uword32 len);
word32 igdCmVxlanVniGetNum(uword32 *entrynum);
word32 igdCmVxlanVniGetAllentry(uword8 *pucInfo, uword32 len);


uword32 igdCmVxlanModuleInit(void);
void  igdCmVxlanModuleExit(void);

#ifdef CONFIG_WITH_VXLAN
void cm_vxlan_oper_init(void);
#else
static inline void cm_vxlan_oper_init(void) { }
#endif

#endif/*IGD_CM_VXLAN_MODULE_PUB_H*/
