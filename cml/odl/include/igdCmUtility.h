/************************************************************************/
/* include file for igdCmUtility.c                                      */
/* Written by qinxj 2015-06-08                                          */
/************************************************************************/

#ifndef IGD_CM_INCLUDE_UTILITY_H
#define IGD_CM_INCLUDE_UTILITY_H

#include "hi_odl_tab_wan_conn.h"

#define CM_LOG_FILE "/log/files/log"

enum {
	NORMAL_MODE = 0,
	FACTORY_TEST_MODE = 1,
};

enum {
	CM_CAP_NONE = 0,
	CM_CAP_CMCC = 0x1,
	CM_CAP_CTC = 0x2,
	CM_CAP_CU = 0x4,
	CM_CAP_SMART = 0x10,
	CM_CAP_VOIP =  0x20,
	CM_CAP_WLAN =  0x40,
	CM_CAP_USB =  0x80,
	CM_CAP_BUF = 0xffffffff,
};

#define CM_CTC_SMART_HGU (CM_CAP_CTC | CM_CAP_SMART)
#define CM_CU_SMART_HGU (CM_CAP_CU | CM_CAP_SMART)

typedef struct {
	char cmd[1024];
} igdCmSystemParam;

typedef struct {
	char cmd[256];
	char type[16];
	FILE *fp;
} igdCmPopenParam;
#define CM_EXECV_PARAM_NUM_MAX     4

typedef struct {
	char file[256];
	char argv[CM_EXECV_PARAM_NUM_MAX][256];
	unsigned int argv_num;
	int ping_pid;
} igdCmExecvParam;

typedef struct {
	unsigned int reboot_source;
	unsigned int reboot_type;
} igdCmRebootTypeParam;

#define CM_UTL_WAN_NAME_LEN 64
#define CM_UTL_WAN_INT_LEN  256
typedef struct {
	char wan_name[CM_UTL_WAN_NAME_LEN];
	char wan_interface[CM_UTL_WAN_INT_LEN];
} igd_cm_wan_param_s;

#define CM_CTRL_MSG_DATA_WORD_NUM  4

int igdCmChartoHex(char srcchar);
char igdCmHextoChar(unsigned char srchex);
int igdCmChartoMac(char *mac_char, unsigned char *mac_digital);
int igdCmMactoChar(unsigned char *mac_digital, char *mac_char);
int igdCmMactoCharWithoutColon(unsigned char *mac_digital, char *mac_char);
unsigned int igdCmChartoIp(char *pszIp);
int igdCmIptoChar(unsigned char *ip_digital, char *ip_char, unsigned int size);
int igdCmULIptoChar(unsigned int *ip_digital, char *ip_char, unsigned int size);
int igdCmIpv6toChar(void *ipv6_digital, char *ipv6_char);
int igdCmChartoIpv6(void *ipv6_digital, char *ipv6_char);
int igdCmMasktoChar(unsigned int mask_int, char *mask_char, unsigned int mask_char_size);
int igdCmChartoMask(char *mask_char);
int va_cmd(const char *cmd, int num, int dowait, ...);  //return 0:OK, other:fail
int igdCmIndexGet(unsigned char *pucInfo, unsigned int len);
char *substr_item_del(char *f_str, char *sub_str, char delim);

/* 获取PPP WAN的vlan */
int pppGetWanVlan(char *pc_waninterface, char *pucInfo);
/*获取可用的WanConnectionDevice实例号*/
int _igdCmFindMaxConDevInstNum(void);
/*获取当前WanConnectionDevice下可用的PPPConnection的实例号*/
int _igdCmFindMaxPPPConInstNum(unsigned int condevnum);
/*获取当前WanConnectionDevice下可用的IPConnection的实例号*/
int _igdCmFindMaxIPConInstNum(unsigned int condevnum);
int igdCmGlobalIndexToXPIndex(unsigned int GlobalIndex, unsigned int *pConDevIndex,
			      unsigned int *pXPConIndex);
int _igdCmXPIndexToGlobalIndex(unsigned int *pGlobalIndex, unsigned int ConDevIndex,
			       unsigned int XPConIndex, unsigned int AddressingType);
int igdCmFindWanConnectionByVid(unsigned int ulVlanId, char *pOutBuf, int OutBufSize);
unsigned int igdCmFirmwareUpgradeStateGet(void *pucInfo, unsigned int len);

unsigned int igdCmFirmwareUpgradeStateClear(void);
unsigned int igdCmFirmwareUpgradeStateSet(void *pucInfo, unsigned int len);

unsigned int igdCmFirmwareUpgradeFlagGet(void);
int igdCMServiceMod(void);
int igdCMCapability(int checkFlag);
void osTaskDelay(unsigned int milliseconds);
unsigned int Alport2BindPort(unsigned int alport);
unsigned int BindPort2Alport(unsigned int bindport);

int igdCmFindWanConnectionByName(char *paucWanName, int *pOutBuf);
int igdCmFindWanConnectionInfoByName(char *p_aucWanName, IgdWanConnectionAttrConfTab* p_entry);
int igdCmFindWanConnectionInfoByInterfaceName(char *p_aucWanIfName, IgdWanConnectionAttrConfTab* p_entry);
int igdCmFindWanConnectionByGlobalIndex(unsigned int ulGlobalIndex, char *pOutBuf, int OutBufSize);
void __igdCmDomainSave(int type, char *puc_url);
int getDomainFromUrl(char *pucUrl, char *pucUrlEnd, char *pucDomain);
int saveRebootInfo(unsigned int source, unsigned int type, time_t boot_time);
int getRebootInfo(igdCmRebootTypeParam *pRebootType);

int get_wlan_root_vap_id(int rf_band);
int get_wlan_ssid_num(int rf_band);
int get_wlan_tot_ssid_num(void);
int get_wlan_rf_band_num(void);
int igdCmSsidIdx2Portid(int ssidIdx);
int igdCmUtlLanportName2Portid(char *portName);
int igdCmUtlLanportid2Cmidx(int portId);
int32_t igd_cm_interface_name_to_interface_name(char *cm_name, char *name, uint32_t name_size);

#define CM_STRLEN(str) strlen((const char *)(str))

#define CM_STRNCMP(a, b, n) strncmp((const char *)(a), (const char*)(b), (n))

int igdCMSysRebootf(void);

/* for CmSysCtrl.c */
int config_restore(void);
int complete_restore();
int config_restore(void);
int remote_restore(void);
int long_keys_restore(void);
int short_keys_restore(void);

uint32_t igdCmCheckIpValid(const char *ipAddr);
uint8_t igd_cm_utl_is_ppp_internet_wan_exist();

int32_t igd_cm_get_wan_info_by_wan_interface(const char *wan_interface, IgdWanConnectionAttrConfTab *wan_conn);
int32_t igd_cm_get_wan_interface_by_wan_name(const char *wan_name, char *wan_interface, uint32_t size);

int igd_cm_clear_file(char *filename);
void igd_cm_lan_devices_reconnect(void);
uint32_t check_time(char *time);
uint32_t check_week_day(char *week_day);

void igd_cm_get_lan_port_num(uint32_t *lan_port_num);
#endif /*IGD_CM_INCLUDE_UTILITY_H*/
