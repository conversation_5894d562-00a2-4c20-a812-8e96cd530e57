/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: igd_rms_speed_test.h
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef IGD_RMS_SPEED_TEST_H
#define IGD_RMS_SPEED_TEST_H

#define CURL_WRITEDATA_FILE "/tmp/body"
#define CURL_WRITEHEAD_FILE "/tmp/head"

#define RMS_GET_CONTRACE_TYPE 1
#define RMS_GET_RESTULTS_TYPE 2

#define RMS_PPPOE_BRIDGE_MODE "0"
#define RMS_PPPOE_ROUTER_MODE "1"

#define RMS_ERROR_BAD_REQUEST      400
#define RMS_ERROR_ILLEGAL_FORMAT   421
#define RMS_ERROR_UNAUTHORIZED     422
#define RMS_ERROR_REQUEST_TIME_OUT 423
#define RMS_ERROR_UPLOAD_FAILED    424
#define RMS_ERROR_DOWNLOAD_FAILED  425

struct rms_speed_test_cfg {
	#define RMS_CFG_SERVER_URL_LEN 256
	char server_url[RMS_CFG_SERVER_URL_LEN];
	#define RMS_CFG_REPORT_URL_LEN 256
	char report_url[RMS_CFG_REPORT_URL_LEN];
	#define RMS_CFG_WIRTE_HEAD_LEN 256
	char write_head[RMS_CFG_WIRTE_HEAD_LEN];
	#define RMS_CFG_WIRTE_DATA_LEN 256
	char write_data[RMS_CFG_WIRTE_DATA_LEN];
	#define RMS_CFG_SPEED_MODE_LEN 256
	char speed_mode[RMS_CFG_SPEED_MODE_LEN];
	#define RMS_CFG_ERROR_CODE_LEN 256
	char error_code[RMS_CFG_ERROR_CODE_LEN];
};

struct result_insert_info {
	#define RESULT_INSERT_INFO_NAME_LEN 256
	char name[RESULT_INSERT_INFO_NAME_LEN];
	#define RESULT_INSERT_INFO_AVERAGE_LEN 256
	char average[RESULT_INSERT_INFO_AVERAGE_LEN];
	#define RESULT_INSERT_INFO_IP_LEN 256
	char ip[RESULT_INSERT_INFO_IP_LEN];
	#define RESULT_INSERT_INFO_SPEED_LEN 256
	char speed[RESULT_INSERT_INFO_SPEED_LEN];
	#define RESULT_INSERT_INFO_START_TIME_LEN 256
	char start_time[RESULT_INSERT_INFO_START_TIME_LEN];
	#define RESULT_INSERT_INFO_STOP_TIME_LEN 256
	char stop_time[RESULT_INSERT_INFO_STOP_TIME_LEN];
	#define RESULT_INSERT_INFO_MAX_SPEED_LEN 256
	char max_speed[RESULT_INSERT_INFO_MAX_SPEED_LEN];
	#define RESULT_INSERT_INFO_DOWNFILE_LARGE_LEN 256
	char downfile_large[RESULT_INSERT_INFO_DOWNFILE_LARGE_LEN];
	#define RESULT_INSERT_INFO_PERCENT_LEN 256
	char percent[RESULT_INSERT_INFO_PERCENT_LEN];
	#define RESULT_INSERT_INFO_SOURCE_LEN 256
	char source[RESULT_INSERT_INFO_SOURCE_LEN];
	#define RESULT_INSERT_INFO_WAN_INTERFACE_LEN 256
	char wan_interface[RESULT_INSERT_INFO_WAN_INTERFACE_LEN];
	#define RESULT_INSERT_INFO_IDENTIFY_LEN 256
	char identify[RESULT_INSERT_INFO_IDENTIFY_LEN];
	#define RESULT_INSERT_INFO_BACKGROUND_SIZE_LEN 256
	char backgrounde_size[RESULT_INSERT_INFO_BACKGROUND_SIZE_LEN];
	#define RESULT_INSERT_INFO_TYPE_LEN 256
	char type[RESULT_INSERT_INFO_TYPE_LEN];
	#define RESULT_INSERT_INFO_URL_LEN 256
	char url[RESULT_INSERT_INFO_URL_LEN];
};

struct contrace_item_info {
	#define CONTRACE_HTTP_TITLE_LEN 128
	char title[CONTRACE_HTTP_TITLE_LEN];
	#define CONTRACE_HTTP_VALUE_LEN 256
	char value[CONTRACE_HTTP_VALUE_LEN];
};

struct contrace_head_info {
	#define HANDERS_INFO_NUMBER_MAX 16
	#define HANDERS_STATUS_CODE_LEN 16
	char code[HANDERS_STATUS_CODE_LEN];
	struct contrace_item_info item[HANDERS_INFO_NUMBER_MAX];
};

struct contrace_rate_info {
	#define CONTRACE_RATE_INFO_NAME_LEN 256
	char name[CONTRACE_RATE_INFO_NAME_LEN];
	#define CONTRACE_RATE_INFO_IP_LEN 256
	char ip[CONTRACE_RATE_INFO_IP_LEN];
	#define CONTRACE_RATE_INFO_DIATYPE_LEN 256
	char diatype[CONTRACE_RATE_INFO_DIATYPE_LEN];
	#define CONTRACE_RATE_INFO_MAC_LEN 256
	char mac[CONTRACE_RATE_INFO_MAC_LEN];
	#define CONTRACE_RATE_INFO_SOFTWARE_LEN 256
	char software[CONTRACE_RATE_INFO_SOFTWARE_LEN];
	#define CONTRACE_RATE_INFO_HARDWARE_LEN 256
	char hardware[CONTRACE_RATE_INFO_HARDWARE_LEN];
	#define CONTRACE_RATE_INFO_PRODUCT_LEN 256
	char product[CONTRACE_RATE_INFO_PRODUCT_LEN];
	#define CONTRACE_RATE_INFO_TYPE_LEN 256
	char type[CONTRACE_RATE_INFO_TYPE_LEN];
};

struct results_speed_info {
	#define RESULTS_SPEED_INFO_SPEED_LEN 32
	char speed[RESULTS_SPEED_INFO_SPEED_LEN];
	#define RESULTS_SPEED_INFO_DOWNLOAD_LEN 32
	char download[RESULTS_SPEED_INFO_DOWNLOAD_LEN];
	#define RESULTS_SPEED_INFO_IDENTIFY_LEN 128
	char identify[RESULTS_SPEED_INFO_IDENTIFY_LEN];
	#define RESULTS_SPEED_INFO_IP_LEN 32
	char ip[RESULTS_SPEED_INFO_IP_LEN];
	#define RESULTS_SPEED_INFO_PORT_LEN 32
	char port[RESULTS_SPEED_INFO_PORT_LEN];
	#define RESULTS_SPEED_INFO_THREAD_COUNT_LEN 32
	char thread_count[RESULTS_SPEED_INFO_THREAD_COUNT_LEN];
	#define RESULTS_SPEED_INFO_CODE_LEN 32
	char code[RESULTS_SPEED_INFO_CODE_LEN];
};

typedef struct rms_speed_test_ctx {
	struct rms_speed_test_cfg conf;
	struct results_speed_info info;
	struct contrace_head_info http;
	struct contrace_rate_info rate;
	#define RMS_CONTRACE_STATUS_LEN 32
	char status[RMS_CONTRACE_STATUS_LEN];
	int32_t retval;
} rms_speed_test_ctx_t;

#endif
