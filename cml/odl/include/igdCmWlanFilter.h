#ifndef IGD_CM_WLAN_FILTER_MODULE_PUB_H
#define IGD_CM_WLAN_FILTER_MODULE_PUB_H

#include "hi_odl_tab_wlan.h"

#define WIFI_TRACE(fmt,...) \
	printf("%s()-%d: " fmt,(char*)__FUNCTION__,__LINE__,##__VA_ARGS__)

#if defined(CONFIG_WITH_WLAN_SSID_FILTER_LIST)
/***************************无线SSID过滤MAC列表************************************/
word32 igdCmWlanSsidFilterListAdd(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidFilterListDel(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidFilterListEntryNumGet(uword32 *entrynum);
word32 igdCmWlanSsidFilterListAllGet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidFilterListAllIndexGet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidFilterListSet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidFilterListGet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidFilterListInit();

word32 igdCmWlanSsidBlackFilterListAdd(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidBlackFilterListDel(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidBlackFilterListEntryNumGet(uword32 *entrynum);
word32 igdCmWlanSsidBlackFilterListAllGet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidBlackFilterListAllIndexGet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidBlackFilterListSet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidBlackFilterListGet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidBlackFilterListInit();

int32_t set_mac_filter_mod_to_all_ssid(hi_wifi_filter_mode_e wifi_filter_mod);
int32_t del_mac_filter_by_all_ssid(char *filter_mac, hi_wifi_filter_mode_e wifi_filter_mod);
int32_t del_mac_filter_by_ssid(int32_t ssid, char *filter_mac);
int32_t add_all_mac_filter_to_ssid(int32_t ssid, hi_wifi_filter_mode_e wifi_filter_mod);
int32_t add_mac_filter_to_ssid(int32_t ssid, char *filter_mac);
int32_t add_mac_filter_to_all_ssid(hi_wifi_filter_mode_e wifi_filter_mod, char *filter_mac);
int32_t get_filter_list_nums();
void cm_wlan_filter_oper_init(void);

#endif

#endif/*IGD_CM_WLAN_FILTER_MODULE_PUB_H*/
