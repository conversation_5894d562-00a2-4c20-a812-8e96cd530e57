#ifndef IGD_CM_MSGQ_PUB_H
#define IGD_CM_MSGQ_PUB_H
#include <pthread.h>
#include <sys/msg.h>
#include "hi_sysdef.h"
#include "igdCmFeatureDef.h"
#include "hi_odl_msg_def.h"

void cm_report_event_omci(int32_t mtype, int32_t sub_type);
void cm_report_event_cwmp(int32_t mtype, int32_t sub_type, const char *desc);

void sendMsg2Cwmp(int type, int wan_type);

word32 init_CMMsg(void);
void deinit_CMMsg(void);

/* upgrade state*/
#define CM_UPGRADE_STATE_SUCCESS       0U
#define CM_UPGRADE_STATE_IDLE          1U
#define CM_UPGRADE_STATE_DOWNLOADING   2U
#define CM_UPGRADE_STATE_DOWNLOAD_OK   3U
#define CM_UPGRADE_STATE_DOWNLOAD_ERR  4U
#define CM_UPGRADE_STATE_MEMEORY_ERR   5U
#define CM_UPGRADE_STATE_PKG_SIZE_ERR  6U
#define CM_UPGRADE_STATE_PKG_CHECK_ERR 7U
#define CM_UPGRADE_STATE_PKG_WRITING   8U
#define CM_UPGRADE_STATE_PKG_WRITE_ERR 9U
#define CM_UPGRADE_STATE_OPT_FLASH_OK  10U
#define CM_UPGRADE_STATE_OTHER_ERR     255U

#ifdef CONFIG_WITH_CMCC_UBUS
#define CM_T_UBUS_MSG_KEY "/tmp/cmcc_ubus"
#define CM_T_IPC_UBUS_MSG_LEN  128

#define CM_UBUS_MSGTYPE_STA_UPDOWN      (1)
#define CM_UBUS_MSGTYPE_WIFI_CHANGE     (2)
#define CM_UBUS_MSGTYPE_MESH_INFO       (3)
#define CM_UBUS_MSGTYPE_CONTROL_REPORT  (4)
#define CM_UBUS_MSGTYPE_WAN_CHANGE      (5)
#define CM_UBUS_MSGTYPE_LED_CHANGE      (6)
#define CM_UBUS_MSGTYPE_WIFI_WIFISWITCH       (7)
#define CM_UBUS_MSGTYPE_WIFI_WPSSWITCH        (8)
#define CM_UBUS_MSGTYPE_WIFI_REJECT_NETWORK_ACCESS        (8)
#define CM_UBUS_MSGTYPE_WIFI_NEWSSID          (9)
#define CM_UBUS_MSGTYPE_DIAGNOSTICS_PING_DONE             (10)
#define CM_UBUS_MSGTYPE_DIAGNOSTICS_TRACEROUTE_DONE       (11)
#define CM_UBUS_MSGTYPE_WIFI_CHANNEL_CHANGE               (12)
#define CM_UBUS_MSGTYPE_WIFI_VSIE_PROBERX                 (13)
#define CM_UBUS_CM_UBUS_MSGTYPE_CONTROL_RESET             (14)
#define CM_UBUS_SUBMSGTYPE_FIRMAWRE_DOWNLOAD  (0)
#define CM_UBUS_SUBMSGTYPE_FIRMAWRE_UPGRADE   (1)
#define CM_UBUS_SUBMSGTYPE_DEV_ONLINE    (1)
#define CM_UBUS_SUBMSGTYPE_DEV_OFFLINE   (0)
#define CM_UBUS_SUBMSGTYPE_MESH_CONFIG   (0)
#define CM_UBUS_SUBMSGTYPE_MESH_TOPO     (1)
#define CM_UBUS_SUBMSGTYPE_LED_ON        (1)
#define CM_UBUS_SUBMSGTYPE_WIFISWITCH_ON        (1)
#define CM_UBUS_SUBMSGTYPE_WIFISWITCH_OFF       (0)
#define CM_UBUS_SUBMSGTYPE_WPSSWITCH_ON        (1)
#define CM_UBUS_SUBMSGTYPE_WPSSWITCH_OFF       (0)
typedef struct {
	int type;
	int subtype;
	char buff[CM_T_IPC_UBUS_MSG_LEN];
} CM_UBUS_MSG;

void sendUbusMsg(CM_UBUS_MSG *Msg);
#endif

void cm_report_obj_event(int32_t tabid, int32_t mtype);

void cm_report_obj_update(int32_t tabid, int32_t act_type);

void cm_report_obj_update1(int32_t tabid, int32_t act_type, int32_t inst);

void cm_report_obj_update2(int32_t tabid, int32_t act_type, int32_t inst1, int32_t inst2);

void cm_report_lanhost_onoff_event(int32_t act_type, int32_t index);

void cm_report_event(int32_t mtype, const char *data);

void cm_report_appfilter_event(int32_t index, const char *data);

void cm_report_sta_info_event(int32_t mtype, const char *data, int32_t msg_len);

void cm_report_voice_event(int32_t mtype, const char *data);

void cm_report_mem_event(const char *data);
#endif /*IGD_CM_MSGQ_PUB_H*/
