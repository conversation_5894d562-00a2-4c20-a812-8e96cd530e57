/************************************************************************/
/* Type define for user system                                          */
/* Written by qinxj 2015-06-08                                          */
/************************************************************************/

#ifndef __IGD_GLOBAL_VAR_TYPE_DEF__
#define __IGD_GLOBAL_VAR_TYPE_DEF__
#include "hi_util_log.h"
#include "syslog.h"
#include "hi_odl_basic_type.h"

#ifndef WIN32
#define __PACK__				__attribute__ ((packed))
#else
#define __PACK__
#endif

#ifdef WIN32
#pragma pack()
#endif

#ifdef <PERSON>MP_DEBUG
#define CWMP_DEBUG  (1)
#else
#define CWMP_DEBUG (0)
#endif

#define PORT_0 0
#define PORT_1 1
#define PORT_2 2
#define PORT_3 3

#define CM_PARM_CHECK(bitmap, bits, value, min, max)      /*lint -save -e568 -e685*/\
	do {\
		if (((bitmap) & (bits)) == (bits)) { \
			if ((value) > (max) || (value) < (min)) \
			{\
				CM_LOG("param(%u) error, param is between %d and %d. \n", (value),(min),(max)); \
				return IGD_CM_GLOBAL_INPUT_PARA_ERROR; \
			} \
		} \
	} while (0)/*lint -restore*/

#define CM_ASSIGN_MIB_BAISC(bitMapName, bits, mibp, igdp, valueName) /*lint -save -e568 -e685*/\
	do { \
		if ((((igdp)->bitMapName) & (bits)) == (bits)) { \
			(mibp)->valueName = (igdp)->valueName; \
			(mibp)->bitMapName |= (bits); \
		} \
	} while (0)/*lint -restore*/

extern void igdCmLog(unsigned int ui_level, unsigned int ui_type, char *puc_fmt, ...);

#define CM_SYS_LOG(lvl, type , fmt, args...) \
	igdCmLog((uword32)lvl, (uword32)type, "[CM]"fmt, ##args)

#define CM_SYS_MOD_LOG(lvl,type, fmt, args...)  igdCmModLog((uword32)lvl, (uword32)type,fmt, ##args)

#endif /*__IGD_GLOBAL_VAR_TYPE_DEF__*/
