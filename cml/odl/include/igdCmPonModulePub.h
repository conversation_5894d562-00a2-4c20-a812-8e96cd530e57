#ifndef IGD_CM_PON_MODULE_PUB_H
#define IGD_CM_PON_MODULE_PUB_H
#include <igdGlobalTypeDef.h>
#include "hi_odl_tab_pon_info.h"

/***************************PON链路状态信息表*********************************/
word32 igdCmPonLinkStatusInfoGet(uword8 *pucInfo, uword32 len);

/***************************PON口统计信息表*********************************/
word32 igdCmPonStatisticsInfoGet(uword8 *pucInfo, uword32 len);

#ifdef CONFIG_WITH_PON
void cm_pon_oper_init(void);
uint32_t igd_cm_pon_module_init(void);
#else
static inline void cm_pon_oper_init(void) { }
static inline uint32_t igd_cm_pon_module_init(void) { return 0; }
#endif
#endif
