/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: igdCmEmuMc.h
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef IGD_CM_EMU_MC_H
#define IGD_CM_EMU_MC_H
#include "hi_odl_tab_emu_mc.h"
/*****************************拉流测试属性表*************************************/
/*表相关宏*/

extern word32 igdEmuMcProcNotify(uword8 *pv_data);
word32 igdCmEmuMcDetectionsAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmEmuMcDetectionsAttrGet(uword8 *pucInfo, uword32 len);
/*****************************拉流测试属性表*************************************/

/*****************************IPTV侦测属性表*************************************/
int32_t igdCmEmuIptvDetectionsAttrSet(uint8_t *info, uint32_t len);
int32_t igdCmEmuIptvDetectionsAttrGet(uint8_t *info, uint32_t len);
int32_t igdEmuIptvProcNotify(uint8_t *data);
int32_t igdEmuTraceRTProcNotify(uint8_t *data);

void cm_emu_mc_oper_init(void);

#endif /* SRC_ODL_INCLUDE_IGDCMEMUMC_*/
