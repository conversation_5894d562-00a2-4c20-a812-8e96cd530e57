/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: igdCmPlatformSrvModulePub.h
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef IGD_CM_PLATFORMSRV_MODULE_PUB_H
#define IGD_CM_PLATFORMSRV_MODULE_PUB_H

#include <igdGlobalTypeDef.h>
#include "hi_odl_tab_platform_srv.h"

/*************************PlatformServiceÊôÐÔ±í*******************************/
word32 igdCmPlatformSrvAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmPlatformSrvAttrGet(uword8 *pucInfo, uword32 len);

void cm_platform_srv_oper_init(void);

#endif/*IGD_CM_PLATFORMSRV_MODULE_PUB_H*/
