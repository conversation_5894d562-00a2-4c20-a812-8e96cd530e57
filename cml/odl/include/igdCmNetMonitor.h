/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: igdCmNetMonitor.h
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef IGD_CM_NETMONITOR_H
#define IGD_CM_NETMONITOR_H
#include "hi_odl_tab_netmonitor.h"

extern word32 igdCmNetMonitorSet(uword32 *pucInfo, uword32 len);
extern word32 igdCmNetMonitorGet(uword32 *pucInfo, uword32 len);
extern word32 igdCmNetMonitorBridgeDataGet(uword8 *pucInfo, uword32 len);
extern word32 igdCmNetMonitorBridgeDataAclAdd(uword8 *pucInfo, uword32 len);
extern word32 igdCmNetMonitorBridgeDataAclDel(uword8 *pucInfo, uword32 len);
extern word32 igdCmNetMonitorBridgeDataAclEntryNumGet(uword32 *entrynum);
extern word32 igdCmNetMonitorBridgeDataAclGetallIndex(uword8 *pucInfo, uword32 len);
extern word32 igdCmNetMonitorBridgeDataAclSet(uword8 *pucInfo, uword32 len);
extern word32 igdCmNetMonitorBridgeDataAclGet(uword8 *pucInfo, uword32 len);
extern word32 igdCmNetMonitorBridgeDataAclInit(void);

extern word32 igdCmNetMonitorInit(void);
void cm_netmonitor_oper_init(void);
#endif
