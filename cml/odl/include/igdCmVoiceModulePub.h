#ifndef IGD_CM_VOICE_MODULE_PUB_H
#define IGD_CM_VOICE_MODULE_PUB_H

#include <stdint.h>
#include <igdGlobalTypeDef.h>
#include "igdCmFeatureDef.h"
#include "hi_odl_tab_voice.h"

#if 0
#define VOICE_TRACE(fmt,...)
#else
#define VOICE_TRACE(fmt,...) \
	printf("%s()-%d: " fmt,(char*)__FUNCTION__,__LINE__,##__VA_ARGS__)
#endif

/***************************语音能力集信息*********************************/
word32 igdCmVoiceCapbilityInfoGet(uword8 *pucInfo, uword32 len);

/***************************语音Codec能力集信息*********************************/
word32 igdCmVoiceCodecCapbilityInfoGet(uword8 *pucInfo, uword32 len);

/***************************用户注册信息*********************************/
word32 igdCmVoiceUserRegInfoGet(uword8 *pucInfo, uword32 len);
/***************************语音通用属性表*********************************/
word32 igdCmVoiceGeneralAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmVoiceGeneralAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmVoiceGeneralAttrEntryNumGet(uword32 *entrynum);

/***************************SIP/IMS基本设置*********************************/
word32 igdCmVoiceSipBasicAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmVoiceSipBasicAttrGet(uword8 *pucInfo, uword32 len);

/***************************SIP/IMS响应*********************************/
int32_t igd_cm_response_map_attr_init(void);
int32_t igd_cm_response_map_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_response_map_attr_get(uint8_t *info, uint32_t len);
int32_t igd_cm_response_map_attr_add(uint8_t *info, uint32_t len);
int32_t igd_cm_response_map_attr_del(uint8_t *info, uint32_t len);
int32_t igd_cm_response_map_get_all_entry(uint8_t *info, uint32_t len);
int32_t igd_cm_response_map_get_entr_num(uint32_t *entry_num);

/***************************SIP用户拨号设置*********************************/
word32 igdCmVoiceSipUserAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmVoiceSipUserAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmVoiceSipUserEntryNumGet(uword32 *entrynum);
word32 igdCmVoiceSipUserAllIndexGet(uword8 *pucInfo, uword32 len);

/***************************H248基本设置*********************************/
word32 igdCmVoiceH248BasicAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmVoiceH248BasicAttrGet(uword8 *pucInfo, uword32 len);

/*操作库函数声明*/
/***************************H248基本设置*********************************/

/***************************H248用户设置*********************************/
word32 igdCmVoiceH248UserAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmVoiceH248UserAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmVoiceH248UserEntryNumGet(uword32 *entrynum);
word32 igdCmVoiceH248UserAllIndexGet(uword8 *pucInfo, uword32 len);

/***************************语音高级业务*********************************/
word32 igdCmVoiceAdvancedAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmVoiceAdvancedAttrGet(uword8 *pucInfo, uword32 len);


/***************************增益设置*********************************/
word32 igdCmVoiceProcessingAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmVoiceProcessingAttrGet(uword8 *pucInfo, uword32 len);

/***************************热线业务*********************************/
word32 igdCmVoiceHotlineAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmVoiceHotlineAttrGet(uword8 *pucInfo, uword32 len);

/***************************RTP业务*********************************/
word32 igdCmVoiceRtpAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmVoiceRtpAttrGet(uword8 *pucInfo, uword32 len);

/***************************传真业务*********************************/
word32 igdCmVoiceFaxAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmVoiceFaxAttrGet(uword8 *pucInfo, uword32 len);

/***************************线路Codec属性*********************************/
word32 igdCmVoiceLineCodecAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmVoiceLineCodecAttrGet(uword8 *pucInfo, uword32 len);

/***************************语音线路测试*********************************/
word32 igdCmVoiceLineTestInfoGet(uword8 *pucInfo, uword32 len);

/***************************模拟主叫通话测试*********************************/
#define WEB_CONFIG 1
word32 igdCmVoiceSimulateTestAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmVoiceSimulateTestAttrGet(uword8 *pucInfo, uword32 len);

/***************************模拟主叫通话测试结果*********************************/
word32 igdCmVoiceSimulateTestStateInfoGet(uword8 *pucInfo, uword32 len);

/***************************IAD模块诊断测试*********************************/
word32 igdCmVoiceIADTestStateInfoGet(uword8 *pucInfo, uword32 len);
word32 igdCmVoiceIADTestStateInfoSet(uword8 *pucInfo, uword32 len);

/***************************物理端口测试*********************************/
word32 igdCmVoicePhyInterfaceTestAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmVoicePhyInterfaceTestAttrGet(uword8 *pucInfo, uword32 len);

/***************************物理端口测试结果*********************************/
word32 igdCmVoicePhyInterfaceTestStateInfoGet(uword8 *pucInfo, uword32 len);
word32  igdCmVoiceModuleServerTypeSet(word32 server_type);

/***************************语音注册失败原因监控*********************************/
word32 igdCmLineLastRegisterErrorInfoSet(uword8 *pucInfo, uword32 len);
word32 igdCmLineLastRegisterErrorInfoGet(uword8 *pucInfo, uword32 len);

/***************************物理端口语音质量最差记录*********************************/
word32 igdCmVoicePoorQualityResetSet(uword8 *pucInfo, uword32 len);
word32 igdCmVoicePoorQualityResetGet(uword8 *pucInfo, uword32 len);

/***************************语音质量信息*********************************/
word32 igdCmVoicePoorQualityListGet(uword8 *pucInfo, uword32 len);
word32 igdCmVoicePoorQualityEntryNumGet(uword32 *entrynum);

/***************************订阅相关业务参数*********************************/
word32 igdCmVoiceLineIMSAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmVoiceLineIMSAttrGet(uword8 *pucInfo, uword32 len);

/***************************用户呼叫统计*********************************/
word32 igdCmVoiceLineStatsAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmVoiceLineStatsAttrGet(uword8 *pucInfo, uword32 len);

/***************************RTP 会话性能统计**begin*******************************/

word32 igdCmVoiceLineRtpStatsAttrGet(uword8 *pucInfo, uword32 len);

/***************************最近通话统计**begin*******************************/
word32 igdCmVoiceRecentCallStatsGet(uword8 *pucInfo, uword32 len);

/***************************IAD Operation操作*********************************/
word32 igdCmVoiceIadOperationAttrSet(uword8 *pucInfo, uword32 len);

/***************************IAD Operation操作*********************************/
/***************************POTS口 通话状态*begin*********************************/
word32 igdCmVoiceLineCallStatsGet(uword8 *pucInfo, uword32 len);

/***************************语音模块（IAD）基本信息查询*begin*********************************/
word32 igdCmVoiceIadInfoGet(uword8 *pucInfo, uword32 len);

/********************VOIP会话性能统计查(SIP代理&&会话统计信息)*begin***********************/
word32 igdCmSipUsageStatsGet(uword8 *pucInfo, uword32 len);

/***************************线路测试*********************************/
int32_t igd_cm_x_cu_line_test_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_x_cu_line_test_attr_get(uint8_t *info, uint32_t len);

/***************************物理端口拓展*********************************/
int32_t igd_cm_cu_extend_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_cu_extend_attr_get(uint8_t *info, uint32_t len);

uword32 igdCmVoiceModuleInit(void);

#ifdef CONFIG_WITH_VOIP
void cm_voice_oper_init(void);
#else
static inline void cm_voice_oper_init(void) {}
#endif

#endif/*IGD_CM_VOICE_MODULE_PUB_H*/
