/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: cm tab special service vr obj attribute
 * Author: HSAN
 * Create: 2023-12-18
 */

#ifndef IGD_CM_SPECIAL_SERVICE_VR_H
#define IGD_CM_SPECIAL_SERVICE_VR_H

extern int32_t igdCmSpecialServiceVRAttrAdd(uword8 *info, uword32 len);
extern int32_t igdCmSpecialServiceVRAttrDel(uword8 *info, uword32 len);
extern int32_t igdCmSpecialServiceVRAttrSet(uword8 *info, uword32 len);
extern int32_t igdCmSpecialServiceVRAttrGet(uword8 *info, uword32 len);
extern int32_t igdCmSpecialServiceVRAttrEntryNumGet(uword32 *entrynum);
extern int32_t igdCmSpecialServiceVRAttrGetAllIndex(uint8_t *info, uint32_t len);

void cm_special_service_vr_oper_init(void);

#endif
