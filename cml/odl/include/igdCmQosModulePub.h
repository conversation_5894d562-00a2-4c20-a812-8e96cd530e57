#ifndef IGD_CM_QOS_MODULE_PUB_H
#define IGD_CM_QOS_MODULE_PUB_H
#include <igdGlobalTypeDef.h>
#include "hi_odl_tab_qos.h"

/***************************Qos基本信息表*********************************/
word32 igdCmQosBasicAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmQosBasicAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmQosBasicAttrInit(void);

/***************************Qos分类信息表*********************************/
word32 igdCmQosClfAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmQosClfAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmQosClfAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmQosClfAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmQosClfEntryNumGet(uword32 *entrynum);
word32 igdCmQosClfGetallIndex(uword8 *pucInfo, uword32 len);
word32 igdCmQosClfGetallConfigInfo(uword8 *pucInfo, uword32 len);

/***************************Qos分类类型表*********************************/
word32 igdCmQosClfTypeAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmQosClfTypeAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmQosClfTypeAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmQosClfTypeAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmQosClfTypeEntryNumGet(uword32 *entrynum);
word32 igdCmQosClfTypeGetallIndex(uword8 *pucInfo, uword32 len);
word32 igdCmQosClfTypeGetallConfigInfo(uword8 *pucInfo, uword32 len);

/***************************APP业务表*********************************/
word32 igdCmQosAppAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmQosAppAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmQosAppAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmQosAppAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmQosAppEntryNumGet(uword32 *entrynum);
word32 igdCmQosAppGetallIndex(uword8 *pucInfo, uword32 len);
word32 igdCmQosAppGetallConfigInfo(uword8 *pucInfo, uword32 len);
word32 igdCmQosAppAttrInit(void);


/***************************队列优先级属性表*********************************/
word32 igdCmQosQueueAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmQosQueueAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmQosQueueAttrInit(void);

/***************************数据流限速属性表*********************************/
word32 igdCmQosFlowLimitAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmQosFlowLimitAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmQosFlowLimitAttrInit(void);

/***************************ctc 数据流限速属性表*********************************/
word32 igdCmQosItemAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmQosItemAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmQosItemAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmQosItemAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmQosItemEntryNumGet(uword32 *entrynum);

int32_t igd_cm_smart_qos_upstream_profile_set(uint8_t *info, uint32_t len);
int32_t igd_cm_smart_qos_downstream_profile_set(uint8_t *info, uint32_t len);

#ifdef CONFIG_WITH_DOWNLINK_QOS
int32_t igd_cm_downlink_qos_basic_set(uint8_t *info, uint32_t len);
int32_t igd_cm_downlink_qos_basic_get(uint8_t *info, uint32_t len);

int32_t igd_cm_downlink_qos_clf_add(uint8_t *info, uint32_t len);
int32_t igd_cm_downlink_qos_clf_del(uint8_t *info, uint32_t len);
int32_t igd_cm_downlink_qos_clf_set(uint8_t *info, uint32_t len);
int32_t igd_cm_downlink_qos_clf_get(uint8_t *info, uint32_t len);
int32_t igd_cm_downlink_qos_clf_entry_num_get(uint32_t *entry_num);
int32_t igd_cm_downlink_qos_clf_all_index_get(uint8_t *info, uint32_t len);
int32_t igd_cm_downlink_qos_clf_all_config_info_get(uint8_t *info, uint32_t len);

int32_t igd_cm_downlink_qos_clf_type_add(uint8_t *info, uint32_t len);
int32_t igd_cm_downlink_qos_clf_type_del(uint8_t *info, uint32_t len);
int32_t igd_cm_downlink_qos_clf_type_set(uint8_t *info, uint32_t len);
int32_t igd_cm_downlink_qos_clf_type_get(uint8_t *info, uint32_t len);
int32_t igd_cm_downlink_qos_clf_type_entry_num_get(uint32_t *entry_num);
int32_t igd_cm_downlink_qos_clf_type_get_all_index(uint8_t *info, uint32_t len);
int32_t igd_cm_downlink_qos_clf_type_get_all_entry(uint8_t *info, uint32_t len);

int32_t igd_cm_downlink_qos_queue_set(uint8_t *info, uint32_t len);
int32_t igd_cm_downlink_qos_queue_get(uint8_t *info, uint32_t len);

void igd_cm_downlink_qos_init();
int32_t igd_cm_downlink_qos_basic_init();
int32_t igd_cm_downlink_qos_queue_init();
#endif

uword32 igdCmQosModuleInit(void);
void  igdCmQosModuleExit(void);

void cm_qos_oper_init(void);

#endif
