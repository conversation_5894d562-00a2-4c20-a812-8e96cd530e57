#ifndef IGD_CM_DPI_MODULE_PUB_H
#define IGD_CM_DPI_MODULE_PUB_H

#include <igdGlobalTypeDef.h>
#include "hi_odl_tab_ctdpi.h"


word32 igdCmDpiUploadCfgAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmDpiUploadCfgAttrGet(uword8 *pucInfo, uword32 len);

word32 igdCmDpiBasicCfgAttrInit(void);
word32 igdCmDpiBasicCfgAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmDpiBasicCfgAttrGet(uword8 *pucInfo, uword32 len);

word32 igdCmDpiDnsQueryCfgAttrInit(void);
word32 igdCmDpiDnsQueryCfgAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmDpiDnsQueryCfgAttrGet(uword8 *pucInfo, uword32 len);

word32 igdCmDpiHttpGetCfgAttrInit(void);
word32 igdCmDpiHttpGetCfgAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmDpiHttpGetCfgAttrGet(uword8 *pucInfo, uword32 len);

word32 igdCmDpiTcpConnectCfgAttrInit(void);
word32 igdCmDpiTcpConnectCfgAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmDpiTcpConnectCfgAttrGet(uword8 *pucInfo, uword32 len);

word32 igdCmDpiAppCfgAttrInit(void);
word32 igdCmDpiAppCfgAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmDpiAppCfgAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmDpiAppCfgAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmDpiAppCfgAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmDpiAppGetAllEntry(uword8 *pucInfo, uword32 len);
word32 igdCmDpiAppGetEntryNum(uword32 *entrynum);

word32 igdCmDpiClassificationCfgAttrInit(void);
word32 igdCmDpiClassificationCfgAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmDpiClassificationCfgAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmDpiClassificationCfgAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmDpiClassificationCfgAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmDpiClassificationGetAllEntry(uword8 *pucInfo, uword32 len);
word32 igdCmDpiClassificationGetEntryNum(uword32 *entrynum);

uword32 igdCmDpiModuleInit(void);
void igdCmDpiModuleExit(void);
void cm_ct_dpi_oper_init(void);

#endif/*IGD_CM_WLAN_MODULE_PUB_H*/
