#ifndef IGD_CM_LAN_MODULE_PUB_H
#define IGD_CM_LAN_MODULE_PUB_H
#include <igdGlobalTypeDef.h>
#include "hi_odl_tab_landevice.h"
#include "hi_odl_tab_lanhost.h"

/***************************LAN口IP地址信息表*********************************/
/*业务逻辑处理函数声明*/
word32 igdCmLanIPAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmLanIPAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmLanIPAttrInit(void);

/***************************LAN侧IPv4属性表*********************************/
/*业务逻辑处理函数声明*/
word32 igdCmLanIPv4AttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmLanIPv4AttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmLanIPv4AttrInit(void);

void igdCmAutoUpdateLanIPv4Attr(const char *WanName);


/***************************LAN侧IPv6属性表*********************************/
word32 igdCmLanIPv6AttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmLanIPv6AttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmLanIPv6AttrDistribute(IgdLanIPv6AttrConfTab *pucInfo, word32 dhcp6srv_mark,
				  word32 radvd_mark, word32 radvd_prefix_mark);
word32 igdCmAutoUpdateLanIPv6Attr(void);
word32 igdCmAutoUpdateNPTv6(void);
word32 igdCmAutoUpdateLanIPv6DNSInfoByName(uword8 *pucWanName);
word32 igdCmAutoUpdateLanIPv6PrefixInfoByName(word8 *pucWanName);
word32 igdCmLanIPv6AttrInit(void);

/***************************LAN口属性表*********************************/
word32 igdCmLanPortAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmLanPortAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmLanPortEntryNumGet(uword32 *entrynum);
word32 igdCmLanPortAttrInit(void);

/***************************LAN口状态信息表*********************************/
word32 igdCmLanPortStatusInfoGet(uword8 *pucInfo, uword32 len);

int32_t igd_cm_lanhost_mng_attr_get(uint8_t *info, uint32_t len);
int32_t igd_cm_lanhost_mng_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_lanhost_mng_attr_init(void);

int32_t igd_cm_lanhost_dev_add(uint8_t *info, uint32_t len);
int32_t igd_cm_lanhost_dev_del(uint8_t *info, uint32_t len);
int32_t igd_cm_lanhost_dev_set(uint8_t *info, uint32_t len);
int32_t igd_cm_lanhost_dev_get(uint8_t *info, uint32_t len);
int32_t igd_cm_lanhost_dev_entry_num_get(uint32_t *entrynum);
int32_t igd_cm_lanhost_dev_all_index_get(uint32_t *info, uint32_t len);
int32_t igd_cm_lanhost_dev_all_entry_get(uint8_t *info, uint32_t len);
int32_t igd_cm_lanhost_dev_init(void);

int32_t igd_cm_lanhost_dev_info_get(uint8_t *info, uint32_t len);
int32_t igd_lanhost_dev_online_entry_num_get(uint32_t *entrynum);

int32_t igd_cm_lanhost_sample_param_set(uint8_t *info, uint32_t len);
int32_t igd_cm_lanhost_sample_param_get(uint8_t *info, uint32_t len);

/***************************LAN侧下挂设备动态IP地址信息表*********************************/
word32 igdCmLanDhcpHostsStatusGet(uword8 *pucInfo, uword32 len);

/********************************LAN口以太网报文过滤*******************************************/
word32 igdCmLanEtherInterfaceFilterListAdd(uword8 *pucInfo, uword32 len);
word32 igdCmLanEtherInterfaceFilterListDel(uword8 *pucInfo, uword32 len);
word32 igdCmLanEtherInterfaceFilterListSet(uword8 *pucInfo, uword32 len);
word32 igdCmLanEtherInterfaceFilterListGet(uword8 *pucInfo, uword32 len);
word32 igdCmLanEtherInterfaceFilterListEntryNumGet(uword32 *entrynum);
word32 igdCmLanEtherInterfaceFilterListGetallConfigInfo(uword8 *pucInfo, uword32 len);
word32 igdCmLanEtherInterfaceFilterListInit(void);

/***************************LAN侧DHCP绑定属性表*********************************/
word32 igdCmLanDhcpStaticListInit(void);
word32 igdCmLanDhcpStaticListAdd(uword8 *pucInfo, uword32 len);
word32 igdCmLanDhcpStaticListDel(uword8 *pucInfo, uword32 len);
word32 igdCmLanDhcpStaticListGet(uword8 *pucInfo, uword32 len);
word32 igdCmLanDhcpStaticEntryNumGet(uword32 *entrynum);
word32 igdCmLanDhcpStaticListAllGet(uword8 *pucInfo, uword32 len);

/*************************** BR-LAN ipv6 addr *********************************/
word32 igdLanBrLanIpv6AddrListGet(uword8 *pucInfo, uword32 len);


word32 IgdCmIsInLanSubnet(word8 *host,  uword32 size);

/***************************LAN侧下挂机顶盒的数目*********************************/
word32 igdCmLanSTBNumberGet(uword8 *pucInfo, uword32 len);

int32_t igd_cm_lan_dhcp_cond_serving_pool_attr_add(uint8_t *info, uint32_t len);
int32_t igd_cm_lan_dhcp_cond_serving_pool_attr_del(uint8_t *info, uint32_t len);
int32_t igd_cm_lan_dhcp_cond_serving_pool_attr_entry_num_get(uint32_t *entry_num);
int32_t igd_cm_lan_dhcp_cond_serving_pool_attr_all_index_get(uint32_t *info, uint32_t len);
int32_t igd_cm_lan_dhcp_cond_serving_pool_attr_all_entry_get(uint8_t *info, uint32_t len);
int32_t igd_cm_lan_dhcp_cond_serving_pool_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_lan_dhcp_cond_serving_pool_attr_get(uint8_t *info, uint32_t len);

int32_t igd_cm_storage_black_list_attr_add(uint8_t *info, uint32_t len);
int32_t igd_cm_storage_black_list_attr_del(uint8_t *info, uint32_t len);
int32_t igd_cm_storage_black_list_attr_entry_num_get(uint32_t *entry_num);
int32_t igd_cm_storage_black_list_attr_all_index_get(uint32_t *info, uint32_t len);
int32_t igd_cm_storage_black_list_attr_all_entry_get(uint8_t *info, uint32_t len);
int32_t igd_cm_storage_black_list_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_storage_black_list_attr_get(uint8_t *info, uint32_t len);

void cm_lan_oper_init(void);

uword32 igdCmLanModuleInit(void);
void  igdCmLanModuleExit(void);

#endif /*IGD_CM_LAN_MODULE_PUB_H*/
