#ifndef IGD_CM_WAN_MODULE_PUB_H
#define IGD_CM_WAN_MODULE_PUB_H

#include <igdGlobalTypeDef.h>
#include "hi_odl_tab_wan_conn.h"

/***************************Wan连接信息总览*********************************/
word32 igdCmWanInfoGet( uword8 *pucInfo, uword32 len);

/***************************Wan接口属性*********************************/
word32 igdCmWanCommInterfaceAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmWanCommInterfaceAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmWanCommInterfaceAttrInit(void);


/*************************** Wan接口信息 *********************************/
word32 igdCmWanCommInterfaceInfoGet(uword8 *pucInfo, uword32 len);

/***************************Wan连接*********************************/
word32 igdCmWanConnectionAdd(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionDel(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionSet(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionGet(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnEntryNumGet(uword32 *entrynum);
word32 igdCmWanConnectionGetallIndex(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionInit(void);

uword32 igdCmWanConnectionDistribute(IgdWanConnectionAttrConfTab *pWanAttr);
int32_t igd_cm_distribute_wan_conns(void);

/***************************Wan连接状态信息*********************************/
word32 igdCmWanConnectionStateSet(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionStateGet(uword8 *pucInfo, uword32 len);


/***************************端口映射*********************************/
word32 igdCmWanConnectionPortMappingAdd(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionPortMappingDel(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionPortMappingSet(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionPortMappingGet(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionPortMappingEntryNumGet(uword32 *entrynum);
word32 igdCmWanPortMappingGetallIndex(uword8 *pucInfo, uword32 len);
word32 igdCmWanPortMappingGetallConfigInfo(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionPortMappingInit(void);

/***************************DDNS*********************************/
word32 igdCmWanConnectionDDNSSet(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionDDNSGet(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionDDNSInit(void);
/*以下接口为dbus提供假多实例*/
word32 igdCmWanConnectionDDNSAdd(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionDDNSDel(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionDDNSEntryNumGet(uword32 *entrynum);
word32 igdCmWanDDNSGetallIndex(uword8 *pucInfo, uword32 len);

/***************************OPTION60*********************************/
word32 igdCmWanConnectionOption60Add(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionOption60Del(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionOption60Set(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionOption60Get(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionOption60EntryNumGet(uword32 *entrynum);
word32 igdCmWanConnectionGetallOption60ConfigInfo(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionOption60Init(void);

/***************************OPTION125*********************************/
word32 igdCmWanConnectionOption125Add(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionOption125Del(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionOption125Set(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionOption125Get(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionOption125EntryNumGet(uword32 *entrynum);
word32 igdCmWanConnectionGetallOption125ConfigInfo(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionOption125Init(void);

/***************************OPTION16*********************************/
word32 igdCmWanConnectionOption16Add(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionOption16Del(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionOption16Set(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionOption16Get(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionOption16EntryNumGet(uword32 *entrynum);
word32 igdCmWanConnectionGetallOption16ConfigInfo(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionOption16Init(void);

/***************************OPTION17*********************************/
word32 igdCmWanConnectionOption17Add(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionOption17Del(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionOption17Set(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionOption17Get(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionOption17EntryNumGet(uword32 *entrynum);
word32 igdCmWanConnectionGetallOption17ConfigInfo(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionOption17Init(void);

/***************************Wan连接统计*********************************/

word32 igdCmWanConnectionStatisticGet(uword8 *pucInfo, uword32 len);

/***************************绑定*********************************/

word32 igdCmWanConnectionBindAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionBindAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmAutoUpdateVlanBindList(word32 lVlanId);
word32 igdCmWanConnectionVlanBindInit(void);


/***************************VLAN绑定*********************************/
typedef struct
{
    uword32 ulVlanInput;
    uword32 ulVlanOutput;
} hgu_vlan_binding;

/*************单播ARP/ND在线探测节点.*******************/
word32 idg_neighbor_detection_cfg_add(uword8 *info, uword32 len);
word32 idg_neighbor_detection_cfg_del(uword8 *info, uword32 len);
word32 idgNeighborDetectionCfgSet(uword8 *info, uword32 len);
word32 idg_neighbor_detection_cfg_get(uword8 *info, uword32 len);
word32 idg_neighbor_detection_cfg_num_get(uword32 *entrynum);
word32 idg_neighbor_detection_get_all_cfg_info(uword8 *info, uword32 len);
word32 igd_neighbor_detection_config_init(void);

/***************************Wan连接索引*********************************/

/***************************端口映射索引*********************************/

/*************************** EponLink / GponLink *********************************/
word32 igdCmWanConnectionGeponLinkSet(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionGeponLinkGet(uword8 *pucInfo, uword32 len);


/***************************DNS_TUNNEL*********************************/
word32 igdCmWanConnectionDNSTunnelAdd(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionDNSTunnelDel(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionDNSTunnelSet(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionDNSTunnelGet(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionDNSTunnelInit(void);
word32 igdCmWanConnectionDNSTunnelEntryNumGet(uword32 *entrynum);
word32 igdCmWanConnectionDNSTunnelGetallIndex(uword8 *pucInfo, uword32 len);

/***************************DNS_TUNNEL*********************************/

/***************************DNS_STATUS*********************************/
word32 igdCmWanConnectionDNSStatusSet(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionDNSStatusGet(uword8 *pucInfo, uword32 len);

/***************************DNS_TUNNEL*********************************/
word32 igdCmWanConnectionIPFwdRouteDelAllEntry();
word32 igdCmWanConnectionIPFwdRouteInit(void);

/******************************默认组播wan**************************************/
word32 igdCmWanConnectionIptvDefSet(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionIptvDefGet(uword8 *pucInfo, uword32 len);

/******************************dns服务********************************/
word32 igdCmDbusDnsServiceAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmDbusDnsServiceAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmDbusFlushDns(void);

int32_t igd_cm_wan_port_trigger_attr_add(uint8_t *info, uint32_t len);
int32_t igd_cm_wan_port_trigger_attr_del(uint8_t *info, uint32_t len);
int32_t igd_cm_wan_port_trigger_attr_entry_num_get(uint32_t *entry_num);
int32_t igd_cm_wan_port_trigger_attr_all_index_get(uint32_t *info, uint32_t len);
int32_t igd_cm_wan_port_trigger_attr_all_entry_get(uint8_t *info, uint32_t len);
int32_t igd_cm_wan_port_trigger_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_wan_port_trigger_attr_get(uint8_t *info, uint32_t len);

uword32 igdCmWanModuleInit(void);
void  igdCmWanModuleExit(void);

/*  IGD_WAN_PORT_TRIGGER_ATTR_CFG_TAB */
#define  IGD_WAN_PORT_TRIGGER_MAXNUM                                     (32)

int32_t igd_cm_wan_port_trigger_attr_add(uint8_t *info, uint32_t len);
int32_t igd_cm_wan_port_trigger_attr_del(uint8_t *info, uint32_t len);
int32_t igd_cm_wan_port_trigger_attr_entry_num_get(uint32_t *entry_num);
int32_t igd_cm_wan_port_trigger_attr_all_index_get(uint32_t *info, uint32_t len);
int32_t igd_cm_wan_port_trigger_attr_all_entry_get(uint8_t *info, uint32_t len);
int32_t igd_cm_wan_port_trigger_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_wan_port_trigger_attr_get(uint8_t *info, uint32_t len);

void cm_wan_oper_init(void);

void wan_detection_config_update(const char *wan_name, uint8_t act);

#endif/*IGD_CM_WAN_MODULE_PUB_H*/
