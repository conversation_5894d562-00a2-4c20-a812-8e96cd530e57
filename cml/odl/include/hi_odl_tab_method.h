#ifndef HI_ODL_TAB_METHODS_H
#define HI_ODL_TAB_METHODS_H
#include "igdCmFeatureDef.h"
#include <igdGlobalTypeDef.h>

#include <igdCmMsgq.h>
#include <igdCmAlarm.h>

#include <igdCmGlobalModulePub.h>
#include <igdCmLanModulePub.h>
#include <igdCmPonModulePub.h>
#include <igdCmUniPonModulePub.h>
#include <igdCmWanModulePub.h>
#include <igdCmVoiceModulePub.h>
#include <igdCmSecureModulePub.h>
#include <igdCmAppModulePub.h>
#include <igdCmQosModulePub.h>
#include <igdCmRemoteMgtModulePub.h>
#include <igdCmSysMngModulePub.h>
#include <igdCmPMModulePub.h>
#include <igdCmIPoEEmulatorPub.h>
#include <igdCmWlanModulePub.h>
#include <igdCmUtility.h>
#include <igdCmEmuMc.h>
#include <igdCmEmuPPP.h>
#include <igdCmEmuPing.h>
#include <igdCmEmuTraceroute.h>
#include <igdCmEmuSpeed.h>
#include <igdCmDPIModulePub.h>
#include <igdCmNetMonitor.h>
#include <igdCmTransferServices.h>

#include <igdCmPlatformSrvModulePub.h>
#include <igdCmTimerModulePub.h>

#include <igdCmAwifiModulePub.h>
#include <igdCmSwModulePub.h>
#include <igdCmIncomingFilter.h>
#include <igdCmVxlanModulePub.h>
#include <igdCmEmModulePub.h>
#include <igdCmVpnModule.h>
#include <igdCmLACPModule.h>
#include "igdCmPacketCapture.h"
#include <igdCmPortMirror.h>
#include <igdCmAppFilter.h>
#include <igdCmDscpAppRoute.h>
#include <igdCmCloudVR.h>
#include <igdCmHardAccCancel.h>
#include <igdCmHardAccCancelManager.h>
#include <igdCmVSIEProbeRspTxVSIE.h>
#include <igdCmVSIEProbeRspTxVSIEManager.h>
#include <igdCmSpecialServiceVR.h>
#include <igdCmDiagHttpdownload.h>

enum hi_odl_apply_act {
	HI_ODL_APPLY_NONE,
	HI_ODL_APPLY_ADD,
	HI_ODL_APPLY_DEL,
	HI_ODL_APPLY_UPDATE,
};

typedef int32_t (*cm_op0_pf)();
typedef int32_t (*cm_op1_pf)(uint32_t *inst_num);
typedef int32_t (*cm_op2_pf)(uint8_t *info, uint32_t len);

void cm_tab_match_dir(uint32_t tab_id);
void cm_oper_reg(uint32_t tab_id, cm_op2_pf set, cm_op2_pf get);
void cm_oper_reg1(uint32_t tab_id, cm_op2_pf add, cm_op2_pf del,
		  cm_op1_pf inst_num, cm_op2_pf all_index, cm_op2_pf all_entry);
void cm_oper_reg2(uint32_t tab_id, cm_op0_pf start, cm_op0_pf stop, cm_op0_pf init);

#define IGDCM_OPER_REG(tab_id, pf_add, pf_del, pf_set, pf_get, \
		       pf_getEntryNum, pf_getAllIndex, pf_getAllEntry,            \
		       pf_start, pf_stop, pf_init)                                \
do {                                                       \
	cm_oper_reg(tab_id, (cm_op2_pf)pf_set, (cm_op2_pf)pf_get); \
	cm_oper_reg1(tab_id, (cm_op2_pf)pf_add, (cm_op2_pf)pf_del, \
		     (cm_op1_pf)pf_getEntryNum, (cm_op2_pf)pf_getAllIndex,  \
		     (cm_op2_pf)pf_getAllEntry);                            \
	cm_oper_reg2(tab_id, pf_start, pf_stop, pf_init);          \
	cm_tab_match_dir(tab_id);                                  \
} while (0)

#ifdef CONFIG_WITH_DBUS_MSG
void cm_notify_flag_reg(uint32_t tab_id, int32_t notify_flag, int32_t idx_num);
void cm_notify_flag_idx_reg(uint32_t tab_id, int32_t idx1_offset, int32_t idx1_size,
			    int32_t idx2_offset, int32_t idx2_size);
#define IGDCM_DBUS_NOTIFY_REG(tab_id, notify_flag, idx_num, idx1_offset, idx1_size, \
			      idx2_offset, idx2_size)                                \
do {                                                       \
	cm_notify_flag_reg(tab_id, notify_flag, idx_num);                     \
	cm_notify_flag_idx_reg(tab_id, idx1_offset, idx1_size, idx2_offset, idx2_size);   \
	cm_tab_match_dir(tab_id);                                  \
} while (0)
#endif

int32_t cm_mib_single_get(uint8_t *info, uint32_t len);

#endif
