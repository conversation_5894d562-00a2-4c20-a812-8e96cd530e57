/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author: HSAN
 * Create: 2023-12-18
 */
#ifndef IGD_ODL_ASYNC_H
#define IGD_ODL_ASYNC_H

#include <stdio.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>

typedef enum {
	CFG_ATTR_OP_INVALID = 0x0,
	CFG_ATTR_OP_SET = 0x1,
	CFG_ATTR_OP_CACHE = 0x2,
	CFG_ATTR_OP_BOTTOM
} cm_attr_op_type;

typedef int32_t (*asyn_rt_pf)(uint8_t *info, uint32_t len);
typedef int32_t (*op_cache_new_pf)(uint8_t *cache, uint32_t inst, uint32_t len);
typedef int32_t (*op_cache_merge_pf)(const uint8_t *cfg, uint8_t *cache, uint32_t inst, uint32_t len);
typedef uint64_t (*op_get_bitmap_pf)(const uint8_t *info, uint32_t len);
typedef void (*op_set_bitmap_pf)(uint64_t bitmap, uint8_t *info, uint32_t len);

typedef struct __igd_cm_cache_op_handler {
	uint32_t table_id;
	op_cache_new_pf cache_new_pf;
	op_cache_merge_pf cache_merge_pf;
	op_get_bitmap_pf get_bitmap_pf;
	op_set_bitmap_pf set_bitmap_pf;
} igd_cm_cache_op_handler;

int32_t igd_cm_asyn_setter_runtine(asyn_rt_pf pf_call, uint8_t *info, uint32_t len);
bool igd_cm_asyn_query_setter_cache(uint32_t table_id, uint32_t inst, uint8_t *out, uint32_t len_of_data);
bool igd_cm_asyn_reg_cache_op_handler(uint32_t table_id, op_cache_new_pf cache_new_pf, op_cache_merge_pf cache_merge_pf,
	op_get_bitmap_pf get_bitmap_pf, op_set_bitmap_pf set_bitmap_pf);

#endif /* IGD_ODL_ASYNC_H */