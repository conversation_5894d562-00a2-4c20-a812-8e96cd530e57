#ifndef IGD_CM_APP_MODULE_PUB_H
#define IGD_CM_APP_MODULE_PUB_H
#include <igdGlobalTypeDef.h>
#include "igdCmApi.h"

#define APP_TELNET_USE 0
#define APP_SSH_USE 1
#define APP_FTP_USE 2
#define APP_SAMBA_USE 3
#define APP_USERNAME_USED_BIT ((1 << APP_TELNET_USE) | (1 << APP_SSH_USE) | (1 << APP_FTP_USE))

#define CM_APP_SERVICE_FTP 1
#define CM_APP_SERVICE_TELNET 2
#define CM_APP_SERVICE_SSH 3
#define CM_APP_SERVICE_SAMBA 4
word32 igdCmAppServiceCheckAccountusedBit(uword32 ulServiceindex, uword8 *pucUsername,
		uword32 *pulReult);


/***************************ALG*********************************/
word32 igdCmAppAlgAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmAppAlgAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmAppAlgAttrInit(void);

word32 igdCmAppDmzAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmAppDmzAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmAppDmzAttrInit(void);

/*业务逻辑处理函数声明*/
word32 igdCmAppUpnpAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmAppUpnpAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmAppUpnpAttrInit(void);
/***************************UPNP*********************************/

/***************************IGMP*********************************/
/*业务逻辑处理函数声明*/
word32 igdCmAppIgmpAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmAppIgmpAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmAppIgmpAttrInit(void);

int32_t igd_cm_igmp_device_authentication_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_igmp_device_authentication_attr_get(uint8_t *info, uint32_t len);
int32_t igd_cm_ott_auth(igdCmTabCmd *tab_cmd);

word32 igdCmAppPortMacGet(uword8 *pucInfo, uword32 len);
/***************************PORT MAC*********************************/

/***************************静态路由*********************************/
/*业务逻辑处理函数声明*/
word32 igdCmAppRouteAdd(uword8 *pucInfo, uword32 len);
word32 igdCmAppRouteDel(uword8 *pucInfo, uword32 len);
word32 igdCmAppRouteGet(uword8 *pucInfo, uword32 len);
word32 igdCmAppRouteEntryNumGet(uword32 *entrynum);
word32 igdCmAppRouteListGetallIndex(uword8 *pucInfo, uword32 len);
word32 igdCmAppRouteInit(void);

/***************************静态路由*********************************/

/***************************时间管理*********************************/
word32 igdCmAppTimeAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmAppTimeAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmAppTimeAttrInit(void);

/***************************接入服务管理*********************************/
word32 igdCmAppServiceManageAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmAppServiceManageAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmAppServiceManageAttrInit(void);
void save_telnet_config(uword32 enable, uword32 port, hi_char8 *account, hi_char8 *password);

/***************************组播VLAN*********************************/
word32 igdCmAppIgmpMulticastVlanInfoSet(uword8 *pucInfo, uword32 len);

/********************************IPV4静态路由参数*******************************************/
word32 igdCmIpLayer3ForwardingInfoSet(uword8 *pucInfo, uword32 len);
word32 igdCmIpLayer3ForwardingInfoGet(uword8 *pucInfo, uword32 len);
word32 igdCmIpLayer3ForwardingAttrInit(void);



/********************************IPV4静态路由参数*******************************************/
word32 igdCmIpv4Layer3RouteAdd(uword8 *pucInfo, uword32 len);
word32 igdCmIpv4Layer3RouteSet(uword8 *pucInfo, uword32 len);
word32 igdCmIpv4Layer3RouteDel(uword8 *pucInfo, uword32 len);
word32 igdCmIpv4Layer3RouteGet(uword8 *pucInfo, uword32 len);
word32 igdCmIpv4Layer3RouteEntryNumGet(uword32 *entrynum);
word32 igdCmIpv4Layer3RouteGetallIndex(uword8 *pucInfo, uword32 len);
word32 igdCmIpv4Layer3RouteGetallConfigInfo(uword8 *pucInfo, uword32 len);
word32 igdCmIpv4Layer3RouteInit(void);

/********************************IPV6静态路由表*******************************************/
word32 igdCmAppv6Layer3RouteAdd(uword8 *pucInfo, uword32 len);
word32 igdCmAppv6Layer3RouteSet(uword8 *pucInfo, uword32 len);
word32 igdCmAppv6Layer3RouteDel(uword8 *pucInfo, uword32 len);
word32 igdCmAppv6Layer3RouteGet(uword8 *pucInfo, uword32 len);
word32 igdCmAppv6Layer3RouteEntryNumGet(uword32 *entrynum);
word32 igdCmAppv6Layer3RouteGetallConfigInfo(uword8 *pucInfo, uword32 len);
word32 igdCmAppv6Layer3RouteGetallGetallIndex(uword8 *pucInfo, uword32 len);
word32 igdCmAppv6Layer3RouteInit(void);
word32 igdCmAppRouteFlush(word8 *pcWanName);
word32 igdCmAppRouteRefresh(word8 *pcOldWanName, word8 *pcNewWanName);

/********************************密码复杂度管理表*******************************************/
word32 igdCmAppPasswordComplexitySet(uword8 *pucInfo, uword32 len);
word32 igdCmAppPasswordComplexityGet(uword8 *pucInfo, uword32 len);
word32 igdCmAppPasswordComplexity(word8 *pucAccount, word8 *pucPassword, word32 *presult);

/********************************telnet账户管理表*******************************************/
word32 igdCmAppTelnetAccountAdd(uword8 *pucInfo, uword32 len);
word32 igdCmAppTelnetAccountSet(uword8 *pucInfo, uword32 len);
word32 igdCmAppTelnetAccountDel(uword8 *pucInfo, uword32 len);
word32 igdCmAppTelnetAccountGet(uword8 *pucInfo, uword32 len);
word32 igdCmAppTelnetAccountEntryNumGet(uword32 *entrynum);
word32 igdCmAppTelnetAccountGetallIndex(uword8 *pucInfo, uword32 len);
word32 igdCmAppTelnetAccountGetallConfigInfo(uword8 *pucInfo, uword32 len);
word32 igdCmAppTelnetAccountInit(void);


/********************************ftp账户管理表*******************************************/
word32 igdCmAppFtpAccountAdd(uword8 *pucInfo, uword32 len);
word32 igdCmAppFtpAccountSet(uword8 *pucInfo, uword32 len);
word32 igdCmAppFtpAccountDel(uword8 *pucInfo, uword32 len);
word32 igdCmAppFtpAccountGet(uword8 *pucInfo, uword32 len);
word32 igdCmAppFtpAccountEntryNumGet(uword32 *entrynum);
word32 igdCmAppFtpAccountGetallIndex(uword8 *pucInfo, uword32 len);
word32 igdCmAppFtpAccountGetallConfigInfo(uword8 *pucInfo, uword32 len);
word32 igdCmAppFtpAccountInit(void);

/******************************** VPN 相关配置*********************************************/
word32 igdCmAppVPNAttrCfgAdd(uword8 *pucInfo, uword32 len);
word32 igdCmAppVPNAttrCfgSet(uword8 *pucInfo, uword32 len);
word32 igdCmAppVPNAttrCfgDel(uword8 *pucInfo, uword32 len);
word32 igdCmAppVPNAttrCfgGet(uword8 *pucInfo, uword32 len);
word32 igdCmAppVPNAttrCfgEntryNumGet(uword32 *entrynum);
word32 igdCmAppVPNAttrCfgGetall(uword8 *pucInfo, uword32 len);
word32 igdCmAppVPNAttrCfgGetallIndex(uword8 *pucInfo, uword32 len);
word32 igdCmAppVPNAttrCfgInit();

word32 igdCmAppVPNAttachCfgDelByTunlIndex(uword8 *pucInfo, uword32 len);
word32 igdCmAppVPNAttachCfgAdd(uword8 *pucInfo, uword32 len);
word32 igdCmAppVPNAttachCfgSet(uword8 *pucInfo, uword32 len);
word32 igdCmAppVPNAttachCfgDel(uword8 *pucInfo, uword32 len);
word32 igdCmAppVPNAttachCfgGet(uword8 *pucInfo, uword32 len);
word32 igdCmAppVPNAttachCfgEntryNumGet(uword32 *entrynum);
word32 igdCmAppVPNAttachCfgGetall(uword8 *pucInfo, uword32 len);
word32 igdCmAppVPNAttachCfgGetallIndex(uword8 *pucInfo, uword32 len);
word32 igdCmAppVPNAttachCfgInit();

word32 igdCmAppVPNAttrStateGet(uword8 *pucInfo, uword32 len);

int32_t igd_cm_transfer_auto_diag_monitor_attr_get(uint8_t *info, uint32_t len);

int32_t igd_cm_transfer_auto_diag_redirect_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_transfer_auto_diag_redirect_attr_get(uint8_t *info, uint32_t len);

int32_t igd_cm_transfer_fast_path_speed_up_attr_add(uint8_t *info, uint32_t len);
int32_t igd_cm_transfer_fast_path_speed_up_attr_del(uint8_t *info, uint32_t len);
int32_t igd_cm_transfer_fast_path_speed_up_attr_entry_num_get(uint32_t *entry_num);
int32_t igd_cm_transfer_fast_path_speed_up_attr_all_index_get(uint32_t *info, uint32_t len);
int32_t igd_cm_transfer_fast_path_speed_up_attr_all_entry_get(uint8_t *info, uint32_t len);
int32_t igd_cm_transfer_fast_path_speed_up_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_transfer_fast_path_speed_up_attr_get(uint8_t *info, uint32_t len);

int32_t igd_cm_transfer_l2tp_vpn_attr_add(uint8_t *info, uint32_t len);
int32_t igd_cm_transfer_l2tp_vpn_attr_del(uint8_t *info, uint32_t len);
int32_t igd_cm_transfer_l2tp_vpn_attr_entry_num_get(uint32_t *entry_num);
int32_t igd_cm_transfer_l2tp_vpn_attr_all_index_get(uint32_t *info, uint32_t len);
int32_t igd_cm_transfer_l2tp_vpn_attr_all_entry_get(uint8_t *info, uint32_t len);
int32_t igd_cm_transfer_l2tp_vpn_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_transfer_l2tp_vpn_attr_get(uint8_t *info, uint32_t len);

uword32 igdCmAppModuleInit(void);
void  igdCmAppModuleExit(void);

void cm_netapp_oper_init(void);

#endif /*IGD_CM_APP_MODULE_PUB_H*/
