/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm lan host obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_LANHOST_H
#define HI_ODL_TAB_LANHOST_H
#include "hi_odl_basic_type.h"

#define LANHOST_MNG_INIT_CFG_STATEANDINDEX		(0xFFFFFFFF)
/***************************LAN侧下挂设备管理信息表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

	uword32 ulLANHostMaxNumber;              /* IN&OUT  下挂设备个数上限 */
	uword32 ulLANHostNumber;                 /* OUT     当前下挂设备个数 */
	uword32 ulLANHostOnlineNumber;           /* OUT     当前下挂在线设备个数 */
	uword32 ulControlListMaxNumber;          /* IN&OUT  下挂设备受控个数上限 */
	uword32 ulControlListNumber;             /* OUT     当前下挂设备受控个数 */

#define LAN_HOSTS_ENABLESTATS_DEACTIVE    (0)
#define LAN_HOSTS_ENABLESTATS_ACTIVE      (1)
	uword8 ucEnableStats;                    /* IN&OUT  是否启用LAN侧设备流量统计功能 */

#define LAN_HOSTS_GETDEVICE_DEACTIVE      (0)
#define LAN_HOSTS_GETDEVICE_ACTIVE        (1)
	uword8 ucGetOnlineDevicesSnapShot;       /* IN      获取所有在线设备的基本信息 */
	uword8 ucGetOnlineDeviceList;            /* IN      获取所有在线设备的列表 */
	uword8 ucGetLANHostPathbyMAC;            /* IN      基于mac地址获取所有设备的基本信息 */
	uword8 aucMacAddr[CM_MAC_ADDR_LEN];      /* IN      MAC地址 */

	uword8 aucPad[CM_TWO_PADS];

#define LAN_HOSTS_MNG_ATTR_MASK_BIT0_MAXNUM          (0x01)
#define LAN_HOSTS_MNG_ATTR_MASK_BIT1_CTL_MAXNUM      (0x02)
#define LAN_HOSTS_MNG_ATTR_MASK_BIT2_ENABLE_STATS    (0x04)
#define LAN_HOSTS_MNG_ATTR_MASK_BIT3_ONLINE_DEVINFO  (0x08)
#define LAN_HOSTS_MNG_ATTR_MASK_BIT4_ONLINE_DEVLIST  (0x10)
#define LAN_HOSTS_MNG_ATTR_MASK_BIT5_DEVINFO_BYMAC   (0x20)
#define LAN_HOSTS_MNG_ATTR_MASK_BIT6_MAC_ADDR        (0x40)
#define LAN_HOSTS_MNG_ATTR_MASK_ALL                  (0x7F)
	uword32 ulBitmap;
} __PACK__ IgdLanHostsMngAttrTab;

/***************************LAN侧下挂设备信息表*********************************/
/*表相关宏*/
#define LANHOST_DEV_INIT_CFG_STATEANDINDEX		(0xFFFFFFFF)
#define IGD_LAN_HOSTS_TAB_RECORD_NUM (253)

typedef struct {
	uword32 ulStateAndIndex;
	uword32	ulIndex;

#define LAN_NAMED_HOST_NAME_LEN (256)
	word8 aucHostName[LAN_NAMED_HOST_NAME_LEN];       /* 下挂设备名称 */
	uword8 aucMacAddr[CM_MAC_ADDR_LEN];                /* MAC地址 */
	uword8 aucPad1[CM_TWO_PADS];

#define LAN_NAMED_HOST_DEVINFO_LEN (64)
	word8 aucMacAddress[CM_MAC_CHAR_LEN];             /* IN&OUT  自定义MAC地址 */
	word8 aucDevType[LAN_NAMED_HOST_DEVINFO_LEN];     /* IN&OUT  下挂设备类型 */
	word8 aucDevBrand[LAN_NAMED_HOST_DEVINFO_LEN];    /* IN&OUT  下挂设备品牌 */
	word8 aucDevModel[LAN_NAMED_HOST_DEVINFO_LEN];    /* IN&OUT  下挂设备型号 */
	word8 aucDevOS[LAN_NAMED_HOST_DEVINFO_LEN];       /* IN&OUT  下挂设备操作系统 */
#define LAN_HOST_ONLINE_NOTIFY_DEACTIVE      (0)
#define LAN_HOST_ONLINE_NOTIFY_ACTIVE        (1)
	uword8 ucOnlineNofication;                         /* IN&OUT  下挂设备上线是否上报 */
#define LAN_HOST_GATEWAY_ACCESS_DEACTIVE     (0)
#define LAN_HOST_GATEWAY_ACCESS_ACTIVE       (1)
#define LAN_HOST_INTERNET_ACCESS_ACTIVE      (2)
	uword8 ucInternetAccess;                           /* IN&OUT  下挂设备上网限制 */
#define LAN_HOST_STORAGE_ACCESS_DEACTIVE     (0)
#define LAN_HOST_STORAGE_ACCESS_ACTIVE       (1)
	uword8 ucStorageAccess;                            /* IN&OUT  下挂设备访问存储设备限制 */
#define LAN_HOST_MACFILTER_MODE_DISABLE      (0)
#define LAN_HOST_MACFILTER_MODE_BLACK        (1)
#define LAN_HOST_MACFILTER_MODE_WHITE        (2)
	uword8 ucMacfilterMode;                            /* IN&OUT  设备黑白名单过滤模式*/
	uword32 ulMaxUSBandwidth;                          /* IN&OUT  下挂设备上行带宽限制 */
	uword32 ulMaxDSBandwidth;                          /* IN&OUT  下挂设备下行带宽限制 */
#define LAN_HOSTS_ONOFFLINE_TIME_LEN (32)
	word8 aucLatestActiveTime[LAN_HOSTS_ONOFFLINE_TIME_LEN];   /* OUT    设备最新的上线时间 */
	word8 aucLatestInactiveTime[LAN_HOSTS_ONOFFLINE_TIME_LEN]; /* OUT    设备最新的下线时间 */
	uword32  ulOnlineTime;                             /* OUT    设备上线时间(UTC 1970-1-1 00:00:00算起到现在所经过的秒数) */
	uword32  ulOfflineTime;                            /* OUT    设备离线时间(UTC 1970-1-1 00:00:00算起到现在所经过的秒数) */
#define LAN_HOST_MACSELECT_SMAC              (0)
#define LAN_HOST_MACSELECT_DMAC              (1)
	uword32  ulMacSelect;

	uword32 ulQosClassID;
	uword8 ucDeviceDownRateEnable;
	uword8 ucPad2[CM_THREE_PADS];
	uword32 ulClassQueue;
	uword32 ulPolicerID;

	uword8 ucLastPort;
#define LAN_HOST_RF_BAND_2_4G 0
#define LAN_HOST_RF_BAND_5G   1
	uword8 ucLastRfBand;
	uword8 ucLastInterfaceType;/*默认Ethernet*/
	uword8 ucLastHostType;/*设备类型*/
	uword8 ucLastConnectionType;
	uword8 ucLastAddressSource;/*地址来源方式*/
	uword8 ucPad3[CM_TWO_PADS];
	word64 lLastLeaseTimeRemaining;/*剩余地址租期*/
#define LAN_HOSTS_NAME_LEN (256)
	word8 aucLastHostName[LAN_HOSTS_NAME_LEN];/*设备名称*/
	word8 aucLastIpAddr[CM_IPV6_ADDR_LEN_MAX];         /*IPv4地址*/
	word8 aucLastIp6Addr[CM_IPV6_ADDR_LEN_MAX *
						  2];     /*IPv6地址, 包括实际分配地址和链路本地地址*/
#define LAN_HOST_OPT_DATA_LEN (128)
	word8  staLastVendor[LAN_HOST_OPT_DATA_LEN];        /* OUT 设备归属厂商信息 */
	word8  aucLastOptData[LAN_HOST_OPT_DATA_LEN];
	uint8_t speedup_state;
	uint8_t pads[CM_THREE_PADS];
	uint32_t us_guarantee_bandwidth;
	uint32_t ds_guarantee_bandwidth;
#define LAN_NAMED_DEV_NAME_LEN (64)
	word8 aucDevName[LAN_NAMED_DEV_NAME_LEN];

#define LAN_HOST_ATTR_MASK_BIT0_HOST_NAME           (0x01)
#define LAN_HOST_ATTR_MASK_BIT1_MAC_ADDR            (0x02)

#define LAN_HOST_ATTR_MASK_BIT2_DEV_TYPE            (0x04)
#define LAN_HOST_ATTR_MASK_BIT3_DEV_BRAND           (0x08)
#define LAN_HOST_ATTR_MASK_BIT4_DEV_MODEL           (0x10)
#define LAN_HOST_ATTR_MASK_BIT5_DEV_OS              (0x20)
#define LAN_HOST_ATTR_MASK_BIT6_ONLINE_NOTIFY       (0x40)
#define LAN_HOST_ATTR_MASK_BIT7_INTERNET_ACCESS     (0x80)
#define LAN_HOST_ATTR_MASK_BIT8_STORAGE_ACCESS      (0x100)
#define LAN_HOST_ATTR_MASK_BIT9_MAX_BANDWIDTH_US    (0x200)
#define LAN_HOST_ATTR_MASK_BIT10_MAX_BANDWIDTH_DS   (0x400)
#define LAN_HOST_ATTR_MASK_BIT11_MACADDRESS         (0x800)
#define LAN_HOST_ATTR_MASK_BIT12_MACFILTMODE        (0x1000)
#define LAN_HOST_ATTR_MASK_BIT13_ACTIVETIME         (0x2000)
#define LAN_HOST_ATTR_MASK_BIT14_INACTIVETIME       (0x4000)
#define LAN_HOST_ATTR_MASK_BIT15_MACSELECT          (0X8000)
#define LAN_HOST_ATTR_MASK_BIT16_QOSCLASSID         (0x10000)
#define LAN_HOST_ATTR_MASK_BIT17_DEV_DOWNRATE_EN    (0x20000)
#define LAN_HOST_ATTR_MASK_BIT18_CLASSQUEUE         (0x40000)
#define LAN_HOST_ATTR_MASK_BIT19_POLICER_ID         (0X80000)
#define LAN_HOST_ATTR_MASK_ALL                      (0x1FFFFFF)

	uword32 ulBitmap;
#define LAN_HOST_ATTR_MASK1_SPEEDUP_STATE            (1 << 0)
#define LAN_HOST_ATTR_MASK1_US_GUARANTEE_BANDWIDTH   (1 << 1)
#define LAN_HOST_ATTR_MASK1_DS_GUARANTEE_BANDWIDTH   (1 << 2)
#define LAN_HOST_ATTR_MASK1_LAST_HOST_NAME           (1 << 3)
#define LAN_HOST_ATTR_MASK1_DEV_NAME_NAME            (1 << 4)
#define LAN_HOST_ATTR_MASK1_ALL                      ((1 << 5) - 1)
	uint32_t bitmap1;

} __PACK__ IgdLanHostsNameAttrTab;

/***************************LAN侧下挂设备采样信息表*********************************/
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulReportInterval;
	uword32 ulSampleInterval;
#define LAN_HOST_STATE_INFO_MASK_BIT0_REPORT_INTERVAL (0x01)
#define LAN_HOST_STATE_INFO_MASK_BIT1_SAMPLE_INTERVAL (0x02)
	uword32 ulBitmap;
} __PACK__ IgdLanHostsDevSampleTab;

/***************************LAN侧下挂设备状态信息表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

	uword32	ulIndex;

#define LAN_HOSTS_INTERFACE_TYPE_ETHERNET (0)
#define LAN_HOSTS_INTERFACE_TYPE_USB (1)
#define LAN_HOSTS_INTERFACE_TYPE_80211 (2)
#define LAN_HOSTS_INTERFACE_TYPE_HOMEPNA (3)
#define LAN_HOSTS_INTERFACE_TYPE_HOMEPLUG (4)
#define LAN_HOSTS_INTERFACE_TYPE_OTHER (5)
	uword8 ucInterfaceType;/*默认Ethernet*/
#define LAN_HOSTS_DEACTIVE (0)
#define LAN_HOSTS_ACTIVE (1)
	uword8 ucActive;/*设备是否在线*/
	uword8 aucPad[CM_TWO_PADS];

#define LAN_HOSTS_TYPE_DEFAULT  (0)
#define LAN_HOSTS_TYPE_COMPUTER (1)
#define LAN_HOSTS_TYPE_HGW      (2)
#define LAN_HOSTS_TYPE_STB      (3)
#define LAN_HOSTS_TYPE_PHONE    (4)
#define LAN_HOSTS_TYPE_CAMERA   (5)
#define LAN_HOSTS_TYPE_PAD      (6)
#define LAN_HOSTS_TYPE_AP       (7)
#define LAN_HOSTS_TYPE_OTHER    (8)
	uword8 ucHostType;/*设备类型*/
#define LAN_HOSTS_ADDRESS_SOURCE_STATIC (0)
#define LAN_HOSTS_ADDRESS_SOURCE_DHCP   (1)
#define LAN_HOSTS_ADDRESS_SOURCE_AUTOIP (2)
	uword8 ucAddressSource;/*地址来源方式*/
	uword8 aucPad1[CM_TWO_PADS];

	word8 aucHostName[LAN_HOSTS_NAME_LEN];/*设备名称*/
	word8 aucIpAddr[CM_IPV6_ADDR_LEN_MAX];         /*IPv4地址*/
	word8 aucIp6Addr[CM_IPV6_ADDR_LEN_MAX *
					      2];     /*IPv6地址, 包括实际分配地址和链路本地地址*/
	uword8 aucMacAddress[CM_MAC_ADDR_LEN];/*Mac地址*/
	uword8 aucPad2[CM_TWO_PADS];
#define LAN_HOSTS_LEASTIME_REMAIN_INFINITE (-1)
	word64 lLeaseTimeRemaining;/*剩余地址租期*/
	uword8 ucControlStatus;                          /* OUT    设备受控状态 */
	uword8 ucConnectionType;                         /* OUT    设备连接方式 */
#define LAN_HOSTS_LINK_FREQ_NA     0
#define LAN_HOSTS_LINK_FREQ_2G     1
#define LAN_HOSTS_LINK_FREQ_5G     2
#define LAN_HOSTS_LINK_FREQ_MLD    3
	uword8 ucLinkFreq;
	uword8 ucPort;                                   /* OUT    设备连接端口 (link id, 从0起始) */
	uword32  ulNegorate;                             /* OUT    设备协商速率 */
	word32 lPowerLevel;                              /* OUT    设备连接信号强度 rssi */
	word32 lPowerLevel_mlo;                              /* OUT    设备连接信号强度 */
	word8 aucLatestActiveTime[LAN_HOSTS_ONOFFLINE_TIME_LEN];
	/* OUT    设备最新的上线时间 */
	word8 aucLatestInactiveTime[LAN_HOSTS_ONOFFLINE_TIME_LEN];
	/* OUT    设备最新的下线时间 */
	uword32  ulOnlineTime;                           /* OUT    设备在线时长 */
	uword32  ulOfflineTime;                          /* OUT    设备离线时长 */
	ulword64 ulRxBytes;                              /* OUT    设备收包字节数 */
	ulword64 ulTxBytes;                              /* OUT    设备发包字节数 */
	ulword64 rxPackets;
	ulword64 txPackets;
	ulword64 crcErrCnt;
#define LAN_HOSTS_CATE_LEN 12
	word8 cate[LAN_HOSTS_CATE_LEN]; /*下挂终端分类，取值："STA"、"MeshNode"*/
	uword32  ulNegoRxRate;                               /* mlo related OUT    设备收包协商速率 */
	uword32  ulNegoRxRate_mlo;                               /* mlo related OUT    设备收包协商速率 */
	uword32  ulNegoTxRate;                               /* mlo related OUT    设备发包协商速率 */
	uword32  ulNegoTxRate_mlo;                               /* mlo related OUT    设备发包协商速率 */
	uword32  ulCurRxRate;                               /* mlo related OUT    设备收包采样实时速率 */
	uword32  ulCurRxRate_mlo;                               /* mlo related OUT    设备收包采样实时速率 */
	uword32  ulCurTxRate;                               /* mlo related OUT    设备发包采样实时速率 */
	uword32  ulCurTxRate_mlo;                               /* mlo related OUT    设备发包采样实时速率 */
	uword32  ulRxRate;                               /* mlo related OUT    设备收包采样均值速率 */
	uword32  ulRxRate_mlo;                               /* mlo related OUT    设备收包采样均值速率 */
	uword32  ulTxRate;                               /* mlo related OUT    设备发包采样均值速率 */
	uword32  ulTxRate_mlo;                               /* mlo related OUT    设备发包采样均值速率 */
	uword32  maxRxRate;                              /* mlo related OUT    设备收包采样最大速率 */
	uword32  maxRxRate_mlo;                              /* mlo related OUT    设备收包采样最大速率 */
	uword32  maxTxRate;                              /* mlo related OUT    设备发包采样最大速率 */
	uword32  maxTxRate_mlo;                              /* mlo related OUT    设备发包采样最大速率 */

	word8   staVendor[LAN_HOST_OPT_DATA_LEN];        /* OUT    设备归属厂商信息 */
	word8   aucOptData[LAN_HOST_OPT_DATA_LEN];
#define LAN_HOSTS_RADIO_TYPE_LEN (8)
	char radio_type[LAN_HOSTS_RADIO_TYPE_LEN];  /* mlo related */

	ulword64 sta_rx_pkt_fail;
	ulword64 sta_tx_pkt_fail;
	ulword64 sta_rx_pkt_retry;
	ulword64 sta_tx_pkt_retry;
	ulword64 pkt_total;
	ulword64 sta_rx_pkt_total;
	ulword64 sta_tx_pkt_total;
	ulword64 pkt_loss;

#define LAN_HOST_STATE_INFO_MASK_BIT0_PORT_NUM (0x01)
#define LAN_HOST_STATE_INFO_MASK_BIT1_INTERFACE_TYPE (0x02)
#define LAN_HOST_STATE_INFO_MASK_BIT2_ACTIVE (0x04)
#define LAN_HOST_STATE_INFO_MASK_BIT3_TYPE (0x08)
#define LAN_HOST_STATE_INFO_MASK_BIT4_ADDRESS_SOURCE (0x10)
#define LAN_HOST_STATE_INFO_MASK_BIT5_NAME (0x20)
#define LAN_HOST_STATE_INFO_MASK_BIT6_IP_ADDR (0x40)
#define LAN_HOST_STATE_INFO_MASK_BIT7_MAC_ADDR (0x80)
#define LAN_HOST_STATE_INFO_MASK_BIT8_LEASE_TIME_REMAIN (0x100)
#define LAN_HOST_STATE_INFO_MASK_BIT9_CONTROLSTATUS (0x200)
#define LAN_HOST_STATE_INFO_MASK_BIT10_CONNECTIONTYPE (0x400)
#define LAN_HOST_STATE_INFO_MASK_BIT11_PORT (0x800)
#define LAN_HOST_STATE_INFO_MASK_BIT12_LATESTTIME_ACTIVE (0x1000)
#define LAN_HOST_STATE_INFO_MASK_BIT13_LATESTTIME_INACTIVE (0x2000)
#define LAN_HOST_STATE_INFO_MASK_BIT14_ONLINETIME (0x4000)
#define LAN_HOST_STATE_INFO_MASK_BIT15_RXBYTES (0x8000)
#define LAN_HOST_STATE_INFO_MASK_BIT16_TXBYTES (0x10000)
#define LAN_HOST_STATE_INFO_MASK_BIT17_POWERLEVEL (0x20000)
#define LAN_HOST_STATE_INFO_MASK_BIT18_IP4_ADDR (0x40000)
#define LAN_HOST_STATE_INFO_MASK_BIT19_STA_RX_PKT_FAIL (0x80000)
#define LAN_HOST_STATE_INFO_MASK_BIT20_STA_TX_PKT_FAIL (0x100000)
#define LAN_HOST_STATE_INFO_MASK_BIT21_STA_RX_PKT_RETRY (0x200000)
#define LAN_HOST_STATE_INFO_MASK_BIT22_STA_TX_PKT_RETRY (0x400000)
#define LAN_HOST_STATE_INFO_MASK_BIT23_PKT_TOTAL (0x800000)
#define LAN_HOST_STATE_INFO_MASK_BIT24_STA_RX_PKT_TOTAL (0x1000000)
#define LAN_HOST_STATE_INFO_MASK_BIT25_STA_TX_PKT_TOTAL (0x2000000)
#define LAN_HOST_STATE_INFO_MASK_BIT26_PKT_LOSS (0x4000000)

#define LAN_HOST_STATE_INFO_MASK_ALL (0x7ffffff)
	uword32 ulBitmap;
} __PACK__ IgdLanHostsStateInfoTab;

/***************************LAN侧下挂设备动态IP地址信息表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define LAN_DHCP_HOSTS_TYPE_DEFAULT  (0)
#define LAN_DHCP_HOSTS_TYPE_COMPUTER (1)
#define LAN_DHCP_HOSTS_TYPE_HGW      (2)
#define LAN_DHCP_HOSTS_TYPE_STB      (3)
#define LAN_DHCP_HOSTS_TYPE_PHONE    (4)
#define LAN_DHCP_HOSTS_TYPE_CAMERA   (5)
	uword8 ucHostType;/*设备类型*/
	uword8 aucPad[CM_TWO_PADS];

#define LAN_DHCP_HOSTS_NAME_LEN (32)
	uword8 aucHostName[LAN_DHCP_HOSTS_NAME_LEN];/*设备名称*/
	uword8 aucIpAddr[CM_IP_ADDR_LEN];/*IP地址*/
	uword8 aucIpv6Addr[CM_IPV6_ADDR_LEN];/*IPv6地址*/
	uword8 aucMacAddress[CM_MAC_ADDR_LEN];/*Mac地址*/
	uword8 aucPad1[CM_TWO_PADS];
#define LAN_HOSTS_LEASTIME_REMAIN_INFINITE (-1)
	word64 lLeaseTimeRemaining;/*剩余地址租期*/
#define LAN_DHCP_HOST_UNDER_LAN1 (0)
#define LAN_DHCP_HOST_UNDER_LAN2 (1)
#define LAN_DHCP_HOST_UNDER_LAN3 (2)
#define LAN_DHCP_HOST_UNDER_LAN4 (3)
#define LAN_DHCP_HOST_UNDER_SSID1 (4)
#define LAN_DHCP_HOST_UNDER_SSID2 (5)
#define LAN_DHCP_HOST_UNDER_SSID3 (6)
#define LAN_DHCP_HOST_UNDER_SSID4 (7)
	uword8 ucLanPortNum;

#define LAN_DHCP_HOST_STATE_INFO_MASK_BIT0_HOST_TYPE (0x02)
#define LAN_DHCP_HOST_STATE_INFO_MASK_BIT1_IP_ADDR (0x04)
#define LAN_DHCP_HOST_STATE_INFO_MASK_BIT2_MAC_ADDR (0x08)
#define LAN_DHCP_HOST_STATE_INFO_MASK_BIT3_LEASE_TIME (0x10)
#define LAN_DHCP_HOST_STATE_INFO_MASK_BIT4_PORT_NUM (0x20)
#define LAN_DHCP_HOST_STATE_INFO_MASK_ALL (0x3f)
	uword32 ulBitmap;
} __PACK__ IgdLanDhcpHostsStateInfoTab;

/***************************LAN侧下挂机顶盒的数目*********************************/
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulSTBNum;
#define LAN_LAN_STB_NUMBER_MASK_BIT0_STB_NUM (0x01)
	uword32 ulBitmap;
} __PACK__ IgdLanStbNumTab;

#endif
