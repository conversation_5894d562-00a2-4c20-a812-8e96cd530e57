/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm global obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_NET_APP_H
#define HI_ODL_NET_APP_H
#include "hi_odl_basic_type.h"
/* IGD_APP_ALG_ATTR_TAB */
typedef struct {
	uword32 ulStateAndIndex;

#define ALG_H323_DISABLE (0)
#define ALG_H323_ENABLE (1)
	uword8 ucH323Enable;
#define ALG_SIP_DISABLE (0)
#define ALG_SIP_ENABLE (1)
	uword8 ucSipEnable;
#define ALG_RTSP_DISABLE (0)
#define ALG_RTSP_ENABLE (1)
	uword8 ucRtspEnable;
#define ALG_L2TP_DISABLE (0)
#define ALG_L2TP_ENABLE (1)
	uword8 ucL2tpEnable;

#define ALG_IPSEC_DISABLE (0)
#define ALG_IPSEC_ENABLE (1)
	uword8 ucIPsecEnable;
#define ALG_FTP_DISABLE (0)
#define ALG_FTP_ENABLE (1)
	uword8 ucFtpEnable;
#define ALG_PPTP_DISABLE (0)
#define ALG_PPTP_ENABLE (1)
	uword8 ucPptpEnable;
	uword8 ucPad;

#define ALG_ATTR_MASK_BIT0_H323_ENABLE (0x01)
#define ALG_ATTR_MASK_BIT1_SIP_ENABLE (0x02)
#define ALG_ATTR_MASK_BIT2_RTSP_ENABLE (0x04)
#define ALG_ATTR_MASK_BIT3_L2TP_ENABLE (0x08)
#define ALG_ATTR_MASK_BIT4_IPSEC_ENABLE (0x10)
#define ALG_ATTR_MASK_BIT5_FTP_ENABLE (0x20)
#define ALG_ATTR_MASK_BIT6_PPTP_ENABLE (0x40)
#define ALG_ATTR_MASK_ALL (0x7f)
	uword32 ulBitmap;
} __PACK__ IgdAppAlgAttrConfTab;

/*操作库函数声明*/
/***************************ALG*********************************/

/***************************DMZ*********************************/
/* IGD_APP_DMZ_ATTR_TAB */

typedef struct {
	uword32 ulStateAndIndex;

#define DMZ_DISABLE (0)
#define DMZ_ENABLE (1)
	uword8 ucDmzEnable;
	uword8 aucPad[CM_THREE_PADS];

	uword8 aucDmzHostIP[CM_IP_ADDR_LEN];
	word8 aucDmzHostWan[CM_WAN_CONNECTION_NAME_LEN];

#define DMZ_ATTR_MASK_BIT0_ENABLE (0x01)
#define DMZ_ATTR_MASK_BIT1_HOST_IP_ADDR (0x02)
#define DMZ_ATTR_MASK_BIT2_HOST_WAN_NAME (0x04)
#define DMZ_ATTR_MASK_ALL (0x07)
	uword32 ulBitmap;
} __PACK__ IgdAppDmzAttrConfTab;

/* IGD_APP_UPNP_ATTR_TAB */
typedef struct {
	uword32 ulStateAndIndex;

#define UPNP_DISABLE (0)
#define UPNP_ENABLE (1)
	uword8 ucUpnpEnable;
	uword8 aucPad[CM_THREE_PADS];

#define UPNP_ATTR_MASK_ALL (0x01)
	uword32 ulBitmap;
} __PACK__ IgdAppUpnpAttrConfTab;


/***************************IGMP*********************************/
/* IGD_APP_IGMP_ATTR_TAB */
typedef struct {
	uword32 ulStateAndIndex;

#define IGMP_DISABLE (0)
#define IGMP_ENABLE (1)
	uword8 ucIgmpEnable;/*默认值：false*/
#define IGMP_STATUS_NO_IPTV_CONNECTION (0)
#define IGMP_STATUS_IPTV_DISCONNECT (1)
#define IGMP_STATUS_IPTV_INVALID_MULTIVLAN (2)
#define IGMP_STATUS_IPTV_BUSSINESS_NOK (3)
#define IGMP_STATUS_IPTV_BUSSINESS_OK (4)
	uword8 ucIPTVStatus;

#define IGMP_NOSTB_DISABLE (0)
#define IGMP_NOSTB_ENABLE (1)
	uword8 no_stb_enable;
	uword8 aucPad;

#define IGMP_SNOOPING_DISABLE (0)
#define IGMP_SNOOPING_ENABLE (1)
	uword8 ucIgmpSnoopingEnable;
#define IGMP_PROXY_DISABLE (0)
#define IGMP_PROXY_ENABLE (1)
	uword8 ucIgmpProxyEnable;
#define MLD_SNOOPING_DISABLE (0)
#define MLD_SNOOPING_ENABLE (1)
	uword8 ucMldSnoopingEnable;
#define MLD_PROXY_DISABLE (0)
#define MLD_PROXY_ENABLE (1)
	uword8 ucMldProxyEnable;
	word8 aucCurWanName[CM_WAN_CONNECTION_NAME_LEN];
	word8 aucStbAddr[CM_STB_ADDR_LEN];
#define TRANSLATE_DISABLE (0)
#define TRANSLATE_ENABLE (1)
	uword8 aucTranslateEnable;
#define IGMP_LINE_STATUS_NO_SINGLECAST_NO_MULTICAST (0)
#define IGMP_LINE_STATUS_NO_SINGLECAST_MULTICAST (1)
#define IGMP_LINE_STATUS_SINGLECAST_NO_MULTICAST (2)
#define IGMP_LINE_STATUS_SINGLECAST_MULTICAST (3)
	uword8 ucIPTVLineStatus;
#define IGMP_OTT_CONFIG_DISABLE 0
#define IGMP_OTT_CONFIG_ENABLE 1
	uint8_t ott_config_enable;
#define IGMP_LOCAL_DHCP_AUTHMODE_NO_VERIFY (0)
#define IGMP_LOCAL_DHCP_AUTHMODE_VERIFY_ACCOUNT (1)
#define IGMP_LOCAL_DHCP_AUTHMODE_VERIFY_ALL (2)
#define IGMP_LOCAL_DHCP_AUTHMODE_VERIFY_BYWAN (3)
	uint8_t local_dhcp_authmode;
	uword32 ulMcAgingTime;
	char ott_account[CM_USERNAME_LEN];
	char ott_password[CM_PASSWORD_LEN];

#define IGMP_ATTR_MASK_BIT0_ENABLE (0x01)
#define IGMP_ATTR_MASK_BIT1_IGMP_SNOOPING_ENABLE (0x02)
#define IGMP_ATTR_MASK_BIT2_IGMP_PROXY_ENABLE (0x04)
#define IGMP_ATTR_MASK_BIT3_MLD_SNOOPING_ENABLE (0x08)
#define IGMP_ATTR_MASK_BIT4_MLD_PROXY_ENABLE (0x10)
#define IGMP_ATTR_MASK_BIT5_WAN_CONNECTION_NAME (0x20)
#define IGMP_ATTR_MASK_BIT6_STB_ADDR (0x40)
#define IGMP_ATTR_MASK_BIT7_IPTVSTATUS (0x80)
#define IGMP_ATTR_MASK_BIT8_TRANSLATE_ENABLE (0x100)
#define IGMP_ATTR_MASK_BIT8_IPTV_LINE_STATUS (0x200)
#define IGMP_ATTR_MASK_BIT8_MC_AGING_TIME (0x400)
#define IGMP_ATTR_MASK_BIT9_OTT_CONFIG_ENABLE (0x800)
#define IGMP_ATTR_MASK_BIT10_LOCAL_DHCP_AUTHMODE (0x1000)
#define IGMP_ATTR_MASK_BIT11_OTT_ACCOUNT (0x2000)
#define IGMP_ATTR_MASK_BIT12_OTT_PASSWORD (0x4000)
#define IGMP_ATTR_MASK_BIT13_NO_STB_ENABLE (0x8000)
#define IGMP_ATTR_MASK_ALL (0xffff)
	uword32 ulBitmap;
} __PACK__ IgdAppIgmpAttrConfTab;

/*操作库函数声明*/

/***************************IGMP*********************************/

/***************************PORT MAC*********************************/
/*表相关宏*/

typedef struct {
	uword8         aucMac[6];
	uword8        aucResv[2];
} __PACK__ IgdAppMacTab;

typedef struct {
	uword32                        ulStateAndIndex;
	uword32                        ulPort;
	IgdAppMacTab                   stInfo[32];
	uword32                        ulCnt;
} __PACK__ IgdAppPortMacTab;

/***************************PORT MAC*********************************/
/***************************静态路由*********************************/
/*表相关宏*/

#define IGD_APP_ROUTE_RECORD_NUM (16)

typedef struct {
	uword32 ulStateAndIndex;

	uword8 ulIndex;
#define STATIC_ROUTE_SELECT_IP_MODE_V4 (1)
#define STATIC_ROUTE_SELECT_IP_MODE_V6 (2)
	uword8 ucIpMode;
	uword8 aucPad[CM_TWO_PADS];

	uword8 aucIpv4Addr[CM_IP_ADDR_LEN];/*IPv4地址*/
	uword8 aucIpv6Addr[CM_IPV6_ADDR_LEN];/*IPv6地址*/
	uword8 aucSubnetMask[CM_IP_ADDR_LEN]; /*V4子网地址,V6地址前缀长度*/
	uword8 aucGetway[CM_IPV6_ADDR_LEN]; /*网关地址,V4/V6*/
	word8 aucWanName[CM_WAN_CONNECTION_NAME_LEN];/*WAN连接名称*/

#define STATIC_ROUTE_ATTR_MASK_BIT0_IP_ADDR (0x01)
#define STATIC_ROUTE_ATTR_MASK_BIT1_SUBNET_MASK (0x02)
#define STATIC_ROUTE_ATTR_MASK_BIT2_GETWAY (0x04)
#define STATIC_ROUTE_ATTR_MASK_BIT3_WAN_NAME (0x08)
#define STATIC_ROUTE_ATTR_MASK_BIT4_IP_MODE (0x10)
#define STATIC_ROUTE_ATTR_MASK_BIT5_ENABLE (0x20)

#define STATIC_ROUTE_ATTR_MASK_ALL (0x1f)
	uword32 ulBitmap;
} __PACK__ IgdAppRouteAttrConfTab;

/*操作库函数声明*/

/***************************静态路由*********************************/

/***************************时间管理*********************************/
/* IGD_APP_TIME_ATTR_TAB */

typedef struct {
	uword32 ulStateAndIndex;

#define NTP_SERVICE_DISABLE (0)
#define NTP_SERVICE_ENABLE (1)
	uword8 ucNTPEnable;/*使能NTP，默认：使能*/
#define NTP_SERVER_TYPE_INTERNET (0)
#define NTP_SERVER_TYPE_VOICE (1)
#define NTP_SERVER_TYPE_TR069 (2)
#define NTP_SERVER_TYPE_OTHER (3)
	uword8 ucNTPServerType; /* 默认值：0 使用上网通道*/
	uword8 aucPad[CM_TWO_PADS];

#define NTP_SYNC_INTERVAL_AUTO (0)
	uword32 ulNTPInterval;/*同步时间间隔：默认：0 自动同步*/
#define NTP_SERVER_ADDR_LEN (32)
	word8 aucNTPServer1[NTP_SERVER_ADDR_LEN];
	word8 aucNTPServer2[NTP_SERVER_ADDR_LEN];
	word8 aucNTPServer3[NTP_SERVER_ADDR_LEN];
	word8 aucNTPServer4[NTP_SERVER_ADDR_LEN];
	word8 aucNTPServer5[NTP_SERVER_ADDR_LEN];
#define TIME_ZONE_LEN (8)
	word32 auclocalTimeZone;
#define TIME_ZONE_NAME_LEN (64)
	word8 aucLocalTimeZoneName[TIME_ZONE_NAME_LEN];

#define NTP_SUPPORT_STATUS_DISABLED (0)
#define NTP_SUPPORT_STATUS_UNSYNCHRONIZED (1) /*CPE的绝对时间未设置*/
#define NTP_SUPPORT_STATUS_SYNCHRONIZED (2)	/*CPE已获得精确的绝对时间，当前时间是准确的*/
#define NTP_SUPPORT_STATUS_ERROR_FAILEDTOSYNCHRONIZED (3)/*CPE获取精确绝对时间时出错，当前时间不真确的*/
#define NTP_SUPPORT_STATUS_ERROR (4)/*可由CPE用来定义本地出错条件*/
	uword8 ucstatus;
	uword8 aucPad1[CM_THREE_PADS];

#define NTP_ATTR_MASK_BIT0_ENABLE (0x01)
#define NTP_ATTR_MASK_BIT1_SERVER_TYPE (0x02)
#define NTP_ATTR_MASK_BIT2_NTP_INTERVAL (0x04)
#define NTP_ATTR_MASK_BIT3_NTP_SERVER1 (0x08)
#define NTP_ATTR_MASK_BIT4_NTP_SERVER2 (0x10)
#define NTP_ATTR_MASK_BIT5_NTP_SERVER3 (0x20)
#define NTP_ATTR_MASK_BIT6_NTP_SERVER4 (0x40)
#define NTP_ATTR_MASK_BIT7_NTP_SERVER5 (0x80)
#define NTP_ATTR_MASK_BIT8_LOCAL_TIME_ZOME (0x100)
#define NTP_ATTR_MASK_BIT9_LOCAL_TIME_ZONE_NAME (0x200)
#define NTP_ATTR_MASK_BIT10_STATUS (0x400)

#define NTP_ATTR_MASK_ALL (0x7ff)
	uword32 ulBitmap;
} __PACK__ IgdAppNtpAttrConfTab;

/*操作库函数声明*/

/***************************时间管理*********************************/

/***************************接入服务管理*********************************/
/* IGD_APP_SERVICE_MANAGE_ATTR_TAB */

typedef struct {
	uword32 ulStateAndIndex;

#define FTP_SERVICE_DISABLE (0)
#define FTP_SERVICE_ENABLE (3)
#define FTP_SERVICE_LOCAL_ENABLE (1)
#define FTP_SERVICE_REMOTE_ENABLE (2)
	uword8 ucFtpEnable; /* 默认值：关闭 */
#define TELNET_SERVICE_DISABLE (0)

#define TELNET_SERVICE_ENABLE (3)
#define TELNET_SERVICE_LOCAL_ENABLE (1)
#define TELNET_SERVICE_REMOTE_ENABLE (2)

	uword8 ucTelnetEnable; /*默认值：关闭 */
	uword8 ucTelnetWANEnable; /*默认值：关闭 */

/* fyy */
#define SSH_SERVICE_DISABLE (0)
#define SSH_SERVICE_ENABLE (3)
#define SSH_SERVICE_LOCAL_ENABLE (1)
#define SSH_SERVICE_REMOTE_ENABLE (2)
	uword8 ucSshEnable; /* 默认值：关闭 */
#define TFTP_SERVICE_DISABLE (0)
#define TFTP_SERVICE_ENABLE (3)
#define TFTP_SERVICE_LOCAL_ENABLE (1)
#define TFTP_SERVICE_REMOTE_ENABLE (2)
	uword8 ucTftpEnable; /*默认值：关闭 */

#define HTTP_SERVICE_DISABLE (0)
#define HTTP_SERVICE_ENABLE_LOCAL (1)
#define HTTP_SERVICE_ENABLE_REMOTE (0x2)
#define HTTP_SERVICE_ENABLE_BOTH (0x3)
	uword32 ulHTTPEnable;  /*默认值：关闭 */

#define NETWORK_PROTOCOL_PORT_NUM_MIN (0)
#define NETWORK_PROTOCOL_PORT_NUM_MAX (65535)
	uword32 ulFtpPort; /*默认值：21 */
	word8 aucFtpUsername[CM_USERNAME_LEN];
	word8 aucFtpPassword[CM_PASSWORD_LEN];
	uword32 ulTelnetPort; /*默认值：23 */
	word8 aucTelnetUsername[CM_USERNAME_LEN];
	word8 aucTelnetPassword[CM_PASSWORD_LEN];
	uword32 ulSshPort; /*默认值：22 */
	word8 aucSshUsername[CM_USERNAME_LEN];
	word8 aucSshPassword[CM_PASSWORD_LEN];
	uword32	ulTftpPort; /*默认值：69 */
	word8 aucTftpUsername[CM_USERNAME_LEN];
	word8 aucTftpPassword[CM_PASSWORD_LEN];
	word8 aucFtpUserRight[CM_URL_LEN]; /*该账号的管理权限*/
	word8 aucFtpPath[CM_URL_LEN];	/*Ftp的路径*/

	uword32 ulTelnetWANPort; /*默认值: 23*/
#define FTP_ANONYMOUS_DISABLE (0)
#define FTP_ANONYMOUS_ENABLE (1)
	uword8 ucFtpAnonymous;
	uword8 ucSerialEnable;
	/* fyy */
    word8 acTelnetWanIpv4Addr[CM_IP_ADDR_STRING_LEN];
    word8 acTelnetWanIpv4Mask[CM_IP_ADDR_STRING_LEN];
    word8 acFtpWanIpv4Addr[CM_IP_ADDR_STRING_LEN];
    word8 acFtpWanIpv4Mask[CM_IP_ADDR_STRING_LEN];
    word8 acIcmpWanIpv4Addr[CM_IP_ADDR_STRING_LEN];
    word8 acIcmpWanIpv4Mask[CM_IP_ADDR_STRING_LEN];
#define ICMP_SERVICE_DISABLE (0)
#define ICMP_SERVICE_REMOTE_ENABLE (2)
    uword8 ucIcmpWANEnable; /*默认值：关闭 */
    word8 acHttpWanIpv4Addr[CM_IP_ADDR_STRING_LEN];
    word8 acHttpWanIpv4Mask[CM_IP_ADDR_STRING_LEN];
    word8 acSshWanIpv4Addr[CM_IP_ADDR_STRING_LEN];
    word8 acSshWanIpv4Mask[CM_IP_ADDR_STRING_LEN];
    word8 acTftpWanIpv4Addr[CM_IP_ADDR_STRING_LEN];
    word8 acTftpWanIpv4Mask[CM_IP_ADDR_STRING_LEN];
#define HTTPS_SERVICE_DISABLE (0)
#define HTTPS_SERVICE_ENABLE_LOCAL (1)
#define HTTPS_SERVICE_ENABLE_REMOTE (0x2)
#define HTTPS_SERVICE_ENABLE_BOTH (0x3)
    uword8 ucHttpsEnable;  /*默认值：关闭 */
    uword32 ulHttpPort;
    word8 acHttpsWanIpv4Addr[CM_IP_ADDR_STRING_LEN];
    word8 acHttpsWanIpv4Mask[CM_IP_ADDR_STRING_LEN];
    uword32 ulHttpsPort; /*默认值：443 */
	uword8 aucPad1[CM_TWO_PADS];


#define SERVICE_MANAGE_ATTR_MASK_BIT0_FTP_ENABLE (0x01)
#define SERVICE_MANAGE_ATTR_MASK_BIT1_TELNET_ENABLE (0x02)
#define SERVICE_MANAGE_ATTR_MASK_BIT2_FTP_PORT (0x04)
#define SERVICE_MANAGE_ATTR_MASK_BIT3_TELNET_PORT (0x08)
#define SERVICE_MANAGE_ATTR_MASK_BIT4_FTP_USERRIGHT (0x10)
#define SERVICE_MANAGE_ATTR_MASK_BIT5_FTP_PATH (0x20)
#define SERVICE_MANAGE_ATTR_MASK_BIT6_TELNET_WANPORT (0x40)
#define SERVICE_MANAGE_ATTR_MASK_BIT7_FTP_ANONYMOUS  (0x80)
#define SERVICE_MANAGE_ATTR_MASK_BIT8_HTTP_ENABLE (0x100)
#define SERVICE_MANAGE_ATTR_MASK_BIT10_SSH_ENABLE (0x400)
#define SERVICE_MANAGE_ATTR_MASK_BIT11_TFTP_ENABLE (0x800)
#define SERVICE_MANAGE_ATTR_MASK_BIT12_SSH_PORT (0x1000)
#define SERVICE_MANAGE_ATTR_MASK_BIT13_SSH_USERNAME (0x2000)
#define SERVICE_MANAGE_ATTR_MASK_BIT14_SSH_PASSWORD (0x4000)
#define SERVICE_MANAGE_ATTR_MASK_BIT15_TFTP_PORT (0x8000)
#define SERVICE_MANAGE_ATTR_MASK_BIT16_TFTP_USERNAME (0x10000)
#define SERVICE_MANAGE_ATTR_MASK_BIT17_TFTP_PASSWORD (0x20000)
#define SERVICE_MANAGE_ATTR_MASK_BIT18_TELNET_WAN_ENABLE (0x40000)
#define SERVICE_MANAGE_ATTR_MASK_BIT19_SERIAL_ENABLE (0x80000)
#define SERVICE_MANAGE_ATTR_MASK_TELNET_USERNAME (0x100000)
#define SERVICE_MANAGE_ATTR_MASK_TELNET_PASSWORD (0x200000)
/* fyy */
#define SERVICE_MANAGE_ATTR_MASK_BIT22_TELNET_WAN_IPV4_ADDR (0x400000)
#define SERVICE_MANAGE_ATTR_MASK_BIT23_TELNET_WAN_IPV4_MASK (0x800000)
#define SERVICE_MANAGE_ATTR_MASK_BIT24_FTP_WAN_IPV4_ADDR (0x1000000)
#define SERVICE_MANAGE_ATTR_MASK_BIT25_FTP_WAN_IPV4_MASK (0x2000000)
#define SERVICE_MANAGE_ATTR_MASK_BIT26_ICMP_WAN_IPV4_ADDR (0x4000000)
#define SERVICE_MANAGE_ATTR_MASK_BIT27_ICMP_WAN_IPV4_MASK (0x8000000)
#define SERVICE_MANAGE_ATTR_MASK_BIT28_ICMP_WAN_ENABLE (0x10000000)
#define SERVICE_MANAGE_ATTR_MASK_BIT29_HTTP_WAN_IPV4_ADDR (0x20000000)
#define SERVICE_MANAGE_ATTR_MASK_BIT30_HTTP_WAN_IPV4_MASK (0x40000000)
#define SERVICE_MANAGE_ATTR_MASK_ALL (0x7fffffff)
	uword32 ulBitmap;
#define SERVICE_MANAGE_ATTR_MASK1_BIT0_SSH_WAN_IPV4_ADDR (0x1)
#define SERVICE_MANAGE_ATTR_MASK1_BIT1_SSH_WAN_IPV4_MASK (0x2)
#define SERVICE_MANAGE_ATTR_MASK1_BIT2_TFTP_WAN_IPV4_ADDR (0x4)
#define SERVICE_MANAGE_ATTR_MASK1_BIT3_TFTP_WAN_IPV4_MASK (0x8)
#define SERVICE_MANAGE_ATTR_MASK1_BIT4_HTTPS_ENABLE (0x10)
#define SERVICE_MANAGE_ATTR_MASK1_BIT5_HTTPS_WAN_IPV4_ADDR (0x20)
#define SERVICE_MANAGE_ATTR_MASK1_BIT6_HTTPS_WAN_IPV4_MASK (0x40)
#define SERVICE_MANAGE_ATTR_MASK1_BIT7_HTTPS_PORT (0x80)
#define SERVICE_MANAGE_ATTR_MASK1_BIT8_HTTP_PORT (0x100)
    uword32 ulBitmap1;
#define SERVICE_MANAGE_ATTR_MASK1_ALL (0xfff)
} __PACK__ IgdAppServiceManageAttrConfTab;



/***************************组播VLAN*********************************/
/* IGD_APP_IGMP_VLAN_INFO_TAB */
typedef struct {
	uword32 ulStateAndIndex;

	word8 aucWanName[CM_WAN_CONNECTION_NAME_LEN];
	uword32 ulMulticastVlanID;

#define IGMP_VLAN_ATTR_MASK_BIT0_WAN_CONNECTION_NAME (0x01)
#define IGMP_VLAN_ATTR_MASK_BIT1_MULTICAST_VLAN (0x02)
#define IGMP_VLAN_ATTR_MASK_ALL (0x03)
	uword32 ulBitmap;
} __PACK__ IgdAppIgmpMulticastVlanInfoTab;

/* IGD_IP_LAYER3_FORWARDING_INFO_TAB */
typedef struct {
	uword32 ulStateAndIndex;
#define IP_LAYER3_FORWARDING_INTERFACE_LEN (256)
	word8 aucDefaultConnectionService[IP_LAYER3_FORWARDING_INTERFACE_LEN];
	char  cur_default_connection[CM_WAN_CONNECTION_NAME_LEN];
#define IP_LAYER3_FORWARDING_MASK_ALL (0x01)
	uword32 ulBitmap;
} __PACK__ IgdCmIpLayer3ForwardingInfoTab;


/********************************IPV4静态路由参数*******************************************/
/* IGD_APP_V4_LAYER3_ROUTE_TAB */
#define IGD_APP_V4_LAYER3_ROUTE_RECORD_NUM (256)

typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulIndex;
	uword8 aucPad;
#define IPV4_LAYER3_ROUTE_DISABLE (0)
#define IPV4_LAYER3_ROUTE_ENABLE (1)
	uword8 ucEnable;	/*是否启用路由条目。缺省启用*/
#define	IPV4_LAYER3_ROUTE_FWD_DISABLE  (0) /**< Disabled */
#define	IPV4_LAYER3_ROUTE_FWD_ENABLE  (1)	/**< Enabled */
#define	IPV4_LAYER3_ROUTE_FWD_ERROR  (2)		/**< Error */
	uword8 ucStatus;
#define	IPV4_LAYER3_ROUTE_TYPE_DEFAULT  (0)		/**< 默认路由*/
#define	IPV4_LAYER3_ROUTE_TYPE_NETWORK (1)		/**< 网络路由*/
#define	IPV4_LAYER3_ROUTE_TYPE_HOST (2)			/**< 主机路由*/
	uword8 ucType;

	uword8 aucDestIPAddress[CM_IP_ADDR_LEN]; 	/*IPv4地址*/
	uword8 aucDestSubnetMask[CM_IP_ADDR_LEN];	/*V4子网地址*/
	uword8 aucSourceIPAddress[CM_IP_ADDR_LEN];		/*IPv4地址*/
	uword8 aucSourceSubnetMask[CM_IP_ADDR_LEN];	/*V4子网地址*/
	uword8 aucGatewayIPAddress[CM_IP_ADDR_LEN];   /*网关地址,V4*/
#define IPV4_LAYER3_ROUTE_INTERFACE_LEN (64)
	word8 aucInterface[IPV4_LAYER3_ROUTE_INTERFACE_LEN];

	uword32 ulForwardingPolicy;
	uword32 ulForwardingMetric;
#define IPV4_LAYER3_ROUTE_MTU_MIN (1)
#define IPV4_LAYER3_ROUTE_MTU_MAX (1540)
	uword32 ulMTU;
	uint8_t  ucstaticroute;

#define IPV4_LAYER3_ROUTE_MASK_BIT0_ENABLE (0x01)
#define IPV4_LAYER3_ROUTE_MASK_BIT1_TYPE (0x02)
#define IPV4_LAYER3_ROUTE_MASK_BIT2_DESTIPADDR (0x04)
#define IPV4_LAYER3_ROUTE_MASK_BIT3_DESTMASK (0x08)
#define IPV4_LAYER3_ROUTE_MASK_BIT4_SRCIPADDR (0x10)
#define IPV4_LAYER3_ROUTE_MASK_BIT5_SRCIPMASK (0x20)
#define IPV4_LAYER3_ROUTE_MASK_BIT6_GATEWAY (0x40)
#define IPV4_LAYER3_ROUTE_MASK_BIT7_INTERFACE (0x80)
#define IPV4_LAYER3_ROUTE_MASK_BIT8_FWD_POLICY (0x100)
#define IPV4_LAYER3_ROUTE_MASK_BIT9_FWD_METRIC (0x200)
#define IPV4_LAYER3_ROUTE_MASK_BIT10_MTU (0x400)
#define IPV4_LAYER3_ROUTE_MASK_ALL (0x7ff)

	uword32 ulBitmap;
} __PACK__ IgdCmAppv4Layer3RouteTab;

/********************************IPV6静态路由表*******************************************/
/* IGD_APP_V6_LAYER3_ROUTE_TAB */

#define IGD_APP_V6_LAYER3_ROUTE_RECORD_NUM (256)

typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulIndex;
	uword8 aucPad;
#define IPV6_LAYER3_ROUTE_DISABLE (0)
#define IPV6_LAYER3_ROUTE_ENABLE (1)
	uword8 ucIPv6Enable;	/**是否启用路由条目;缺省为启用*/

#define	IPV6_LAYER3_ROUTE_FWD_DISABLE  (0)      /* Disabled */
#define	IPV6_LAYER3_ROUTE_FWD_ENABLE  (1)	/* Enabled */
#define	IPV6_LAYER3_ROUTE_FWD_ERROR  (2)	/* Error */
	uint8_t status;
#define	IPV6_LAYER3_ROUTE_TYPE_DEFAULT  (0)	/* default route */
#define	IPV6_LAYER3_ROUTE_TYPE_NETWORK (1)	/* network route */
#define	IPV6_LAYER3_ROUTE_TYPE_HOST (2)		/* host route */
	uint8_t type;

#define IPV6_LAYER3_ROUTE_LEN (64)
	word8 aucAlias[IPV6_LAYER3_ROUTE_LEN];	/*别名，用来标识路由*/
	word8 aucDestIPPrefix[CM_IPV6_ADDR_LEN];	/*ipv6地址*/
	uword32 ulSubnetMask; /*V6地址前缀长度*/
	word8 aucNextHop[CM_IPV6_ADDR_LEN];		/*下一跳地,网关*/
#define IPV6_LAYER3_ROUTE_INTERFACE_LEN (64)
	word8 aucInterface[IPV6_LAYER3_ROUTE_INTERFACE_LEN];	/*WAN 接口（TR069节点全路径）*/

#define IPV6_LAYER3_ROUTE_MASK_BIT0_ENABLE (0x01)
#define IPV6_LAYER3_ROUTE_MASK_BIT1_ALIAS (0x02)
#define IPV6_LAYER3_ROUTE_MASK_BIT2_DESTIPPREFIX (0x04)
#define IPV6_LAYER3_ROUTE_MASK_BIT3_NEXTHOP (0x08)
#define IPV6_LAYER3_ROUTE_MASK_BIT4_INTERFACE (0x010)
#define IPV6_LAYER3_ROUTE_MASK_BIT5_SUBNTMASK (0x020)
#define IPV6_LAYER3_ROUTE_MASK_TYPE           (0x040)
#define IPV6_LAYER3_ROUTE_MASK_ALL (0x7f)

	uword32 ulBitmap;
} __PACK__ IgdCmAppv6Layer3RouteTab;


/********************************IPV6静态路由表*******************************************/

/********************************密码复杂度管理表*******************************************/
/* IGD_APP_PASSWORD_COMPLEXITY_TAB */
typedef struct {
	uword32 ulStateAndIndex;
	uword8 ucPasswdMinLength;
#define PASSWORD_COMPLEXITY_LOWERCASE (0x01)
#define PASSWORD_COMPLEXITY_UPPERCASE (0x02)
#define PASSWORD_COMPLEXITY_NUMBER    (0x04)
#define PASSWORD_COMPLEXITY_SPECIAL   (0x08)
	uword8 ucChecktype;
#define PASSWORD_COMPLEXITY_CHECKDIFF_DISABLE  	(0)
#define PASSWORD_COMPLEXITY_CHECKDIFF_ENABLE 	(1)
	uword8 ucDiffFromAccount;
#define PASSWORD_COMPLEXITY_CHECK_DISABLE  	(0)
#define PASSWORD_COMPLEXITY_CHECK_ENABLE 	(1)
	uword8 ucCheckEnable;

#define PASSWORD_COMPLEXITY_MASK_BIT0_MINLENGTH 		(0x01)
#define PASSWORD_COMPLEXITY_MASK_BIT1_CHECKTYPE 		(0x02)
#define PASSWORD_COMPLEXITY_MASK_BIT2_DIFFFROMACCOUNT 	(0x04)
#define PASSWORD_COMPLEXITY_MASK_BIT3_CHECKENABLE	 	(0x08)
#define PASSWORD_COMPLEXITY_MASK_ALL (0xf)

	uword32 ulBitmap;
} __PACK__ IgdCmAppPasswordComplexityTab;

/********************************密码复杂度管理表*******************************************/

/********************************telnet账户管理表*******************************************/
/* IGD_APP_TELNET_ACCOUNT_TAB */

#define IGD_APP_TELNET_ACCOUNT_RECORD_NUM (9)
#define IGD_APP_TELNET_ACCOUNT_PWD_VERLEN  (8)

typedef struct {
	uword32 ulStateAndIndex;
	word8 aucUsername[CM_USERNAME_LEN];
	word8 aucPassword[CM_PASSWORD_LEN];
#define TELNET_ACCOUNT_TELNET_TYPE_LAN (1)
#define TELNET_ACCOUNT_TELNET_TYPE_WAN (2)
	uword8 ucTelnettype;
	uword8 ucAccountIndex;
	uword8 aucPad[CM_TWO_PADS];

#define TELNET_ACCOUNT_MASK_BIT0_USERNAME (0x01)
#define TELNET_ACCOUNT_MASK_BIT1_PASSWORD (0x02)
#define TELNET_ACCOUNT_MASK_BIT2_TELNET_TYPE (0x04)
#define TELNET_ACCOUNT_MASK_BIT3_ACCOUNT_INDEX (0x08)
#define TELNET_ACCOUNT_MASK_ALL (0xf)

	uword32 ulBitmap;
} __PACK__ IgdCmAppTelnetAccountTab;

/********************************telnet账户管理表*******************************************/

/********************************ftp账户管理表*******************************************/
/* IGD_APP_FTP_ACCOUNT_TAB */

#define IGD_APP_FTP_ACCOUNT_RECORD_NUM (9)

typedef struct {
	uword32 ulStateAndIndex;
	word8 aucFtpUsername[CM_USERNAME_LEN];
	word8 aucFtpPassword[CM_PASSWORD_LEN];
	uword8 ucFtpAccountIndex;
	uword8 aucPad[CM_THREE_PADS];

#define FTP_ACCOUNT_MASK_BIT0_USERNAME (0x01)
#define FTP_ACCOUNT_MASK_BIT1_PASSWORD (0x02)
#define FTP_ACCOUNT_MASK_BIT2_ACCOUNT_INDEX (0x04)
#define FTP_ACCOUNT_MASK_ALL (0x7)

	uword32 ulBitmap;
} __PACK__ IgdCmAppFtpAccountTab;


/********************************ftp账户管理表*******************************************/
/******************************** VPN 相关配置*********************************************/
/* IGD_APP_VPN_ATTACH_TAB */
#define VPN_USER_ID_LENGTH (16)
#define VPN_TUNNEL_NAME_LENGTH (32)
#define IGD_WAN_NAME_LENGTH (64)
#define IGD_APP_VPN_CFG_NUM (6)
#define IGD_APP_VPN_ATTACH_NUM (6)
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulVPNTunlIndex;
#define VPN_TYPE_L2TP (0)
#define VPN_TYPE_PPTP (1)
	uword8 ucVPNType;
	uword8 ucVPNEnable ; /*1使能; 0 去使能，保留配置且关闭VPN通道*/
#define VPN_ACCOUNT_MODE_RANDOM (1)
#define VPN_ACCOUNT_MODE_STEADY  (0)
	uword8 ucVPNMode ;
	uword8 ucVPNPriority ; /*取值0-7，值越小优先级越高*/

	word8 aucVPNUserId[VPN_USER_ID_LENGTH]
	; /*由平台分配，用于区分VPN业务的提供者，标识VPN通道*/

	word8 aucVPNTunnelName[VPN_TUNNEL_NAME_LENGTH];
	char wan_name[IGD_WAN_NAME_LENGTH];

	uword32 ulVPNIdleTime;
#define VPN_ACCOUNT_PROXY_LEN (64)
	word8 aucVPNAccountProxy[VPN_ACCOUNT_PROXY_LEN]; /*获取动态账号密码的地址*/
#define VPN_ADDR_LEN (64)
	word8 aucVPNAddress[VPN_ADDR_LEN];
#define VPN_ACCOUNT_LEN (64)
	word8 aucVPNAccountName[VPN_ACCOUNT_LEN] ;
#define VPN_ACCOUNT_PWD_LEN (260)
	word8  aucVPNPassWord[VPN_ACCOUNT_PWD_LEN] ;
	uword32 ulVPNPort ;
	uword8 ucL2TPOperate; /* 0：IPSet 之间或的关系; 1：IPSet 之间与的关系 */
	uword8 aucPad[CM_THREE_PADS];
#define VPN_L2TP_SET_NAME_MAX_NUM (8)
#define VPN_L2TP_SET_NAME_LEN (64)
	char aucL2TPSetName[VPN_L2TP_SET_NAME_MAX_NUM * VPN_L2TP_SET_NAME_LEN]; /* IPSet 名称数组 */
#define VPN_MASK_BIT0_TYPE  (0x01)
#define VPN_MASK_BIT1_ENABLE (0x02)
#define VPN_MASK_BIT2_MODE  (0x04)
#define VPN_MASK_BIT3_PRIORITY  (0x08)
#define VPN_MASK_BIT4_USERID  (0x10)
#define VPN_MASK_BIT5_TUNNEL_NAME  (0x20)
#define VPN_MASK_BIT6_IDLE_TIME  (0x40)
#define VPN_MASK_BIT7_ACCOUNT_PROXY  (0x80)
#define VPN_MASK_BIT8_ADDRESS  (0x100)
#define VPN_MASK_BIT9_ACCOUNT_NAME  (0x200)
#define VPN_MASK_BIT10_ACCOUNT_PASSWORD  (0x400)
#define VPN_MASK_BIT11_PORT  (0x800)
#define VPN_MASK_BIT12_L2TP_OPREATE   (0x1000)
#define VPN_MASK_BIT13_L2TP_SET_NAME  (0x2000)
#define VPN_MASK_BIT_WAN_NAME         (0x4000)
#define VPN_MASK_BIT_ALL         (0xffffff)
	uword32 ulBitmap;
} __PACK__ IgdCmAppVPNAttrCfgTab;

#define VPN_ATTACH_CONTEXT_LEN (1024)
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulAttachIndex;
	word8 aucVPNTunnelName[VPN_TUNNEL_NAME_LENGTH];

#define VPN_ATTACH_MODE_DESTINATION (1)
#define VPN_ATTACH_MODE_SOURCE_MAC (2)
	uword8 ucVPNAttachMode ;  /*关联模型（1：按目的地址；2：按源MAC地址）*/
	uword8 aucPad[3];
	word8 aucAttachContext[VPN_ATTACH_CONTEXT_LEN] ;  /*关联域名*/
	uword32 ulVPNTunlIndex; /*属于那个VPN通道，跟tunnel name一样，移动不指定tunnel name所以就用index标识该通道*/

#define VPN_ATTACH_MASK_BIT1_TUNNEL_NAME (0x01)
#define VPN_ATTACH_MASK_BIT2_MODE  (0x02)
#define VPN_ATTACH_MASK_BIT3_CONTEXT (0x04)
#define VPN_ATTACH_MASK_BIT4_VPNINDEX  (0x8)
	uword32 ulBitmap;
} __PACK__ IgdCmAppVPNAttachCfgTab;

#define VPN_HITIP_LIST_CONTEXT_LEN 512
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulVPNIndex;
	uword32 ulVPNAttachIndex;
	uword32 ulVPNStatus;   /*VPN的状态*/
	uword8  aucHitIPlist[VPN_HITIP_LIST_CONTEXT_LEN]
	;  /*命中的IP列表 或者 关联统计IP/DOMAIN*/
	uword32 ulCount;
#define VPN_STATE_MASK_BIT1_STATUS (0x01)
#define VPN_STATE_MASK_BIT2_IPLIST (0x02)
#define VPN_STATE_MASK_BIT3_STATS (0x04)

	uword32 ulBitmap;
} __PACK__ IgdCmAppVPNAttrStateCfgTab;

/* */
#define IGD_IGMP_DEVICE_AUTH_ATTR_MAX 1
typedef struct {
	uint32_t state_and_index;
	uint8_t  enable;
#define IGMP_DEVICE_AUTHENTICATION_MODE_BLACKLIST 0
#define IGMP_DEVICE_AUTHENTICATION_MODE_WHITELIST 1
	uint8_t mode;
#define IGMP_DEVICE_AUTHENTICATION_INFO_IP  0
#define IGMP_DEVICE_AUTHENTICATION_INFO_MAC 1
	uint8_t authenticationinfo;
	uint8_t rsv1;
#define IGMP_DEVICE_AUTHENTICATION_DEVICEINFO_STRING_LEN         (256)
	char deviceinfo[IGMP_DEVICE_AUTHENTICATION_DEVICEINFO_STRING_LEN];
#define IGMP_DEVICE_AUTHENTICATION_ATTR_MASK_ENABLE              (1 << 0)
#define IGMP_DEVICE_AUTHENTICATION_ATTR_MASK_MODE                (1 << 1)
#define IGMP_DEVICE_AUTHENTICATION_ATTR_MASK_AUTHENTICATIONINFO  (1 << 2)
#define IGMP_DEVICE_AUTHENTICATION_ATTR_MASK_DEVICEINFO          (1 << 3)
#define IGMP_DEVICE_AUTHENTICATION_ATTR_MASK_ALL                ((1 << 4) - 1)
	uint32_t bit_map;
} __PACK__ igd_igmp_device_authentication_attr_conf_tab_t;

#define  IGD_TRANSFER_AUTO_DIAG_MONITOR_MAXNUM                   (1)
typedef struct
{
	uint32_t state_and_index;
#define FAULT_CODE_9000201_PON_LOS                               (1)
#define FAULT_CODE_9000202_ONU_ROUGE                             (2)
#define FAULT_CODE_9000101_OLT_RESGISTER_FAILED                  (3)
#define FAULT_CODE_9000102_OLT_RESGISTER_OVERTIME                (4)
#define FAULT_CODE_9000103_PPPOE_AUTH_FAILED                     (5)
#define FAULT_CODE_9000104_PPPOE_AUTH_OVERTIME                   (6)
#define FAULT_CODE_9000105_WAN_ACQUIRE_IP_FAILED                 (7)
#define FAULT_CODE_INIT                                          (254)
#define FAULT_CODE_NO_ERROR                                      (255)
	uint32_t  fault_code;
} __PACK__ igd_transfer_auto_diag_monitor_attr_conf_tab_t;

#define  IGD_TRANSFER_AUTO_DIAG_REDIRECT_MAXNUM                  (1)
typedef struct
{
	uint32_t state_and_index;
	uint8_t  redirect_enable;
	uint8_t  pad[CM_THREE_PADS];
#define TRANSFER_AUTO_DIAG_REDIRECT_REDIRECT_URL_STRING_LEN      (256)
	uint8_t  redirect_url[TRANSFER_AUTO_DIAG_REDIRECT_REDIRECT_URL_STRING_LEN];
#define TRANSFER_AUTO_DIAG_REDIRECT_ATTR_MASK_REDIRECT_ENABLE    (1 << 0)
#define TRANSFER_AUTO_DIAG_REDIRECT_ATTR_MASK_REDIRECT_URL       (1 << 1)
#define TRANSFER_AUTO_DIAG_REDIRECT_ATTR_MASK_ALL               ((1 << 2) - 1)
	uint32_t bit_map;
} __PACK__ igd_transfer_auto_diag_redirect_attr_conf_tab_t;

#define  IGD_TRANSFER_FAST_PATH_SPEED_UP_MAXNUM                  (4)
typedef struct
{
	uint32_t state_and_index;
	uint32_t instance_index;
#define TRANSFER_FAST_PATH_SPEED_UP_DEST_IP_STRING_LEN           (16)
	uint8_t  dest_ip[TRANSFER_FAST_PATH_SPEED_UP_DEST_IP_STRING_LEN];
	uint32_t dest_port;
#define TRANSFER_FAST_PATH_SPEED_UP_SOURCE_IP_STRING_LEN         (16)
	uint8_t  source_ip[TRANSFER_FAST_PATH_SPEED_UP_SOURCE_IP_STRING_LEN];
	uint32_t source_port;
#define TRANSFER_FAST_PATH_SPEED_UP_PROTOCOL_STRING_LEN          (4)
	uint8_t  protocol[TRANSFER_FAST_PATH_SPEED_UP_PROTOCOL_STRING_LEN];
#define TRANSFER_FAST_PATH_SPEED_UP_ATTR_MASK_DEST_IP            (1 << 0)
#define TRANSFER_FAST_PATH_SPEED_UP_ATTR_MASK_DEST_PORT          (1 << 1)
#define TRANSFER_FAST_PATH_SPEED_UP_ATTR_MASK_SOURCE_IP          (1 << 2)
#define TRANSFER_FAST_PATH_SPEED_UP_ATTR_MASK_SOURCE_PORT        (1 << 3)
#define TRANSFER_FAST_PATH_SPEED_UP_ATTR_MASK_PROTOCOL           (1 << 4)
#define TRANSFER_FAST_PATH_SPEED_UP_ATTR_MASK_ALL               ((1 << 5) - 1)
	uint32_t bit_map;
} __PACK__ igd_transfer_fast_path_speed_up_attr_conf_tab_t;

#define  IGD_TRANSFER_L2TP_VPN_MAXNUM                                    (8)
#define  TRANSFER_L2TP_VPN_INDEX_START                                   (10)
#define  TRANSFER_L2TP_VPN_NAME_PREX                                     "trvpn"

typedef struct
{
	uint32_t state_and_index;
	uint32_t instance_index;
#define TRANSFER_L2TP_VPN_INS_ADDRESS_STRING_LEN                 (64)
	uint8_t  ins_address[TRANSFER_L2TP_VPN_INS_ADDRESS_STRING_LEN];
	uint32_t dest_port;
#define TRANSFER_L2TP_VPN_USER_NAME_STRING_LEN                   (64)
	uint8_t  user_name[TRANSFER_L2TP_VPN_USER_NAME_STRING_LEN];
#define TRANSFER_L2TP_VPN_PASSWORD_STRING_LEN                    (64)
	uint8_t  password[TRANSFER_L2TP_VPN_PASSWORD_STRING_LEN];
#define TRANSFER_L2TP_VPN_LIST_INDEX_MAX                         (8)
#define TRANSFER_L2TP_VPN_DEST_ADDRESS_LIST_STRING_LEN           (64)
	char  dest_address_list[TRANSFER_L2TP_VPN_LIST_INDEX_MAX * TRANSFER_L2TP_VPN_DEST_ADDRESS_LIST_STRING_LEN];
#define TRANSFER_L2TP_VPN_SOURCE_MAC_LIST_STRING_LEN             (18)
	char  source_mac_list[TRANSFER_L2TP_VPN_LIST_INDEX_MAX * TRANSFER_L2TP_VPN_SOURCE_MAC_LIST_STRING_LEN];
#define TRANSFER_L2TP_VPN_ATTR_MASK_INS_ADDRESS                          (1 << 0)
#define TRANSFER_L2TP_VPN_ATTR_MASK_DEST_PORT                            (1 << 1)
#define TRANSFER_L2TP_VPN_ATTR_MASK_USER_NAME                            (1 << 2)
#define TRANSFER_L2TP_VPN_ATTR_MASK_PASSWORD                             (1 << 3)
#define TRANSFER_L2TP_VPN_ATTR_MASK_DEST_ADDRESS_LIST                    (1 << 4)
#define TRANSFER_L2TP_VPN_ATTR_MASK_SOURCE_MAC_LIST                      (1 << 5)
#define TRANSFER_L2TP_VPN_ATTR_MASK_ALL                                 ((1 << 6) - 1)
	uint32_t bit_map;
} __PACK__ igd_transfer_l2tp_vpn_attr_conf_tab_t;

#endif
