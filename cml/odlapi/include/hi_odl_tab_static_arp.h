/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm tab static arp obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_STATIC_ARP_H
#define HI_ODL_TAB_STATIC_ARP_H
#include "hi_odl_basic_type.h"

/***************************ARP绑定基本属性表*********************************/
typedef struct {
	uword32 ulStateAndIndex;
#define SECUR_STATIC_ARP_DISABLE (0)
#define SECUR_STATIC_ARP_ENABLE (1)
	uword32 ulEnable; /* 0：Disable 1：Enable*/

#define SECURE_STATIC_ARP_ATTR_MASK_ENABLE (0x01 << 0)

	uword32 ulBitmap;
} __PACK__ IgdSecurStaticArpAttrConfTab;

/***************************静态ARP基本属性表*********************************/
#define IGD_SECUR_STATIC_ARP_LIST_NUM 32
typedef struct {
	uword32 ulStateAndIndex;
	uword32	ulIndex;

	uword8 aucMACAddr[CM_MAC_ADDR_LEN];

#define SECUR_STATIC_ARP_LIST_DISABLE   0
#define SECUR_STATIC_ARP_LIST_ENABLE    1
	uword8 ucEnable;

	uword8 ucPad;
	word8 aucIPAddr[CM_IP_ADDR_STRING_LEN];

#define SECURE_STATIC_ARP_LIST_MASK_MAC (0x01 << 1)
#define SECURE_STATIC_ARP_LIST_MASK_IP (0x01 << 2)
#define SECURE_STATIC_ARP_LIST_MASK_ENABLE (0x01 << 3)
	uword32 ulBitmap;
} __PACK__ IgdSecurStaticArpListConfTab;

#endif
