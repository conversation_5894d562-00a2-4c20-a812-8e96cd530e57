/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm tab remote manager obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_REMOTE_MGT_H
#define HI_ODL_TAB_REMOTE_MGT_H
#include "hi_odl_basic_type.h"

typedef struct {
	uword32 ulStateAndIndex;
#define TR069_ATTR_WEB_MODE_DISABLE (0)
#define TR069_ATTR_WEB_MODE_ENABLE (1)
	uword8 ucWebModEnable; /*web是否可修改*/
#define TR069_ATTR_RAND_INFORM_DISABLE (0)
#define TR069_ATTR_RAND_INFORM_ENABLE (1)
	uword8 ucRandInformEnable; /*默认：false*/
#define TR069_ATTR_PERIOD_INFORM_DISABLE (0)
#define TR069_ATTR_PERIOD_INFORM_ENABLE (1)
	uword8 ucPeriodicInformEnable;
#define TR069_ATTR_UPGRADES_MANAGED_NO (0)
#define TR069_ATTR_UPGRADES_MANAGED_YES (1)
	uword8 ucUpgradesManaged;

#define TR069_ATTR_STUN_DISABLE (0)
#define TR069_ATTR_STUN_ENABLE (1)
	uword8 ucStunEnable;/*默认：false*/
#define TR069_ATTR_NAT_DETECT_DISABLE (0)
#define TR069_ATTR_NAT_DETECT_ENABLE (1)
	uword8 ucNatDetected;/*默认：false*/
	uword8 aucPad[CM_TWO_PADS];

	word8 aucAcsUrl[CM_URL_LEN];
	uword32 ulPortNum;
	word8 aucUsername[CM_USERNAME_LEN];
	word8 aucPassword[CM_USERNAME_LEN];
	word8 aucConnReqUsername[CM_USERNAME_LEN];
	word8 aucConnReqPassword[CM_USERNAME_LEN];
#define TR069_ATTR_PERIOD_INFORM_INTERVAL_MIN (1)
#define TR069_ATTR_PERIOD_INFORM_INTERVAL_MAX (864000)
	uword32 ulPeriodicInformInterval;
	uword64 ulPeriodicInformTime;
	uword32 ulConnReqAddrNotiLimit;
	word8 aucStunServerAddr[CM_URL_LEN];
#define TR069_ATTR_STUN_SERVER_PORT_MIN (0)
#define TR069_ATTR_STUN_SERVER_PORT_MAX (65535)
	uword32 ulStunServerPort;
	word8 aucStunUsername[CM_USERNAME_LEN];
	word8 aucStunPassword[CM_USERNAME_LEN];
#define TR069_ATTR_STUN_KEEP_ALIVE_PERIOD_MIN (-1)
	word32 lStunMaxKeepAlivePeriod;
	uword32 ulStunMinKeepAlivePeriod;

#define TR069_PARAMETER_KEY_LEN (36) //string(32)
	word8 aucParameterKey[TR069_PARAMETER_KEY_LEN];
	word8 aucAcsOldUrl[CM_URL_LEN];
	word8 aucLanConfPassword[CM_PASSWORD_LEN];
#define TR069_ATTR_DHCP_SERVER_CONF_DISABLE (0)
#define TR069_ATTR_DHCP_SERVER_CONF_ENABLE (1)
	uword8 ucDHCPServerConf;
#define TR069_ATTR_LAN_IF_CONF_DISABLE (0)
#define TR069_ATTR_LAN_IF_ENABLE (1)
	uword8 ucLanIpIfEnable;
#define TR069_ATTR_ETH_IF_DISABLE (0)
#define TR069_ATTR_ETH_IF_ENABLE (1)
	uword8 ucLanEthIfEnable;

	uword8 ucFlag;
	uword8 ucFlag2;
	uword8 ucFirstInform;
	uword8 ucPad2;

#define TR069_ATTR_KEY_LEN (36) //string(32)
	uword8 aucDownCommandKey[TR069_ATTR_KEY_LEN];
	uword32 ulDownStartTime;
	uword32 ulDownCompleteTime;
	uword32 ulDownFaultCode;
	uword32 ulInformEventCode;
	word8 aucRBCommandKey[TR069_ATTR_KEY_LEN];
	word8 aucCertPassword[CM_PASSWORD_LEN];
	word8 aucSICommandKey[TR069_ATTR_KEY_LEN];

#define TR069_ATTR_PERSISTENT_DATA_LEN (256)
	word8 aucPersistentData[TR069_ATTR_PERSISTENT_DATA_LEN];
	word8 aucConnReqPath[TR069_ATTR_KEY_LEN];

#define TR069_ATTR_URL_LEN (128)
	word8 aucUdpConnReqAddr[TR069_ATTR_URL_LEN];
	word8 aucKickUrl[TR069_ATTR_URL_LEN];
	word8 aucDownProgressUrl[TR069_ATTR_URL_LEN];
#define TR069_ATTR_PROJECT_ID_LEN (20)
	word8 aucProjectId[TR069_ATTR_PROJECT_ID_LEN];
	uword8 cwmp_enable;/*Add by wzx for bug#18674*/

#define TR069_INTERVAL_LEN_MIN 1
#define TR069_MULTIPLIER_LEN_MIN 2000
#define TR069_MULTIPLIER_DIV 1000
	uword32 ulMinimumWaitInterval;
	uword32 ulIntervalMultiplier;
	uint32_t uludpconnectionrequestaddressnotificationlimit;

	uword8 aucCertEnable; /*for 证书启用 by hyj*/
	uword8 ucPad3[CM_TWO_PADS];
#define TR069_ATTR_MASK_BIT0_WEB_MOD_ENABLE (0x01)
#define TR069_ATTR_MASK_BIT1_RAND_INFORM_ENABLE (0x02)
#define TR069_ATTR_MASK_BIT2_PEROID_INFORM_ENABLE (0x04)
#define TR069_ATTR_MASK_BIT3_UPGRADES_MANAGED (0x08)
#define TR069_ATTR_MASK_BIT4_STUN_ENABLE (0x10)
#define TR069_ATTR_MASK_BIT5_NAT_DETECTED (0x20)
#define TR069_ATTR_MASK_BIT6_ACS_URL (0x40)
#define TR069_ATTR_MASK_BIT7_PORTNUM (0x80)
#define TR069_ATTR_MASK_BIT8_ACS_USERNAME (0x10)
#define TR069_ATTR_MASK_BIT9_ACS_PASSWORD (0x200)
#define TR069_ATTR_MASK_BIT10_CONNREQ_USERNAME (0x400)
#define TR069_ATTR_MASK_BIT11_CONNREQ_PASSWORD (0x800)
#define TR069_ATTR_MASK_BIT12_INFORM_INTERNAL (0x1000)
#define TR069_ATTR_MASK_BIT13_INFORM_TIME (0x2000)
#define TR069_ATTR_MASK_BIT14_CONN_REQ_ADDR_NOTI_LIMIT (0x4000)
#define TR069_ATTR_MASK_BIT15_STUN_SERVER_ADDR (0x8000)
#define TR069_ATTR_MASK_BIT16_STUN_SERVER_PORT (0x10000)
#define TR069_ATTR_MASK_BIT17_STUN_USERNAME (0x20000)
#define TR069_ATTR_MASK_BIT18_STUN_PASSWORD (0x40000)
#define TR069_ATTR_MASK_BIT19_STUN_MAX_KEEPALIVE_PERIOD (0x80000)
#define TR069_ATTR_MASK_BIT20_STUN_MIN_KEETPALIVE_PERIOD (0x100000)
#define TR069_ATTR_MASK_BIT21_PARAMETER_KEY (0x200000)
#define TR069_ATTR_MASK_BIT22_ACS_OLD_URL (0x400000)
#define TR069_ATTR_MASK_BIT23_LAN_CONF_PASSWORD (0x800000)
#define TR069_ATTR_MASK_BIT24_FLAG1 (0x1000000)
#define TR069_ATTR_MASK_BIT25_FLAG2 (0x2000000)
#define TR069_ATTR_MASK_BIT26_DOWN_COMMAND_KEY (0x4000000)
#define TR069_ATTR_MASK_BIT27_DOWN_START_TIME (0x8000000)
#define TR069_ATTR_MASK_BIT28_DOWN_COMPLETE_TIME (0x10000000)
#define TR069_ATTR_MASK_BIT29_DOWN_FAULT_CLDE (0x20000000)
#define TR069_ATTR_MASK_BIT30_INFORM_EVENT_CODE (0x40000000)
#define TR069_ATTR_MASK_BIT31_RB_COMMAND_KEY (0x80000000)
#define TR069_ATTR_MASK_ALL (0xffffffff)
	uword32 ulBitmap;

#define TR069_ATTR_MASK1_BIT1_CERT_PASSWORD (0x01)
#define TR069_ATTR_MASK1_BIT2_SI_COMMAND_KEY (0x02)
#define TR069_ATTR_MASK1_BIT3_PERSISTENT_DATA (0x04)
#define TR069_ATTR_MASK1_BIT4_CONNREQ_PATH (0x08)
#define TR069_ATTR_MASK1_BIT5_UDP_CONREQ_URL (0x10)
#define TR069_ATTR_MASK1_BIT6_KICK_URL (0x20)
#define TR069_ATTR_MASK1_BIT7_DOWN_PROGRESS_URL (0x40)
#define TR069_ATTR_MASK1_BIT8_FIRST_INFORM (0x80)
#define TR069_ATTR_MASK1_BIT9_MINIMUM_WAIT_INTERVAL (0x100)
#define TR069_ATTR_MASK1_BI20_INTERVAL_MULTIPLIER (0x200)
#define TR069_ATTR_MASK1_BIT11_UDP_CONNECTION_REQUEST_ADDRESS_NOTIFICATION_LIMIT       (1 << 10)
#define TR069_ATTR_MASK1_BIT12_CERT_ENABLE       (1 << 11)
#define TR069_ATTR_MASK1_ALL       ((1 << 12) - 1)
	uword32 ulBitmap1;
} __PACK__ IgdRmtMgtTr069AttrConfTab;

/***************************Tr069属性表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define TR069_INFO_URL_LEN (128)
	uword8 aucConnReqURL[TR069_INFO_URL_LEN];/*acs查询设备的URL*/

#define TR069_STATE_INFO_ATTR_MASK_BIT0_CONREQ_URL (0x01)
#define TR069_STATE_INFO_ATTR_MASK_ALL (0x01)
	uword32 ulBitmap;
} __PACK__ IgdRmtMgtTr069InfoConfTab;

/***********Start******************TR369属性表*******add by hyj for TR369 obuspa配置******************************/
typedef struct{
	uword32 ulStateAndIndex;
#define TR369_FLAG_ENABLE 1
	uword8 ucFlag; /*启用和关闭 。 预留 debug flag*/
	uword8 ucFlag2; /*预留 数据模型相关flag*/
} __PACK__ IgdRmtMgtTr369AttrConfTab;

typedef struct{
	uword32 ulStateAndIndex;

	uword32 reboot_cause;
#define TR369_REBOOT_CMDKEY_LEN 32+1
	word8 reboot_cmdkey[TR369_REBOOT_CMDKEY_LEN];
#define TR369_REBOOT_LAST_SW_VER 32+1
	word8 reboot_last_sw_ver[TR369_REBOOT_LAST_SW_VER];
	word32 reboot_req_inst;
} __PACK__ IgdRmtMgtTr369RebootInfoConfTab;

#ifndef TR181_ALIAS_LEN
#define TR181_ALIAS_LEN 64
#endif
#define IGD_TR369_LA_MTP_RECORD_NUM (5)
typedef struct{
	uword32 ulStateAndIndex;
	uword8 Enable;
	word32 instnum;
	word8 Alias[TR181_ALIAS_LEN];
#define TR369_LA_MTP_PROTOCOL_LEN 16
	word8 Protocol[TR369_LA_MTP_PROTOCOL_LEN];
	uword8 EnableMDNS;
#define TR369_LA_MTP_COAP_INTERFACES_LEN 256
	word8 CoAP_Interfaces[TR369_LA_MTP_COAP_INTERFACES_LEN];
	uword32 CoAP_Port;
#define TR369_LA_MTP_COAP_PATH_LEN 64
	word8 CoAP_Path[TR369_LA_MTP_COAP_PATH_LEN];
	uword8 CoAP_EnableEncryption;
#define TR369_LA_MTP_STOMP_REFERENCE_LEN 256
	word8 STOMP_Reference[TR369_LA_MTP_STOMP_REFERENCE_LEN];
#define TR369_LA_MTP_STOMP_DESTINATION_LEN 256
	word8 STOMP_Destination[TR369_LA_MTP_STOMP_DESTINATION_LEN];
#define TR369_LA_MTP_WEBSOCKET_INTERFACES_LEN 256
	word8 WebSocket_Interfaces[TR369_LA_MTP_WEBSOCKET_INTERFACES_LEN];
	uword32 WebSocket_Port;
#define TR369_LA_MTP_WEBSOCKET_PATH_LEN 64
	word8 WebSocket_Path[TR369_LA_MTP_WEBSOCKET_PATH_LEN];
	uword8 WebSocket_EnableEncryption;
#define TR369_LA_MTP_MQTT_REFERENCE_LEN 256
	word8 MQTT_Reference[TR369_LA_MTP_MQTT_REFERENCE_LEN];
#define TR369_LA_MTP_MQTT_RESPONSETOPICCONFIGURED_LEN 256
	word8 MQTT_ResponseTopicConfigured[TR369_LA_MTP_MQTT_RESPONSETOPICCONFIGURED_LEN];
	uword8 MQTT_PublishQoS;
} __PACK__ IgdRmtMgtTr369LocalAgentMtpAttrConfTab;

#define IGD_TR369_LA_CONTROLLER_RECORD_NUM (5)
typedef struct{
	uword32 ulStateAndIndex;
	uword8 Enable;
	word32 instnum;
	word8 Alias[TR181_ALIAS_LEN];
#define TR369_LA_CONTROLLER_EndpointID_LEN 64
	word8 EndpointID[TR369_LA_CONTROLLER_EndpointID_LEN];
#define TR369_LA_CONTROLLER_ControllerCode_LEN 128+1
	word8 ControllerCode[TR369_LA_CONTROLLER_ControllerCode_LEN];
#define TR369_LA_CONTROLLER_ProvisioningCode_LEN 64+1
	word8 ProvisioningCode[TR369_LA_CONTROLLER_ProvisioningCode_LEN];
#define TR369_LA_CONTROLLER_AssignedRole_LEN 256
	word8 AssignedRole[TR369_LA_CONTROLLER_AssignedRole_LEN];
#define TR369_LA_CONTROLLER_Credential_LEN 256
	word8 Credential[TR369_LA_CONTROLLER_Credential_LEN];
	uword32 PeriodicNotifInterval;
	uword32 PeriodicNotifTime;
	uword32 USPNotifRetryMinimumWaitInterval;
	uword32 USPNotifRetryIntervalMultiplier;
} __PACK__ IgdRmtMgtTr369LocalAgentControllerAttrConfTab;

#define IGD_TR369_LA_CONTROLLER_MTP_RECORD_NUM (15)
typedef struct{
	uword32 ulStateAndIndex;
	uword8 Enable;
	word32 instnum;
	word8 Alias[TR181_ALIAS_LEN];

	uword32 controller_instnum;
#define TR369_LA_CONTROLLER_MTP_PROTOCOL_LEN 64
	uword8 Protocol[TR369_LA_CONTROLLER_MTP_PROTOCOL_LEN];

	/* Device.LocalAgent.Controller.{i}.MTP.{i}.CoAP. */
#define TR369_LA_CONTROLLER_MTP_COAP_HOST_LEN 256+1
	uword8 CoAP_Host[TR369_LA_CONTROLLER_MTP_COAP_HOST_LEN];
	uword32 CoAP_Port;
#define TR369_LA_CONTROLLER_MTP_COAP_PATH_LEN 64
	uword8 CoAP_Path[TR369_LA_CONTROLLER_MTP_COAP_PATH_LEN];
	uword8 CoAP_EnableEncryption;

	/* Device.LocalAgent.Controller.{i}.MTP.{i}.STOMP. */
#define TR369_LA_CONTROLLER_MTP_STOMP_REFERENCE_LEN 256
	uword8 STOMP_Reference[TR369_LA_CONTROLLER_MTP_STOMP_REFERENCE_LEN];
#define TR369_LA_CONTROLLER_MTP_STOMP_DESTINATION_LEN 256
	uword8 STOMP_Destination[TR369_LA_CONTROLLER_MTP_STOMP_DESTINATION_LEN];

	/* Device.LocalAgent.Controller.{i}.MTP.{i}.WebSocket. */
#define TR369_LA_CONTROLLER_MTP_WEBSOCKET_HOST_LEN 256+1
	uword8 WebSocket_Host[TR369_LA_CONTROLLER_MTP_WEBSOCKET_HOST_LEN];
	uword32 WebSocket_Port;
#define TR369_LA_CONTROLLER_MTP_WEBSOCKET_PATH_LEN 64
	uword8 WebSocket_Path[TR369_LA_CONTROLLER_MTP_WEBSOCKET_PATH_LEN];
	uword8 WebSocket_EnableEncryption;
	uword32 WebSocket_KeepAliveInterval;
	uword32 WebSocket_SessionRetryMinimumWaitInterval;
	uword32 WebSocket_SessionRetryIntervalMultiplier;

	/* Device.LocalAgent.Controller.{i}.MTP.{i}.MQTT. */
#define TR369_LA_CONTROLLER_MTP_MQTT_REFERENCE_LEN 256
	uword8 MQTT_Reference[TR369_LA_CONTROLLER_MTP_MQTT_REFERENCE_LEN];
#define TR369_LA_CONTROLLER_MTP_MQTT_TOPIC_LEN 256
	uword8 MQTT_Topic[TR369_LA_CONTROLLER_MTP_MQTT_TOPIC_LEN];
	uword8 MQTT_PublishRetainResponse;
	uword8 MQTT_PublishRetainNotify;
} __PACK__ IgdRmtMgtTr369LocalAgentControllerMtpAttrConfTab;

#define IGD_TR369_STOME_CONN_RECORD_NUM (5)
typedef struct{
	uword32 ulStateAndIndex;
	uword8 Enable;
	uword32 instnum;
	uword8 Alias[TR181_ALIAS_LEN];
#define TR369_STOMP_CONN_HOST_LEN 64+1
	uword8 Host[TR369_STOMP_CONN_HOST_LEN];
	uword32 Port;
#define TR369_STOMP_CONN_USERNAME_LEN 256+1
	uword8 Username[TR369_STOMP_CONN_USERNAME_LEN];
#define TR369_STOMP_CONN_PASSWORD_LEN  256+1
	uword8 Password[TR369_STOMP_CONN_PASSWORD_LEN];
#define TR369_STOMP_CONN_VIRTUALHOST_LEN 256+1
	uword8 VirtualHost[TR369_STOMP_CONN_VIRTUALHOST_LEN];
	uword8 EnableHeartbeats;
	uword32 OutgoingHeartbeat;
	uword32 IncomingHeartbeat;
	uword32 ServerRetryInitialInterval;
	uword32 ServerRetryIntervalMultiplier;
	uword32 ServerRetryMaxInterval;
	uword8 EnableEncryption;
} __PACK__ IgdRmtMgtTr369StompConnAttrConfTab;

#define IGD_TR369_SUBSCRIPTION_RECORD_NUM (15)
typedef struct{
	uword32 ulStateAndIndex;
	uword8 Enable;
	uword32 instnum;
	uword8 Alias[TR181_ALIAS_LEN];
#define TR369_SUBSCRIPTION_RECIPIENT_LEN 64
	uword8 Recipient[TR369_SUBSCRIPTION_RECIPIENT_LEN];
#define TR369_SUBSCRIPTION_ID_LEN 64
	uword8 ID[TR369_SUBSCRIPTION_ID_LEN];
	lword64 CreationDate;
#define TR369_SUBSCRIPTION_NOTIFTYPE_LEN 32
	uword8 NotifType[TR369_SUBSCRIPTION_NOTIFTYPE_LEN];
#define TR369_SUBSCRIPTION_REFERENCELIST_LEN 256+1
	uword8 ReferenceList[TR369_SUBSCRIPTION_REFERENCELIST_LEN];
	uword8 Persistent;
	uword32 TimeToLive;
	uword8 NotifRetry;
	uword32 NotifExpiration;
} __PACK__ IgdRmtMgtTr369SubscriptionAttrConfTab;

#define MAX_MQTT_CLIENT 5
enum eMQTT_ProtocolVersion
{
        MQTT_Protocol_3_1 = 0,
        MQTT_Protocol_3_1_1,
        MQTT_Protocol_5_0,
};
enum eMQTT_TransportProtocol
{
        MQTT_TransportProtocol_TCPIP = 0,
        MQTT_TransportProtocol_TLS,
};

#define IGD_TR369_MQTT_CLIENT_RECORD_NUM (5)
typedef struct{
	uword32 ulStateAndIndex;
	uword8 Enable;
	uword32 instnum;
	uword8 Alias[TR181_ALIAS_LEN];
#define TR369_MQTT_CLIENT_NAME_LEN 64
	uword8 Name[TR369_MQTT_CLIENT_NAME_LEN];
	uword8 ProtocolVersion;
	uword8 EnableEncryption;
#define TR369_MQTT_CLIENT_BROKERADDRESS_LEN 256
	uword8 BrokerAddress[TR369_MQTT_CLIENT_BROKERADDRESS_LEN];
	uword32 BrokerPort;
	uword8 TransportProtocol;
	uword8 CleanSession;
	uword8 CleanStart;
	uword8 WillEnable;
	uword32 WillQoS;
	uword8 WillRetain;
	uword32 KeepAliveTime;
	uword32 SessionExpiryInterval;
	uword32 ReceiveMaximum;
	uword32 MaximumPacketSize;
	uword32 TopicAliasMaximum;
	uword8 RequestResponseInfo;
	uword8 RequestProblemInfo;
#define TR369_MQTT_CLIENT_AUTHENTICATIONMETHOD_LEN 256
	uword8 AuthenticationMethod[TR369_MQTT_CLIENT_AUTHENTICATIONMETHOD_LEN];
#define TR369_MQTT_CLIENT_CLIENTID_LEN 256
	uword8 ClientID[TR369_MQTT_CLIENT_CLIENTID_LEN];
	uword32 WillDelayInterval;
	uword32 WillMessageExpiryInterval;
#define TR369_MQTT_CLIENT_WILLCONTENTTYPE_LEN 256
	uword8 WillContentType[TR369_MQTT_CLIENT_WILLCONTENTTYPE_LEN];
#define TR369_MQTT_CLIENT_WILLRESPONSETOPIC_LEN 256
	uword8 WillResponseTopic[TR369_MQTT_CLIENT_WILLRESPONSETOPIC_LEN];
#define TR369_MQTT_CLIENT_WILLTOPIC_LEN 256
	uword8 WillTopic[TR369_MQTT_CLIENT_WILLTOPIC_LEN];
#define TR369_MQTT_CLIENT_WILLVALUE_LEN 256
	uword8 WillValue[TR369_MQTT_CLIENT_WILLVALUE_LEN];
#define TR369_MQTT_CLIENT_USERNAME_LEN 256
	uword8 Username[TR369_MQTT_CLIENT_USERNAME_LEN];
#define TR369_MQTT_CLIENT_PASSWORD_LEN 256
	uword8 Password[TR369_MQTT_CLIENT_PASSWORD_LEN];
	uword32 PublishMessageExpiryInterval;
	uword32 MessageRetryTime;
	uword32 ConnectRetryTime;
	uword32 ConnectRetryIntervalMultiplier;
	uword32 ConnectRetryMaxInterval;
} __PACK__ IgdRmtMgtTr369MqttClientAttrConfTab;


#define IGD_TR369_MQTT_CLIENT_SUB_RECORD_NUM (15)
#define MAX_MQTT_CLIENT_SUBSCRIPTION 5
typedef struct{
	uword32 ulStateAndIndex;
	uword8 Enable;
	uword32 instnum;
	uword32 client_instnum;
	uword8 Alias[TR181_ALIAS_LEN];
#define TR369_MQTT_CLIENT_SUBSCRIPTION_TOPIC_LEN 512
	uword8 Topic[TR369_MQTT_CLIENT_SUBSCRIPTION_TOPIC_LEN];
	uword32 QoS;
} __PACK__ IgdRmtMgtTr369MqttClientSubscriptionAttrConfTab;

/***********Ending******************TR369属性表*******add by hyj for TR369 obuspa配置******************************/

/**********************单播ARP/NP探测节点配置表**************************/
#define IGD_NEIGHBOR_DETECTION_CFG_STATEANDINDEX     (0xFFFFFFFF)
#define IGD_NEIGHBOR_DETECTION_CONFIG_TAB_RECORD_NUM (8)
#define IGD_NEIGHBOR_DETECTION_INTERVAL_DEFAULT (60)
#define IGD_NEIGHBOR_DETECTION_REPETITION_DEFAULT (3)
#define IGD_NEIGHBOR_DETECTION_IPV4_FAIL_MASK (1 << 0)
#define IGD_NEIGHBOR_DETECTION_IPV6_FAIL_MASK (1 << 1)
typedef struct {
	uword32 state_and_index;

	uword8 neighbor_detection_config_index;

#define NEIGHBOR_DETECTION_CONFIG_DISABLE (0)
#define NEIGHBOR_DETECTION_CONFIG_ENABLE (1)
	uword8 config_enable;
#define NEIGHBOR_DETECTION_CONFIG_NUM_REPET_MIN (1)
#define NEIGHBOR_DETECTION_CONFIG_NUM_REPET_MAX (255)
	uword8 number_of_repetitions;
	uword8 pad;
#define NEIGHBOR_DETECTION_CONFIG_INTERVAL_MIN (1)
#define NEIGHBOR_DETECTION_CONFIG_INTERVAL_MAX (3600)
	uword32 interval;
	uword32 neighbor_wan_global_index;
	word8 neighbor_wan_name[CM_WAN_CONNECTION_NAME_LEN];

#define NEIGHBOR_DETECTION_CONFIG_MASK_BIT0_ENABLE (0x01)
#define NEIGHBOR_DETECTION_CONFIG_MASK_BIT1_NUM_OF_REPT (0x02)
#define NEIGHBOR_DETECTION_CONFIG_MASK_BIT2_INTERVAL (0x04)
#define NEIGHBOR_DETECTION_CONFIG_MASK_BIT3_WAN_GLOBAL_INDEX (0x08)
#define NEIGHBOR_DETECTION_CONFIG_MASK_BIT4_WAN_NAME (0x10)
#define NEIGHBOR_DETECTION_CONFIG_MASK_ALL (0x1F)
	uword32 bit_map;
} __PACK__ idg_neighbor_detection_config_tab;

/***************************中间键属性表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define MIDDWARE_ONLY_ENABLE (0)
#define TR069_ONLY_ENABLE (1)
#define MIDDWARE_AND_TR069_ENABLE (2)
	uword8 ucTr069Enable; /*默认启用tr069而关闭中间件*/
	uword8 aucPad[CM_THREE_PADS];

#define MIDDWARE_URL_LEN (128)
	word8 aucMiddlewareURL[MIDDWARE_URL_LEN];
	uword32 ulMiddwarePort;
#define MIDDWARE_MGT_ATTR_MASK_BIT0_TR069_ENABLE (0x01)
#define MIDDWARE_MGT_ATTR_MASK_BIT1_MIDDWARE_URL (0x02)
#define MIDDWARE_MGT_ATTR_MASK_BIT2_MIDDWARE_PORT (0x04)
#define MIDDWARE_MGT_ATTR_MASK_ALL (0x07)
	uword32 ulBitmap;
} __PACK__ IgdRmtMgtMiddwareAttrConfTab;


/***************************LOID注册信息表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define LOID_PASSWORD_CONFIG_DISABLE (0)
#define LOID_PASSWORD_CONFIG_ENABLE (1)
	uword8 ucPasswdConfigEnable; /* web use only*/

#define DEVICE_REG_STATE_INIT (0)
#define DEVICE_REG_STATE_SUCC_ONCE (1)
	uword8 device_reg_state;
	uword8 aucPad1[CM_TWO_PADS];

#define LOID_NAME_LEN (24+1)
	word8 aucLoidName[LOID_NAME_LEN];/*LOID*/
#define LOID_PASSWORD_LEN (12+1)
	word8 aucLoidPasswd[LOID_PASSWORD_LEN];
	uword8 aucPad[CM_TWO_PADS];

#define ITMS_AUTH_STATUS_SUCCESS (0)
#define ITMS_AUTH_STATUS_USER_AUTH_CODE_NOT_EXIST (1)
#define ITMS_AUTH_STATUS_USER_LOID_NOT_EXIST (2)
#define ITMS_AUTH_STATUS_LOID_MATCH_FAILED (3)
#define ITMS_AUTH_STATUS_TIMEOUT (4)
#define ITMS_AUTH_STATUS_NO_NEW_JOB_TO_EXECURE (5)
#define ITMS_AUTH_STATUS_NO_AUTH_RESULT (99)
	uword8 ucStatus; /*缺省值：99，表示无认证结果信息*/
	uword8 ucLimit;/* 缺省值：10 */
	uword8 ucTimes;/*终端认证重试次数*/
#define ITMS_WORKING_ORDER_ISSUE_START (0)
#define ITMS_WORKING_ORDER_ISSUE_SUCCESS (1)
#define ITMS_WORKING_ORDER_ISSUE_FAILED (2)
#define ITMS_WORKING_ORDER_ISSUE_IDLE (99)
	uword8 ucResult; /*缺省值为99，表示无下发结果信息*/

#define ITMS_SERVICE_NUM_MAX (8)
	uword8 ucSeviceNum;
	/*逻辑ID注册时将要下发配置的业务数量*/
#define ITMS_SERVICE_NAME_IPTV (0x01)
#define ITMS_SERVICE_NAME_INTERNET (0x02)
#define ITMS_SERVICE_NAME_VOIP (0x04)
#define ITMS_SERVICE_NAME_OTHER (0x08)
#define ITMS_SERVICE_NAME_OTT (0x10)
	uword8 ucSeviceName;/*当前下发业务名称*/
#define ITMS_AUTH_TYPE_NONE (0)
#define ITMS_AUTH_TYPE_LOID (1)
#define ITMS_AUTH_TYPE_LOID_PWD (2)
	uword8 aucAuthType;/*认证类型*/

#define PUSH_AUTH_WEB   (1)
#define NO_PUSH_AUTH_WEB (2)
	uword8 ucAuthFailProcMode;     /*认证失败后是否强推模式: 1,强制推送， 2:不推送(缺省值)*/
#define   ITMS_SERVICE_TOTAL_NAME (64)
	uword8 aucServiceTotalName[ITMS_SERVICE_TOTAL_NAME];
#define LOID_REG_ATTR_MASK_BIT0_PASSWD_CONF_ENABLE (0x01)
#define LOID_REG_ATTR_MASK_BIT1_LOID (0x02)
#define LOID_REG_ATTR_MASK_BIT2_LOID_PASSWD (0x04)
#define LOID_REG_ATTR_MASK_BIT3_STATUS (0x08)
#define LOID_REG_ATTR_MASK_BIT4_LIMITS (0x10)
#define LOID_REG_ATTR_MASK_BIT5_TIMES (0x20)
#define LOID_REG_ATTR_MASK_BIT6_RESULT (0x40)
#define LOID_REG_ATTR_MASK_BIT7_SERVICE_NUM (0x80)
#define LOID_REG_ATTR_MASK_BIT8_SERVICE_NAME (0x100)
#define LOID_REG_ATTR_MASK_BIT9_SERVICE_TOTAL_NAME (0x200)
#define LOID_REG_ATTR_MASK_ALL (0x3ff)
	uword32 ulBitmap;
} __PACK__ IgdRmtMgtLoidRegAttrConfTab;


/***************************ITMS注册状态*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define ITMS_INFORM_STATUS_SUCCESS (0)
#define ITMS_INFORM_STATUS_NO_REPORT (1)
#define ITMS_INFORM_STATUS_NO_RESPONSE (2)
#define ITMS_INFORM_STATUS_ABORT (3)
#define ITMS_INFORM_STATUS_NO_REPORT_RMT_MNG_WAN (4)
#define ITMS_INFORM_STATUS_NO_REPORT_INVALID_WAN (5)
#define ITMS_INFORM_STATUS_NO_REPORT_INVALID_ACS (6)
#define ITMS_INFORM_STATUS_NO_REPORT_FAIL_TO_GET_ACS_ADDR (7)
	uword8 ucInformStatus; /*Inform上报状态*/
#define ITMS_CONN_STATUS_CONNECTON_ACCEPTED (0)
#define ITMS_CONN_STATUS_SUCCESS (1)
#define ITMS_CONN_STATUS_CONNECTION_UNRECEIVED (2)
#define ITMS_CONN_STATUS_CONNECTION_ABORT (3)
#define ITMS_CONN_STATUS_UNKNOWN (4)
	uword8 ucITMSConnectStatus; /*ITMS连接CPE设备的状态*/
	uword8 aucPad[CM_TWO_PADS];

#define ITMS_REG_INFO_MASK_BIT0_INFORM_STATUS (0x01)
#define ITMS_REG_INFO_MASK_BIT1_CONNECTION_STATUS (0x02)
#define ITMS_REG_INFO_MASK_ALL (0x03)
	uword32 ulBitmap;
} __PACK__ IgdRmtMgtITMSRegStateTab;


/***************************PASSWORD 认证信息表*********************************/
typedef struct {
	uword32 ulStateAndIndex;
#define SN_NAME_LEN (12+1)
	word8 aucSN[SN_NAME_LEN];
	// uword8 aucPad1[CM_TWO_PADS];

#define PASSWORD_LEN (10+1)
	word8 aucPasswd[PASSWORD_LEN];
	// uword8 aucPad[CM_TWO_PADS];

#define ITMS_AUTH_STATUS_SUCCESS (0)
#define ITMS_AUTH_STATUS_USER_AUTH_CODE_NOT_EXIST (1)
#define ITMS_AUTH_STATUS_UNDETERMINED2 (2)
#define ITMS_AUTH_STATUS_UNDETERMINED3 (3)
#define ITMS_AUTH_STATUS_TIMEOUT (4)
#define ITMS_AUTH_STATUS_NO_NEW_JOB_TO_EXECURE (5)
#define ITMS_AUTH_STATUS_NO_AUTH_RESULT (99)
	uword8 ucStatus; /*缺省值：99，表示无认证结果信息*/
	uword8 ucLimit;/* 缺省值：10 */
	uword8 ucTimes;/*终端认证重试次数*/
#define ITMS_WORKING_ORDER_ISSUE_START (0)
#define ITMS_WORKING_ORDER_ISSUE_SUCCESS (1)
#define ITMS_WORKING_ORDER_ISSUE_FAILED (2)
#define ITMS_WORKING_ORDER_ISSUE_IDLE (99)
	uword8 ucResult; /*缺省值为99，表示无下发结果信息*/

#define ITMS_SERVICE_NUM_MAX (8)
	uword8 ucSeviceNum;
	/*逻辑ID注册时将要下发配置的业务数量*/
#define ITMS_SERVICE_NAME_IPTV (0x01)
#define ITMS_SERVICE_NAME_INTERNET (0x02)
#define ITMS_SERVICE_NAME_VOIP (0x04)
#define ITMS_SERVICE_NAME_OTHER (0x08)
#define ITMS_SERVICE_NAME_OTT (0x10)
	uword8 ucSeviceName;/*当前下发业务名称*/
#define ITMS_AUTH_TYPE_NONE (0)
#define ITMS_AUTH_TYPE_PASSWORD (1)
#define ITMS_AUTH_TYPE_LOID_SN  (2)
	uword8 aucAuthType;/*认证类型*/
#define PUSH_AUTH_WEB   (1)
#define NO_PUSH_AUTH_WEB (2)
	uword8 ucAuthFailProcMode;     /*认证失败后是否强推模式: 1,强制推送， 2:不推送(缺省值)*/

#define PASSWORD_REG_ATTR_MASK_BIT0_PASSWD (0x01)
#define PASSWORD_REG_ATTR_MASK_BIT1_STATUS (0x02)
#define PASSWORD_REG_ATTR_MASK_BIT2_LIMITS (0x04)
#define PASSWORD_REG_ATTR_MASK_BIT3_TIMES (0x08)
#define PASSWORD_REG_ATTR_MASK_BIT4_RESULT (0x10)
#define PASSWORD_REG_ATTR_MASK_BIT5_SERVICE_NUM (0x20)
#define PASSWORD_REG_ATTR_MASK_BIT6_SERVICE_NAME (0x40)
#define PASSWORD_REG_ATTR_MASK_BIT7_SN (0x80)
#define PASSWORD_REG_ATTR_MASK_ALL (0xff)
	uword32 ulBitmap;
} __PACK__ IgdRmtMgtPasswordRegAttrConfTab;

/*************************** 远程访问web *********************************/
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulEnable;								/* 是否使能 */
	uword32 ulPort;									/* 远程访问web 端口 */
	word8	aucIpAddr[CM_IP_ADDR_STRING_LEN];		/* 远程访问web IP地址 */
#define MGTWEB_REG_ATTR_MASK_BIT0_ENABLE (0x01)
#define MGTWEB_REG_ATTR_MASK_BIT1_PORT (0x02)
#define MGTWEB_REG_ATTR_MASK_BIT2_IPADDR (0x04)
#define MGTWEB_REG_ATTR_MASK_ALL 		(0xff)
	uword32 ulBitmap;
} __PACK__ IgdRmtMgtWebAttrConfTab;

/*业务逻辑处理函数声明*/
word32 igdCmRmtMgtWebAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmRmtMgtWebAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmRmtMgtWebAttrInit(void);

/*************************** 用户和性能数据上传*********************************/

/*表相关宏*/


typedef struct {
	uword32 ulStateAndIndex;

	uword32	ulEnable;
	word8	acServerUrl[128];
	uword32	ulUploadInterval;
	uword32	ulCountInterval;

	uword8	ucReportSnEn;
	uword8	ucCpuRateEn;
	uword8	ucMemRateEn;
	uword8	ucSerEn;
	uword8	ucErrCodeEn;
	uword8	ucPlrEn;
	uword8	ucPacketLostEn;
	uword8	ucTempEn;
	uword8	ucUpdataEn;
	uword8	ucDowndataEn;
	uword8	ucAllDeviceNumEn;
	uword8	ucLanDeviceMacEn;
	uword8	ucWlanDeviceMacEn;
	uword8	ucOpticalInPowerEn;
	uword8	ucOpticalOutPowerEn;
	uword8	ucMulticastNumEn;
	uword8	ucRouteModeEn;
	uword8	ucDialingNumEn;
	uword8	ucDialingErrEn;
	uword8	ucRegisterNumEn;
	uword8	ucRegisterSucNumEn;

	uword8	ucAllWirelessChannelEn;
	uword8	ucBestWirelessChannelEn;
	uword8	ucWirelessChannelNumEn;
	uword8	ucWirelessBandWidthEn;
	uword8	ucWirelessPowerEn;

	uword8	ucQosTypeEn;
	uword8	ucWirelessTypeEn;

#define DATA_UPLOAD_ATTR_MASK_BIT0_ENABLE 						(0x01)
#define DATA_UPLOAD_ATTR_MASK_BIT1_SERVER_URL 					(0x02)
#define DATA_UPLOAD_ATTR_MASK_BIT2_UPLOAD_INTERVAL 				(0x04)
#define DATA_UPLOAD_ATTR_MASK_BIT3_COUNT_INTERVAL				(0x08)
#define DATA_UPLOAD_ATTR_MASK_BIT4_REPORT_SN_EN 				(0x10)
#define DATA_UPLOAD_ATTR_MASK_BIT5_CPU_RATE_EN 					(0x20)
#define DATA_UPLOAD_ATTR_MASK_BIT6_MEM_RATE_EN 					(0x40)
#define DATA_UPLOAD_ATTR_MASK_BIT7_SER_EN 						(0x80)
#define DATA_UPLOAD_ATTR_MASK_BIT8_ERR_CODE_EN 					(0x100)
#define DATA_UPLOAD_ATTR_MASK_BIT9_PLR_EN 						(0x200)
#define DATA_UPLOAD_ATTR_MASK_BIT10_PACKET_LOST_EN 				(0x400)
#define DATA_UPLOAD_ATTR_MASK_BIT11_TEMP_EN 					(0x800)

#define DATA_UPLOAD_ATTR_MASK_BIT12_UP_DATA_EN 					(0x1000)
#define DATA_UPLOAD_ATTR_MASK_BIT13_DOWN_DATA_EN 				(0x2000)
#define DATA_UPLOAD_ATTR_MASK_BIT14_ALL_DEV_NUM_EN 				(0x4000)
#define DATA_UPLOAD_ATTR_MASK_BIT15_LAN_DEV_MAC_EN				(0x8000)
#define DATA_UPLOAD_ATTR_MASK_BIT16_WLAN_DEV_MAC_EN 			(0x10000)
#define DATA_UPLOAD_ATTR_MASK_BIT17_OPTICAL_IN_POWER_EN 		(0x20000)
#define DATA_UPLOAD_ATTR_MASK_BIT18_OPTICAL_OUT_POWER_EN		(0x40000)
#define DATA_UPLOAD_ATTR_MASK_BIT19_MC_NUM_EN 					(0x80000)
#define DATA_UPLOAD_ATTR_MASK_BIT20_ROUTE_MODE_EN 				(0x100000)
#define DATA_UPLOAD_ATTR_MASK_BIT21_DIAL_NUM_EN 				(0x200000)
#define DATA_UPLOAD_ATTR_MASK_BIT22_DIAL_ERR_EN 				(0x400000)
#define DATA_UPLOAD_ATTR_MASK_BIT23_REGISTER_NUM_EN 			(0x800000)
#define DATA_UPLOAD_ATTR_MASK_BIT24_REGISTER_SUC_NUM_EN 		(0x1000000)
#define DATA_UPLOAD_ATTR_MASK_BIT25_ALL_WIRELESS_CHANNEL_EN 	(0x2000000)
#define DATA_UPLOAD_ATTR_MASK_BIT26_BEST_WIRELESS_CHANNEL_EN	(0x4000000)
#define DATA_UPLOAD_ATTR_MASK_BIT27_WIRELESS_CHANNEL_NUM_EN 	(0x8000000)
#define DATA_UPLOAD_ATTR_MASK_BIT28_WIRELESS_BANDWIDTH_EN 		(0x10000000)
#define DATA_UPLOAD_ATTR_MASK_BIT29_WIRELESS_POWER_EN 			(0x20000000)
#define DATA_UPLOAD_ATTR_MASK_BIT30_QOS_TYPE_EN 				(0x80000000)
#define DATA_UPLOAD_ATTR_MASK_BIT31_WIRELESS_TYPE_EN 			(0x40000000)

#define DATA_UPLOAD_ATTR_MASK_ALL (0xffffffff)
	uword32 ulBitmap;
} __PACK__ IgdRmtMgtDataUploadAttrConfTab;

/*业务逻辑处理函数声明*/
word32 igdCmRmtMgtDataUploadAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmRmtMgtDataUploadAttrGet(uword8 *pucInfo, uword32 len);

word32 igdCmRmtMgtDataUploadAttrInit(void);



typedef struct {
	uword32 ulStateAndIndex;
#define REMOTEMGT_RESTORE_STATUS_DEFAULT (0)
#define REMOTEMGT_RESTORE_STATUS_LONG (1)
#define REMOTEMGT_RESTORE_STATUS_SHORT (2)
#define REMOTEMGT_RESTORE_STATUS_LOCAL (3)
#define REMOTEMGT_RESTORE_STATUS_ITMS (4)
#define REMOTEMGT_RESTORE_STATUS_PLATFORM (5)
	uword8   ucRestoreStatus;

#define  REMOTEMGT_TAG_TRIGGER_OP (0)
#define  REMOTEMGT_TAG_UNTRIGGER_OP (1)
	uword8 ucOPTag;
	uword8 aucPad[CM_TWO_PADS];

#define REMOTEMGT_RESTORE_MODE_SAVE_KEYCONFIG (0)
#define REMOTEMGT_RESTORE_MODE_NOTSAVE_KEYCONFIG (1)
	word32 mode;

#define REMOTEMGT_RESTORE_ATTR_MASK_RESTORE_STATUS              (0x01)
#define REMOTEMGT_RESTORE_ATTR_MASK_MODE                        (0x02)
#define REMOTEMGT_RESTORE_ATTR_MASK_ALL                         (0x03)
	uword32 ulBitmap;
} __PACK__ IgdRmtRestoreTab;

/*òμ?????-′|àíoˉêyéù?÷*/

word32 igdCmRmtRestoreAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmRmtRestoreAttrGet(uword8 *pucInfo, uword32 len);

/*表相关宏*/


typedef struct {
	word8	acReportSn[32];
	word8	acCpuRate[32];
	word8	acMemRate[32];
	word8	acSer[32];
	word8	acErr[32];
	word8	acPlr[32];
	word8	acPacketLost[32];
	word8	acTemp[32];
	word8	acUpdata[128];
	word8	acDowndata[128];
	word8	acAllClientNum[32];
	word8	acLanClient[128];
	word8	acWlanClient[128];
	word8	acOpticalInPower[32];
	word8	acOpticalOutPower[32];
	word8	acMulticastNum[32];
	word8	acRoutingMode[32];
	word8	acDialingNum[32];
	word8	acDialingErr[32];
	word8	acRegisterNum[32];
	word8	acRegisterSucNum[32];

	word8	acAllWlanChannel[128];
	word8	acBestWlanChannel[128];
	word8	acWlanChannel[32];
	word8	acWlanBw[32];
	word8	acWlanPower[32];

	word8	acQosType[32];
	word8	acWlanMode[32];
} __PACK__ IgdRmtMgtDataUploadResultConfTab;


/*hyj */
/* 
 TR181扩展的表：
	Device.Ethernet.
		Device.Ethernet.Interface 
		Device.Ethernet.Link  
		Device.Ethernet.VlanTermination. VLANTERMINATION
 */
#define TR181_ALIAS_LEN 64
typedef struct {
	uword32 ulStateAndIndex;
	uword8 Enable;
	word32 instnum;
	word8 Alias[TR181_ALIAS_LEN];
#define LOWERLAYERS_LEN 1024+1
	word8 LowerLayers[LOWERLAYERS_LEN];
	word32 MaxBitRate;
	word8 DuplexMode; /*Half Full Auto*/
	word8 EEEEnable;
} __PACK__ IgdRmtMgtDataTr181EthInterfaceAttrConfTab;

#define WAN_NAME_LEN 64
typedef struct {
	uword32 ulStateAndIndex;
	uword8 Enable;
	word32 instnum;
	word8 Alias[TR181_ALIAS_LEN];
#define LOWERLAYERS_LEN 1024+1
	word8 LowerLayers[LOWERLAYERS_LEN];
	word8 PriorityTagging;
	word8 wanName[WAN_NAME_LEN];
} __PACK__ IgdRmtMgtDataTr181EthLinkAttrConfTab;

typedef struct {
	uword32 ulStateAndIndex;
	uword8 Enable;
	word32 instnum;
	word8 Alias[TR181_ALIAS_LEN];
#define LOWERLAYERS_LEN 1024+1
	word8 LowerLayers[LOWERLAYERS_LEN];
	word32 VLANID;
	word32 TPID;
	word8 wanName[WAN_NAME_LEN];
} __PACK__ IgdRmtMgtDataTr181EthVlanTermAttrConfTab;


/*Device.ATM.Link*/
typedef struct {
	uword32 ulStateAndIndex;
	uword8 Enable;
	word32 instnum;
	word8 Alias[TR181_ALIAS_LEN];
#define LOWERLAYERS_LEN 1024+1
	word8 LowerLayers[LOWERLAYERS_LEN];
	word8 LinkType; /*EoA IPoA PPPoA CIP Unconfigured*/
	word8 VPI; /*Not found in tr181-2-11 xml*/
	word32 VCI; /*Not found in tr181-2-11 xml*/
	word8 Encapsulation;  /*LLC VCMUX*/
	word8 FCSPreserved;
#define VCSEARCHLIST_LEN 256+1
	word8 VCSearchList[VCSEARCHLIST_LEN]; 
} __PACK__ IgdRmtMgtDataTr181ATMLinkAttrConfTab;

/*Device.PTM.Link*/
typedef struct {
	uword32 ulStateAndIndex;
	uword8 Enable;
	word32 instnum;
	word8 Alias[TR181_ALIAS_LEN];
#define LOWERLAYERS_LEN 1024+1
	word8 LowerLayers[LOWERLAYERS_LEN];
} __PACK__ IgdRmtMgtDataTr181PTMLinkAttrConfTab;

/*Device.Optical.Interface.*/
typedef struct {
	uword32 ulStateAndIndex;
	uword8 Enable;
	word32 instnum;
	word8 Alias[TR181_ALIAS_LEN];
#define LOWERLAYERS_LEN 1024+1
	word8 LowerLayers[LOWERLAYERS_LEN];
} __PACK__ IgdRmtMgtDataTr181OpticalInterfaceAttrConfTab;

/*Device.PPP.Interface.*/
typedef struct {
	uword32 ulStateAndIndex;
	uword8 Enable;
	word32 instnum;
	word8 Alias[TR181_ALIAS_LEN];
#define LOWERLAYERS_LEN 1024+1
	word8 LowerLayers[LOWERLAYERS_LEN];
	uword8 Reset;
	word32 AutoDisconnectTime;
	word32 IdleDisconnectTime;
	word32 WarnDisconnectDelay;
#define PPP_USERNAME_LEN 64+1
	word8 Username[PPP_USERNAME_LEN];
#define PPP_PASSWORD_LEN 64+1
	word8 Password[PPP_PASSWORD_LEN];
	uword32 MaxMRUSize; /*unsignedInt(64:65535)*/
#define CONNECTIONTRIGGER_ONDEMAND 0
#define CONNECTIONTRIGGER_ALWAYSON 1
#define CONNECTIONTRIGGER_MANUAL 2
	uword8 ConnectionTrigger; /*OnDemand AlwaysOn  Manual */
	uword8 IPCPEnable; 
	uword8 IPv6CPEnable;
#define PPPOE_ACNAME_LEN 256+1
	word8 PPPoE_ACName[PPPOE_ACNAME_LEN];
	word8 PPPoE_ServiceName[PPPOE_ACNAME_LEN];
	uword8 IPCP_PassthroughEnable;
#define IPCP_PASSTHROUGHDHCPPOOL_LEN 256+1
	word8 IPCP_PassthroughDHCPPool[IPCP_PASSTHROUGHDHCPPOOL_LEN];
} __PACK__ IgdRmtMgtDataTr181PPPInterfaceAttrConfTab;

/*Device.IP.Interface.*/
typedef struct {
	uword32 ulStateAndIndex;
	uword8 Enable;
	word32 instnum;
	word8 Alias[TR181_ALIAS_LEN];
#define LOWERLAYERS_LEN 1024+1
	word8 LowerLayers[LOWERLAYERS_LEN];
 	uword8 IPv4Enable;
 	uword8 IPv6Enable;
 	uword8 ULAEnable;
#define ROUTER_LEN 256+1
 	word8 Router[ROUTER_LEN];
 	uword8 Reset;
 	uword32 MaxMTUSize; /*unsignedInt(64:65535)*/
 	uword8 Loopback;
 	uword8 AutoIPEnable;
} __PACK__ IgdRmtMgtDataTr181IPInterfaceAttrConfTab;

#define IGD_TR369_BRIDGING_BRIDGE_RECORD_NUM (16)
/*Device.Bridging.Bridge.{i}*/
typedef struct {
	uword32 ulStateAndIndex;
	uword8 Enable;
	word8 Alias[TR181_ALIAS_LEN];
#define STANDARD_8021D_2004 0
#define STANDARD_8021Q_2005 1
#define STANDARD_8021Q_2011 2
#define STANDARD_8021D_2004_STR "02.1D-2004"
#define STANDARD_8021Q_2005_STR "802.1Q-2005"
#define STANDARD_8021Q_2011_STR "802.1Q-2011"
	uword8 Standard; /*802.1D-2004 802.1Q-2005 802.1Q-2011 */
	word32 instnum;
} __PACK__ IgdRmtMgtDataTr181BridgingBridgeAttrConfTab;

/*Device.Bridging.Bridge.Port.{i}.*/
typedef struct {
	uword32 ulStateAndIndex;
	uword8 Enable;
	word32 instnum;
	word8 Alias[TR181_ALIAS_LEN];
#define LOWERLAYERS_LEN 1024+1
	word8 LowerLayers[LOWERLAYERS_LEN];
  	uword8 ManagementPort;
#define TYPE_PROVIDERNETWORKPORT 0
#define TYPE_CUSTOMERNETWORKPORT 1
#define TYPE_CUSTOMEREDGEPORT 2
#define TYPE_CUSTOMERVLANPORT 3
#define TYPE_VLANUNAWAREPORT 4
/*
ProviderNetworkPort (Indicates this Port is an S-TAG aware port of a ProviderBridge)
CustomerNetworkPort (Indicates this Port is an S-TAG aware port of a ProviderBridge)
CustomerEdgePort (Indicates this Port is an C-TAG aware port of a ProviderBridge)
CustomerVLANPort (Indicates this Port is an C-TAG aware port of a Customer Bridge)
VLANUnawarePort (Indicates this Port is a VLAN unaware member of an 802.1D-2004 bridge)
*/
  	uword8 Type;
  	uword32 DefaultUserPriority; /*unsignedInt(0:7)*/
#define PRIORITYREGENERATION_LEN 8+7+1
  	uword8 PriorityRegeneration[PRIORITYREGENERATION_LEN]; /*unsignedInt(0:7)[8:8], default 0,1,2,3,4,5,6,7	*/
  	word32 PVID; /*int(1:4094)*/
  	uword32 TPID; 
/*
AdmitAll
AdmitOnlyVLANTagged (OPTIONAL)
AdmitOnlyPrioUntagged (OPTIONAL) For an 802.1D [802.1D-2004] Bridge, the value of this parameter MUST be AdmitAll.
*/
#define ACCEPTABLEFRAMETYPES_ADMITALL 0
#define ACCEPTABLEFRAMETYPES_ADMITONLYVLANTAGGED 1
#define ACCEPTABLEFRAMETYPES_ADMITONLYPRIOUNTAGGED 2
  	uword8 AcceptableFrameTypes;
  	uword8 IngressFiltering;
  	uword8 ServiceAccessPrioritySelection;
#define SERVICEACCESSPRIORITYTRANSLATION_LEN 8+7+1
  	uword8 ServiceAccessPriorityTranslation[SERVICEACCESSPRIORITYTRANSLATION_LEN];  /*unsignedInt(0:7)[8:8], default 0,1,2,3,4,5,6,7	*/
  	uword8 PriorityTagging;
  	word32 bridge_instnum;
} __PACK__ IgdRmtMgtDataTr181BridgingBridgePortAttrConfTab;

/*Device.Bridging.Bridge.{i}.VLAN.{i}*/
typedef struct {
	uword32 ulStateAndIndex;
	uword8 Enable;
	word32 instnum;
	word8 Alias[TR181_ALIAS_LEN];
#define BRIDGE_NAME_LEN 64+1
	word8 Name[BRIDGE_NAME_LEN];
	word32 VLANID; /*1~4094*/
  	word32 bridge_instnum;
} __PACK__ IgdRmtMgtDataTr181BridgingBridgeVlanAttrConfTab;

/*Device.Bridging.Bridge.{i}.VLANPort.{i}*/
typedef struct {
	uword32 ulStateAndIndex;
	uword8 Enable;
	word32 instnum;
	word8 Alias[TR181_ALIAS_LEN];
#define VLAN_LEN 256+1
	word8 VLAN[VLAN_LEN];
#define PORT_LEN 256+1
	word8 Port[PORT_LEN];
	uword8 Untagged;
  	word32 bridge_instnum;
} __PACK__ IgdRmtMgtDataTr181BridgingBridgeVlanPortAttrConfTab;

/*Device.Bridging.Filter.{i}.*/
typedef struct {
	uword32 ulStateAndIndex;
	uword8 Enable;
	word8 Alias[TR181_ALIAS_LEN];
#define BRIDGE_LEN 256+1
	word8 Bridge[BRIDGE_LEN];
#define BRIDEG_FILTER_INTERFACE_LEN 256+1
	word8 Interface[BRIDEG_FILTER_INTERFACE_LEN];
	word32 instnum;
} __PACK__ IgdRmtMgtDataTr181BridgingFilterAttrConfTab;

/*Device.IP.Interface.{i}.IPv6Address.{i}.*/
typedef struct {
	uword32 ulStateAndIndex;
	uword8 Enable;
	word32 instnum;
	word8 Alias[TR181_ALIAS_LEN];
	uword8 IPAddress[CM_IPV6_ADDR_LEN_MAX];
#define ORIGIN
/*
AutoConfigured (Automatically generated. For example, a link-local address as specified by SLAAC [Section 5.3/RFC4862], a global address as specified by SLAAC [Section 5.5/RFC4862], or generated via CPE logic (e.g. from delegated prefix as specified by [RFC3633]), or from ULA /48 prefix as specified by [RFC4193])
DHCPv6 (Assigned by DHCPv6 [RFC3315])
IKEv2 (Assigned by IKEv2 [RFC5996])
MAP (Assigned by MAP [RFC7597], i.e. is this interface’s MAP IPv6 address)
WellKnown (Specified by a standards organization, e.g. the ::1 loopback address, which is defined in [RFC4291])
Static (For example, present in the factory default configuration (but not WellKnown), created by the ACS, or created by some other management entity (e.g. via a GUI)) This parameter is based on ipOrigin from [RFC4293].
*/
#define IP_INTF_IPV6_ORIGIN_AUTOCONFIGURED 0
#define IP_INTF_IPV6_ORIGIN_DHCPV6 1
#define IP_INTF_IPV6_ORIGIN_IKEV2 2
#define IP_INTF_IPV6_ORIGIN_MAP 3 
#define IP_INTF_IPV6_ORIGIN_WELLKNOWN 4 
#define IP_INTF_IPV6_ORIGIN_STATIC 5
	uword8 Origin; /*R*/
	word8 Prefix[CM_IPV6_ADDR_LEN_MAX];
	uword32 PreferredLifetime; /*datetime format : 9999-12-31T23:59:59Z*/
	uword32 ValidLifetime;
	uword8 Anycast;
	uword32 ParentInstnum;
} __PACK__ IgdRmtMgtDataTr181IPInterfaceIPv6AddrAttrConfTab;

/*Device.IP.Interface.{i}.IPv6Prefix.{i}.*/
typedef struct {
	uword32 ulStateAndIndex;
	uword8 Enable;
	word32 instnum;
	word8 Alias[TR181_ALIAS_LEN];
	word8 Prefix[CM_IPV6_ADDR_LEN_MAX];
	word8 PrefixLen;
/*
AutoConfigured (Generated via internal CPE logic (e.g. the ULA /48 prefix) or derived from an internal prefix that is not modeled in any IPv6Prefix table)
PrefixDelegation (Delegated via DHCPv6 [RFC3633] or some other protocol, e.g. IPv6rd [RFC5969]. Also see StaticType)
RouterAdvertisement (Discovered via router advertisement [RFC4861] Prefix Information Option)
WellKnown (Specified by a standards organization, e.g. fe80::/10 for link-local addresses, or ::1/128 for the loopback address, both of which are defined in [RFC4291])
Static (Created by the ACS, by some other management entity (e.g. via a GUI), or present in the factory default configuration (but not WellKnown). Unrelated to any shorter length prefix that might exist on the CPE. Also see StaticType. Can be used for RA (Prefix Information), DHCPv6 address assignment (IA_NA) or DHCPv6 prefix delegation (IA_PD))
Child (Derived from an associated AutoConfigured or PrefixDelegation parent prefix. Also see StaticType, ParentPrefix and ChildPrefixBits. Can be used for RA (Prefix Information), DHCPv6 address assignment (IA_NA) or DHCPv6 prefix delegation (IA_PD)) Note that:
PrefixDelegation and RouterAdvertisement prefixes can exist only on upstream interfaces (i.e. interfaces for which the physical layer interface object has Upstream = true),
AutoConfigured and WellKnown prefixes can exist on any interface, and
Static and Child prefixes can exist only on downstream interfaces (i.e. interfaces for which the physical layer interface object has Upstream = false). Also note that a Child prefix’s ParentPrefix will always be an AutoConfigured, PrefixDelegation, or RouterAdvertisement prefix. This parameter is based on ipAddressOrigin from [RFC4293].
*/
#define IP_INTF_IPV6_PREFIX_ORIGIN_AUTOCONFIGURED 0
#define IP_INTF_IPV6_PREFIX_ORIGIN_PREFIXDELEGATION 1
#define IP_INTF_IPV6_PREFIX_ORIGIN_ROUTERADVERTISEMENT 2
#define IP_INTF_IPV6_PREFIX_ORIGIN_WELLKNOWN 3
#define IP_INTF_IPV6_PREFIX_ORIGIN_STATIC 4
#define IP_INTF_IPV6_PREFIX_ORIGIN_CHILD 5
	uword8 Origin;
/*
Static (Prefix is a “normal” Static prefix)
Inapplicable (Prefix is not Static, so this parameter does not apply, READONLY)
PrefixDelegation (Prefix will be populated when a PrefixDelegation prefix needs to be created)
Child (Prefix will be populated when a Child prefix needs to be created. In this case, the ACS needs also to set ParentPrefix and might want to set ChildPrefixBits (if parent prefix is not set, or goes away, then the child prefix will become operationally disabled)) This mechanism works as follows:
When this parameter is set to PrefixDelegation or Child, the instance becomes a “prefix slot” of the specified type.
Such an instance can be administratively enabled (Enable = true) but will remain operationally disabled (Status = Disabled) until it has been populated.
When a new prefix of of type T is needed, the CPE will look for a matching unpopulated instance, i.e. an instance with (Origin,StaticType,Prefix) = (Static,T,““). If the CPE finds at least one such instance it will choose one and populate it. If already administratively enabled it will immediately become operationally enabled. If the CPE finds no such instances, it will create and populate a new instance with (Origin,StaticType) = (T,T). If the CPE finds more than one such instance, the algorithm via which it chooses which instance to populate is implementation-specific.
When a prefix that was populated via this mechanism becomes invalid, the CPE will reset Prefix to an empty string. This does not affect the value of the Enable parameter. The prefix StaticType can only be modified if Origin is Static.
*/
#define STATICTYPE_STATIC 0
#define STATICTYPE_INAPPLICABLE 1
#define STATICTYPE_PREFIXDELEGATION 2
#define STATICTYPE_CHILD 3
	uword8 StaticType;
	word8 ParentPrefix[CM_IPV6_ADDR_LEN_MAX];
	word8 ChildPrefixBits[CM_IPV6_ADDR_LEN_MAX];
	uword8 OnLink;
	uword8 Autonomous;
	uword8 PreferredLifetime;
	uword32 ValidLifetime;
	uword32 ParentInstnum;
} __PACK__ IgdRmtMgtDataTr181IPInterfaceIPv6PrefixAttrConfTab;

/*Device.DHCPv6.Client.{i}.*/
typedef struct {
	uword32 ulStateAndIndex;
	uword8 Enable;
	word8 Alias[TR181_ALIAS_LEN];
	uword8 RequestAddresses;
	uword8 RequestPrefixes;
	uword8 RapidCommit;
	word32 SuggestedT1;
	word32 SuggestedT2;
#define REQUESTEDOPTIONS_LEN 64
	word8 requestedOptions[REQUESTEDOPTIONS_LEN];
	word32 IPInstnum;
	word32 instnum;
} __PACK__ IgdRmtMgtDataTr181DHCPv6ClientAttrConfTab;

/*Device.DHCPv6.Server.Pool.{i}.*/
typedef struct {
	uword32 ulStateAndIndex;
	uword8 Enable;
	word8 Alias[TR181_ALIAS_LEN];
	word32 Order;
	word8 *DUID; /*hexBinary(:130)*/
	uword8 DUID_len;
	uword8 DUIDExclude;
	word8 *VendorClassID; /*hexBinary(:65535)*/
	uword8 VendorClassID_len;
	uword8 VendorClassIDExclude;
	word8 *UserClassID;
	uword8 UserClassID_len;
	uword8 UserClassIDExclude;
	word8 SourceAddress[CM_IPV6_ADDR_LEN_MAX];
	word8 SourceAddressMask[CM_IPV6_ADDR_LEN_MAX];
	uword8 SourceAddressExclude;
	uword8 IANAEnable;
	word8 IANAManualPrefixes[CM_IPV6_ADDR_LEN_MAX*8]; /*string[:8]  8 items*/
	word8 IANAPrefixes[CM_IPV6_ADDR_LEN_MAX*8];
	uword8 IAPDEnable;
	word8 IAPDManualPrefixes[CM_IPV6_ADDR_LEN_MAX*8];
	uword8 IAPDPrefixes[CM_IPV6_ADDR_LEN_MAX*8];
	uword32 IAPDAddLength; /*unsignedInt(:64)*/
	word32 IPInstnum;
	word32 instnum;
} __PACK__ IgdRmtMgtDataTr181DHCPv6ServerPoolAttrConfTab;


/*Device.DHCPv6.Server.Pool.{i}.Client.{i}.*/
typedef struct {
	uword32 ulStateAndIndex;
	uword8 Enable;
	word8 Alias[TR181_ALIAS_LEN];
	uword8 IAAddr[CM_IPV6_ADDR_LEN_MAX];
	word8 *DUID;
	uword8 DUID_len;
	uword32 ActiveTime;
	word8 Pool_InstNum;
	word8 client_InstNum;
} __PACK__ IgdRmtMgtDataTr181DHCPv6ServerPoolClientAttrConfTab;

/*Device.DSLite.InterfaceSetting.{i}.*/
typedef struct {
	uword32 ulStateAndIndex;
	uword8 Enable;
	word8 Alias[TR181_ALIAS_LEN];
	uword8 AssignmentPrecedence;
	uword8 AddressTypePrecedence;
	uword8 fqdn;
	uword8 IPAddress[CM_IPV6_ADDR_LEN_MAX];
	uword32 TunnelIntf_IPInstnum;
	uword32 TunneledIntf_IPInstnum;
	word8 instnum;
} __PACK__ IgdRmtMgtDataTr181DDSLiteInterfaceSettingAttrConfTab;

/*Device.NeighborDiscovery.InterfaceSetting.{i}.*/
typedef struct {
	uword32 ulStateAndIndex;
	uword8 Enable;
	word8 Alias[TR181_ALIAS_LEN];
#define NDISC_INTERFACE_LEN 256+1
	word8 Interface[NDISC_INTERFACE_LEN];
	uword32 NS_RtrInterval;
	uword32 RS_RtrInterval;
	uword32 MaxRtrRS;
	uword8 NUDEnable;
	uword8 RSEnable;
	uword32 DADTransmits;
	word32 instnum;
} __PACK__ IgdRmtMgtDataTr181NeighborDiscoveryInterfaceSettingAttrConfTab;

/*Device.ManagementServer.InformParameter.{i}.*/
typedef struct {
	uword32 ulStateAndIndex;
	uword8 Enable;
	word8 Alias[TR181_ALIAS_LEN];
#define PARAMETERNAME_LEN 256 + 1
	word8 ParameterName[PARAMETERNAME_LEN];
	uword32 EventList; /*Comma-separated list of strings. */
	word32 instnum;
} __PACK__ IgdRmtMgtDataTr181ManagementServerInformParameterAttrConfTab;
#endif
