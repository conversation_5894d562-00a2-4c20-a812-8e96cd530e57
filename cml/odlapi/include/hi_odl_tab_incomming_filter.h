/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm global obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_INCOMMING_FILTER_H
#define HI_ODL_TAB_INCOMMING_FILTER_H
#include "hi_odl_basic_type.h"

#define IGD_DBUS_INCOMING_FILTER_RECORD_NUM (100)

typedef struct {
	uword32 ulStateAndIndex;

	uword32 entry_index;
	uword8 ucKey;

#define DBUS_INCOMING_FILTE_PROTOCOL_TCP (0)
#define DBUS_INCOMING_FILTE_PROTOCOL_UDP (1)
#define DBUS_INCOMING_FILTE_PROTOCOL_ALL (2)	/*表示TCP&UDP*/
	uword8 ucProtocol;

#define DBUS_INCOMING_FILTER_INTERFACE_WAN (0)
#define DBUS_INCOMING_FILTER_INTERFACE_LAN (1)
	uword8 ucInterface;
	uword8 pad;


#define DBUS_INCOMING_FILTER_PORT_NUM_MIN (0)
#define DBUS_INCOMING_FILTER_PORT_NUM_MAX (65535)
	uword32 ulPort;

#define DBUS_INCOMING_IP_MAX_LEN (64)
	word8 aucRemoteIP[DBUS_INCOMING_IP_MAX_LEN];		/*远端HOST IP地址, 如果为""表示任何IP*/

#define DBUS_INCOMING_ATTR_MASK_BIT0_PROTOCOL  		 (0x01)
#define DBUS_INCOMING_ATTR_MASK_BIT1_PORT  		 	 (0x02)
#define DBUS_INCOMING_ATTR_MASK_BIT2_INTERFACE  		 (0x04)
#define DBUS_INCOMING_ATTR_MASK_BIT3_REMOTEIP  		 (0x08)
#define DBUS_INCOMING_ATTR_MASK_ALL 					 (0x0f)

	uword32 ulBitmap;

} __PACK__ IgdDbusIncomingFilterAttrConfigTab;


/***************************incoming过滤表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define INCOMING_FILTER_IP_ADDRESS_LEN (40)
	uword8 aucRemoteIP[INCOMING_FILTER_IP_ADDRESS_LEN];
#define INCOMING_FILTER_PROTOCOL_TCP (0)
#define INCOMING_FILTER_PROTOCOL_UDP (1)
#define INCOMING_FILTER_PROTOCOL_TCP_UDP (2)
	uword8 ucIPProtocol;/*协议类型*/
#define INCOMING_FILTER_WAN (0)
#define INCOMING_FILTER_LAN (1)
	uword8 ucInterface;
#define INCOMING_FILTER_ADD (0)
#define INCOMING_FILTER_DEL (1)
	uword8 ucMethodType;
	uword8 ucPad;
	uword32 ulFilterPort;

#define INCOMING_FILTER_LIST_ATTR_MASK_BIT0_REMOTE_IP        (0x01)
#define INCOMING_FILTER_LIST_ATTR_MASK_BIT1_FILETER_PROTOCOL (0x02)
#define INCOMING_FILTER_LIST_ATTR_MASK_BIT2_INTERFACE        (0x04)
#define INCOMING_FILTER_LIST_ATTR_MASK_BIT3_METHOD_TYPE      (0x08)
#define INCOMING_FILTER_LIST_ATTR_MASK_BIT4_FILETER_PORT     (0x10)
#define INCOMING_FILTER_LIST_ATTR_MASK_ALL (0x1f)
	uword32 ulBitmap;
} __PACK__ IgdSecurIncomingFilterConfTab;

#endif
