/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author: HSAN
 * Create: 2023-12-18
 */
#ifndef HI_ODL_TAB_PROBE_RSP_TX_VSIE_MGR_H
#define HI_ODL_TAB_PROBE_RSP_TX_VSIE_MGR_H
#include "hi_odl_basic_type.h"

typedef struct {
	uword32 state_and_index;
	uword32 max_entry_num;
	uword32 current_entry_num;
	uword32 bitmap;

#define IGD_ATTR_MASK_PROBE_RSP_TX_VSIE_MGR_MAX (0x01)
#define IGD_ATTR_MASK_PROBE_RSP_TX_VSIE_MGR_CURRENT (0x02)
#define IGD_ATTR_MASK_PROBE_RSP_TX_VSIE_MGR_ALL (0xffff)

} __PACK__ IgdVSIEProbeRspTxVSIEMgrAttrConfTab;

#endif
