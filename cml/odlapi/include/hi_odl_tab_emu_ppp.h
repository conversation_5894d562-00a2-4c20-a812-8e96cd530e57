/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm emu ppp obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_EMU_PPP_H
#define HI_ODL_TAB_EMU_PPP_H
#include "hi_odl_basic_type.h"

typedef struct {
	uword32 ulStateAndIndex;

	word8 aucUsername[CM_USERNAME_LEN];
	word8 aucPassword[CM_USERNAME_LEN];
#define PPPOE_EMULATOR_DIAGNOSTIC_WANIF_NAME_LEN (256)
	word8 aucWANInterface[PPPOE_EMULATOR_DIAGNOSTIC_WANIF_NAME_LEN];
#define PPPOE_EMULATOR_DIAGNOSTIC_PROTOCOL_CHAP (0)
#define PPPOE_EMULATOR_DIAGNOSTIC_PROTOCOL_PAP (1)
#define PPPOE_EMULATOR_DIAGNOSTIC_PROTOCOL_AUTO (2)
	uword8 ucPPPAuthenticationProtocol;

#define PPPOE_EMULATOR_DIAGNOSTIC_STATE_NONE (0)
#define PPPOE_EMULATOR_DIAGNOSTIC_STATE_START (1)
#define PPPOE_EMULATOR_DIAGNOSTIC_STATE_STOP (2)
#define PPPOE_EMULATOR_DIAGNOSTIC_STATE_COMPLETE (3)
#define PPPOE_EMULATOR_DIAGNOSTIC_STATE_RUNNING (4)
	uword8 ucState;

#define PPPOE_EMULATOR_DIAGNOSTIC_RETRY_TIMES_MIN (0)
#define PPPOE_EMULATOR_DIAGNOSTIC_RETRY_TIMES_MAX (3)
	uword8 ucRetryTimes;
#define PPPOE_EMULATOR_DIAGNOSTIC_RESULT_SUCC (0)
#define PPPOE_EMULATOR_DIAGNOSTIC_RESULT_NEGOFAIL (1)
#define PPPOE_EMULATOR_DIAGNOSTIC_RESULT_AUTHFAIL (2)
#define PPPOE_EMULATOR_DIAGNOSTIC_RESULT_TIMEOUT (3)
#define PPPOE_EMULATOR_DIAGNOSTIC_RESULT_USERSTOP (4)
#define PPPOE_EMULATOR_DIAGNOSTIC_RESULT_UNKNOWN (5)
	uword8 ucResult;
	uword32 ulPPPSessionID;
	word8 aucExternalIPAddress[CM_IP_ADDR_STRING_LEN];
	word8 aucDefaultGateway[CM_IP_ADDR_STRING_LEN];

#define PPPOE_EMULATOR_ERROR_CODE_LEN (64)
	word8 aucErrorCode[PPPOE_EMULATOR_ERROR_CODE_LEN];
	uword8 ucWANPPPConnectionIPMode;
	uword8 ucDslite_Enable;
	uword8 aucPad[CM_TWO_PADS];
	uword32 ulAftrAddress;
#define PPPOE_EMULATOR_IPV6_MAX_LEN (256)
	word8 aucWANIPv6IPAddress[PPPOE_EMULATOR_IPV6_MAX_LEN];
	word8 aucWANIPv6DNSServers[PPPOE_EMULATOR_IPV6_MAX_LEN];
	word8 aucWANIPv6Prefix[PPPOE_EMULATOR_IPV6_MAX_LEN];
	word8 aucWANDefaultIPv6Gateway[PPPOE_EMULATOR_IPV6_MAX_LEN];
	word8 aucLANIPv6Prefix[PPPOE_EMULATOR_IPV6_MAX_LEN];
#define PPPOE_EMULATOR_DIAGNOSTIC_LANIF_NAME_LEN (256)
	uint8_t lan_interface[PPPOE_EMULATOR_DIAGNOSTIC_LANIF_NAME_LEN];
#define PPPOE_EMULATOR_ATTR_MASK_BIT0_STATE (0x01)
#define PPPOE_EMULATOR_ATTR_MASK_BIT1_USERNAME (0x02)
#define PPPOE_EMULATOR_ATTR_MASK_BIT2_PASSWORD (0x04)
#define PPPOE_EMULATOR_ATTR_MASK_BIT3_WANIF (0x08)
#define PPPOE_EMULATOR_ATTR_MASK_BIT4_PROTOCOL (0x10)
#define PPPOE_EMULATOR_ATTR_MASK_BIT5_TIMES (0x20)
#define PPPOE_EMULATOR_ATTR_MASK_BIT6_RESULT (0x40)
#define PPPOE_EMULATOR_ATTR_MASK_BIT7_SESSIONID (0x80)
#define PPPOE_EMULATOR_ATTR_MASK_BIT8_IP (0x100)
#define PPPOE_EMULATOR_ATTR_MASK_BIT9_GATEWAY (0x200)
#define PPPOE_EMULATOR_ATTR_MASK_BIT_ERROR_CODE (0x400)
#define PPPOE_EMULATOR_ATTR_MASK_BIT_IPMODE (0x800)
#define PPPOE_EMULATOR_ATTR_MASK_BIT_DSLITE (0x1000)
#define PPPOE_EMULATOR_ATTR_MASK_BIT_AFTRADDR (0x2000)
#define PPPOE_EMULATOR_ATTR_MASK_BIT_IPV6ADDR (0x4000)
#define PPPOE_EMULATOR_ATTR_MASK_BIT_IPV6DNS (0x8000)
#define PPPOE_EMULATOR_ATTR_MASK_BIT_IPV6PREFIX (0x10000)
#define PPPOE_EMULATOR_ATTR_MASK_BIT_IPV6GATEWAY (0x20000)
#define PPPOE_EMULATOR_ATTR_MASK_BIT_LANIPV6PREFIX (0x40000)
#define PPPOE_EMULATOR_ATTR_MASK_LAN_INTERFACE (0x80000)
#define PPPOE_EMULATOR_ATTR_MASK_ALL (0xfffff)
	//#define PPPOE_EMULATOR_ATTR_MASK_ALL (0x3f)
	uword32 ulBitmap;
} __PACK__ IgdPMPPPOEEmulatorAttrConfTab;

#endif
