/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm tab l2detect obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_L2DETECT_H
#define HI_ODL_TAB_L2DETECT_H
#include "hi_odl_basic_type.h"

enum
{
    L2DETECT_STATE_NONE_E  = 0,
    L2DETECT_STATE_START_E  = 1,
    L2DETECT_STATE_STOP_E  = 2,
    L2DETECT_STATE_COMPLETE_E  = 3,
    L2DETECT_STATE_RUNNING_E  = 4,
    L2DETECT_STATE_NUM_E,
};

typedef struct
{
    uword32  ulStateAndIndex;
    uword32  ulState;
    word8    acResult[256];
    uword32  ulPeriod;
    uword32  ulFreq;
#define L2DETECT_ATTR_MASK_BIT0_PERIOD              (0x01)
#define L2DETECT_ATTR_MASK_BIT1_FREQ                (0x02)
#define L2DETECT_ATTR_MASK_ALL                      (0x03)
	uword32  ulBitmap;
} __PACK__ IgdCmL2DetectAttrConfTab;

#endif
