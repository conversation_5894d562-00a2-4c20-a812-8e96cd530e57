/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: cm tab special service vr obj attribute
 * Author: HSAN
 * Create: 2023-12-18
 */
#ifndef HI_ODL_TAB_SPECIAL_SERVICE_VR_H
#define HI_ODL_TAB_SPECIAL_SERVICE_VR_H
#include "hi_odl_basic_type.h"

#define IGD_SPECIAL_SERVICE_VR_INDEX_LEN (8)
#define IGD_SPECIAL_SERVICE_VR_WANNAME_LEN (64)
#define IGD_SPECIAL_SERVICE_VR_IFNAME_LEN (32)
#define IGD_SPECIAL_SERVICE_VR_IPFORWARDLIST_LEN (128) /* TODO: 格式及长度同云网超宽X_CT-COM_IPForwardList要求*/
#define IGD_SPECIAL_SERVICE_VR_MAX_ENTRY (16)
typedef struct {
	uword32 state_and_index;
	uword32 entry_index;
	char index[IGD_SPECIAL_SERVICE_VR_INDEX_LEN];
	char wan_name[IGD_SPECIAL_SERVICE_VR_WANNAME_LEN];
	char ifname[IGD_SPECIAL_SERVICE_VR_IFNAME_LEN];
	char ip_forward_list[IGD_SPECIAL_SERVICE_VR_IPFORWARDLIST_LEN];

#define IGD_ATTR_MASK_BIT_SPECIAL_SERVICE_VR_INDEX   (0x01)
#define IGD_ATTR_MASK_BIT_SPECIAL_SERVICE_VR_WANNAME (0x02)
#define IGD_ATTR_MASK_BIT_SPECIAL_SERVICE_VR_IFNAME  (0x04)
#define IGD_ATTR_MASK_BIT_SPECIAL_SERVICE_VR_IPFORWARDLIST (0x08)
#define IGD_ATTR_MASK_BIT_SPECIAL_SERVICE_VR_ALL (0xff)

	uword32 bitmap;
} __PACK__ IgdSpecialSeriveVRAttrConfTab;

#endif
