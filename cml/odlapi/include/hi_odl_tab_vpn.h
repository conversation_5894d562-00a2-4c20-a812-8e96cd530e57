#ifndef HI_ODL_TAB_VPN_H
#define HI_ODL_TAB_VPN_H

#include <igdGlobalTypeDef.h>
#include "hi_uspace.h"

#define VPN_WAN_NAME_LEN (64)
#define VPN_USER_ID_LEN (64)
#define VPN_TUNNEL_NAME_LEN (64)
#define IGD_VPN_CFG_NUM (6)
#define IGD_VPN_ATTACH_NUM (6)
#define VPN_L2TP_DEFAULT_PORT 1701
#define VPN_PPTP_DEFAULT_PORT 1723
#define HI_VPN_PORT_RULE_DEL 0
#define HI_VPN_PORT_RULE_ADD 1
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulVPNTunlIndex;
#define VPN_TYPE_STR_L2TP "l2tp"
#define VPN_TYPE_STR_PPTP "pptp"
#define VPN_TYPE_LEN (16)
	word8 aucVPNType[VPN_TYPE_LEN]; /* l2tp or pptp */
	uword8 ucVPNEnable; /* 1使能; 0 去使能，保留配置且关闭VPN通道 */
	uword8 ucVPNPriority; /* 取值0-7，值越小优先级越高 */
	uword8 aucPad[2];
#define VPN_MODE_STR_RANDOM "random"
#define VPN_MODE_STR_STEADY "steady"
#define VPN_MODE_LEN (16)
	word8 aucVPNMode[VPN_MODE_LEN]; /* random or steady */
	uword8 aucVPNUserId[VPN_USER_ID_LEN]; /* 由平台分配，用于区分VPN业务的提供者，标识VPN通道 */
	word8 aucVPNTunnelName[VPN_TUNNEL_NAME_LEN];
	word8 aucVPNWanName[VPN_WAN_NAME_LEN];
	uword32 ulVPNIdleTime;
#define VPN_ADDR_LEN (64)
	word8 aucVPNAddress[VPN_ADDR_LEN];
#define VPN_ACCOUNT_PROXY_LEN (64)
	word8 aucVPNAccountProxy[VPN_ACCOUNT_PROXY_LEN]; /* 获取动态账号密码的地址 */
#define VPN_ACCOUNT_LEN (64)
	word8 aucVPNAccountName[VPN_ACCOUNT_LEN] ;
#define VPN_ACCOUNT_PWD_LEN (260)
	word8  aucVPNPassWord[VPN_ACCOUNT_PWD_LEN] ;
	uword32 ulVPNPort ;
#define VPN_MASK_BIT0_TYPE (0x01)
#define VPN_MASK_BIT1_ENABLE (0x02)
#define VPN_MASK_BIT2_MODE (0x04)
#define VPN_MASK_BIT3_PRIORITY (0x08)
#define VPN_MASK_BIT4_USERID (0x10)
#define VPN_MASK_BIT5_TUNNEL_NAME (0x20)
#define VPN_MASK_BIT6_WAN_NAME (0x40)
#define VPN_MASK_BIT7_IDLE_TIME (0x80)
#define VPN_MASK_BIT8_ADDRESS (0x100)
#define VPN_MASK_BIT9_ACCOUNT_PROXY (0x200)
#define VPN_MASK_BIT10_ACCOUNT_NAME (0x400)
#define VPN_MASK_BIT11_ACCOUNT_PASSWORD (0x800)
#define VPN_MASK_BIT12_PORT (0x1000)
	uword32 ulBitmap;
} __PACK__ igdCmVpnAttrCfgTab;

#define VPN_ATTACH_TEMP_FILE_NAME_LENGTH (32)   /* 临时文件的名字长度 */
#define VPN_ATTACH_TEMP_FILE_DOMAIN "vpn_domain"
#define VPN_ATTACH_TEMP_FILE_IP "vpn_ip"
#define VPN_ATTACH_TEMP_FILE_MAC "vpn_mac"

/* 电信规格变大,attach内容采用文件方式,
* dbus设置的时候: cm把/tmp文件拷贝到/config/work/下面,修改为其它文件名,然后删除dbus创建的文件，debug模式的时候不删除
* dbus读取的时候: 直接读取/config/work/下面的文件
*/
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulAttachIndex;
	word8 aucVPNTunnelName[VPN_TUNNEL_NAME_LEN];
#define VPN_ATTACH_MODE_BOTH (0)
#define VPN_ATTACH_MODE_DESTINATION (1)
#define VPN_ATTACH_MODE_SOURCE_MAC (2)
	uword8 ucVPNAttachMode;  /* 关联模型（1：按目的地址；2：按源MAC地址） */
	uword8 aucPad[3];
	word8 aucAttachDomainContextFile[VPN_ATTACH_TEMP_FILE_NAME_LENGTH]; /* 由于规格太大，修改为通过文件传输 */
	word8 aucAttachIPContextFile[VPN_ATTACH_TEMP_FILE_NAME_LENGTH];  /* 由于规格太大，修改为通过文件传输 */
	word8 aucAttachMACContextFile[VPN_ATTACH_TEMP_FILE_NAME_LENGTH]; /* 由于规格太大，修改为通过文件传输 */
	uword32 ulVPNTunlIndex; /* 属于那个VPN通道，跟tunnel name一样，移动不指定tunnel name所以就用index标识该通道 */
#define VPN_ATTACH_MASK_BIT1_TUNNEL_NAME (0x01)
#define VPN_ATTACH_MASK_BIT2_MODE  (0x02)
#define VPN_ATTACH_MASK_BIT3_DOMAIN_CONTEXT (0x04)
#define VPN_ATTACH_MASK_BIT4_IP_CONTEXT (0x08)
#define VPN_ATTACH_MASK_BIT5_MAC_CONTEXT (0x10)
#define VPN_ATTACH_MASK_BIT6_VPNINDEX  (0x20)
	uword32 ulBitmap;
} __PACK__ igdCmVpnAttachCfgTab;

#define VPN_HITIP_LIST_CONTEXT_LEN 512
#define HI_CM_STATIC_IP_FILE "/tmp/vpnipsstats_" /* 和hi_vpn.c保持一致 */
#define HI_CM_STATIC_DOMAIN_FILE "/tmp/vpndomainsstats_" /* 和hi_vpn.c保持一致 */
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulVPNIndex;
	uword32 ulVPNAttachIndex;
#define HI_VPN_STATE_UP 0
#define HI_VPN_STATE_DOWN 1
	uword32 ulVPNStatus;   /* VPN的状态 0: 正常 1: 不正常 */
	uword32 ulmark; /* 0:没有统计 1:ip 2: domain 3:ip+domain */
#define VPN_STATE_MASK_BIT1_STATUS (0x01)
#define VPN_STATE_MASK_BIT2_STATS (0x02)
	uword32 ulBitmap;
} __PACK__ igdCmVpnAttrStateCfgTab;

#endif