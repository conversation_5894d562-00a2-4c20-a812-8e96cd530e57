/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm tab user limit obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_VOICE_H
#define HI_ODL_TAB_VOICE_H
#include "hi_odl_basic_type.h"

/***************************语音能力集信息*********************************/
typedef struct {
	uword32 ulStateAndIndex;
	uword8 ucprotocolChanegSupport;
#define VOICE_HEARTBEAT_SUPPORT_NO (0)
#define VOICE_HEARTBEAT_SUPPORT_YES (1)
	uword8 ucHeartbeatSupport;/*是否支持网关和软交换间心跳，默认：支持*/
#define VOICE_DIGITMAP_SUPPORT_NO (0)
#define VOICE_DIGITMAP_SUPPORT_YES (1)
	uword8 ucDigitMapSupport;/*是否支持拨号计划，默认支持*/
#define VOICE_SUPPORT_SIP (1)
#define VOICE_SUPPORT_H248 (2)
#define VOICE_SUPPORT_BOTH (3)
	uword8 ucSignalingProtocols;/*支持的语音通信协议，默认SIP和H248都支持*/
#define VOICE_SUPPORT_LINE_NUM_MIN (1)
#define VOICE_SUPPORT_LINE_NUM_MAX (2)
	uword8 ucSupportLineNum; /*支持的语音线路个数，默认值:1*/

#define VOICE_SUPPORT_LINE_CODEC_NUM_MIN (1)
#define VOICE_SUPPORT_LINE_CODEC_NUM_MAX (16)
	uword8 ucSupportLineCodecNum; /*每条线路支持的Codec个数，默认值:4*/
	uword8 aucPad1[CM_TWO_PADS];
	uword32 ucSupportMaxProfileCount; /*支持的不同语音配置文件类型的最大总数*/
	uword32 ucSupportMaxLineCount; /*所有配置文件支持的线路的最大总数*/
	uword32 ucSupportMaxSessionsPerLine; /*支持的通过所有配置文件给定的线路的最大语音会话的数量*/
	uword32 ucSupportMaxSessionsCount;
#define VOICE_SUPPORT_REGIONS_LEN_MAX (256)
	word8 ucSupportRegions[VOICE_SUPPORT_REGIONS_LEN_MAX];
	uword8 ucSupportRTCP;
	uword8 ucSupportSRTP;
	uword8 ucRTPRedundancy;
	uword8 ucDSCPCoupled;
	uword8 ucEthernetTaggingCoupled;
	uword8 ucPSTNSoftSwitchOver;
	uword8 ucSupportFaxT38;
	uword8 ucSupportFaxPassThrough;
	uword8 ucSupportModemPassThrough;
	uword8 ucSupportToneGeneration;
	uword8 ucSupportRingGeneration;
	uword8 ucSupportNumberingPlan;
	uword8 ucSupportButtonMap;
	uword8 ucSupportVoicePortTests;
	uword8 aucPad2[CM_TWO_PADS];

#define VOICE_CAPBILITY_ATTR_MASK_BIT0_HEARTBEAT_SUP (0x01)
#define VOICE_CAPBILITY_ATTR_MASK_BIT1_DIGITMAP_SUP (0x02)
#define VOICE_CAPBILITY_ATTR_MASK_BIT2_PROTOCOL_SUP (0x04)
#define VOICE_CAPBILITY_ATTR_MASK_BIT3_LINE_NUM_SUP (0x08)
#define VOICE_CAPBILITY_ATTR_MASK_BIT4_CODEC_NUM_SUP (0x10)
#define VOICE_CAPBILITY_ATTR_MASK_ALL (0x07)
	uword32 ulBitmap;
} __PACK__ IgdVoiceCapbilityInfoTab;


/***************************语音Codec能力集信息*********************************/
#define IGD_VOICE_CODEC_RECORD_NUM (4)

typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulCodecNum;

#define VOICE_CODEC_NUM_MAX (16)

#define VOICE_CODEC_TYPE_G711A (1)
#define VOICE_CODEC_TYPE_G711M (2)
#define VOICE_CODEC_TYPE_G729 (3)
#define VOICE_CODEC_TYPE_G722 (4)
#define VOICE_CODEC_TYPE_G726 (5)
#define VOICE_CODEC_TYPE_G729A (6)
#define VOICE_CODEC_TYPE_G729E (7)
#define VOICE_CODEC_TYPE_G728 (8)
#define VOICE_CODEC_TYPE_G723_1 (9)
#define VOICE_CODEC_TYPE_G722_1 (10)
#define VOICE_CODEC_TYPE_G722_2 (11)
#define VOICE_CODEC_TYPE_GSM_FR (12)
#define VOICE_CODEC_TYPE_GSM_HR (13)
#define VOICE_CODEC_TYPE_GSM_EFR (14)
#define VOICE_CODEC_TYPE_GSM_ILBC (15)
	uword8 aucCodec[VOICE_CODEC_NUM_MAX];/*Codec类型*/

#define VOICE_CODEC_PACKETIZATION_PERIOD_LEN (32)
	word8 aucPacketizationPeriod[VOICE_CODEC_PACKETIZATION_PERIOD_LEN];/*打包间隔*/
	uword32 ulBitRate;
#define VOICE_CODEC_SILENCE_SUPPRESSION_DISABLE (0)
#define VOICE_CODEC_SILENCE_SUPPRESSION_ENABLE (1)
	uword8 ucSilenceSuppression;
	uword8 aucPad[CM_THREE_PADS];

#define VOICE_CODEC_CAPBILITY_ATTR_MASK_BIT0_TYPE (0x01)
#define VOICE_CODEC_CAPBILITY_ATTR_MASK_BIT1_PACKETIZATION_PERIOD (0x01)
#define VOICE_CODEC_CAPBILITY_ATTR_MASK_BITRATE (0x1 << 2)
#define VOICE_CODEC_CAPBILITY_ATTR_MASK_SLIENCE_SUPPRESSION (0x1 << 3)
#define VOICE_CODEC_CAPBILITY_ATTR_MASK_ALL ((0x1 << 4) - 1)
	uword32 ulBitmap;
} __PACK__ IgdVoiceCodecCapbilityInfoTab;

/***************************用户注册信息*********************************/
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucLineNum; /*线路编号，只有1路，固定为：1*/
#define USER_REG_SATUS_CONFIG_NOT_FINISHED (0)
#define USER_REG_SATUS_CONFIG_DISABLE (1)
#define USER_REG_SATUS_REGISTERING (2)
#define USER_REG_SATUS_REG_SUCCESS (3)
#define USER_REG_SATUS_REG_FAILED (4)
	uword8 ucRegStatus;/*未完成配置/未启用/注册中/注册成功/ 注册失败*/
#define HOOK_STATUS_IDLE (0)
#define HOOK_STATUS_USING (1)
	uword8 ucHookStatus; /*摘挂机状态，空闲、通话中*/
#define USER_REG_ERR_OLT_REG_FAIED (0) 					/* OLT注册失败 */
#define USER_REG_ERR_NO_VOICE_CHANNEL (1) 				/* 无语音通道 */
#define USER_REG_ERR_VOICE_CHANNEL_GET_IP_FAILED (2) 	/* 语音通道获取IP地址失败 */
#define USER_REG_ERR_SERVER_NO_RESPONSE (3) 			/* 服务器地址不可达 */
#define USER_REG_ERR_CONFIG_INFO_INCOMPLETE (4) 		/* 配置信息不全 */
#define USER_REG_ERR_USER_FORBID (5)					/* 用户被禁用 */
#define USER_REG_ERR_REG_AUTH_FAILED (6)				/* 注册鉴权失败 */
#define USER_REG_ERR_REG_TIMEOUT (7)					/* 注册超时 */
#define USER_REG_ERR_SERVER_ERROR_RESPONSE (8)			/* 服务器返回错误响应 */
#define USER_REG_ERR_UNKNOWN (9)						/* 未知错误原因 */
#define USER_REG_NO_ERR_REGSTING (10)					/* 注册中 */
#define USER_REG_ERR_DNS_DOMAIN (11)					/* 域名解析失败 */
#define USER_REG_NO_ERR_SUCCESS (99)					/* 注册成功 */
	uword8 ucRegErrInfo;

#define USER_REG_STATUS_MASK_BIT0_REG_STATUS (0x01)
#define USER_REG_STATUS_MASK_BIT1_HOOK_STATUS (0x02)
#define USER_REG_STATUS_MASK_BIT2_REG_ERR_INFO (0x04)
#define USER_REG_STATUS_MASK_ALL (0x07)
	uword32 ulBitmap;
} __PACK__ IgdVoiceUserRegInfoTab;

/***************************语音通用属性表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define VOICE_SERVER_TYPE_IMS_SIP (0)
#define VOICE_SERVER_TYPE_SOFT_SWITCHING (1)
#define VOICE_SERVER_TYPE_H248 (2)
	uword8 ucVoiceServerType;/*协议类型：IMS/软交换/H.248*/
#define VOICE_DIGIT_MAP_DISABLE (0)
#define VOICE_DIGIT_MAP_ENABLE (1)
	uword8 ucDigitMapEnable;/*拨号计划是否有效*/
#define VOICE_DIGIT_MAP_MATCH_MODE_MIN (1)
#define VOICE_DIGIT_MAP_MATCH_MODE_MAX (2)
	uword8 ucDigitMapMatchMode;/*数图匹配模式（最小匹配/最大匹配）"Min""Max"*/
#define VOICE_IMMEDIATLY_DIGIT_DISABLE (0)
#define VOICE_IMMEDIATLY_DIGIT_ENABLE (1)
	uword8 ucImmediatlyDigit;/*立即拨号送出末尾#*/

	uword8 ucOffHookStartDigitTime;/*摘机不拨号的时间，单位为秒，范围为：10～20*/
	uword8 ucDigitTimeShort;/*短定时器时间，单位为秒，默认值为：5*/
	uword8 ucDigitTimeLong;/*长定时器时间，单位为秒，默认值为：16*/
	uword8 ucHangRemindToneTime;/*播放催挂音时间，单位为秒，默认值为：60*/

	uword8 ucBusyToneTime;/*播放忙音时间，单位为秒，默认值为：40*/
	uword8 ucNoAnswerToneTime;/*久叫不应时间，单位为秒，默认值为：60*/
#define VOICE_T_TIMEOUT_WAITING_TIME_SHORT_TIMER (1)
#define VOICE_T_TIMEOUT_WAITING_TIME_LONG_TIMER (2)
	uword8 ucTtimeoutWaitingTime;/*"T"超时等待时间*/
#define VOICE_DOT_TIMEOUT_WAITING_TIME_DISABLE (0)
#define VOICE_DOT_TIMEOUT_WAITING_TIME_SHORT_TIMER (1)
#define VOICE_DOT_TIMEOUT_WAITING_TIME_LONG_TIMER (2)
	uword8 ucDotTimeoutWaitingTime;/*"."超时等待时间*/

#define VOICE_OUTNUM_CONTAIN_IMMEDIATE_DIAL_KEY_DISABLE (0)
#define VOICE_OUTNUM_CONTAIN_IMMEDIATE_DIAL_KEY_ENABLE (1)
	uword8 ucOutNumberContainImmediateDialKey;/*立即拨号时送出末尾"#"*/
	uword8 aucPad1[CM_THREE_PADS];

#define VOICE_DIGIG_MAP_STRING_LEN (4096)
	word8 aucDigitMap[VOICE_DIGIG_MAP_STRING_LEN]; /*拨号计划规则(数图)*/


#define VOICE_DM_MISMATCH_METHOD_NO_SEND_CALL (0)
#define VOICE_DM_MISMATCH_METHOD_DELAY_SEND_CALL (1)
#define VOICE_DM_MISMATCH_METHOD_SEND_CALL_IMMIDIATELY (2)
	uword32 ulDmMismatchMethod;

#define VOICE_EMERGENCY_MAP_STRING_LEN (128)
	char auEmergencyMap[VOICE_EMERGENCY_MAP_STRING_LEN]; //紧急呼叫拨号规则

#define VOICE_EMERGENCY_MAP_DISABLE (0)
#define VOICE_EMERGENCY_MAP_ENABLE  (1)
	uword32 ulEmergencyMapEnable; //紧急呼叫匹配使能
#define VOICE_PROFILE_DISABLED (0)
#define VOICE_PROFILE_ENABLED (1)
#define VOICE_PROFILE_QUIESCENT (2)
	uword8 ucEnable;
#define VOICE_PROFILE_RESET_GET (0)
#define VOICE_PROFILE_RESET  (1)
	uword8  ucReset;
	uword8 aucPad2[CM_TWO_PADS];
	uword32 ulNumberOfLines;
#define VOICE_PROFILE_NAME_LEN (64)
	word8 aucName[VOICE_PROFILE_NAME_LEN];
#define VOICE_PROFILE_SUPPORT_SIP (1)
#define VOICE_PROFILE_SUPPORT_H248 (2)
#define VOICE_PROFILE_SUPPORT_BOTH (3)
	uword8 ucSignalingProtocols;
	uword8 ucMaxSessions;
#define VOICE_PROFILE_INTERFACE_STATE_CLOSED (0)
#define VOICE_PROFILE_INTERFACE_STATE_CLOSEING (1)
#define VOICE_PROFILE_INTERFACE_STATE_RESTARTING (2)
#define VOICE_PROFILE_INTERFACE_STATE_INSERVICE (3)
#define VOICE_PROFILE_INTERFACE_STATE_GRACEFUL_CLOSED (4)
#define VOICE_PROFILE_INTERFACE_STATE_MGC_DISCONNETED (5)
#define VOICE_PROFILE_INTERFACE_STATE_MGC_SWITCHING (6)
	uword8 ucInterfaceState;
	uword8 ucPad3;
	word8 aucWanName[CM_WAN_CONNECTION_NAME_LEN];
#define H248_DM_NAME_LEN (64)
	word8 aucDMName[H248_DM_NAME_LEN];

#define VOICE_GENTRAL_ATTR_MASK_BIT0_SERVER_TYPE (0x01)
#define VOICE_GENTRAL_ATTR_MASK_BIT1_DIGIT_MAP_ENABLE (0x02)
#define VOICE_GENTRAL_ATTR_MASK_BIT2_DIGIT_MAP_MATCH_MODE (0x04)
#define VOICE_GENTRAL_ATTR_MASK_BIT3_IMMEDIATLY_DIGIT (0x08)
#define VOICE_GENTRAL_ATTR_MASK_BIT4_OFF_HOOK_TIME (0x10)
#define VOICE_GENTRAL_ATTR_MASK_BIT5_DIGIT_TIME_SHORT (0x20)
#define VOICE_GENTRAL_ATTR_MASK_BIT6_DIGIT_TIME_LONG (0x40)
#define VOICE_GENTRAL_ATTR_MASK_BIT7_HANG_REMIND_TOME_TIME (0x80)
#define VOICE_GENTRAL_ATTR_MASK_BIT8_BUSY_TONE_TIME (0x100)
#define VOICE_GENTRAL_ATTR_MASK_BIT9_NO_ANSWER_TONE_TIME (0x200)
#define VOICE_GENTRAL_ATTR_MASK_BIT10_T_TIMEOUT_WAITING_TIME (0x400)
#define VOICE_GENTRAL_ATTR_MASK_BIT11_DOT_TIMEOUT_WAITING_TIME (0x800)
#define VOICE_GENTRAL_ATTR_MASK_BIT12_DIGIT_MAP (0x1000)
#define VOICE_GENTRAL_ATTR_MASK_BIT13_DM_MISMATCH_METHOD (0x2000)
#define VOICE_GENTRAL_ATTR_MASK_BIT14_DM_EMERGENCY_PATTERN (0x4000)
#define VOICE_GENTRAL_ATTR_MASK_BIT15_DM_EMERGENCY_ENABLE (0x8000)
#define VOICE_GENTRAL_ATTR_MASK_ENABLE (0x10000)
#define VOICE_GENTRAL_ATTR_MASK_RESET (0x20000)
#define VOICE_GENTRAL_ATTR_MASK_NAME (0x40000)
#define VOICE_GENTRAL_ATTR_MASK_PROTOCOLS (0x80000)
#define VOICE_GENTRAL_ATTR_MASK_MAX_SESSIONS (0x100000)
#define VOICE_GENTRAL_ATTR_MASK_DM_NAME (0x200000)

#define VOICE_GENTRAL_ATTR_MASK_ALL (0x3fffff)
	uword32 ulBitmap;
} __PACK__ IgdVoiceGeneralAttrConfTab;

/***************************SIP/IMS基本设置*********************************/
typedef struct {
#define SIP_SERVER_LEN (128)
#define SIP_SERVER_TRANSPORT_PROTOCOL_TCP (0)
#define SIP_SERVER_TRANSPORT_PROTOCOL_UDP (1)
#define SIP_SERVER_TRANSPORT_PROTOCOL_TLS (2)
#define SIP_SERVER_TRANSPORT_PROTOCOL_SCTP (3)

#define SIP_SERVER_PORT_MIN (0)
#define SIP_SERVER_PORT_MAX (65535)

	uword32 ulStateAndIndex;

	word8 aucProxyServer[SIP_SERVER_LEN];/*代理服务器*/
	uword32 ulProxyServerPort;/*代理服务器端口*/
	uword8 ucProxyServerTransport;/*代理服务器传输协议，包括TCP/UDP/TLS/SCTP*/
	uword8 aucPad1[CM_THREE_PADS];

	word8 aucRegistrarServer[SIP_SERVER_LEN];/*注册服务器*/
	uword32 ulRegistrarServerPort;/*注册服务器端口*/
	uword8 ucRegistrarServerTransport;/*注册服务器传输协议，包括TCP/UDP/TLS/SCTP*/
	uword8 aucPad2[CM_THREE_PADS];

	word8 aucOutBoundProxyServer[SIP_SERVER_LEN];/*outbound服务器*/
	uword32 ulOutBoundProxyServerPort;/*outbound服务器端口*/
	uword8 ucOutBoundProxyServerTransport;/*outbound服务器传输协议，包括TCP/UDP/TLS/SCTP*/
	uword8 aucPad3[CM_THREE_PADS];

	word8 aucStandbyProxyServer[SIP_SERVER_LEN];/*备用代理服务器*/
	uword32 ulStandbyProxyServerPort;/*备用代理服务器端口*/
	uword8 ucStandbyProxyServerTransport;/*备用代理服务器传输协议，包括TCP/UDP/TLS/SCTP*/
	uword8 aucPad4[CM_THREE_PADS];

	word8 aucStandbyRegistrarServer[SIP_SERVER_LEN];/*备用注册服务器*/
	uword32 ulStandbyRegistrarServerPort;/*备用注册服务器端口*/
	uword8 ucStandbyRegistrarServerTransport;/*备用注册服务器传输协议，包括TCP/UDP/TLS/SCTP*/
	uword8 aucPad5[CM_THREE_PADS];

	word8 aucStandbyOutBoundProxyServer[SIP_SERVER_LEN];/*outbound备用服务器*/
	uword32 ulStandbyOutBoundProxyServerPort;/*outbound备用服务器端口*/
	uword8 ucStandbyOutBoundProxyServerTransport;/*outbound备用服务器传输协议，包括TCP/UDP/TLS/SCTP*/
	uword8 aucPad6[CM_THREE_PADS];

	word8 aucUserAgentDomain[SIP_SERVER_LEN];/*家庭网关域名*/
	uword32 ulUserAgentPort;/*域名服务器端口*/
	uword8 ucUserAgentTransport; /*域名传输协议，包括TCP/UDP/TLS/SCTP*/
	uword8 aucPad7[CM_THREE_PADS];

#define SIP_SERVER_VLAN_MARK_MIN (-1)
	word32 lVlanMark;/*在SIP消息流打VLANID标签*/
	word32 l8021pMark; /*在SIP消息流打802.1p标签*/
	word32 lSessionUpdateTimer;/*会话周期更新定时器，单位为分钟，默认值为：30*/
#define SIP_SESSION_TIMER_DISABLE (0)
#define SIP_SESSION_TIMER_ENABLE (1)
	word8 ucRequireTimer; /* session timer 开关 */

#define SIP_HEARTBEAT_SWITCH_DISABLE (0)
#define SIP_HEARTBEAT_SWITCH_ENABLE (1)
#define SIP_HEARTBEAT_SWITCH_PASSIVE (2)
	uword8 ucHeartbeatSwitch;/*是否启用网关和软交换/BAC间心跳*/
	uword8 aucPad8[CM_TWO_PADS];

	word32 lHeartbeatCycle;/*心跳周期，以秒为单位，默认：60*/
	word32 llHeartbeatCount;/*心跳超时次数，默认：3 （软交换3次不回应，则网关向软交换重新注册）*/

	uword32 ulTimerT1;
	uword32 ulTimerT2;
	uword32 ulTimerT4;
	uword32 ulRegisterRetryInterval;
	uword32 ulDSCPMark;

#define SIP_BASIC_ATTR_MASK_BIT0_PROXY_SERVER (0x01)
#define SIP_BASIC_ATTR_MASK_BIT1_PROXY_SERVER_PORT (0x02)
#define SIP_BASIC_ATTR_MASK_BIT2_PROXY_SERVER_TRANSPORT (0x04)
#define SIP_BASIC_ATTR_MASK_BIT3_REG_SERVER (0x08)
#define SIP_BASIC_ATTR_MASK_BIT4_REG_SERVER_PORT (0x10)
#define SIP_BASIC_ATTR_MASK_BIT5_REG_SERVER_TRANSPORT (0x20)
#define SIP_BASIC_ATTR_MASK_BIT6_OUTBOUND_PROXY_SERVER (0x40)
#define SIP_BASIC_ATTR_MASK_BIT7_OUTBOUND_PROXY_SERVER_PORT (0x80)
#define SIP_BASIC_ATTR_MASK_BIT8_OUTBOUND_PROXY_SERVER_TRANSPORT (0x100)
#define SIP_BASIC_ATTR_MASK_BIT9_STANDBY_PROXY_SERVER (0x200)
#define SIP_BASIC_ATTR_MASK_BIT10_STANDBY_PROXY_SERVER_PORT (0x400)
#define SIP_BASIC_ATTR_MASK_BIT11_STANDBY_PROXY_SERVER_TRANSPORT (0x800)
#define SIP_BASIC_ATTR_MASK_BIT12_STANDBY_REG_SERVER (0x1000)
#define SIP_BASIC_ATTR_MASK_BIT13_STANDBY_REG_SERVER_PORT (0x2000)
#define SIP_BASIC_ATTR_MASK_BIT14_STANDBY_REG_SERVER_TRANSPORT (0x4000)
#define SIP_BASIC_ATTR_MASK_BIT15_STANDBY_OUTBOUND_PROXY_SERVER (0x8000)
#define SIP_BASIC_ATTR_MASK_BIT16_STANDBY_OUTBOUND_PROXY_SERVER_PORT (0x10000)
#define SIP_BASIC_ATTR_MASK_BIT17_STANDBY_OUTBOUND_PROXY_SERVER_TRANSPORT (0x20000)
#define SIP_BASIC_ATTR_MASK_BIT18_USER_AGENT_DOMAIN (0x40000)
#define SIP_BASIC_ATTR_MASK_BIT19_USER_AGENT_PORT (0x80000)
#define SIP_BASIC_ATTR_MASK_BIT20_USER_AGENT_TRANSPORT (0x100000)
#define SIP_BASIC_ATTR_MASK_BIT21_VLAN_MARK (0x200000)
#define SIP_BASIC_ATTR_MASK_BIT22_8021P_MARK (0x400000)
#define SIP_BASIC_ATTR_MASK_BIT23_SESSION_UPDATE_TIMER (0x800000)
#define SIP_BASIC_ATTR_MASK_BIT24_HEARTBEAT_SWITCH (0x1000000)
#define SIP_BASIC_ATTR_MASK_BIT25_HEARTBEAT_CYCLE (0x2000000)
#define SIP_BASIC_ATTR_MASK_BIT26_HEARTBEAT_COUNT (0x4000000)
#define SIP_BASIC_ATTR_MASK_TIMER_T1 (0x8000000)
#define SIP_BASIC_ATTR_MASK_TIMER_T2 (0x10000000)
#define SIP_BASIC_ATTR_MASK_TIMER_T4 (0x20000000)
#define SIP_BASIC_ATTR_MASK_DSCP_MARK (0x40000000)
#define SIP_BASIC_ATTR_MASK_REQUIRE_TIMER (0x80000000)
#define SIP_BASIC_ATTR_MASK_ALL (0xffffffff)
	uword32 ulBitmap;
#define SIP_BASIC_ATTR_MASK1_USE_CODEC_PRIOTITY (0x01)
#define SIP_BASIC_ATTR_MASK1_REGISTER_RETRY_INTERVAL (0x02)
#define SIP_BASIC_ATTR_MASK1_ALL (0x0003)
	uword32 ulBitmap1;
} __PACK__ IgdVoiceSipBasicAttrConfTab;

/***************************SIP用户拨号设置*********************************/
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucLineNum;/*线路编号：1个POTS口，固定为1*/
#define SIP_USER_DISABLE (0)
#define SIP_USER_ENABLE (1)
	uword8 ucUserEnable;/* VOIP线路是否启用*/
	uword8 aucPad1[CM_TWO_PADS];

#define SIP_AUTH_USERNAME_LEN (128)
	word8 aucAuthUsername[SIP_AUTH_USERNAME_LEN];/*sip鉴权用户名*/
#define SIP_AUTH_PASSWORD_LEN (128)
	word8 aucAuthPassword[SIP_AUTH_PASSWORD_LEN];/*sip鉴权密码*/
#define SIP_NUMBER_URL_LEN (128)
	word8 aucNumberUrl[SIP_NUMBER_URL_LEN];/*sip用户电话号码*/

#define SIP_SUBSUA_DISABLE (0)
#define SIP_SUBSUA_ENABLE (1)
	uword32 ulSubsUA;

#define SIP_SUBSREG_DISABLE (0)
#define SIP_SUBSREG_ENABLE (1)
	uword32 ulSubsReg;

#define SIP_PBXPREFIX_LEN (128)
	word8 aucPBXPrefix[SIP_PBXPREFIX_LEN];

#define SIP_PBXPREFIX_DISABLE (0)
#define SIP_PBXPREFIX_ENABLE (1)
	uword32 ulPBXPrefixEnable;

#define SIP_PBXDIALSECONDARY_DISABLE (0)
#define SIP_PBXDIALSECONDARY_ENABLE (1)
	uword32 ulPBXDialSecondaryEnable;
#define SIP_DIRECTORY_NUMBER_LEN (64)
	word8 aucDirectoryNumber[SIP_DIRECTORY_NUMBER_LEN];

#define SIP_USER_ATTR_MASK_BIT0_USER_ENABLE (0x01)
#define SIP_USER_ATTR_MASK_BIT1_AUTH_USERNAME (0x02)
#define SIP_USER_ATTR_MASK_BIT2_AUTH_PASSWORD (0x04)
#define SIP_USER_ATTR_MASK_BIT3_NUMBER_URL (0x08)
#define SIP_USER_ATTR_MASK_BIT4_SUBSUA (0x10)
#define SIP_USER_ATTR_MASK_BIT5_SUBSREG (0x20)
#define SIP_USER_ATTR_MASK_BIT6_PBXPREFIX (0x40)
#define SIP_USER_ATTR_MASK_BIT7_PBXPREFIX_ENABLE (0x80)
#define SIP_USER_ATTR_MASK_BIT8_PBXDIALSECONDARY_ENABLE (0x100)
#define SIP_USER_ATTR_MASK_DIRECTORY_NUMBER (0x200)
#define SIP_USER_ATTR_MASK_PHY_REFERENCE_LIST (0x400)
#define SIP_USER_ATTR_MASK_CALL_STATE (0x800)
#define SIP_USER_ATTR_MASK_LINE_NAME (0x1000)
#define SIP_USER_ATTR_MASK_ALL (0x1fff)
	uword32 ulBitmap;
} __PACK__ IgdVoiceSipUserAttrConfTab;

/***************************SIP/IMS响应*********************************/
/* IGD_RESPONSE_MAP_ATTR_TAB */
#define IGD_RESPONSE_MAP_ATTR_MAX 8
typedef struct {
	uint32_t state_and_index;
	uint32_t response_index;
	uint32_t sip_response_number;
	uint32_t tone;
	uint32_t x_cu_duration;
#define RESPONSE_MAP_ATTR_MASK_SIP_RESPONSE_NUMBER               (1 << 0)
#define RESPONSE_MAP_ATTR_MASK_TONE                              (1 << 1)
#define RESPONSE_MAP_ATTR_MASK_X_CU_DURATION                     (1 << 2)
#define RESPONSE_MAP_ATTR_MASK_ALL                              ((1 << 3) - 1)
	uint32_t bit_map;
} __PACK__ igd_response_map_attr_conf_tab_t;

/***************************H248基本设置*********************************/
typedef struct {
#define H248_SERVER_LEN (128)
#define H248_PRIFIX_LEN (64)

	uword32 ulStateAndIndex;

	word32 lMediaGatewayPort;/*终端侧H.248协议UDP端口号，需要和软交换侧一致*/
	word8 aucMediaGatewayControler[H248_SERVER_LEN];/*主用MGC IP地址或者域名*/
	word32 lMediaGatewayControlerPort;/*主用MGC软交换端口，默认为：2944*/
	word8 aucStandbyMediaGatewayControler[H248_SERVER_LEN];/*备用MGC IP地址或者域名，如果为0，则不启用双归属*/
	word32 lStandbyMediaGatewayControlerPort;/*备用MGC软交换端口，默认为：2944*/

#define MESSING_ENCODING_TYPE_ABNF (0)
#define MESSING_ENCODING_TYPE_ASN1 (1)
	uword8 ucMessageEncodingType;/* H.248协议的编码类型，可以为：ABNF，ASN.1*/
#define DEVICE_ID_TYPE_IP_ADDR (0)
#define DEVICE_ID_TYPE_DOMAIN (1)
#define DEVICE_ID_TYPE_DEVICE_NAME (2)
	uword8 ucDeviceIDType;/* 终端标识的类型，0：IP地址，1：域名，2：设备名*/
	uword8 aucPad1[CM_TWO_PADS];

#define DEVICE_IDENTIFICATION_NAME_LEN (64)
	word8 ucDeviceID[DEVICE_IDENTIFICATION_NAME_LEN];/*终端向软交换平台注册时使用的全局唯一的标识*/

#define PHYSICAL_TERMID_CONFIG_METHOD_GENERAL (0)
#define PHYSICAL_TERMID_CONFIG_METHOD_ONEBYONE (1)
	uword8 ucPhysicalTermIDConfigMethod;/*物理终结点的配置方法，0：通配，1：逐个配置*/
	uword8 aucPad2[CM_THREE_PADS];

	word8 aucPhysicalTermIDPrefix[H248_PRIFIX_LEN];/*终端的物理终结点标识前缀*/
	word8 aucPhysicalTermID[H248_PRIFIX_LEN];/*终端物理终结点起始值，起始1*/
	uword32 ulPhysicalTermIDAddLen;/*物理终结点标识的前缀后面添加的位数*/
	word8 aucRTPPrefix[H248_PRIFIX_LEN];
	uword32 ulEphemeralTermIDAddLen;/* RTP终结点标识的前缀后面添加的数字部分的位数*/

#define EPHEMERAL_TERMID_UNIFORM_ALIGNMENT (0)
#define EPHEMERAL_TERMID_UNIFORM_NOT_ALIGNMENT (1)
	uword8 ucEphemeralTermIDUniform;/* RTP终结点标识是否使用对齐格式，0：对齐方式，1：不对齐方式。*/
	uword8 aucPad3[CM_THREE_PADS];

	uword32 ulEphemeralTermIDStart;/*RTP终结点标识名起始值，默认为：0*/

#define THREE_HAND_SHAKE_ENABLE (0)
#define THREE_HAND_SHAKE_DISABLE (1)
	uword8 ucThreeHandShake;/* H.248协议消息是否三次握手，0：是，1：否*/
	uword8 aucPad4[CM_THREE_PADS];

	uword32 ulLongTimer;/* H.248事务处理中的长定时器时间*/
	uword32 ulPendingTimerInit;/* Pending 定时器初始时长*/
	uword32 ulRetranIntervalTimer;/* Retrans 定时器初始时长*/
	uword32 ulMaxRetranCount;/*最大Retrans次数*/
	word32 lRetransmissionTime;/* 重传时长，以秒为单位，默认为25 秒*/
	word32 lRetransmissionCycle;/* 重传间隔，以秒为单位，默认4秒*/

#define HEARTBEAT_MODE_DISABLE (0)
#define HEARTBEAT_MODE_NOTIFY (1)
#define HEARTBEAT_MODE_OTHER (2)
	uword8 ucHeartbeatMode;/*链路检测模式，默认为：1*/
	uword8 aucPad5[CM_THREE_PADS];

	word32 lHeartbeatCycle;/* 心跳周期，默认：90秒*/
	word32 lHeartbeatCount;/*心跳检测次数，默认：3*/
	uword32 ulRegisterCycle;/* 重启注册周期，以秒为单位，取值范围为1~300秒*/

#define MANDATORY_REGISTER_DISABLE (0)
#define MANDATORY_REGISTER_ENABLE (1)
	uword8 ucMandatoryRegister;/*人工启动网关发起注册消息*/
#define H248_AUTH_METH_NO_AUTH (0)
#define H248_AUTH_METH_MD5 (1)
#define H248_AUTH_METH_OTHER (2)
	uword8 ucAuthenticationMethID;/* H.248网关接入认证算法，默认无认证*/
	uword8 aucPad6[CM_TWO_PADS];

#define H248_STACKINFO_SHORT (0)
#define H248_STACKINFO_LONG (1)
	uword8 ulMsgFormat; /*消息长短格式*/
#define H248_STACKINFO_HANDSHAKE_DISABLE (0)
#define H248_STACKINFO_HANDSHAKE_ENABLE (1)
	uword8 ucResponseAckCtrl; /*是否启用三次握手*/
	uword8 aucPad7[CM_TWO_PADS];
#define H248_MGPROV_TIMER_MIN (6000)
#define H248_MGPROV_TIMER_MAX (8000)
	uword32 ulMgcProvTimer; /*MG临时响应定时器时长，8000ms*/
#define H248_MTU_VALUE_MIN (1)
#define H248_MTU_VALUE_MAX (5000)
	uword32 ulMTUValue; /*MG最大传输单元，1500*/
#define H248_STACKINFO_MSGSEG_DISABLE (0)
#define H248_STACKINFO_MSGSEG_ENABLE (1)
	uword8 ulMsgSegmentation; /*是否支持报文分片*/
	uword8 aucPad8[CM_THREE_PADS];
	uword32 ulLocalPort;
#define H248_DOMAIN_LEN (256)
	word8 aucDomain[H248_DOMAIN_LEN];
#define H248_DEVICE_NAME_LEN (256)
	word8 aucDeviceName[H248_DEVICE_NAME_LEN];
	uword32 ulSoftswitchVendor;
#define H248_SOFT_SWITCH_VERSION_LEN (256)
	word8 aucSoftswitchVersion[H248_SOFT_SWITCH_VERSION_LEN];
#define H248_AUTH_DISABLE (0)
#define H248_AUTH_ENABLE (1)
	uword8 ucAuth;
#define H248_AUTH_HEADER_DISABLE (0)
#define H248_AUTH_HEADER_ENABLE (1)
	uword8 ucAuthHeader;
	uword8 aucPad9[CM_TWO_PADS];
#define H248_AUTH_INIT_KEY_LEN (64)
	word8 aucAuthInitKey[H248_AUTH_INIT_KEY_LEN];
#define H248_AUTH_MG_ID_LEN (64)
	word8 aucAuthMgId[H248_AUTH_MG_ID_LEN];
#define H248_AUTH_HEADER_SECURITY_TYPE_MD5  (0)
#define H248_AUTH_HEADER_SECURITY_TYPE_SHA1 (1)
#define H248_AUTH_HEADER_SECURITY_TYPE_NOAH (2)
	uword8 ucHeaderSecurityType;
	uword8 aucPad10[CM_THREE_PADS];
#define H248_AUTH_RFC_ENCRYPT_KEY_LEN (64)
	word8 aucRfc2833EncryptKey[H248_AUTH_RFC_ENCRYPT_KEY_LEN];
	uword32 ulHeartBeatRetransTimer;
	uword32 ulMWDBaseTime;
	uword32 ulMWD;
#define H248_EXTEND_MGC_SWITCH_METHOD_NOT_SUPPORT (0)
#define H248_EXTEND_MGC_SWITCH_METHOD_SUPPORT_SWITCH (1)
#define H248_EXTEND_MGC_SWITCH_METHOD_AUTO_SWITCH_BACK (2)
	uword8 ucMgcSwitchMethod;
	uword8 aucPad11[CM_THREE_PADS];

#define H248_BASIC_ATTR_MASK_BIT0_MEDIA_GATEWAY_PORT (0x01)
#define H248_BASIC_ATTR_MASK_BIT1_MEDIA_GATEWAY_CONTROLER (0x02)
#define H248_BASIC_ATTR_MASK_BIT2_MEDIA_GATEWAY_CONTROLER_PORT (0x04)
#define H248_BASIC_ATTR_MASK_BIT3_STANDBY_MEDIA_GATEWAY_CONTROLER (0x08)
#define H248_BASIC_ATTR_MASK_BIT4_STANDBY_MEDIA_GATEWAY_CONTROLER_PORT (0x10)
#define H248_BASIC_ATTR_MASK_BIT5_MESSAG_ENCODING_TYPE (0x20)
#define H248_BASIC_ATTR_MASK_BIT6_DEVICEID_TYPE (0x40)
#define H248_BASIC_ATTR_MASK_BIT7_DEVICEID (0x80)
#define H248_BASIC_ATTR_MASK_BIT8_PHY_TERMID_CONFIG_METHOD (0x100)
#define H248_BASIC_ATTR_MASK_BIT9_PHY_TERMID_PREFIX (0x200)
#define H248_BASIC_ATTR_MASK_BIT10_PHY_TERMID (0x400)
#define H248_BASIC_ATTR_MASK_BIT11_PHY_TERMID_ADDR_LEN (0x800)
#define H248_BASIC_ATTR_MASK_BIT12_RTP_PREFIX (0x1000)
#define H248_BASIC_ATTR_MASK_BIT13_EPHEMERAL_TERMID_ADDR_LEN (0x2000)
#define H248_BASIC_ATTR_MASK_BIT14_EPHEMERAL_TERMID_UNIFORM (0x4000)
#define H248_BASIC_ATTR_MASK_BIT15_EPHEMERAL_TERMID_START (0x8000)
#define H248_BASIC_ATTR_MASK_BIT16_THREE_HAND_SHAKE (0x10000)
#define H248_BASIC_ATTR_MASK_BIT17_LONG_TIMER (0x20000)
#define H248_BASIC_ATTR_MASK_BIT18_PENDING_TIMER_INIT (0x40000)
#define H248_BASIC_ATTR_MASK_BIT19_RETRANS_INTERVAL_TIMER (0x80000)
#define H248_BASIC_ATTR_MASK_BIT20_MAX_RETRANS_COUNT (0x100000)
#define H248_BASIC_ATTR_MASK_BIT21_RETRANS_MESSION_TIME (0x200000)
#define H248_BASIC_ATTR_MASK_BIT22_RETRANS_MESSION_CYCLE (0x400000)
#define H248_BASIC_ATTR_MASK_BIT23_HEARTBEAT_MODE (0x800000)
#define H248_BASIC_ATTR_MASK_BIT24_HEARTBEAT_CYCLE (0x1000000)
#define H248_BASIC_ATTR_MASK_BIT25_HEARTBEAT_COUNT (0x2000000)
#define H248_BASIC_ATTR_MASK_BIT26_HEARTBEAT_REGISTER_CYCLE (0x4000000)
#define H248_BASIC_ATTR_MASK_BIT27_MANDATORY_REGISTER (0x8000000)
#define H248_BASIC_ATTR_MASK_BIT28_AUTH_METH_ID (0x10000000)
#define H248_BASIC_ATTR_MASK_ALL (0x1fffffff)
	uword32 ulBitmap;
#define H248_BASIC_ATTR_MASK1_BIT0_MSG_FORMAT (0x01)
#define H248_BASIC_ATTR_MASK1_BIT1_RESP_ACK_CTRL (0x02)
#define H248_BASIC_ATTR_MASK1_BIT2_MGC_PROV_TIMER (0x04)
#define H248_BASIC_ATTR_MASK1_BIT3_MTU_VALUE (0x08)
#define H248_BASIC_ATTR_MASK1_BIT4_MSG_SEGMENTATION (0x10)
#define H248_BASIC_ATTR_MASK1_LOCAL_PORT (0x20)
#define H248_BASIC_ATTR_MASK1_DOAMIN (0x40)
#define H248_BASIC_ATTR_MASK1_DEVICE_NAME (0x80)
#define H248_BASIC_ATTR_MASK1_SOFT_SWITCH_VENDOR (0x100)
#define H248_BASIC_ATTR_MASK1_SOFT_SWITCH_VERSION (0x200)
#define H248_BASIC_ATTR_MASK1_AUTH (0x400)
#define H248_BASIC_ATTR_MASK1_AUTH_HEADER (0x800)
#define H248_BASIC_ATTR_MASK1_AUTH_INIT_KEY (0x1000)
#define H248_BASIC_ATTR_MASK1_AUTH_MG_ID (0x2000)
#define H248_BASIC_ATTR_MASK1_AUTH_HEADER_SECURITY_TYPE (0x4000)
#define H248_BASIC_ATTR_MASK1_AUTH_RFC_ENCRYPT_KEY (0x8000)
#define H248_BASIC_ATTR_MASK1_EXTEND_HEART_BEAT_MODE (0x10000)
#define H248_BASIC_ATTR_MASK1_EXTEND_HEART_BEAT_RETRANS_TIMER (0x20000)
#define H248_BASIC_ATTR_MASK1_EXTEND_MWD_BASE_TIME (0x40000)
#define H248_BASIC_ATTR_MASK1_EXTEND_MWD (0x80000)
#define H248_BASIC_ATTR_MASK1_EXTEND_MGC_SWITCH_METHOD (0x100000)
#define H248_BASIC_ATTR_MASK1_ALL (0x1fffff)
	uword32 ulBitmap1;
} __PACK__ IgdVoiceH248BasicAttrConfTab;

/***************************H248用户设置*********************************/
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucLineNum;/*线路编号：1个POTS口，固定为1*/
#define H248_USER_DISABLE (0)
#define H248_USER_ENABLE (1)
	uword8 ucUserEnable;/*线路是否启用*/
	uword8 aucPad[CM_TWO_PADS];

#define H248_PHYSICAL_TERM_ID_LEN (64)
	word8 aucLinePhysicalTermID[H248_PHYSICAL_TERM_ID_LEN];/*线路终端物理终结点标示*/

#define H248_USER_ATTR_MASK_BIT0_USER_ENABLE (0x01)
#define H248_USER_ATTR_MASK_BIT1_LINE_PHYSICAL_TERM_ID (0x02)
#define H248_USER_ATTR_MASK_ALL (0x03)
	uword32 ulBitmap;
} __PACK__ IgdVoiceH248UserAttrConfTab;

/***************************语音高级业务*********************************/
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucLineIndex;
#define URI_TYPE_SIP (0)
#define URI_TYPE_TEL (1)
	uword8 ucUriType;/*URI类型*/
#define DTMF_METHOD_RFC2833 (0)
#define DTMF_METHOD_INBAND (1)
#define DTMF_METHOD_SIPINFO (2)
	uword8 ucDtmfMethod;/*二次拨号模式*/
#define SIP_DSCP_MARK_VALUE_MIN (0)
#define SIP_DSCP_MARK_VALUE_MAX (63)
	uword8 ucSipDscpMark;/*SIP Dscp Mark 0~63*/
#define RTP_DSCP_MARK_VALUE_MIN (0)
#define RTP_DSCP_MARK_VALUE_MAX (63)
	uword8 ucRtpDscpMark;/*RTP Dscp Mark 0~63*/

	uword32 ulSipLocalPort;/*SIP 本地端口*/
	uword32 ulRegisterExpireTime;/*注册过期时间*/
	uword32 ulRegisterRetryInterval;/*注册失败重注册时间*/
	uword32 ulRtpPortStart;/*本地RTP端口最小值*/
	uword32 ulRtpPortEnd;/*本地RTP端口最大值*/

#define VOICE_PRACK_DISABLE (0)
#define VOICE_PRACK_ENABLE (1)
	uword8 ucPrackEnable;/*拍叉簧使能*/
	uword32 ucFlashMinTime;/*拍叉最短时间*/
	uword32 ucFlashMaxTime;/*拍叉最长时间*/
#define VOICE_BOOT_DEREGISTER_DISABLE (0)
#define VOICE_BOOT_DEREGISTER_ENABLE (1)
	uword8 ucBootDeRegisterEnable;/*启用开机注销*/

#define VOICE_VAD_CNG_DISABLE (0)
#define VOICE_VAD_CNG_ENABLE (1)
	uword8 ucVadCngEnable;/*静音检测*/
#define VOICE_POLARITY_REVERSE_DISABLE (0)
#define VOICE_POLARITY_REVERSE_ENABLE (1)
	uword8 ucPolarityReverseEnable;/*极性反转*/
	uword8 aucPad1[CM_TWO_PADS];

#define VOICE_TIME_SYNC_MODE_DISABLE (0)
#define VOICE_TIME_SYNC_MODE_USE_SYSTEM_TIME (1)
#define VOICE_TIME_SYNC_MODE_USE_CORE_NETWORK_TIME (2)
	uword8 ucTimeSyncMode;/*时间同步模式*/
#define VOICE_CALLID_SHOW_DISABLE (0)
#define VOICE_CALLID_SHOW_MODE_DTMF (1)
#define VOICE_CALLID_SHOW_MODE_FSK (2)
#define VOICE_CALLID_SHOW_MODE_SDMF_FSK (3)
	uword8 ucCallIDShowMode;/*来显模式：DTMF或FSK*/
#define VOICE_SUBSCRIBE_DISABLE (0)
#define VOICE_SUBSCRIBE_ENABLE (1)
	uword8 ucSubscribeEnable;/*业务订阅*/
	uword8 ucPad;

	uword32 ulRingVoltage;/*振铃电压*/

#define VOICE_DIGIG_MAP_SPECIAL_STRING_LEN (4096)
	word8 aucDigitMapSpecial[VOICE_DIGIG_MAP_SPECIAL_STRING_LEN];

#define VOICE_DIGITMAP_SPECIAL_DISABLE (0)
#define VOICE_DIGITMAP_SPECIAL_ENABLE (1)
	uword32 ulDigitMapSpecialEnable;

#define VOICE_IMPLICIT_REGISTRATION_DISABLE (0)
#define VOICE_IMPLICIT_REGISTRATION_ENABLE (1)
	uword32 ulImplicitRegistrationEnable;

#define VOICE_POUND_KEY_TRANS_DISABLE (0)
#define VOICE_POUND_KEY_TRANS_ENABLE (1)
	uword32 ulPoundKeyTransSwitch;

#define VOICE_CID_PAI_PRIORITY (0)
#define VOICE_CID_FROM_PRIORITY (1)
	uword32 ulCIDPriorityMode;

#define VOICE_PREFER_REMOTE_CODEC_REMOTE (0)  //编解码使用远端协商
#define VOICE_PREFER_REMOTE_CODEC_LOCAL  (1)  //编解码使用本端协商
	uword32 ulPreferRemoteCodecSwitch;

#define VOICE_ADV_ATTR_MASK_BIT0_URI_TYPE (0x01)
#define VOICE_ADV_ATTR_MASK_BIT1_DTMF_METHOD (0x02)
#define VOICE_ADV_ATTR_MASK_BIT2_SIP_DSCP_MARK (0x04)
#define VOICE_ADV_ATTR_MASK_BIT3_RTP_DSCP_MARK (0x08)
#define VOICE_ADV_ATTR_MASK_BIT4_SIP_LOCAL_PORT (0x10)
#define VOICE_ADV_ATTR_MASK_BIT5_REG_EXPIRE_TIME (0x20)
#define VOICE_ADV_ATTR_MASK_BIT6_REG_RETRY_INTERVAL (0x40)
#define VOICE_ADV_ATTR_MASK_BIT8_RTP_PORT_START (0x100)
#define VOICE_ADV_ATTR_MASK_BIT9_RTP_PORT_END (0x200)
#define VOICE_ADV_ATTR_MASK_BIT10_PRACK_ENABLE (0x400)
#define VOICE_ADV_ATTR_MASK_BIT11_FLASH_MIN_TIME (0x800)
#define VOICE_ADV_ATTR_MASK_BIT12_FLASH_MAX_TIME (0x1000)
#define VOICE_ADV_ATTR_MASK_BIT13_BOOT_DEREG (0x2000)
#define VOICE_ADV_ATTR_MASK_BIT14_VAD_CNG_ENABLE (0x4000)
#define VOICE_ADV_ATTR_MASK_BIT15_POLA_REVERSE (0x8000)
#define VOICE_ADV_ATTR_MASK_BIT16_TIME_SYNC_MODE (0x10000)
#define VOICE_ADV_ATTR_MASK_BIT17_CALLID_SHOW_MODE (0x20000)
#define VOICE_ADV_ATTR_MASK_BIT18_SUBSCRIBE_ENABLE (0x40000)
#define VOICE_ADV_ATTR_MASK_BIT19_RING_VOLTAGE (0x80000)
#define VOICE_ADV_ATTR_MASK_BIT20_DIGITMAP_SPECIAL (0X100000)
#define VOICE_ADV_ATTR_MASK_BIT21_DIGITMAP_SPECIAL_ENABLE (0X200000)
#define VOICE_ADV_ATTR_MASK_BIT22_IMPLICIT_REGISTRATION_ENABLE (0X400000)
#define VOICE_ADV_ATTR_MASK_BIT23_BOUNDKEY_TRANS_SWITCH (0X800000)
#define VOICE_ADV_ATTR_MASK_BIT24_CID_PRIORITY_MODE (0X1000000)
#define VOICE_ADV_ATTR_MASK_BIT25_PREFER_REMOTE_CODEC_SWITCH (0X2000000)
#define VOICE_ADV_ATTR_MASK_ALL (0xfffffff)
	uword32 ulBitmap;
} __PACK__ IgdVoiceAdvancedAttrConfTab;

/***************************增益设置*********************************/
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucLineIndex;
#define VOICE_ECHO_CANCELLATION_DISABLE (0)
#define VOICE_ECHO_CANCELLATION_ENABLE (1)
	uword8 ucEchoCancellationEnable;/*回声抑制功能是否打开*/
	uword8 ucEchoCancellationInUse;/*标识当前线路回声抑制是否打开*/
	uword8 ucPad;

	word32 lTransmitGain;/*呼出增益*/
	word32 lReceiveGain;/*呼入增益*/
	uword32 ulEchoCancellationTail;/*回声抑制的时间*/

	word32 lLineTxGain; /*slic线路发送增益*/
	word32 lLineRxGain; /*slic线路接收增益*/

#define VOICE_PROCESSING_ATTR_MASK_BIT0_ECHO_CANCELLATION_ENABLE (0x01)
#define VOICE_PROCESSING_ATTR_MASK_BIT1_ECHO_CANCELLATION_USED (0x02)
#define VOICE_PROCESSING_ATTR_MASK_BIT2_TRANSMIT_GAIN (0x04)
#define VOICE_PROCESSING_ATTR_MASK_BIT3_RECIEVE_GAIN (0x08)
#define VOICE_PROCESSING_ATTR_MASK_BIT4_ECHO_CANCELLATION_TAIL (0x10)
#define VOICE_PROCESSING_ATTR_MASK_BIT5_LINE_TX_GAIN (0x20)
#define VOICE_PROCESSING_ATTR_MASK_BIT6_LINE_RX_GAIN (0x40)
#define VOICE_PROCESSING_ATTR_MASK_ALL (0x7f)
	uword32 ulBitmap;
} __PACK__ IgdVoiceProcessingAttrConfTab;

/***************************热线业务*********************************/
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucLineIndex;

#define VOICE_THREE_PARTY_CONFERENCE_DISABLE (0)
#define VOICE_THREE_PARTY_CONFERENCE_ENSABLE (1)
	uword8 ucThreePartyConferenceEnable; /*三方通话使能*/

#define VOICE_HOTLINE_DISABLE (0)
#define VOICE_HOTLINE_ENABLE (1)
	uword8 ucHotlineEnable;/*热线使能*/
#define VOICE_CALL_TRANSFER_DISABLE (0)
#define VOICE_CALL_TRANSFER_ENABLE (1)
	uword8 ucCallTransferEnable;/*呼叫转移使能*/
#define VOICE_CALL_WAITING_DISABLE (0)
#define VOICE_CALL_WAITING_ENABLE (1)
	uword8 ucCallWaitingEnable;/*呼叫等待使能*/
#define VOICE_MWI_DISABLE (0)
#define VOICE_MWI_ENABLE (1)
	uword8 ucMWIEnable;

#define VOICE_HOTLINE_NUMBER_LEN (64)
	word8 aucHotlineNumber[VOICE_HOTLINE_NUMBER_LEN];/*热线号码*/
	uword32 ulHotlineTimer;/*热线延迟时间*/

#define VOICE_THREE_PARTY_CONFERENCE_MODE_LOCAL (0)
#define VOICE_THREE_PARTY_CONFERENCE_MODE_SERVER (1)
	uword8 ucThreePartyConferenceMode;/*三方通话模式*/
	uword8 aucPad[CM_THREE_PADS];

#define VOICE_CONFERENCE_URI_LEN (128)
	word8 aucConferenceUrl[VOICE_CONFERENCE_URI_LEN];/*会议URI*/

#define VOICE_CALL_FORWARD_UNCONDITIONAL_DISABLE (0)
#define VOICE_CALL_FORWARD_UNCONDITIONAL_ENABLE (1)
	uword8 ucCallFwdUnconditionalEnable;/*无条件转移使能*/
#define VOICE_CALL_FORWARD_ON_BUSY_DISABLE (0)
#define VOICE_CALL_FORWARD_ON_BUSY_ENABLE (1)
	uword8 ucCallFwdOnBusyEnable;/* 遇忙呼叫使能*/
#define VOICE_CALL_FORWARD_ON_NO_ANSWER_DISABLE (0)
#define VOICE_CALL_FORWARD_ON_NO_ANSWER_ENABLE (1)
	uword8 ucCallFwdOnNoAnswerEnable;/*无应答呼叫转移使能*/
#define VOICE_CALL_FORWARD_ON_NO_ANSWER_RING_COUNT_MIN (1)
#define VOICE_CALL_FORWARD_ON_NO_ANSWER_RING_COUNT_MAX (64)
	uword8 ucCallFwdOnNoAnswerRingCount;/*无应答响铃次数*/

#define VOICE_CALL_FORWARD_NUM_LEN (32)
	word8 aucCallFwdUnconditionalNum[VOICE_CALL_FORWARD_NUM_LEN];/*无条件转移号码*/
	word8 aucCallFwdOnBusyNum[VOICE_CALL_FORWARD_NUM_LEN];/*遇忙呼叫号码*/
	word8 aucCallFwdOnNoAnswerNum[VOICE_CALL_FORWARD_NUM_LEN];/*无应答呼叫转移号码*/

#define HOTLINE_ATTR_MASK_BIT0_ENABLE (0x01)
#define HOTLINE_ATTR_MASK_BIT1_CALL_TRANSFER_ENABLE (0x02)
#define HOTLINE_ATTR_MASK_BIT2_CALL_WAITING_ENABLE (0x04)
#define HOTLINE_ATTR_MASK_BIT3_MWI_ENABLE (0x08)
#define HOTLINE_ATTR_MASK_BIT4_HOTLINE_NUMBER (0x10)
#define HOTLINE_ATTR_MASK_BIT5_HOTLINE_TIMER (0x20)
#define HOTLINE_ATTR_MASK_BIT6_THREE_PARTY_CONFERENCE_MODE (0x40)
#define HOTLINE_ATTR_MASK_BIT7_CONFERENCE_URI (0x80)
#define HOTLINE_ATTR_MASK_BIT8_CALL_FWD_UNCONDITIONAL_ENABLE (0x100)
#define HOTLINE_ATTR_MASK_BIT9_CALL_FWD_ON_BUSY_ENABLE (0x200)
#define HOTLINE_ATTR_MASK_BIT10_CALL_FWD_ON_NO_ANSWER_ENABLE (0x400)
#define HOTLINE_ATTR_MASK_BIT11_CALL_FWD_ON_NO_ANSWER_RING_COUNT (0x800)
#define HOTLINE_ATTR_MASK_BIT12_CALL_FWD_UNCONDITIONAL_NUMBER (0x1000)
#define HOTLINE_ATTR_MASK_BIT13_CALL_FWD_ON_BUSY_NUMBER (0x20000)
#define HOTLINE_ATTR_MASK_BIT14_CALL_FWD_ON_NO_ANSWER_NUMBER (0x40000)
#define HOTLINE_ATTR_MASK_BIT15_THREE_PARTY_CONFERENCE_ENABLE (0x80000)
#define HOTLINE_ATTR_MASK_ALL (0xfffff)
	uword32 ulBitmap;
} __PACK__ IgdVoiceHotlineServiceAttrConfTab;

/***************************RTP业务*********************************/
typedef struct {
	uword32 ulStateAndIndex;

	uword8 uc8021pMark;/*在RTP消息流打802.1p标签*/
#define VOICE_RTP_DSCP_MARK_MIN (0)
#define VOICE_RTP_DSCP_MARK_MAX (63)
	uword8 ucDscpMark;/*标记DSCP字段的值*/
	uword8 aucPad[CM_TWO_PADS];

	word8 aucWanName[CM_WAN_CONNECTION_NAME_LEN];
	/*媒体报文使用的WAN口名称*/
#define VOICE_RTP_VLAN_MARK_MIN (-1)
	word32 lVlanIdMark;/* 在RTP消息流打VLANID标签*/
#define VOICE_RTP_TELEVT_PT_MIN (96)
#define VOICE_RTP_TELEVT_PT_MAX (127)
	uword32 ulTelEvtPayloadType;/*RFC2833报文PT值*/

#define VOICE_RTCP_DISABLE (0)
#define VOICE_RTCP_ENABLE (1)
	uword8 ucRtcpEnable;/*RTCP是否使能，默认使能*/
#define VOICE_RTCPXR_DISABLE (0)
#define VOICE_RTCPXR_ENABLE (1)
	uword8 ucRtcpXrFlag;/*RTCPXR是否使能，默认使能*/
	uword8 aucPad1[CM_TWO_PADS];
	uword32 ulTxRepeatInterval;/*RTCP发送时间间隔*/
#define VOICE_RTCPRFC_DISABLE (0)
#define VOICE_RTCPRFC_ENABLE (1)
	uword8 ucRfcEnable;/*是否发起RFC2198冗余协商*/
#define VOICE_RTCPRFC_FIXED_START_DISABLE (0)
#define VOICE_RTCPRFC_FIXED_START_ENABLE (1)
	uword8 ucRfcEnableFixedStart;/*是否固定启动RFC2198*/
	uword8 aucPad2[CM_TWO_PADS];
#define VOICE_RFC_PT_MIN (0)
#define VOICE_RFC_PT_MAX (127)
	uword32 ulRfcPayloadType;/*RFC2198PT值*/

#define RTP_SERVICE_ATTR_MASK_BIT0_8021P_MARK (0x01)
#define RTP_SERVICE_ATTR_MASK_BIT1_DSCP_MARK (0x02)
#define RTP_SERVICE_ATTR_MASK_BIT2_WAN_NAME (0x04)
#define RTP_SERVICE_ATTR_MASK_BIT3_VLANID_MARK (0x08)
#define RTP_SERVICE_ATTR_MASK_BIT4_TELEVT_PT (0x10)
#define RTP_SERVICE_ATTR_MASK_BIT5_RTCP_ENABLE (0x20)
#define RTP_SERVICE_ATTR_MASK_BIT6_TXREPEAT_INTERVAL (0x40)
#define RTP_SERVICE_ATTR_MASK_BIT7_RTCP_XRFLAG (0x80)
#define RTP_SERVICE_ATTR_MASK_BIT8_RFC_ENABLE (0x100)
#define RTP_SERVICE_ATTR_MASK_BIT9_RFC_PT (0x200)
#define RTP_SERVICE_ATTR_MASK_BIT10_RFC_FIXED_START (0x400)
#define RTP_SERVICE_ATTR_MASK_ALL (0x7ff)
	uword32 ulBitmap;
} __PACK__ IgdVoiceRTPServiceAttrConfTab;

/***************************传真业务*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define VOICE_FAX_MODE_T30 (0)
#define VOICE_FAX_MODE_T38 (1)
	uword8 ucFaxMode;/*传真模式，默认为T.30模式*/
#define VOICE_FAX_DISABLE (0)
#define VOICE_FAX_ENABLE (1)
	uword8 ucFaxEnable;/*T.30模式，默认使能*/
#define VOICE_FAX_T30_CONTROL_TYPE_ALL (0)
#define VOICE_FAX_T30_CONTROL_TYPE_OTHER (1)
	uword8 ucFaxControlType;
	/*T.30模式时的传真协商方式，默认：all*/
#define VOICE_FAX_PORT_ADD2_DISABLE (0)
#define VOICE_FAX_PORT_ADD2_ENABLE (1)
	uword8 ucFaxPortAdd2;/*传真端口是否加2*/
#define VOICE_FAX_BITRATE_MIN (2400)
#define VOICE_FAX_BITRATE_MAX (33600)
	uword32 ulBitRate;/*T38传真训练最大速率*/
#define VOICE_FAX_TCF_LOCAL (0)
#define VOICE_FAX_TCF_NETWORK (1)
	uword8 ucTCFMethod;/*T38传真训练模式*/
	uword8 aucPad[CM_THREE_PADS];
#define VOICE_FAX_HIGHSPEED_REDUNDANCY_MIN (0)
#define VOICE_FAX_HIGHSPEED_REDUNDANCY_MAX (3)
	uword32 ulHighSpeedRedundancy;/*传真页数据帧冗余个数*/
#define VOICE_FAX_LOWSPEED_REDUNDANCY_MIN (0)
#define VOICE_FAX_LOWSPEED_REDUNDANCY_MAX (5)
	uword32 ulLowSpeedRedundancy;/*T30信号帧冗余个数*/

#define VOICE_FAX_ATTR_MASK_BIT0_FAX_MODE (0x01)
#define VOICE_FAX_ATTR_MASK_BIT1_FAX_ENABLE (0x02)
#define VOICE_FAX_ATTR_MASK_BIT2_FAX_CONTROL_TYPE (0x04)
#define VOICE_FAX_ATTR_MASK_BIT3_FAX_PORT_ADD2 (0x08)
#define VOICE_FAX_ATTR_MASK_BIT4_FAX_BIT_RATE (0x10)
#define VOICE_FAX_ATTR_MASK_BIT5_FAX_TCF_METHOD (0x20)
#define VOICE_FAX_ATTR_MASK_BIT6_FAX_HIGHSPEED_REDUNDANCY (0x40)
#define VOICE_FAX_ATTR_MASK_BIT7_FAX_LOWSPEED_REDUNDANCY (0x80)
#define VOICE_FAX_ATTR_MASK_ALL (0x0ff)
	uword32 ulBitmap;
} __PACK__ IgdVoiceFaxAttrConfTab;

/***************************线路Codec属性*********************************/
#define IGD_VOICE_LINE_CODEC_RECORD_NUM (16)


typedef struct {
	uword32 ulStateAndIndex;

#define VOICE_LINE_CODEC_DISABLE (0)
#define VOICE_LINE_CODEC_ENABLE (1)
	uword8 ucEnable;/*是否启用*/
#define VOICE_LINE_CODEC_PRIORITY_MIN (16)
#define VOICE_LINE_CODEC_PRIORITY_MAX (1)
	uword8 ucPriority;
#define VOICE_LINE_CODEC_NUM_MIN (1)
#define VOICE_LINE_CODEC_NUM_MAX (32)
	uword8 ucEntryID;/*Codec唯一标示号*/
#define VOICE_LINE_CODEC_TYPE_G711A (1)
#define VOICE_LINE_CODEC_TYPE_G711M (2)
#define VOICE_LINE_CODEC_TYPE_G729 (3)
#define VOICE_LINE_CODEC_TYPE_G722 (4)
#define VOICE_LINE_CODEC_TYPE_G726 (5)
#define VOICE_LINE_CODEC_TYPE_G729A (6)
#define VOICE_LINE_CODEC_TYPE_G729E (7)
#define VOICE_LINE_CODEC_TYPE_G728 (8)
#define VOICE_LINE_CODEC_TYPE_G723_1 (9)
#define VOICE_LINE_CODEC_TYPE_G722_1 (10)
#define VOICE_LINE_CODEC_TYPE_G722_2 (11)
#define VOICE_LINE_CODEC_TYPE_GSM_FR (12)
#define VOICE_LINE_CODEC_TYPE_GSM_HR (13)
#define VOICE_LINE_CODEC_TYPE_GSM_EFR (14)
#define VOICE_LINE_CODEC_TYPE_GSM_ILBC (15)
#define VOICE_LINE_CODEC_TYPE_DVI4_8000 (16)
#define VOICE_LINE_CODEC_TYPE_DVI4_16000 (17)
#define VOICE_LINE_CODEC_TYPE_DVI4_11025 (18)
#define VOICE_LINE_CODEC_TYPE_DVI4_22050 (19)
#define VOICE_LINE_CODEC_TYPE_LPC (20)
#define VOICE_LINE_CODEC_TYPE_L16_2C (21)
#define VOICE_LINE_CODEC_TYPE_L16_1C (22)
#define VOICE_LINE_CODEC_TYPE_QCELP (23)
#define VOICE_LINE_CODEC_TYPE_CN (24)
#define VOICE_LINE_CODEC_TYPE_MPA (25)
	uword8 ucCodec;/*Codec类型*/
	uword8 ucOmciCfg;
	uword8 ucPad[CM_THREE_PADS];
#define VOICE_LINE_CODEC_PACKETIZATION_PERIOD_LEN (32)
	word8 aucPacketizationPeriod[VOICE_LINE_CODEC_PACKETIZATION_PERIOD_LEN];/*打包间隔*/

#define VOICE_LINE_CODEC_CAPBILITY_ATTR_MASK_BIT0_ENABLE (0x01)
#define VOICE_LINE_CODEC_CAPBILITY_ATTR_MASK_BIT1_PRIORITY (0x02)
#define VOICE_LINE_CODEC_CAPBILITY_ATTR_MASK_BIT2_TYPE (0x04)
#define VOICE_LINE_CODEC_CAPBILITY_ATTR_MASK_BIT3_PACKETIZATION_PERIOD (0x08)
#define VOICE_LINE_CODEC_CAPBILITY_ATTR_MASK_CODEC (0x10)
#define VOICE_LINE_CODEC_CAPBILITY_ATTR_MASK_ALL (0x1f)
	uword32 ulBitmap;
} __PACK__ IgdVoiceLineCodecAttrConfTab;

/***************************语音线路测试*********************************/
typedef struct {
	uword32 ulStateAndIndex;

	uword32 ucLineIndex;
	uword32 ulExterLineVoltage;/*外线电压*/
	uword32 ulRingImpedance;/*振铃阻抗*/

#define LINE_DETECT_ATTR_MASK_BIT0_EXTER_LINE_VOLTAGE (0x01)
#define LINE_DETECT_ATTR_MASK_BIT1_RING_IMPEDANCE (0x02)
#define LINE_DETECT_ATTR_MASK_ALL (0x03)
	uword32 ulBitmap;
} __PACK__ IgdVoiceLineDetectInfoTab;

/***************************模拟主叫通话测试*********************************/
#define WEB_CONFIG 1

typedef struct {
	uword32 ulStateAndIndex;
	uword32 ucLineIndex;
	uword32 ucWebTag;
#define SIMULATE_TEST_TYPE_CALLER (0)
#define SIMULATE_TEST_TYPE_CALLED (1)
#define SIMULATE_TEST_TYPE_NONE (2)
	uword8 ucTestType;                                          /*仿真测试类型，"Caller" 主叫仿真，"Called" 被叫仿真，"None" 取消仿真*/
#define DIAL_DTMF_CONFIRM_DISABLE (0)
#define DIAL_DTMF_CONFIRM_ENABLE (1)
	uword8 ucDailDTMFConfirmEnable;                             /*拨号确认是否开启*/
	uword8 aucPad[CM_TWO_PADS];

#define SIMULATE_TEST_NUMBER_LEN (32)
	word8 aucCalledNumber[SIMULATE_TEST_NUMBER_LEN];           /*主叫仿真时的被叫号码*/
	word8 aucDailDTMFConfirmNumber[SIMULATE_TEST_NUMBER_LEN];  /*拨号确认的号码，只能是*#0-9的字符*/
	uword32 ucCallHoldTimer;
	uword32 ucCalledWaitTimer;

#define SIMULATE_TEST_ATTR_MASK_BIT0_TEST_TYPE (0x01)
#define SIMULATE_TEST_ATTR_MASK_BIT1_DIAL_DTMF_CONFIRM_ENABLE (0x02)
#define SIMULATE_TEST_ATTR_MASK_BIT2_CALLED_NUMBER (0x04)
#define SIMULATE_TEST_ATTR_MASK_BIT3_DIAL_DTMF_CONFIRM_NUMBER (0x08)
#define SIMULATE_TEST_ATTR_MASK_CALL_HOLD_TIMER (0x10)
#define SIMULATE_TEST_ATTR_MASK_CALLED_WAIT_TIMER (0x20)
#define SIMULATE_TEST_ATTR_MASK_ALL (0x3f)
	uword32 ulBitmap;
} __PACK__ IgdVoiceSimulateTestAttrConfTab;

/***************************模拟主叫通话测试结果*********************************/

typedef struct {
	uword32 ulStateAndIndex;

#define DIAL_DTMF_CONFIRM_RESULT_LEN (32)
	uword8 aucDailDTMFConfirmResult[DIAL_DTMF_CONFIRM_RESULT_LEN];/*拨号确认结果*/
#define SIMULATE_TEST_STATE_IDLE (0)
#define SIMULATE_TEST_STATE_OFF_HOOK (1)
#define SIMULATE_TEST_STATE_DIAL_TONE (2)
#define SIMULATE_TEST_STATE_RECIEVING (3)
#define SIMULATE_TEST_STATE_RECIEVE_END (4)
#define SIMULATE_TEST_STATE_RECIEVE_BACK (5)
#define SIMULATE_TEST_STATE_CONNECTED (6)
#define SIMULATE_TEST_STATE_TEST_END (7)
	uword8 ucStatus;/*仿真当前状态*/
#define SIMULATE_TEST_CONCLUSION_FIALED (0)
#define SIMULATE_TEST_CONCLUSION_SUCCESS (1)
	uword8 ucConclusion;/*仿真结果*/
#define SIMULATE_TEST_CALLER_FAILED_NO_DIAL_TONE (0)
#define SIMULATE_TEST_CALLER_FAILED_OFF_HOOK_RELEASE (1)
#define SIMULATE_TEST_CALLER_FAILED_DIALING_RELEASE (2)
#define SIMULATE_TEST_CALLER_FAILED_AFTER_DIAL_RELEASE (3)
#define SIMULATE_TEST_CALLER_FAILED_NO_ANSWER (4)
#define SIMULATE_TEST_CALLER_FAILED_KEY_ERROR (5)
	uword8 ucCallerFailReason;/*主叫仿真失败原因*/
#define SIMULATE_TEST_CALLED_FAILED_NO_INCOMING_CALL (0)
#define SIMULATE_TEST_CALLED_FAILED_OFF_HOOK_RELEASE (1)
#define SIMULATE_TEST_CALLED_FAILED_NO_ANSWER (2)
#define SIMULATE_TEST_CALLED_FAILED_KEY_ERROR (3)
	uword8 ucCalledFailReason;/*被叫仿真失败原因*/

#define SIMULATE_TEST_FAILED_RESPONSE_CODE_NONE (0)
	uword8 ucFailedResponseCode;/*仿真失败时接收到的错误码*/
	uword8 aucPad[CM_THREE_PADS];

#define SIMULATE_TEST_STATE_ATTR_MASK_BIT0_DIAL_DTMF_CONFIRM_RESULT (0x01)
#define SIMULATE_TEST_STATE_ATTR_MASK_BIT1_STATUS (0x02)
#define SIMULATE_TEST_STATE_ATTR_MASK_BIT2_CONCLUTION (0x04)
#define SIMULATE_TEST_STATE_ATTR_MASK_BIT3_CALLER_FAILED_REASON (0x08)
#define SIMULATE_TEST_STATE_ATTR_MASK_BIT4_CALLED_FAILED_REASON (0x10)
#define SIMULATE_TEST_STATE_ATTR_MASK_BIT5_FAILED_RESPONSE_CODE (0x20)
#define SIMULATE_TEST_STATE_ATTR_MASK_ALL (0x3f)
	uword32 ulBitmap;
} __PACK__ IgdVoiceSimulateTestStateInfoTab;

/***************************IAD模块诊断测试*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define VOICE_IAD_DIAGNOSTICS_STATE_NONE (0)
#define VOICE_IAD_DIAGNOSTICS_STATE_REQUESTED (1)
#define VOICE_IAD_DIAGNOSTICS_STATE_COMPLETE (2)
	uword8 ucIADDiagnosticsState;/*测试状态*/
#define VOICE_IAD_TEST_SERVER_PRIMARY (1)
#define VOICE_IAD_TEST_SERVER_BACKUP (2)
	uword8 ucTestServer;/*注册服务器*/
#define VOICE_IAD_REGIST_RESULT_SUCCESS (0)
#define VOICE_IAD_REGIST_RESULT_FAILED (1)
	uword8 ucRegistResult;/*注册是否成功*/
#define VOICE_IAD_REGIST_FAILED_REASON_IAD_MODULE_ERROR (1)
#define VOICE_IAD_REGIST_FAILED_REASON_ROUTE_UNREACHABLE (2)
#define VOICE_IAD_REGIST_FAILED_REASON_SERVER_NO_RESPONSE (3)
#define VOICE_IAD_REGIST_FAILED_REASON_ACCOUNT_ERROR (4)
#define VOICE_IAD_REGIST_FAILED_REASON_UNKNOWN_ERROR (5)
	uword8 ucRegistFailReason;/*注册失败原因*/

#define VOICE_IAD_TEST_STATE_ATTR_MASK_BIT0_STATE (0x01)
#define VOICE_IAD_TEST_STATE_ATTR_MASK_BIT1_TEST_SERVER (0x02)
#define VOICE_IAD_TEST_STATE_ATTR_MASK_BIT2_REGIST_RESULT (0x04)
#define VOICE_IAD_TEST_STATE_ATTR_MASK_BIT3_REGIST_FAIL_REASON (0x08)
#define VOICE_IAD_TEST_STATE_ATTR_MASK_ALL (0x0f)
	uword32 ulBitmap;
} __PACK__ IgdVoiceIADTestStateInfofTab;

/***************************物理端口测试*********************************/
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulInterfaceID;
#define VOICE_PHY_INTERFACE_TEST_STATE_NONE (0)
#define VOICE_PHY_INTERFACE_TEST_STATE_REQUESTED (1)
#define VOICE_PHY_INTERFACE_TEST_STATE_COMPLETE (2)
	uword8 ucTestState;/*测试状态指示和设置*/
#define VOICE_PHY_TEST_SELECTOR_PHONE_CONNECTIVITY_TEST (1)
#define VOICE_PHY_TEST_SELECTOR_CALL_SIMULATION_TEST    (2)
#define VOICE_PHY_TEST_SELECTOR_LINE_TEST               (3)
	uword8 ucTestSelector;/*进行测试的类型*/
#define VOICE_PHY_PHYPORT_LEN  (2)
	word8 ucPhyPort[VOICE_PHY_PHYPORT_LEN];
	uword8 aucPad[CM_TWO_PADS];

#define VOICE_PHY_INTERFACE_TEST_ATTR_MASK_BIT0_STATE (0x01)
#define VOICE_PHY_INTERFACE_TEST_ATTR_MASK_BIT1_TEST_SELECTOR (0x02)
#define VOICE_PHY_INTERFACE_TEST_ATTR_MASK_BIT2_TEST_INTERFACEID (0x04)
#define VOICE_PHY_INTERFACE_TEST_ATTR_MASK_PHY_PHYPORT (0x08)
#define VOICE_PHY_INTERFACE_TEST_ATTR_MASK_ALL (0x0F)
	uword32 ulBitmap;
} __PACK__ IgdVoicePhyInterfaceTestAttrConfTab;

/***************************物理端口测试结果*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define VOICE_PHY_INTERFACE_TEST_RESULT_PHONE_UNCONNECTED (0)
#define VOICE_PHY_INTERFACE_TEST_RESULT_PHONE_CONNECTED (1)
	uword8 ucPhoneConnectivity;/*PhoneConnectivityTest的测试结果，是否连接了话机*/
#define VOICE_PHY_INTERFACE_TEST_RESULT_PHONE_OFFHOOKALARM_OFF (0)
#define VOICE_PHY_INTERFACE_TEST_RESULT_PHONE_OFFHOOKALARM_ON (1)
	uword8 ucOffHookAlarmStatus;

#define VOICE_PHY_INTERFACE_TEST_STATE_ATTR_MASK_ALL (0x01)
	uword32 ulBitmap;
} __PACK__ IgdVoicePhyInterfaceTestStateInfoTab;

/***************************语音注册失败原因监控*********************************/
typedef struct {
	uword32 ulStateAndIndex;
	uword8 ucLineNum;/*线路编号：1个POTS口，固定为1*/
#define LAST_REG_ERROR_IAD (1)
#define LAST_REG_ERROR_ROUTER (2)
#define LAST_REG_ERROR_NORESP (3)
#define LAST_REG_ERROR_ACCOUNT (4)
#define LAST_REG_ERROR_UNKNOWN (5)
	uword8 ucReason; /*  */
	uword8 aucPad[CM_TWO_PADS];

#define VOICE_LINE_LAST_REG_ERROR_ATTR_MASK_ALL (0x01)
	uword32 ulBitmap;
} __PACK__ IgdLineLastRegisterErrorInfofTab;
/***************************物理端口语音质量最差记录*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define VOICE_RESET_STATS_FALSE (0)
#define VOICE_RESET_STATS_TRUE (1)
	uword8 ucResetStatistics;
	uword8 aucPad[CM_THREE_PADS];

#define VOICE_PHY_INTERFACE_STATS_RESET_MASK (0x01)
	uword32 ulBitmap;
} __PACK__ IgdVoicePoorQualityResetTab;

/***************************语音质量信息*********************************/
#define IGD_VOICE_PHY_INTERFACE_POOR_QUALITY_LIST_RECORD_NUM (10)
typedef struct {
	uword32 ulStateAndIndex;

#define POOR_QUALITY_LIST_IP_LEN  (128)
#define POOR_QUALITY_LIST_NUM_MIN (1)
#define POOR_QUALITY_LIST_NUM_MAX (10)
	uword8 ucListIndex;
	uword64 ullStatTime;
	uword32 ulTxPackets;
	uword32 ulRxPackets;
	uword32 ulMeanDelay;
	uword32 ulMeanJitter;
	uword8 ucFractionLoss;
	word8 aucLocalIPAddress[POOR_QUALITY_LIST_IP_LEN];
	uword32 ulLocalUDPPort;
	word8 aucFarEndIPAddress[POOR_QUALITY_LIST_IP_LEN];
	uword32 ulFarEndUDPPort;
	uword8 ucMosLq;
#define POOR_QUALITY_LIST_CODEC_TYPE_G711MULAW (0)
#define POOR_QUALITY_LIST_CODEC_TYPE_G711ALAW (1)
#define POOR_QUALITY_LIST_CODEC_TYPE_G729 (2)
#define POOR_QUALITY_LIST_CODEC_TYPE_G722 (3)
#define POOR_QUALITY_LIST_CODEC_TYPE_T38 (4)
	uword8 ucCodec;/*Codec类型*/

#define VOICE_PHY_INTERFACE_POOR_QUALITY_LIST_MASK_ALL (0x01)
	uword32 ulBitmap;
} __PACK__ IgdVoicePoorQualityListTab;

/***************************订阅相关业务参数*********************************/
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucLineNum;/*线路编号：1个POTS口，固定为1*/
#define STANDARD_DIAL_TONE (0)
#define SPECIAL_DIAL_TONE (1)
	uword8 ucPattern;/* 拨号音模式*/
#define MCID_SERVICE_PROVISIONED (0)
#define MCID_SERVICE_WITHDRAWN (1)
	uword8 ucMcidService;/* 恶意呼叫追查*/
#define NO_DIALING_REJECTCALL (0)
#define NO_DIALING_IMMEDIATECALL (1)
#define NO_DIALING_DEFERERCALL (2)
	uword8 ucNoDialing;/* 摘机不拨号*/
#define HOLD_SERVICE_PROVISIONED (0)
#define HOLD_SERVICE_WITHDRAWN (1)
	uword8 ucHoldService;/* 呼叫保持*/
#define THREE_PART_SERVICE_PROVISIONED (0)
#define THREE_PART_SERVICE_WITHDRAWN (1)
	uword8 ucThreePartService;/* 呼叫保持*/
#define CW_SERVICE_PROVISIONED (0)
#define CW_SERVICE_WITHDRAWN (1)
	uword8 ucCWService;/* 呼叫等待*/
	uword8 ucPad;

#define LINE_IMS_URI_LEN (64)
	word8 aucConferenceURL[LINE_IMS_URI_LEN];/*三方通话的会议URI*/
	word8 aucHotlineURL[LINE_IMS_URI_LEN];/*热线电话的URI*/

#define LINE_IMS_ATTR_MASK_DIAL_TONE_PATTERN (0x01)
#define LINE_IMS_ATTR_MASK_MCID_SERVICE (0x02)
#define LINE_IMS_ATTR_MASK_NO_DIAL (0x04)
#define LINE_IMS_ATTR_MASK_HOLD_SERVICE (0x08)
#define LINE_IMS_ATTR_MASK_THREE_PART_SERVICE (0x10)
#define LINE_IMS_ATTR_MASK_CONFERENCE_URL (0x20)
#define LINE_IMS_ATTR_MASK_HOTLINE_URL (0x40)
#define LINE_IMS_ATTR_MASK_CW_SERVICE (0x80)
#define LINE_IMS_ATTR_MASK_ALL (0x0ff)
	uword32 ulBitmap;
} __PACK__ IgdVoiceLineIMSAttrConfTab;

/***************************用户呼叫统计*********************************/
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucLineNum;/*线路编号：1个POTS口，固定为1*/
#define VOICE_LINE_RESET_STATS_FALSE (0)
#define VOICE_LINE_RESET_STATS_TRUE (1)
	uword8 ucResetStatistics;
	uword8 aucPad[CM_TWO_PADS];
	uword32 ulPacketsSent;
	uword32 ulPacketsReceived;
	uword32 ulBytesSent;
	uword32 ulBytesReceived;
	uword32 ulPacketsLost;
	uword32 ulIncomingCallsReceived;
	uword32 ulIncomingCallsAnswered;
	uword32 ulIncomingCallsConnected;
	uword32 ulIncomingCallsFailed;
	uword32 ulOutgoingCallsAttempted;
	uword32 ulOutgoingCallsAnswered;
	uword32 ulOutgoingCallsConnected;
	uword32 ulOutgoingCallsFailed;
	uword32 ulCallsDropped;
	uword32 ulTotalCallTime;
	uword32 ulReceivePacketLossRate;
	uword32 ulFarEndPacketLossRate;
	uword32 ulReceiveInterarrivalJitter;
	uword32 ulFarEndInterarrivalJitter;
	uword32 ulRoundTripDelay;
	uword32 ulServerDownTime;
#define VOICE_PHY_INTERFACE_LINE_STATS_MASK_RESET_STATS (0x01)
#define VOICE_PHY_INTERFACE_LINE_STATS_MASK_ALL (0x01)
	uword32 ulBitmap;
} __PACK__ IgdVoiceLineStatsAttrTab;

/***************************RTP 会话性能统计**begin*******************************/
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucLineNum;				/*线路编号：1个POTS口，固定为1*/
	uword8 aucPad[CM_THREE_PADS];
	uword32 ulRtpErrors;
	uword32 ulPacketLoss;
	uword32 ulMaxiJitter;
	uword32 ulMaxiBetwRtcp;
	uword32 ulBufferUnder;
	uword32 ulBufferOver;
	uword32 ulCallSetupFailures;
	uword32 ulLongestDurationSingleCallSetup;
	uword32 ulTerminatedCalls;
	uword32 ulTimesPortRelease;
	uword32 ulLongestPeriodOffHookDetected;

#define VOICE_LINE_CODEC_G711A (0)
#define VOICE_LINE_CODEC_G711M (1)
#define VOICE_LINE_CODEC_G729 (2)
#define VOICE_LINE_CODEC_G723_53K (3)
#define VOICE_LINE_CODEC_G723_63K (4)
#define VOICE_LINE_CODEC_ILBC (5)
#define VOICE_LINE_CODEC_VBDCLEAR (6)
#define VOICE_LINE_CODEC_G722 (7)
#define VOICE_LINE_CODEC_T38 (0x5a)
#define VOICE_LINE_CODEC_REDUNDANCY (0xff)
	uword32 ulCodec;

	uword32 ulBitmap;
} __PACK__ IgdVoiceLineRtpStatsAttrTab;

/***************************最近通话统计**begin*******************************/
#define CM_BEGIN_TIME_LEN 64
#define CM_CALL_DIRECT_LEN 8
#define CM_CALL_NUMBER_LEN 32
#define CM_CALL_CODEC_LEN 16

typedef struct {
	uword32 ulStateAndIndex;
	uword8 ucListIndex;				/*记录编号，1-11？*/
	uword8 aucPad[CM_THREE_PADS];

	uword8  aucBeginTime[CM_BEGIN_TIME_LEN];
	uword32 ulCallDuration;
	uword8  aucCallDirection[CM_CALL_DIRECT_LEN];
	uword8  aucCallerNumber[CM_CALL_NUMBER_LEN];
	word8  aucCalledNumber[CM_CALL_NUMBER_LEN];
	uword8  aucCodec[CM_CALL_CODEC_LEN];
	uword32 ulSendPacket;
	uword32 ulReceivePacket;
	uword32 ulLostPacket;
	uword32 ulDelay;
	uword32 ulJitter;
	uword32 ulMosLq;

	uword32 ulBitmap;
} __PACK__ IgdVoiceRecentCallStatsTab;

/***************************IAD Operation操作*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define VOICE_IAD_OPCODE_REGISTER     (0) //对软交换平台重新注册
#define VOICE_IAD_OPCODE_UNREGISTER_E (1) //从软交换平台注销
#define VOICE_IAD_OPCODE_RESET_E      (2) //复位，仅对语音模块复位
	uword32 ulOpCode;

#define VOICE_IAD_OPERATION_MASK_OPCODE (0x01)
#define VOICE_IAD_OPERATION_MASK_ALL    (0x01)
	uword32 ulBitmap;
} __PACK__ IgdVoiceIadOperationAttrTab;

/***************************POTS口 通话状态*begin*********************************/
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucLineNum;				/*线路编号：1个POTS口，固定为1*/
	uword8 aucPad[CM_THREE_PADS];

#define VOICE_LINE_SESSION_TYPE_IDLE       0
#define VOICE_LINE_SESSION_TYPE_2_WAY      1
#define VOICE_LINE_SESSION_TYPE_3_WAY      2
#define VOICE_LINE_SESSION_TYPE_FAX        3
#define VOICE_LINE_SESSION_TYPE_TELEM      4
#define VOICE_LINE_SESSION_TYPE_CONFERENCE 5
	uword32 ulSessionType;

#define VOICE_LINE_VOIP_LINE_STATE_IDLE_ONHOOK 0
#define VOICE_LINE_VOIP_LINE_STATE_OFFHOOK 1
#define VOICE_LINE_VOIP_LINE_STATE_DIALING 2
#define VOICE_LINE_VOIP_LINE_STATE_RINGING 3
#define VOICE_LINE_VOIP_LINE_STATE_AUDIBLE_RINGBACK 4
#define VOICE_LINE_VOIP_LINE_STATE_CONNECTING 5
#define VOICE_LINE_VOIP_LINE_STATE_CONNECTED 6
#define VOICE_LINE_VOIP_LINE_STATE_DISCONNECTING 7
#define VOICE_LINE_VOIP_LINE_STATE_OFFHOOKALARM_LOCK  8
#define VOICE_LINE_VOIP_LINE_STATE_HOLD  9
	uword32 ulVoipLineState;

#define VOICE_LINE_NO_EMERGENCY_CALL_IN_PROGRESS 0
#define VOICE_LINE_EMERGENCY_CALL_IN_PROGRESS 1
	uword32 ulEmergencyCallStatus;

	#define VOICE_LINE_CALL_STATS_ATTR_MASK_ALL (0x00)
	uword32 ulBitmap;
} __PACK__ IgdVoiceLineCallStatsTab;

typedef struct {
	uint32_t state_and_index;
#define VOICE_H248_INTERFACE_STATE_CLOSED (0)
#define VOICE_H248_INTERFACE_STATE_CLOSEING (1)
#define VOICE_H248_INTERFACE_STATE_RESTARTING (2)
#define VOICE_H248_INTERFACE_STATE_INSERVICE (3)
#define VOICE_H248_INTERFACE_STATE_GRACEFUL_CLOSED (4)
#define VOICE_H248_INTERFACE_STATE_MGC_DISCONNETED (5)
#define VOICE_H248_INTERFACE_STATE_MGC_SWITCHING (6)
	uint32_t interface_state;

#define VOICE_H248_INTERFACE_ATTR_STATE               (1 << 0)
#define VOICE_H248_INTERFACE_ATTR_MASK_ALL            ((1 << 1) - 1)
	uint32_t bit_map;
} __PACK__ igd_h248_interface_state_tab_t;

/***************************语音模块（IAD）基本信息查询*begin*********************************/
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucLineNum;				/*线路编号：1个POTS口，固定为1*/
	uword8 aucPad[CM_THREE_PADS];

#define IAD_MAC_ADDRESS_LEN (6)
	uword8 aucMACAddress[IAD_MAC_ADDRESS_LEN];

#define VOICE_PROTOCOL_SUPPORT_H248 0
#define VOICE_PROTOCOL_SUPPORT_SIP 1
	uword32 ulProtocolSupported;


#define IAD_SOFTWARE_VER_LEN (32)
	uword8 ucIadSoftwareVersion[IAD_SOFTWARE_VER_LEN];

#define IAD_SOFTWARE_TIME_LEN (32)
	uword8 ucIadSoftwareTime[IAD_SOFTWARE_TIME_LEN];

	uword32 ulVoipUserCount;

	uword32 ulBitmap;
} __PACK__ IgdVoiceIadInfoTab;

/********************VOIP会话性能统计查(SIP代理&&会话统计信息)*begin***********************/
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucLineNum;				/*线路编号：1个POTS口，固定为1*/
	uword8 aucPad[CM_THREE_PADS];

	uword32 initiated_cnt;            //Transactions initiated
	uword32 recv_invite_cnt;          //Invite received
	uword32 recv_re_invite_cnt;       //Invite retransmission received
	uword32 recv_noinvite_cnt;        //Non-invite received
	uword32 recv_re_noinvite_cnt;     //Non-invite retransmission received
	uword32 recv_response_cnt;        //Responses received
	uword32 recv_re_response_cnt;     //Response retransmissions received
	uword32 send_invite_cnt;          //Invite transmitted
	uword32 send_re_invite_cnt;       //Invite retransmission transmitted
	uword32 send_noinvite_cnt;        //Non-invite transmitted
	uword32 send_re_noinvite_cnt;     //Non-invite retransmission transmitted
	uword32 send_response_cnt;        //Total responses sent
	uword32 send_re_response_cnt;     //Response retransmissions sent
	uword32 connect_fail_cnt;         //Failed to reach/connect its TCP/UDP peer
	uword32 check_fail_cnt;           //Failed to validate its peer
	uword32 timeout_cnt;              //Times of timed out
	uword32 recv_err_code_cnt;        //Received failure error code
	uword32 authentication_fail_cnt;  //Failed authentication

	uword32 ulBitmap;
} __PACK__ IgdVoiceSipUsageStatsTab;

/***************************线路测试*********************************/
/* IGD_X_CU_LINE_TEST_ATTR_TAB */
#define IGD_X_CU_LINE_TEST_ATTR_MAX 1
typedef struct {
	uint32_t state_and_index;
#define X_CU_LINE_TEST_IS_TEST_ON_NOT_BUSY (0)
#define X_CU_LINE_TEST_IS_TEST_ON_BUSY (1)
	uint8_t  is_test_on_busy;
#define X_CU_LINE_TEST_CONCLUSION_NORMAL (0)
#define X_CU_LINE_TEST_CONCLUSION_PHONE_DISCONNECT (1)
#define X_CU_LINE_TEST_CONCLUSION_PHONE_OFFHOOK (2)
#define X_CU_LINE_TEST_CONCLUSION_POWER_LINE_CONTACTED (3)
#define X_CU_LINE_TEST_CONCLUSION_BOTH_LOOP_LINE_MIX_OTHER (4)
#define X_CU_LINE_TEST_CONCLUSION_ALINE_MIX_OTHER (5)
#define X_CU_LINE_TEST_CONCLUSION_BLINE_MIX_OTHER (6)
#define X_CU_LINE_TEST_CONCLUSION_BOTH_LINE_GROUNDING (7)
#define X_CU_LINE_TEST_CONCLUSION_ALINE_GROUNDING (8)
#define X_CU_LINE_TEST_CONCLUSION_BLINE_GROUNDING (9)
#define X_CU_LINE_TEST_CONCLUSION_ABLINE_POOR_INSULATION (10)
#define X_CU_LINE_TEST_CONCLUSION_SHORT_CIRCUIT (11)
#define X_CU_LINE_TEST_CONCLUSION_BOTH_LINE_LEAK_AGE_TO_GROUND (12)
#define X_CU_LINE_TEST_CONCLUSION_ALINE_LEAK_AGE_TO_GROUND (13)
#define X_CU_LINE_TEST_CONCLUSION_BLINE_LEAK_AGE_TO_GROUND (14)
	uint8_t  conclusion;
	uint8_t pads[CM_TWO_PADS];
	int32_t ag_ac_voltage;
	int32_t bg_ac_voltage;
	int32_t ab_ac_voltage;
	int32_t ag_dc_voltage;
	int32_t bg_dc_voltage;
	int32_t ab_dc_voltage;
	uint32_t ag_insulation_resistance;
	uint32_t bg_insulation_resistance;
	uint32_t ab_insulation_resistance;
	uint32_t bg_capacitance;
	uint32_t ag_capacitance;
	uint32_t ab_capacitance;
#define X_CU_LINE_TEST_ATTR_MASK_IS_TEST_ON_BUSY                 (1 << 0)
#define X_CU_LINE_TEST_ATTR_MASK_ALL                            ((1 << 1) - 1)
	uint32_t bit_map;
} __PACK__ igd_x_cu_line_test_attr_conf_tab_t;


/***************************物理端口拓展*********************************/
/* IGD_X_CU_EXTEND_ATTR_TAB */
#define IGD_X_CU_EXTEND_ATTR_MAX 1
typedef struct {
	uint32_t state_and_index;
#define X_CU_EXTEND_DIALMODE_FIRSET (0)
#define X_CU_EXTEND_DIALMODE_DTMF (1)
#define X_CU_EXTEND_DIALMODE_PULSE (2)
	uint8_t  dial_mode;
#define X_CU_EXTEND_REVERSE_POLE_PULSE_DISABLE (0)
#define X_CU_EXTEND_REVERSE_POLE_PULSE_ENABLE (1)
	uint8_t  reverse_pole_pulse;
#define X_CU_EXTEND_KCTYPE_16KC (0)
#define X_CU_EXTEND_KCTYPE_12KC (1)
	uint8_t  kc_type;
#define X_CU_EXTEND_CLIP_TRANS_WHEN_AFTER_RING (0)
#define X_CU_EXTEND_CLIP_TRANS_WHEN_BEFORE_RING (1)
	uint8_t  clip_trans_when;
	uint32_t hook_flash_up_time;
	uint32_t hook_flash_down_time;
	uint32_t ring_dc_voltage_overlapped;
#define X_CU_EXTEND_CLIP_REVERSE_POLE_DISABLE (0)
#define X_CU_EXTEND_CLIP_REVERSE_POLE_ENABLE (1)
	uint8_t  clip_reverse_pole;
#define X_CU_EXTEND_MWI_RING_FLAG_DISABLE (0)
#define X_CU_EXTEND_MWI_RING_FLAG_ENABLE (1)
	uint8_t  mwi_ring_flag;
#define X_CU_EXTEND_DATA_SERVICE_MODE_DISABLE (0)
#define X_CU_EXTEND_DATA_SERVICE_MODE_FAX (1)
#define X_CU_EXTEND_DATA_SERVICE_MODE_MODEM (2)
	uint8_t data_service_mode;
	uint8_t pad;
#define X_CU_EXTEND_ATTR_MASK_DIAL_MODE                           (1 << 0)
#define X_CU_EXTEND_ATTR_MASK_REVERSE_POLE_PULSE                  (1 << 1)
#define X_CU_EXTEND_ATTR_MASK_KC_TYPE                             (1 << 2)
#define X_CU_EXTEND_ATTR_MASK_CLIP_TRANS_WHEN                     (1 << 3)
#define X_CU_EXTEND_ATTR_MASK_HOOK_FLASH_UP_TIME                  (1 << 4)
#define X_CU_EXTEND_ATTR_MASK_HOOK_FLASH_DOWN_TIME                (1 << 5)
#define X_CU_EXTEND_ATTR_MASK_RING_DC_VOLTAGE_OVERLAPPED          (1 << 6)
#define X_CU_EXTEND_ATTR_MASK_CLIP_REVERSE_POLE                   (1 << 7)
#define X_CU_EXTEND_ATTR_MASK_MWI_RING_FLAG                       (1 << 8)
#define X_CU_EXTEND_ATTR_MASK_CLIP_FORMAT                         (1 << 9)
#define X_CU_EXTEND_ATTR_MASK_DATA_SERVICE_MODE                   (1 << 10)
#define X_CU_EXTEND_ATTR_MASK_ALL                                 ((1 << 11) - 1)
	uint32_t bit_map;
} __PACK__ igd_cu_extend_attr_conf_tab_t;

#define VOICE_EVENT_TYPE_ONHOOK 0
#define VOICE_EVENT_TYPE_OFFHOOK 1
#define VOICE_EVENT_TYPE_REGISTER 2
typedef struct {
	uint32_t line_id;
	uint32_t event_type; /* 0:onhook, 1:offhook 2:register */
	uint32_t description;
} IgdVoiceSysEventTab;

#endif
