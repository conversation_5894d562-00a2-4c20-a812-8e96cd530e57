/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm device alarm obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_DEVICE_ALARM_H
#define HI_ODL_DEVICE_ALARM_H
#include "hi_odl_basic_type.h"

enum igd_alarm_state {
	ALARM_STATE_CLEAR = 0,
	ALARM_STATE_OCCUR,
	ALARM_STATE_CLEAR_FIX,
	ALARM_STATE_OCCUR_FIX,
	ALARM_STATE_NON_RECOVERABLE,
	ALARM_STATE_MAX,
};

enum igd_alarm_level {
	ALARM_LEVEL_SERIOUS = 1,
	ALARM_LEVEL_MAJOR,
	ALARM_LEVEL_MINOR,
};

enum igdAlarmIndex {
	ALARM_ID_DEVICE_REBOOT_INDEX,
	ALARM_ID_PORT_UNAVAILAVLE_INDEX,
	ALARM_ID_WLAN_HW_FAULT_INDEX,
	ALARM_ID_DEVICE_CPU_OVERLAOD_INDEX,
	ALARM_ID_DEVICE_MEM_OVERLAOD_INDEX,
	ALARM_ID_SYSLOG_SIZE_LIMIT_INDX,
	ALARM_ID_FLASH_SIZE_LIMIT_INDEX,
	ALARM_ID_DEVICE_LOGIN_EXCEED_INDEX,
	ALARM_ID_FILE_UNREACH_INDEX,
	ALARM_ID_FILE_AUTH_FAIL_INDEX,
	ALARM_ID_FILE_DNLOAD_OVERTIME_INDEX,
	ALARM_ID_FILE_SERVIER_INVALID_INDEX,
	ALARM_ID_FILE_UPDATE_CONFIG_FAIL_INDEX,
	ALARM_ID_FILE_UPLOAD_CONFIG_FAIL_INDEX,
	ALARM_ID_FILE_DONWLOAD_CONFIG_FAIL_INDEX,
	ALARM_ID_FILE_CONFIG_INAVAILD_INDEX,
	ALARM_ID_FILE_WEB_UPGRADE_FAILURE_INDEX,
	ALARM_ID_FILE_UPLOAD_LOG_FAIL_INDEX,
	ALARM_ID_UPGRADE_FAIL_INDEX,
	ALARM_ID_VOIP_SERVER_UNREACH_INDEX,
	ALARM_ID_VOIP_REG_FAILURE_AUTH_INDEX,
	ALARM_ID_VOIP_REG_FAILURE_OTHER_INDEX,
	ALARM_ID_VOIP_REG_NO_RESPONSE_INDEX,
	ALARM_ID_DDNS_SERVER_UNREACH_INDEX,
	ALARM_ID_DDNS_AUTH_FAIL_INDEX,
	ALARM_ID_APP_RUN_EXIT_INDEX,
	ALARM_ID_WAN_DOWN_INDEX,
	ALARM_ID_STA_RSSI_LOW_INDEX,
	ALARM_ID_MAX,
};

#endif
