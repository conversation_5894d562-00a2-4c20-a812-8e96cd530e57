/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm port mirror obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_PORT_MIRROR_H
#define HI_ODL_TAB_PORT_MIRROR_H
#include "hi_odl_basic_type.h"

/* IGD_PORTMIIROR_ATTR_TAB */
#define IGD_PORTMIIROR_ATTR_MAX 1
typedef struct {
	uword32 ulStateAndIndex;
#define PORT_MIRROR_LEN 16
	word8 aucIgr[PORT_MIRROR_LEN];
	word8 aucEgr[PORT_MIRROR_LEN];
	word8 aucDport[PORT_MIRROR_LEN];
	word8 aucType[PORT_MIRROR_LEN];
	word8 aucSaveflag[PORT_MIRROR_LEN];
} __PACK__ IgdCmPortMirrorConfTab;

#endif
