/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm global obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_GLOBAL_INFO_H
#define HI_ODL_GLOBAL_INFO_H
#include "hi_odl_basic_type.h"

typedef struct {
	uword32 ulStateAndIndex;
#define DEVICE_SUMMARY_LEN (128)
	word8 aucDevSummary[DEVICE_SUMMARY_LEN];/*设备描述*/
	uword8 ucPotsNum;/*POTS口个数，默认为1*/
	uword8 ucLanDeviceNum;/*LAN设备个数，默认为1*/
	uword8 ucWanDeviceNum;/*WAN设备个数，默认为1*/
	uword8 ucLanPortNum;/* LAN口个数。*/
	uword8 ucGePortNum;/*Ge口个数，默认为1。*/
	uword8 ucFePortNum;/*Fe口个数，默认为1。*/
	uword8 ucUsbPortNum;/*USB接口个数，默认为0*/
	uword8 ucWlanNum; /*WLAN接口个数，默认为0*/
#define DEVICE_CARD_NOT_SUPPORT 0
#define DEVICE_CARD_SUPPORT 1
	uword8 ucCardSupport; /*默认值0：不支持*/
	uword8 ucNFCSupport;  /*默认值0：不支持*/
	uword8 Wlan24Num;  /* wlan 2.4g ssid num */
	uword8 ucUsb3PortNum;/*USB3.0接口个数，默认为0*/

#define DEV_CAPBILITY_ATTR_MASK_BIT0_SUMMARY (0x01)
#define DEV_CAPBILITY_ATTR_MASK_BIT1_POTSNUM (0x02)
#define DEV_CAPBILITY_ATTR_MASK_BIT2_LANDEVNUM (0x04)
#define DEV_CAPBILITY_ATTR_MASK_BIT3_WANDEVNUM (0x08)
#define DEV_CAPBILITY_ATTR_MASK_BIT4_LANNUM (0x10)
#define DEV_CAPBILITY_ATTR_MASK_BIT5_USBNUM (0x20)
#define DEV_CAPBILITY_ATTR_MASK_BIT6_WLANNUM (0x40)
#define DEV_CAPBILITY_ATTR_MASK_BIT7_CARDSUPPORT (0x80)
#define DEV_CAPBILITY_ATTR_MASK_ALL               (0xfff)
	uword32 ulBitmap;
} __PACK__ IgdDevCapabilityTab;

typedef struct {
	uword32 ulStateAndIndex;
#define DEVICE_MANUFACTURER_LEN (64)
	word8 aucManufacturer[DEVICE_MANUFACTURER_LEN];/* 生产厂家 */
#define DEVICE_MANUFACTURE_OUI_LEN (6)
	word8 aucManufacturerOUI[DEVICE_MANUFACTURE_OUI_LEN + 1];
	uword8 ucBMSRegType;    /* 插件中心注册方式，默认跟ucPonRegType保持一致 */
#define DEVICE_MODEL_NAME_LEN (64)
	word8 aucModelName[DEVICE_MODEL_NAME_LEN];
#define DEVICE_DESCRIPTION_LEN (64)
	word8 aucDevDesc[DEVICE_DESCRIPTION_LEN];
#define DEVICE_PRODUCTCLASS_LEN (64)
	word8 aucProductClass[DEVICE_PRODUCTCLASS_LEN]; /* 设备型号 */
#define DEVICE_SERIAL_NUMBER_LEN (128)
	word8 aucSerialNum[DEVICE_SERIAL_NUMBER_LEN]; /* 设备标识号 */
#define DEVICE_CTEI_NUM_LEN (16)
	char ctei_num[DEVICE_CTEI_NUM_LEN]; /* 设备CTEI码  格式 <TUI><终端序列号> 共15位，其中<TUI>为8位，<终端序列号>为7位，示例：181234561234567， */

#define DEVICE_HARDWARE_VER_LEN (64)
	word8 aucHardVer[DEVICE_HARDWARE_VER_LEN]; /* 硬件版本 */
#define DEVICE_SOFTWARE_VER_LEN (64)
	word8 aucSoftVer[DEVICE_SOFTWARE_VER_LEN]; /* 软件版本 */
#define DEVICE_SOFTWARE_COMPILE_DATE_LEN (64)
	word8 aucSoftCompileDate[DEVICE_SOFTWARE_COMPILE_DATE_LEN]; /* 软件版本编译时间 */
#define DEVICE_LAN_ABILITY_LEN (64)
	word8 aucLanAbility[DEVICE_LAN_ABILITY_LEN]; /* 智能网关以太网接口能力 */
#define DEVICE_SPEC_VERSION_LEN (32)
	word8 aucSpecVer[DEVICE_SPEC_VERSION_LEN];

#define DEVICE_PROVINCE_LEN (16)
	word8 aucProvince[DEVICE_PROVINCE_LEN];/*省份*/
#define DEVICE_OPERATOR_LEN (12)
	word8 aucOperator[DEVICE_OPERATOR_LEN];/*运营商*/
#define DEVICE_BATTERY_STATUS_NORMAL (1)
#define DEVICE_BATTERY_STATUS_MISSING (2)
#define DEVICE_BATTERY_STATUS_FAILURE (3)
#define DEVICE_BATTERY_STATUS_VOLTLOW (4)
#define DEVICE_BATTERY_STATUS_NA (5)
	uword8 ucBatteryStatus;
#define DEVICE_TYPE_STANDARD_E8B (1)
#define DEVICE_TYPE_NORMAL_E8B (2)
#define DEVICE_TYPE_STANDARD_E8C (3)
#define DEVICE_TYPE_NORMAL_E8C (4)
#define DEVICE_TYPE_AP_EXTERNAL_E8C (5)
#define DEVICE_TYPE_1PLUS_1SFU (6)
#define DEVICE_TYPE_INTELLIGENT_GATEWAY_FORM_2P1 (7)
#define DEVICE_TYPE_INTELLIGENT_GATEWAY_FORM_2P0 (8)
#define DEVICE_TYPE_INTELLIGENT_GATEWAY_SFN_4P1 (9)
#define DEVICE_TYPE_INTELLIGENT_GATEWAY_DFN_4P1 (10)
#define DEVICE_TYPE_INTELLIGENT_GATEWAY_PRO (11)
#define DEVICE_TYPE_INTELLIGENT_ROUTER (12)
#define DEVICE_TYPE_INTELLIGENT_GATEWAY_NO_WIFI (13)
#define DEVICE_TYPE_INTELLIGENT_GATEWAY_NO_WIFI_NO_VOIP (14)
#define DEVICE_TYPE_INTELLIGENT_GATEWAY_MASTER (40)
#define DEVICE_TYPE_INTELLIGENT_GATEWAY_SLAVE  (41)
#define DEVICE_TYPE_OTHER (100)
	uword8 ucDeviceType; /*设备类型, 默认3：标准型e8-C网关 */
#define DEVICE_UPLINK_TYPE_ADSL2PLUS (1)
#define DEVICE_UPLINK_TYPE_LAN (2)
#define DEVICE_UPLINK_TYPE_VDSL2 (3)
#define DEVICE_UPLINK_TYPE_EPON (4)
#define DEVICE_UPLINK_TYPE_GPON (5)
#define DEVICE_UPLINK_TYPE_10GEPON (6)
#define DEVICE_UPLINK_TYPE_XGPON (7)
#define DEVICE_UPLINK_TYPE_XGSPON (8)
	uword8 ucUplinkType;/*线路协议，默认值：EPON(4)，GPON(5)*/
#define PON_REG_TYPE_LOID (0)
#define PON_REG_TYPE_PASSWORD (1)
#define PON_REG_TYPE_SN (2)
	uword8 ucPonRegType;/*pon注册模式选择，默认值：Password*/

#define DEVICE_CARD_KEY_LEN (16)
	word8 aucCardKey[DEVICE_CARD_KEY_LEN];
#define DEVICE_INTERFACE_VER_LEN (32)
	word8 aucInterfaceVer[DEVICE_INTERFACE_VER_LEN];
#define DEVICE_CARD_INTER_VER_LEN (16)
	word8 aucCardInterVer[DEVICE_CARD_INTER_VER_LEN];
#define DEVINFO_CAPABILITY_LEN (64)
	word8 aucCapability[DEVINFO_CAPABILITY_LEN];
#define DEVICE_WEB_TYPE_VERTICAL (0)
#define DEVICE_WEB_TYPE_HORIZONTAL (1)
	uword8 ucWebType;
	uword8 ucWebHttps;
	word8 aucPad[CM_TWO_PADS];
#define DEVINFO_CONFIGFILEINFO_LEN (32)
	word8 aucConfigFileInfo[DEVINFO_CONFIGFILEINFO_LEN];
#define DEVINFO_ITMSVERSION_LEN (32)
	word8 aucITMSVersion[DEVINFO_ITMSVERSION_LEN];
#define DEVINFO_SPECVERSION_LEN (32)
	word8 aucSpecVersion[DEVINFO_SPECVERSION_LEN];
#define DEVINFO_DEVNAME_LEN (128)
	word8 aucDevName[DEVINFO_DEVNAME_LEN];
#define DEVINFO_DEVTYPE_LEN (16)
	uword8 aucDeviceType[DEVINFO_DEVTYPE_LEN];
#define DEVINFO_CLASS_LEN (64)
	uword8 aucFlashClass[DEVINFO_CLASS_LEN];
	uword8 aucMemoryClass[DEVINFO_CLASS_LEN];
	uword8 aucWiFiChipClass[DEVINFO_CLASS_LEN];

#define DEVINFO_DEVICECATEGORY_STRING_LEN                   	 (64)
	uint8_t  aucdevicecategory[DEVINFO_DEVICECATEGORY_STRING_LEN];
#define DEVINFO_SERIALNUMBER_STRING_LEN                     	 (64)
	uint8_t  aucserialnumber[DEVINFO_SERIALNUMBER_STRING_LEN];
#define DEVINFO_PROVISIONINGCODE_STRING_LEN                      (64)
	uint8_t  aucprovisioningcode[DEVINFO_PROVISIONINGCODE_STRING_LEN];
#define DEVINFO_UPTIME_STRING_LEN                                (32)
	uint8_t  aucuptime[DEVINFO_UPTIME_STRING_LEN];
#define DEVINFO_IPPROTOCOLVERSION_STRING_LEN                	 (32)
	uint8_t  aucipprotocolversion[DEVINFO_IPPROTOCOLVERSION_STRING_LEN];
#define DEVINFO_CHIPSET_STRING_LEN                          	 (32)
	uint8_t  aucchipset[DEVINFO_CHIPSET_STRING_LEN];
#define DEVINFO_OS_STRING_LEN                               	 (256)
	uint8_t  aucos[DEVINFO_OS_STRING_LEN];
#define DEVINFO_MAC_LEN                                           (6)
	uint8_t  gateway_mac[DEVINFO_MAC_LEN];

#define DEVINFO_CHIP_ID_LEN 32
	word8 chip_id[DEVINFO_CHIP_ID_LEN];
#define DEVINFO_CHECK_KEY_LEN DEVINFO_CHIP_ID_LEN
	word8 check_key[DEVINFO_CHECK_KEY_LEN];

#define DEVICEINFO_ATTR_MASK_BIT0_MANUFACTURE (0x01)
#define DEVICEINFO_ATTR_MASK_BIT1_OUI (0x02)
#define DEVICEINFO_ATTR_MASK_BIT2_MODELNAME (0x04)
#define DEVICEINFO_ATTR_MASK_BIT3_DESCRIPTION (0x08)
#define DEVICEINFO_ATTR_MASK_BIT4_PRODUCTCLASS (0x10)
#define DEVICEINFO_ATTR_MASK_BIT5_SERIALNUM (0x20)
#define DEVICEINFO_ATTR_MASK_BIT6_HARDWAREVER (0x40)
#define DEVICEINFO_ATTR_MASK_BIT7_SOFTWAREVER (0x80)
#define DEVICEINFO_ATTR_MASK_BIT8_SPECVERSION (0x100)
#define DEVICEINFO_ATTR_MASK_BIT9_BATTERY_STATUS (0x200)
#define DEVICEINFO_ATTR_MASK_BIT10_DEVICE_TYPE (0x400)
#define DEVICEINFO_ATTR_MASK_BIT11_UPLINK_TYPE (0x800)
#define DEVICEINFO_ATTR_MASK_BIT12_CARDKEY (0x1000)
#define DEVICEINFO_ATTR_MASK_BIT13_wordERFACEVER (0x2000)
#define DEVICEINFO_ATTR_MASK_BIT14_CARDwordERVER (0x4000)
#define DEVICEINFO_ATTR_MASK_BIT15_CAPBILITY (0x8000)
#define DEVICEINFO_ATTR_MASK_BIT16_CONFIGFILEINFO (0x10000)
#define DEVICEINFO_ATTR_MASK_BIT17_SPECVERSION (0x20000)
#define DEVICEINFO_ATTR_MASK_BIT18_DEVNAME (0x40000)
#define DEVICEINFO_ATTR_MASK_BIT19_REGTYPE (0x80000)
#define DEVICEINFO_ATTR_MASK_BIT20_BMSREGTYPE (0x100000)
#define DEVICEINFO_ATTR_MASK_BIT21_ITMSVERSION (0x200000)
#define DEVICEINFO_ATTR_MASK_BIT22_DEVTYPE (0x400000)
#define DEVICEINFO_ATTR_MASK_BIT23_PROVISIONINGCODE                       (1 << 23)
#define DEVICEINFO_ATTR_MASK_BIT24_IPPROTOCOLVERSION                	  (1 << 24)
#define DEVICEINFO_ATTR_MASK_CTEI_NUM                                     (1 << 25)
#define DEVICEINFO_ATTR_MASK_BIT26_CHIP_ID (1 << 26)
#define DEVICEINFO_ATTR_MASK_BIT27_CHECK_KEY (1 << 27)
#define DEVICEINFO_ATTR_MASK_ALL                                         (0xffffffff)
	uword32 ulBitmap;
} __PACK__ IgdDevInfoTab;

typedef struct {
	uword32 ulStateAndIndex;

#define IP_PROTOCOL_VERSION_V4 (1)
#define IP_PROTOCOL_VERSION_V6 (2)
#define IP_PROTOCOL_VERSION_V4_OR_V6 (3)
	uword8 ucIPProtocolVer; /* 网关IP协议版本，默认：3 */
#define IP_FORWARD_MODE_DISABLE (0)
#define IP_FORWARD_MODE_ENABLE (1)
	uword8 ucIPForwardEnable;/*全路由模式启用：默认不启用*/
#define DEVICE_RECONNECT_DISABLE (0)
#define DEVICE_RECONNECT_ENABLE (1)
	uword8 ucReconnectEnable; /*断线重连：使能*/
#define DLNA_DMS_DISABLE (0)
#define DLNA_DMS_ENABLE (1)
	uword8 ucDMSEnable; /*DLNA的DMS是否开启,默认开启*/
#define PROVISIONING_CODE_LEN (64)
	word8 aucProvCode[PROVISIONING_CODE_LEN];
#define DEVICE_NAME_LEN (32)
	word8 aucDeviceName[DEVICE_NAME_LEN];/*家庭网络命名*/

#define DEV_GLOBAL_LED_DISABLE (0)
#define DEV_GLOBAL_LED_ENABLE  (1)
	uword32 ulLedSwitch;    /* 全局LED 开关 */
	uword32 ulwanBindwidthTime;  /*各条WAN连接的上下行实际使用带宽的计量及限制单位为kbps。计量周期缺省为1秒*/
#define DEV_GLOBAL_MODE_BRIDGE   (0)
#define DEV_GLOBAL_MODE_ROUTE    (1)
#define DEV_GLOBAL_MODE_REPEATER (2)
#define DEV_GLOBAL_MODE_EASYMESH_REPEATER (4)
	uword32 ulModeType;         /* 设备形态 */
#define DEV_GLOBAL_MODE_ERPEATER_PROXY		(0)
#define DEV_GLOBAL_MODE_ERPEATER_MULTI		(1)
#define DEV_GLOBAL_MODE_ERPEATER_WDS		(2)
#define DEV_GLOBAL_MODE_MLD_REPEATER 		(8)
#define DEV_GLOBAL_MODE_MLD_EASYMESH 		(16)
	uword32 ulRepeaterModeType; /* repeater状态下 三种模式 */
#define DEV_GLOBAL_FACTORY_ENABLE (0)
#define DEV_GLOBAL_FACTORY_DISENABLE  (1)
	uword32 ulfactoryenable;
#define DEV_GLOBAL_LOCK_DISENABLE (0)
#define DEV_GLOBAL_LOCK_ENABLE  (1)
	uword32 ulLockStatus;
#define DEV_GLOBAL_ATTR_MASK_BIT0_IPPROTOCOL_VER (0x01)
#define DEV_GLOBAL_ATTR_MASK_BIT1_IPFORWARD_ENABLE (0x02)
#define DEV_GLOBAL_ATTR_MASK_BIT2_RECONNECT_ENABLE (0x04)
#define DEV_GLOBAL_ATTR_MASK_BIT3_DMS_ENABLE (0x08)
#define DEV_GLOBAL_ATTR_MASK_BIT4_PROVISIONING_CODE (0x10)
#define DEV_GLOBAL_ATTR_MASK_BIT5_DEVICE_NAME (0x20)
#define DEV_GLOBAL_ATTR_MASK_BIT6_LED_SWITCH (0x40)
#define DEV_GLOBAL_ATTR_MASK_BIT7_WAN_BINDWIDTH_TIME (0x80)
#define DEV_GLOBAL_ATTR_MASK_BIT8_MODE_TYPE   (0x100)
#define DEV_GLOBAL_ATTR_MASK_BIT9_REPEAGER_MODE_TYPE   (0x200)
#define DEV_GLOBAL_ATTR_MASK_BIT9_FACTORY_ENABLE   (0x400)
#define DEV_GLOBAL_ATTR_MASK_BIT11_LOCK_ENABLE   (0x800)
#define DEV_GLOBAL_ATTR_MASK_ALL (0xfff)
	uword32 ulBitmap;
} __PACK__ IgdDevGlobalAttrConfTab;


typedef struct {
	uword32 ulStateAndIndex;

#define PON_PHY_LINK_STATUS_UP (1)
#define PON_PHY_LINK_STATUS_INITIALIZING (2)
#define PON_PHY_LINK_STATUS_ESTABLISHED_LINK (3)
#define PON_PHY_LINK_STATUS_NO_SIGNAL (4)
#define PON_PHY_LINK_STATUS_ERROR (5)
#define PON_PHY_LINK_STATUS_DISABLE (6)
	uword8 ucPonPhyStatus;/* pon链路连接状态*/
#define PON_CONNECTION_STATUS_UP (1)
#define PON_CONNECTION_STATUS_DOWN (2)
	uword8 ucPonConnStatus;
	/*pon 连接状态：已连接or未连接 认证通过的表示已连接*/
#define LOID_AUTH_STATUS_FIBER_DISCONNECTED (1)
#define LOID_AUTH_STATUS_AUTH_SUCCESS (2)
#define LOID_AUTH_STATUS_AUTH_FAILURE (3)
	uword8 ucLoidAuthStatus; /*光纤未连接 | 认证成功 | 认证失败*/
#define ITMS_REG_STATUS_UNREGISTERD (0)
#define ITMS_REG_STATUS_CONNECTED (1)
#define ITMS_REG_STATUS_DISCONNECTED (2)
#define ITMS_REG_STATUS_REGISTING (3)
	uword8 ucItmsRegStatus;/*ITMS 注册状态：未注册 | 注册成功 | 注册失败*/

	uword32 ulPonConectedTime; /*连接持续时间*/
	uword32 ulDevUpTime; /*系统上电时间，单位：s*/
	uword64 ullCurLocalTime;/*当前系统时间*/
#define  PON_STANDARDS_SUPPORTED_EPON (0)
#define  PON_STANDARDS_SUPPORTED_10GEPON_SYM (1)
#define  PON_STANDARDS_SUPPORTED_10GEPON_ASYM (2)
#define  PON_STANDARDS_SUPPORTED_GPON (3)
#define  PON_STANDARDS_SUPPORTED_10GGPON_SYM (4)
#define  PON_STANDARDS_SUPPORTED_10GGPON_ASYM (5)
	uword8 ucStandardsSupported;	/*网关上行WAN口所支持的标准:*/
	uword8 ucStandardUsed;
#define   PON_UPLINK_DISABLED_E  (0)      /**< pon模块故障 Disabled */
#define   PON_UPLINK_INITIALIZING_E  (1)    /**< ploam O1状态 Initializing */
#define   PON_UPLINK_STANDBY_E     (2)     /**< ploam O2状态 EstablishingLink */
#define   PON_UPLINK_SN_E           (3)      /**< ploam O3状态 EstablishingLink */
#define   PON_UPLINK_RANGING_E    (4)        /**< ploam O4状态 EstablishingLink */
#define   PON_UPLINK_UP_E           (5)    /**< ploam O5状态 Up */
#define   PON_UPLINK_POPUP_E          (6)    /**< ploam O6状态 Error */
#define   PON_UPLINK_EMERGENCY_E     (7)     /**< ploam O7状态 Error */
#define   PON_UPLINK_NOSIGNAL_E      (8)      /**< 断纤 NoSignal */
#define   PON_REG_ERR_E        (9)          /*光纤未链接*/
	uword8 ucFibreLinkStatus;
	uword8 ucPad;
	/*网关上行WAN口使用的接口标准，该参数取StandardsSupported参数值中的一个。*/
	uword32 ulUpstreamMaxRate;	/*PON口上行可达到的最大带宽以Mbps计*/
	uword32 ulDownstreamMaxRate; 	/*PON口下行可达到的最大带宽以Mbps计*/

	uword32 ulCPURate;				/*CPU 利用率*/
	uword32 ulMemoryRate;			/*内存利用率*/
	uword32 ulSysDuration;
#define CPU_CLASS_LEN (32)
	word8  aucCPUClass[CPU_CLASS_LEN];

#define  PON_STATUS_REG_AUTH (0)
#define  PON_STATUS_REG_NO_AUTH (1)
#define  PON_STATUS_NO_REG_NO_AUTH (2)
	uword32 ulPONStatus;
	uword32 ulFlashUsage;
	uword32 ulFlashSize;
	uword32 ulFlashFreeSize;
	uword32 ulRamSize;
	uword32 ulHGWSleep;
	uword32 ulUSBdevicenum;
	uword32 ulMainChipTemperature;

#define ITMS_WEB_SHOW_STATUS_UNREGISTERD (0)
#define ITMS_WEB_SHOW_STATUS_CONNECTED (1)
#define ITMS_WEB_SHOW_STATUS_DISCONNECTED (2)
#define ITMS_WEB_SHOW_STATUS_REGISTING (3)
	uword8 ucWebShowRegStatus;/*WEB注册状态显示*/
	uword8 Pad2[CM_THREE_PADS];

#define DEV_STATUS_INFO_MASK_BIT0_PON_PHY_STATUS (0x01)
#define DEV_STATUS_INFO_MASK_BIT1_PON_CONN_STATUS (0x02)
#define DEV_STATUS_INFO_MASK_BIT2_LOID_AUTH_STATUS (0x04)
#define DEV_STATUS_INFO_MASK_BIT3_ITMS_REG_STATUS (0x08)
#define DEV_STATUS_INFO_MASK_BIT4_PON_CONNECTED_TIME (0x10)
#define DEV_STATUS_INFO_MASK_BIT5_CUR_LOCAL_TIME (0x20)
#define DEV_STATUS_INFO_MASK_BIT6_DEV_UPTIME (0x40)
#define DEV_STATUS_INFO_MASK_BIT7_DEV_STANDARDS_SUPPORTED (0x80)
#define DEV_STATUS_INFO_MASK_BIT8_DEV_STANDARDS_USED (0x100)
#define DEV_STATUS_INFO_MASK_BIT9_DEV_UP_STREAMMAXRATE (0x200)
#define DEV_STATUS_INFO_MASK_BIT10_DEV_DOWN_STREAMMAXRATE (0x400)
#define DEV_STATUS_INFO_MASK_BIT11_CPURATE (0x800)
#define DEV_STATUS_INFO_MASK_BIT12_MEMORYRATE (0x1000)
#define DEV_STATUS_INFO_MASK_BIT15_SYSDURATION (0x8000)
#define DEV_STATUS_INFO_MASK_BIT18_CPUCLASS (0x40000)
#define DEV_STATUS_INFO_MASK_BIT21_WEBSHOW (0x200000)
#define DEV_STATUS_INFO_MASK_BIT13_PONStatus (0x2000)
#define DEV_STATUS_INFO_MASK_BIT14_FLASHUSAGE (0x4000)
#define DEV_STATUS_INFO_MASK_BIT16_FLASHSIZE (0x10000)
#define DEV_STATUS_INFO_MASK_BIT17_RAMSIZE (0x20000)
#define DEV_STATUS_INFO_MASK_BIT19_HGWSLEEP (0x80000)
#define DEV_STATUS_INFO_MASK_BIT20_USBDEVICENUM (0x100000)
#define DEV_STATUS_INFO_MASK_BIT21_MAINCHIPTEMPERATURE (0x200000)
#define DEV_STATUS_INFO_MASK_BIT22_FLASHFREESIZE (0x400000)

#define DEV_STATUS_INFO_MASK_ALL (0xffffff)

	uword32 ulBitmap;
} __PACK__ IgdDevStatusInfoTab;


/***************************系统命令配置表*********************************/
/*表相关宏*/
typedef struct {
	uword32 ulStateAndIndex;

#define SYSCMD_LOID_NAME_LEN (24+1)
	word8 aucLoidName[SYSCMD_LOID_NAME_LEN];/*LOID*/
	uword8 aucPad[CM_THREE_PADS];
#define SYSCMD_LOID_PASSWORD_LEN (12+1)
	word8 aucLoidPasswd[SYSCMD_LOID_PASSWORD_LEN];
	uword8 aucPad1[CM_THREE_PADS];
#define SYSCMD_LOID_PROVINCE_LEN (36)
	word8 aucProvince[SYSCMD_LOID_PROVINCE_LEN];

#define SYSCMD_SET_LOID_DISABLE (0)
#define SYSCMD_SET_LOID_ENABLE  (1)
	uword8 ucSetLOID;
#define SYSCMD_REGISTER_LOID_DISABLE (0)
#define SYSCMD_REGISTER_LOID_ENABLE  (1)
	uword8 ucRegisterLOID;
#define SYSCMD_CHECK_LOID_DISABLE (0)
#define SYSCMD_CHECK_LOID_ENABLE  (1)
	uword8 ucCheckLOID;
#define SYSCMD_CHECK_LOID_AUTH_SUCCESS (0)
#define SYSCMD_CHECK_LOID_AUTH_FAILED  (1)
	uword8 ucCheckLOIDStatus;
#define SYSCMD_DATE_TIME_LEN (256)
	uword8 aucTime[SYSCMD_DATE_TIME_LEN];
#define SYSCMD_SET_DATETIME_DISABLE (0)
#define SYSCMD_SET_DATETIME_ENABLE  (1)
	uword8 ucSetDateTime;
#define SYSCMD_CHECK_PASSWD_MATCH     (0)
#define SYSCMD_CHECK_PASSWD_MISMATCH  (1)
	uword8 ucCheckPasswdStatus;
	uword8 aucPad2[CM_TWO_PADS];

	uword8 aucPassword[CM_PASSWORD_LEN];
#define SYSCMD_CHECK_UAPASSWD_DISABLE (0)
#define SYSCMD_CHECK_UAPASSWD_ENABLE  (1)
	uword8 ucCheckUAPasswd;
#define SYSCMD_SET_UAPASSWD_DISABLE (0)
#define SYSCMD_SET_UAPASSWD_ENABLE  (1)
	uword8 ucSetUAPasswd;
#define SYSCMD_CHECK_TAPASSWD_DISABLE (0)
#define SYSCMD_CHECK_TAPASSWD_ENABLE  (1)
	uword8 ucCheckTAPasswd;
#define SYSCMD_GET_TAPASSWD_DISABLE (0)
#define SYSCMD_GET_TAPASSWD_ENABLE  (1)
	uword8 ucGetTAPasswd;

	uword8 aucUploadURL[CM_URL_LEN];
	uword8 aucUploadUsername[CM_USERNAME_LEN];
	uword8 aucUploadPassword[CM_PASSWORD_LEN];
#define SYSCMD_FAULTCATEGORY_ALL       (0)
#define SYSCMD_FAULTCATEGORY_INTERNET  (1)
#define SYSCMD_FAULTCATEGORY_VOICE     (2)
#define SYSCMD_FAULTCATEGORY_IPTV      (3)
#define SYSCMD_FAULTCATEGORY_WIFI      (4)
#define SYSCMD_FAULTCATEGORY_SYSTEM    (5)
#define SYSCMD_FAULTCATEGORY_FRAMEWORK (6)
	uword8 ucFaultCategory;
#define SYSCMD_CATEGORY_ALL       (0)
#define SYSCMD_CATEGORY_INTERNET  (1)
#define SYSCMD_CATEGORY_VOICE     (2)
#define SYSCMD_CATEGORY_IPTV      (3)
#define SYSCMD_CATEGORY_WIFI      (4)
#define SYSCMD_CATEGORY_SYSTEM    (5)
#define SYSCMD_CATEGORY_FRAMEWORK (6)
	uword8 ucCategory;
#define SYSCMD_UPLOADTROUBLE_DISABLE (0)
#define SYSCMD_UPLOADTROUBLE_ENABLE  (1)
	uword8 ucUploadTroubleLocatingInfo;
#define SYSCMD_STARTCOLLECTION_DISABLE (0)
#define SYSCMD_STARTCOLLECTION_ENABLE  (1)
	uword8 ucStartCollectionDebugInfo;
	uword32 ulTimeout;
#define SYSCMD_CURRENT_PROVINCE_LEN (36)
	uword8 aucCurrentProvince[SYSCMD_CURRENT_PROVINCE_LEN];

#define SYSCMD_INFO_MASK_BIT0_LOID_NAME       (0x01)
#define SYSCMD_INFO_MASK_BIT1_LOID_PASSWORD   (0x02)
#define SYSCMD_INFO_MASK_BIT2_LOID_PROVINCE   (0x04)
#define SYSCMD_INFO_MASK_BIT3_LOID_SET        (0x08)
#define SYSCMD_INFO_MASK_BIT4_LOID_REGISTER   (0x10)
#define SYSCMD_INFO_MASK_BIT5_LOID_CHECK      (0x20)
#define SYSCMD_INFO_MASK_BIT6_LOID_AUTH_STATUS (0x40)
#define SYSCMD_INFO_MASK_BIT7_DATE_TIME       (0x80)
#define SYSCMD_INFO_MASK_BIT8_SET_DATE_TIME   (0x100)
#define SYSCMD_INFO_MASK_BIT9_CHECK_PASSWD_STATUS (0x200)
#define SYSCMD_INFO_MASK_BIT10_PASSWORD        (0x400)
#define SYSCMD_INFO_MASK_BIT11_CHECK_UAPASSWD (0x800)
#define SYSCMD_INFO_MASK_BIT12_SET_UAPASSWD   (0x1000)
#define SYSCMD_INFO_MASK_BIT13_CHECK_TAPASSWD (0x2000)
#define SYSCMD_INFO_MASK_BIT14_GET_TAPASSWD   (0x4000)
#define SYSCMD_INFO_MASK_BIT15_UPLOADURL      (0x8000)
#define SYSCMD_INFO_MASK_BIT16_UPLOADUSERNAME (0x10000)
#define SYSCMD_INFO_MASK_BIT17_UPLOADPASSWORD (0x20000)
#define SYSCMD_INFO_MASK_BIT18_FAULTCATEGORY  (0x40000)
#define SYSCMD_INFO_MASK_BIT19_CATEGORY       (0x80000)
#define SYSCMD_INFO_MASK_BIT20_UPLOADTROUBLELOCATINGINFO (0x100000)
#define SYSCMD_INFO_MASK_BIT21_STARTCOLLECTIONDEBUGINFO  (0x200000)
#define SYSCMD_INFO_MASK_BIT22_TIMEOUT        (0x400000)
#define SYSCMD_INFO_MASK_BIT23_CURRENT_PROVINCE (0x800000)
#define SYSCMD_INFO_MASK_BIT24_GET_UAPASSWD   (0x1000000)
#define SYSCMD_INFO_MASK_ALL (0x1ffffff)
	uword32 ulBitmap;
} __PACK__ IgdSYSCMDConfigTab;


/*业务逻辑处理函数声明*/
word32 igdCmSysCMDConfigSet(uword8 *pucInfo, uword32 len);

/*操作库函数声明*/
/***************************系统命令配置表*********************************/
/*
参数名：ulLoidRegStatus  参数可能取值:
参数可能取值:
		0 - 未开始注册
		2 - 20% ，具体说明参数 《中国电信家庭网关总体技术要求》
		3 - 30%
		4 - 40%
		5 - 50%
		6 - 60%
		7~9 - 61%~99%  [可自行进行划分]
		10 - 100%

参数名：ulLoidErrCode
参数可能取值
		0 - 没有错误[默认状态]
        1 - 在OLT上注册失败， 具体说明参数 《中国电信家庭网关总体技术要求》30%对应错误
        2 - 不能连接ITMS,   40% 对应错误
        3 - 在ITMS上注册失败， 50%对应错误
        4 - 在ITMS上注册失败2， 60%对应错误
        5 - ITMS未下发业务或业务下发异常： 61-99% 对应错误

参数名：aucCurServiceName
参数可能取值
		“” - 没有下发业务时为空
		“INTERNET” - 下发Internet业务时
		“iTV”  -
		“VOICE” -
		"Other" -

参数名：ulCurServiceNum
参数可能取值
		0 - 没有业务下发时
        其他根据具体下发业务个数填写

参数名：aucTotalServiceName
参数可能取值：
	该参数包含已下发的所有业务，以字符串形式显示。
	例，已下发了3个业务 "INTERNET"，“iTV”， “VOICE”，  则该参数值便为 “INTERNET,iTV,VOICE”
*/
typedef struct {
	uword32 ulStateAndIndex;
#define SYSCMD_REMOTE_AUTH_LOID (0)
#define SYSCMD_REMOTE_AUTH_PWD (1)
	uword32 ulAuthType;

	uword32 ulLoidRegStatus; /* 等同于 loidplan */
	uword32 ulLoidErrCode; /* 等同于 loiderrcode */
	uword32 ulCurServiceNum; /* 等同于 servicecount */

	word8 aucCurServiceName[CM_WAN_CONNECTION_NAME_LEN]; /* 等同于 currentservice */
	word8 aucTotalServiceName[4 * CM_WAN_CONNECTION_NAME_LEN]; /* 等同于servicetotal */

	uword32 ulCheckLOIDResult; /* 等同于 loidplan 为dbus提供*/
#define SYSCMD_CHECK_LOID_ERRDESC_LEN  (256)
	word8 aucCheckLOIDErrdesc[SYSCMD_CHECK_LOID_ERRDESC_LEN];

	uword32 ulBitmap;
} __PACK__ IgdDevLOIDRegStatusInfoTab;

/* 出厂信息表 */
typedef struct {
	uword32 ulStateAndIndex;
#define WLAN_SSID_NAME_STR_LEN                          (36)
	word8  aucSSIDName1[WLAN_SSID_NAME_STR_LEN];
#define WLAN_SSID_PSK_STR_LEN                              (64)
	uword8  aucPreSharedKey1[WLAN_SSID_PSK_STR_LEN];
	word8 aucUserAccountName[CM_USERNAME_LEN];
	word8 aucUserAccountPassword[CM_PASSWORD_LEN];
#define FACTORY_PRODUCAT_DATE_LEN (16)
	word8 productdate[FACTORY_PRODUCAT_DATE_LEN];
#define DEV_FACTORY_ATTR_MASK_BIT0_SSID_NAME (0x01)
#define DEV_FACTORY_ATTR_MASK_BIT1_SSID_PSK (0x02)
#define DEV_FACTORY_ATTR_MASK_BIT2_USER_NAME (0x04)
#define DEV_FACTORY_ATTR_MASK_BIT3_USER_PWD (0x08)
#define DEV_FACTORY_ATTR_MASK_BIT4_PRODCUT_DATE (0x10)
#define DEV_FACTORY_ATTR_MASK_ALL (0x1f)
	uword32 ulBitmap;
} __PACK__ IgdFactoryAttrConfTab;

/* OMCI配置开关表 */
typedef struct {
	uword32 ulStateAndIndex;
#define OMCI_CONF_VOIP_DISABLE                          (0)
#define OMCI_CONF_VOIP_ENABLE                          (1)
	uword32 ulOmciVoip;
#define OMCI_CONF_TR069_DISABLE                          (0)
#define OMCI_CONF_TR069_ENABLE                          (1)
	uword32 ulOmciTr069;
#define DEV_OMCI_ATTR_MASK_BIT0_VOIP (0x01)
#define DEV_OMCI_ATTR_MASK_BIT1_TR069 (0x02)
#define DEV_OMCI_ATTR_MASK_ALL (0x3)
	uword32 ulBitmap;
} __PACK__ IgdOmciAttrConfTab;

#endif
