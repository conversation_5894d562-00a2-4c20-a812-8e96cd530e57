/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: cm tab cloudvr obj attribute
 * Author: HSAN
 * Create: 2023-12-15
 */
#ifndef HI_ODL_TAB_CLOUDVR_H
#define HI_ODL_TAB_CLOUDVR_H
#include "hi_odl_basic_type.h"

#define IGD_CLOUDVR_USER_ID_LEN (64)
#define IGD_CLOUDVR_PASSWORD_LEN (64)

typedef struct {
	uint32_t state_and_index;
#define CLOUD_VR_DISABLE (0)
#define CLOUD_VR_ENABLE (1)
	uint8_t enable;
	uint8_t pad[3];
	char user_id[IGD_CLOUDVR_USER_ID_LEN];
	char password[IGD_CLOUDVR_PASSWORD_LEN];

#define IGD_ATTR_MASK_BIT_CLOUDVR_ENABLE (0x01)
#define IGD_ATTR_MASK_BIT_CLOUDVR_USER_ID (0x02)
#define IGD_ATTR_MASK_BIT_CLOUDVR_PASSWORD (0x04)
#define IGD_ATTR_MASK_BIT_CLOUDVR_ALL (0xFF)

	uint32_t bitmap;
} __PACK__ IgdCloudVRAttrConfTab;

#endif
