/*
 * Copyright (c) vsol. 2024-2025. All rights reserved.
 * Description: cm vsol custom obj attribute
 * Author: fyy
 * Create: 2024-11-19
 */
#ifndef HI_ODL_TAB_VS_CUSTOM_H_H
#define HI_ODL_TAB_VS_CUSTOM_H_H
#include "hi_odl_basic_type.h"

#define VS_CUSTOM_ATTR_DISABLE (0)
#define VS_CUSTOM_ATTR_ENABLE  (1)

/* IGD_VS_CUSTOM_ATTR_TAB */
#define IGD_VS_CUSTOM_ATTR_MAX 1
typedef struct {
	uword32 ulStateAndIndex;                    // 配置的 index (CUSTOM 仅有 0)

#define VS_CUSTOM_CHANGEPW_DISABLE (0)
#define VS_CUSTOM_CHANGEPW_ENABLE  (1)
	uword8 uc_cf_user_changepw_admin;           //admin 默认密码登录需要修改密码 (未实现)
    uword8 uc_cf_user_changepw_user;            //user 默认密码登录需要修改密码 (未实现)
    uword8 uc_cf_user_normal_enable;            //普通 User 账户启用
    uword8 uc_cf_web_captcha;                   //web 验证码启用

    uword8 uc_cf_disable_reset_button;          //禁用 reset button
    uword8 uc_cf_allow_config_reset_button;     //允许 web 上配置 reset button 使能
    uword8 uc_cf_udp_receiver_enable;           //udpcast 接收功能启用
    uword8 uc_cf_tr069_wan_en;                  //提供给路由器使用tr069 wan 使能

    uword8 uc_cf_web_tr369_en;                  //tr369功能是否显示在网页
    uword8 uc_cf_wlan_domain_readonly;                //是否禁用网页地区修改
    uword8 uc_cf_admin_password_readonly;                //是否禁用快速配置超级用户密码修改
    uword8 allow_config_security_acl;                //是否显示security acl
    uword8 uc_tr069_using_vince;                //是否对接vince
    word8  auc_vince_device_type[32];           //保存InternetGatewayDevice.DeviceInfo.VinceDeviceType信息
    word8  auc_vince_project_id[20];            //保存InternetGatewayDevice.DeviceInfo.ProjectID信息
    word8  auc_cf_wlan_ssid_format[64];         //wlan ssid format
    word8  auc_cf_wlan_password_format[64];         //wlan password format
    word8  auc_cf_admin_password_format[64];         //admin password format
    word8  auc_cf_special_admin_password[64];         //special admin password
#define VS_CUSTOM_IPHOST_SRV_TR069              (0x1)
#define VS_CUSTOM_IPHOST_SRV_INTERNET           (0x2)
#define VS_CUSTOM_IPHOST_SRV_OTHER              (0x4)
#define VS_CUSTOM_IPHOST_SRV_VOIP              (0x8)
    uword32 ul_cf_iphost_srv;                   //为了跟rtk方案保持一致, 这里四个字节表示4条iphost wan, 每个字节表示一条iphost wan
    word8  aucPad[3];                           //填充字节: 保证结构体 4 字节对齐

#define VS_CUSTOM_ATTR_MASK_BIT0_CHANGEPW_ADMIN (0x01)
#define VS_CUSTOM_ATTR_MASK_BIT1_CHANGEPW_USER  (0x02)
#define VS_CUSTOM_ATTR_MASK_BIT2_NORMAL_ENABLE  (0x04)
#define VS_CUSTOM_ATTR_MASK_BIT3_WEB_CAPTCHA    (0x08)
#define VS_CUSTOM_ATTR_MASK_BIT4_DISABLE_RESET_BUTTON    (0x10)

	uint32_t ulBitmap;
} __PACK__ IgdCmVsCustomConfTab;

#endif
