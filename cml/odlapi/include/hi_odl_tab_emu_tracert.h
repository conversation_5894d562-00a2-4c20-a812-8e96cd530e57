/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm emu traceroute obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_EMU_TRACERT_H
#define HI_ODL_TAB_EMU_TRACERT_H
#include "hi_odl_basic_type.h"

/* IGD_EMU_TRACERT_ATTR_TAB*/
typedef struct {
	uword32						ulStateAndIndex;
	uword32						ulInstId;
	word8						acWanIf[256];
	word8						acHost[16];
	uword32						ulProto;
	uword32						ulTimeout;
	uword32						ulMaxhops;
#define EMU_TRACEROUTE_ATTR_MASK_BIT0_WANIF				(0x01)
#define EMU_TRACEROUTE_ATTR_MASK_BIT1_HOST				(0x02)
#define EMU_TRACEROUTE_ATTR_MASK_BIT2_PROTO				(0x04)
#define EMU_TRACEROUTE_ATTR_MASK_BIT3_TIMEOUT			(0x08)
#define EMU_TRACEROUTE_ATTR_MASK_BIT4_MAXHOPS			(0x10)
#define EMU_TRACEROUTE_ATTR_MASK_ALL					(0x1f)
	uword32						ulBitmap;
}  IgdCmEmuTracerouteConfTab;

/* IGD_SYSMNG_TRACEROUTE_CONFIG_TAB */
typedef struct {
	uword32 ulStateAndIndex;

#define TRACEROUTE_STATE_NONE (0)
#define TRACEROUTE_STATE_REQUESTED (1)
#define TRACEROUTE_STATE_COMPLETE (2)
#define TRACEROUTE_STATE_RESOLVE_HOST_NAME_ERROR (3)
#define TRACEROUTE_STATE_RESOLVE_MAX_HOPCOUNT_EXCEEDED (4)
	uword8 ucDiagnosticsState;
	uword8 aucPad[CM_THREE_PADS];

#define TRACEROUTE_INTERFACE_NAME_LEN (128)
	uword8 aucInterface[TRACEROUTE_INTERFACE_NAME_LEN];/*操作路径*/
#define TRACEROUTE_HOST_IP_NAME_LEN (256)
	uword8 aucHost[TRACEROUTE_HOST_IP_NAME_LEN];/*目标IP或域名*/
#define TRACEROUTE_TRIES_NUM_MIN (0)
#define TRACEROUTE_TRIES_NUM_MAX (3)
	uword32 ulNumberOfTries;
#define TRACEROUTE_TIMEOUT_MIN (1)
#define TRACEROUTE_TIMEOUT_MAX (65535)
	uword32 ulTimeout;
#define TRACEROUTE_DATABLOCK_MIN (1)
#define TRACEROUTE_DATABLOCK_MAX (65535)
	uword32 ulDataBlockSize;
#define TRACEROUTE_DSCP_MIN (0)
#define TRACEROUTE_DSCP_MAX (63)
	uword32 ulDscp;
#define TRACEROUTE_MAXHOP_COUNT_MIN (1)
#define TRACEROUTE_MAXHOP_COUNT_MAX (64)
	uword32 ulMaxHopCount;
#define TRACEROUTE_MODE_UDP (0)
#define TRACEROUTE_MODE_ICMP (1)
	uword8 ucMode;
	uword8 aucPad1[CM_THREE_PADS];
	uint32_t ulresponsetime;

#define TRACEROUTE_CONFIG_ATTR_MASK_BIT0_DIAGNOSTIC_STATE  (0x01)
#define TRACEROUTE_CONFIG_ATTR_MASK_BIT1_INTERFACE (0x02)
#define TRACEROUTE_CONFIG_ATTR_MASK_BIT2_HOST_NAME (0x04)
#define TRACEROUTE_CONFIG_ATTR_MASK_BIT3_REPEAT_NUM (0x08)
#define TRACEROUTE_CONFIG_ATTR_MASK_BIT4_TIMEOUT_VALUE (0x10)
#define TRACEROUTE_CONFIG_ATTR_MASK_BIT5_DATA_BLOCK_SIZE (0x20)
#define TRACEROUTE_CONFIG_ATTR_MASK_BIT6_DSCP_VALUE (0x40)
#define TRACEROUTE_CONFIG_ATTR_MASK_BIT7_MAX_HOP (0x80)
#define TRACEROUTE_CONFIG_ATTR_MASK_BIT8_MAX_MODE (0x100)
#define TRACEROUTE_CONFIG_ATTR_MASK_ALL (0x1ff)

	uword32 ulBitmap;
} __PACK__ IgdSysmngTraceRouteConfigAttrConfTab;

typedef struct {
	uword32                     ulStateAndIndex;
	uword32						ulHopsId;
	uword32						ulError;
	uword32						ulRespTime[4];
	word8						acRespHost[256];
	word8						acRespAddr[40];
} IgdCmEmuTracerouteHop;

typedef struct {
	uword32						ulPad;
	uword32						ulState;
	uword32						ulResptime;
	uword32						ulHops;
} IgdCmEmuTracerouteInfo;

#endif
