/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm qos obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_QOS_H
#define HI_ODL_TAB_QOS_H
#include "hi_odl_basic_type.h"


/***************************Qos基本信息表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define QOS_TEMPLATE_NAME_LEN (32)
	word8 aucMode[QOS_TEMPLATE_NAME_LEN];/*默认模板（VOIP, TR069, IPTV, VR, INTERNET）*/

#define QOS_DISABLE (0)
#define QOS_ENABLE (1)
	uword8 ucQosEnable; /*默认：启用*/
#define QOS_PLAN_PRIORITY (1)
#define QOS_PLAN_WEIGHT (2)
#define QOS_PLAN_CAR (3)
	uword8 ucPlan; /* 缺省值：1、表示sp 2、表示wrr */
#define QOS_FORCE_WEIGHT_DISABLE (0)
#define QOS_FORCE_WEIGHT_ENABLE (1)
	uword8 ucEnableForceWeight; /* 表示带宽限速*/
#define QOS_DSCP_MARK_DISABLE (0)
#define QOS_DSCP_MARK_ENABLE (1)
	uword8 ucEnableDscpMark; /* 默认：disable */

#define QOS_8021P_DISABLE (0)
#define QOS_8021P_RETAIN (1)
#define QOS_8021P_OVERWRITE (2)
	uword8 ucEnable8021p; /* 默认：0表示不使能*/
#define QOS_TC_MARK_DISABLE (0)
#define QOS_TC_MARK_ENABLE (1)
	uword8 ucEnableTcMark; /* 默认：0表示不使能*/
	uword8 aucPad[CM_TWO_PADS];

	uword32 ulBandwidth; /* 默认：0  0表示不限速*/
	uword32 ulTcpSession; /* 默认：61000 */

#define QOS_BASIC_ATTR_MASK_BIT0_MODE (0x01)
#define QOS_BASIC_ATTR_MASK_BIT1_QOS_ENABLE (0x02)
#define QOS_BASIC_ATTR_MASK_BIT2_PLAN (0x04)
#define QOS_BASIC_ATTR_MASK_BIT3_FRC_WEIGHT_ENABLE (0x08)
#define QOS_BASIC_ATTR_MASK_BIT4_DSCPMARK_ENABLE (0x10)
#define QOS_BASIC_ATTR_MASK_BIT5_8021P_ENABLE (0x20)
#define QOS_BASIC_ATTR_MASK_BIT6_TCMARK_ENABLE (0x40)
#define QOS_BASIC_ATTR_MASK_BIT7_BANDWIDTH (0x80)
#define QOS_BASIC_ATTR_MASK_BIT8_TCP_SESSION (0x100)
#define QOS_BASIC_ATTR_MASK_ALL (0x1ff)
	uword32 ulBitmap;
} __PACK__ IgdQosBasicAttrConfTab;

/***************************Qos分类信息表*********************************/
#define IGD_QOS_CLF_ATTR_TAB_RECORD_NUM (8)
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucClfIndex;   /*分类索引：1 ~ 8*/
	uword8 aucPad[CM_TWO_PADS];
	/* 是否启用队列优先级的标志，0表示该队列优先级不生效，仅作为重标记的匹配条件，1表示启用该队列优先级，缺省值：1。 */
#define QOS_CLASS_QUEUE_DISABLE (0)
#define QOS_CLASS_QUEUE_ENABLE (1)
	uword8 class_queue_enable;

#define QOS_QUEUE_NUM_MIN (1)
#define QOS_QUEUE_NUM_MAX (8)
	uword8 ucQueueNum; /*默认值：队列8 */
#define QOS_DSCP_MARK_VALUE_MIN (0)
#define QOS_DSCP_MARK_VALUE_MAX (63)
	uword8 ucDscpMarkValue;/*默认0  范围0 - 63*/
#define QOS_8021P_VAULE_MIN (0)
#define QOS_8021P_VAULE_MAX (7)
	uword8 uc8021pValue;/*默认0  范围0 - 7*/
#define QOS_TC_VALUE_MIN (0)
#define QOS_TC_VALUE_MAX (63)
	uword8 ucTcValue;/*默认0   范围0 - 63*/

#define QOS_CLF_ATTR_MASK_BIT0_QUEUE_NUM (0x01)
#define QOS_CLF_ATTR_MASK_BIT1_DSCP_MARK_VALUE (0x02)
#define QOS_CLF_ATTR_MASK_BIT2_8021P_VALUE (0x04)
#define QOS_CLF_ATTR_MASK_BIT3_TC_VALUE (0x08)
#define QOS_CLF_ATTR_MASK_BIT4_CLASS_QUEUE_ENABLE (0x10)
#define QOS_CLF_ATTR_MASK_ALL (0x1f)
	uword32 ulBitmap;
} __PACK__ IgdQosClfAttrConfTab;

/***************************Qos分类类型表*********************************/
#define IGD_QOS_CLF_TYPE_TAB_RECORD_NUM_PER_CLF (4)
#define IGD_QOS_CLF_TYPE_TAB_MAX_NUM (IGD_QOS_CLF_TYPE_TAB_RECORD_NUM_PER_CLF * IGD_QOS_CLF_ATTR_TAB_RECORD_NUM)
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucClfIndex;/*分类索引，1 ~ 8*/
	uword8 ucTypeIndex;/*分类类型索引，1 ~ 4*/
#define QOS_CLF_TYPE_PROTOCOL_TCP (0x01)
#define QOS_CLF_TYPE_PROTOCOL_UDP (0x02)
#define QOS_CLF_TYPE_PROTOCOL_ICMP (0x04)
#define QOS_CLF_TYPE_PROTOCOL_RTP (0x08)
#define QOS_CLF_TYPE_PROTOCOL_ICMPV6 (0x10)
	uword8 ucProtocolList;/*四种方式组合使用，带组合后掩码*/
#define QOS_CLF_TYPE_DEFAULT (0)
#define QOS_CLF_TYPE_S_MAC (1)
#define QOS_CLF_TYPE_D_MAC (2)
#define QOS_CLF_TYPE_S_IP (3)
#define QOS_CLF_TYPE_D_IP (4)
#define QOS_CLF_TYPE_S_PORT (5)
#define QOS_CLF_TYPE_D_PORT (6)
#define QOS_CLF_TYPE_TOS (7)
#define QOS_CLF_TYPE_DSCP (8)
#define QOS_CLF_TYPE_8021P (9)
#define QOS_CLF_TYPE_WAN_INTERFACE (10)
#define QOS_CLF_TYPE_LAN_INTERFACE (11)
#define QOS_CLF_TYPE_TC (12)
#define QOS_CLF_TYPE_FL (13)
#define QOS_CLF_TYPE_IPVERSION (14)
#define QOS_CLF_TYPE_DROP (15)
#define QOS_CLF_TYPE_VLAN (16)
	uword8 ucType; /*类型1-14依次表示源MAC地址/目的MAC地址/源IP地址/ 目的IP地址/ 源端口号/ 目的端口号/ TOS/ DSCP/802.1p/wan连接/ LAN侧接口/ TC/ FL/丢弃*/

#define QOS_CLF_STRING_LEN (128)
	word8 aucMax[QOS_CLF_STRING_LEN];/*MAC、IP地址和WAN连接名称*/
	word8 aucMin[QOS_CLF_STRING_LEN];/*MAC、IP地址和WAN连接名称*/
	uword32 ulMax; /*for others*/
	uword32 ulMin; /*for others*/
#define QOS_CLF_TYPE_MASK_BIT0_PROTOCOL_LIST (0x01)
#define QOS_CLF_TYPE_MASK_BIT1_TYPE (0x02)
#define QOS_CLF_TYPE_MASK_BIT2_MAX (0x04)
#define QOS_CLF_TYPE_MASK_BIT3_MIN (0x08)
#define QOS_CLF_TYPE_MASK_ALL (0x0f)
	uword32 ulBitmap;
} __PACK__ IgdQosClfTypeAttrConfTab;

/***************************APP业务表*********************************/
#define IGD_QOS_APP_TAB_RECORD_NUM (8)

typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucAppIndex;/*1、tr069  2、voip  */
#define QOS_APP_CLASS_QUEUE_MIN (1)
#define QOS_APP_CLASS_QUEUE_MAX (8)
	uword8 ucClassQueue;/* 选择进入哪个队列，1，2，3，4，5,6,7,8 */
	uword8 aucPad[CM_TWO_PADS];

#define QOS_APP_NAME_LEN (32)
	word8 aucAppName[QOS_APP_NAME_LEN];

#define QOS_APP_ATTR_MASK_BIT0_APP_NAME (0x01)
#define QOS_APP_ATTR_MASK_BIT1_CLASS_QUEUE (0x02)
#define QOS_APP_ATTR_MASK_ALL (0x03)
	uword32 ulBitmap;
} __PACK__ IgdQosAppAttrConfTab;

/***************************队列优先级属性表*********************************/
#define IGD_QOS_QUEUE_TAB_RECORD_NUM (8)

typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucQueueIndex;/*队列索引*/
#define QOS_QUEUE_DISABLE (0)
#define QOS_QUEUE_ENABLE (1)
	uword8 ucEnable;
#define QOS_QUEUE_PRIORITY_MIN (1)
#define QOS_QUEUE_PRIORITY_MAX (8)
	uword8 ucPriority;/* 取值范围1，2，3，4，5,6,7,8 要求和队列实例号保持一致。 */
	uword8 ucPad;

	uword32 ulWeight;
	word32 ulRate;
#define QOS_QUEUE_ATTR_MASK_BIT0_ENABLE (0x01)
#define QOS_QUEUE_ATTR_MASK_BIT1_PRIORITY (0x02)
#define QOS_QUEUE_ATTR_MASK_BIT2_WEIGHT (0x04)
#define QOS_QUEUE_ATTR_MASK_BIT3_RATE (0x08)
#define QOS_QUEUE_ATTR_MASK_ALL (0x0f)
	uword32 ulBitmap;
} __PACK__ IgdQosQueueAttrConfTab;

/***************************数据流限速属性表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define UP_LIMIT_DISABLE (0)
#define UP_LIMIT_MODE_LAN (1)
#define UP_LIMIT_MODE_VLAN (2)
#define UP_LIMIT_MODE_IP_RANGE (3)
	uword8 ucUpLimitMode;
#define DOWN_LIMIT_DISABLE (0)
#define DOWN_LIMIT_MODE_LAN (1)
#define DOWN_LIMIT_MODE_VLAN (2)
#define DOWN_LIMIT_MODE_IP_RANGE (3)
	uword8 ucDownLimitMode;
	uword8 aucPad[CM_TWO_PADS];

#define LAN_LIMIT_STRING_LEN (128)
	word8 aucUpLanLimit[LAN_LIMIT_STRING_LEN];
	word8 aucDownLanLimit[LAN_LIMIT_STRING_LEN];
#define VLAN_LIMIT_STRING_LEN (128)
	word8 aucUpVlanLimit[VLAN_LIMIT_STRING_LEN];
	word8 aucDownVlanLimit[VLAN_LIMIT_STRING_LEN];
#define IP_RANGE_LIMIT_STRING_LEN (128)
	word8 aucUpIpLimit[IP_RANGE_LIMIT_STRING_LEN];
	word8 aucDownIpLimit[IP_RANGE_LIMIT_STRING_LEN];

#define FLOW_LIMIT_ATTR_MASK_BIT0_LIMIT_MODE_UP (0x01)
#define FLOW_LIMIT_ATTR_MASK_BIT1_LIMIT_MODE_DOWN (0x02)
#define FLOW_LIMIT_ATTR_MASK_BIT2_LAN_LIMIT_UP (0x04)
#define FLOW_LIMIT_ATTR_MASK_BIT3_LAN_LIMIT_DOWN (0x08)
#define FLOW_LIMIT_ATTR_MASK_BIT4_VLAN_LIMIT_UP (0x10)
#define FLOW_LIMIT_ATTR_MASK_BIT5_VLAN_LIMIT_DOWN (0x20)
#define FLOW_LIMIT_ATTR_MASK_BIT6_IP_LIMIT_UP (0x40)
#define FLOW_LIMIT_ATTR_MASK_BIT7_IP_LIMIT_DOWN (0x80)
#define FLOW_LIMIT_ATTR_MASK_ALL (0xff)
	uword32 ulBitmap;
} __PACK__ IgdQosFlowLimitAttrConfTab;

/***************************Qos分类类型索引*********************************/
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucClfIndex;/*分类索引，1 ~ 8*/
	uword8 ucTypeIndex;/*分类类型索引，1 ~ 4*/
	uword8 aucPad[CM_TWO_PADS];

	uword32 ulBitmap;
} __PACK__ IgdCmQosClfTypeIndexAttrTab;

/***************************ctc 数据流限速属性表*********************************/
/*表相关宏*/
#define IGD_QOS_ITEM_ATTR_TAB_RECORD_NUM (32)

#define QOS_ITEM_NAME_LEN 32
#define QOS_ITEM_PROTOCOL_LEN 32
#define QOS_ITEM_IP_LEN 48
#define QOS_ITEM_MASK_LEN 48
#define QOS_ITEM_PORT_LEN 8
#define QOS_ITEM_MAC_LEN 20

typedef struct {
	uword32 ulStateAndIndex;

	uword32 ulIndex;
	word8 aucName[QOS_ITEM_NAME_LEN];
	uword32 ulPriority;
	uword32 ulDscp;
	uword32 ulVlan8021p;
	word8 aucProtocol[QOS_ITEM_PROTOCOL_LEN];
	word8 aucSrcIp[QOS_ITEM_IP_LEN];
	word8 aucSrcMask[QOS_ITEM_MASK_LEN];
	word8 aucSrcPort[QOS_ITEM_PORT_LEN];
	word8 aucDstcIp[QOS_ITEM_IP_LEN];
	word8 aucDstMask[QOS_ITEM_MASK_LEN];
	word8 aucDstPort[QOS_ITEM_PORT_LEN];
	word8 aucSrcMac[QOS_ITEM_MAC_LEN];
	word8 aucDstMac[QOS_ITEM_MAC_LEN];

#define QOS_ITEM_ATTR_MASK_BIT0_NAME      (0x01)
#define QOS_ITEM_ATTR_MASK_BIT1_PRIORITY  (0x02)
#define QOS_ITEM_ATTR_MASK_BIT2_DSCP      (0x04)
#define QOS_ITEM_ATTR_MASK_BIT3_VLAN8021P (0x08)
#define QOS_ITEM_ATTR_MASK_BIT4_PROTOCOL  (0x10)
#define QOS_ITEM_ATTR_MASK_BIT5_SRCIP     (0x20)
#define QOS_ITEM_ATTR_MASK_BIT6_SRCMASK   (0x40)
#define QOS_ITEM_ATTR_MASK_BIT7_SRCPORT   (0x80)
#define QOS_ITEM_ATTR_MASK_BIT8_DSTIP     (0x100)
#define QOS_ITEM_ATTR_MASK_BIT9_DSTMASK   (0x200)
#define QOS_ITEM_ATTR_MASK_BIT10_DSTPORT   (0x400)
#define QOS_ITEM_ATTR_MASK_BIT11_SRCMAC   (0x800)
#define QOS_ITEM_ATTR_MASK_BIT12_DSTMAC   (0x1000)
#define QOS_ITEM_ATTR_MASK_ALL            (0x1fff)
	uword32 ulBitmap;
} __PACK__ IgdCmQosItemAttrTab;


#define  IGD_INTERFACE_SPEED_CTRL_MAXNUM 20
typedef struct {
	uint32_t state_and_index;
	uint32_t instance_index;

#define INTERFACE_NAME_LEN 64
	char interface_name[INTERFACE_NAME_LEN];
	uint32_t upload_speed;
	uint32_t download_speed;

#define INTERFACE_SPEED_ATTR_MASK_INTERFACE_NAME                 (1 << 0)
#define INTERFACE_SPEED_ATTR_MASK_UPLOAD_SPEED                   (1 << 1)
#define INTERFACE_SPEED_ATTR_MASK_DOWNLOAD_SPEED                 (1 << 2)
#define INTERFACE_SPEED_ATTR_MASK_ALL                            ((1 << 3) - 1)

	uint32_t bit_map;
} __PACK__ igd_interface_speed_attr_conf_tab_t;

typedef struct {
	uint32_t state_and_index;
#define QOS_DOWNLINK_DISABLE (0)
#define QOS_DOWNLINK_ENABLE (1)
	uint8_t qos_enable;
#define QOS_DOWNLINK_PLAN_PRIORITY (1)
#define QOS_DOWNLINK_PLAN_WEIGHT (2)
	uint8_t plan;             /* default pri mode */
#define QOS_DOWNLINK_FORCE_WEIGHT_DISABLE (0)
#define QOS_DOWNLINK_FORCE_WEIGHT_ENABLE (1)
	uint8_t enable_force_weight;
#define QOS_DOWNLINK_DSCP_MARK_DISABLE (0)
#define QOS_DOWNLINK_DSCP_MARK_ENABLE (1)
	uint8_t enable_dscp_mark;
	uint32_t bandwidth;
#define QOS_DOWNLINK_8021P_DISABLE (0)
#define QOS_DOWNLINK_8021P_RETAIN (1)
#define QOS_DOWNLINK_8021P_OVERWRITE (2)
	uint8_t enable_8021p;
	uint8_t pad[CM_THREE_PADS];

#define QOS_DOWNLINK_ATTR_MASK_ENABLE                         (1 << 0)
#define QOS_DOWNLINK_ATTR_MASK_PLAN                           (1 << 1)
#define QOS_DOWNLINK_ATTR_MASK_FORCE_WEIGHT_ENABLE            (1 << 2)
#define QOS_DOWNLINK_ATTR_MASK_DSCP_MARK_ENABLE               (1 << 3)
#define QOS_DOWNLINK_ATTR_MASK_BANDWIDTH                      (1 << 4)
#define QOS_DOWNLINK_ATTR_MASK_8021P_ENABLE                   (1 << 5)
#define QOS_DOWNLINK_ATTR_MASK_ALL                            ((1 << 6) - 1)
	uint32_t bit_map;
} __PACK__ igd_qos_downlink_attr_basic_tab;

#define IGD_QOS_DOWNLINK_CLF_TAB_RECORD_NUM (8)
typedef struct {
	uint32_t state_and_index;
	uint8_t index;
	uint8_t pad[CM_THREE_PADS];
#define QOS_DOWNLINK_QUEUE_NUM_MIN (1)
#define QOS_DOWNLINK_QUEUE_NUM_MAX (8)
	uint32_t class_queue;
#define QOS_DOWNLINK_DSCP_MARK_VALUE_MIN (0)
#define QOS_DOWNLINK_DSCP_MARK_VALUE_MAX (63)
	uint32_t dscp_mark_value;
#define QOS_DOWNLINK_8021P_VAULE_MIN (0)
#define QOS_DOWNLINK_8021P_VAULE_MAX (7)
	uint32_t _8021p_value;

#define QOS_DOWNLINK_CLF_MASK_CLASS_QUEUE                   (1 << 0)
#define QOS_DOWNLINK_CLF_MASK_DSCP_MARK                     (1 << 1)
#define QOS_DOWNLINK_CLF_MASK_8021P_VAULE                   (1 << 2)
#define QOS_DOWNLINK_CLF_MASK_ALL                           ((1 << 3) - 1)
	uint32_t bit_map;
} __PACK__ igd_qos_downlink_clf_tab;

#define IGD_QOS_DOWNLINK_CLF_TYPE_TAB_RECORD_NUM_PRE_CLF (4)
#define IGD_QOS_DOWNLINK_CLF_TYPE_TAB_MAX_NUM (IGD_QOS_DOWNLINK_CLF_TYPE_TAB_RECORD_NUM_PRE_CLF *\
							IGD_QOS_DOWNLINK_CLF_TAB_RECORD_NUM)
typedef struct {
	uint32_t state_and_index;

	uint8_t clf_index;
	uint8_t type_index;
	uint8_t type; /* same as uplink clf type */
	uint8_t protocol_list; /* same as uplink clf protocol_list */
	char min[QOS_CLF_STRING_LEN];  /* for mac\ip\wan name */
	char max[QOS_CLF_STRING_LEN];  /* for mac\ip\wan name */
	uint32_t min_value; /* for other type */
	uint32_t max_value; /* for other type */

#define QOS_DOWNLINK_CLF_TYPE_MASK_TYPE                    (1 << 0)
#define QOS_DOWNLINK_CLF_TYPE_MASK_PROTOCOL_LIST           (1 << 1)
#define QOS_DOWNLINK_CLF_TYPE_MASK_MIN                     (1 << 2)
#define QOS_DOWNLINK_CLF_TYPE_MASK_MAX                     (1 << 3)
#define QOS_DOWNLINK_CLF_TYPE_MASK_ALL                     ((1 << 4) - 1)
	uint32_t bit_map;
} __PACK__ igd_qos_downlink_clf_type_tab;

#define IGD_QOS_DOWNLINK_QUEUE_TAB_RECORD_NUM (8)
typedef struct {
	uint32_t state_and_index;
	uint8_t index;
	uint8_t enable;
	uint8_t pad[CM_TWO_PADS];
	uint32_t priority;
	uint32_t weight;

#define QOS_DOWNLINK_QUEUE_MASK_ENABLE                    (1 << 0)
#define QOS_DOWNLINK_QUEUE_MASK_PRIORITY                  (1 << 1)
#define QOS_DOWNLINK_QUEUE_MASK_WEIGHT                    (1 << 2)
#define QOS_DOWNLINK_QUEUE_MASK_ALL                       ((1 << 3) - 1)
	uint32_t bit_map;
} __PACK__ igd_qos_downlink_queue_tab;

typedef struct {
	uint8_t clf_index;
	uint8_t type_index;
	uint8_t pad[CM_TWO_PADS];
} __PACK__ igd_qos_downlink_clf_type_index_tab;

//for smart qos
typedef struct {
	uint32_t rate_limit;
} __PACK__ igd_smart_qos_upstream_profile_tab;

typedef struct {
	uint32_t rate_limit;
} __PACK__ igd_smart_qos_downstream_profile_tab;

#endif
