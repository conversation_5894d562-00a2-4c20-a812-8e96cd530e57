/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author: HSAN
 * Create: 2023-12-18
 */
#ifndef HI_ODL_TAB_HARD_ACC_CANCEL_H
#define HI_ODL_TAB_HARD_ACC_CANCEL_H
#include "hi_odl_basic_type.h"

#define IGD_HARD_ACC_CANCEL_SRCADDRESS_LEN (16)
#define IGD_HARD_ACC_CANCEL_DESTADDRESS_LEN (16)
#define IGD_HARD_ACC_CANCEL_PROTOCOL_LEN (16)

typedef enum {
	IGD_HARD_ACC_CANCEL_MATCHTYPE_IP_SRCMAC = 0,
	IGD_HARD_ACC_CANCEL_MATCHTYPE_IP = 1,
	IGD_HARD_ACC_CANCEL_MATCHTYPE_SRCMAC = 2,
	IGD_HARD_ACC_CANCEL_MATCHTYPE_BOTTOM
} IGD_HARD_ACC_CANCEL_MATCHTYPE;

/* FIXME: domain_list\dest_ip_list\mac_list 如何存储 */
typedef struct {
	uword32 state_and_index;
	uword32 entry_index;
	uword8 enable;
	uword8 pad1[3];
	char src_address[IGD_HARD_ACC_CANCEL_SRCADDRESS_LEN];
	uword32 src_port;
	char dest_address[IGD_HARD_ACC_CANCEL_DESTADDRESS_LEN];
	uword32 dest_port;
	char protocol[IGD_HARD_ACC_CANCEL_PROTOCOL_LEN];

#define IGD_ATTR_MASK_HARD_ACC_CANCEL_ENABLE (0x01)
#define IGD_ATTR_MASK_HARD_ACC_CANCEL_SRCADDRESS (0x02)
#define IGD_ATTR_MASK_HARD_ACC_CANCEL_SRCPORT (0x04)
#define IGD_ATTR_MASK_HARD_ACC_CANCEL_DESTADDRESS (0x08)
#define IGD_ATTR_MASK_HARD_ACC_CANCEL_DESTPORT (0x10)
#define IGD_ATTR_MASK_HARD_ACC_CANCEL_PROTOCOL (0x20)
#define IGD_ATTR_MASK_HARD_ACC_CANCEL_ALL (0xffff)

	uword32 bitmap;
} __PACK__ IgdHardAccCancelAttrConfTab;

#endif
