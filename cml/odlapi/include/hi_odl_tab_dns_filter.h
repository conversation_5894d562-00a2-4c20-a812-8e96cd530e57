/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm tab dns filter obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_DNS_FILTER_H
#define HI_ODL_TAB_DNS_FILTER_H
#include "hi_odl_basic_type.h"

/********************************域名过滤*******************************************/
/* IGD_DNS_FILTER_CONFIG_TAB */
#define IGD_DNS_FILTER_CONFIG_RECORD_NUM (100)

#define IGD_DNS_FILTER_HOSTLIST_FILE_LEN (64)
#define IGD_DNS_FILTER_TEMP_FILE "/tmp/dnsfilter_hostlist_"
#define IGD_DNS_FILTER_WEEKDAY_LEN (32)
#define IGD_DNS_FILTER_TIME_LEN (64)

typedef struct {
	uword32 ulStateAndIndex;
	uword8 ucIndex;
	uword8 ucEnable;  //0-disable.1-enable
#define DNS_FILTER_ACTION_DROP 0
#define DNS_FILTER_ACTION_LAN_IP 1
#define DNS_FILTER_ACTION_NAME_ERR 2
	uword8 ucAction;//0-drop,1-relay gateway ip,2-relay name error
	uword8 ucMode;

#define DNS_FILTER_CONFIG_NAME_LEN (64)
	uword8 aucName[DNS_FILTER_CONFIG_NAME_LEN];
	uword8 aucMACAddress[CM_MAC_CHAR_LEN];

#define DNS_FILTER_CONFIG_STRING_LEN (256)
	uword8 aucDomain[DNS_FILTER_CONFIG_STRING_LEN];
	uword8 aucHostListName[IGD_DNS_FILTER_HOSTLIST_FILE_LEN];
	uword8 aucWeekdays[IGD_DNS_FILTER_WEEKDAY_LEN];
	uword8 aucTime[IGD_DNS_FILTER_TIME_LEN];

	uword32 ulBlockedTimes;

#define DNS_FILTER_CONFIG_MASK_BIT0_ENABLE (0x01)
#define DNS_FILTER_CONFIG_MASK_BIT1_MAC (0x02)
#define DNS_FILTER_CONFIG_MASK_BIT2_DOMAIN (0x04)
#define DNS_FILTER_CONFIG_MASK_BIT3_ACTION (0x08)
#define DNS_FILTER_CONFIG_MASK_BIT4_NAME (0x10)
#define DNS_FILTER_CONFIG_MASK_BIT5_MODE (0x20)
#define DNS_FILTER_CONFIG_MASK_BIT6_HOSTLIST  (0x40)
#define DNS_FILTER_CONFIG_MASK_BIT7_WEEKDAYS (0x80)
#define DNS_FILTER_CONFIG_MASK_BIT8_TIME (0x100)

#define DNS_FILTER_CONFIG_MASK_ALL (0x1ff)
	uword32 ulBitmap;
} __PACK__ IgdCmDnsFilterConfigTab;

/***************************vsol dns domain过滤名单*********************************/
#define CM_DOMAIN_LEN (256)								/* domain长度 */
#define IGD_SECUR_DNS_FILTER_LIST_RECORD_NUM (100)		/* dns过滤列表最大个数 */
#define DNS_FILTER_HOST_FILE_DIR "/tmp/"
#define DNS_FILTER_HOST_NAME DNS_FILTER_HOST_FILE_DIR"cgi_dnsfilter"

typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulIndex;
	word8 aucFilterDomain[CM_DOMAIN_LEN];
#define DNS_FILTER_LIST_ATTR_DOMAIN_MASK (0x01)
#define DNS_FILTER_LIST_ATTR_MASK_ALL (0xffff)
	uword32 ulBitmap;
} __PACK__ IgdSecurDnsFilterListAttrConfTab;


#endif
