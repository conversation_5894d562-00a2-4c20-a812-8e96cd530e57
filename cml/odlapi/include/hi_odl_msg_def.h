/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm msg define
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_MSG_DEF_H
#define HI_ODL_MSG_DEF_H
#include "hi_odl_basic_type.h"
#include "hi_sysdef.h"

/* cm report msg to cwmp or omci */
#define CM_T_OMCI_MSG_KEY "/usr/lib/libhi_omci_ctl.so"
#define CM_T_CWMP_MSG_KEY "/etc/cwmp_cert/cpe.key"

/* mtype */
#define CM_T_MSG_TYPE      1158
#define CM_T_MSG_TYPE1     1159
#define DIAGNOSTIC_DONE    1160
#define CM_RESTORE_NOTIFY  1161
#define DNSLINIT_ALARM     1162
#define CM_T_INIT_STEP     1163

/* wan_type */
enum Msg_type {
	IS_TR069_WAN = 1,
	IS_IP_WAN    = 2,
	IS_WAN       = 3,
	IS_INFORM    = 4,
	IS_MAINTENCE = 5,
	IS_REMOTE_ATTRIBUTE = 6,
	IS_TR069_WAN_UP = 7,
	IS_LOID_REGISTER_START = 8,
	IS_PWR_REGISTER_START = 9,
	IS_SN_REGISTER_START = 10,
	IS_OMCI = 11,
	IS_SHORT_RESTORE = 12,
	IS_LONG_RESTORE = 13,
	IS_DDNS_AUTHFALIED = 13,
	IS_DDNS_SERVERERROR = 14,
	IS_IPOEWAN_DOWN = 15,
	STB_NUM_CHANGE = 16
};

#define WAN_NAME_LEN 64
typedef struct CM_msg {
	long msg_type;
	int wan_type;
	char wan_name[WAN_NAME_LEN];
	char buff[256];
} CM_msg;
#define ITMS_REG_STATUS_FULL_FILE_PATH_NAME "/tmp/itms"

/* cm report msg ipc_service */
#define CM_T_IPCS_MSG_KEY "/etc/dbus-1/system.conf"
/* mtype */
typedef enum {
	CM_T_IPCSERVICE_MSG_TYPE 				= 1200,
	CM_T_IPCSERVICE_MSG_TYPE_USB_INSERT 			= 1201,
	CM_T_IPCSERVICE_MSG_TYPE_USB_PULL 			= 1202,

	/* IPCS signal message */
	CM_T_IPCSERVICE_MSG_TYPE_MEM_ALARM 			= 1203,
	CM_T_IPCSERVICE_MSG_TYPE_AP_RSSI_QUERY_RESULT 		= 1204,
	CM_T_IPCSERVICE_MSG_TYPE_STA_BSS_TRANSITION_RESULT	= 1205,
	CM_T_IPCSERVICE_MSG_TYPE_STA_RSSI_QUERY_RESULT		= 1206,
	CM_T_IPCSERVICE_MSG_TYPE_STA_RSSI_LOW_ALARM		= 1207,
	//CM_T_IPCSERVICE_MSG_TYPE_CPU_TEMP_ALARM		= 1208,
	//CM_T_IPCSERVICE_MSG_TYPE_PON_TEMP_ALARM		= 1209,
	//CM_T_IPCSERVICE_MSG_TYPE_SYS_WRITABLE_FLASH_ALARM	= 1210,
	CM_T_IPCSERVICE_MSG_TYPE_SYS_CONN_NUM_ALARM		= 1211,
	CM_T_IPCSERVICE_MSG_TYPE_COLLECT_WIFIINFO_STATUS	= 1212,
	CM_T_IPCSERVICE_MSG_TYPE_WIFI_CONN_FAILURE		= 1213,
	CM_T_IPCSERVICE_MSG_TYPE_SYS_VOICE_EVENTS		= 1214,

	/* Async-method result message */
	CM_T_IPCSERVICE_MSG_TYPE_HTTP_DOWNLOAD_TEST_FIN		= 1215,
	CM_T_IPCSERVICE_MSG_TYPE_UPGRADE_FIN			= 1216,
	CM_T_IPCSERVICE_MSG_TYPE_DNS_SPEEDLIMIT_FIN		= 1217,
	CM_T_IPCSERVICE_MSG_TYPE_IPTV_DETECT_FIN		= 1218,
	CM_T_IPCSERVICE_MSG_TYPE_PHONE_CONNECT_RESULT		= 1219,
	CM_T_IPCSERVICE_MSG_TYPE_SPEED_TESTFF_FIN		= 1220,
	CM_T_IPCSERVICE_MSG_TYPE_VOIP_SIMULATE_TEST_FIN		= 1221,

	/* beacon tx vsie status message */
	CM_T_IPCSERVICE_MSG_TYPE_BEACON_TX_VSIE_STATUS		= 1222,

	CM_T_IPCSERVICE_MSG_TYPE_WAN_UPDATE			= 1223,

	/* CM notify signal message */
	CM_T_IPCSERVICE_MSG_TYPE_IPPING_DONE			= 1224,
	CM_T_IPCSERVICE_MSG_TYPE_TRACEROUTE_DONE		= 1225,
	CM_T_IPCSERVICE_MSG_TYPE_AUTODIAG_MONITOR_CHG		= 1226,
	CM_T_IPCSERVICE_MSG_TYPE_RMS_SPEEDTEST_DONE		= 1227,

	CM_T_IPCSERVICE_MSG_TYPE_WAN_ADDR_DETECTED		= 1230,
	CM_T_IPCSERVICE_MSG_TYPE_WAN_ADDR_DISAPPEAR		= 1231,

	CM_T_IPCSERVICE_MSG_TYPE_LANHOST_ONOFFLINE		= 1233,
	CM_T_IPCSERVICE_MSG_TYPE_HTTP_TRAFFIC_PROCESS		= 1234,
	CM_T_IPCSERVICE_MSG_TYPE_L2TPVPN_STATUS_PROCESS		= 1235,

	CM_T_IPCSERVICE_MSG_TYPE_SPEED_TESTLOCAL_FIN		= 1236,
	CM_T_IPCSERVICE_MSG_TYPE_APP_FILTER 			= 1237, /** appfilter */
	CM_T_X_CT_COM_IPOEWANDOWN                               = 1238,
	CM_T_IPCSERVICE_MSG_TYPE_STA_INFO				= 1239, /*sta info msg*/
} CM_T_IPCSERVICE_MSG_TYPE_E;

#define CM_T_IPC_MSG_LEN 1024
typedef enum {
	CM_MSG_IPCS_SET = 1,
	CM_MSG_IPCS_ADD = 2,
	CM_MSG_IPCS_DEL = 3, /* cfg report active */
	CM_MSG_IPCS_UPDATA = 4, /* status report active */
	CM_MSG_IPCS_UPDATA_WITH_DATE = 5,
} CM_MSG_IPCS_ACT;

typedef struct {
	int aui_index[4];     // multiobj max level  = 4
	int ui_level_num;
} hi_ipcs_cm_level_s;

typedef struct CM_IPCServcie_msg {
	long mtype;                 /* 消息类型:CM_T_IPCSERVICE_MSG_TYPE */
	int cm_tab_id;              /* cm表项索引 */
	int obj_type;               /* 实例类型: single=0,multi=1 */
	int obj_index;              /* 多实例对象编号 */
	CM_MSG_IPCS_ACT obj_act;    /* 动作类型 */
	hi_ipcs_cm_level_s level;
	char buff[CM_T_IPC_MSG_LEN];
} CM_IPCServcie_msg;

#endif
