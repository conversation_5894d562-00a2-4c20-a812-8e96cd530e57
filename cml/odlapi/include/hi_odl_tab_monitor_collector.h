/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm monitor collector obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_MONITOR_COLLECTOR_H
#define HI_ODL_TAB_MONITOR_COLLECTOR_H
#include "hi_odl_basic_type.h"

/***************************状态上报监控基本属性表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define MONITORCOLLECTOR_DISABLE (0)
#define MONITORCOLLECTOR_ENABLE (1)
	uword8 ucEnable;/*默认不启用监控*/
#define MONITORCOLLECTOR_PROTOCOL_FTP (0)
#define MONITORCOLLECTOR_PROTOCOL_TFTP (1)
	uword8 ucProtocol;
	uword8 ucPad[CM_TWO_PADS];

	uword8 aucServerUrl[CM_URL_LEN];
	uword32 ulTftpPort;
	uword8 aucUsername[CM_USERNAME_LEN];
	uword8 aucPassword[CM_PASSWORD_LEN];
#define MONITOR_COLLECTOR_ATTR_MASK_BIT0_ENABLE (0x01)
#define MONITOR_COLLECTOR_ATTR_MASK_BIT1_URL (0x02)
#define MONITOR_COLLECTOR_ATTR_MASK_BIT2_TFTPPORT (0x04)
#define MONITOR_COLLECTOR_ATTR_MASK_BIT3_PROTOCOL (0x08)
#define MONITOR_COLLECTOR_ATTR_MASK_BIT4_NAME (0x10)
#define MONITOR_COLLECTOR_ATTR_MASK_BIT5_PSD (0x20)
#define MONITOR_COLLECTOR_ATTR_MASK_ALL (0x3f)
	uword32 ulBitmap;
} __PACK__ IgdPMMonitorCollectorAttrConfTab;


/***************************监控参数属性表*********************************/
#define IGD_PM_MONITOR_CONFIG_TAB_RECORD_NUM (16)

typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucMonitorConfigIndex;
	uword8 aucPad[CM_THREE_PADS];

#define MONITOR_CFG_PARA_LEN (128*10)
	uword8 aucParaList[MONITOR_CFG_PARA_LEN];
	uword32 ulTimeList;
#define MONITOR_CFG_ATTR_MASK_BIT0_PARA_LIST (0x01)
#define MONITOR_CFG_ATTR_MASK_BIT1_TIME_LIST (0x02)
#define MONITOR_CFG_ATTR_MASK_ALL (0x03)
	uword32 ulBitmap;
} __PACK__ IgdPMMonitorCfgAttrConfTab;

#endif
