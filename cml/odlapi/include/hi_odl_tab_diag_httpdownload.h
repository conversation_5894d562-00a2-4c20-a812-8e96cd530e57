/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: cm emu http download obj attribute
 * Author: HSAN
 * Create: 2024-02-04
 */
#ifndef HI_ODL_TAB_DIAG_HTTPDOWNLOAD_H
#define HI_ODL_TAB_DIAG_HTTPDOWNLOAD_H
#include "hi_odl_basic_type.h"

/* for CTC httpdowntest */
#define SPEEDTEST_HTTPDOWNLOAD_RESULT_LEN (256)
#define SPEEDTEST_HTTPDOWNLOAD_URL_LEN (1024)

typedef struct {
	uint32_t state_and_index;
#define SPEEDTEST_HTTPDOWNLOAD_STATUS_SUCCEED 0
#define SPEEDTEST_HTTPDOWNLOAD_STATUS_FAILED 1
	uint32_t status;
	char result[SPEEDTEST_HTTPDOWNLOAD_RESULT_LEN];
	char url[SPEEDTEST_HTTPDOWNLOAD_URL_LEN];
	uint32_t duration_time; /* seconds */
#define SPEEDTEST_HTTPDOWNLOAD_ATTR_MASK_STATUS (0x01)
#define SPEEDTEST_HTTPDOWNLOAD_ATTR_MASK_RESULT (0x02)
#define SPEEDTEST_HTTPDOWNLOAD_ATTR_MASK_URL (0x04)
#define SPEEDTEST_HTTPDOWNLOAD_ATTR_MASK_DURATION_TIME (0x08)
#define SPEEDTEST_HTTPDOWNLOAD_ATTR_MASK_ALL (0xFF)
	uint32_t bitmap;
} __PACK__ IgdPMDiagSpeedTestHttpDownloadCfgAttrTab;

typedef enum {
	IGD_DIAG_SPEEDTEST_FF_SPEEDTYPE_DOWN = 1,
	IGD_DIAG_SPEEDTEST_FF_SPEEDTYPE_UP = 2,
	IGD_DIAG_SPEEDTEST_FF_SPEEDTYPE_UPDOWN = 3,
	IGD_DIAG_SPEEDTEST_FF_SPEEDTYPE_PING = 4,
	IGD_DIAG_SPEEDTEST_FF_SPEEDTYPE_BOTTOM
} igd_diag_speedtest_ff_speedtype;
typedef struct {
	uint32_t state_and_index;

#define IGD_DIAG_SPEEDTEST_FF_TESTINGSTATUS_TESTING (1)
#define IGD_DIAG_SPEEDTEST_FF_TESTINGSTATUS_TESTEND (0)
	/* Internal Notification Usage */
#define IGD_DIAG_SPEEDTEST_FF_TESTINGSTATUS_TESTNOTIFY (2)
	uint8_t testing_status; /* true is testing , false is done */
	uint8_t speed_type;
	uint8_t pad0[2];

#define IGD_DIAG_SPEEDTEST_FF_RESULT_SUCC (0)
#define IGD_DIAG_SPEEDTEST_FF_RESULT_FAIL (1)
	uint32_t result;

#define IGD_DIAG_SPEEDTEST_FF_TICKET_STRING_LEN (80)
	char ticket[IGD_DIAG_SPEEDTEST_FF_TICKET_STRING_LEN];

#define IGD_DIAG_SPEEDTEST_FF_URL_STRING_LEN (1024)
	char url[IGD_DIAG_SPEEDTEST_FF_URL_STRING_LEN];

#define IGD_DIAG_SPEEDTEST_FF_RATE_STR_LEN (16)
	char dw_speed_max_rate[IGD_DIAG_SPEEDTEST_FF_RATE_STR_LEN];
	char dw_speed_rate[IGD_DIAG_SPEEDTEST_FF_RATE_STR_LEN];
	char up_speed_max_rate[IGD_DIAG_SPEEDTEST_FF_RATE_STR_LEN];
	char up_speed_rate[IGD_DIAG_SPEEDTEST_FF_RATE_STR_LEN];

#define IGD_DIAG_SPEEDTEST_FF_TIME_STRING_LEN (32)
	char begin_time[IGD_DIAG_SPEEDTEST_FF_TIME_STRING_LEN];
	char end_time[IGD_DIAG_SPEEDTEST_FF_TIME_STRING_LEN];

#define IGD_DIAG_SPEEDTEST_FF_TIME_DELAY_STR_LEN (16)
	char max_time_delay[IGD_DIAG_SPEEDTEST_FF_TIME_DELAY_STR_LEN];
	char min_time_delay[IGD_DIAG_SPEEDTEST_FF_TIME_DELAY_STR_LEN];
	char avg_time_delay[IGD_DIAG_SPEEDTEST_FF_TIME_DELAY_STR_LEN];

#define IGD_DIAG_SPEEDTEST_FF_PACKET_LOSS_LEN (16)
#define IGD_DIAG_SPEEDTEST_FF_PACKET_SHAKE_LEN (16)
	char packet_loss[IGD_DIAG_SPEEDTEST_FF_PACKET_LOSS_LEN];
	char packet_shake[IGD_DIAG_SPEEDTEST_FF_PACKET_SHAKE_LEN];

	uint32_t duration_time;

#define IGD_DIAG_SPEEDTEST_FF_ATTR_MASK_TESTINGSTATUS (0x01)
#define IGD_DIAG_SPEEDTEST_FF_ATTR_MASK_RESULT (0x02)
#define IGD_DIAG_SPEEDTEST_FF_ATTR_MASK_TICKET (0x04)
#define IGD_DIAG_SPEEDTEST_FF_ATTR_MASK_SPEEDTYPE (0x08)
#define IGD_DIAG_SPEEDTEST_FF_ATTR_MASK_URL (0x10)
#define IGD_DIAG_SPEEDTEST_FF_ATTR_MASK_DWMAXRATE (0x20)
#define IGD_DIAG_SPEEDTEST_FF_ATTR_MASK_DWRATE (0x40)
#define IGD_DIAG_SPEEDTEST_FF_ATTR_MASK_UPMAXRATE (0x80)
#define IGD_DIAG_SPEEDTEST_FF_ATTR_MASK_UPRATE (0x100)
#define IGD_DIAG_SPEEDTEST_FF_ATTR_MASK_BEGINGTIME (0x200)
#define IGD_DIAG_SPEEDTEST_FF_ATTR_MASK_ENDTIME (0x400)
#define IGD_DIAG_SPEEDTEST_FF_ATTR_MASK_MAX_TIME_DELAY (0x800)
#define IGD_DIAG_SPEEDTEST_FF_ATTR_MASK_MIN_TIME_DELAY (0x1000)
#define IGD_DIAG_SPEEDTEST_FF_ATTR_MASK_AVG_TIME_DELAY (0x2000)
#define IGD_DIAG_SPEEDTEST_FF_ATTR_MASK_PACKET_LOSS (0x4000)
#define IGD_DIAG_SPEEDTEST_FF_ATTR_MASK_PACKET_SHAKE (0x8000)
#define IGD_DIAG_SPEEDTEST_FF_ATTR_MASK_DURATION_TIME (0x10000)
#define IGD_DIAG_SPEEDTEST_FF_ATTR_MASK_ALL (0xFFFFFFFF)
	uint32_t bitmap;
} __PACK__ IgdPMDiagSpeedTestFFCfgAttrTab;

typedef enum {
	IGD_DIAG_SPEEDTEST_LOCAL_SPEEDTYPE_DOWN = 1,
	IGD_DIAG_SPEEDTEST_LOCAL_SPEEDTYPE_UP = 2,
	IGD_DIAG_SPEEDTEST_LOCAL_SPEEDTYPE_UPDOWN = 3,
	IGD_DIAG_SPEEDTEST_LOCAL_SPEEDTYPE_PING = 4,
	IGD_DIAG_SPEEDTEST_LOCAL_SPEEDTYPE_BOTTOM
} igd_diag_speedtest_local_speedtype;
typedef struct {
	uint32_t state_and_index;

#define IGD_DIAG_SPEEDTEST_LOCAL_DISABLED (0)
#define IGD_DIAG_SPEEDTEST_LOCAL_ENABLED (1)
	uint8_t enable;

#define IGD_DIAG_SPEEDTEST_LOCAL_TESTINGSTATUS_TESTEND (0)
#define IGD_DIAG_SPEEDTEST_LOCAL_TESTINGSTATUS_TESTING (1)
	/* Internal Notification Usage */
#define IGD_DIAG_SPEEDTEST_LOCAL_TESTINGSTATUS_TESTNOTIFY (2)
	uint8_t testing_status; /* true is testing , false is done */
	uint8_t speed_type;
	uint8_t pad;

#define IGD_DIAG_SPEEDTEST_LOCAL_RESULT_SUCC (0)
#define IGD_DIAG_SPEEDTEST_LOCAL_RESULT_FAIL (1)
	uint32_t result;

#define IGD_DIAG_SPEEDTEST_LOCAL_URL_STRING_LEN (1024)
	char url[IGD_DIAG_SPEEDTEST_LOCAL_URL_STRING_LEN];

#define IGD_DIAG_SPEEDTEST_LOCAL_RATE_STR_LEN (16)
	char dw_speed_max_rate[IGD_DIAG_SPEEDTEST_LOCAL_RATE_STR_LEN];
	char dw_speed_rate[IGD_DIAG_SPEEDTEST_LOCAL_RATE_STR_LEN];
	char up_speed_max_rate[IGD_DIAG_SPEEDTEST_LOCAL_RATE_STR_LEN];
	char up_speed_rate[IGD_DIAG_SPEEDTEST_LOCAL_RATE_STR_LEN];

#define IGD_DIAG_SPEEDTEST_LOCAL_TIME_STRING_LEN (32)
	char begin_time[IGD_DIAG_SPEEDTEST_LOCAL_TIME_STRING_LEN];
	char end_time[IGD_DIAG_SPEEDTEST_LOCAL_TIME_STRING_LEN];

	uint8_t rfu1[32];
	uint8_t rfu2[32];

#define IGD_DIAG_SPEEDTEST_LOCAL_ATTR_MASK_ENABLE (0x01)
#define IGD_DIAG_SPEEDTEST_LOCAL_ATTR_MASK_TESTINGSTATUS (0x02)
#define IGD_DIAG_SPEEDTEST_LOCAL_ATTR_MASK_RESULT (0x04)
#define IGD_DIAG_SPEEDTEST_LOCAL_ATTR_MASK_SPEEDTYPE (0x08)
#define IGD_DIAG_SPEEDTEST_LOCAL_ATTR_MASK_URL (0x10)
#define IGD_DIAG_SPEEDTEST_LOCAL_ATTR_MASK_DW_SPEED_MAX_RATE (0x20)
#define IGD_DIAG_SPEEDTEST_LOCAL_ATTR_MASK_DW_SPEED_RATE (0x40)
#define IGD_DIAG_SPEEDTEST_LOCAL_ATTR_MASK_UP_SPEED_MAX_RATEE (0x80)
#define IGD_DIAG_SPEEDTEST_LOCAL_ATTR_MASK_UP_SPEED_RATE (0x100)
#define IGD_DIAG_SPEEDTEST_LOCAL_ATTR_MASK_BEGINGTIME (0x200)
#define IGD_DIAG_SPEEDTEST_LOCAL_ATTR_MASK_ENDTIME (0x400)
#define IGD_DIAG_SPEEDTEST_LOCAL_ATTR_MASK_ALL (0xffffff)
	uint32_t bitmap;
} __PACK__ IgdPMDiagSpeedTestLocalCfgAttrTab;

#endif /* HI_ODL_TAB_DIAG_HTTPDOWNLOAD_H */
