/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author: HSAN
 * Create: 2023-12-18
 */
#ifndef HI_ODL_TAB_DSCP_APP_ROUTE_H
#define HI_ODL_TAB_DSCP_APP_ROUTE_H
#include "hi_odl_basic_type.h"

#define IGD_DSCP_APPROUTE_MAX_ENTRY (256)
#define IGD_DSCP_APPROUTE_APP_ID_LEN (32)
#define IGD_DSCP_APPROUTE_NAME_LEN (64)
#define IGD_DSCP_APPROUTE_IF_NAME_LEN (32)
#define IGD_DSCP_APPROUTE_DOMAINLIST_MAX_SIZE (256)
#define IGD_DSCP_APPROUTE_DOMAIN_LEN (256)
#define IGD_DSCP_APPROUTE_DESTIPLIST_MAX_SIZE (2048)
#define IGD_DSCP_APPROUTE_DESTIP_LEN (INET6_ADDRSTRLEN)
#define IGD_DSCP_APPROUTE_MACLIST_MAX_SIZE (32)
#define IGD_DSCP_APPROUTE_MAC_LEN (13) // 13: xxxxxxxxxxxx\0

#define DSCP_APPROUTE_CONFIG_PATH_MAX_LEN 64

typedef struct {
    uword32 state_and_index;
    uword32 entry_index;
    char app_id[IGD_DSCP_APPROUTE_APP_ID_LEN];
    uword8 enable;
    uword8 match_type;
    uword8 pad1[2];
    char name[IGD_DSCP_APPROUTE_NAME_LEN];
    char if_name[IGD_DSCP_APPROUTE_IF_NAME_LEN];
    word32 dscpmarkvalue_up;
    word32 dscpmarkvalue_down;
    char domain_list_file[DSCP_APPROUTE_CONFIG_PATH_MAX_LEN];
    char dest_ip_list_file[DSCP_APPROUTE_CONFIG_PATH_MAX_LEN];
    char mac_list_file[DSCP_APPROUTE_CONFIG_PATH_MAX_LEN];
#define ACTION_TYPE_DEST_IP 0
#define ACTION_TYPE_DOMAIN 1
#define ACTION_TYPE_MAC 2
    uword32 action_type; /** 0: DestIPList, 1: DomainList, 2: MACList */
#define ACTION_DEL 0
#define ACTION_ADD 1
    uword32 action; /** 0: delete, 1: add */
    char tmp_list_file[DSCP_APPROUTE_CONFIG_PATH_MAX_LEN];

#define IGD_ATTR_MASK_DSCP_APP_ROUTE_APPID (0x01)
#define IGD_ATTR_MASK_DSCP_APP_ROUTE_ENABLE (0x02)
#define IGD_ATTR_MASK_DSCP_APP_ROUTE_MATCH_TYPE (0x04)
#define IGD_ATTR_MASK_DSCP_APP_ROUTE_NAME (0x08)
#define IGD_ATTR_MASK_DSCP_APP_ROUTE_IF_NAME (0x10)
#define IGD_ATTR_MASK_DSCP_APP_ROUTE_DSCPMARKVALUE_UP (0x20)
#define IGD_ATTR_MASK_DSCP_APP_ROUTE_DSCPMARKVALUE_DOWN (0x40)
#define IGD_ATTR_MASK_DSCP_APP_ROUTE_DOMAIN_LIST (0x80)
#define IGD_ATTR_MASK_DSCP_APP_ROUTE_DEST_IP_LIST (0x100)
#define IGD_ATTR_MASK_DSCP_APP_ROUTE_MAC_LIST (0x200)
#define IGD_ATTR_MASK_DSCP_APP_ROUTE_ACTION (0x400)
#define IGD_ATTR_MASK_DSCP_APP_ROUTE_ALL (0xffff)

    uword32 bitmap;
} __PACK__ IgdDscpAppRouteAttrConfTab;

#endif
