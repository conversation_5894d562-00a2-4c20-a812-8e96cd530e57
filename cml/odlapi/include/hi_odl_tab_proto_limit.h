/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm tab proto rate limit obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_PROTO_LIMIT_H
#define HI_ODL_TAB_PROTO_LIMIT_H
#include "hi_odl_basic_type.h"

/***************************协议报文流量控制*********************************/
#define PROTO_CAR_PORT_MAX 8

typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulIndex;
	uword32  ulArpRate;       /*0-不限制，!0-限制速率kbps*/
	uword32  ulDhcpRate;      /*0-不限制，!0-限制速率kbps*/
	uword32  ulIgmpRate;      /*0-不限制，!0-限制速率kbps*/
	uword32  ulDhcpv6Rate;      /*0-不限制，!0-限制速率kbps*/
	uword32  ulIcmpv6Rate;      /*0-不限制，!0-限制速率kbps*/
	uword32  ulPPPoeRate;      /*0-不限制，!0-限制速率kbps*/
	uword32  ulMldRate;      /*0-不限制，!0-限制速率kbps*/
	uword32  ulBcRate;        /*0-不限制，!0-限制速率kbps*/
#define SECURE_PROTO_CAR_ATTR_MASK_BIT0_ARP_RATE (0x01)
#define SECURE_PROTO_CAR_ATTR_MASK_BIT0_DHCP_RATE (0x02)
#define SECURE_PROTO_CAR_ATTR_MASK_BIT0_IGMP_RATE (0x04)
#define SECURE_PROTO_CAR_ATTR_MASK_BIT0_DHCPV6_RATE (0x08)
#define SECURE_PROTO_CAR_ATTR_MASK_BIT0_ICMPV6_RATE (0x10)
#define SECURE_PROTO_CAR_ATTR_MASK_BIT0_PPPOE_RATE (0x20)
#define SECURE_PROTO_CAR_ATTR_MASK_BIT0_MLD_RATE (0x40)
#define SECURE_PROTO_CAR_ATTR_MASK_BIT0_BC_RATE (0x80)
#define SECURE_PROTO_CAR_ATTR_MASK_MASK_ALL (0xff)
	uword32 ulBitmap;
} __PACK__ IgdSecureProtoCarTab;

#endif
