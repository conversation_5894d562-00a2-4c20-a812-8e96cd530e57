/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm device ctrl command define
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_CTRL_CMD_H
#define HI_ODL_CTRL_CMD_H
#include "hi_odl_basic_type.h"
#include "hi_logdef.h"

#ifndef LOG_DEBUG
#define LOG_DEBUG HI_DBG_LEVEL_DEBUG
#endif
#ifndef LOG_ERR
#define LOG_ERR 	HI_DBG_LEVEL_ERR
#endif
#ifndef LOG_INFO
#define LOG_INFO 	HI_DBG_LEVEL_INFO
#endif

#define IGD_CM_OPERATE_SUCCESS (0)
#define IGD_CM_OPERATE_FAIL (-1)
#define IGD_CM_OPERATE_FULL (-2)
#define IGD_CM_OPERATE_ITEM_EXIST (-3)
#define IGD_CM_OPERATE_ITEM_NOT_EXIST (-4)
#define IGD_CM_OPERATE_PARAM_INVALID  (-5)
#define IGD_CM_OPERATE_ASYNC_START_FAILED  (-6)
#define IGD_CM_OPERATE_ASYNC_TASK_BUSY  (-7)
#define IGD_CM_OPERATE_MEM_NOT_ENOUGH  (-8)
#define IGD_CM_OPERATE_NOTRESOLV (-2)
#define IGD_CM_ERROR_CODE_BASE_ADDR (0x10000000)
#define IGD_CM_GLOBAL_INPUT_PARA_ERROR (IGD_CM_ERROR_CODE_BASE_ADDR + 0x00000001)


#define IGD_CM_RES_STATE_BUSY                          0x80000000
#define IGD_CM_RES_STATE_WEB                            (0x010000)
#define IGD_CM_RES_STATE_DBUS                           (0x020000)
#define IGD_CM_RES_STATE_TR069                          (0x040000)
#define IGD_CM_RES_STATE_CM                             (0x080000)
#define IGD_CM_RES_STATE_OTHER                          (0x100000)


#define IGD_CM_CMD_BASE_ADDR (0x20000000)
#define IGD_CM_CMD_REBOOT (IGD_CM_CMD_BASE_ADDR + 0x00000001)
#define IGD_CM_CMD_SHORT_KEY_RESTORE (IGD_CM_CMD_BASE_ADDR + 0x00000002)
#define IGD_CM_CMD_LONG_KEY_RESTORE (IGD_CM_CMD_BASE_ADDR + 0x00000003)
#define IGD_CM_CMD_REMOTE_KEY_RESTORE (IGD_CM_CMD_BASE_ADDR + 0x00000004)
#define IGD_CM_CMD_FACTORY_TEST_RESET (IGD_CM_CMD_BASE_ADDR + 0x00000005)
#define IGD_CM_CMD_DOWNLOAD_CONFIG (IGD_CM_CMD_BASE_ADDR + 0x00000006)
#define IGD_CM_CMD_UPLOAD_CONFIG (IGD_CM_CMD_BASE_ADDR + 0x00000007)
#define IGD_CM_CMD_SYSLOG_CLEAR (IGD_CM_CMD_BASE_ADDR + 0x00000008)
#define IGD_CM_CMD_COMPLETELY_RESTORE (IGD_CM_CMD_BASE_ADDR + 0x00000009)
#define IGD_CM_CMD_LOS_LED_CTRL_ENABLE (IGD_CM_CMD_BASE_ADDR + 0x0000000a)
#define IGD_CM_CMD_LOS_LED_CTRL_DISABLE (IGD_CM_CMD_BASE_ADDR + 0x0000000b)

#define IGD_CM_CMD_REBOOT_BY_ITMS (IGD_CM_CMD_BASE_ADDR + 0x0000000c)
#define IGD_CM_CMD_REBOOT_BY_TELECOMADMIN (IGD_CM_CMD_BASE_ADDR + 0x0000000d)
#define IGD_CM_CMD_REBOOT_BY_DBUS (IGD_CM_CMD_BASE_ADDR + 0x0000000e)
#define IGD_CM_CMD_REBOOT_BY_POWER (IGD_CM_CMD_BASE_ADDR + 0x0000000f)
#define IGD_CM_CMD_REBOOT_BY_UBUS (IGD_CM_CMD_BASE_ADDR + 0x00000017)
#define IGD_CM_CMD_REBOOT_BY_EXECPTION (IGD_CM_CMD_BASE_ADDR + 0x00000018)

#define IGD_CM_CMD_WEB_KEY_RESTORE (IGD_CM_CMD_BASE_ADDR + 0x00000011)
#define IGD_CM_CMD_PLATFORM_KEY_RESTORE (IGD_CM_CMD_BASE_ADDR + 0x00000012)
#define IGD_CM_CMD_CLEAR_RESTORE (IGD_CM_CMD_BASE_ADDR + 0x00000013)
#define IGD_CM_CMD_REMOTE_CFG_RECORVERY (IGD_CM_CMD_BASE_ADDR + 0x00000014)
#define IGD_CM_CMD_REMOTE_CFG_UPLOAD (IGD_CM_CMD_BASE_ADDR + 0x00000015)
#define IGD_CM_CMD_FLUSH_LRN_TAB (IGD_CM_CMD_BASE_ADDR + 0x00000016)
#define IGD_CM_CMD_NO_KEY_RESTORE (IGD_CM_CMD_BASE_ADDR + 0x00000017)

enum {
	REBOOT_BY_NORMAL_RESET = 1,
	REBOOT_BY_RESTORE_CFG,
	REBOOT_BY_UPGRADE_IMAGE,
};

enum {
	BOOT_BY_WAKE_UP = 4,
	BOOT_BY_TR069_WAN,
	BOOT_BY_NEGO_PERIOD
};


#endif
