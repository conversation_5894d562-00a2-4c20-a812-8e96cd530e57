/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm all obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_ALL_STRUCT_H
#define HI_ODL_TAB_ALL_STRUCT_H
#include "hi_odl_tab_awifi.h"
#include "hi_odl_tab_client_download.h"
#include "hi_odl_tab_ctdpi.h"
#include "hi_odl_tab_device_alarm.h"
#include "hi_odl_tab_dns_filter.h"
// #include "hi_odl_tab_easymesh.h"
#include "hi_odl_tab_emu_httpdownload.h"
#include "hi_odl_tab_emu_ipoe.h"
#include "hi_odl_tab_emu_mc.h"
#include "hi_odl_tab_emu_ping.h"
#include "hi_odl_tab_emu_ppp.h"
// #include "hi_odl_tab_emu_speed.h"
#include "hi_odl_tab_emu_tracert.h"
#include "hi_odl_tab_l2detect.h"
#include "hi_odl_tab_ext_device.h"
#include "hi_odl_tab_firewall.h"
#include "hi_odl_tab_global_info.h"
#include "hi_odl_tab_incomming_filter.h"
#include "hi_odl_tab_ip_filter.h"
#include "hi_odl_tab_lacp.h"
#include "hi_odl_tab_landevice.h"
#include "hi_odl_tab_lanhost.h"
#include "hi_odl_tab_mac_filter.h"
#include "hi_odl_tab_monitor_collector.h"
#include "hi_odl_tab_netapp.h"
#include "hi_odl_tab_netmonitor.h"
#include "hi_odl_tab_osgi_env.h"
#include "hi_odl_tab_packet_capture.h"
#include "hi_odl_tab_parent_ctrl.h"
#include "hi_odl_tab_platform_srv.h"
#include "hi_odl_tab_pon_info.h"
#include "hi_odl_tab_uni_pon_info.h"
#include "hi_odl_tab_portal.h"
#include "hi_odl_tab_port_mirror.h"
#include "hi_odl_tab_proto_limit.h"
#include "hi_odl_tab_qos.h"
#include "hi_odl_tab_remote_mgt.h"
#include "hi_odl_tab_static_arp.h"
#include "hi_odl_tab_storage.h"
#include "hi_odl_tab_sysmng.h"
#include "hi_odl_tab_timer.h"
#include "hi_odl_tab_transfer_service.h"
#include "hi_odl_tab_uplink.h"
#include "hi_odl_tab_url_filter.h"
#include "hi_odl_tab_userlimit.h"
#include "hi_odl_tab_voice.h"
#include "hi_odl_tab_vpn.h"
#include "hi_odl_tab_vxlan.h"
#include "hi_odl_tab_wan_conn.h"
#include "hi_odl_tab_wlan.h"
#include "hi_odl_tab_common.h"
#include "hi_odl_tab_dscp_app_route.h"
#include "hi_odl_tab_app_filter.h"
#include "hi_odl_tab_hard_acc_cancel_mgr.h"
#include "hi_odl_tab_hard_acc_cancel.h"
#include "hi_odl_tab_vsie_probe_rsp_tx_vsie.h"
#include "hi_odl_tab_vsie_probe_rsp_tx_vsie_mgr.h"
#include "hi_odl_tab_cloudvr.h"
#include "hi_odl_tab_special_service_vr.h"
#include "hi_odl_tab_diag_httpdownload.h"
#include "hi_odl_tab_vs_custom.h"

#endif
