/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm global obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_CT_DPI_H
#define HI_ODL_TAB_CT_DPI_H
#include "hi_odl_basic_type.h"

#define CM_DPI_NM_DNS_NAME_LEN	128
#define CM_DPI_NM_SRV_IP_LEN	32

#define CM_DPI_UPLOAD_URL_LEN	128

#define CM_DPI_UPLOAD_NAME_LEN	32
#define	CM_DPI_UPLOAD_PWD_LEN	32

#define CM_DPI_NM_WAN_DEV_LEN	16

#define CM_DPI_WEBSITE_MAX_LEN	128

#define CM_DPI_APP_NAME_LEN		32

#define CM_DPI_TR098_LEN		256

#define CM_DPI_PROTOCOL_LEN		16
#define CM_DPI_SEARCH_LEN		128
#define CM_DPI_IP_LEN			16

/***************************无线通用功能属性表************************************/
/*表相关宏*/

typedef struct {
	uword32 ulStateAndIndex;
	uword32	ulEnable;
	uword32 ulPeriodic;
	word8	acUrl[CM_DPI_UPLOAD_URL_LEN];
	word8	acUsername[CM_DPI_UPLOAD_NAME_LEN];
	word8	acPassword[CM_DPI_UPLOAD_PWD_LEN];
	word8	acCompressPassword[CM_DPI_UPLOAD_PWD_LEN];
	uword32	ulFlowExportPeriodic;
	uword32	ulClassficationNumOfEntries;
	uword32	ulAppNumOfEntries;
	uword32	ulFlowNumOfEntries;

#define DPI_UPLOAD_ENABLE     						(1<<0)
#define DPI_UPLOAD_PERIODIC   						(1<<1)
#define DPI_UPLOAD_URL            					(1<<2)
#define DPI_UPLOAD_USERNAME       					(1<<3)
#define DPI_UPLOAD_USERPASSWORD             		(1<<4)
#define DPI_UPLOAD_COMPRESSPASSWORD         		(1<<5)
#define DPI_UPLOAD_FLOW_EXPORT_PERIODIC             (1<<6)
#define DPI_UPLOAD_CLASSIFICATION_NUM_OF_ENTRIES	(1<<7)
#define DPI_UPLOAD_APP_NUM_OF_ENTRIES       		(1<<8)
#define DPI_UPLOAD_FLOW_NUM_OF_ENTRIES         		(1<<9)

	uword32 ulBitmap;
} __PACK__ IgdDpiUploadAttrCfgTab;


typedef struct {
	uword32 ulStateAndIndex;
	uword32	ulDevCtrlEnable;
	uword32 ulFlowStatEnable;
	uword32 ulPktStatEnable;

#define DPI_DEV_CTRL_ENABLE     	(1<<0)
#define DPI_FLOW_STAT_ENABLE   		(1<<1)
#define DPI_PKT_STAT_ENABLE      	(1<<2)

	uword32 ulBitmap;
} __PACK__ IgdDpiBasicAttrCfgTab;

/*表相关宏*/
typedef struct {
	uword32 ulStateAndIndex;
	uword32	ulEnable;
	uword32 ulInterval;
	word8	acInterface[CM_DPI_TR098_LEN];
	word8	acDNSSrvIp[CM_DPI_NM_SRV_IP_LEN];
	uword32 ulDNSSrvPort;
	word8 	acDomainName[CM_DPI_WEBSITE_MAX_LEN];
	uword32	ulNumOfRepetions;
	uword32	ulTimeOfThreshold;

#define DPI_DNS_QUERY_ENABLE     		(1<<0)
#define DPI_DNS_QUERY_INTERVAL   		(1<<1)
#define DPI_DNS_QUERY_INTERFACE      	(1<<2)
#define DPI_DNS_QUERY_SRV_IP      		(1<<3)
#define DPI_DNS_QUERY_SRV_PORT     		(1<<4)
#define DPI_DNS_QUERY_DOMAIN_NAME    	(1<<5)
#define DPI_DNS_QUERY_NUM_OF_REPETION    (1<<6)
#define DPI_DNS_QUERY_TIME_OF_THRESHOLD  (1<<7)

	uword32 ulBitmap;
} __PACK__ IgdDpiDnsQueryAttrCfgTab;

typedef struct {
	uword32 ulStateAndIndex;
	uword32	ulEnable;
	uword32 ulInterval;
	word8	acInterface[CM_DPI_TR098_LEN];
	word8	acHttpSrvUrl[CM_DPI_NM_SRV_IP_LEN];
	uword32 ulHttpSrvPort;
	uword32	ulNumOfRepetions;
	uword32	ulTimeOfThreshold;

#define DPI_HTTP_GET_ENABLE     			(1<<0)
#define DPI_HTTP_GET_INTERVAL   			(1<<1)
#define DPI_HTTP_GET_INTERFACE      		(1<<2)
#define DPI_HTTP_GET_SRV_URL      			(1<<3)
#define DPI_HTTP_GET_SRV_PORT     			(1<<4)
#define DPI_HTTP_GET_NUM_OF_REPETION    	(1<<5)
#define DPI_HTTP_GET_TIME_OF_THRESHOLD  	(1<<6)

	uword32 ulBitmap;
} __PACK__ IgdDpiHttpGetAttrCfgTab;

typedef struct {
	uword32 ulStateAndIndex;
	uword32	ulEnable;
	uword32 ulInterval;
	word8	acInterface[CM_DPI_TR098_LEN];
	word8	acTcpSrvIp[CM_DPI_NM_SRV_IP_LEN];
	uword32 ulTcpSrvPort;
	uword32	ulNumOfRepetions;
	uword32	ulTimeOfThreshold;

#define DPI_TCP_CONNECT_ENABLE     			(1<<0)
#define DPI_TCP_CONNECT_INTERVAL   			(1<<1)
#define DPI_TCP_CONNECT_INTERFACE      		(1<<2)
#define DPI_TCP_CONNECT_SRV_IP      		(1<<3)
#define DPI_TCP_CONNECT_SRV_PORT     		(1<<4)
#define DPI_TCP_CONNECT_NUM_OF_REPETION    	(1<<5)
#define DPI_TCP_CONNECT_TIME_OF_THRESHOLD  	(1<<6)

	uword32 ulBitmap;
} __PACK__ IgdDpiTcpConnectAttrCfgTab;

typedef struct {
	uword32 ulStateAndIndex;
	uword32	ulIndex;
	word8	acAppName[CM_DPI_APP_NAME_LEN];
	uword32 ulAppEnable;

#define DPI_APP_NAME     			(1<<0)
#define DPI_APP_ENABLE   			(1<<1)

	uword32 ulBitmap;
} __PACK__ IgdDpiAppAttrCfgTab;

typedef struct {
	uword32 ulStateAndIndex;
	uword32	ulIndex;
	uword32	ulEnable;
	word8	acClassInterface[CM_DPI_TR098_LEN];
	word8	acProtocol[CM_DPI_PROTOCOL_LEN];
	uword32	ulProtocolExclude;
	word8	acSearchEngines[CM_DPI_SEARCH_LEN];
	word8	acParticularKey[CM_DPI_SEARCH_LEN];
	word8	acDestIp[CM_DPI_IP_LEN];
	word8	acDestMask[CM_DPI_IP_LEN];
	uword32	ulDestIpExclude;
	word8	acSourceIp[CM_DPI_IP_LEN];
	word8	acSourceMask[CM_DPI_IP_LEN];
	uword32	ulSourceIpExclude;
	word32	ulDestPort;
	word32	ulDestPortRangeMax;
	word32	ulDestPortExclude;
	word32	ulSourcePort;
	word32	ulSourcePortRangeMax;
	word32	ulSourcePortExclude;
	word8	acOtherExpression[CM_DPI_TR098_LEN];
	word8	acApplicationPath[CM_DPI_TR098_LEN];
	word8	acFlowPath[CM_DPI_TR098_LEN];

#define DPI_CLASSIFICATION_ENABLE     				(1<<0)
#define DPI_CLASSIFICATION_CLASS_INTERFACE  		(1<<1)
#define DPI_CLASSIFICATION_PROTOCOL    				(1<<2)
#define DPI_CLASSIFICATION_PROTOCOL_EXCLUDE  		(1<<3)
#define DPI_CLASSIFICATION_SEARCH_ENGINES     		(1<<4)
#define DPI_CLASSIFICATION_PARTICULAR_KEY  			(1<<5)
#define DPI_CLASSIFICATION_DEST_IP     				(1<<6)
#define DPI_CLASSIFICATION_DEST_MASK  				(1<<7)
#define DPI_CLASSIFICATION_DEST_IP_EXCLUDE  		(1<<8)
#define DPI_CLASSIFICATION_SOURCE_IP     			(1<<9)
#define DPI_CLASSIFICATION_SOURCE_MASK  			(1<<10)
#define DPI_CLASSIFICATION_SOURCE_IP_EXCLUDE  		(1<<11)
#define DPI_CLASSIFICATION_DEST_PORT     			(1<<12)
#define DPI_CLASSIFICATION_DEST_PORT_RANGE_MAX  	(1<<13)
#define DPI_CLASSIFICATION_DEST_PORT_EXCLUDE  		(1<<14)
#define DPI_CLASSIFICATION_SOURCE_PORT     			(1<<15)
#define DPI_CLASSIFICATION_SOURCE_PORT_RANGE_MAX	(1<<16)
#define DPI_CLASSIFICATION_SOURCE_PORT_EXCLUDE  	(1<<17)
#define DPI_CLASSIFICATION_OTHER_EXPRESSION     	(1<<18)
#define DPI_CLASSIFICATION_APPLICATION_PATH  		(1<<19)
#define DPI_CLASSIFICATION_FLOW_PATH  				(1<<20)
	uword32 ulBitmap;
} __PACK__ IgdDpiClassificationAttrCfgTab;

#endif
