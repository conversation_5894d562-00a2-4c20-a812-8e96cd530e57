#ifndef IGD_CM_API_H
#define IGD_CM_API_H
#include "hi_odl_basic_type.h"
#include "hi_odl_tab_def.h"
#include "hi_odl_msg_def.h"
#include "hi_odl_para.h"
#include "hi_odl_notify_msg.h"
#include "hi_odl_tab_cfg_def.h"
#include "hi_odl_ctrl_cmd.h"
#include "hi_odl_tab_all_structs.h"

typedef enum {
    IGD_INIT_STATE_NONE = 0,
    IGD_INIT_STATE_EARLY,
    IGD_INIT_STATE_MAIN,
    IGD_INIT_STATE_MEDIUM,
    IGD_INIT_STATE_TARDY,
    IGD_INIT_STATE_FINISH,
    IGD_INIT_STATE_CMREADY,
    IGD_INIT_STATE_OMCI,
    IGD_INIT_STATE_OSGI,
    IGD_INIT_STATE_MAX,
} IgdModuleStateBitMap;

typedef struct {
    unsigned int module_type;
    unsigned int cur_state;
} hi_module_state_s;

typedef struct{
    unsigned int ui_mod;       /*消息所属模块*/
    unsigned int ui_level;     /*日志等级*/
    unsigned int ui_type;     /*日志类型*/
    char ac_data[512]; /*日志内容*/
}hi_log_msg_s;

typedef struct hi_log_modify
{
    unsigned int ui_mod;       /*消息所属模块*/
    unsigned int ui_level;     /*日志等级*/
    unsigned int ui_act;       /* 日志动作类型 */
    unsigned int ui_type;     /*日志类型*/
    char ac_data[512];          /*日志内容*/
}hi_log_modify_s;

typedef struct {
    char ac_data[1024];  /* crashlog内容 */
} hi_cm_crashlog_s;

#define HI_BUNDLEID_NUM_MAX 1024
typedef struct {
    uword32    lTableID;
    uword32    lInstNum;
    uword32    alIndexs[HI_BUNDLEID_NUM_MAX];
} igdCmConfAllIndex;

#define IGD_CM_TABCMD_BUFLEN 256
typedef struct {
    uint32_t    tab_handle;
    uint32_t    cmd_type;
    uint32_t    buf_len;
    uint8_t     cmd_buf[IGD_CM_TABCMD_BUFLEN];
} igdCmTabCmd;

//Add by fyy for voip status
#define IGD_CM_VOIP_TMP_STATUS "/tmp/voipstatus"
#define IGM_CM_VOIP_REGFAIL_CODE "/tmp/voipRegFailCode"

#define IGD_CM_VOIP_MAX   1
typedef enum {
	IGD_CM_VS_CALL_EP_REGISTERING,
	IGD_CM_VS_CALL_EP_REGISTERED,
	IGD_CM_VS_CALL_EP_HOOK_OFF,
	IGD_CM_VS_CALL_EP_DIAL,
	IGD_CM_VS_CALL_EP_RING,
	IGD_CM_VS_CALL_EP_RINGBACK,
	IGD_CM_VS_CALL_EP_CONNECTING,
	IGD_CM_VS_CALL_EP_CONNECTED,
	IGD_CM_VS_CALL_EP_REGISTER_FAIL,
	IGD_CM_VS_CALL_EP_INACTIVED,
}IGD_CM_VS_VOIP_STATUS;

//Bohannon for bug#00016150, refer to IGM_CM_VOIP_REGFAIL_CODE to check error code 
#define VS_CALL_REG_ERR_NO_ERR 0 // = VS_CALL_REG_NO_ERR
//End of Add

extern int igdCmConfStart(unsigned int tab_id, unsigned int *inst, unsigned int len);
extern int igdCmConfStop(unsigned int tab_id, unsigned int *inst, unsigned int len);

#ifdef CONFIG_PLATFORM_OPENWRT
inline int igdCmConfSet(unsigned int tab_id, unsigned char *pucInfo, unsigned int len)
{
    return HI_RET_SUCC;
}
inline int igdCmConfGet(unsigned int tab_id, unsigned char *pucInfo, unsigned int len)
{
    return HI_RET_SUCC;
}
inline int igdCmConfGetEntryNum(unsigned int tab_id, unsigned int *entrynum)
{
    return HI_RET_SUCC;
}
inline int igdCmConfAdd(unsigned int tab_id, unsigned char *pucInfo, unsigned int len)
{
    return HI_RET_SUCC;
}
inline int igdCmConfDel(unsigned int tab_id, unsigned char *pucInfo, unsigned int len)
{
    return HI_RET_SUCC;
}
inline int igdCmConfGetAllEntry(unsigned int tab_id, unsigned char *pucInfo, unsigned int len)
{
    return HI_RET_SUCC;
}
inline unsigned int igdCmRebootInfo(unsigned int reboot_source, unsigned int reboot_type)
{
    return HI_RET_SUCC;
}
inline int igdCmConfGetallIndex(unsigned int tab_id, unsigned char *pucInfo, unsigned int len)
{
    return HI_RET_SUCC;
}
inline int igdCmOamCtrl(unsigned int ulCmd)
{
    return HI_RET_SUCC;
}
inline int igd_cm_voip_status(unsigned char ucPortNo)
{
	return HI_RET_SUCC;
}
inline int igdCmGetMainSsidIdx(int band)
{
	return HI_RET_SUCC;
}
#else
extern int igdCmConfAdd(unsigned int tab_id, unsigned char *pucInfo, unsigned int len);
extern int igdCmConfDel(unsigned int tab_id, unsigned char *pucInfo, unsigned int len);
extern int igdCmConfSet(unsigned int tab_id, unsigned char *pucInfo, unsigned int len);
extern int igdCmConfGet(unsigned int tab_id, unsigned char *pucInfo, unsigned int len);
extern int igdCmConfGetEntryNum(unsigned int tab_id, unsigned int *entrynum);
extern int igdCmConfGetAllEntry(unsigned int tab_id, unsigned char *pucInfo, unsigned int len);
extern unsigned int igdCmRebootInfo(unsigned int reboot_source, unsigned int reboot_type);
extern int igdCmConfGetallIndex(unsigned int tab_id, unsigned char *pucInfo, unsigned int len);
extern int igdCmOamCtrl(unsigned int ulCmd);
extern int igd_cm_voip_status(unsigned char ucPortNo);
int igdCmGetMainSsidIdx(int band);
#endif


extern int igdCmOamCtrlAsync(unsigned int ulCmd);

extern int igdCmApiChartoMac(const char *mac_char, unsigned char *mac_digital);
extern int igdCmApiMactoChar(const unsigned char *mac_digital,char *mac_char);
extern unsigned int igdCmApiChartoIp(const char* pszIp);
extern int igdCmApiIptoChar(const unsigned char *ip_digital,char *ip_char, unsigned int size);
extern int igdCmApiIpv6toChar(const void *ipv6_digital,char *ipv6_char);
extern int igdCmApiChartoIpv6(const void *ipv6_digital,char *ipv6_char);
extern int igdCmApiIpv4Valid(char *pc_addr);
extern int igdCmApiChartoHex(char srcchar);
extern char igdCmApiHextoChar(unsigned char srchex);
extern unsigned int igdCmFindMaxConDevInstNum();
extern unsigned int igdCmFindMaxPPPConInstNum(unsigned int condevnum);
extern unsigned int igdCmFindMaxIPConInstNum(unsigned int condevnum);
extern unsigned int igdCmFindXPIDConInstNum(unsigned int condevnum);
extern unsigned int igdCmFindGlobalIDConInstNum(unsigned int condevnum);
extern unsigned int igdCmXPIndexToGlobalIndex(unsigned int *pGlobalIndex, unsigned int ConDevIndex, unsigned int XPConIndex, unsigned int AddressingType);
unsigned int igdCmSSIDToIndex(unsigned int ulIndex);
unsigned int igdCmIndexToSSID(unsigned int ulIndex);
extern int igdCmWanConfCheck(unsigned char *pucInfo, unsigned int len);
extern unsigned int igdCmFirmwareUpgradeApi(void *pucInfo, unsigned int len);
extern unsigned int igdCmFirmwareUpgradeStateGet(void *pucInfo, unsigned int len);
extern unsigned int igdCmFirmwareUpgradeFlagGet(void);
extern unsigned int igdCmLogApi(void *pucInfo, unsigned int len,unsigned int mod ,unsigned int level,unsigned int type);
extern unsigned int igdCmLogModApi(void *pucLog, unsigned int len , unsigned int mod, unsigned int lvl, int act,unsigned int type);

extern unsigned int igdCmGetRebootInfo(unsigned int *reboot_type);
extern unsigned int igdCmFirmworkUpgradeApi(void *pucInfo, unsigned int len);
extern int idgCmDoSystem(char *cmd);
extern int idgCmDoExecv(char *file, char *argv[], unsigned int arg_count);

int igdCmSetAlarmCode(int alarm_index, int state);
int igdCmGetRfbandNum(void);
int igdCmGetBandSsidNum(int band);
int igdCmGetEmBHSsidIdx(int band);
int igd_cm_get_total_ssid_num(void);

extern int igdCmCrashLogGet(char *crashlog, unsigned int len);
int igdCmApiULtoChar(unsigned int num, char *des, int size);
int igdCmApiUnlink(char *data);

int32_t igd_cm_api_split_string(char *arry1d, uint32_t arry1d_size, char *arry2d, uint32_t rows, uint32_t cols);
int32_t igd_cm_api_splice_string(char *arry2d, uint32_t rows, uint32_t cols, char *arry1d, uint32_t arry1d_size);

int32_t igd_cm_api_get_wan_name_by_wan_interface(const char* wan_interface, char *wan_info, uint32_t size);
int32_t igd_cm_api_get_wan_interface_by_wan_name(const char *wan_name, char *wan_interface, uint32_t size);

int igd_cm_write_file(const char *file_name, const void *data, unsigned int count);
int igd_cm_read_file(const char *file_name, const void *data, unsigned int count);
int igd_cm_del_file(const char *file_name);
long igd_cm_get_file_size(const char *file_name);
int igd_cm_flush_hard_acc(unsigned char *pucInfo, unsigned int len);
int igd_cm_wifi_channel_check(uint8_t region, uint8_t band, uint8_t channel);

typedef struct {
    char aucFileName[256];
    char aucPhysicalFolderName[256];
    uint32_t ulIsFolder;
    uint32_t ulFileSize;
    char aucModifiedTime[64];
}igd_file_info;
void hi_parse_folder_info(igd_file_info *filelist, uint32_t len, char *foldername);
#endif /*IGD_CM_API_H*/
