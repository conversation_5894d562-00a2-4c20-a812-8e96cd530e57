/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm table cfg obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_CFG_DEF_H
#define HI_ODL_TAB_CFG_DEF_H
#include <stdint.h>
#include "hi_odl_tab_def.h"

#define CM_CFGSRC_WEB 0x1
#define CM_CFGSRC_CWMP 0x2
#define CM_CFGSRC_IPCS 0x3
#define CM_CFGSRC_OSGI 0x4
#define CM_CFGSRC_CM 0x5

#define CM_ID_TABID_SHIFT 0
#define CM_ID_INDEX_SHIFT 16
#define CM_ID_TABTYPE_SHIFT 24
#define CM_ID_CFGSRC_SHIFT 28

#define CM_ID_TABID_BITMASK (0xFFFF)
#define CM_ID_INDEX_BITMASK (0xFF)
#define CM_ID_TABTYPE_BITMASK (0x1)
#define CM_ID_CFGSRC_BITMASK (0xF)

#define CM_ID_BITS_GET(a, b) ((a & (CM_ID_##b##_BITMASK) << CM_ID_##b##_SHIFT) >> CM_ID_##b##_SHIFT)
#define CM_ID_BITS_SET(a, b) ((a & CM_ID_##b##_BITMASK) << CM_ID_##b##_SHIFT)

#define BITMAP_CHECK(bitmap, bit) (((bitmap) & (bit)) != 0)
#define BITMAP_CHECK64_H(bitmap, bit) (((bitmap >> 32) & (bit)) != 0)
#define BITMAP_CHECK64_L(bitmap, bit) (((bitmap & 0xffffffff) & (bit)) != 0)

/* 采用如下结构体更方便设置属性，同时兼容原有的TABID(uint32), 后续将逐渐替换 */
typedef struct __hi_odl_cfghdr {
	union {
		uint32_t data;
		struct {
			uint32_t tabid : 12; /* obj (tab) index */
			uint32_t resv1 : 4;  /* resv */
			uint32_t inst : 8;   /* obj inst id */
			uint32_t tabtype : 1;

#define CM_LAZY_DISABLE (0x0)
#define CM_LAZY_ENABLE (0x1)
			uint32_t lazy : 1; /* lazy apply, true(1): Execute the call asynchronously, otherwise set it false(0) */
			uint32_t resv2 : 2;
			uint32_t cfg_src : 4;
		};
	};
} hi_odl_cfghdr;

static inline uint32_t odl_cfg_src(uint32_t *state_and_index)
{
	return ((hi_odl_cfghdr *)state_and_index)->cfg_src;
}

static inline uint32_t odl_tabid_src(uint32_t tab_id, uint32_t cfg_src)
{
	((hi_odl_cfghdr *)&tab_id)->cfg_src = cfg_src;
	return tab_id;
}

#define ODL_CFGHDR_ZERO()                                            \
	{                                                                \
		.tabid = 0, .inst = 0, .tabtype = 0, .lazy = 0, .cfg_src = 0 \
	}

#define ODL_CFGHDR_INIT(p)           \
	{                                \
		.data = ((uint32_t *)(p))[0] \
	}

#define ODL_CFGHDR_LAZY_DISABLE(p)                       \
	do {                                                 \
		hi_odl_cfghdr *__tmp_hdr = (hi_odl_cfghdr *)(p); \
		__tmp_hdr->lazy = CM_LAZY_DISABLE;                             \
	} while (0)

#define ODL_CFGHDR_LAZY_ENABLE(p)                        \
	do {                                                 \
		hi_odl_cfghdr *__tmp_hdr = (hi_odl_cfghdr *)(p); \
		__tmp_hdr->lazy = CM_LAZY_ENABLE;                             \
	} while (0)

#define ODL_CFGHDR_OBJ(p) (hi_odl_cfghdr *)(p)

#endif
