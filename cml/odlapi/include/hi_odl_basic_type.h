/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm basic type
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_BASIC_TYPE_H
#define HI_ODL_BASIC_TYPE_H
#include "hi_basic.h"

#ifndef WIN32
#define __PACK__ __attribute__ ((packed))
#else
#define __PACK__
#endif

typedef unsigned char uword8;
typedef char word8;
typedef unsigned short uword16;
typedef short word16;
typedef unsigned int uword32;
typedef int word32;
typedef unsigned long long int uword64;
typedef long long int word64;
typedef float float32;
typedef long long int lword64;
typedef unsigned long long ulword64;

#define IGD_ATTR_DISABLED (0x0)
#define IGD_ATTR_ENABLED (0x1)

//通用字段定义
#define CM_IP_ADDR_LEN (4)          /* IPv4地址长度 */
#define CM_IP_ADDR_STRING_LEN (16) /* IPv4addr length */
#define CM_IPV6_ADDR_LEN (16)       /* IPv6地址长度 */
#define CM_IPV6_ADDR_LEN_MAX (48)  /* IPv6addr max length */
#define CM_MAC_ADDR_LEN (6)         /* MAC地址长度 */
#define CM_DOMAIN_NAME_LEN (64)    /* 域名长度 */
#define CM_URL_LEN (256)           /* URL长度 */
#define CM_TWO_PADS (2)            /* 填充字段 */
#define CM_THREE_PADS (3)          /* 填充字段 */
#define CM_USERNAME_LEN (64)      /* 认证用户名长度 */
#define CM_PASSWORD_LEN (64)      /* 认证密码长度 */
#define CM_WAN_CONNECTION_NAME_LEN (64)  /* Wan连接的名字 */
#define CM_CONNECTION_URL_LEN (32) /* 反向连接URL长度 */
#define CM_MC_LEN (16)
#define CM_MAC_CHAR_LEN (24)
#define CM_BRLAN_IPV6_ADDR_NUM (6)
#define CM_STB_ADDR_LEN (128)           /* 机顶盒地址长度 */
#define CM_PORTMAPPING_EXTERNAL_PORT_LEN	(12)	/* 端口映射外部端口范围字符串长度 */
#define CM_URL_FILTER_MAC_LEN (260)           /*URL长度*/
#define CM_CMD_LEN 512
#define CM_FILE_PATH_LEN 128

enum {
	CM_LOG_EMERG_E = 0,
	CM_LOG_ALERT_E,
	CM_LOG_CRIT_E,
	CM_LOG_ERR_E,
	CM_LOG_WARNING_E,
	CM_LOG_NOTICE_E,
	CM_LOG_INFO_E,
	CM_LOG_DEBUG_E,
};

#endif
