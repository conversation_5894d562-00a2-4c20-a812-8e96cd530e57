/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author: HSAN
 * Create: 2023-12-18
 */
#ifndef HI_ODL_TAB_APP_FILTER_H
#define HI_ODL_TAB_APP_FILTER_H
#include "hi_odl_basic_type.h"

#define IGD_APP_FILTER_MAX_ENTRY (256)

#define IGD_APP_FILTER_APP_ID_LEN (32)
#define IGD_APP_FILTER_NAME_LEN (64)
#define IGD_APP_FILTER_IF_NAME_LEN (32)
#define IGD_APP_FILTER_DOMAINLIST_LEN (512)
#define IGD_APP_FILTER_DESTIPLIST_LEN (512)
#define IGD_APP_FILTER_MACLIST_LEN (512)
#define IGD_APP_FILTER_WEEKDAYS_LEN (32)
#define IGD_APP_FILTER_TIME_LEN (64)

typedef enum {
    IGD_APP_FILTER_MODE_BLACKLIST = 0,
    IGD_APP_FILTER_MODE_WHITELIST = 1,
    IGD_APP_FILTER_MODE_BOTTOM
} IGD_APP_FILTER_MODE;

typedef enum {
    IGD_APP_FILTER_DOMAINACTION_DO_NOTHING = 0,
    IGD_APP_FILTER_DOMAINACTION_REPLY_LANIP = 1,
    IGD_APP_FILTER_DOMAINACTION_DNS_REPLY_NAME_ERROR = 2,
    IGD_APP_FILTER_DOMAINACTION_BOTTOM
} IGD_APP_FILTER_DOMAINACTION;

typedef enum {
    IGD_APP_FILTER_DESTIPACTION_DROP = 0,
    IGD_APP_FILTER_DESTIPACTION_REJECT = 1,
    IGD_APP_FILTER_DESTIPACTION_BOTTOM
} IGD_APP_FILTER_DESTIPACTION;

typedef enum {
    IGD_APP_FILTER_SIGNAL_ENABLE_CLOSE = 0,
    IGD_APP_FILTER_SIGNAL_ENABLE_OPEN = 1,
    IGD_APP_FILTER_SIGNAL_ENABLE_BOTTOM
} IGD_APP_FILTER_SIGNAL_ENABLE;

typedef struct {
    uword32 state_and_index;
    uword32 entry_index;
    char app_id[IGD_APP_FILTER_APP_ID_LEN];
    uword8 enable;
    uword8 mode;
    uword8 pad1[2];
    char name[IGD_APP_FILTER_NAME_LEN];
#define APPFILTER_MAX_FILEPATH_LEN 64
    char domain_list_file[APPFILTER_MAX_FILEPATH_LEN];
    char dest_ip_list_file[APPFILTER_MAX_FILEPATH_LEN];
    char mac_list_file[APPFILTER_MAX_FILEPATH_LEN];

    uword8 domain_action;
    uword8 dest_ip_action;
    uword8 signal_enable;
    uword8 pad2;

    char weekdays[IGD_APP_FILTER_WEEKDAYS_LEN];
    char time[IGD_APP_FILTER_TIME_LEN];
    uword32 blocked_times;

#define ACTION_TYPE_DEST_IP 0
#define ACTION_TYPE_DOMAIN 1
#define ACTION_TYPE_MAC 2
    uword32 action_type; /** 0: DestIPList, 1: DomainList, 2: MACList */
#define ACTION_DEL 0
#define ACTION_ADD 1
    uword32 action; /** 0: delete, 1: add */
    char tmp_list_file[APPFILTER_MAX_FILEPATH_LEN];

#define IGD_ATTR_MASK_APP_FILTER_APPID (0x01)
#define IGD_ATTR_MASK_APP_FILTER_ENABLE (0x02)
#define IGD_ATTR_MASK_APP_FILTER_MODE (0x04)
#define IGD_ATTR_MASK_APP_FILTER_NAME (0x08)
#define IGD_ATTR_MASK_APP_FILTER_DOMAIN_LIST (0x10)
#define IGD_ATTR_MASK_APP_FILTER_DEST_IP_LIST (0x20)
#define IGD_ATTR_MASK_APP_FILTER_MAC_LIST (0x40)
#define IGD_ATTR_MASK_APP_FILTER_DOMAINACTION (0x80)
#define IGD_ATTR_MASK_APP_FILTER_DESTIPACTION (0x100)
#define IGD_ATTR_MASK_APP_FILTER_WEEKDAYS (0x200)
#define IGD_ATTR_MASK_APP_FILTER_TIME (0x400)
#define IGD_ATTR_MASK_APP_FILTER_BLOCKEDTIMES (0x800)
#define IGD_ATTR_MASK_APP_FILTER_SIGNALENABLE (0x1000)
#define IGD_ATTR_MASK_APP_FILTER_ACTION (0x2000)
#define IGD_ATTR_MASK_APP_FILTER_ALL (0xffff)

    uword32 bitmap;
} __PACK__ IgdAppFilterAttrConfTab;

#endif
