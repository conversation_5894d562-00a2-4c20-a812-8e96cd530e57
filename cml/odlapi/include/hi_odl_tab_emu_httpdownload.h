/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm emu http download obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_EMU_HTTPDOWNLOAD_H
#define HI_ODL_TAB_EMU_HTTPDOWNLOAD_H
#include "hi_odl_basic_type.h"

/**********************************电信httpdowntest功能************************************/
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulStatus ;
#define HTTP_DOWNLOAD_RESULT_LEN (256)
	word8  aucResult[HTTP_DOWNLOAD_RESULT_LEN] ;/*字节数单位kbyte 数据类型为整数*/
#define HTTP_DOWNLOAD_URL_LEN (1024)
	word8  aucURL[HTTP_DOWNLOAD_URL_LEN]
	;/*下载地址,地址内容包含类型比如http,ftp,多个地址以|隔开*/
	uword32 ulDurTime ; /*持续时间*/
	uword32 ulEnable ; /*测试开始*/
#define PM_HTTP_FTP_DT_MARK_BIT0_URL (0x01)
#define PM_HTTP_FTP_DT_MARK_BIT1_DURTIME (0x02)
#define PM_HTTP_FTP_DT_MARK_BIT2_ENABLE (0x04)
	uword32 ulBitmap;
} __PACK__ IgdPMHttpFtpDownloadTestCfgAttrConfTab;

#endif
