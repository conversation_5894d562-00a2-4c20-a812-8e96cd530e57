/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm link aggregation cfg
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_LACP_H
#define HI_ODL_TAB_LACP_H
#include "hi_odl_basic_type.h"


typedef struct {
	uword32 ulStateAndIndex;
#define  LACP_DEV_MAX 2
	uword8 ucIndex;
	uword8 ucEnable;
	uword8  aucPad[2];
#define LACP_DEV_PORT_ETH0 0x1
#define LACP_DEV_PORT_ETH1 0x2
#define LACP_DEV_PORT_ETH2 0x4
#define LACP_DEV_PORT_ETH3 0x8
#define LACP_DEV_PORT_ETH4 0x10
#define LACP_DEV_PORT_ETH5 0x20
#define LACP_DEV_PORT_ETH6 0x40
#define LACP_DEV_PORT_ETH7 0x80
#define LACP_DEV_PORT_ALL 0xff
	uword32  ulPortBind;

#define LACP_DEV_CFG_MASK_ENABLE    (0x1)
#define LACP_DEV_CFG_MASK_PORT_BIND (0x2)
#define LACP_DEV_CFG_MASK_ALL       (0x3)
	uword32 ulBitmap;
} __PACK__ IgdLACPDevCfgTab;

#endif
