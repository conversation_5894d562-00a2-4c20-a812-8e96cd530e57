/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm pon info obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_PON_INFO_H
#define HI_ODL_TAB_PON_INFO_H
#include <stdbool.h>
#include "hi_odl_basic_type.h"

/***************************PON链路状态信息表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define PON_FEC_ABILITY_SUPPORT_NO (0)
#define PON_FEC_ABILITY_SUPPORT_YES (1)
	uword8 ucFecAbility;/*FEC能力：支持、不支持*/
#define PON_FEC_STATE_DISABLE (0)
#define PON_FEC_STATE_ENABLE (1)
	uword8 ucFecStatusEnable;/*FEC开启、关闭*/
	uword8 aucPad[CM_TWO_PADS];

	word32 lTxPower; /* 发送光功率 */
	word32 lRxPower; /* 接收光功率 */
	word32 lTransTemperature; /*光模块工作温度 */
	word32 lSupplyVottage; /*光模块供电电压*/
	word32 lBiasCurrent; /* 偏置电流*/
	bool ui_dnopttxpwrenable;
	bool ui_dnoptrxpwrenable;
	double ui_dnopttxpower;
	double ui_dnopttxpwrthresholdh;
	double ui_dnopttxpwrthresholdl;
	double ui_dnoptrxpwrthresholdh;
	double ui_dnoptrxpwrthresholdl;
#define PON_LINK_STATUS_MASK_BIT0_FEC_ABILITY (0x01)
#define PON_LINK_STATUS_MASK_BIT1_FEC_ENABLE (0x02)
#define PON_LINK_STATUS_MASK_BIT2_TX_POWER (0x04)
#define PON_LINK_STATUS_MASK_BIT3_RX_POWER (0x08)
#define PON_LINK_STATUS_MASK_BIT4_TRANS_TEMPERATURE (0x10)
#define PON_LINK_STATUS_MASK_BIT5_SUPPLY_VOTTAGE (0x20)
#define PON_LINK_STATUS_MASK_BIT6_BIASCURRENT (0x40)
#define PON_LINK_STATUS_MASK_ALL (0x7f)
#define PON_LINK_STATUS_MASK_ALL_OPTICALPOWEROPTIMIZE (0xff)
	uword32 ulBitmap;
} __PACK__ IgdPonLinkStatusInfoTab;

/***************************PON口统计信息表*********************************/
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulReceiveBlocks; /* PON口接收到的数据块数 */
	uword32 ulTransmitBlocks; /* PON口发送的数据块数 */
	ulword64 ulBytesSent;/* PON口发送的字节数*/
	ulword64 ulByteReceived; /*PON口接收的字节数*/
	uword32 ulPacketsSent; /*PON口发送帧个数*/
	uword32 ulPacketsReceived; /*PON口接收帧个数*/
	uword32 ulUniPacketsSent; /*PON口发送单波帧数*/
	uword32 ulUniPacketsRecieved; /*PON口接收单波帧数*/
	uword32 ulMulPacketsSent; /*PON口发送组波帧数*/
	uword32 ulMulPacketsRecieved; /*PON口接收组波帧数*/
	uword32 ulBrPacketsSent; /*PON口发送广播波帧数*/
	uword32 ulBrPacketsRecieved; /*PON口接收广播波帧数*/
	uword32 ulFecError; /*PON口接收的FEC错误帧数*/
	uword32 ulFcsError;/*检测到的FCS错误总数*/
	uword32 ulHecError; /*PON口接收的HEC错误帧数*/
	uword32 ulDropPackets; /*PON口发送方向丢帧数*/
	uword32 ulPausePacketsSent;/*PON口发送的PAUSE流控制帧数*/
	uword32 ulPausePacketsRecieved; /* PON口接收的PAUSE流控制帧数*/
	uword32 ulDropPacketsRecieved; /*pon dropped recv packets*/
	uword32 ulEqD; /*EqD*/
	uword32 ulDistance; /*ONT与OLT之间间距离*/
	uword32 ulOltRegistTime; /*网关从收光到完成OLT注册达到O5状态消耗的时长（单位：毫秒）*/
#define LAST_REG_TIME_LEN 32
	word8 acOltLastRegistTime[LAST_REG_TIME_LEN]; /*OLT上次注册时间点, 格式：yyyy-MM-dd-HH-mm-ss-ms*/
#define NO_REG_NO_AUTH 1
#define REG_NO_AUTH 2
#define REG_AUTH 3
	uword32 ulOltLastRegistStatus; /*OLT上次注册状态,取值：NO_REG_NO_AUTH 未注册未授权；REG_NO_AUTH 已注册未授权；REG_AUTH 已注册已授权；*/

#define PON_STATISTICS_MASK_ALL (0x0f)
#define PON_STATISTICS_MASK_ALL_UPLINK (0xf0)
	uword32 ulBitmap;
} __PACK__ IgdPonStatisticsInfoTab;

/***************************IGD_CATV_ATTR_TAB表*********************************/
typedef struct {
	uword32 ulStateAndIndex;
    uword8  uc_catv_enable;
    uword32 ul_catv_rx_power;
    uword32 ul_catv_tx_power;
    word8   aucPad[3];
	uword32 ulBitmap;
} __PACK__ IgdCatvTab;

#endif
