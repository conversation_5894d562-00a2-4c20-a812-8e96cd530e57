include $(HI_EXT_CONFIG)

#===============================================================================
# export sub dir
#===============================================================================
HI_SUB_DIR :=
HI_SUB_DIR += source


#===============================================================================
# export lib
#===============================================================================
HI_LOC_LIB += -lhi_ipc
HI_LOC_INCLUDE += -I$(HI_LOC_CUR)/include

#
#===============================================================================
# target
#===============================================================================
TARGET 		= libhi_odlapi.so
TARGET_TYPE	= so

include $(HI_HISI_GW_APP_SCRIPT_DIR)/app.mk