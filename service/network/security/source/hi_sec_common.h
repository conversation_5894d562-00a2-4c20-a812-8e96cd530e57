/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2022. All rights reserved.
 * Description:Ethernet packet filtering
 * Author:HSAN
 * Create:2018-12-25
 * History:
 *     1.2019-01-29 hsan code restyle
 *     2.2022-11-18 hsan code restyle
 */
#ifndef __HI_SEC_COMMON_H__
#define __HI_SEC_COMMON_H__

#include <sys/time.h>
#include <libipset/ipset.h>
#include <libipset/types.h>
#include "hi_uspace.h"
#include "hi_ipc.h"
#include "hi_ipc_def.h"
#include "hi_notifier.h"
#include "hi_board.h"
#include "hi_sysinfo.h"
#include "hi_lan.h"
#include "hi_wan.h"
#include "hi_wan_notify.h"
#include "hi_xtables.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

#define HI_SAL_SEC_SYSCALL_STRLEN 300
#define HI_SAL_SEC_FILTER_NUM_MAX 100 /* filter num */
#define HI_SAL_SEC_FILTER_PORT_NUM 8 /* filter port num */
#define HI_SAL_SEC_LAN_PORT_MASK 0xF
#define HI_SAL_SEC_LAN_PORT_NUM 4


#define SEC_RELEASE
#if defined(SEC_RELEASE)
#define HI_SAL_SEC_TRACE(__ret, __arg1, __arg2, __arg3, __arg4)                          \
        hi_systrace((hi_uint32)HI_SRCMODULE_NET_SEC, (hi_uint32)__ret,               \
        (hi_uint32)__arg1, (hi_uint32)__arg2, (hi_uint32)__arg3, (hi_uint32)__arg4)

#define HI_SAL_SEC_DBG(fmt, arg...)                                                      \
        hi_print(HI_SRCMODULE_NET_SEC, HI_LOG_LEVEL_INFO_E,                              \
            "\n[NET_DBG:%s(%d)]"fmt, __func__,__LINE__,##arg)

#elif defined(SEC_DEBUG)
#define  HI_SAL_SEC_DBG(format, arg...)                           \
    do                                                         \
    {                                                          \
        hi_os_printf("DEBUG:[%s] [%d] ", __FUNCTION__, __LINE__); \
        hi_os_printf(""format"\n", ## arg);                      \
    } while (0)

#define HI_SAL_SEC_TRACE(__ret, __arg1, __arg2, __arg3, __arg4)                   \
    do                                                                  \
    {                                                                   \
        hi_os_printf("\n%s", __FUNCTION__);                             \
        hi_os_printf("\nline-%05u : ret=%08x arg1=%08x arg2=%08x arg3=%08x arg4=%08x\n", \
                     __LINE__, __ret, __arg1, __arg2, __arg3, __arg4);            \
    } while(0)

#elif defined(SEC_CLEAR)
#define  HI_SAL_SEC_DBG(format, arg...) do{}while (0)
#define  HI_SAL_SEC_TRACE(__ret, __arg1, __arg2, __arg3, __arg4)
#endif

int32_t hi_sec_ipfilter_init(void);
int32_t hi_sec_dbus_urlfilter_exit(void);
int32_t hi_sec_dbus_urlfilter_init(void);
int32_t hi_sec_ipfilter_exit(void);
int32_t hi_sec_urlfilter_init(void);
int32_t hi_sec_urlfilter_exit(void);
int32_t hi_sec_dnsfilter_init(void);
int32_t hi_sec_dnsfilter_exit(void);
int32_t hi_sec_protocar_init(void);
int32_t hi_sec_protocar_exit(void);
int32_t hi_sec_firewall_init(void);
int32_t hi_sec_firewall_exit(void);
int32_t hi_sec_wlan_isolation_init(void);
int32_t hi_sec_wlan_isolation_exit(void);



#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif/*__HI_SEC_COMMON_H__*/
