#include "hi_sec.h"
#include "hi_sec_common.h"
#include "hi_ipset.h"
#include "hi_netif_uapi.h"

#define WLAN_ISOLATION_CHAIN "wlan_isolation"

static int _ebtables_chain_exists(const char *chain)
{
    char cmd[256] = {0};
    snprintf(cmd, sizeof(cmd), "ebtables -t filter -L %s >/dev/null 2>&1", chain);
    return (system(cmd) == 0) ? 1 : 0;
}

static int _ebtables_rule_exists(const char *chain, const char *rule_match)
{
    char cmd[256] = {0};
    snprintf(cmd, sizeof(cmd), "ebtables -t filter -L %s | grep -Fq '%s' >/dev/null 2>&1", chain, rule_match);
    return (system(cmd) == 0) ? 1 : 0;
}

static void setup_wlan_isolation_rules()
{
    if (_ebtables_chain_exists(WLAN_ISOLATION_CHAIN) == 0)
    {
        printf("Creating ebtables chain: %s\n", WLAN_ISOLATION_CHAIN);
        hi_ebtables_rule_op("filter", "-N", WLAN_ISOLATION_CHAIN, "");
    }

    if (!_ebtables_rule_exists(WLAN_ISOLATION_CHAIN, "Policy: DROP"))
    {
        printf("Setting default policy to DROP for %s\n", WLAN_ISOLATION_CHAIN);
        hi_ebtables_rule_op("filter", "-P", WLAN_ISOLATION_CHAIN, "DROP");
    }

    const char* accept_interfaces[] = {"wan+", "em_bh_5g_sta", "em_bh_2g_sta"};
    for (int i = 0; i < sizeof(accept_interfaces)/sizeof(accept_interfaces[0]); i++)
    {
        char rule[128] = {0};
        snprintf(rule, sizeof(rule), "-o %s -j ACCEPT", accept_interfaces[i]);

        if (_ebtables_rule_exists(WLAN_ISOLATION_CHAIN, rule) == 0)
        {
            printf("Adding rule: %s to %s\n", rule, WLAN_ISOLATION_CHAIN);
            hi_ebtables_rule_op("filter", "-A", WLAN_ISOLATION_CHAIN, rule);
        }
    }

    const char* forward_interfaces[] = {"vap1", "vap9"};
    for (int i = 0; i < sizeof(forward_interfaces)/sizeof(forward_interfaces[0]); i++)
    {
        char rule[128] = {0};
        snprintf(rule, sizeof(rule), "-i %s -j %s", forward_interfaces[i], WLAN_ISOLATION_CHAIN);

        if (_ebtables_rule_exists("FORWARD", rule) == 0)
        {
            printf("Adding FORWARD rule: %s\n", rule);
            hi_ebtables_rule_op("filter", "-A", "FORWARD", rule);
        }
    }

}

static hi_void teardown_wlan_isolation_rules()
{
    const char* forward_interfaces[] = {"vap1", "vap9"};
    for (int i = 0; i < sizeof(forward_interfaces)/sizeof(forward_interfaces[0]); i++)
    {
        char rule[128] = {0};
        snprintf(rule, sizeof(rule), "-i %s -j %s", forward_interfaces[i], WLAN_ISOLATION_CHAIN);

        if (_ebtables_rule_exists("FORWARD", rule))
        {
            printf("Removing FORWARD rule: %s\n", rule);
            hi_ebtables_rule_op("filter", "-D", "FORWARD", rule);
        }
    }

    const char* accept_interfaces[] = {"wan+", "em_bh_5g_sta", "em_bh_2g_sta"};
    for (int i = 0; i < sizeof(accept_interfaces)/sizeof(accept_interfaces[0]); i++)
    {
        char rule[128] = {0};
        snprintf(rule, sizeof(rule), "-o %s -j ACCEPT", accept_interfaces[i]);

        if (_ebtables_rule_exists(WLAN_ISOLATION_CHAIN, rule))
        {
            printf("Removing rule: %s from %s\n", rule, WLAN_ISOLATION_CHAIN);
            hi_ebtables_rule_op("filter", "-D", WLAN_ISOLATION_CHAIN, rule);
        }
    }

    if (_ebtables_chain_exists(WLAN_ISOLATION_CHAIN))
    {
        printf("Deleting ebtables chain: %s\n", WLAN_ISOLATION_CHAIN);
        hi_ebtables_rule_op("filter", "-X", WLAN_ISOLATION_CHAIN, "");
    }
}


int32_t hi_sec_wlan_isolation_init(void)
{
    setup_wlan_isolation_rules();
    return 0;
}

int32_t hi_sec_wlan_isolation_exit(void)
{
    teardown_wlan_isolation_rules();
    return 0;
}

