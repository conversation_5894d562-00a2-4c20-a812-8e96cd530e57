/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_circuitpack.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-25
  Description: circuitpack
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"



/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

hi_int32 hi_omci_me_circuitpack_init(hi_uint32 ui_isup, hi_uint32 ui_index, hi_uint32 ui_ishgu)
{
	hi_omci_me_circuitpack_s st_entry;
	hi_omci_tapi_sysinfo_s st_info;
	hi_omci_tapi_sn_loid_s st_sn_loid;
	hi_int32 i_ret;
	hi_uint32 ui_unit_type;
	hi_uint32 ui_tcontid;
	hi_uint32 ui_tcontnum = 0;
	hi_uint32 ui_tcontbuff = 0;
	hi_uint32 ui_portnum = 1;
	hi_uint32 ui_portid;
	hi_uint32 ui_qnum;
	hi_uint32 ui_qnum_total = 0;
	hi_ushort16 us_instid = (0x0100 | (ui_index & 0xff));
	hi_uint32 ui_tsnum = 0;

	i_ret = hi_omci_tapi_sysinfo_get(&st_info);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_tapi_sn_loid_get(&st_sn_loid);
	HI_OMCI_RET_CHECK(i_ret);

	if (HI_TRUE == ui_isup) {
		us_instid = (0x0100 | (1 << 7));

		ui_unit_type = HI_OMCI_ME_PLUGIN_UNIT_TYPE_GPON24881244;

		i_ret = hi_omci_tapi_tcont_num_get(&ui_tcontnum);
		HI_OMCI_RET_CHECK(i_ret);

		ui_tcontbuff = ui_tcontnum - 1; //tcont0不上报
		ui_tsnum = 2;

		for (ui_tcontid = 1; ui_tcontid < ui_tcontnum; ui_tcontid++) {
			i_ret = hi_omci_tapi_queue_num_get((hi_omci_tapi_egress_type_e)(HI_OMCI_TAPI_EGRESS_TCONT0_E + ui_tcontid), &ui_qnum);
			HI_OMCI_RET_CHECK(i_ret);
			ui_qnum_total += ui_qnum;
		}

		ui_portnum = 1;//代表1个pon口
	} else if (HI_TRUE == ui_ishgu) {
        /*Modified by fyy for bug#15630 wifi uni num*/
        if (8 == ui_index) {
            us_instid = (0x0100 | 8);
            ui_unit_type = HI_OMCI_ME_PLUGIN_UNIT_TYPE_WIFI;
            ui_tsnum = 0;
            ui_qnum_total = 0;
            ui_portnum = st_info.ui_ssid_num;
        } else {
            us_instid = (0x0100 | 6);
            ui_unit_type = HI_OMCI_ME_PLUGIN_UNIT_TYPE_VEIP;
            ui_tsnum = 2;
            i_ret = hi_omci_tapi_queue_num_get(HI_OMCI_TAPI_EGRESS_VEIP_E, &ui_qnum_total);
            HI_OMCI_RET_CHECK(i_ret);
        }
        /*End of Modified*/
	} else {
		us_instid = (0x0100 | (ui_index & 0xff));//ui_index 0:伪槽位  1-254:自定义

		if (2 == ui_index) {
			ui_unit_type = HI_OMCI_ME_PLUGIN_UNIT_TYPE_POTS;
			ui_portnum = st_info.ui_pots_num;
			ui_qnum_total = 0;
			ui_tsnum = 0;
		} else if (0 == ui_index) {
			ui_unit_type = HI_OMCI_ME_PLUGIN_UNIT_TYPE_10100BASET;
			ui_portnum = st_info.ui_fe_num;
			ui_tsnum = 2;
			for (ui_portid = 0; ui_portid < ui_portnum; ui_portid++) {
				i_ret = hi_omci_tapi_queue_num_get((hi_omci_tapi_egress_type_e)(HI_OMCI_TAPI_EGRESS_UNI0_E + ui_portid), &ui_qnum);
				HI_OMCI_RET_CHECK(i_ret);
				ui_qnum_total += ui_qnum;
			}
		} else if (1 == ui_index) {
			ui_unit_type = HI_OMCI_ME_PLUGIN_UNIT_TYPE_101001000BASET;
			ui_portnum = st_info.ui_ge_num;
			ui_tsnum = 2;

			for (ui_portid = 0; ui_portid < ui_portnum; ui_portid++) {
				i_ret = hi_omci_tapi_queue_num_get((hi_omci_tapi_egress_type_e)(HI_OMCI_TAPI_EGRESS_UNI0_E + ui_portid), &ui_qnum);
				HI_OMCI_RET_CHECK(i_ret);
				ui_qnum_total += ui_qnum;
			}
		} else {
			hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
			return HI_RET_SUCC;
		}
	}

	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_CIRCUIT_PACK_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_entry, sizeof(st_entry), 0, sizeof(st_entry));
	st_entry.st_msghead.us_instid = us_instid;
	HI_OS_MEMCPY_S(st_entry.uc_sn, sizeof(st_entry.uc_sn), st_sn_loid.auc_sn, sizeof(st_entry.uc_sn));
	HI_OS_MEMCPY_S(st_entry.uc_version, sizeof(st_entry.uc_version), st_info.auc_version, sizeof(st_entry.uc_version));
	HI_OS_MEMCPY_S(st_entry.uc_equipid, sizeof(st_entry.uc_equipid), st_info.auc_equid, sizeof(st_entry.uc_equipid));
	st_entry.uc_type = (hi_uchar8)ui_unit_type;
	st_entry.uc_portnum = (hi_uchar8)ui_portnum;
	st_entry.ui_vendorid = htonl(st_info.ui_vendor_id);
	st_entry.uc_tcontnum = (hi_uchar8)ui_tcontbuff;
	st_entry.uc_pqnum = (hi_uchar8)ui_qnum_total;
	st_entry.uc_tsnum = (hi_uchar8)ui_tsnum;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_CIRCUIT_PACK_E, us_instid, &st_entry);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

