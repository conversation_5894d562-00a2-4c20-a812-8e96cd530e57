/******************************************************************************

                  版权所有 (C), 2009-2019, 海思半导体有限公司

 ******************************************************************************
  文 件 名   : omci_me_onu-data.c
  版 本 号   : 初稿
  作    者   : y00185833
  生成日期   : D2011_09_29
  功能描述   : G.988 OMCI manager entity onu data file.
******************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

//#define HI_OMCI_TCONT_FIRST_INST 0

/******************************************************************************
 Function    : hi_omci_me_onudata_reset
 Description : MIB reset业务处理，获取ONUID配置数据库
 Input Parm  : hi_void*pv_data,
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_onudata_reset(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	//hi_uint32 ui_onuid = 0xffffffff;
	//hi_uint32 ui_ret;
	//   hi_ploam_onuid_get(&ui_onuid);

	//    ui_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_TCONT_E, HI_OMCI_TCONT_FIRST_INST, HI_OMCI_ATTR1, &ui_onuid);
	//    HI_OMCI_RET_CHECK(ui_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);

	hi_omci_me_reset();
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_onudata_init()
{
	hi_omci_me_onudata_s st_entity;
	hi_int32 i_ret;
	hi_ushort16 us_instid = 0;

	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_ONT_DATA_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
	st_entity.st_msghead.us_instid = us_instid;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_ONT_DATA_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	return HI_RET_SUCC;
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */