/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_onu2_g.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-25
  Description: ONU2-G
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_ipc.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_onu2g_init
 Description : ONU2-G init
 Input Parm  : 无
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_onu2g_init()
{
	hi_omci_me_onu2_g_s st_entry;
	hi_omci_me_onu3_g_s st_onu3_entry;
	hi_omci_tapi_sysinfo_s st_info;
	hi_int32 i_ret;
	hi_uint32 ui_tcont_num;
	hi_uint32 ui_gemport_num;
	hi_uint32 ui_port_num;
	hi_uint32 ui_tcontid;
	hi_uint32 ui_portid;
	hi_uint32 ui_qnum;
	hi_uint32 ui_qnum_total = 0;

	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_ONT2_G_E, 0);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_tapi_sysinfo_get(&st_info);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_tapi_tcont_num_get(&ui_tcont_num);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_tapi_gemport_num_get(&ui_gemport_num);
	HI_OMCI_RET_CHECK(i_ret);

	for (ui_tcontid = 1; ui_tcontid < ui_tcont_num; ui_tcontid++) { //tcont0不上报
		i_ret = hi_omci_tapi_queue_num_get((hi_omci_tapi_egress_type_e)(HI_OMCI_TAPI_EGRESS_TCONT0_E + ui_tcontid), &ui_qnum);
		HI_OMCI_RET_CHECK(i_ret);
		ui_qnum_total += ui_qnum;
	}

	ui_port_num = st_info.ui_port_num;
	if (ui_port_num > 4) {
		ui_port_num = 4;
	}

	for (ui_portid = 0; ui_portid < ui_port_num; ui_portid++) {
		i_ret = hi_omci_tapi_queue_num_get((hi_omci_tapi_egress_type_e)(HI_OMCI_TAPI_EGRESS_UNI0_E + ui_portid), &ui_qnum);
		HI_OMCI_RET_CHECK(i_ret);
		//ui_qnum_total += ui_qnum;
	}

	if (HI_OMCI_ME_ONU_CAPABILITY_TYPE_HGU_E == st_info.ui_product_mode) {
		i_ret = hi_omci_tapi_queue_num_get(HI_OMCI_TAPI_EGRESS_VEIP_E, &ui_qnum);
		HI_OMCI_RET_CHECK(i_ret);
		//ui_qnum_total += ui_qnum;
	}

	HI_OS_MEMSET_S(&st_entry, sizeof(st_entry), 0, sizeof(st_entry));
	st_entry.st_msghead.us_instid = 0;

	HI_OS_MEMCPY_S(st_entry.uc_equipid, sizeof(st_entry.uc_equipid), st_info.auc_equid, sizeof(st_entry.uc_equipid));
    /*Modified by fyy*/
	//st_entry.uc_omccversion = (st_info.omic_ext == HI_ENABLE) ? HI_OMCI_ME_ONU2_OMCC_988_EXT : HI_OMCI_ME_ONU2_OMCC_988;
    st_entry.uc_omccversion = (st_info.omic_ext == HI_DISABLE) ? HI_OMCI_ME_ONU2_OMCC_988 : (hi_uchar8)st_info.omic_ext;
	st_entry.us_vendorcode = (hi_ushort16)st_info.ui_product_code;
	st_entry.uc_secflag = HI_OMCI_ME_ONU2_SEC_MODE_AES128;
	st_entry.uc_secmode = HI_OMCI_ME_ONU2_SEC_MODE_AES128;
	st_entry.us_pqnum = (hi_ushort16)ui_qnum_total;
	st_entry.uc_trafficnum = 7;//7个tcont的意思
	st_entry.uc_deprecated = 1;
	st_entry.us_gemportnum = (hi_ushort16)ui_gemport_num;

	/*
	该参数应根据单口端口SFU和HGU选择 1：MP； 多口端口SFU，选择N：MP。
	依据是：在FTTH场景下，主要有2种应用方式：采用HGU、采用单端口SFU+以太网上行家庭网关?    ５ザ丝赟FU或HGU的单播业务和组播业务的OMCI配置模型应符合G.984.4 Implementer’s Guide（second revision）9.3.1和9.4.1节的规定
	，如图6-2所示。如果采用多端口SFU，其单播业务和组播业务应采用图6-3所示的N：MP bridge-map-filtering的OMCI配置模型。
	*/
	if (st_info.ui_product_mode == HI_OMCI_ME_ONU_CAPABILITY_TYPE_HGU_E) {
		//st_entry.us_concap = HI_OMCI_ME_ONU2_SERVICE_MODEL_1_MP_MAP_FILTER;
		st_entry.us_concap = 0x7f;
	} else if (st_info.ui_product_mode == HI_OMCI_ME_ONU_CAPABILITY_TYPE_SFU_E) {
		if (st_info.ui_port_num == 1) {
			st_entry.us_concap = HI_OMCI_ME_ONU2_SERVICE_MODEL_1_MP_MAP_FILTER;
		} else {
			st_entry.us_concap = HI_OMCI_ME_ONU2_SERVICE_MODEL_N_MP_BR_MAP_FILTER;
		}
	}

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_ONT2_G_E, 0, &st_entry);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_ONT3_G_E, 0);
	HI_OMCI_RET_CHECK(i_ret);
	HI_OS_MEMSET_S(&st_onu3_entry, sizeof(st_onu3_entry), 0, sizeof(st_onu3_entry));
	st_onu3_entry.uc_cpu_usage = 0x64;
	st_onu3_entry.uc_memory_usage = 0x64;
	st_onu3_entry.ui_rate = 0x64;
	st_onu3_entry.uc_controycode = 0x96;
	HI_OS_STRCPY_S((hi_char8 *)st_onu3_entry.auc_onu_type, sizeof(st_onu3_entry.auc_onu_type),
		       (hi_char8 *)st_info.auc_equid);
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_ONT3_G_E, 0, &st_onu3_entry);
	HI_OMCI_RET_CHECK(i_ret);
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_onu2g_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
#ifdef HI_VERSION_DEBUG
	hi_int32 ret;
	hi_uint32 key_mode = HI_OMCI_ME_ONU2_SEC_MODE_AES128;
	hi_omci_me_onu2_g_s *pst_msg = (hi_omci_me_onu2_g_s *)pv_data;

	hi_omci_debug("sec_mode : %u \n", pst_msg->uc_secmode);

	switch (pst_msg->uc_secmode) {
	case HI_OMCI_ME_ONU2_SEC_MODE_AES256:
		key_mode = HI_GPON_KEY_MODE_AES256;
		break;
	case HI_OMCI_ME_ONU2_SEC_MODE_SM4:
		key_mode = HI_GPON_KEY_MODE_SM4;
		break;
	default:
		key_mode = HI_GPON_KEY_MODE_AES128;
		break;
	}

	ret = HI_IPC_CALL("hi_pon_core_set_key_mode", &key_mode);
	if (ret != HI_RET_SUCC)
		return ret;
#endif
	return HI_RET_SUCC;
}

/*Add by fyy for zte restore fac*/
static hi_void vs_omci_me_facotry_timer_handler(hi_void *pv_cookie)
{
    hi_int32 i_ret = HI_RET_SUCC;

    vs_omci_tapi_factory_reset();

    i_ret = vs_omci_timer_delete(VS_OMCI_TIMER_MASK_FACTORY);
    if (HI_RET_SUCC != i_ret)
    {
        PRT("vs_omci_timer_delete fail\n");
    }
}
hi_int32 vs_omci_me_onu3g_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_me_onu3_g_s *pst_entity = (hi_omci_me_onu3_g_s *)pv_data;
	hi_ushort16 us_instid, us_attrmask;
    hi_int32 i_ret = HI_RET_SUCC;
	
    us_instid   = pst_entity->st_msghead.us_instid;
    us_attrmask = pst_entity->st_msghead.us_attmask;

    hi_omci_debug("[INFO]:us_instid=0x%x, attrmask=0x%x\n", us_instid, us_attrmask);


    if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_attrmask, HI_OMCI_ATTR16))
    {
        if (pst_entity->uc_restore_factory) {
            i_ret = vs_omci_timer_create(vs_omci_me_facotry_timer_handler, VS_OMCI_TIMER_MASK_FACTORY, VS_OMCI_TIMER_TIMEOUT);
            if (HI_RET_SUCC != i_ret)
            {
                PRT("vs_omci_me_facotry_timer_handler create timer fail\n");
            }
        }
    }

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}