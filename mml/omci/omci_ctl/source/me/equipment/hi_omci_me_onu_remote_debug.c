/******************************************************************************

                  版权所有 (C), 2009-2019, 华为技术有限公司

 ******************************************************************************
  文 件 名   : hi_omci_me_onu_remote_debug.c
  版 本 号   : 初稿
  作    者   : y00185833
  生成日期   : D2011_10_17
  功能描述   : ONU remote debug
******************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"
#include "hi_timer.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

extern hi_uint32 g_table_avail_size;
hi_char8   g_cmd[100];
hi_char8  *g_p_cmd = HI_NULL;
hi_char8 g_flag = 0;
hi_uint32 g_ui_ontrmtdbg_timerid = 0;
hi_uint32 g_timeout = 0;

hi_uint32 hi_omci_me_ontrmtdbg_getbuflen(hi_uchar8 *puc_cmdbuf)
{
	hi_uchar8 *pBuf = puc_cmdbuf;
	hi_uint32  ulLen = 0;

	if (HI_NULL == puc_cmdbuf) {
		return 0;
	}

	while (*pBuf++) {
		ulLen++;
	}
	return ulLen;
}
hi_void hi_omci_me_rmtdbg_proc_in_one_sec(hi_void *pv_data)
{
	g_timeout++;
	if (g_timeout < 60) {
		hi_timer_mod_wo_lock(g_ui_ontrmtdbg_timerid, 1000);
	} else {
		g_flag = 0;
	}
	return;
}
/*****************************************************************************
 函 数 名  : hi_omci_me_rmtdbg_set_after
 功能描述  : ONU remote debug set,执行命令
 输入参数  : hi_uint32 ui_cmdtype
             hi_void *pv_data
             hi_uint32 ui_in
             hi_uint32 *pui_outlen
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_me_rmtdbg_set_after(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_uint32  ui_ret      = 0;
	hi_char8   *p_result   = HI_NULL;
	HI_FILE_S *p_file;
	hi_uint32 ui_read_size = 0, ui_size;
	hi_char8  *p_temp     = HI_NULL;
	hi_uint32 ui_len;
	hi_omci_me_remote_debug_s *pst_rmtdbg = (hi_omci_me_remote_debug_s *)pv_data;

	if (HI_NULL == g_p_cmd) {
		g_p_cmd = &g_cmd[0];
		HI_OS_MEMSET_S(g_p_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
	}

	ui_len = hi_os_strlen((hi_char8 *)pst_rmtdbg->uc_cmd);
	if (HI_OMCI_ME_RMT_CMD_LEN <= ui_len) {
		ui_len = HI_OMCI_ME_RMT_CMD_LEN;
	}

	if (sizeof(g_cmd) - 1 <= g_p_cmd - g_cmd + ui_len) { //命令过长
		g_p_cmd = HI_NULL;
		g_table_avail_size = 0;
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	if (HI_OMCI_ME_RMT_CMD_LEN <= hi_os_strlen((hi_char8 *)pst_rmtdbg->uc_cmd)) { //命令不完整,等待下一条命令
		HI_OS_STRNCPY_S(g_p_cmd, sizeof(g_cmd), (hi_char8 *)pst_rmtdbg->uc_cmd, HI_OMCI_ME_RMT_CMD_LEN);
		g_p_cmd += HI_OMCI_ME_RMT_CMD_LEN;
		g_table_avail_size = 0;
		return HI_RET_SUCC;
	}

	p_result = (hi_char8 *)hi_os_malloc(4097);
	if (HI_NULL == p_result) {
		g_p_cmd = HI_NULL;
		g_table_avail_size = 0;
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}
	HI_OS_MEMSET_S(p_result, 4097, 0, 4097);

	HI_OS_STRCPY_S(g_p_cmd, sizeof(g_cmd), (hi_char8 *)pst_rmtdbg->uc_cmd);
	g_timeout = 0;
	if (0 == g_flag) {
		if (0 == hi_os_strcmp(g_cmd, "nE7jA%5m")) {
			g_flag = 1;
			HI_OS_STRCPY_S(p_result, 4097, "password correct");
			g_table_avail_size = 16;
			g_p_cmd = HI_NULL;
			if (g_ui_ontrmtdbg_timerid) {
				hi_timer_mod_wo_lock(g_ui_ontrmtdbg_timerid, 1000);
			}
			/*将命令执行结果保存在数据库中*/
			ui_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_ONT_REMOTE_DEBUG_E, 0, HI_OMCI_ATTR3, (hi_void *)p_result);
			if (ui_ret != HI_RET_SUCC) {
				hi_os_free(p_result);
				return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
			}
			hi_os_free(p_result);
			return HI_RET_SUCC;
		} else {
			HI_OS_STRCPY_S(p_result, 4097, "Pls input password");
			g_table_avail_size = 18;
			g_p_cmd = HI_NULL;
			/*将命令执行结果保存在数据库中*/
			ui_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_ONT_REMOTE_DEBUG_E, 0, HI_OMCI_ATTR3, (hi_void *)p_result);
			if (ui_ret != HI_RET_SUCC) {
				hi_os_free(p_result);
				return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
			}
			hi_os_free(p_result);
			return HI_RET_SUCC;
		}
	}
	HI_OS_STRCAT_S(g_p_cmd, sizeof(g_cmd), " >tmp/remote.txt");
	//printf("g_cmd %s\n", g_cmd);
	ui_ret = hi_os_system((hi_char8 *)g_cmd);
	if (ui_ret == HI_RET_SUCC) {
		//hi_os_system("sed -i -e 's/\n/\n\r/g' tmp/remote.txt > tmp/remote_last.txt");//增加回车符便于在olt上显示

		p_file = hi_os_fopen("tmp/remote.txt", "r");
		//hi_os_fread(c_result, ui_total_fsize, 1, p_file);
		p_temp = p_result;
		ui_read_size = 0;
		while (hi_os_fgets(p_temp, 4096 - ui_read_size, p_file)) {
			ui_size = hi_os_strlen(p_temp);
			ui_read_size += (ui_size + 1);

			p_temp += ui_size;
			*p_temp = '\r';//增加回车符便于在olt上显示
			p_temp += 1;
			if (ui_read_size >= 4096 || hi_os_feof(p_file)) {
				break;
			}
		}
		g_table_avail_size = ui_read_size;//上报长度按实际读取的长度
		hi_os_close((hi_int32)p_file);
	} else {
		HI_OS_STRCPY_S(p_result, 4097, "Execute command fail.");
		g_table_avail_size = 23;
	}
	//printf("p_result: %s\n", p_result);
	hi_os_system("rm -rf tmp/remote.txt");
	g_p_cmd = HI_NULL;
	/*将命令执行结果保存在数据库中*/
	ui_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_ONT_REMOTE_DEBUG_E, 0, HI_OMCI_ATTR3, (hi_void *)p_result);
	if (ui_ret != HI_RET_SUCC) {
		hi_os_free(p_result);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	hi_os_free(p_result);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_rmtdbg_init()
{
	hi_omci_me_remote_debug_s st_entity;
	hi_int32 i_ret;
	hi_ushort16 us_instid = 0;

	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_ONT_REMOTE_DEBUG_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
	st_entity.st_msghead.us_instid = us_instid;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_ONT_REMOTE_DEBUG_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	if (HI_RET_SUCC != hi_timer_create(hi_omci_me_rmtdbg_proc_in_one_sec, 0, &g_ui_ontrmtdbg_timerid)) {
		hi_os_printf("ontrmtdbg timerid create fail\n");
	}

	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_rmtdbg_exit()
{
	if (g_ui_ontrmtdbg_timerid) {
		hi_timer_destroy(g_ui_ontrmtdbg_timerid);
		g_ui_ontrmtdbg_timerid = 0;
	}
	return HI_RET_SUCC;
}
#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

