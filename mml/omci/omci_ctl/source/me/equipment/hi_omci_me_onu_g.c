/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_onu_g.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-25
  Description: ONU-G
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"
#include "hi_ipc.h"
#include "hi_notifier.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
#define HI_OMCI_ME_ONUG_STATUS_CORRECT  0
#define HI_OMCI_ME_ONUG_STATUS_INVALID  1
#define HI_OMCI_ME_ONUG_TRAFFIC_MGT  2

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_onug_init
 Description : ONU-G init
 Input Parm  : 无
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_onug_init()
{
	hi_omci_me_onu_g_msg_s st_entry;
	hi_omci_tapi_sysinfo_s st_info;
	hi_omci_tapi_sn_loid_s st_sn_loid;
	hi_int32 i_ret;
	hi_uint32 ui_vendorid;

	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_ONT_G_E, 0);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_tapi_sysinfo_get(&st_info);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_tapi_sn_loid_get(&st_sn_loid);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_entry, sizeof(st_entry), 0, sizeof(st_entry));
	st_entry.st_msghead.us_instid = 0;

	ui_vendorid = htonl(st_info.ui_vendor_id);
	HI_OS_MEMCPY_S(st_entry.st_onug.uc_vendorid, sizeof(st_entry.st_onug.uc_vendorid), &ui_vendorid,
		       sizeof(st_entry.st_onug.uc_vendorid));
	HI_OS_MEMCPY_S(st_entry.st_onug.uc_version, sizeof(st_entry.st_onug.uc_version), st_info.auc_version,
		       sizeof(st_entry.st_onug.uc_version));
	HI_OS_MEMCPY_S(st_entry.st_onug.uc_sn, sizeof(st_entry.st_onug.uc_sn), st_sn_loid.auc_sn,
		       sizeof(st_entry.st_onug.uc_sn));
	HI_OS_MEMCPY_S(st_entry.st_onug.uc_loid, sizeof(st_entry.st_onug.uc_loid),
			st_sn_loid.auc_loid, sizeof(st_entry.st_onug.uc_loid));
	HI_OS_MEMCPY_S(st_entry.st_onug.uc_passwd, sizeof(st_entry.st_onug.uc_passwd),
			st_sn_loid.auc_lopwd, sizeof(st_entry.st_onug.uc_passwd));
	st_entry.st_onug.uc_trafficflag = HI_OMCI_ME_ONUG_TRAFFIC_MGT;
	st_entry.st_onug.uc_status = HI_OMCI_ME_ONUG_STATUS_CORRECT;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_ONT_G_E, 0, &st_entry);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/* *****************************************************************************
 Function    : hi_omci_me_onug_get
 Description : ONT-G
               数据库更新前处理
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_me_onug_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_onu_g_msg_s *pst_msg = (hi_omci_me_onu_g_msg_s *)pv_data;
	hi_omci_me_onu_g_msg_s st_entry = { 0 };
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_msg->st_msghead.us_instid;
	hi_omci_tapi_sn_loid_s st_sn_loid = { 0 };

	i_ret = hi_omci_tapi_sn_loid_get(&st_sn_loid);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_ONT_G_E, us_instid, &st_entry);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMCPY_S(st_entry.st_onug.uc_loid, sizeof(st_entry.st_onug.uc_loid),
			st_sn_loid.auc_loid, sizeof(st_entry.st_onug.uc_loid));
	HI_OS_MEMCPY_S(st_entry.st_onug.uc_passwd, sizeof(st_entry.st_onug.uc_passwd),
			st_sn_loid.auc_lopwd, sizeof(st_entry.st_onug.uc_passwd));

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_ONT_G_E, us_instid, &st_entry);
	HI_OMCI_RET_CHECK(i_ret);
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_onug_set
 Description : ONT-G
               在数据库操作后处理
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_onug_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_int32 i_ret;
	hi_uint32 ui_state;
	hi_omci_me_onu_g_msg_s *pst_msg = (hi_omci_me_onu_g_msg_s *)pv_data;
	hi_omci_notifier_data_s st_notifier_data = { 0 };

	hi_omci_debug("authstatus : %hhu \n", pst_msg->st_onug.uc_status);
	if (pst_msg->st_onug.uc_status > HI_OMCI_ME_ONUG_DUP_LOID) {
		hi_omci_systrace(HI_OMCI_PRO_ERR_PARA_ERR_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PARA_ERR_E;
	}

	st_notifier_data.em_type = HI_OMCI_NOTIFY_LOID_REGSTA_E;
	st_notifier_data.ui_data = pst_msg->st_onug.uc_status;
#ifdef CONFIG_PLATFORM_OPENWRT
    HI_IPC_CALL("hi_sml_owal_led_gpon_proc", &st_notifier_data);
#else
	i_ret = hi_notifier_call(HI_OMCI_NOTIFIY_NAME, &st_notifier_data);
	HI_OMCI_RET_CHECK(i_ret);
#endif
	ui_state = (pst_msg->st_onug.uc_status <= HI_OMCI_ME_ONUG_AUTH_SUCC ? HI_FALSE : pst_msg->st_onug.uc_status);
	i_ret = HI_IPC_CALL("hi_pon_set_fail_regstate", &ui_state);
	if (HI_RET_SUCC != i_ret)
		return i_ret;
	return HI_RET_SUCC;
}
