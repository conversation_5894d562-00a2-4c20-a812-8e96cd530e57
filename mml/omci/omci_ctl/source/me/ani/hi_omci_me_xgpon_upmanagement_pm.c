/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_xgpon_upmanagement_pm.c
  Version    : 初稿
  Author     :
  Creation   :
  Description: XG-PON upstream management performance monitoring history data
******************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */


/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
static hi_omci_tapi_stat_xgponupmng_pm_s gst_xgponupmng_pm_history;
static hi_omci_tapi_stat_xgponupmng_pm_s gst_xgponupmng_pm_prev_history;
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_xgponupmngpm_stat_get
 Description :
 Input Parm  :
 Output Parm :
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_xgponupmngpm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_stat_timer_s *pst_timer = (hi_omci_me_stat_timer_s *)pv_data;
	hi_omci_tapi_stat_xgponupmng_pm_s st_upmngpm_curr;
	hi_omci_me_xgponupmng_pm_s st_entity;
	hi_int32 i_ret;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->ui_instid;

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));

	/* 读取芯片当前统计 */
	i_ret = hi_omci_tapi_stat_upmng_get(&st_upmngpm_curr);
	HI_OMCI_RET_CHECK(i_ret);

	/* 与历史数据相减，将结构保存到数据库 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_XGPON_UPMNG_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	st_entity.uc_endtime++;
	st_entity.ui_upsploammesgcnt = (st_upmngpm_curr.ui_upsploammesgcnt > gst_xgponupmng_pm_history.ui_upsploammesgcnt) ?
				       (st_upmngpm_curr.ui_upsploammesgcnt - gst_xgponupmng_pm_history.ui_upsploammesgcnt)
				       : 0;
	st_entity.ui_snonumesgcnt = (st_upmngpm_curr.ui_snonumesgcnt > gst_xgponupmng_pm_history.ui_snonumesgcnt) ?
				    (st_upmngpm_curr.ui_snonumesgcnt - gst_xgponupmng_pm_history.ui_snonumesgcnt) : 0;
	st_entity.ui_regmemesgcnt = (st_upmngpm_curr.ui_regmemesgcnt > gst_xgponupmng_pm_history.ui_regmemesgcnt) ?
				    (st_upmngpm_curr.ui_regmemesgcnt - gst_xgponupmng_pm_history.ui_regmemesgcnt) : 0;
	st_entity.ui_keyrepmesgcnt = (st_upmngpm_curr.ui_keyrepmesgcnt > gst_xgponupmng_pm_history.ui_keyrepmesgcnt) ?
				     (st_upmngpm_curr.ui_keyrepmesgcnt - gst_xgponupmng_pm_history.ui_keyrepmesgcnt) : 0;
	st_entity.ui_ackmesgcnt = (st_upmngpm_curr.ui_ackmesgcnt > gst_xgponupmng_pm_history.ui_ackmesgcnt) ?
				  (st_upmngpm_curr.ui_ackmesgcnt - gst_xgponupmng_pm_history.ui_ackmesgcnt) : 0;
	st_entity.ui_sleepreqmesgcnt = (st_upmngpm_curr.ui_sleepreqmesgcnt > gst_xgponupmng_pm_history.ui_sleepreqmesgcnt) ?
				       (st_upmngpm_curr.ui_sleepreqmesgcnt - gst_xgponupmng_pm_history.ui_sleepreqmesgcnt)
				       : 0;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_XGPON_UPMNG_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	/* 将当前统计更新到历史统计 */
	HI_OS_MEMCPY_S(&gst_xgponupmng_pm_prev_history, sizeof(gst_xgponupmng_pm_prev_history), &gst_xgponupmng_pm_history,
		       sizeof(hi_omci_tapi_stat_xgponupmng_pm_s));
	HI_OS_MEMCPY_S(&gst_xgponupmng_pm_history, sizeof(gst_xgponupmng_pm_history), &st_upmngpm_curr,
		       sizeof(hi_omci_tapi_stat_xgponupmng_pm_s));

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_xgponupmngpm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_xgponupmng_pm_s *pst_timer = (hi_omci_me_xgponupmng_pm_s *)pv_data;
	hi_omci_me_xgponupmng_pm_s st_entity;
	hi_int32 i_ret;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->st_msghead.us_instid;;

	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_XGPON_UPMNG_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	st_entity.ui_upsploammesgcnt = (gst_xgponupmng_pm_history.ui_upsploammesgcnt >
					gst_xgponupmng_pm_prev_history.ui_upsploammesgcnt) ? (gst_xgponupmng_pm_history.ui_upsploammesgcnt -
							gst_xgponupmng_pm_prev_history.ui_upsploammesgcnt) : 0;
	st_entity.ui_snonumesgcnt = (gst_xgponupmng_pm_history.ui_snonumesgcnt > gst_xgponupmng_pm_prev_history.ui_snonumesgcnt)
				    ? (gst_xgponupmng_pm_history.ui_snonumesgcnt -
				       gst_xgponupmng_pm_prev_history.ui_snonumesgcnt) : 0;
	st_entity.ui_regmemesgcnt = (gst_xgponupmng_pm_history.ui_regmemesgcnt > gst_xgponupmng_pm_prev_history.ui_regmemesgcnt)
				    ? (gst_xgponupmng_pm_history.ui_regmemesgcnt -
				       gst_xgponupmng_pm_prev_history.ui_regmemesgcnt) : 0;
	st_entity.ui_keyrepmesgcnt = (gst_xgponupmng_pm_history.ui_keyrepmesgcnt >
				      gst_xgponupmng_pm_prev_history.ui_keyrepmesgcnt) ? (gst_xgponupmng_pm_history.ui_keyrepmesgcnt -
						      gst_xgponupmng_pm_prev_history.ui_keyrepmesgcnt) : 0;
	st_entity.ui_ackmesgcnt = (gst_xgponupmng_pm_history.ui_ackmesgcnt > gst_xgponupmng_pm_prev_history.ui_ackmesgcnt) ?
				  (gst_xgponupmng_pm_history.ui_ackmesgcnt -
				   gst_xgponupmng_pm_prev_history.ui_ackmesgcnt) : 0;
	st_entity.ui_sleepreqmesgcnt = (gst_xgponupmng_pm_history.ui_sleepreqmesgcnt >
					gst_xgponupmng_pm_prev_history.ui_sleepreqmesgcnt) ? (gst_xgponupmng_pm_history.ui_sleepreqmesgcnt -
							gst_xgponupmng_pm_prev_history.ui_sleepreqmesgcnt) : 0;
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_XGPON_UPMNG_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_xgponupmngpm_getcurrent
 Description :
 Input Parm  :
 Output Parm :
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_xgponupmngpm_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_xgponupmng_pm_s *pst_timer = (hi_omci_me_xgponupmng_pm_s *)pv_data;
	hi_omci_tapi_stat_xgponupmng_pm_s st_upmngpm_curr;
	hi_omci_me_xgponupmng_pm_s st_entity;
	hi_int32 i_ret;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->st_msghead.us_instid;

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));

	/* 读取芯片当前统计 */
	i_ret = hi_omci_tapi_stat_upmng_get(&st_upmngpm_curr);
	HI_OMCI_RET_CHECK(i_ret);

	/* 与历史数据相减*/
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_XGPON_UPMNG_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	st_entity.uc_endtime++;
	st_entity.ui_upsploammesgcnt = (st_upmngpm_curr.ui_upsploammesgcnt > gst_xgponupmng_pm_history.ui_upsploammesgcnt) ?
				       (st_upmngpm_curr.ui_upsploammesgcnt - gst_xgponupmng_pm_history.ui_upsploammesgcnt)
				       : 0;
	st_entity.ui_snonumesgcnt = (st_upmngpm_curr.ui_snonumesgcnt > gst_xgponupmng_pm_history.ui_snonumesgcnt) ?
				    (st_upmngpm_curr.ui_snonumesgcnt - gst_xgponupmng_pm_history.ui_snonumesgcnt) : 0;
	st_entity.ui_regmemesgcnt = (st_upmngpm_curr.ui_regmemesgcnt > gst_xgponupmng_pm_history.ui_regmemesgcnt) ?
				    (st_upmngpm_curr.ui_regmemesgcnt - gst_xgponupmng_pm_history.ui_regmemesgcnt) : 0;
	st_entity.ui_keyrepmesgcnt = (st_upmngpm_curr.ui_keyrepmesgcnt > gst_xgponupmng_pm_history.ui_keyrepmesgcnt) ?
				     (st_upmngpm_curr.ui_keyrepmesgcnt - gst_xgponupmng_pm_history.ui_keyrepmesgcnt) : 0;
	st_entity.ui_ackmesgcnt = (st_upmngpm_curr.ui_ackmesgcnt > gst_xgponupmng_pm_history.ui_ackmesgcnt) ?
				  (st_upmngpm_curr.ui_ackmesgcnt - gst_xgponupmng_pm_history.ui_ackmesgcnt) : 0;
	st_entity.ui_sleepreqmesgcnt = (st_upmngpm_curr.ui_sleepreqmesgcnt > gst_xgponupmng_pm_history.ui_sleepreqmesgcnt) ?
				       (st_upmngpm_curr.ui_sleepreqmesgcnt - gst_xgponupmng_pm_history.ui_sleepreqmesgcnt)
				       : 0;
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_XGPON_UPMNG_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_xgponupmngpm_create
 Description :
 Input Parm  :
 Output Parm :
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_xgponupmngpm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_xgponupmng_pm_s *pst_entry = (hi_omci_me_xgponupmng_pm_s *)pv_data;
	hi_omci_me_stat_timer_s st_stat_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	HI_OS_MEMSET_S(&gst_xgponupmng_pm_history, sizeof(gst_xgponupmng_pm_history), 0,
		       sizeof(hi_omci_tapi_stat_xgponupmng_pm_s));
	HI_OS_MEMSET_S(&gst_xgponupmng_pm_prev_history, sizeof(gst_xgponupmng_pm_prev_history), 0,
		       sizeof(hi_omci_tapi_stat_xgponupmng_pm_s));

	/* 创建15分钟定时器，获取统计 */
	HI_OS_MEMSET_S(&st_stat_timer, sizeof(st_stat_timer), 0, sizeof(hi_omci_me_stat_timer_s));
	st_stat_timer.ui_meid = HI_OMCI_PRO_ME_XGPON_UPMNG_PM_E;
	st_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_start(&st_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_xgponupmng_pm_delete
 Description :
 Input Parm  :
 Output Parm :
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_xgponupmngpm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_xgponupmng_pm_s *pst_entry = (hi_omci_me_xgponupmng_pm_s *)pv_data;
	hi_omci_me_stat_timer_s st_stat_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	/* 删除定时器 */
	HI_OS_MEMSET_S(&st_stat_timer, sizeof(st_stat_timer), 0, sizeof(hi_omci_me_stat_timer_s));
	st_stat_timer.ui_meid = HI_OMCI_PRO_ME_XGPON_UPMNG_PM_E;
	st_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_stop(&st_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

