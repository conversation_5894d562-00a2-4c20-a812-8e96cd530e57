/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_ani_g.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-16
  Description: ANI-G
******************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"

#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

#include <math.h>

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
#define HI_OMCI_ME_ANIG_PIGGYBACK_MODE0     0
#define HI_OMCI_ME_ANIG_PIGGYBACK_MODE1     1
#define HI_OMCI_ME_ANIG_PIGGYBACK_IGNORE    4

#define HI_OMCI_ME_ANIG_POWER_THRSH(dBm) ((hi_uchar8)((dBm) * 2))   /*单位是0.5dbm*/

//#define HI_OMCI_ME_ANIG_MWPOWER_THRSH(mw) HI_OMCI_ME_ANIG_POWER_THRSH((10 * log10(mw)))

#define HI_OMCI_ME_ANIG_MWPOWER_THRSH(mw) ((10 * log10(mw)) * 2)

/* Valid values are -127dBm (coded as 254) to 0 dBm (coded as 0) in 0.5 dB increments. */
#define HI_OMCI_ME_ANIG_RXPOWER_LOW_THRSH   HI_OMCI_ME_ANIG_POWER_THRSH(28)  /* -28dBm */
#define HI_OMCI_ME_ANIG_RXPOWER_UPPER_THRSH HI_OMCI_ME_ANIG_POWER_THRSH(8)  /* -8dBm */
#define HI_OMCI_ME_ANIG_TXPOWER_LOW_THRSH   HI_OMCI_ME_ANIG_POWER_THRSH(0)  /* 0dBm */
#define HI_OMCI_ME_ANIG_TXPOWER_UPPER_THRSH HI_OMCI_ME_ANIG_POWER_THRSH(5)  /* 5dBm */

#define HI_OMCI_ME_ANIG_POWER_DEFAULT_THRSH HI_OMCI_ME_ANIG_POWER_THRSH(40)  /* -40dBm */

/* The default value 0xFF selects the ONU.s internal policy */
#define HI_OMCI_ANIG_RXPOWER_DEFAULT_THRSH  0xff

/* The default value -63.5 (0x81) selects the ONU.s internal policy */
#define HI_OMCI_ANIG_TXPOWER_DEFAULT_THRSH  0x81

/* Its value is a 2s complement integer referred to 1 mW (ie dBm), with 0.002 dB granularity */
#define HI_OMCI_ANIG_OPTICAL_LEVEL(mw) ((hi_ushort16)(10 * log10(mw * 1000) * 500))

/* V, 2s complement, 20 mV resolution */
#define HI_OMCI_ANIG_VOL(vol) ((hi_short16)((vol) * 50))

/* laser bias current Unsigned integer, 2 μA resolution */
#define HI_OMCI_ANIG_IBIAS(ibias) ((hi_short16)((ibias) * 500))

/* temperature, degrees 2s complement, 1/256 degree C resolution*/
#define HI_OMCI_ANIG_TEMPETURE(temp) ((hi_short16)((temp) * 256))

#define HI_OMCI_ME_ANIG_IS_GEMBLOCK(mask) HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR3)

#define HI_OMCI_ME_ANIG_ALARM_LOW_RXPOWER   HI_OMCI_ME_ALARM_BITMAP(0)
#define HI_OMCI_ME_ANIG_ALARM_HIGH_RXPOWER  HI_OMCI_ME_ALARM_BITMAP(1)
#define HI_OMCI_ME_ANIG_ALARM_SF            HI_OMCI_ME_ALARM_BITMAP(2)
#define HI_OMCI_ME_ANIG_ALARM_SD            HI_OMCI_ME_ALARM_BITMAP(3)
#define HI_OMCI_ME_ANIG_ALARM_LOW_TXPOWER   HI_OMCI_ME_ALARM_BITMAP(4)
#define HI_OMCI_ME_ANIG_ALARM_HIGH_TXPOWER  HI_OMCI_ME_ALARM_BITMAP(5)
//#define HI_OMCI_ME_ANIG_ALARM_BIAS_CURRENT  HI_OMCI_ME_ALARM_BITMAP(6)

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
static hi_uint32 gui_tstrslt_proc = HI_DISABLE;
hi_ulong64 gui_sd_alarm_count = 0;
hi_ulong64 gui_sf_alarm_count = 0;
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
static uint16_t hi_omci_optical_dbm_level(float mw)
{
    return (uint16_t)(0xa * log10(mw) * 0x1f4);
}
/******************************************************************************
 Function    : hi_omci_me_anig_alarm_get
 Description : ANI-G光器件告警检查
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_anig_alarm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_alarm_timer_s *pst_timer = (hi_omci_me_alarm_timer_s *)pv_data;
	hi_omci_tapi_optical_attr_s st_opt_attr;
	hi_omci_tapi_gpon_alarm_s st_gpon_alarm;
	hi_omci_me_ani_g_msg_s st_entry;
	hi_omci_me_ani_g_tst_rslt_msg_s st_tst_rslt;
	hi_int32 i_ret;
	hi_ushort16 us_bitmap_curr = 0;
	hi_ushort16 us_bitmap_hist;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->ui_instid;
	hi_ushort16 us_opt_level;
	hi_ushort16 us_tmp = 0;
	//hi_char8 c_rxpower;
	//hi_char8 c_txpower;
	float  f_rxpower;
	float  f_txpower;
	hi_uchar8 uc_loweropt_tmp;
	hi_uchar8 uc_upperopt_tmp;
	signed char uc_lowertx_tmp;
	signed char uc_uppertx_tmp;

	/* 读取光器件参数 */
	HI_OS_MEMSET_S(&st_opt_attr, sizeof(hi_omci_tapi_optical_attr_s), 0, sizeof(hi_omci_tapi_optical_attr_s));
	i_ret = hi_omci_tapi_optical_attr_get(HI_DISABLE, &st_opt_attr);
	if (HI_RET_STOP == i_ret) {
		return HI_RET_SUCC;
	}
	/* Bohannon: for bug#00019656 
	when ui_rxpower=ui_txpower=0, it means some thing wrong with i2c while reading bob DDM */
	if(st_opt_attr.ui_rxpower == 0 && st_opt_attr.ui_txpower == 0)
	{
		return HI_RET_SUCC;
	}
	f_rxpower = (float)fabs(HI_OMCI_ME_ANIG_MWPOWER_THRSH(st_opt_attr.ui_rxpower));
	f_txpower = (float)fabs(HI_OMCI_ME_ANIG_MWPOWER_THRSH(st_opt_attr.ui_txpower));


	/* 获取各参数阈值 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_ANI_G_E, us_instid, &st_entry);
	HI_OMCI_RET_CHECK(i_ret);


	if (HI_OMCI_ANIG_RXPOWER_DEFAULT_THRSH == st_entry.st_anig.uc_loweropt) {
		uc_loweropt_tmp = HI_OMCI_ME_ANIG_RXPOWER_LOW_THRSH;
	} else {
		uc_loweropt_tmp = st_entry.st_anig.uc_loweropt;
	}

	if (HI_OMCI_ANIG_RXPOWER_DEFAULT_THRSH == st_entry.st_anig.uc_upperopt) {
		uc_upperopt_tmp = HI_OMCI_ME_ANIG_RXPOWER_UPPER_THRSH;
	} else {
		uc_upperopt_tmp = st_entry.st_anig.uc_upperopt;
	}

	if (HI_OMCI_ANIG_TXPOWER_DEFAULT_THRSH == st_entry.st_anig.uc_lowertx) {
		uc_lowertx_tmp = HI_OMCI_ME_ANIG_TXPOWER_LOW_THRSH;
	} else {
		uc_lowertx_tmp = (signed char)st_entry.st_anig.uc_lowertx;
	}

	if (HI_OMCI_ANIG_TXPOWER_DEFAULT_THRSH == st_entry.st_anig.uc_uppertx) {
		uc_uppertx_tmp = HI_OMCI_ME_ANIG_TXPOWER_UPPER_THRSH;
	} else {
		uc_uppertx_tmp = (signed char)st_entry.st_anig.uc_uppertx;
	}


	/* 比较是否超出阈值，确定是告警产生，还是撤销 */
	if (f_rxpower > uc_loweropt_tmp) { /**绝对值大于低接收门限，表示实际值小于低接收门限*/
		us_bitmap_curr |= HI_OMCI_ME_ANIG_ALARM_LOW_RXPOWER;
	} else if (f_rxpower < uc_upperopt_tmp) { /**绝对值小于高接收门限，表示实际值大于高接收门限*/
		us_bitmap_curr |= HI_OMCI_ME_ANIG_ALARM_HIGH_RXPOWER;
	}
	if ((f_txpower < uc_lowertx_tmp) && (f_txpower != HI_OMCI_ME_ANIG_POWER_DEFAULT_THRSH)) {
		us_bitmap_curr |= HI_OMCI_ME_ANIG_ALARM_LOW_TXPOWER;
	} else if ((f_txpower > uc_uppertx_tmp) && (f_txpower != HI_OMCI_ME_ANIG_POWER_DEFAULT_THRSH)) {
		us_bitmap_curr |= HI_OMCI_ME_ANIG_ALARM_HIGH_TXPOWER;
	}

	/* BIAS偏执电流告警需要确认告警阈值再做 */

	//还需要再初始化时开启这两个告警的使能，以及考虑要不要在这个文件里面初始化gui_sd_alarm_count和gui_sf_alarm_count的值
	i_ret = hi_omci_tapi_gpon_alarm_get(&st_gpon_alarm);
	if (gui_sd_alarm_count < st_gpon_alarm.ui_sd_alarm_count) {
		us_bitmap_curr |= HI_OMCI_ME_ANIG_ALARM_SD;
	}
	if (gui_sf_alarm_count < st_gpon_alarm.ui_sf_alarm_count) {
		us_bitmap_curr |= HI_OMCI_ME_ANIG_ALARM_SF;
	}
	gui_sd_alarm_count = st_gpon_alarm.ui_sd_alarm_count;
	gui_sf_alarm_count = st_gpon_alarm.ui_sf_alarm_count;

	/* 更新数据库的收光功率和发光功率 */
	us_opt_level = hi_omci_optical_dbm_level(st_opt_attr.ui_rxpower);
	if (us_opt_level != st_entry.st_anig.us_optlevel) {
		i_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_ANI_G_E, us_instid, HI_OMCI_ATTR10, &us_opt_level);
		HI_OMCI_RET_CHECK(i_ret);
	}

	us_opt_level = hi_omci_optical_dbm_level(st_opt_attr.ui_txpower);
	if (us_opt_level != st_entry.st_anig.us_txoptlevel) {
		i_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_ANI_G_E, us_instid, HI_OMCI_ATTR14, &us_opt_level);
		HI_OMCI_RET_CHECK(i_ret);
	}

	/* 读取数据库当前告警状态 */
	i_ret = hi_omci_sql_alarm_inst_get(HI_OMCI_PRO_ME_ANI_G_E, us_instid, &us_bitmap_hist);
	HI_OMCI_RET_CHECK(i_ret);


	/* 如果告警状态发生变化，更新数据库的告警状态 */
	if (us_bitmap_curr != us_bitmap_hist) {
		i_ret = hi_omci_sql_alarm_inst_set(HI_OMCI_PRO_ME_ANI_G_E, us_instid, us_bitmap_curr);
		HI_OMCI_RET_CHECK(i_ret);
		/* 获取实例的ARC标识 */
		/* 如果当前不禁止告警上报则发生告警消息 */
		if (HI_FALSE == st_entry.st_anig.uc_arc) {
			i_ret = hi_omci_proc_sendalarm(HI_OMCI_PRO_ME_ANI_G_E, us_instid, us_bitmap_curr);
			HI_OMCI_RET_CHECK(i_ret);
		}
	}

	/* 利用告警定时器延后发送test result消息 */
	if (HI_ENABLE == gui_tstrslt_proc) {
		/* 发送Test Result消息 */
		st_tst_rslt.st_item[0].uc_type = 1;
		st_tst_rslt.st_item[0].us_value = (hi_ushort16)htons(HI_OMCI_ANIG_VOL(st_opt_attr.ui_voltage));
		st_tst_rslt.st_item[1].uc_type = 3;
		us_tmp = HI_OMCI_ANIG_OPTICAL_LEVEL(st_opt_attr.ui_rxpower);
		st_tst_rslt.st_item[1].us_value = (hi_ushort16)htons(us_tmp);

		st_tst_rslt.st_item[2].uc_type = 5;
		us_tmp = HI_OMCI_ANIG_OPTICAL_LEVEL(st_opt_attr.ui_txpower);
		st_tst_rslt.st_item[2].us_value = (hi_ushort16)htons(us_tmp);
		st_tst_rslt.st_item[3].uc_type = 9;
		st_tst_rslt.st_item[3].us_value = (hi_ushort16)htons(HI_OMCI_ANIG_IBIAS(st_opt_attr.ui_ibias));
		st_tst_rslt.st_item[4].uc_type = 12;
		st_tst_rslt.st_item[4].us_value = (hi_ushort16)htons(HI_OMCI_ANIG_TEMPETURE(st_opt_attr.ui_temprature));
		st_tst_rslt.us_vendor_spec = 0;
		i_ret = hi_omci_proc_sendtestresult(HI_OMCI_PRO_ME_ANI_G_E, us_instid, (hi_void *)&st_tst_rslt);
		HI_OMCI_RET_CHECK(i_ret);

		gui_tstrslt_proc = HI_DISABLE;
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_anig_test
 Description : ANI-G光器件告警检查
               数据库操作后处理
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_anig_test(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	gui_tstrslt_proc = HI_ENABLE;
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_anig_set
 Description : ANI-G set
               数据库操作后处理
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_anig_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_ani_g_msg_s *pst_entry = (hi_omci_me_ani_g_msg_s *)pv_data;
	hi_int32 i_ret;
	hi_ushort16 us_instid;
	hi_ushort16 us_bitmap_curr = 0;
	hi_ushort16 us_bitmap_hist;
	hi_ushort16 us_mask = pst_entry->st_msghead.us_attmask;

	us_instid = pst_entry->st_msghead.us_instid;
	if (HI_OMCI_ME_ANIG_IS_GEMBLOCK(us_mask)) {
		i_ret = hi_omci_tapi_gem_block_set(pst_entry->st_anig.us_gemblocklen);
		HI_OMCI_RET_CHECK(i_ret);
	}

	i_ret = hi_omci_sql_alarm_inst_get(HI_OMCI_PRO_ME_ANI_G_E, us_instid, &us_bitmap_curr);
	HI_OMCI_RET_CHECK(i_ret);

	if (HI_OMCI_ANIG_RXPOWER_DEFAULT_THRSH == pst_entry->st_anig.uc_loweropt) {
		us_bitmap_curr &= 0x7fff;
	}


	if (HI_OMCI_ANIG_RXPOWER_DEFAULT_THRSH == pst_entry->st_anig.uc_upperopt) {
		us_bitmap_curr &= 0xbfff;
	}


	if (HI_OMCI_ANIG_TXPOWER_DEFAULT_THRSH == pst_entry->st_anig.uc_lowertx) {
		us_bitmap_curr &= 0xf7ff;
	}


	if (HI_OMCI_ANIG_TXPOWER_DEFAULT_THRSH == pst_entry->st_anig.uc_uppertx) {
		us_bitmap_curr &= 0xfbff;
	}

	/* 读取数据库当前告警状态 */
	i_ret = hi_omci_sql_alarm_inst_get(HI_OMCI_PRO_ME_ANI_G_E, us_instid, &us_bitmap_hist);
	HI_OMCI_RET_CHECK(i_ret);

	if (us_bitmap_curr != us_bitmap_hist) {
		i_ret = hi_omci_sql_alarm_inst_set(HI_OMCI_PRO_ME_ANI_G_E, us_instid, us_bitmap_curr);
		HI_OMCI_RET_CHECK(i_ret);

	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_anig_get
 Description :
 Input Parm  : *pv_data:
             : ui_inlen:
             : :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_anig_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_ani_g_msg_s *pst_entiry = (hi_omci_me_ani_g_msg_s *)pv_data;
	hi_omci_me_ani_g_msg_s st_entity;
	hi_int32 i_ret;
	//hi_ushort16 us_mask = pst_entiry->st_msghead.us_attmask;
	hi_ushort16 us_instid  = pst_entiry->st_msghead.us_instid;
	hi_uint32 ui_gemblocklen;

	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_ANI_G_E, us_instid, &st_entity);
	if (HI_RET_SUCC != i_ret) {
		hi_omci_systrace(i_ret, 0, 0, 0, 0);
		return i_ret;
	}

	i_ret = hi_omci_tapi_gem_block_get(&ui_gemblocklen);
	HI_OMCI_RET_CHECK(i_ret);

	st_entity.st_anig.us_gemblocklen = (hi_ushort16)ui_gemblocklen;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_ANI_G_E, us_instid, &st_entity);
	if (HI_RET_SUCC != i_ret) {
		hi_omci_systrace(i_ret, 0, 0, 0, 0);
		return i_ret;
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_anig_create
 Description : ANI-G create
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_anig_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_ani_g_msg_s *pst_entry = (hi_omci_me_ani_g_msg_s *)pv_data;
	hi_omci_me_alarm_timer_s st_anig_alarm_timer;
	hi_omci_tapi_dba_rpt_mode_e em_dbamode;
	hi_omci_tapi_optical_attr_s st_opt_attr;
	hi_omci_tapi_gpon_alarm_s st_gpon_alarm;
	hi_int32 i_ret;
	hi_uint32 ui_tcontnum;
	hi_uint32 ui_gemblock;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	i_ret = hi_omci_tapi_tcont_num_get(&ui_tcontnum);
	HI_OMCI_RET_CHECK(i_ret);
	pst_entry->st_anig.us_tcontnum = (hi_ushort16)(ui_tcontnum - 1); //tcont0不上报

	i_ret = hi_omci_tapi_gem_block_get(&ui_gemblock);
	HI_OMCI_RET_CHECK(i_ret);
	pst_entry->st_anig.us_gemblocklen = (hi_ushort16)ui_gemblock;

	pst_entry->st_anig.us_onursptime = 35829;   /* 经验值 */

	i_ret = hi_omci_tapi_dba_mode_get(&em_dbamode);
	HI_OMCI_RET_CHECK(i_ret);

	switch (em_dbamode) {
	case HI_OMCI_TAPI_DBA_RPT_NSR:
		pst_entry->st_anig.uc_sr = HI_FALSE;
		pst_entry->st_anig.uc_piggyback = HI_OMCI_ME_ANIG_PIGGYBACK_IGNORE;
		break;
	case HI_OMCI_TAPI_DBA_RPT_SR_MODE0:
		pst_entry->st_anig.uc_sr = HI_TRUE;
		pst_entry->st_anig.uc_piggyback = HI_OMCI_ME_ANIG_PIGGYBACK_MODE0;
		break;
	case HI_OMCI_TAPI_DBA_RPT_SR_MODE0_1:
	case HI_OMCI_TAPI_DBA_RPT_SR_MODE0_1_2:
		pst_entry->st_anig.uc_sr = HI_TRUE;
		pst_entry->st_anig.uc_piggyback = HI_OMCI_ME_ANIG_PIGGYBACK_MODE1;
		break;
	default:
		break;
	}

	HI_OS_MEMSET_S(&st_opt_attr, sizeof(hi_omci_tapi_optical_attr_s), 0, sizeof(hi_omci_tapi_optical_attr_s));

	i_ret = hi_omci_tapi_optical_attr_get(HI_ENABLE, &st_opt_attr);
	HI_OMCI_RET_CHECK(i_ret);

	pst_entry->st_anig.us_optlevel = hi_omci_optical_dbm_level(st_opt_attr.ui_rxpower);
	pst_entry->st_anig.us_txoptlevel = hi_omci_optical_dbm_level(st_opt_attr.ui_txpower);
	pst_entry->st_anig.uc_sf = 5;
	pst_entry->st_anig.uc_sd = 9;
	pst_entry->st_anig.uc_loweropt = HI_OMCI_ME_ANIG_RXPOWER_LOW_THRSH;
	pst_entry->st_anig.uc_upperopt = HI_OMCI_ME_ANIG_RXPOWER_UPPER_THRSH;
	pst_entry->st_anig.uc_lowertx = HI_OMCI_ME_ANIG_TXPOWER_LOW_THRSH;
	pst_entry->st_anig.uc_uppertx = HI_OMCI_ME_ANIG_TXPOWER_UPPER_THRSH;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_ANI_G_E, us_instid, pst_entry);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_tapi_gpon_alarm_get(&st_gpon_alarm);
	gui_sd_alarm_count = st_gpon_alarm.ui_sd_alarm_count;
	gui_sf_alarm_count = st_gpon_alarm.ui_sf_alarm_count;

	/* 创建ANI-G告警实体 */
	i_ret = hi_omci_sql_alarm_inst_create(HI_OMCI_PRO_ME_ANI_G_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	/* 创建定时器，上报告警 */
	HI_OS_MEMSET_S(&st_anig_alarm_timer, sizeof(st_anig_alarm_timer), 0, sizeof(st_anig_alarm_timer));
	st_anig_alarm_timer.ui_meid = HI_OMCI_PRO_ME_ANI_G_E;
	st_anig_alarm_timer.ui_instid = us_instid;
	st_anig_alarm_timer.ui_timeout = 1000;      /*单位ms*/
	st_anig_alarm_timer.ui_local = HI_ENABLE;
	i_ret = hi_omci_me_alarm_start(&st_anig_alarm_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_anig_destory
 Description : ANI-G destory
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_anig_destory(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_ani_g_msg_s *pst_entry = (hi_omci_me_ani_g_msg_s *)pv_data;
	hi_omci_me_alarm_timer_s st_anig_alarm_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	/* 删除ANI-G告警实体 */
	i_ret = hi_omci_sql_alarm_inst_delete(HI_OMCI_PRO_ME_ANI_G_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_anig_alarm_timer, sizeof(st_anig_alarm_timer), 0, sizeof(st_anig_alarm_timer));
	st_anig_alarm_timer.ui_meid = HI_OMCI_PRO_ME_ANI_G_E;
	st_anig_alarm_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_alarm_stop(&st_anig_alarm_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_anig_init
 Description : ANI-G init
 Input Parm  : 无
 Output Parm : 无
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_anig_init()
{
	hi_omci_me_ani_g_msg_s st_entry;
	hi_int32 i_ret;
	hi_uint32 ui_outlen;
	hi_ushort16 us_instid = 0x8001;

	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_ANI_G_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_entry, sizeof(st_entry), 0, sizeof(st_entry));
	st_entry.st_msghead.us_instid = us_instid;

	i_ret = hi_omci_me_anig_create(&st_entry, sizeof(st_entry), &ui_outlen);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_anig_exit
 Description : ANI-G exit
 Input Parm  : 无
 Output Parm : 无
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_anig_exit()
{
	hi_omci_me_ani_g_msg_s st_entry;
	hi_int32 i_ret;
	hi_uint32 ui_outlen;
	hi_ushort16 us_instid = 0x8001;

	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_ANI_G_E, us_instid, &st_entry);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_me_anig_destory(&st_entry, sizeof(st_entry), &ui_outlen);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

