/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_gem_iwtp.c
  Version    : 初稿
  Author     : owen
  Creation   : 2014-1-9
  Description: GEM Interworking TP
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"

#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/

/* GAL loopback configuration */
#define HI_OMCI_ME_GEMIWTP_IS_LOOPBACK(mask) HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR8)

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_gem_iwtp_set
 Description : 配置GEMPORT属性，这里只实现环回
               数据库刷新后执行此函数
 Input Parm  : hi_void *pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_gem_iwtp_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_int32 i_ret;
	hi_omci_me_gem_ctp_s st_gemctp;
	hi_omci_me_gem_iwtp_s *pst_entry = (hi_omci_me_gem_iwtp_s *)pv_data;
	hi_ushort16 us_mask = pst_entry->st_msghead.us_attmask;
	hi_ushort16 us_tcont = 0;

	HI_OS_MEMSET_S(&st_gemctp, sizeof(st_gemctp), 0, sizeof(st_gemctp));
	if (HI_OMCI_ME_GEMIWTP_IS_LOOPBACK(us_mask)) {
		i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_GEM_CTP_E, pst_entry->us_gemctp_ptr, &st_gemctp);
		HI_OMCI_RET_CHECK(i_ret);
		us_tcont = HI_OMCI_GET_TCONT_ID(st_gemctp.us_tcont_ptr);
		i_ret = hi_omci_tapi_gemport_loopback_set(st_gemctp.us_portid, us_tcont, pst_entry->uc_galloopback);
		HI_OMCI_RET_CHECK(i_ret);
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}


/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
