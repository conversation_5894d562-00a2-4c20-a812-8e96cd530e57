/******************************************************************************
                  Copyright (C), 2012-2013, HSAN
 ******************************************************************************
  Filename    : hi_omci_me_traffic_descriptor.c
  Version     : 初稿
  Author      : owen
  Creation    : 2013-10-9
  Description : Qos配置接口
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"

#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"
#include "hi_list.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
#define HI_OMCI_ME_TRAFFIC_DESC_IS_CIR(mask) HI_OMCI_CHECK_ATTR((mask), HI_OMCI_ATTR1)
#define HI_OMCI_ME_TRAFFIC_DESC_IS_PIR(mask) HI_OMCI_CHECK_ATTR((mask), HI_OMCI_ATTR2)
#define HI_OMCI_ME_TRAFFIC_DESC_IS_CBS(mask) HI_OMCI_CHECK_ATTR((mask), HI_OMCI_ATTR3)
#define HI_OMCI_ME_TRAFFIC_DESC_IS_PBS(mask) HI_OMCI_CHECK_ATTR((mask), HI_OMCI_ATTR4)

//#define HI_OMCI_ME_TRAFFIC_DESC_BS_GET(rate) ((rate) * 10)
#define HI_OMCI_CAR_NUM_MAX 64

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
typedef struct {
	hi_uint32               ui_instid;
	hi_uint32               ui_carid;
	hi_list_head          st_list;
} hi_omci_traffic_dest_s;

static hi_list_head g_st_traffic_dest_list;

/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_traffic_desc_carid_get
 Description : 获取CARID
 Input Parm  : hi_uint32 ui_instid,
 Output Parm : hi_uint32 *pui_carid
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_traffic_desc_carid_get(hi_uint32 ui_instid, hi_uint32 *pui_carid)
{
	hi_omci_traffic_dest_s *pst_node = HI_NULL, *pst_tmp_node = HI_NULL;

	hi_list_for_each_entry_safe(pst_node, pst_tmp_node, &g_st_traffic_dest_list, st_list) {
		if (ui_instid == pst_node->ui_instid) {
			*pui_carid = pst_node->ui_carid;
			return HI_RET_SUCC;
		}
	}
	return (hi_int32)HI_RET_FAIL;
}


/******************************************************************************
 Function    : hi_omci_me_traffic_desc_set
 Description : Traffic descriptor set
               在数据库操作后执行
 Input Parm  : hi_void*pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_traffic_desc_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_traffic_desc_s *pst_entry = (hi_omci_me_traffic_desc_s *)pv_data;
	hi_omci_tapi_car_s st_car;
	hi_int32 i_ret, i_mark = 0;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;
	hi_ushort16 us_mask = pst_entry->st_msghead.us_attmask;
	hi_uint32 ui_carid = HI_OMCI_CAR_NUM_MAX;
	hi_omci_traffic_dest_s *pst_node = HI_NULL, *pst_tmp_node = HI_NULL;

	HI_OS_MEMSET_S(&st_car, sizeof(st_car), 0, sizeof(st_car));
	hi_list_for_each_entry_safe(pst_node, pst_tmp_node, &g_st_traffic_dest_list, st_list) {
		if (us_instid == pst_node->ui_instid) {
			ui_carid = pst_node->ui_carid;
			hi_omci_tapi_car_get(ui_carid, &st_car);
			hi_omci_tapi_car_release(ui_carid);
			i_mark = 1;
			break;
		}
	}

	if (HI_OMCI_ME_TRAFFIC_DESC_IS_CIR(us_mask)) {
		st_car.ui_pir = pst_entry->st_traffic.ui_cir;
	}

	/* The default value 0 accepts the ONU.s factory policy */
	if (HI_OMCI_ME_TRAFFIC_DESC_IS_PIR(us_mask)) {
		st_car.ui_pir = pst_entry->st_traffic.ui_pir;
		if (st_car.ui_pir == 0) {
			return HI_RET_SUCC;
		}
	}

	i_ret = hi_omci_tapi_car_set(&ui_carid, &st_car);
	HI_OMCI_RET_CHECK(i_ret);
	if (1 == i_mark) {
		pst_node->ui_carid = ui_carid;
		return HI_RET_SUCC;
	}
	pst_node = (hi_omci_traffic_dest_s *)hi_os_malloc(sizeof(hi_omci_traffic_dest_s));
	if (pst_node == HI_NULL) {
		hi_omci_systrace(HI_RET_FAIL, 0, 0, 0, 0);
		return HI_RET_FAIL;
	}
	pst_node->ui_carid = ui_carid;
	pst_node->ui_instid =  pst_entry->st_msghead.us_instid;
	hi_list_add_tail(&pst_node->st_list, &g_st_traffic_dest_list);
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_traffic_desc_create
 Description : Traffic descriptor create
               在数据库操作后执行
 Input Parm  : hi_void*pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_traffic_desc_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_traffic_desc_s *pst_entry = (hi_omci_me_traffic_desc_s *)pv_data;
	hi_omci_tapi_car_s st_car, st_tmp_car;
	hi_uint32 ui_carid = pst_entry->st_msghead.us_instid;
	hi_int32 i_ret = 0;
	hi_omci_traffic_dest_s *pst_node;

	HI_OS_MEMSET_S(&st_car, sizeof(st_car), 0, sizeof(st_car));
	HI_OS_MEMSET_S(&st_tmp_car, sizeof(st_tmp_car), 0, sizeof(st_tmp_car));
	hi_omci_debug("carid[%010u] instid[%05hu] cbs[%010u] cir[%010u] pbs[%010u] pir[%010u]\n",
		      ui_carid, pst_entry->st_msghead.us_instid, pst_entry->st_traffic.ui_cbs,
		      pst_entry->st_traffic.ui_cir, pst_entry->st_traffic.ui_pbs, pst_entry->st_traffic.ui_pir);

	st_car.ui_cir = pst_entry->st_traffic.ui_cir;

	/* The default value 0 accepts the ONU.s factory policy */
	if (0 == pst_entry->st_traffic.ui_pir) {
		return HI_RET_SUCC;
	} else {
		st_car.ui_pir = pst_entry->st_traffic.ui_pir;
	}

	if (0 == pst_entry->st_traffic.ui_cbs) {
		st_car.ui_cbs = 20000;
	} else {
		st_car.ui_cbs = pst_entry->st_traffic.ui_cbs;
	}

	if (0 == pst_entry->st_traffic.ui_pbs) {
		st_car.ui_pbs = 20000;
	} else {
		st_car.ui_pbs = pst_entry->st_traffic.ui_pbs;
	}

	i_ret = hi_omci_tapi_car_set(&ui_carid, &st_car);
	if (i_ret == HI_RET_FAIL) {
		return HI_RET_SUCC;
	}
	pst_node = (hi_omci_traffic_dest_s *)hi_os_malloc(sizeof(hi_omci_traffic_dest_s));
	if (pst_node == HI_NULL) {
		hi_omci_systrace(HI_RET_FAIL, 0, 0, 0, 0);
		return (hi_int32)HI_RET_FAIL;
	}
	pst_node->ui_carid = ui_carid;
	pst_node->ui_instid =  pst_entry->st_msghead.us_instid;
	hi_list_add_tail(&pst_node->st_list, &g_st_traffic_dest_list);

	hi_omci_systrace(i_ret, ui_carid, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_traffic_desc_delete
 Description : Traffic descriptor delete
               在数据库操作前执行
 Input Parm  : hi_void*pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_traffic_desc_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_traffic_desc_s *pst_entry = (hi_omci_me_traffic_desc_s *)pv_data;
	hi_int32 i_ret = 0;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;
	hi_uint32 ui_carid;
	hi_omci_traffic_dest_s *pst_node = HI_NULL, *pst_tmp_node = HI_NULL;

	hi_list_for_each_entry_safe(pst_node, pst_tmp_node, &g_st_traffic_dest_list, st_list) {
		if (us_instid == pst_node->ui_instid) {
			ui_carid = pst_node->ui_carid;
			i_ret = hi_omci_tapi_car_release(ui_carid);
			HI_OMCI_RET_CHECK(i_ret);
			hi_list_del(&pst_node->st_list);
			hi_os_free(pst_node);
			break;
		}
	}

	hi_omci_systrace(i_ret, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_traffic_desc_init()
{
	hi_list_init_head(&g_st_traffic_dest_list);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_traffic_desc_exit()
{
	hi_list_head *list_pos = HI_NULL;
	hi_list_head *list_next = HI_NULL;
	hi_omci_traffic_dest_s *entity = HI_NULL;

	hi_list_for_each_safe(list_pos, list_next, &g_st_traffic_dest_list) {
		entity = (hi_omci_traffic_dest_s *)hi_list_entry(list_pos, hi_omci_traffic_dest_s, st_list);
		if (HI_NULL != entity) {
			hi_omci_tapi_car_release(entity->ui_carid);
			hi_list_del(&entity->st_list);
			hi_os_free(entity);
		}
	}
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
