/******************************************************************************
                  Copyright (C), 2012-2013, HSAN
 ******************************************************************************
  Filename    : hi_omci_me_tcont.c
  Version     : 初稿
  Author      : owen
  Creation    : 2013-10-9
  Description : ANI配置接口
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
#define HI_OMCI_ME_TCONT_INVALID1   0xff
#define HI_OMCI_ME_TCONT_INVALID2   0xffff

#define HI_OMCI_ME_TCONT_IS_ALLOCID(mask) HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR1)
#define HI_OMCI_ME_TCONT_IS_POLICY(mask) HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR3)

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_tcont_set
 Description : t-cont set
               数据库操作后处理
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_void*pv_data
               hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_tcont_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_tcont_s *pst_inst = (hi_omci_me_tcont_s *)pv_data;
	hi_ushort16 us_mask = pst_inst->st_msghead.us_attmask;
	hi_uint32 ui_tcontid = HI_OMCI_GET_TCONT_ID(pst_inst->st_msghead.us_instid);
	hi_int32 i_ret = 0;

	if (HI_OMCI_ME_TCONT_IS_ALLOCID(us_mask)) {
		if (pst_inst->us_allocid == HI_OMCI_ME_TCONT_INVALID1 ||
		    pst_inst->us_allocid == HI_OMCI_ME_TCONT_INVALID2) {
			i_ret = hi_omci_tapi_tcont_del(ui_tcontid);
			HI_OMCI_RET_CHECK(i_ret);
		} else {
			i_ret = hi_omci_tapi_tcont_set(ui_tcontid, pst_inst->us_allocid);
			HI_OMCI_RET_CHECK(i_ret);
		}
	}

	if (HI_OMCI_ME_TCONT_IS_POLICY(us_mask)) {
		if (pst_inst->uc_policy == HI_OMCI_ME_TCONT_POLICY_SP_E) {
			i_ret = hi_omci_tapi_tcont_policy_set(ui_tcontid, HI_OMCI_TAPI_TCONT_SP_E);
		} else if (pst_inst->uc_policy == HI_OMCI_ME_TCONT_POLICY_WRR_E) {
			i_ret = hi_omci_tapi_tcont_policy_set(ui_tcontid, HI_OMCI_TAPI_TCONT_WRR_E);
		}

		HI_OMCI_RET_CHECK(i_ret);
	}

	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
/******************************************************************************
 Function    : hi_omci_me_tcont_init
 Description : t-cont init
               ONU初始化创建t-cont实例
 Input Parm  : 无
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_tcont_init(hi_uint32 ui_tcontid)
{
	hi_omci_me_tcont_s st_entry;
	hi_omci_me_traffic_sche_s st_traffic_entry;
	hi_omci_tapi_gpon_mode_e em_mode;
	hi_int32 i_ret;
	hi_ushort16 us_instid;

	us_instid = (0x8000 | ui_tcontid);
	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_TCONT_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_tapi_gponmode_get(&em_mode);
	HI_OMCI_RET_CHECK(i_ret);

	if (em_mode == HI_OMCI_TAPI_10GGPON_U2DOT5G_E || em_mode == HI_OMCI_TAPI_10GGPON_10G_E) {
		st_entry.us_allocid = HI_OMCI_ME_TCONT_INVALID2;
	} else {
		st_entry.us_allocid = HI_OMCI_ME_TCONT_INVALID1;
	}

	st_entry.st_msghead.us_instid = us_instid;
	st_entry.uc_deprecated = 1;
	st_entry.uc_policy = HI_OMCI_ME_TCONT_POLICY_SP_E;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_TCONT_E, us_instid, &st_entry);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_TS_G_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);
	HI_OS_MEMSET_S(&st_traffic_entry, sizeof(st_traffic_entry),  0, sizeof(st_traffic_entry));
	st_traffic_entry.st_trafficsche.us_tcont_ptr = us_instid;
	st_traffic_entry.st_msghead.us_instid = us_instid;
	st_traffic_entry.st_trafficsche.uc_policy = 2;
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_TS_G_E, us_instid, &st_traffic_entry);
	HI_OMCI_RET_CHECK(i_ret);
	return HI_RET_SUCC;
}

