/******************************************************************************

                  版权所有 (C), 2009-2019, 华为技术有限公司

 ******************************************************************************
  文 件 名   : hi_omci_me_pq.c
  版 本 号   : 初稿
  作    者   : y00185833
  生成日期   : D2011_10_18
  功能描述   : OMCI manager entity Priority queue file.
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"

#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
#define HI_OMCI_ME_PQ_MIN_GREEN_THRU    0
#define HI_OMCI_ME_PQ_MAX_GREEN_THRU    1
#define HI_OMCI_ME_PQ_MIN_YELLOW_THRU   2
#define HI_OMCI_ME_PQ_MAX_YELLOW_THRU   3

#define HI_OMCI_ME_PQ_IS_UPSTREAM(instid) (((instid) & 0x8000) == 0x8000)
#define HI_OMCI_ME_PQ_IS_DNSTREAM(instid) (((instid) & 0x0000) == HI_FALSE)

#define HI_OMCI_ME_PQ_GET_EGR(relatted_port) (((relatted_port) >> 16) & 0xff)
#define HI_OMCI_ME_PQ_GET_PRI(relatted_port) ((relatted_port) & 0xffff)
#define HI_OMCI_ME_PQ_GET_PORT(erg, pri) ((((erg) & 0xffff) << 16) | (pri & 0xffff))
#define HI_OMCI_ME_PQ_GET_GREEN_PRBLT(prblt) ((prblt) & 0xff)
#define HI_OMCI_ME_PQ_GET_YELLOW_PRBLT(prblt) (((prblt) >> 8) & 0xff)
#define HI_OMCI_ME_PQ_GET_PRBLT(green, yellow) ((((yellow) & 0xff) << 8) | ((green) & 0xff))

#define HI_OMCI_ME_PQ_GET_INSTID(dirc, tcont, port, pri) \
	((dirc) == 1 ? ((0x8000 | ((tcont) & 0x7) << 3) | ((pri) & 0x7)) : ((((port) & 0xf) << 3) | ((pri) & 0x7)))

#define HI_OMCI_ME_PQ_IS_ALLOCSIZE(mask)    HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR3)
#define HI_OMCI_ME_PQ_IS_WEIGHT(mask)       HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR8)
#define HI_OMCI_ME_PQ_IS_DROPTHRU(mask)     HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR13)
#define HI_OMCI_ME_PQ_IS_DROPMACP(mask)     HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR14)

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/

/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

hi_int32 hi_omci_me_pq_pri_get(hi_ushort16 us_instid, hi_uint32 *ui_egr, hi_uint32 *pui_pri)
{
	hi_uint32 ui_related_port;
	hi_uint32 ui_ret;

	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_PQ_G_E, us_instid, HI_OMCI_ATTR6, &ui_related_port);
	HI_OMCI_RET_CHECK(ui_ret);

	*ui_egr = HI_OMCI_ME_PQ_GET_EGR(ui_related_port);
	*pui_pri = HI_OMCI_ME_PQ_GET_PRI(ui_related_port);

	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_me_pq_set_after
 功能描述  : Priority queue set
             数据库操作后配置
 输入参数  : hi_uint32 ui_cmdtype
             hi_void*pv_data
             hi_uint32 ui_in
             hi_uint32 *pui_outlen
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_me_pq_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_int32 i_ret;
	hi_omci_me_pq_s *pst_entity = (hi_omci_me_pq_s *)pv_data;
	hi_omci_tapi_egress_type_e em_egr;
	hi_omci_tapi_queue_para_s st_queue;
	hi_uint32 ui_relatedport = pst_entity->st_pq.ui_relatedport;
	hi_ushort16 us_instid = pst_entity->st_msghead.us_instid;
	hi_ushort16 us_mask = pst_entity->st_msghead.us_attmask;
	hi_ushort16 us_portinstid;
	hi_ushort16 us_tcontid;
	hi_ushort16 us_portid;
	hi_uchar8 uc_pqid;
	if (!HI_OMCI_ME_PQ_IS_ALLOCSIZE(us_mask) &&
	    !HI_OMCI_ME_PQ_IS_DROPTHRU(us_mask) &&
	    !HI_OMCI_ME_PQ_IS_DROPMACP(us_mask)) {
		hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}
	us_portinstid = HI_OMCI_ME_PQ_GET_EGR(ui_relatedport);
	uc_pqid = (hi_uchar8)HI_OMCI_ME_PQ_GET_PRI(ui_relatedport);

	if (HI_OMCI_ME_VEIP_INSTID == us_portinstid) {
		hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}
	if (HI_OMCI_ME_PQ_IS_UPSTREAM(us_instid)) {
		//em_egr = (hi_omci_tapi_egress_type_e)(us_instid & 0x7);
		us_tcontid = HI_OMCI_GET_TCONT_ID(us_instid);
		em_egr = (hi_omci_tapi_egress_type_e)(us_tcontid >> 3);//上行队列：8个TCONT，每个TCONT有8个队列。TCONT 0～7 对应队列0x8000~0x803F。
	} else {
		us_portid = HI_OMCI_GET_PORTID_FROM_INSTID(us_portinstid);
		em_egr = (hi_omci_tapi_egress_type_e)(HI_OMCI_TAPI_EGRESS_UNI0_E + us_portid);
	}

	HI_OS_MEMSET_S(&st_queue, sizeof(st_queue), 0, sizeof(st_queue));
	st_queue.ui_max_size = pst_entity->st_pq.us_maxqueue;
	st_queue.ui_size = pst_entity->st_pq.us_allocsize;
	st_queue.ui_min_green_thrd = pst_entity->st_pq.us_qthresh[HI_OMCI_ME_PQ_MIN_GREEN_THRU];
	st_queue.ui_max_green_thrd = pst_entity->st_pq.us_qthresh[HI_OMCI_ME_PQ_MAX_GREEN_THRU];
	st_queue.ui_min_yellow_thrd = pst_entity->st_pq.us_qthresh[HI_OMCI_ME_PQ_MIN_YELLOW_THRU];
	st_queue.ui_max_yellow_thrd = pst_entity->st_pq.us_qthresh[HI_OMCI_ME_PQ_MAX_YELLOW_THRU];
	st_queue.ui_green_prblt = HI_OMCI_ME_PQ_GET_GREEN_PRBLT(pst_entity->st_pq.us_max_p);
	st_queue.ui_yellow_prblt = HI_OMCI_ME_PQ_GET_YELLOW_PRBLT(pst_entity->st_pq.us_max_p);

	i_ret = hi_omci_tapi_queue_set(em_egr, uc_pqid, &st_queue);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}


/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

hi_int32 hi_omci_me_pq_init(hi_uint32 ui_direction, hi_uint32 ui_tcont, hi_uint32 portid, hi_uint32 pri)
{
	hi_omci_me_pq_s st_entity;
	hi_omci_tapi_queue_para_s st_queue;
	hi_omci_tapi_egress_type_e em_egr;
	hi_omci_tapi_sysinfo_s st_info;
	hi_int32 i_ret;
	hi_ushort16 us_tcont_instid = (hi_ushort16)(0x8000 | ((ui_tcont) & 0x7));
	hi_ushort16 us_port_instid = (hi_ushort16)portid;
	hi_ushort16 us_related_instid;
	hi_ushort16 us_instid;

	us_instid = HI_OMCI_ME_PQ_GET_INSTID(ui_direction, ui_tcont, portid, pri);

	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_PQ_G_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_tapi_sysinfo_get(&st_info);
	HI_OMCI_RET_CHECK(i_ret);

	if (HI_TRUE == ui_direction) {
		em_egr = (hi_omci_tapi_egress_type_e)ui_tcont;
		us_related_instid = us_tcont_instid;
	} else {
		if (st_info.ui_port_num == portid) {
			us_related_instid = HI_OMCI_ME_VEIP_INSTID;
			em_egr = HI_OMCI_TAPI_EGRESS_VEIP_E;
		} else {
			hi_omci_tapi_port_pptpinstid_get(us_port_instid, &us_related_instid);
			em_egr = (hi_omci_tapi_egress_type_e)(HI_OMCI_TAPI_EGRESS_UNI0_E + portid);
		}
	}

	i_ret = hi_omci_tapi_queue_get(em_egr, pri, &st_queue);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
	st_entity.st_msghead.us_instid = us_instid;
	st_entity.st_pq.us_maxqueue = (hi_ushort16)st_queue.ui_max_size;
	st_entity.st_pq.us_allocsize = (hi_ushort16)st_queue.ui_size;
	st_entity.st_pq.us_resetintvl = 30000;
	st_entity.st_pq.ui_relatedport = HI_OMCI_ME_PQ_GET_PORT(us_related_instid, pri);
	//st_entity.st_pq.uc_weight = (hi_uchar8)st_queue.ui_weight;
	st_entity.st_pq.uc_weight = 1;/*ZTE 10G OLT此参数需要配置为1，不然会config fail*/
	st_entity.st_pq.us_discard = (hi_ushort16)st_queue.ui_max_size;
	st_entity.st_pq.us_operation = 1;
	st_entity.st_pq.us_qthresh[HI_OMCI_ME_PQ_MIN_GREEN_THRU] = (hi_ushort16)st_queue.ui_min_green_thrd;
	st_entity.st_pq.us_qthresh[HI_OMCI_ME_PQ_MAX_GREEN_THRU] = (hi_ushort16)st_queue.ui_max_green_thrd;
	st_entity.st_pq.us_qthresh[HI_OMCI_ME_PQ_MIN_YELLOW_THRU] = (hi_ushort16)st_queue.ui_min_yellow_thrd;
	st_entity.st_pq.us_qthresh[HI_OMCI_ME_PQ_MAX_YELLOW_THRU] = (hi_ushort16)st_queue.ui_max_yellow_thrd;
	st_entity.st_pq.us_max_p = HI_OMCI_ME_PQ_GET_PRBLT(st_queue.ui_green_prblt, st_queue.ui_yellow_prblt);

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_PQ_G_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

