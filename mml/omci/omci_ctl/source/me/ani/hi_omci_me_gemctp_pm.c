/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_gemctp_pm.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-16
  Description: GEM port network CTP PM history data
******************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */


/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
hi_omci_tapi_stat_gemctppm_s gst_gempm_history[HI_OMCI_GEM_PM_MAX_NUM];
hi_omci_tapi_stat_gemctppm_s gst_gempm_prev_history[HI_OMCI_GEM_PM_MAX_NUM];
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_gemctppm_stat_get
 Description : 获取当前GEMPORT统计
 Input Parm  : hi_void *pv_data       GEMPORT历史累计统计数据
               hi_uint32 ui_inlen
 Output Parm : hi_void *pv_data       GEMPORT当前累计统计数据
               hi_uint32 *pui_outlen  输出数据长度
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_gemctppm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_stat_timer_s *pst_timer = (hi_omci_me_stat_timer_s *)pv_data;
	hi_omci_tapi_stat_gemctppm_s st_gempm_curr;
	hi_omci_me_gemctp_pm_s st_entity;
	hi_int32 i_ret;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->ui_instid;
	hi_ushort16 us_gemportid;
	hi_uint32 ui_index;

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
	i_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_GEM_CTP_E, us_instid, HI_OMCI_ATTR1, &us_gemportid);
	HI_OMCI_RET_CHECK(i_ret);

	/* 读取芯片当前统计 */
	i_ret = hi_omci_tapi_stat_gemport_get(us_gemportid, &st_gempm_curr);
	HI_OMCI_RET_CHECK(i_ret);

	for (ui_index = 0; ui_index < HI_OMCI_GEM_PM_MAX_NUM; ui_index++) {
		if (us_gemportid == gst_gempm_history[ui_index].ui_gemport) {
			break;
		}
	}

	/* 与历史数据相减，将结构保存到数据库 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_GEM_PORT_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	if (HI_OMCI_GEM_PM_MAX_NUM > ui_index) {
		st_entity.uc_endtime++;
		st_entity.ui_transframes = st_gempm_curr.ui_transframes - gst_gempm_history[ui_index].ui_transframes;
		st_entity.ui_rcvframes = st_gempm_curr.ui_rcvframes - gst_gempm_history[ui_index].ui_rcvframes;
		st_entity.ui_encryerrs = st_gempm_curr.ui_encryerrs - gst_gempm_history[ui_index].ui_encryerrs;
		st_entity.ul_rcvpayload  = st_gempm_curr.ul_rcvpayload - gst_gempm_history[ui_index].ul_rcvpayload;
		st_entity.ul_transpayload = st_gempm_curr.ul_transpayload - gst_gempm_history[ui_index].ul_transpayload;

		i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_GEM_PORT_PM_E, us_instid, &st_entity);
		HI_OMCI_RET_CHECK(i_ret);

		/* 将当前统计更新到历史统计 */
		st_gempm_curr.ui_gemport = gst_gempm_history[ui_index].ui_gemport;
		HI_OS_MEMCPY_S(&gst_gempm_prev_history[ui_index], sizeof(gst_gempm_prev_history[ui_index]),
			       &gst_gempm_history[ui_index], sizeof(hi_omci_tapi_stat_gemctppm_s));
		HI_OS_MEMCPY_S(&gst_gempm_history[ui_index], sizeof(gst_gempm_history[ui_index]), &st_gempm_curr,
			       sizeof(hi_omci_tapi_stat_gemctppm_s));
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_gemctppm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_gemctp_pm_s *pst_entry = (hi_omci_me_gemctp_pm_s *)pv_data;
	hi_omci_me_gemctp_pm_s st_entity;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;
	hi_ushort16 us_gemportid;
	hi_uint32 ui_index;

	i_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_GEM_CTP_E, us_instid, HI_OMCI_ATTR1, &us_gemportid);
	HI_OMCI_RET_CHECK(i_ret);

	for (ui_index = 0; ui_index < HI_OMCI_GEM_PM_MAX_NUM; ui_index++) {
		if (us_gemportid == gst_gempm_history[ui_index].ui_gemport) {
			break;
		}
	}

	/*获取gemport PM实例数据 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_GEM_PORT_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	if (HI_OMCI_GEM_PM_MAX_NUM > ui_index) {
		st_entity.ui_transframes = gst_gempm_history[ui_index].ui_transframes - gst_gempm_prev_history[ui_index].ui_transframes;
		st_entity.ui_rcvframes = gst_gempm_history[ui_index].ui_rcvframes - gst_gempm_prev_history[ui_index].ui_rcvframes;
		st_entity.ui_encryerrs = gst_gempm_history[ui_index].ui_encryerrs - gst_gempm_prev_history[ui_index].ui_encryerrs;
		st_entity.ul_rcvpayload = gst_gempm_history[ui_index].ul_rcvpayload - gst_gempm_prev_history[ui_index].ul_rcvpayload;
		st_entity.ul_transpayload = gst_gempm_history[ui_index].ul_transpayload -
					    gst_gempm_prev_history[ui_index].ul_transpayload;

		/*将清零的数据保存到gemport PM实例数据中*/
		i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_GEM_PORT_PM_E, us_instid, &st_entity);
		HI_OMCI_RET_CHECK(i_ret);
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_gemctppm_getcurrent
 Description : GEM port network CTP PM history data getcurrent
 Input Parm  : hi_void *pv_data,
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_gemctppm_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_gemctp_pm_s *pst_entry = (hi_omci_me_gemctp_pm_s *)pv_data;
	hi_omci_tapi_stat_gemctppm_s st_gempm_curr;
	hi_omci_me_gemctp_pm_s st_entity;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;
	hi_ushort16 us_gemportid;
	hi_uint32 ui_index;

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
	i_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_GEM_CTP_E, us_instid, HI_OMCI_ATTR1, &us_gemportid);
	HI_OMCI_RET_CHECK(i_ret);

	/* 读取芯片当前统计 */
	i_ret = hi_omci_tapi_stat_gemport_get(us_gemportid, &st_gempm_curr);
	HI_OMCI_RET_CHECK(i_ret);

	for (ui_index = 0; ui_index < HI_OMCI_GEM_PM_MAX_NUM; ui_index++) {
		if (us_gemportid == gst_gempm_history[ui_index].ui_gemport) {
			break;
		}
	}

	if (HI_OMCI_GEM_PM_MAX_NUM > ui_index) {
		st_entity.uc_endtime++;
		st_entity.ui_transframes = st_gempm_curr.ui_transframes - gst_gempm_history[ui_index].ui_transframes;
		st_entity.ui_rcvframes = st_gempm_curr.ui_rcvframes - gst_gempm_history[ui_index].ui_rcvframes;
		st_entity.ui_encryerrs = st_gempm_curr.ui_encryerrs - gst_gempm_history[ui_index].ui_encryerrs;
		st_entity.ul_rcvpayload = st_gempm_curr.ul_rcvpayload - gst_gempm_history[ui_index].ul_rcvpayload;
		st_entity.ul_transpayload = st_gempm_curr.ul_transpayload - gst_gempm_history[ui_index].ul_transpayload;

		i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_GEM_PORT_PM_E, us_instid, &st_entity);
		HI_OMCI_RET_CHECK(i_ret);
	}


	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_gemctppm_create
 Description : GEM port network CTP PM history data create
 Input Parm  : hi_void *pv_data,
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_gemctppm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_gemctp_pm_s *pst_entry = (hi_omci_me_gemctp_pm_s *)pv_data;
	hi_omci_me_stat_timer_s st_gemctppm_stat_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	/* 创建15分钟定时器，获取统计 */
	HI_OS_MEMSET_S(&st_gemctppm_stat_timer, sizeof(st_gemctppm_stat_timer), 0, sizeof(st_gemctppm_stat_timer));
	st_gemctppm_stat_timer.ui_meid = HI_OMCI_PRO_ME_GEM_PORT_PM_E;
	st_gemctppm_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_start(&st_gemctppm_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_gemctppm_delete
 Description : GEM port network CTP PM history data delete
 Input Parm  : hi_void *pv_data,
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_gemctppm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_gemctp_pm_s *pst_entry = (hi_omci_me_gemctp_pm_s *)pv_data;
	hi_omci_me_stat_timer_s st_gemctppm_stat_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	/* 删除定时器 */
	HI_OS_MEMSET_S(&st_gemctppm_stat_timer, sizeof(st_gemctppm_stat_timer), 0, sizeof(st_gemctppm_stat_timer));
	st_gemctppm_stat_timer.ui_meid = HI_OMCI_PRO_ME_GEM_PORT_PM_E;
	st_gemctppm_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_stop(&st_gemctppm_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_gemctppm_init()
{
	hi_uint32 ui_index;

	HI_OS_MEMSET_S(&gst_gempm_prev_history[0], sizeof(hi_omci_tapi_stat_gemctppm_s)*HI_OMCI_GEM_PM_MAX_NUM, 0,
		       sizeof(hi_omci_tapi_stat_gemctppm_s)*HI_OMCI_GEM_PM_MAX_NUM);
	HI_OS_MEMSET_S(&gst_gempm_history[0], sizeof(hi_omci_tapi_stat_gemctppm_s)*HI_OMCI_GEM_PM_MAX_NUM, 0,
		       sizeof(hi_omci_tapi_stat_gemctppm_s)*HI_OMCI_GEM_PM_MAX_NUM);

	for (ui_index = 0; ui_index < HI_OMCI_GEM_PM_MAX_NUM; ui_index++) {
		gst_gempm_prev_history[ui_index].ui_gemport = 0xFFFFFFFF;
		gst_gempm_history[ui_index].ui_gemport = 0xFFFFFFFF;
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

