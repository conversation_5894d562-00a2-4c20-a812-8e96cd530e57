/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_multi_gem_iwtp.c
  Version    : 初稿
  Author     : owen
  Creation   : 2015-1-15
  Description: Multicast GEM interworking termination point
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"

#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/

#define HI_OMCI_ME_MULTIWTP_IS_ADDRTB(mask) HI_OMCI_CHECK_ATTR((mask), HI_OMCI_ATTR9)

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/******************************************************************************
 Function    : __omci_me_multiwtp_table_printf
 Description : 表项打印
 Input Parm  : hi_uchar8 *puc_table,
               hi_uint32 ui_len
 Output Parm : N/A

 Return      : hi_void
******************************************************************************/
hi_void __omci_me_multiwtp_table_printf(hi_uchar8 *puc_table, hi_uint32 ui_len)
{
	hi_uint32 ui_size = sizeof(hi_omci_me_multi_addrtable_s);
	hi_uint32 ui_index;

	if (!hi_log_print_on((hi_uint32)HI_SUBMODULE_OMCI_COM, HI_DBG_LEVEL_DEBUG)
	    && !hi_log_print_on((hi_uint32)HI_SYSBASE_GLB, HI_DBG_LEVEL_DEBUG)) {
		return;
	}

	hi_os_printf("================== start =====================\n");

	for (ui_index = 0; ui_index < ui_len; ui_index += ui_size, puc_table += ui_size) {
		HI_PRINT_MEM(ui_size, puc_table);
	}

	hi_os_printf("================== end =====================\n");
}

/******************************************************************************
 Function    : hi_omci_me_multiwtp_set
 Description : IPv4 Multicast address table entry set
 Input Parm  : hi_omci_me_multi_addrtable_s *pst_entry,
               hi_omci_me_multi_addrtable_s *pst_tab,
               hi_uint32 ui_tabnum
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 __omci_me_multiwtp_addrtb_entry_set(hi_omci_me_multi_addrtable_s *pst_entry,
		hi_omci_me_multi_addrtable_s *pst_tab, hi_uint32 ui_tabnum)
{
	hi_omci_tapi_mc_ip_addr_s st_filter;
	hi_omci_me_multi_addrtable_s *pst_tmp;
	hi_omci_tapi_gemport_para_s st_gempara;
	hi_int32 i_ret;
	hi_uint32 ui_index;
	hi_uint32 ui_gemportid = ntohs(pst_entry->us_gemportid);
	//hi_uint32 ui_secondindex  =ntohs(pst_entry->us_secondaryindex);

	if (pst_entry->ui_startip == 0 && pst_entry->ui_endip == 0) {
		/* 删除表项 */
		/* 查找表项*/
		for (ui_index = 0; ui_index < ui_tabnum; ui_index++, pst_tab++) {
			if (pst_tab->us_gemportid == pst_entry->us_gemportid
			    && pst_tab->us_secondaryindex == pst_entry->us_secondaryindex) {
				st_filter.start_ip.st_ipv4.ui_v4 = ntohl(pst_tab->ui_startip);
				st_filter.end_ip.st_ipv4.ui_v4 = ntohl(pst_tab->ui_endip);
				i_ret = hi_omci_tapi_mc_filter_del(ntohs(pst_entry->us_gemportid), &st_filter);
				HI_OMCI_RET_CHECK(i_ret);

				//i_ret = hi_omci_tapi_gemport_del(ui_gemportid);

				pst_tab->ui_startip = 0;
				pst_tab->ui_endip = 0;
				break;
			}
		}
	} else {
		/* 配置表项 */
		/* 查找表项*/
		for (ui_index = 0, pst_tmp = pst_tab; ui_index < ui_tabnum; ui_index++, pst_tmp++) {
			if (pst_tab->us_gemportid == pst_entry->us_gemportid
			    && pst_tab->us_secondaryindex == pst_entry->us_secondaryindex) {
				st_filter.start_ip.st_ipv4.ui_v4 = ntohl(pst_entry->ui_startip);
				st_filter.end_ip.st_ipv4.ui_v4 = ntohl(pst_entry->ui_endip);
				i_ret = hi_omci_tapi_mc_filter_set(ntohs(pst_entry->us_gemportid), &st_filter);
				HI_OMCI_RET_CHECK(i_ret);

				pst_tmp->ui_startip = pst_entry->ui_startip;
				pst_tmp->ui_endip = pst_entry->ui_endip;

				hi_omci_ani_systrace(HI_RET_SUCC, 0, 0, 0, 0);
				return HI_RET_SUCC;
			}
		}

		/* 查找空表项 */
		for (ui_index = 0, pst_tmp = pst_tab; ui_index < ui_tabnum; ui_index++, pst_tmp++) {
			if (pst_tmp->ui_startip == 0 && pst_tmp->ui_endip == 0) {
				i_ret = hi_omci_tapi_gemport_get(ui_gemportid, &st_gempara);
				if (i_ret != HI_RET_SUCC || st_gempara.em_type != HI_OMCI_TAPI_MULTICAST_GEMPORT_E) {
					st_gempara.em_type = HI_OMCI_TAPI_MULTICAST_GEMPORT_E;
					st_gempara.ui_dncarid = HI_OMCI_TAPI_CARID_INVALID;
					st_gempara.ui_upcarid = HI_OMCI_TAPI_CARID_INVALID;
					st_gempara.ui_dnpri = (hi_uint32)HI_OMCI_TAPI_PRI_INVALID;
					st_gempara.ui_uppri = (hi_uint32)HI_OMCI_TAPI_PRI_INVALID;
					i_ret = hi_omci_tapi_gemport_set(ui_gemportid, &st_gempara);
					HI_OMCI_RET_CHECK(i_ret);
				}

				st_filter.start_ip.st_ipv4.ui_v4 = ntohl(pst_entry->ui_startip);
				st_filter.end_ip.st_ipv4.ui_v4 = ntohl(pst_entry->ui_endip);
				i_ret = hi_omci_tapi_mc_filter_set(ui_gemportid, &st_filter);
				HI_OMCI_RET_CHECK(i_ret);

				HI_OS_MEMCPY_S(pst_tmp, sizeof(*pst_tmp), pst_entry, sizeof(hi_omci_me_multi_addrtable_s));

				hi_omci_ani_systrace(HI_RET_SUCC, 0, 0, 0, 0);
				return HI_RET_SUCC;
			}
		}
	}

	hi_omci_ani_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_multiwtp_set
 Description : Multicast GEM interworking termination point set
               数据库刷新前执行此函数
 Input Parm  : hi_void *pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_multiwtp_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_multigemiwtp_s *pst_entity = (hi_omci_me_multigemiwtp_s *)pv_data;
	hi_omci_me_multi_addrtable_s *pst_addrtb;
	hi_uint32 ui_tabnum;
	hi_uint32 ui_size;
	hi_uint32 ui_ret;
	hi_ushort16 us_instid = pst_entity->st_msghead.us_instid;
	hi_ushort16 us_mask = pst_entity->st_msghead.us_attmask;

	if (HI_OMCI_ME_MULTIWTP_IS_ADDRTB(us_mask)) {
		ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_MULTICAST_IWTP_E, HI_OMCI_ATTR9, &ui_tabnum);
		HI_OMCI_RET_CHECK(ui_ret);

		ui_size = sizeof(hi_omci_me_multi_addrtable_s) * ui_tabnum;
		pst_addrtb = (hi_omci_me_multi_addrtable_s *)hi_os_malloc(ui_size);
		if (HI_NULL == pst_addrtb) {
			hi_omci_ani_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
			return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
		}

		HI_OS_MEMSET_S(pst_addrtb, ui_size, 0, ui_size);

		ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MULTICAST_IWTP_E, us_instid, HI_OMCI_ATTR9, pst_addrtb);
		if (HI_RET_SUCC != ui_ret) {
			hi_os_free(pst_addrtb);
			hi_omci_ani_systrace(ui_ret, 0, 0, 0, 0);
			return ui_ret;
		}
		__omci_me_multiwtp_table_printf((hi_uchar8 *)pst_addrtb, ui_size);
		__omci_me_multiwtp_table_printf((hi_uchar8 *)&pst_entity->mcaddresstable, sizeof(hi_omci_me_multi_addrtable_s));

		ui_ret = __omci_me_multiwtp_addrtb_entry_set(&pst_entity->mcaddresstable, pst_addrtb, ui_tabnum);
		if (HI_RET_SUCC != ui_ret) {
			hi_os_free(pst_addrtb);
			hi_omci_ani_systrace(ui_ret, 0, 0, 0, 0);
			return ui_ret;
		}

		__omci_me_multiwtp_table_printf((hi_uchar8 *)pst_addrtb, ui_size);

		ui_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_MULTICAST_IWTP_E, us_instid, HI_OMCI_ATTR9, pst_addrtb);
		if (HI_RET_SUCC != ui_ret) {
			hi_os_free(pst_addrtb);
			hi_omci_ani_systrace(ui_ret, 0, 0, 0, 0);
			return ui_ret;
		}

		hi_os_free(pst_addrtb);
	}

	hi_omci_ani_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}


/******************************************************************************
 Function    : hi_omci_me_multiwtp_create
 Description : 创建组播数据流
               数据库刷新后执行此函数
 Input Parm  : hi_void *pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_multiwtp_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_multigemiwtp_s *pst_entity = (hi_omci_me_multigemiwtp_s *)pv_data;
	hi_omci_tapi_gemport_para_s st_para;
	hi_omci_me_multi_addrtable_s *pst_addrtb;
	hi_uint32 ui_tabnum;
	hi_uint32 ui_ret;
	hi_ushort16 us_gemportid;
	hi_uint32 ui_ctp_ptr;
	hi_ushort16 us_instid = pst_entity->st_msghead.us_instid;

	hi_omci_debug("instid=0x%x\n", pst_entity->st_msghead.us_instid);
	hi_omci_debug("ctppointer=%hu, interworking=%hhu, service profile=%hu, operstate=%hhu, \n",
		      pst_entity->us_ctp_ptr, pst_entity->uc_interworking, pst_entity->us_svc_ptr, pst_entity->uc_operstate);

	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_GEM_CTP_E, pst_entity->us_ctp_ptr, HI_OMCI_ATTR1, &us_gemportid);
	if (HI_RET_SUCC != ui_ret) {
		/* 从me281实例中获取ctp_ptr */
		ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MULTICAST_IWTP_E, us_instid, HI_OMCI_ATTR1, &ui_ctp_ptr);
		if (HI_RET_SUCC != ui_ret) {
			hi_omci_ani_systrace(ui_ret, 0, 0, 0, 0);
			return ui_ret;
		}

		ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_GEM_CTP_E, ui_ctp_ptr, HI_OMCI_ATTR1, &us_gemportid);
		if (HI_RET_SUCC != ui_ret) {
			hi_omci_ani_systrace(ui_ret, 0, 0, 0, 0);
			return HI_RET_SUCC;
		}
	}

	ui_ret = hi_omci_tapi_gemport_get(us_gemportid, &st_para);
	HI_OMCI_RET_CHECK(ui_ret);

	st_para.em_type = HI_OMCI_TAPI_MULTICAST_GEMPORT_E;
	ui_ret = hi_omci_tapi_gemport_set(us_gemportid, &st_para);
	HI_OMCI_RET_CHECK(ui_ret);

	/* 初始化清空表项 */
	ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_MULTICAST_IWTP_E, HI_OMCI_ATTR9, &ui_tabnum);
	HI_OMCI_RET_CHECK(ui_ret);

	pst_addrtb = (hi_omci_me_multi_addrtable_s *)hi_os_malloc(sizeof(hi_omci_me_multi_addrtable_s) * ui_tabnum);
	if (HI_NULL == pst_addrtb) {
		hi_omci_ani_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MULTICAST_IWTP_E, us_instid, HI_OMCI_ATTR9, pst_addrtb);
	if (HI_RET_SUCC != ui_ret) {
		hi_os_free(pst_addrtb);
		hi_omci_ani_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	HI_OS_MEMSET_S(pst_addrtb, (sizeof(hi_omci_me_multi_addrtable_s) * ui_tabnum), 0,
		       (sizeof(hi_omci_me_multi_addrtable_s) * ui_tabnum));

	ui_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_MULTICAST_IWTP_E, us_instid, HI_OMCI_ATTR9, pst_addrtb);
	if (HI_RET_SUCC != ui_ret) {
		hi_os_free(pst_addrtb);
		hi_omci_ani_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	hi_os_free(pst_addrtb);

	hi_omci_ani_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_multiwtp_delete
 Description : 创建组播数据流
               数据库刷新前执行此函数
 Input Parm  : hi_void *pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_multiwtp_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_multigemiwtp_s *pst_entity = (hi_omci_me_multigemiwtp_s *)pv_data;
	hi_omci_tapi_gemport_para_s st_para;
	hi_omci_me_multi_addrtable_s *pst_addrtb;
	hi_omci_me_multi_addrtable_s *pst_list;
	hi_omci_me_multi_addrtable_s st_list;
	hi_uint32 ui_tabnum;
	hi_uint32 ui_gemportid;
	hi_uint32 ui_index;
	hi_uint32 ui_ctp_ptr;
	hi_uint32 ui_ret;
	hi_ushort16 us_instid = pst_entity->st_msghead.us_instid;

	/* 将GEMPORT类型修改为ETH */
	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_GEM_CTP_E, pst_entity->us_ctp_ptr, HI_OMCI_ATTR1, &ui_gemportid);
	if (HI_RET_SUCC != ui_ret) {
		/* 从me281实例中获取ctp_ptr */
		ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MULTICAST_IWTP_E, us_instid, HI_OMCI_ATTR1, &ui_ctp_ptr);
		if (HI_RET_SUCC != ui_ret) {
			hi_omci_ani_systrace(ui_ret, 0, 0, 0, 0);
			return ui_ret;
		}

		ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_GEM_CTP_E, ui_ctp_ptr, HI_OMCI_ATTR1, &ui_gemportid);
		if (HI_RET_SUCC != ui_ret) {
			hi_omci_ani_systrace(ui_ret, 0, 0, 0, 0);
			return HI_RET_SUCC;
		}
	}

	ui_ret = hi_omci_tapi_gemport_get(ui_gemportid, &st_para);
	HI_OMCI_RET_CHECK(ui_ret);

	st_para.em_type = HI_OMCI_TAPI_GENERAL_GEMPORT_E;
	ui_ret = hi_omci_tapi_gemport_set(ui_gemportid, &st_para);
	HI_OMCI_RET_CHECK(ui_ret);

	/* 清空过滤表项 */
	ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_MULTICAST_IWTP_E, HI_OMCI_ATTR9, &ui_tabnum);
	HI_OMCI_RET_CHECK(ui_ret);

	pst_addrtb = (hi_omci_me_multi_addrtable_s *)hi_os_malloc(sizeof(hi_omci_me_multi_addrtable_s) * ui_tabnum);
	if (HI_NULL == pst_addrtb) {
		hi_omci_ani_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MULTICAST_IWTP_E, us_instid, HI_OMCI_ATTR9, pst_addrtb);
	if (HI_RET_SUCC != ui_ret) {
		hi_os_free(pst_addrtb);
		hi_omci_ani_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	for (ui_index = 0, pst_list = pst_addrtb; ui_index < ui_tabnum; ui_index++, pst_list++) {
		if (0 == pst_list->ui_startip || 0 == pst_list->ui_endip) {
			continue;
		}

		HI_OS_MEMSET_S(&st_list, sizeof(st_list), 0, sizeof(st_list));
		st_list.us_gemportid = pst_list->us_gemportid;
		st_list.us_secondaryindex = pst_list->us_secondaryindex;
		ui_ret = __omci_me_multiwtp_addrtb_entry_set(&st_list, pst_addrtb, ui_tabnum);
		if (HI_RET_SUCC != ui_ret) {
			hi_os_free(pst_addrtb);
			hi_omci_ani_systrace(ui_ret, 0, 0, 0, 0);
			return ui_ret;
		}
	}

	hi_os_free(pst_addrtb);

	hi_omci_ani_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/


