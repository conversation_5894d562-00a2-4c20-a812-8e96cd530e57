/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_fec_pm.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-16
  Description: FEC performance monitoring history data
******************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
#define HI_OMCI_ME_FECPM_ALARM_CORRECT_BYTES        HI_OMCI_ME_ALARM_BITMAP(1)
#define HI_OMCI_ME_FECPM_ALARM_CORRECT_CODE_WORDS   HI_OMCI_ME_ALARM_BITMAP(2)
#define HI_OMCI_ME_FECPM_ALARM_UNCORRECT_CODE_WORDS HI_OMCI_ME_ALARM_BITMAP(3)
#define HI_OMCI_ME_FECPM_ALARM_FEC_SEC              HI_OMCI_ME_ALARM_BITMAP(4)

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
static hi_omci_tapi_stat_fecpm_s gst_fecpm_history;
static hi_omci_tapi_stat_fecpm_s gst_fecpm_prev_history;

/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/

/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_fecpm_alarm_get
 Description : FEC统计告警检查
 Input Parm  : hi_void *pv_data,
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_fecpm_alarm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_alarm_timer_s *pst_timer = (hi_omci_me_alarm_timer_s *)pv_data;
	hi_omci_me_fec_pm_s st_fecpm;
	hi_omci_me_threshold1_s st_threshold1;
	hi_int32 i_ret = 0;
	hi_ushort16 us_bitmap_curr = 0;
	hi_ushort16 us_bitmap_hist;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->ui_instid;

	HI_OS_MEMSET_S(&st_fecpm, sizeof(st_fecpm), 0, sizeof(st_fecpm));
	HI_OS_MEMSET_S(&st_threshold1, sizeof(st_threshold1), 0, sizeof(st_threshold1));

	/* 读取当前芯片FEC统计 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_FEC_PM_E, us_instid, &st_fecpm);
	HI_OMCI_RET_CHECK(i_ret);

	/* 获取各参数阈值 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_THRESHOLD_DATA1_E, st_fecpm.us_thresid, &st_threshold1);
	HI_OMCI_RET_CHECK(i_ret);

	/* 比较是否超出阈值，确定是告警产生，还是撤销 */
	if (st_fecpm.ui_correctbytes > st_threshold1.ui_thres1) {
		us_bitmap_curr |= HI_OMCI_ME_FECPM_ALARM_CORRECT_BYTES;
	}

	if (st_fecpm.ui_correctcode > st_threshold1.ui_thres2) {
		us_bitmap_curr |= HI_OMCI_ME_FECPM_ALARM_CORRECT_CODE_WORDS;
	}

	if (st_fecpm.ui_uncorrectcode > st_threshold1.ui_thres3) {
		us_bitmap_curr |= HI_OMCI_ME_FECPM_ALARM_UNCORRECT_CODE_WORDS;
	}

	if (st_fecpm.us_fecseconds > st_threshold1.ui_thres4) {
		us_bitmap_curr |= HI_OMCI_ME_FECPM_ALARM_FEC_SEC;
	}

	/* 读取数据库当前告警状态 */
	i_ret = hi_omci_sql_alarm_inst_get(HI_OMCI_PRO_ME_FEC_PM_E, us_instid, &us_bitmap_hist);
	HI_OMCI_RET_CHECK(i_ret);

	/* 如果告警状态发生变化，更新数据库的告警状态，并发生告警消息 */
	if (us_bitmap_curr != us_bitmap_hist) {
		i_ret = hi_omci_sql_alarm_inst_set(HI_OMCI_PRO_ME_FEC_PM_E, us_instid, us_bitmap_curr);
		HI_OMCI_RET_CHECK(i_ret);

		i_ret = hi_omci_proc_sendalarm(HI_OMCI_PRO_ME_FEC_PM_E, us_instid, us_bitmap_curr);
		HI_OMCI_RET_CHECK(i_ret);
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_fecpm_stat_get
 Description : fec pm统计获取
 Input Parm  : hi_void *pv_data       FEC历史累计统计数据
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_fecpm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_stat_timer_s *pst_timer = (hi_omci_me_stat_timer_s *)pv_data;
	hi_omci_me_fec_pm_s st_entity;
	hi_omci_tapi_stat_fecpm_s st_stat;
	hi_int32 i_ret;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->ui_instid;
	HI_OS_MEMSET_S(&st_stat, sizeof(hi_omci_tapi_stat_fecpm_s), 0, sizeof(hi_omci_tapi_stat_fecpm_s));

	/* 读取芯片当前统计 */
	i_ret = hi_omci_tapi_stat_fec_get(&st_stat);
	HI_OMCI_RET_CHECK(i_ret);

	/* 与历史数据相减，将结果保存到数据库 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_FEC_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	st_entity.uc_endtime++;
	st_entity.ui_correctbytes = st_stat.ui_correctbytes - gst_fecpm_history.ui_correctbytes;
	st_entity.ui_correctcode = st_stat.ui_correctcode - gst_fecpm_history.ui_correctcode;
	st_entity.ui_uncorrectcode = st_stat.ui_uncorrectcode - gst_fecpm_history.ui_uncorrectcode;
	st_entity.ui_totalcode = st_stat.ui_totalcode - gst_fecpm_history.ui_totalcode;
	st_entity.us_fecseconds = st_stat.us_fecseconds - gst_fecpm_history.us_fecseconds;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_FEC_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	/* 将当前统计更新到历史统计 */
	HI_OS_MEMCPY_S(&gst_fecpm_prev_history, sizeof(gst_fecpm_prev_history), &gst_fecpm_history,
		       sizeof(gst_fecpm_prev_history));
	HI_OS_MEMCPY_S(&gst_fecpm_history, sizeof(gst_fecpm_history), &st_stat, sizeof(gst_fecpm_history));


	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}


hi_int32 hi_omci_me_fecpm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_fec_pm_s *pst_entry = (hi_omci_me_fec_pm_s *)pv_data;
	hi_omci_me_fec_pm_s st_entity;

	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_FEC_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	st_entity.ui_correctbytes = gst_fecpm_history.ui_correctbytes - gst_fecpm_prev_history.ui_correctbytes;
	st_entity.ui_correctcode = gst_fecpm_history.ui_correctcode - gst_fecpm_prev_history.ui_correctcode;
	st_entity.ui_uncorrectcode = gst_fecpm_history.ui_uncorrectcode - gst_fecpm_prev_history.ui_uncorrectcode;
	st_entity.ui_totalcode = gst_fecpm_history.ui_totalcode - gst_fecpm_prev_history.ui_totalcode;
	st_entity.us_fecseconds = gst_fecpm_history.us_fecseconds - gst_fecpm_prev_history.us_fecseconds;


	/*将数据保存到实例数据中*/
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_FEC_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;

}


hi_int32 hi_omci_me_fecpm_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_fec_pm_s *pst_entry = (hi_omci_me_fec_pm_s *)pv_data;
	hi_omci_me_fec_pm_s st_entity;
	hi_omci_tapi_stat_fecpm_s st_stat;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_FEC_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_stat, sizeof(hi_omci_tapi_stat_fecpm_s), 0, sizeof(hi_omci_tapi_stat_fecpm_s));
	/* 读取芯片当前统计 */
	i_ret = hi_omci_tapi_stat_fec_get(&st_stat);
	HI_OMCI_RET_CHECK(i_ret);

	st_entity.ui_correctbytes = st_stat.ui_correctbytes - gst_fecpm_history.ui_correctbytes;
	st_entity.ui_correctcode = st_stat.ui_correctcode - gst_fecpm_history.ui_correctcode;
	st_entity.ui_uncorrectcode = st_stat.ui_uncorrectcode - gst_fecpm_history.ui_uncorrectcode;
	st_entity.ui_totalcode = st_stat.ui_totalcode - gst_fecpm_history.ui_totalcode;
	st_entity.us_fecseconds = st_stat.us_fecseconds - gst_fecpm_history.us_fecseconds;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_FEC_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_fec_pm_create
 Description : FEC performance monitoring history data create
 Input Parm  : hi_void *pv_data,
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_fecpm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_fec_pm_s *pst_entry = (hi_omci_me_fec_pm_s *)pv_data;
	hi_omci_me_stat_timer_s st_fecpm_stat_timer;
	hi_omci_me_alarm_timer_s st_fecpm_alarm_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	/* 清空历史统计 */
	HI_OS_MEMSET_S(&gst_fecpm_prev_history, sizeof(gst_fecpm_prev_history), 0, sizeof(gst_fecpm_prev_history));
	HI_OS_MEMSET_S(&gst_fecpm_history, sizeof(gst_fecpm_history), 0, sizeof(gst_fecpm_history));

	/* 创建FEC PM告警实体 */
	i_ret = hi_omci_sql_alarm_inst_create(HI_OMCI_PRO_ME_FEC_PM_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	/* 创建15分钟定时器，获取统计 */
	HI_OS_MEMSET_S(&st_fecpm_stat_timer, sizeof(st_fecpm_stat_timer), 0, sizeof(st_fecpm_stat_timer));
	st_fecpm_stat_timer.ui_meid = HI_OMCI_PRO_ME_FEC_PM_E;
	st_fecpm_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_start(&st_fecpm_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	/* 创建15分钟定时器，上报告警 */
	HI_OS_MEMSET_S(&st_fecpm_alarm_timer, sizeof(st_fecpm_alarm_timer), 0, sizeof(st_fecpm_alarm_timer));
	st_fecpm_alarm_timer.ui_meid = HI_OMCI_PRO_ME_FEC_PM_E;
	st_fecpm_alarm_timer.ui_instid = us_instid;
	st_fecpm_alarm_timer.ui_timeout = HI_OMCI_ME_ALARM_COMM_TIMEOUT;
	i_ret = hi_omci_me_alarm_start(&st_fecpm_alarm_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_fecpm_delete
 Description : FEC performance monitoring history data delete
 Input Parm  : hi_void *pv_data,
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_fecpm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_fec_pm_s *pst_entry = (hi_omci_me_fec_pm_s *)pv_data;
	hi_omci_me_stat_timer_s st_fecpm_stat_timer;
	hi_omci_me_alarm_timer_s st_fecpm_alarm_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	/* 删除FEC PM告警实体 */
	i_ret = hi_omci_sql_alarm_inst_delete(HI_OMCI_PRO_ME_FEC_PM_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	/* 删除定时器 */
	HI_OS_MEMSET_S(&st_fecpm_stat_timer, sizeof(st_fecpm_stat_timer), 0, sizeof(st_fecpm_stat_timer));
	st_fecpm_stat_timer.ui_meid = HI_OMCI_PRO_ME_FEC_PM_E;
	st_fecpm_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_stop(&st_fecpm_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_fecpm_alarm_timer, sizeof(st_fecpm_alarm_timer), 0, sizeof(st_fecpm_alarm_timer));
	st_fecpm_alarm_timer.ui_meid = HI_OMCI_PRO_ME_FEC_PM_E;
	st_fecpm_alarm_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_alarm_stop(&st_fecpm_alarm_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}



/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

