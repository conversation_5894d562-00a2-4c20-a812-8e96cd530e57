/******************************************************************************

                  版权所有 (C), 2009-2019, 华为技术有限公司

 ******************************************************************************
  文 件 名   : hi_omci_me_mac_port_design.c
  版 本 号   : 初稿
  作    者   : h66243
  生成日期   : D2012_02_21
  功能描述   : MAC bridge port design data
  注释              :该实体用于获取各端口的状态，暂可不实现
******************************************************************************/
#include "hi_omci.h"
#include "hi_omci_me_l2_def.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

hi_uint32 hi_omci_me_mac_port_design_create_after(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_uint32 ui_ret = HI_RET_SUCC;
	hi_ushort16  us_instid = 0;
	hi_omci_me_mac_port_design_s *pst_portdesign = pv_data;
	hi_omci_me_mac_port_design_s st_portdesign;
	/*TODO : you need to add code here*/
	hi_uchar8 uc_designroot[24] = {"SD5113--SD5113--00050000"};

	us_instid = pst_portdesign->st_msghead.us_instid;
	ui_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_MAC_PORT_DES_DATA_E, us_instid);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_MAC_PORT_DES_DATA_E, us_instid, &st_portdesign);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	st_portdesign.uc_portstate = HI_OMCI_DESIGN_PORT_STATE_DISABLE_E;
	HI_OS_MEMCPY_S(st_portdesign.uc_designroot, sizeof(st_portdesign.uc_designroot), uc_designroot, 24);

	ui_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_MAC_PORT_DES_DATA_E, us_instid, &st_portdesign);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_uint32 hi_omci_me_mac_design_get_bef(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	//hi_uint32 ui_ret = HI_RET_SUCC;
	return HI_RET_SUCC;
#if 0
	hi_omci_me_mac_port_design_s *pst_portdesign = pv_data;
	hi_omci_me_mac_port_design_s st_portdesign;

	/*TODO : you need to add code here*/
	hi_ushort16  us_instid = 0;
	hi_uint32    ui_portstate = HI_OMCI_DESIGN_PORT_STATE_FORWARDING_E;
	hi_uchar8 uc_portid = 0 ;
	us_instid = pst_portdesign->st_msghead.us_instid;
	ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_MAC_PORT_DES_DATA_E, us_instid, &st_portdesign);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	/* get current port status from device */
	ui_ret = hi_lsw_port_get_stpstate(uc_portid, &ui_portstate);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	switch (ui_portstate) {
	case HI_ETH_STP_FORWARD_E:
		st_portdesign.uc_portstate = HI_OMCI_DESIGN_PORT_STATE_FORWARDING_E;
		break;

	case HI_ETH_STP_LEARN_E:
		st_portdesign.uc_portstate = HI_OMCI_DESIGN_PORT_STATE_LEARNING_E;
		break;

	case HI_ETH_STP_LISTEN_E:
		st_portdesign.uc_portstate = HI_OMCI_DESIGN_PORT_STATE_LISTENING_E;
		break;

	case HI_ETH_STP_BLOCK_E:
		st_portdesign.uc_portstate = HI_OMCI_DESIGN_PORT_STATE_BLOCKING_E;
		break;

	case HI_ETH_STP_CLOSE_E:
		st_portdesign.uc_portstate = HI_OMCI_DESIGN_PORT_STATE_STP_OFF_E;
		break;

	default:
		break;
	}

	ui_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_MAC_PORT_DES_DATA_E, us_instid, &st_portdesign);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
#endif
}

hi_uint32 hi_omci_me_mac_port_design_del_before(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_uint32 ui_ret = HI_RET_SUCC;
	hi_omci_me_mac_port_design_s *pst_portdesign = pv_data;
	hi_ushort16  us_instid = 0;

	us_instid = pst_portdesign->st_msghead.us_instid;
	ui_ret = hi_omci_ext_del_inst(HI_OMCI_PRO_ME_MAC_PORT_DES_DATA_E, us_instid);
	if ((HI_RET_SUCC != ui_ret)) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	hi_omci_systrace(ui_ret, 0, 0, 0, 0);
	return ui_ret;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


