/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_mac_port_bridge_table.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-28
  Description: MAC bridge port bridge table data
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
typedef union {
    struct {
        hi_ushort16 filter: 1;
        hi_ushort16 resv1: 1;
        hi_ushort16 dynamic: 1;
        hi_ushort16 resv2: 1;
        hi_ushort16 age: 12;
    } bits;

    hi_ushort16 ui_val;
} hi_omci_me_macport_table_info_s;

extern hi_uint32 g_table_avail_size;
extern uint32_t g_ext_tab_avail_size;
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_macport_table_get
 Description : MAC bridge port bridge table data get
               操作数据库前处理
 Input Parm  : hi_void *pv_data,
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_macport_table_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_me_mac_port_table_s *pst_entry = (hi_omci_me_mac_port_table_s *)pv_data;
    hi_omci_tapi_br_table_s *pst_table=NULL;
    hi_omci_tapi_br_table_s *pst_omci_tapitbltmp;
    omci_me_bridge_table_s *pst_brtbl;
    omci_me_bridge_table_s *pst_brtbltmp;
    omci_me_ext_bridge_table_s *pst_ext_brtbl;
    omci_me_ext_bridge_table_s *pst_ext_brtbltmp;
    hi_omci_me_macport_table_info_s st_macinfo;
    hi_int32 i_ret;
    hi_uint32 ui_tabnum;
    hi_uint32 ui_index;
    hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;
    hi_ushort16 us_mask = pst_entry->st_msghead.us_attmask;
    hi_uchar8 uc_portid;
    hi_ushort16 us_vid=0x01;
    hi_uint32 ui_brtabnum;

    i_ret = hi_omci_me_macportcfg_get_portid(us_instid, &uc_portid);
    HI_OMCI_RET_CHECK(i_ret);

#if 0
    if (HI_OMCI_TAPI_VEIP_E == uc_portid) {
        hi_omci_systrace(HI_RET_FAIL, 0, 0, 0, 0);
        return (hi_int32)HI_RET_FAIL;
    }
#endif

    if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR1)) {
        i_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_MAC_PORT_TAB_DATA_E, HI_OMCI_ATTR1, &ui_tabnum);
        HI_OMCI_RET_CHECK(i_ret);
        
        // i_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_TAB_DATA_E, us_instid, HI_OMCI_ATTR2, &ui_vlan);
        // HI_OMCI_RET_CHECK(i_ret);
        
        hi_omci_debug("ui_tabnum = %u,uc_portid = %u\n", ui_tabnum, uc_portid);
        pst_table = (hi_omci_tapi_br_table_s *)hi_os_malloc(sizeof(hi_omci_tapi_br_table_s) * ui_tabnum);
        if (HI_NULL == pst_table) {
            hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
            return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
        }
        HI_OS_MEMSET_S(pst_table, sizeof(hi_omci_tapi_br_table_s) * ui_tabnum, 0, sizeof(hi_omci_tapi_br_table_s) * ui_tabnum);
        
        i_ret = hi_omci_tapi_br_table_get((hi_omci_tapi_port_e)uc_portid, &ui_tabnum, pst_table);
        if (i_ret != HI_RET_SUCC) {
            hi_os_free(pst_table);
            hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
            return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
        }
        
        hi_omci_debug("ui_tabnum = %u\n", ui_tabnum);
        if (ui_tabnum == 0) {
            g_table_avail_size = g_ext_tab_avail_size = 0;
            hi_os_free(pst_table);
            hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
            return HI_RET_SUCC;
        }
        pst_brtbl = (omci_me_bridge_table_s *)hi_os_malloc(sizeof(omci_me_bridge_table_s) * ui_tabnum);
        if (HI_NULL == pst_brtbl) {
            hi_os_free(pst_table);
            hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
            return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
        }

        HI_OS_MEMSET_S(pst_brtbl, sizeof(omci_me_bridge_table_s) * ui_tabnum, 0, sizeof(omci_me_bridge_table_s) * ui_tabnum);

        g_table_avail_size = g_ext_tab_avail_size= ui_tabnum * sizeof(omci_me_bridge_table_s);

        for (ui_index = 0, pst_omci_tapitbltmp = pst_table, pst_brtbltmp = pst_brtbl;
             ui_index < ui_tabnum;
             ui_index++, pst_omci_tapitbltmp++, pst_brtbltmp++) {
            st_macinfo.ui_val = 0;
            st_macinfo.bits.dynamic = HI_OMCI_PORT_TAB_MAC_DYNAMICAL;
            st_macinfo.bits.age = pst_omci_tapitbltmp->ui_age;
            st_macinfo.bits.filter = HI_OMCI_PORT_TAB_PACKET_FORWARD;
            HI_OS_MEMCPY_S(pst_brtbltmp->uc_macaddr, sizeof(pst_brtbltmp->uc_macaddr), pst_omci_tapitbltmp->aui_mac, HI_MAC_LEN);
            pst_brtbltmp->us_info = htons(st_macinfo.ui_val);

            hi_omci_debug("us_info = 0x%x uc_macaddr = %02x:%02x:%02x:%02x:%02x:%02x\n", pst_brtbltmp->us_info,
                      pst_brtbltmp->uc_macaddr[0], pst_brtbltmp->uc_macaddr[1],
                      pst_brtbltmp->uc_macaddr[2], pst_brtbltmp->uc_macaddr[3],
                      pst_brtbltmp->uc_macaddr[4], pst_brtbltmp->uc_macaddr[5]);
        }

        hi_os_free(pst_table);

        i_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_MAC_PORT_TAB_DATA_E, us_instid, HI_OMCI_ATTR1, pst_brtbl);
        hi_os_free(pst_brtbl);
        HI_OMCI_RET_CHECK(i_ret);
        hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
        return HI_RET_SUCC;
    }
    /* Bohannon: hwtc extened attr3 */
    else if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR3)) 
    {
        i_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_MAC_PORT_TAB_DATA_E, HI_OMCI_ATTR3, &ui_tabnum);
        HI_OMCI_RET_CHECK(i_ret);
        
        // i_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_TAB_DATA_E, us_instid, HI_OMCI_ATTR2, &ui_vlan);
        // HI_OMCI_RET_CHECK(i_ret);
        
        hi_omci_debug("ui_tabnum = %u,uc_portid = %u\n", ui_tabnum, uc_portid);
        pst_table = (hi_omci_tapi_br_table_s *)hi_os_malloc(sizeof(hi_omci_tapi_br_table_s) * ui_tabnum);
        if (HI_NULL == pst_table) {
            hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
            return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
        }
        HI_OS_MEMSET_S(pst_table, sizeof(hi_omci_tapi_br_table_s) * ui_tabnum, 0, sizeof(hi_omci_tapi_br_table_s) * ui_tabnum);
        ui_brtabnum = ui_tabnum;
        i_ret = hi_omci_tapi_br_table_get((hi_omci_tapi_port_e)uc_portid, &ui_brtabnum, pst_table);
        if (i_ret != HI_RET_SUCC) {
            hi_os_free(pst_table);
            hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
            return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
        }
        
        hi_omci_debug("ui_brtabnum = %u\n", ui_brtabnum);
        if (ui_brtabnum == 0) {
            g_table_avail_size = g_ext_tab_avail_size = 0;
            HI_OMCI_RET_CHECK(i_ret);
            hi_os_free(pst_table);
            hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
            return HI_RET_SUCC;
        }
        ui_tabnum = ui_brtabnum < ui_tabnum?ui_brtabnum:ui_tabnum;
        pst_ext_brtbl = (omci_me_ext_bridge_table_s *)hi_os_malloc(sizeof(omci_me_ext_bridge_table_s) * ui_tabnum);
        if (HI_NULL == pst_ext_brtbl) {
            hi_os_free(pst_table);
            hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
            return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
        }

        HI_OS_MEMSET_S(pst_ext_brtbl, sizeof(omci_me_ext_bridge_table_s) * ui_tabnum, 0, sizeof(omci_me_ext_bridge_table_s) * ui_tabnum);

        g_table_avail_size = g_ext_tab_avail_size = ui_tabnum * sizeof(omci_me_ext_bridge_table_s);

        for (ui_index = 0, pst_omci_tapitbltmp = pst_table, pst_ext_brtbltmp = pst_ext_brtbl;
             ui_index < ui_tabnum;
             ui_index++, pst_omci_tapitbltmp++, pst_ext_brtbltmp++) {
            st_macinfo.ui_val = 0;
            st_macinfo.bits.dynamic = HI_OMCI_PORT_TAB_MAC_DYNAMICAL;
            st_macinfo.bits.age = pst_omci_tapitbltmp->ui_age;
            st_macinfo.bits.filter = HI_OMCI_PORT_TAB_PACKET_FORWARD;
            HI_OS_MEMCPY_S(pst_ext_brtbltmp->uc_macaddr, sizeof(pst_ext_brtbltmp->uc_macaddr), pst_omci_tapitbltmp->aui_mac, HI_MAC_LEN);
            pst_ext_brtbltmp->us_info = htons(st_macinfo.ui_val);
            pst_ext_brtbltmp->us_vlan = htons(us_vid);

            hi_omci_debug("us_info = 0x%x uc_macaddr = %02x:%02x:%02x:%02x:%02x:%02x\n", pst_ext_brtbltmp->us_info,
                      pst_ext_brtbltmp->uc_macaddr[0], pst_ext_brtbltmp->uc_macaddr[1],
                      pst_ext_brtbltmp->uc_macaddr[2], pst_ext_brtbltmp->uc_macaddr[3],
                      pst_ext_brtbltmp->uc_macaddr[4], pst_ext_brtbltmp->uc_macaddr[5]);
        }

        hi_os_free(pst_table);

        i_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_MAC_PORT_TAB_DATA_E, us_instid, HI_OMCI_ATTR3, pst_ext_brtbl);
        hi_os_free(pst_ext_brtbl);
        HI_OMCI_RET_CHECK(i_ret);
        hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
        return HI_RET_SUCC;
    }
    hi_os_free(pst_table);
    return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_me_macport_table_create
  Description  : MAC bridge port bridge table data create
  Input Param  : hi_ushort16 us_instid
  Output Param ：
  Return       :
*****************************************************************************/
hi_uint32 hi_omci_me_macport_table_create(hi_ushort16 us_instid)
{
    hi_uint32 ui_ret;

    ui_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_MAC_PORT_TAB_DATA_E, us_instid);
    HI_OMCI_RET_CHECK(ui_ret);

    return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_me_macport_table_del
  Description  : MAC bridge port bridge table data delete
  Input Param  : hi_ushort16 us_instid
  Output Param ：
  Return       :
*****************************************************************************/
hi_uint32 hi_omci_me_macport_table_delete(hi_ushort16 us_instid)
{
    hi_uint32 ui_ret;

    ui_ret = hi_omci_ext_del_inst(HI_OMCI_PRO_ME_MAC_PORT_TAB_DATA_E, us_instid);
    HI_OMCI_RET_CHECK(ui_ret);

    return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
