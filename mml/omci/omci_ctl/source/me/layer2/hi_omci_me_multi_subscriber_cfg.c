/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_multi_subscriber_cfg.c
  Version    : 初稿
  Author     : owen
  Creation   : 2015-1-21
  Description: Multicast subscriber config info
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
#define HI_OMCI_ME_MULTISUBS_CFG_DEFGROUP   256

#define HI_OMCI_ME_MULTISUBS_CFG_IS_MAXGROUP(mask) HI_OMCI_CHECK_ATTR((mask), HI_OMCI_ATTR3)
#define HI_OMCI_ME_MULTISUBS_CFG_IS_PKGTAB(mask) HI_OMCI_CHECK_ATTR((mask), HI_OMCI_ATTR6)

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/

/******************************************************************************
 Function    : __omci_me_multisubs_cfg_table_printf
 Description : 表项打印
 Input Parm  : 无
 Output Parm : N/A

 Return      : hi_void
******************************************************************************/
hi_void __omci_me_multisubs_cfg_table_printf(hi_uchar8 *puc_table, hi_uint32 ui_len)
{
	hi_uint32 ui_size = sizeof(hi_omci_me_multisubs_pkgtab_s);
	hi_uint32 ui_index;

	if (!hi_log_print_on((hi_uint32)HI_SUBMODULE_OMCI_COM, HI_DBG_LEVEL_DEBUG)
	    && !hi_log_print_on((hi_uint32)HI_SYSBASE_GLB, HI_DBG_LEVEL_DEBUG)) {
		return;
	}

	hi_os_printf("================== start =====================\n");

	for (ui_index = 0; ui_index < ui_len; ui_index += ui_size, puc_table += ui_size) {
		HI_PRINT_MEM(ui_size, puc_table);
	}

	hi_os_printf("================== end =====================\n");
}

/******************************************************************************
 Function    : __omci_me_multisubs_cfg_groupnum_set
 Description : Multicast subscriber config info set
               数据库刷新后执行此函数
 Input Parm  : hi_void *pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 __omci_me_multisubs_cfg_groupnum_set(hi_ushort16 us_instid, hi_ushort16 us_groupnum)
{
	hi_uint32 ui_ret;
	hi_uchar8 uc_portid;

	ui_ret = hi_omci_me_macportcfg_get_portid(us_instid, &uc_portid);
	HI_OMCI_RET_CHECK(ui_ret);

	if (!us_groupnum) {
		us_groupnum = HI_OMCI_ME_MULTISUBS_CFG_DEFGROUP;
	}

	ui_ret = hi_omci_tapi_mc_group_num_set((hi_omci_tapi_port_e)uc_portid, us_groupnum);
	HI_OMCI_RET_CHECK(ui_ret);

	hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_multisubs_cfg_set
 Description : Multicast subscriber config info set
               数据库刷新前执行此函数
 Input Parm  : hi_void *pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_multisubs_cfg_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_multisubs_cfg_s *pst_entry = (hi_omci_me_multisubs_cfg_s *)pv_data;
	hi_omci_me_multisubs_pkgtab_s *pst_pkgtab;
	hi_omci_me_multisubs_pkgtab_s *pst_tmp;
	hi_omci_me_multisubs_pkgtab_s st_empty;
	hi_omci_me_multisubs_pkgtab_tabctrl st_tabctrl;
	hi_uint32 ui_ret;
	hi_uint32 ui_tabnum;
	hi_uint32 ui_size;
	hi_uint32 ui_index;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;
	hi_ushort16 us_mask = pst_entry->st_msghead.us_attmask;
	hi_omci_tapi_sysinfo_s st_info;

	ui_ret = hi_omci_tapi_sysinfo_get(&st_info);
	HI_OMCI_RET_CHECK(ui_ret);
	if (HI_OMCI_ME_ONU_CAPABILITY_TYPE_HGU_E == st_info.ui_product_mode) {
		return HI_RET_SUCC;
	}

	if (HI_OMCI_ME_MULTISUBS_CFG_IS_MAXGROUP(us_mask)) {
		ui_ret = __omci_me_multisubs_cfg_groupnum_set(pst_entry->st_msghead.us_instid, pst_entry->us_maxgrp);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	if (HI_OMCI_ME_MULTISUBS_CFG_IS_PKGTAB(us_mask)) {
		__omci_me_multisubs_cfg_table_printf((hi_uchar8 *)&pst_entry->st_pkgtab, sizeof(hi_omci_me_multisubs_pkgtab_s));

		st_tabctrl.us_value = (hi_ushort16)ntohs(pst_entry->st_pkgtab.st_tabctrl.us_value);

		hi_omci_debug("table control operation = %hu, rowkey = %hu\n",
			      st_tabctrl.bits.opt, st_tabctrl.bits.rowkey);

		if (st_tabctrl.bits.opt < HI_OMCI_ME_MULTISUBS_PKGTAB_SET) {
			hi_omci_l2_systrace(HI_OMCI_PRO_ERR_PARA_ERR_E, 0, 0, 0, 0);
			return HI_OMCI_PRO_ERR_PARA_ERR_E;
		}

		ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_MULTI_SUBS_CONFIG_E, HI_OMCI_ATTR6, &ui_tabnum);
		HI_OMCI_RET_CHECK(ui_ret);

		hi_omci_debug("ui_tabnum = %u\n", ui_tabnum);

		ui_size = sizeof(hi_omci_me_multisubs_pkgtab_s) * ui_tabnum;
		pst_pkgtab = (hi_omci_me_multisubs_pkgtab_s *)hi_os_malloc(ui_size);
		if (HI_NULL == pst_pkgtab) {
			hi_omci_l2_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
			return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
		}

		HI_OS_MEMSET_S(pst_pkgtab, sizeof(ui_size), 0, sizeof(ui_size));

		ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MULTI_SUBS_CONFIG_E,
					      us_instid, HI_OMCI_ATTR6, pst_pkgtab);
		if (HI_RET_SUCC != ui_ret) {
			hi_os_free(pst_pkgtab);
			hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
			hi_omci_systrace(ui_ret, 0, 0, 0, 0);
			return ui_ret;
		}

		__omci_me_multisubs_cfg_table_printf((hi_uchar8 *)pst_pkgtab, ui_size);

		switch (st_tabctrl.bits.opt) {
		case HI_OMCI_ME_MULTISUBS_PKGTAB_SET:

			HI_OS_MEMSET_S(&st_empty, sizeof(st_empty), 0, sizeof(st_empty));

			/* 修改已有表项 */
			for (ui_index = 0, pst_tmp = pst_pkgtab;
			     ui_index < ui_tabnum;
			     ui_index++, pst_tmp++) {
				if (pst_entry->st_pkgtab.st_tabctrl.bits.rowkey == pst_tmp->st_tabctrl.bits.rowkey) {
					HI_OS_MEMCPY_S(pst_tmp, sizeof(*pst_tmp), &pst_entry->st_pkgtab, sizeof(pst_entry->st_pkgtab));
					break;
				}
			}

			/* 添加新表项 */
			if (ui_index >= ui_tabnum) {
				for (ui_index = 0, pst_tmp = pst_pkgtab;
				     ui_index < ui_tabnum;
				     ui_index++, pst_tmp++) {
					if (0 == hi_os_memcmp(&st_empty, pst_tmp, sizeof(st_empty))) {
						HI_OS_MEMCPY_S(pst_tmp, sizeof(*pst_tmp), &pst_entry->st_pkgtab, sizeof(pst_entry->st_pkgtab));
						break;
					}
				}
			}

			if (ui_index >= ui_tabnum) {
				hi_omci_debug("[INFO]:package table is full\n");
			}

			break;

		case HI_OMCI_ME_MULTISUBS_PKGTAB_DEL:

			for (ui_index = 0, pst_tmp = pst_pkgtab;
			     ui_index < ui_tabnum;
			     ui_index++, pst_tmp++) {
				if (pst_entry->st_pkgtab.st_tabctrl.bits.rowkey == pst_tmp->st_tabctrl.bits.rowkey) {
					HI_OS_MEMSET_S(pst_tmp, sizeof(hi_omci_me_multisubs_pkgtab_s), 0, sizeof(hi_omci_me_multisubs_pkgtab_s));
					break;
				}
			}

			break;

		case HI_OMCI_ME_MULTISUBS_PKGTAB_CLR:
			HI_OS_MEMSET_S(pst_pkgtab, ui_size, 0, ui_size);
			break;

		default :
			break;
		}

		__omci_me_multisubs_cfg_table_printf((hi_uchar8 *)pst_pkgtab, ui_size);

		ui_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_MULTI_SUBS_CONFIG_E,
					      us_instid, HI_OMCI_ATTR6, pst_pkgtab);
		if (HI_RET_SUCC != ui_ret) {
			hi_os_free(pst_pkgtab);
			hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
			hi_omci_systrace(ui_ret, 0, 0, 0, 0);
			return ui_ret;
		}

		hi_os_free(pst_pkgtab);
	}

	hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_multisubs_cfg_create
 Description : Multicast subscriber config info create
               数据库刷新后执行此函数
 Input Parm  : hi_void *pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_multisubs_cfg_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_multisubs_cfg_s *pst_entry = (hi_omci_me_multisubs_cfg_s *)pv_data;
	hi_omci_me_multi_oper_profile_s st_profile;
	hi_omci_me_multisubs_pkgtab_s *pst_pkgtab;
	hi_uint32 ui_tabnum;
	hi_uint32 ui_size;
	hi_uint32 ui_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;
	hi_omci_tapi_sysinfo_s st_info;

	ui_ret = hi_omci_tapi_sysinfo_get(&st_info);
	HI_OMCI_RET_CHECK(ui_ret);
	if (HI_OMCI_ME_ONU_CAPABILITY_TYPE_HGU_E == st_info.ui_product_mode) {
		return HI_RET_SUCC;
	}

	ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_MULTI_OPER_PROFILE_E, pst_entry->us_profile_ptr, &st_profile);
	HI_OMCI_RET_CHECK(ui_ret);

	st_profile.st_msghead.us_instid = pst_entry->us_profile_ptr;
	ui_ret = hi_omci_me_multi_oper_create(&st_profile, sizeof(st_profile), pui_outlen);
	HI_OMCI_RET_CHECK(ui_ret);

	ui_ret = __omci_me_multisubs_cfg_groupnum_set(pst_entry->st_msghead.us_instid, pst_entry->us_maxgrp);
	HI_OMCI_RET_CHECK(ui_ret);

	ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_MULTI_SUBS_CONFIG_E, HI_OMCI_ATTR6, &ui_tabnum);
	HI_OMCI_RET_CHECK(ui_ret);

	ui_size = sizeof(hi_omci_me_multisubs_pkgtab_s) * ui_tabnum;
	pst_pkgtab = (hi_omci_me_multisubs_pkgtab_s *)hi_os_malloc(ui_size);
	if (HI_NULL == pst_pkgtab) {
		hi_omci_l2_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	HI_OS_MEMSET_S(pst_pkgtab, ui_size, 0, ui_size);

	ui_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_MULTI_SUBS_CONFIG_E,
				      us_instid, HI_OMCI_ATTR6, pst_pkgtab);
	if (HI_RET_SUCC != ui_ret) {
		hi_os_free(pst_pkgtab);
		hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	hi_os_free(pst_pkgtab);

	hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_multisubs_cfg_delete
 Description : Multicast subscriber config info delete
               数据库刷新前执行此函数
 Input Parm  : hi_void *pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_multisubs_cfg_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_multisubs_cfg_s *pst_entry = (hi_omci_me_multisubs_cfg_s *)pv_data;
	hi_uint32 ui_ret;
	hi_omci_tapi_sysinfo_s st_info;

	ui_ret = hi_omci_tapi_sysinfo_get(&st_info);
	HI_OMCI_RET_CHECK(ui_ret);
	if (HI_OMCI_ME_ONU_CAPABILITY_TYPE_HGU_E == st_info.ui_product_mode) {
		return HI_RET_SUCC;
	}

	ui_ret = __omci_me_multisubs_cfg_groupnum_set(pst_entry->st_msghead.us_instid, 0);
	HI_OMCI_RET_CHECK(ui_ret);

	hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
