/* *****************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_tr069_management.c
  Version    : 初稿
  Author     :
  Creation   :
  Description:
***************************************************************************** */

/* ****************************************************************************
 * INCLUDE                                    *
 * *************************************************************************** */
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

/* ****************************************************************************
 * LOCAL_DEFINE                               *
 * *************************************************************************** */
/* ****************************************************************************
 * LOCAL_TYPEDEF                              *
 * *************************************************************************** */
/* ****************************************************************************
 * LOCAL_VARIABLE                             *
 * *************************************************************************** */
extern hi_uint32 gui_omci_voip_flag;
hi_uint32 gui_omci_tr069_management_mask = 0;
extern hi_omci_tapi_voip_extvlan_s g_st_extvlan_table;
static hi_uint32 gui_omci_tr069_management_wanconfig = 0;
/* ****************************************************************************
 * LOCAL_FUNCTION                             *
 * *************************************************************************** */
/* ****************************************************************************
 * PUBLIC_FUNCTION                            *
 * *************************************************************************** */
/* ****************************************************************************
 * INIT_EXIT                                  *
 * *************************************************************************** */
hi_int32 hi_omci_me_tr069_management_init()
{
	hi_omci_me_tr069_mng_server_s st_entry;
	hi_int32 i_ret;
	hi_ushort16 us_instid = HI_OMCI_ME_VEIP_INSTID;

	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_TR069_MANAGEMENT_SERVER_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);
	HI_OS_MEMSET_S(&st_entry, sizeof(st_entry), 0, sizeof(st_entry));
	st_entry.st_msghead.us_instid = us_instid;
	st_entry.uc_adminstate = 1;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_TR069_MANAGEMENT_SERVER_E, us_instid, &st_entry);
	HI_OMCI_RET_CHECK(i_ret);
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_me_tr069_management_set
 Description : TR-069 management server
               在数据库操作后处理
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_me_tr069_management_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_uint32 i_ret = HI_RET_SUCC;
	hi_uint32 ui_index = 0;
	hi_uint32 ui_status = 0;
	hi_omci_me_tr069_mng_server_s *pst_entity = (hi_omci_me_tr069_mng_server_s *)pv_data;
	// hi_omci_me_tr069_mng_server_s st_entity;
	hi_omci_me_network_addr_s st_network;
	hi_omci_me_large_string_s st_string;
	hi_omci_me_auth_secur_method_s st_auth;
	hi_omci_tapi_tr069_server_s st_server;
	hi_omci_me_veip_s st_veip;
	hi_omci_me_udptcp_cfg_s st_udptcp;
	hi_omci_tapi_voip_iphost_s st_tapi_iphost;
	hi_omci_me_iphost_cfg_s st_iphost;
	HI_OS_MEMSET_S(&st_network, sizeof(st_network), 0, sizeof(st_network));
	HI_OS_MEMSET_S(&st_veip, sizeof(st_veip), 0, sizeof(st_veip));
	hi_ushort16 us_instid = pst_entity->st_msghead.us_instid;
	hi_ushort16 us_mask = pst_entity->st_msghead.us_attmask;

	hi_omci_debug("[INFO]:us_instid=0x%x,mask=0x%x\n", us_instid, us_mask);

	hi_omci_debug("[INFO]:adminstate=%hhu,acsaddr=%hu,tag=%hu\n", pst_entity->uc_adminstate,
		      pst_entity->us_acsaddr, pst_entity->us_tag);

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR1) ||
	    HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR2)) {
		if ((0 != pst_entity->us_acsaddr) && (0xffff != pst_entity->us_acsaddr)) {
			if (0 != gui_omci_voip_flag) {
				i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_NETWORK_ADDR_E, pst_entity->us_acsaddr, &st_network);
				HI_OMCI_RET_CHECK(i_ret);
				i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_LARGE_STR_E, st_network.us_addr_ptr, &st_string);
				HI_OMCI_RET_CHECK(i_ret);
				i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_ATH_SECR_METHOD_E, st_network.us_secur_ptr, &st_auth);
				HI_OMCI_RET_CHECK(i_ret);

				HI_OS_MEMSET_S(&st_server, sizeof(st_server), 0, sizeof(st_server));
				HI_OS_STRCAT_S((hi_char8 *)st_server.uc_acsaddr, sizeof(st_server.uc_acsaddr),
					       (hi_char8 *)st_string.uc_part[0]);

				HI_OS_MEMCPY_S(st_server.uc_auth_username, sizeof(st_server.uc_auth_username), st_auth.uc_username1,
					       sizeof(st_auth.uc_username1));
				HI_OS_STRCAT_S((hi_char8 *)st_server.uc_auth_username, sizeof(st_server.uc_auth_username),
					       (hi_char8 *)st_auth.uc_username2);
				HI_OS_MEMCPY_S(st_server.uc_auth_password, sizeof(st_server.uc_auth_password), st_auth.uc_password,
					       sizeof(st_auth.uc_password));

				if (0 == pst_entity->uc_adminstate) {
					hi_omci_debug("[INFO]:tr069.adminstate=%hhu acsaddr=%s\n",
						      pst_entity->uc_adminstate, st_server.uc_acsaddr);
					i_ret = hi_omci_tapi_tr069_server_set(&st_server);
					HI_OMCI_RET_CHECK(i_ret);
				}
			} else {
				HI_OMCI_BIT_SET(gui_omci_tr069_management_mask, (16 - HI_OMCI_ATTR2));
			}
		}
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR3)) {
		if ((0xffff == pst_entity->us_tag) && (0 == pst_entity->uc_adminstate)) {
			if (0 == gui_omci_tr069_management_wanconfig) {
				hi_omci_debug("[INFO]:tr069_management_wanconfig=%u\n",
					      gui_omci_tr069_management_wanconfig);
				hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
				return HI_RET_SUCC;
			}
			/* 删除tr069 wan */
			i_ret = hi_omci_tapi_voip_wan_del(1);
			HI_OMCI_RET_CHECK(i_ret);
			i_ret = hi_omci_tapi_tr069_conf_set(&ui_status);
			HI_OMCI_RET_CHECK(i_ret);
			hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
			gui_omci_tr069_management_wanconfig = 0;
			hi_omci_debug("[INFO]:delete tr069 management wan OK\n");
			return HI_RET_SUCC;
		}

		if (0 != gui_omci_voip_flag) {
			ui_status = 1;
			i_ret = hi_omci_tapi_tr069_conf_set(&ui_status);
			HI_OMCI_RET_CHECK(i_ret);
			HI_OS_MEMSET_S(&st_tapi_iphost, sizeof(st_tapi_iphost), 0, sizeof(st_tapi_iphost));
			if (0xFFFF != pst_entity->us_tag) {
				st_tapi_iphost.ui_vlan = pst_entity->us_tag & 0xFFF;
				st_tapi_iphost.ui_vlanen = 1;
				st_tapi_iphost.us_8021p = (pst_entity->us_tag >> 13) & 0x7;
				st_tapi_iphost.us_8021pen = 1;
			}
			st_tapi_iphost.us_flag = 1;
			/* 适配贝尔olt流程,在VEIP提前配置的情况下:
			   如果TCP/UDP指针无效，使用默认的iphost实例触发iphost配置流程
			   如果TCP/UDP指针有效，则使用指向的iphost实例触发iphost配置流程 */
			if (HI_RET_SUCC == hi_omci_ext_get_inst(HI_OMCI_PRO_ME_VEIP_E, us_instid, &st_veip)) {
				if ((HI_RET_SUCC !=
				     hi_omci_ext_get_inst(HI_OMCI_PRO_ME_TCP_UDP_CFG_DATA_E, st_veip.us_tcpudp_ptr, &st_udptcp)) &&
				    (0xffff == st_veip.us_tcpudp_ptr)) {
					if (HI_RET_SUCC == hi_omci_ext_get_inst(HI_OMCI_PRO_ME_IP_HOST_CFG_DATA_E,
										HI_OMCI_TAPI_IPHOST_INSTID, &st_iphost)) {
						st_iphost.st_msghead.us_instid = HI_OMCI_TAPI_IPHOST_INSTID;
						for (ui_index = 0; ui_index < HI_OMCI_TAPI_EXTVLAN_TABLE_NUM; ui_index++) {
							if (g_st_extvlan_table.st_extvlan[ui_index].ui_index == HI_OMCI_TAPI_IPHOST_INSTID) {
								break;
							}
						}
					}
				} else if (HI_RET_SUCC ==
					   hi_omci_ext_get_inst(HI_OMCI_PRO_ME_TCP_UDP_CFG_DATA_E, st_veip.us_tcpudp_ptr, &st_udptcp)) {
					if (HI_RET_SUCC ==
					    hi_omci_ext_get_inst(HI_OMCI_PRO_ME_IP_HOST_CFG_DATA_E, st_udptcp.us_iphost_ptr, &st_iphost)) {
						st_iphost.st_msghead.us_instid = st_udptcp.us_iphost_ptr;
						for (ui_index = 0; ui_index < HI_OMCI_TAPI_EXTVLAN_TABLE_NUM; ui_index++) {
							if (g_st_extvlan_table.st_extvlan[ui_index].ui_index == st_udptcp.us_iphost_ptr) {
								break;
							}
						}
					}
				}

				if (HI_OMCI_TAPI_EXTVLAN_TABLE_NUM != ui_index) {
					g_st_extvlan_table.st_extvlan[ui_index].ui_mode = 2;
					hi_omci_debug("[INFO] iphost us_instid=0x%x\n", st_udptcp.us_iphost_ptr);
					hi_omci_me_set_iphost_attr(&st_tapi_iphost, g_st_extvlan_table.st_extvlan[ui_index].ui_index);
				}
			}

			i_ret = hi_omci_tapi_voip_wan_set(&st_tapi_iphost);
			HI_OMCI_RET_CHECK(i_ret);
			gui_omci_tr069_management_wanconfig = 1;
		} else {
			HI_OMCI_BIT_SET(gui_omci_tr069_management_mask, (16 - HI_OMCI_ATTR3));
		}
	}
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
