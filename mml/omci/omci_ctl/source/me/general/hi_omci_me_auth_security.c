/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_auth_security.c
  Version    : 初稿
  Author     :
  Creation   :
  Description:
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_auth_security_set
 Description : Authentication security method set
               在数据库操作后处理
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_auth_security_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_uint32 ret = HI_RET_SUCC;
	hi_omci_me_auth_secur_method_s *pst_entity = pv_data;
	hi_omci_tapi_voip_auth_s st_tapiauth = { 0 };
	hi_ushort16 us_instid = 0, us_sipdata_instid = 0, us_pptp_instid, us_mask;

	us_instid  = pst_entity->st_msghead.us_instid;
	us_mask    = pst_entity->st_msghead.us_attmask;
	hi_omci_debug("[INFO]:us_instid=0x%x,mask=0x%x\n", us_instid, us_mask);
	hi_omci_debug("[INFO]:validscheme=%hhu,username1=%s,password=%s,realm =%s,username2=%s\n",
		      pst_entity->uc_validscheme, pst_entity->uc_username1, pst_entity->uc_password, pst_entity->uc_realm,
		      pst_entity->uc_username2);

	ret = hi_omci_ext_get_instid_byattr(HI_OMCI_PRO_ME_SIP_USER_DATA_E, HI_OMCI_ATTR4, &us_instid, &us_sipdata_instid);
	if (ret != HI_RET_SUCC) {
		hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ret, __LINE__);
		return HI_RET_SUCC;
	}
	ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_SIP_USER_DATA_E, us_sipdata_instid, HI_OMCI_ATTR10, &us_pptp_instid);
	if (ret != HI_RET_SUCC) {
		hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ret, __LINE__);
		return HI_RET_SUCC;
	}

	memset_s(&st_tapiauth, sizeof(st_tapiauth), 0, sizeof(st_tapiauth));
	st_tapiauth.ui_lineidx = HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(us_pptp_instid);
	hi_omci_tapi_voip_sipauth_get(&st_tapiauth);

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR2)) {
		memset_s(&st_tapiauth.uc_username, HI_OMCI_TAPI_VOIP_STR_LEN, 0, HI_OMCI_TAPI_VOIP_STR_LEN);
		memcpy_s(st_tapiauth.uc_username, HI_OMCI_TAPI_VOIP_STR_LEN, pst_entity->uc_username1, HI_OMCI_VOIP_STP_LEN);
		strcat_s((hi_char8 *)st_tapiauth.uc_username, HI_OMCI_TAPI_VOIP_STR_LEN, (hi_char8 *)pst_entity->uc_username2);
		hi_omci_tapi_voip_sipauth_set(&st_tapiauth);
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR3)) {
		memset_s(&st_tapiauth.uc_password, HI_OMCI_TAPI_VOIP_STR_LEN, 0, HI_OMCI_TAPI_VOIP_STR_LEN);
		memcpy_s(st_tapiauth.uc_password, HI_OMCI_TAPI_VOIP_STR_LEN, pst_entity->uc_password, HI_OMCI_VOIP_STP_LEN);
		hi_omci_tapi_voip_sipauth_set(&st_tapiauth);
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
#if 0
/******************************************************************************
 Function    : hi_omci_me_auth_security_get
 Description : Authentication security method  get
               在数据库操作前处理
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_auth_security_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_uint32 i_ret = HI_RET_SUCC;
	hi_omci_me_auth_secur_method_s *pst_entity = pv_data;
	hi_omci_me_auth_secur_method_s st_entity;
	IgdVoiceSipUserAttrConfTab st_data;
	hi_ushort16 us_instid, us_sipdata_instid, us_pptp_instid;
	hi_ushort16 us_mask;

	/* get the instance ID */
	us_instid  = pst_entity->st_msghead.us_instid;
	us_mask    = pst_entity->st_msghead.us_attmask;

	hi_omci_debug("[INFO]:us_instid=0x%x,mask=0x%x\n", us_instid, us_mask);
	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));

	HI_OMCI_HWEXT_RET(hi_omci_ext_get_instid_byattr(HI_OMCI_PRO_ME_SIP_USER_DATA_E,
			  HI_OMCI_ATTR4, &us_instid, &us_sipdata_instid));

	i_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_SIP_USER_DATA_E, us_sipdata_instid, HI_OMCI_ATTR10, &us_pptp_instid);
	HI_OMCI_RET_CHECK(i_ret);

	st_data.ucLineIndex = HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(us_pptp_instid);
	i_ret = igdCmConfGet(IGD_VOICE_SIP_USER_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);
	HI_OS_MEMCPY_S(st_entity.uc_username1, sizeof(st_entity.uc_username1), st_data.aucAuthUsername, HI_OMCI_VOIP_STP_LEN);
	HI_OS_MEMCPY_S(st_entity.uc_username2, sizeof(st_entity.uc_username2), &st_data.aucAuthUsername[HI_OMCI_VOIP_STP_LEN],
		       HI_OMCI_VOIP_STP_LEN);
	HI_OS_MEMCPY_S(st_entity.uc_password, sizeof(st_entity.uc_password), st_data.aucAuthPassword, HI_OMCI_VOIP_STP_LEN);
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_ATH_SECR_METHOD_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
#endif
