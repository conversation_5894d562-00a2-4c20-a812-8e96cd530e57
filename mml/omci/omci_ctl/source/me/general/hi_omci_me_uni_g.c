/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_uni_g.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-25
  Description: UNI-G
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
#define HI_OMCI_ME_UNI_MGT_OMCI_ONLY    0
//#define HI_OMCI_ME_UNI_MGT_NON_OMCI     1
#define HI_OMCI_ME_UNI_MGT_BOTH         2

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

hi_int32 hi_omci_me_unig_init(hi_uint32 ui_portid)
{
	hi_omci_me_uni_g_s st_entry;
	hi_omci_tapi_sysinfo_s st_info;
	hi_int32 i_ret;
	hi_uint32 ui_mgt;
	hi_ushort16 us_veipid = 0;
	hi_ushort16 us_instid;//与pptp eht uni同一个实例号

	i_ret = hi_omci_tapi_port_pptpinstid_get(ui_portid, &us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_tapi_sysinfo_get(&st_info);
	HI_OMCI_RET_CHECK(i_ret);

	if (HI_OMCI_TAPI_VEIP_E == ui_portid) {
		ui_mgt = HI_OMCI_ME_UNI_MGT_BOTH;
		us_veipid = HI_OMCI_ME_VEIP_INSTID;
		us_instid = HI_OMCI_ME_VEIP_INSTID;
	} else if (HI_OMCI_TAPI_VOIP_E == ui_portid) {
		ui_mgt = HI_OMCI_ME_UNI_MGT_BOTH;
		us_veipid = HI_OMCI_ME_VOIP_INSTID;
		us_instid = HI_OMCI_ME_VOIP_INSTID;
	} else if (HI_OMCI_TAPI_CATV_E == ui_portid) {
		ui_mgt = HI_OMCI_ME_UNI_MGT_BOTH;
		us_veipid = HI_OMCI_ME_CATV_INSTID;
		us_instid = HI_OMCI_ME_CATV_INSTID;
	} else if (HI_OMCI_TAPI_WIFI_E == ui_portid) {
		ui_mgt = HI_OMCI_ME_UNI_MGT_BOTH;
		us_veipid = HI_OMCI_ME_WIFI_INSTID;
		us_instid = HI_OMCI_ME_WIFI_INSTID;
	} else {
		ui_mgt = HI_OMCI_ME_UNI_MGT_BOTH;
		us_veipid = us_instid;
	}

	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_UNI_G_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_entry, sizeof(st_entry), 0, sizeof(st_entry));
	st_entry.st_msghead.us_instid = us_instid;
	st_entry.us_confgsta = 0xe;
	st_entry.uc_mngcap = (hi_uchar8)ui_mgt;
	st_entry.us_non_omci = us_veipid;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_UNI_G_E, us_instid, &st_entry);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

