/******************************************************************************

                  Copyright (C), 2024-2025 VSOL

 ******************************************************************************
  Filename   : hwtc_omci_me_GeneralPurposeBuffer.c
  Version    : 1.0
  Author     : Bohannon
  Creation   : 2025-09-10 14:17:24
  Meid       : 308
  Description: GENERALPURPOSEBUFFER
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
*****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

extern hi_uint32 g_table_avail_size;
extern uint32_t g_ext_tab_avail_size;

hi_int32 hwtc_omci_me_GeneralPurposeBuffer_init()
{
    hi_ushort16 meid = HI_OMCI_PRO_ME_GPB;
    hi_ushort16 us_instid = 0;
    hwtc_omci_me_GeneralPurposeBuffer_s st_entity;
    hi_int32 i_ret = HI_RET_SUCC;

    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;

    i_ret = hwtc_omci_me_GeneralPurposeBuffer_get(&st_entity, sizeof(st_entity), NULL);
    HI_OMCI_RET_CHECK(i_ret);

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}
    
hi_int32 hwtc_omci_me_GeneralPurposeBuffer_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{

    hwtc_omci_me_GeneralPurposeBuffer_s  *pst_entity = (hwtc_omci_me_GeneralPurposeBuffer_s *)pv_data;
    hi_int32 i_ret = HI_RET_SUCC;
    hi_ushort16 us_instid, us_attrmask;
    hi_int32 tbl_size=0;
    
    us_instid = pst_entity->st_msghead.us_instid;
    us_attrmask = pst_entity->st_msghead.us_attmask;
    
    if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_attrmask, HI_OMCI_ATTR2)){
        i_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_GPB, us_instid, HI_OMCI_ATTR3, &tbl_size);
        HI_OMCI_RET_CHECK(i_ret);
        hi_omci_debug("get gpb gpbPointer=0x%x tbl_size=%d\n", us_instid, tbl_size);
        
        /* prepare for actgetnext */
        g_table_avail_size = g_ext_tab_avail_size = tbl_size;
    }

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_GeneralPurposeBuffer_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_GeneralPurposeBuffer_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
    hwtc_omci_me_GeneralPurposeBuffer_s  *pst_entity = (hwtc_omci_me_GeneralPurposeBuffer_s *)pv_data;
    hi_ushort16 us_instid, us_attrmask;

    us_instid   = pst_entity->st_msghead.us_instid;
    us_attrmask = pst_entity->st_msghead.us_attmask;

    hi_omci_debug("[INFO]:us_instid=0x%x, attrmask=0x%x\n", us_instid, us_attrmask);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_GeneralPurposeBuffer_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
    hwtc_omci_me_GeneralPurposeBuffer_s *pst_entity = (hwtc_omci_me_GeneralPurposeBuffer_s *)pv_data;
    hi_ushort16 us_instid;

    us_instid   = pst_entity->st_msghead.us_instid;

    hi_omci_debug("[INFO]:us_instid=0x%x\n", us_instid);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

