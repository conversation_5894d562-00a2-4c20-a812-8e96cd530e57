/******************************************************************************

                  Copyright (C), 2024-2025 VSOL

 ******************************************************************************
  Filename   : hi_omci_me_pptp_80211_uni.c
  Version    : 1.0
  Author     : fyy
  Creation   : 2025-02-27 13:28:33
  Meid       : 91
  Description: PPTP_80211_UNI
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
*****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

hi_int32 hi_omci_me_pptp_80211_uni_init(hi_ushort16 port_id)
{
    hi_ushort16 meid = HI_OMCI_PRO_ME_PPTP_80211_UNI;
    hi_ushort16 us_instid = HI_OMCI_ME_WIFI_INSTID+port_id;
    hi_omci_me_pptp_80211_uni_s st_entity;
    hi_int32 i_ret = HI_RET_SUCC;

    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.uc_AdminState = 1;

	i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hi_omci_me_pptp_80211_uni_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_me_pptp_80211_uni_s  *pst_entity = (hi_omci_me_pptp_80211_uni_s *)pv_data;
    hi_omci_me_pptp_80211_uni_s  st_entity;
    hi_ushort16 meid = HI_OMCI_PRO_ME_PPTP_80211_UNI;
	hi_ushort16 us_instid, us_attrmask;
    hi_uint32   i_ret = HI_RET_SUCC;
	
    us_instid   = pst_entity->st_msghead.us_instid;
    us_attrmask = pst_entity->st_msghead.us_attmask;

    hi_omci_debug("[INFO]:us_instid=0x%x, attrmask=0x%x\n", us_instid, us_attrmask);

	i_ret = hi_omci_ext_get_inst(meid, us_instid, &st_entity);
	if (HI_RET_SUCC != i_ret) {
		hi_omci_systrace(i_ret, 0, 0, 0, 0);
		return i_ret;
	}

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hi_omci_me_pptp_80211_uni_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_me_pptp_80211_uni_s *pst_entity = (hi_omci_me_pptp_80211_uni_s *)pv_data;
	hi_ushort16 us_instid, us_attrmask;
    //hi_uint32 i_ret = HI_RET_SUCC;
    //hi_uchar8 uc_cfgChange = 0;
	
    us_instid   = pst_entity->st_msghead.us_instid;
    us_attrmask = pst_entity->st_msghead.us_attmask;

    hi_omci_debug("[INFO]:us_instid=0x%x, attrmask=0x%x\n", us_instid, us_attrmask);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}
