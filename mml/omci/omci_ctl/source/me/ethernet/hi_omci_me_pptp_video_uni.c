/******************************************************************************

                  Copyright (C), 2024-2025 VSOL

 ******************************************************************************
  Filename   : hi_omci_me_pptp_video_uni.c
  Version    : V1.0
  Author     : fyy
  Creation   : 2025-01-03
  Meid       : 82
  Description: std omci pptp video uni
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
*****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_AdminState;
    hi_uchar8 uc_OpState;
    hi_uchar8 uc_Arc;
    hi_uchar8 uc_ArcIntvl;
    hi_uchar8 uc_PwrCtrl;
} __attribute__((packed))hi_omci_me_pptp_video_uni_s;

hi_int32 hi_omci_me_pptp_video_uni_init()
{
    hi_ushort16 meid = HI_OMCI_PRO_ME_PPTP_VIDEO_UNI_E;
    hi_ushort16 us_instid = HI_OMCI_ME_CATV_INSTID;
    hi_omci_me_pptp_video_uni_s st_entity;
    hi_int32 i_ret = HI_RET_SUCC;

    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;

    i_ret = hi_omci_me_pptp_video_uni_get(&st_entity, sizeof(st_entity), NULL);
    HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hi_omci_me_pptp_video_uni_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_me_pptp_video_uni_s  *pst_entity = (hi_omci_me_pptp_video_uni_s *)pv_data;
    hi_omci_me_pptp_video_uni_s  st_entity;
    hi_ushort16 meid = HI_OMCI_PRO_ME_PPTP_VIDEO_UNI_E;
	hi_ushort16 us_instid, us_attrmask;
    hi_uint32   i_ret = HI_RET_SUCC;
    hi_uchar8 uc_onuCatv = 0;
	
    us_instid   = pst_entity->st_msghead.us_instid;
    us_attrmask = pst_entity->st_msghead.us_attmask;

    hi_omci_debug("[INFO]:us_instid=0x%x, attrmask=0x%x\n", us_instid, us_attrmask);
    
	i_ret = hi_omci_ext_get_inst(meid, us_instid, &st_entity);
	if (HI_RET_SUCC != i_ret) {
		hi_omci_systrace(i_ret, 0, 0, 0, 0);
		return i_ret;
	}

    vs_omci_tapi_onu_catv_get(&uc_onuCatv);

    if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_attrmask, HI_OMCI_ATTR1)) 
    {
        st_entity.uc_AdminState = uc_onuCatv?0:1;
    }

    if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_attrmask, HI_OMCI_ATTR2)) 
    {
        st_entity.uc_OpState = uc_onuCatv?0:1;
    }

    if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_attrmask, HI_OMCI_ATTR5)) 
    {
        st_entity.uc_PwrCtrl = uc_onuCatv;
    }

	i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hi_omci_me_pptp_video_uni_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_me_pptp_video_uni_s *pst_entity = (hi_omci_me_pptp_video_uni_s *)pv_data;
	hi_ushort16 us_instid, us_attrmask;
    hi_uint32 i_ret = HI_RET_SUCC;
    hi_uchar8 uc_cfgChange = 0;
    hi_uchar8 uc_onuCatv = 0;
    hi_uchar8 uc_old_onuCatv;
	
    us_instid   = pst_entity->st_msghead.us_instid;
    us_attrmask = pst_entity->st_msghead.us_attmask;

    hi_omci_debug("[INFO]:us_instid=0x%x, attrmask=0x%x\n", us_instid, us_attrmask);

    vs_omci_tapi_onu_catv_get(&uc_old_onuCatv);

    if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_attrmask, HI_OMCI_ATTR1))
    {
        uc_onuCatv = pst_entity->uc_AdminState?0:1;
        if (uc_onuCatv != uc_old_onuCatv)
            uc_cfgChange = 1;
    }

    if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_attrmask, HI_OMCI_ATTR5))
    {
        uc_onuCatv = pst_entity->uc_PwrCtrl;
        if (uc_onuCatv != uc_old_onuCatv)
            uc_cfgChange = 1;
    }
    
    if (uc_cfgChange)
    {
        i_ret = vs_omci_tapi_onu_catv_set(uc_onuCatv);
        HI_OMCI_RET_CHECK(i_ret);
    }

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}
