/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_eth_pm3.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-16
  Description: Ethernet performance monitoring history data 3
******************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
#define HI_OMCI_ME_ETHPM3_ALARM_DROP        HI_OMCI_ME_ALARM_BITMAP(1)
#define HI_OMCI_ME_ETHPM3_ALARM_UNDERSIZE   HI_OMCI_ME_ALARM_BITMAP(2)
#define HI_OMCI_ME_ETHPM3_ALARM_FRAGMENT    HI_OMCI_ME_ALARM_BITMAP(3)
#define HI_OMCI_ME_ETHPM3_ALARM_JABBER      HI_OMCI_ME_ALARM_BITMAP(4)

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
static hi_omci_tapi_stat_eth_s gst_ethpm3_history[HI_OMCI_ETH_PM3_MAX_NUM];
static hi_omci_tapi_stat_eth_s gst_ethpm3_prev_history[HI_OMCI_ETH_PM3_MAX_NUM];
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_ethpm3_alarm_get
 Description : ethernet data3统计告警检查
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_ethpm3_alarm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_alarm_timer_s *pst_timer = (hi_omci_me_alarm_timer_s *)pv_data;
	hi_omci_me_ethpm3_s st_entity;
	hi_omci_me_threshold1_s st_threshold1;
	hi_int32 i_ret = 0;
	hi_ushort16 us_bitmap_curr = 0;
	hi_ushort16 us_bitmap_hist = 0;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->ui_instid;

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
	HI_OS_MEMSET_S(&st_threshold1, sizeof(st_threshold1), 0, sizeof(st_threshold1));
	/* 读取当前芯片端口MAC统计 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_ETH_PM3_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	/* 获取各参数阈值 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_THRESHOLD_DATA1_E, st_entity.us_thresid, &st_threshold1);
	HI_OMCI_RET_CHECK(i_ret);

	/* 比较是否超出阈值，确定是告警产生，还是撤销 */
	if (st_entity.ui_dropsevt > st_threshold1.ui_thres1) {
		us_bitmap_curr |= HI_OMCI_ME_ETHPM3_ALARM_DROP;
	}

	if (st_entity.ui_undersizepkt > st_threshold1.ui_thres2) {
		us_bitmap_curr |= HI_OMCI_ME_ETHPM3_ALARM_UNDERSIZE;
	}

	if (st_entity.ui_fragments > st_threshold1.ui_thres3) {
		us_bitmap_curr |= HI_OMCI_ME_ETHPM3_ALARM_FRAGMENT;
	}

	if (st_entity.ui_jabber > st_threshold1.ui_thres4) {
		us_bitmap_curr |= HI_OMCI_ME_ETHPM3_ALARM_JABBER;
	}

	/* 读取数据库当前告警状态 */
	i_ret = hi_omci_sql_alarm_inst_get(HI_OMCI_PRO_ME_ETH_PM3_E, us_instid, &us_bitmap_hist);
	HI_OMCI_RET_CHECK(i_ret);

	/* 如果告警状态发生变化，更新数据库的告警状态 */
	if (us_bitmap_curr != us_bitmap_hist) {
		i_ret = hi_omci_sql_alarm_inst_set(HI_OMCI_PRO_ME_ETH_PM3_E, us_instid, us_bitmap_curr);
		HI_OMCI_RET_CHECK(i_ret);

		i_ret = hi_omci_proc_sendalarm(HI_OMCI_PRO_ME_ETH_PM3_E, us_instid, us_bitmap_curr);
		HI_OMCI_RET_CHECK(i_ret);
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_ethpm3_stat_get
 Description : 获取eth pm3统计
 Input Parm  : hi_void *pv_data       ethernet port data 历史累计统计数据
               hi_uint32 ui_inlen
 Output Parm : hi_void *pv_data       ethernet port data 当前累计统计数据
               hi_uint32 *pui_outlen  输出数据长度
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_ethpm3_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_stat_timer_s *pst_timer = (hi_omci_me_stat_timer_s *)pv_data;
	hi_omci_me_ethpm3_s st_entity;
	hi_omci_tapi_stat_eth_s st_ethpm3_curr;
	hi_int32 i_ret = 0;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->ui_instid;
	hi_ushort16 us_portid = 0;

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
	HI_OS_MEMSET_S(&st_ethpm3_curr, sizeof(st_ethpm3_curr), 0, sizeof(st_ethpm3_curr));
	us_portid = HI_OMCI_GET_PORTID_FROM_INSTID(us_instid);

	/* 读取芯片当前统计 */
	i_ret = hi_omci_tapi_stat_eth_get((hi_omci_tapi_port_e)us_portid, &st_ethpm3_curr);
	HI_OMCI_RET_CHECK(i_ret);

	/* 与历史数据相减，将结构保存到数据库 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_ETH_PM3_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);


	if (HI_OMCI_ETH_PM3_MAX_NUM > us_portid) {
		st_entity.uc_endtime++;
		st_entity.ui_dropsevt = (st_ethpm3_curr.ui_dropsevt > gst_ethpm3_history[us_portid].ui_dropsevt) ?
					(st_ethpm3_curr.ui_dropsevt - gst_ethpm3_history[us_portid].ui_dropsevt) : 0;
		st_entity.ui_octets = (st_ethpm3_curr.ui_octets > gst_ethpm3_history[us_portid].ui_octets) ?
				      (st_ethpm3_curr.ui_octets - gst_ethpm3_history[us_portid].ui_octets) : 0;
		st_entity.ui_packets = (st_ethpm3_curr.ui_packets > gst_ethpm3_history[us_portid].ui_packets) ?
				       (st_ethpm3_curr.ui_packets - gst_ethpm3_history[us_portid].ui_packets) : 0;
		st_entity.ui_bcpkt = (st_ethpm3_curr.ui_bcpkt > gst_ethpm3_history[us_portid].ui_bcpkt) ?
				     (st_ethpm3_curr.ui_bcpkt - gst_ethpm3_history[us_portid].ui_bcpkt) : 0;
		st_entity.ui_mcpkt = (st_ethpm3_curr.ui_mcpkt > gst_ethpm3_history[us_portid].ui_mcpkt) ?
				     (st_ethpm3_curr.ui_mcpkt - gst_ethpm3_history[us_portid].ui_mcpkt) : 0;
		st_entity.ui_undersizepkt = (st_ethpm3_curr.ui_undersizepkt > gst_ethpm3_history[us_portid].ui_undersizepkt) ?
					    (st_ethpm3_curr.ui_undersizepkt - gst_ethpm3_history[us_portid].ui_undersizepkt) : 0;
		st_entity.ui_fragments = (st_ethpm3_curr.ui_fragments > gst_ethpm3_history[us_portid].ui_fragments) ?
					 (st_ethpm3_curr.ui_fragments - gst_ethpm3_history[us_portid].ui_fragments) : 0;
		st_entity.ui_jabber = (st_ethpm3_curr.ui_jabber > gst_ethpm3_history[us_portid].ui_jabber) ?
				      (st_ethpm3_curr.ui_jabber - gst_ethpm3_history[us_portid].ui_jabber) : 0;
		st_entity.ui_pkt64 = (st_ethpm3_curr.ui_pkt64 > gst_ethpm3_history[us_portid].ui_pkt64) ?
				     (st_ethpm3_curr.ui_pkt64 - gst_ethpm3_history[us_portid].ui_pkt64) : 0;
		st_entity.ui_pkt65to127 = (st_ethpm3_curr.ui_pkt65to127 > gst_ethpm3_history[us_portid].ui_pkt65to127) ?
					  (st_ethpm3_curr.ui_pkt65to127 - gst_ethpm3_history[us_portid].ui_pkt65to127) : 0;
		st_entity.ui_pkt128to255 = (st_ethpm3_curr.ui_pkt128to255 > gst_ethpm3_history[us_portid].ui_pkt128to255) ?
					   (st_ethpm3_curr.ui_pkt128to255 - gst_ethpm3_history[us_portid].ui_pkt128to255) : 0;
		st_entity.ui_pkt256to511 = (st_ethpm3_curr.ui_pkt256to511 > gst_ethpm3_history[us_portid].ui_pkt256to511) ?
					   (st_ethpm3_curr.ui_pkt256to511 - gst_ethpm3_history[us_portid].ui_pkt256to511) : 0;
		st_entity.ui_pkt512to1023 = (st_ethpm3_curr.ui_pkt512to1023 > gst_ethpm3_history[us_portid].ui_pkt512to1023) ?
					    (st_ethpm3_curr.ui_pkt512to1023 - gst_ethpm3_history[us_portid].ui_pkt512to1023) : 0;
		st_entity.ui_pkt1024to1518 = (st_ethpm3_curr.ui_pkt1024to1518 > gst_ethpm3_history[us_portid].ui_pkt1024to1518) ?
					     (st_ethpm3_curr.ui_pkt1024to1518 - gst_ethpm3_history[us_portid].ui_pkt1024to1518) :
					     0;

		i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_ETH_PM3_E, us_instid, &st_entity);
		HI_OMCI_RET_CHECK(i_ret);

		/* 将当前统计更新到历史统计 */
		HI_OS_MEMCPY_S(&gst_ethpm3_prev_history[us_portid], sizeof(gst_ethpm3_prev_history[us_portid]),
			       &gst_ethpm3_history[us_portid], sizeof(hi_omci_tapi_stat_eth_s));
		HI_OS_MEMCPY_S(&gst_ethpm3_history[us_portid], sizeof(gst_ethpm3_history[us_portid]), &st_ethpm3_curr,
			       sizeof(hi_omci_tapi_stat_eth_s));
	}


	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}


hi_int32 hi_omci_me_ethpm3_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_ethpm3_s *pst_entry = (hi_omci_me_ethpm3_s *)pv_data;
	hi_omci_me_ethpm3_s st_entity;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;
	hi_ushort16 us_portid;


	/*获取ETH PM实例数据 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_ETH_PM3_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);
	us_portid = HI_OMCI_GET_PORTID_FROM_INSTID(us_instid);
	if (HI_OMCI_ETH_PM3_MAX_NUM > us_portid) {
		st_entity.ui_dropsevt = (gst_ethpm3_history[us_portid].ui_dropsevt > gst_ethpm3_prev_history[us_portid].ui_dropsevt) ?
					(gst_ethpm3_history[us_portid].ui_dropsevt -
					 gst_ethpm3_prev_history[us_portid].ui_dropsevt) : 0;
		st_entity.ui_octets = (gst_ethpm3_history[us_portid].ui_octets > gst_ethpm3_prev_history[us_portid].ui_octets) ?
				      (gst_ethpm3_history[us_portid].ui_octets -
				       gst_ethpm3_prev_history[us_portid].ui_octets) : 0;
		st_entity.ui_packets = (gst_ethpm3_history[us_portid].ui_packets > gst_ethpm3_prev_history[us_portid].ui_packets) ?
				       (gst_ethpm3_history[us_portid].ui_packets -
					gst_ethpm3_prev_history[us_portid].ui_packets) : 0;
		st_entity.ui_bcpkt = (gst_ethpm3_history[us_portid].ui_bcpkt > gst_ethpm3_prev_history[us_portid].ui_bcpkt) ?
				     (gst_ethpm3_history[us_portid].ui_bcpkt - gst_ethpm3_prev_history[us_portid].ui_bcpkt) :
				     0;
		st_entity.ui_mcpkt = (gst_ethpm3_history[us_portid].ui_mcpkt > gst_ethpm3_prev_history[us_portid].ui_mcpkt) ?
				     (gst_ethpm3_history[us_portid].ui_mcpkt - gst_ethpm3_prev_history[us_portid].ui_mcpkt) :
				     0;
		st_entity.ui_undersizepkt = (gst_ethpm3_history[us_portid].ui_undersizepkt >
					     gst_ethpm3_prev_history[us_portid].ui_undersizepkt) ? (gst_ethpm3_history[us_portid].ui_undersizepkt -
							     gst_ethpm3_prev_history[us_portid].ui_undersizepkt) : 0;
		st_entity.ui_fragments = (gst_ethpm3_history[us_portid].ui_fragments > gst_ethpm3_prev_history[us_portid].ui_fragments)
					 ? (gst_ethpm3_history[us_portid].ui_fragments -
					    gst_ethpm3_prev_history[us_portid].ui_fragments) : 0;
		st_entity.ui_jabber = (gst_ethpm3_history[us_portid].ui_jabber > gst_ethpm3_prev_history[us_portid].ui_jabber) ?
				      (gst_ethpm3_history[us_portid].ui_jabber -
				       gst_ethpm3_prev_history[us_portid].ui_jabber) : 0;
		st_entity.ui_pkt64 = (gst_ethpm3_history[us_portid].ui_pkt64 > gst_ethpm3_prev_history[us_portid].ui_pkt64) ?
				     (gst_ethpm3_history[us_portid].ui_pkt64 - gst_ethpm3_prev_history[us_portid].ui_pkt64) :
				     0;
		st_entity.ui_pkt65to127 = (gst_ethpm3_history[us_portid].ui_pkt65to127 >
					   gst_ethpm3_prev_history[us_portid].ui_pkt65to127) ? (gst_ethpm3_history[us_portid].ui_pkt65to127 -
							   gst_ethpm3_prev_history[us_portid].ui_pkt65to127) : 0;
		st_entity.ui_pkt128to255 = (gst_ethpm3_history[us_portid].ui_pkt128to255 >
					    gst_ethpm3_prev_history[us_portid].ui_pkt128to255) ? (gst_ethpm3_history[us_portid].ui_pkt128to255 -
							    gst_ethpm3_prev_history[us_portid].ui_pkt128to255) : 0;
		st_entity.ui_pkt256to511 = (gst_ethpm3_history[us_portid].ui_pkt256to511 >
					    gst_ethpm3_prev_history[us_portid].ui_pkt256to511) ? (gst_ethpm3_history[us_portid].ui_pkt256to511 -
							    gst_ethpm3_prev_history[us_portid].ui_pkt256to511) : 0;
		st_entity.ui_pkt512to1023 = (gst_ethpm3_history[us_portid].ui_pkt512to1023 >
					     gst_ethpm3_prev_history[us_portid].ui_pkt512to1023) ? (gst_ethpm3_history[us_portid].ui_pkt512to1023 -
							     gst_ethpm3_prev_history[us_portid].ui_pkt512to1023) : 0;
		st_entity.ui_pkt1024to1518 = (gst_ethpm3_history[us_portid].ui_pkt1024to1518 >
					      gst_ethpm3_prev_history[us_portid].ui_pkt1024to1518) ? (gst_ethpm3_history[us_portid].ui_pkt1024to1518 -
							      gst_ethpm3_prev_history[us_portid].ui_pkt1024to1518) : 0;

		i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_ETH_PM3_E, us_instid, &st_entity);
		HI_OMCI_RET_CHECK(i_ret);
	}


	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_ethpm3_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_ethpm3_s *pst_entry = (hi_omci_me_ethpm3_s *)pv_data;
	hi_omci_me_ethpm3_s st_entity;
	hi_omci_tapi_stat_eth_s st_ethpm3_curr;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;
	hi_ushort16 us_portid = HI_OMCI_GET_PORTID_FROM_INSTID(us_instid);

	/*获取ETH PM3实例数据 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_ETH_PM3_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	/* 读取芯片当前统计 */
	i_ret = hi_omci_tapi_stat_eth_get((hi_omci_tapi_port_e)us_portid, &st_ethpm3_curr);
	HI_OMCI_RET_CHECK(i_ret);

	if (HI_OMCI_ETH_PM3_MAX_NUM > us_portid) {
		st_entity.ui_dropsevt = (st_ethpm3_curr.ui_dropsevt > gst_ethpm3_history[us_portid].ui_dropsevt) ?
					(st_ethpm3_curr.ui_dropsevt - gst_ethpm3_history[us_portid].ui_dropsevt) : 0;
		st_entity.ui_octets = (st_ethpm3_curr.ui_octets > gst_ethpm3_history[us_portid].ui_octets) ?
				      (st_ethpm3_curr.ui_octets - gst_ethpm3_history[us_portid].ui_octets) : 0;
		st_entity.ui_packets = (st_ethpm3_curr.ui_packets > gst_ethpm3_history[us_portid].ui_packets) ?
				       (st_ethpm3_curr.ui_packets - gst_ethpm3_history[us_portid].ui_packets) : 0;
		st_entity.ui_bcpkt = (st_ethpm3_curr.ui_bcpkt > gst_ethpm3_history[us_portid].ui_bcpkt) ?
				     (st_ethpm3_curr.ui_bcpkt - gst_ethpm3_history[us_portid].ui_bcpkt) : 0;
		st_entity.ui_mcpkt = (st_ethpm3_curr.ui_mcpkt > gst_ethpm3_history[us_portid].ui_mcpkt) ?
				     (st_ethpm3_curr.ui_mcpkt - gst_ethpm3_history[us_portid].ui_mcpkt) : 0;
		st_entity.ui_undersizepkt = (st_ethpm3_curr.ui_undersizepkt > gst_ethpm3_history[us_portid].ui_undersizepkt) ?
					    (st_ethpm3_curr.ui_undersizepkt - gst_ethpm3_history[us_portid].ui_undersizepkt) : 0;
		st_entity.ui_fragments = (st_ethpm3_curr.ui_fragments > gst_ethpm3_history[us_portid].ui_fragments) ?
					 (st_ethpm3_curr.ui_fragments - gst_ethpm3_history[us_portid].ui_fragments) : 0;
		st_entity.ui_jabber = (st_ethpm3_curr.ui_jabber > gst_ethpm3_history[us_portid].ui_jabber) ?
				      (st_ethpm3_curr.ui_jabber - gst_ethpm3_history[us_portid].ui_jabber) : 0;
		st_entity.ui_pkt64 = (st_ethpm3_curr.ui_pkt64 > gst_ethpm3_history[us_portid].ui_pkt64) ?
				     (st_ethpm3_curr.ui_pkt64 - gst_ethpm3_history[us_portid].ui_pkt64) : 0;
		st_entity.ui_pkt65to127 = (st_ethpm3_curr.ui_pkt65to127 > gst_ethpm3_history[us_portid].ui_pkt65to127) ?
					  (st_ethpm3_curr.ui_pkt65to127 - gst_ethpm3_history[us_portid].ui_pkt65to127) : 0;
		st_entity.ui_pkt128to255 = (st_ethpm3_curr.ui_pkt128to255 > gst_ethpm3_history[us_portid].ui_pkt128to255) ?
					   (st_ethpm3_curr.ui_pkt128to255 - gst_ethpm3_history[us_portid].ui_pkt128to255) : 0;
		st_entity.ui_pkt256to511 = (st_ethpm3_curr.ui_pkt256to511 > gst_ethpm3_history[us_portid].ui_pkt256to511) ?
					   (st_ethpm3_curr.ui_pkt256to511 - gst_ethpm3_history[us_portid].ui_pkt256to511) : 0;
		st_entity.ui_pkt512to1023 = (st_ethpm3_curr.ui_pkt512to1023 > gst_ethpm3_history[us_portid].ui_pkt512to1023) ?
					    (st_ethpm3_curr.ui_pkt512to1023 - gst_ethpm3_history[us_portid].ui_pkt512to1023) : 0;
		st_entity.ui_pkt1024to1518 = (st_ethpm3_curr.ui_pkt1024to1518 > gst_ethpm3_history[us_portid].ui_pkt1024to1518) ?
					     (st_ethpm3_curr.ui_pkt1024to1518 - gst_ethpm3_history[us_portid].ui_pkt1024to1518) :
					     0;

		/*将数据保存到ETH PM实例数据中*/
		i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_ETH_PM3_E, us_instid, &st_entity);
		HI_OMCI_RET_CHECK(i_ret);
	}


	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_ethpm3_create
 Description : Ethernet performance monitoring history data create
 Input Parm  : hi_void *pv_data,
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_ethpm3_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_ethpm3_s *pst_entry = (hi_omci_me_ethpm3_s *)pv_data;
	hi_omci_me_stat_timer_s st_ethpm3_stat_timer;
	hi_omci_me_alarm_timer_s st_ethpm3_alarm_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	HI_OS_MEMSET_S(&gst_ethpm3_prev_history[0], sizeof(hi_omci_tapi_stat_eth_s) * HI_OMCI_ETH_PM3_MAX_NUM, 0,
		       sizeof(hi_omci_tapi_stat_eth_s) * HI_OMCI_ETH_PM3_MAX_NUM);
	HI_OS_MEMSET_S(&gst_ethpm3_history[0], sizeof(hi_omci_tapi_stat_eth_s) * HI_OMCI_ETH_PM3_MAX_NUM, 0,
		       sizeof(hi_omci_tapi_stat_eth_s) * HI_OMCI_ETH_PM3_MAX_NUM);

	/* 创建ETH PM3告警实体 */
	i_ret = hi_omci_sql_alarm_inst_create(HI_OMCI_PRO_ME_ETH_PM3_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	/* 创建15分钟定时器，获取统计 */
	HI_OS_MEMSET_S(&st_ethpm3_stat_timer, sizeof(st_ethpm3_stat_timer), 0, sizeof(st_ethpm3_stat_timer));
	st_ethpm3_stat_timer.ui_meid = HI_OMCI_PRO_ME_ETH_PM3_E;
	st_ethpm3_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_start(&st_ethpm3_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	/* 创建3秒定时器，上报告警 */
	HI_OS_MEMSET_S(&st_ethpm3_alarm_timer, sizeof(st_ethpm3_alarm_timer), 0, sizeof(st_ethpm3_alarm_timer));
	st_ethpm3_alarm_timer.ui_meid = HI_OMCI_PRO_ME_ETH_PM3_E;
	st_ethpm3_alarm_timer.ui_instid = us_instid;
	st_ethpm3_alarm_timer.ui_timeout = HI_OMCI_ME_ALARM_COMM_TIMEOUT;
	i_ret = hi_omci_me_alarm_start(&st_ethpm3_alarm_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_ethpm3_delete
 Description : Ethernet performance monitoring history data delete
 Input Parm  : hi_void *pv_data,
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_ethpm3_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_ethpm3_s *pst_entry = (hi_omci_me_ethpm3_s *)pv_data;
	hi_omci_me_stat_timer_s st_ethpm3_stat_timer;
	hi_omci_me_alarm_timer_s st_ethpm3_alarm_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	/* 删除ETH PM3告警实体 */
	i_ret = hi_omci_sql_alarm_inst_delete(HI_OMCI_PRO_ME_ETH_PM3_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	/* 删除定时器 */
	HI_OS_MEMSET_S(&st_ethpm3_stat_timer, sizeof(st_ethpm3_stat_timer), 0, sizeof(st_ethpm3_stat_timer));
	st_ethpm3_stat_timer.ui_meid = HI_OMCI_PRO_ME_ETH_PM3_E;
	st_ethpm3_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_stop(&st_ethpm3_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_ethpm3_alarm_timer, sizeof(st_ethpm3_alarm_timer), 0, sizeof(st_ethpm3_alarm_timer));
	st_ethpm3_alarm_timer.ui_meid = HI_OMCI_PRO_ME_ETH_PM3_E;
	st_ethpm3_alarm_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_alarm_stop(&st_ethpm3_alarm_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}


/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

