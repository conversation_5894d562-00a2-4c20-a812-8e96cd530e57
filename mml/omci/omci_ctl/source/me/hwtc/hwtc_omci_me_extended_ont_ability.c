/******************************************************************************

                  Copyright (C), 2024-2025 VSOL

 ******************************************************************************
  Filename   : hi_omci_me_extended_ont_ability.c
  Version    : V1.0
  Author     : fyy
  Creation   : 2025-01-03
  Meid       : 350
  Description: huawei pri me
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
*****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

typedef struct
{
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 flow_mapping_mode;//1
    hi_uchar8 traffic_management_option;//2
    hi_uchar8 flow_car;//3
    hi_uchar8 ont_transparent;//4
    hi_ushort16 current_mac_learning_number;//5
    hi_uint32 mac_agetime;//6
    hi_uchar8 time_sync_from_olt_identify;//7
    hi_uint32 olt_time_high_32bit;//8
    hi_uint32 olt_time_low_32bit;//9
    hi_short16 resv1;//10
    hi_short16 resv2;//11
    hi_short16 resv3;//12
    hi_short16 resv4;//13
    hi_uint32 queue_capbility_8021p;//14
    hi_uchar8 queue_map_8021p[6];//15
    hi_uchar8 battery_mode;//16
} __attribute((packed)) hwtc_omci_me350_t;

typedef struct
{
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 optical_tx_power_isolation_option;//1
    hi_uchar8 flow_transparent;//2
    hi_uchar8 muticast_dynamic_acl_ability;//3
    hi_uchar8 sw_protect_ability;//4
    hi_uchar8 big_entity_ability;//5
    hi_uchar8 memory_occupation_ability;//6
    hi_uchar8 cpu_occupation_ability;//7
    hi_uchar8 cpu_temperature_ability;//8
    hi_ushort16 hw_extended_MGC_config_data_abilty;//9
    hi_ushort16 hw_extended_MGC_user_config_data_ability;//10
    hi_ushort16 hw_extended_VOIP_media_profile;//11
    hi_ushort16 hw_extended_SIP_agent_config_data;//12
    hi_uchar8 hw_extended_impedance_of_ports_port;//13
    hi_uchar8 hw_extended_ont_service_type;//14
    hi_uchar8 hw_extended_big_entity_ability2;//15
} __attribute((packed)) hwtc_omci_me373_t;

hi_int32 hwtc_omci_me_350_init()
{
    hi_ushort16 meid = HI_OMCI_PRO_ME_EXT_ONT_ABILITY_E;
    hi_ushort16 us_instid = 0;
    hwtc_omci_me350_t st_entity;
    hi_int32 i_ret = HI_RET_SUCC;

    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.traffic_management_option = 2; //2
    st_entity.flow_car = 1; //3
    st_entity.ont_transparent = 2; //4
    st_entity.mac_agetime = 300; //6
    st_entity.time_sync_from_olt_identify = 2;//7
    st_entity.queue_capbility_8021p = 0x01080108; //14
    st_entity.queue_map_8021p[0] = 0x05; //15
    st_entity.queue_map_8021p[1] = 0x39;
    st_entity.queue_map_8021p[2] = 0x77;
    st_entity.queue_map_8021p[3] = 0x05;
    st_entity.queue_map_8021p[4] = 0x39;
    st_entity.queue_map_8021p[5] = 0x77;

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_350_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_int32 i;
    hi_uint32 num, tot_num;
    hi_int32 i_ret = HI_RET_SUCC;
    hi_ushort16 us_instid;
    hi_omci_tapi_sysinfo_s sy_info;
    hwtc_omci_me350_t st_entity;
    
    i_ret = hi_omci_tapi_sysinfo_get(&sy_info);
    HI_OMCI_RET_CHECK(i_ret);

    tot_num=0;
    for(i=0; i<sy_info.ui_port_num; i++){
        num=0;
        hi_omci_tapi_br_learn_num_get(i, &num);
        tot_num += num;
    }
    
    hi_omci_debug("tot_num=%d\n", tot_num);

    us_instid = 0;
    i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_EXT_ONT_ABILITY_E, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);
    st_entity.current_mac_learning_number = tot_num;
    i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_EXT_ONT_ABILITY_E, us_instid, &st_entity);
    if (HI_RET_SUCC != i_ret) {
        hi_omci_systrace(i_ret, 0, 0, 0, 0);
        return i_ret;
    }
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_350_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_373_init()
{
    hi_ushort16 meid = HI_OMCI_PRO_ME_EXT_ONT2_ABILITY_E;
    hi_ushort16 us_instid = 0;
    hwtc_omci_me373_t st_entity;
    hi_int32 i_ret = HI_RET_SUCC;

    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;

    st_entity.optical_tx_power_isolation_option = 1;//1
    st_entity.flow_transparent = 1;//2
    st_entity.muticast_dynamic_acl_ability = 0;//3
    st_entity.sw_protect_ability = 0;//4
    st_entity.big_entity_ability = 1;//5
    st_entity.memory_occupation_ability = 1;//6
    st_entity.cpu_occupation_ability = 1;//7
    st_entity.cpu_temperature_ability = 0;//8
    /*del for hw std voip*/
    st_entity.hw_extended_MGC_config_data_abilty = 0xff80;//9
    st_entity.hw_extended_MGC_user_config_data_ability = 0x8000;//10
    st_entity.hw_extended_VOIP_media_profile = 0xe000;//11
    //Bohannon set to 0 to use standard meclass for sip cfg
    st_entity.hw_extended_SIP_agent_config_data = 0 ;//0xc000;//12
    st_entity.hw_extended_impedance_of_ports_port = 1;//13
    st_entity.hw_extended_ont_service_type = 0;//14
    st_entity.hw_extended_big_entity_ability2 = 0;//15

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_373_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_373_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}
