/*****************************************************************************
 *                                INCLUDE                                    *
*****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"
#include "igdGlobalTypeDef.h"
#include "hi_timer.h"

#define HW_EXTERNED_PPPOE_MAX_INST_NUM 4

hi_int32 hwtc_omci_me_init()
{
    hi_int32 i_ret;
    hi_ushort16 us_instid = 0;
    hi_omci_tapi_sysinfo_s st_info;

    /* CTC+HW extended ONT-G */
    i_ret = hwtc_omci_me_ctc_extended_onu_g_init();
    HI_OMCI_RET_CHECK(i_ret);
    
    /* HW extended ONT ability */
    i_ret = hwtc_omci_me_350_init();
    HI_OMCI_RET_CHECK(i_ret);
    
    /* HW extended ONT2 ability */
    i_ret = hwtc_omci_me_373_init();
    HI_OMCI_RET_CHECK(i_ret);

    i_ret = hwtc_omci_me_65425_init();
    HI_OMCI_RET_CHECK(i_ret);

    i_ret = hwtc_omci_me_65426_init();
    HI_OMCI_RET_CHECK(i_ret);

    /* HW proprietary me */
    i_ret = hwtc_omci_me_65427_init();
    HI_OMCI_RET_CHECK(i_ret);

    i_ret =hwtc_omci_me_65398_init();
    HI_OMCI_RET_CHECK(i_ret);
    
    i_ret =hwtc_omci_me_65414_init();
    HI_OMCI_RET_CHECK(i_ret);

    i_ret = hwtc_omci_me_65415_init();
    HI_OMCI_RET_CHECK(i_ret);
    
    i_ret =hwtc_omci_me_65423_init();
    HI_OMCI_RET_CHECK(i_ret);

    i_ret =hwtc_omci_me_65430_init();
    HI_OMCI_RET_CHECK(i_ret);
    
    i_ret =hwtc_omci_me_65431_init();
    HI_OMCI_RET_CHECK(i_ret);

    i_ret = hi_omci_tapi_sysinfo_get(&st_info);
    HI_OMCI_RET_CHECK(i_ret);

    for (us_instid=0; us_instid<st_info.ui_ssid_num; us_instid++)
    {
        hwtc_omci_me_Ieee80211StaMgmData1_init(us_instid); /* maybe move to hi_omci_me_init ? */
        hwtc_omci_me_65444_init(us_instid);
        hwtc_omci_me_65454_init(us_instid);
    }
    
    i_ret =hwtc_omci_me_65447_init();
    HI_OMCI_RET_CHECK(i_ret);
    
    i_ret =hwtc_omci_me_65450_init();
    HI_OMCI_RET_CHECK(i_ret);

//create by set
//    i_ret =hwtc_omci_me_65451_init();
//    HI_OMCI_RET_CHECK(i_ret);
//    i_ret =hwtc_omci_me_65452_init();
//    HI_OMCI_RET_CHECK(i_ret);
    
    i_ret =hwtc_omci_me_65453_init();
    HI_OMCI_RET_CHECK(i_ret);
    
    /* HW extended pppoe config */
    for (us_instid=0; us_instid<HW_EXTERNED_PPPOE_MAX_INST_NUM; us_instid++)
    {
        i_ret = hwtc_omci_me_65417_init(us_instid);
        HI_OMCI_RET_CHECK(i_ret);
    }

    hi_os_printf("hwtc omci init success ...\n");
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}


