/******************************************************************************

                  Copyright (C), 2024-2025 VSOL

 ******************************************************************************
  Filename   : vs_omci_me_ctc_extended_onu_g.c
  Version    : 1.0
  Author     : fyy
  Creation   : 2025-03-03 13:53:14
  Meid       : 65408
  Description: CTC_EXTENDED_ONU_G
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
*****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

hi_int32 hwtc_omci_me_ctc_extended_onu_g_init()
{
    hi_ushort16 meid = HWTC_OMCI_PRO_ME_CTC_EXTENDED_ONU_G;
    hi_ushort16 us_instid = 0;
    hwtc_omci_me_ctc_extended_onu_g_s st_entity;
    hi_omci_tapi_sysinfo_s st_info;
    hi_int32 i_ret = HI_RET_SUCC;

    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    
    i_ret = hi_omci_tapi_sysinfo_get(&st_info);
    HI_OMCI_RET_CHECK(i_ret);
    HI_OS_STRCPY_S((hi_char8 *)st_entity.attr7, sizeof(st_entity.attr7), (hi_char8 *)st_info.ac_manufacturer);
    HI_OS_SNPRINTF_S((hi_char8 *)st_entity.attr9, sizeof(st_entity.attr9), sizeof(st_entity.attr9), \
                                    "%uGE+%uPOTS+%uWLAN", st_info.ui_ge_num, st_info.ui_pots_num, st_info.ui_ssid_num);
    
    HI_OS_SNPRINTF_S((hi_char8 *)st_entity.attr12, sizeof(st_entity.attr12), sizeof(st_entity.attr12), "%s", "@CN#Common&");

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_ctc_extended_onu_g_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hwtc_omci_me_ctc_extended_onu_g_s  *pst_entity = (hwtc_omci_me_ctc_extended_onu_g_s *)pv_data;
    hi_ushort16 us_instid, us_attrmask;

    us_instid   = pst_entity->st_msghead.us_instid;
    us_attrmask = pst_entity->st_msghead.us_attmask;

    hi_omci_debug("[INFO]:us_instid=0x%x, attrmask=0x%x\n", us_instid, us_attrmask);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_ctc_extended_onu_g_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hwtc_omci_me_ctc_extended_onu_g_s *pst_entity = (hwtc_omci_me_ctc_extended_onu_g_s *)pv_data;
    hi_ushort16 us_instid, us_attrmask;

    us_instid   = pst_entity->st_msghead.us_instid;
    us_attrmask = pst_entity->st_msghead.us_attmask;

    hi_omci_debug("[INFO]:us_instid=0x%x, attrmask=0x%x\n", us_instid, us_attrmask);


    if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_attrmask, HI_OMCI_ATTR1))
    {
        //if (pst_entity->uc_ResetDefault) //Del by fyy for bug#17730
            vs_omci_tapi_factory_reset();
    }

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}
