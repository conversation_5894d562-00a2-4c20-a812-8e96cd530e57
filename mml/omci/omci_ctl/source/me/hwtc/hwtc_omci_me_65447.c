/******************************************************************************

                  Copyright (C), 2024-2025 VSOL

 ******************************************************************************
  Filename   : hwtc_omci_me_65447.c
  Version    : 1.0
  Author     : Bohannon
  Creation   : 2025-09-05 13:47:53
  Meid       : 65447
  Description: 65447
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
*****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

hi_int32 hwtc_omci_me_65447_init()
{
    hi_ushort16 meid = HWTC_OMCI_PRO_ME_65447;
    hi_ushort16 us_instid = 0;
    hwtc_omci_me_65447_s st_entity;
    hi_omci_tapi_sysinfo_s sy_info;
    hi_int32 i_ret = HI_RET_SUCC;
    hi_int32 i;
    
    i_ret = hi_omci_tapi_sysinfo_get(&sy_info);
    HI_OMCI_RET_CHECK(i_ret);
    
    for(i=0; i<(sy_info.ui_fe_num+sy_info.ui_ge_num); i++){
        us_instid=i+1;
        i_ret = hi_omci_ext_create_inst(meid, us_instid);
        HI_OMCI_RET_CHECK(i_ret);

        HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
        st_entity.st_msghead.us_instid = us_instid;
        st_entity.st_msghead.us_attmask = 0xffff;

        i_ret = hwtc_omci_me_65447_get(&st_entity, sizeof(st_entity), NULL);
        HI_OMCI_RET_CHECK(i_ret);
        
        i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
        HI_OMCI_RET_CHECK(i_ret);
    }

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}
    
hi_int32 hwtc_omci_me_65447_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_65447_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

