/******************************************************************************

                  Copyright (C), 2024-2025 VSOL

 ******************************************************************************
  Filename   : hwtc_omci_me_Ieee80211StaMgmData1.c
  Version    : 1.0
  Author     : Bohannon
  Creation   : 2025-09-10 17:37:13
  Meid       : 92
  Description: IEEE80211STAMGMDATA1
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
*****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

hi_int32 hwtc_omci_me_Ieee80211StaMgmData1_init(hi_ushort16 port_id)
{
    hi_ushort16 meid = HI_OMCI_PRO_ME_IEEE80211STAMGMDATA1;
    hi_ushort16 us_instid = HI_OMCI_ME_WIFI_INSTID+port_id;
    hwtc_omci_me_Ieee80211StaMgmData1_s st_entity;
    hi_int32 i_ret = HI_RET_SUCC;

#if 0
    if(hwtc_omci_tapi_is_valid_ssid_idx(port_id) != HI_RET_SUCC){
        hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
        return HI_RET_SUCC;
    }
#endif

    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;

    i_ret = hwtc_omci_me_Ieee80211StaMgmData1_get(&st_entity, sizeof(st_entity), NULL);
    HI_OMCI_RET_CHECK(i_ret);

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_Ieee80211StaMgmData1_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hwtc_omci_me_Ieee80211StaMgmData1_s  *pst_entity = (hwtc_omci_me_Ieee80211StaMgmData1_s *)pv_data;
    hwtc_omci_me_Ieee80211StaMgmData1_s  st_entity;
    vs_omci_tapi_ssid_s  st_data;
    hi_ushort16 meid = HI_OMCI_PRO_ME_IEEE80211STAMGMDATA1;
    hi_ushort16 us_instid, us_attrmask;
    hi_uint32 i_ret = HI_RET_SUCC;
    hi_uchar8 uc_wifiSsidIndex;

    us_instid = pst_entity->st_msghead.us_instid;
    us_attrmask = pst_entity->st_msghead.us_attmask;

    hi_omci_debug("[INFO]:us_instid=0x%x, attrmask=0x%x\n", us_instid, us_attrmask);

    i_ret = hi_omci_ext_get_inst(meid, us_instid, &st_entity);
    if (HI_RET_SUCC != i_ret) {
        hi_omci_systrace(i_ret, 0, 0, 0, 0);
        return i_ret;
    }

    HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
    st_data.uc_cfgMode = VS_CONFIG_MODE_GET_FROM_FLASH;
    uc_wifiSsidIndex = (us_instid & 0xff) -1;

#if 0
    if(hwtc_omci_tapi_is_valid_ssid_idx(uc_wifiSsidIndex) != HI_RET_SUCC){
        hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
        return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
    }
#endif

    st_data.uc_wifiSsidIndex = uc_wifiSsidIndex;
    vs_omci_tapi_ssid_get(&st_data);

    if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_attrmask, HI_OMCI_ATTR8))
    {
        HI_OS_STRCPY_S((hi_char8 *)st_entity.auc_attr8, sizeof(st_entity.auc_attr8), st_data.ssidData[uc_wifiSsidIndex].auc_ssidName1);
        HI_OS_STRCAT_S((hi_char8 *)st_entity.auc_attr8, sizeof(st_entity.auc_attr8), st_data.ssidData[uc_wifiSsidIndex].auc_ssidName2);
    }

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_Ieee80211StaMgmData1_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

