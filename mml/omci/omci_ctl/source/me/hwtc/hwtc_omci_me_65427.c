/******************************************************************************

                  Copyright (C), 2024-2025 VSOL

 ******************************************************************************
  Filename   : hwtc_omci_me_hw_externed_65427.c
  Version    : 1.0
  Author     : <PERSON><PERSON><PERSON>
  Creation   : 2025-09-04 15:32:29
  Meid       : 65427
  Description: HW_EXTERNED_65427
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
*****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

hi_int32 hwtc_omci_me_65427_init()
{
    hi_ushort16 meid = HWTC_OMCI_PRO_ME_65427;
    hi_ushort16 us_instid = 0;
    hwtc_omci_me_65427_s st_entity;
    hi_int32 i_ret = HI_RET_SUCC;
    hi_int32 i;

    hi_uchar8 tmp0x82[0x10]={0x57, 0x57, 0x57, 0x57, 0x57, 0x57, 0x57, 0x57, 0x57, 0x57, 0x53, 0x57, 0x57, 0x71, 0x71, 0x53};
    hi_uchar8 tmp0x100[0x10]={0x51, 0x51, 0x51, 0x53, 0x51, 0x53, 0x53, 0x51, 0x51, 0x51, 0x51, 0x53, 0x51};
    hi_uchar8 tmp0x172[0x10]={0x53, 0x53, 0x51, 0x53, 0x51, 0x51, 0x51, 0x51, 0x71, 0x51, 0x53, 0x53, 0x53, 0x73};
    hi_uchar8 tmp0x15f[0x10]={0x51, 0x57, 0x51, 0x51, 0x51, 0x51, 0x51, 0x51, 0x51, 0x51, 0x51, 0x51, 0x51, 0x51, 0x51, 0x51};
    hi_uchar8 tmp0x15e[0x10]={0x53, 0x51, 0x53, 0x53, 0x51, 0x53, 0x51, 0x53, 0x53, 0x51, 0x51, 0x51, 0x51, 0x51, 0x53, 0x51};
    hi_uchar8 tmp0xff86[0x10]={0x51, 0x51, 0x51, 0x51, 0x51, 0x51, 0x51, 0x51, 0x51, 0x51, 0x51, 0x51, 0x51, 0x51};
    hi_uchar8 tmp0xff8f[0x10]={0x51, 0x51, 0x51, 0x51, 0x53, 0x53, 0x53, 0x51, 0x51, 0x53, 0x51, 0x51, 0x53, 0x53, 0x53, 0x53};
    hi_uchar8 tmp0xff80 [0x10]={0x52, 0x53, 0x51, 0x51, 0x51, 0x53, 0x51, 0x53, 0x51, 0x53, 0x53, 0x53, 0x53, 0x51, 0x51, 0x53};
    hi_uchar8 tmp0xff76[0x10]={0x53, 0x53, 0x53, 0x53, 0x52, 0x51, 0x53, 0x53, 0x53, 0x53, 0x53, 0x53, 0x53, 0x51, 0x53};
    hi_uchar8 tmp0xff77[0x10]={0x57, 0x53, 0x53, 0x53, 0x53, 0x53, 0x53, 0x53, 0x53, 0x53, 0x53};
    hi_uchar8 tmp0xffa6[0x10]={0x57, 0x55, 0x71};
    hi_uchar8 tmp0xff87[0x10]={0x53, 0x71, 0x71};

    us_instid = 0x82;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xffff;
    HI_OS_MEMCPY_S(st_entity.auc_attr2, sizeof(st_entity.auc_attr2), tmp0x82, sizeof(st_entity.auc_attr2));

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);


    us_instid = 0x100;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xfff8;
    HI_OS_MEMCPY_S(st_entity.auc_attr2, sizeof(st_entity.auc_attr2), tmp0x100, sizeof(st_entity.auc_attr2));

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);


    us_instid = 0x172;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xfffc;
    HI_OS_MEMCPY_S(st_entity.auc_attr2, sizeof(st_entity.auc_attr2), tmp0x172, sizeof(st_entity.auc_attr2));

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);


    us_instid = 0x15f;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xffff;
    HI_OS_MEMCPY_S(st_entity.auc_attr2, sizeof(st_entity.auc_attr2), tmp0x15f, sizeof(st_entity.auc_attr2));

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);
    

    us_instid = 0x15e;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xffff;
    HI_OS_MEMCPY_S(st_entity.auc_attr2, sizeof(st_entity.auc_attr2), tmp0x15e, sizeof(st_entity.auc_attr2));

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);


    us_instid = 0xff86;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xfffc;
    HI_OS_MEMCPY_S(st_entity.auc_attr2, sizeof(st_entity.auc_attr2), tmp0xff86, sizeof(st_entity.auc_attr2));

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    us_instid = 0x175;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xfff8;
    for(i=0; i<13; i++){
        st_entity.auc_attr2[i]=0x51;
    }

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);


    us_instid = 0x177;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xffff;
    HI_OS_MEMSET_S(st_entity.auc_attr2, sizeof(st_entity.auc_attr2), 0x57, sizeof(st_entity.auc_attr2));

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    us_instid = 0xff8f;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xffff;
    HI_OS_MEMCPY_S(st_entity.auc_attr2, sizeof(st_entity.auc_attr2), tmp0xff8f, sizeof(st_entity.auc_attr2));

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);


    us_instid = 0x160;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xfe00;
    HI_OS_MEMSET_S(st_entity.auc_attr2, 7, 0x57, 7);

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    us_instid = 0xff80;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xffff;
    HI_OS_MEMCPY_S(st_entity.auc_attr2, sizeof(st_entity.auc_attr2), tmp0xff80, sizeof(st_entity.auc_attr2));

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    us_instid = 0xff76;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xfffe;
    HI_OS_MEMCPY_S(st_entity.auc_attr2, sizeof(st_entity.auc_attr2), tmp0xff76, sizeof(st_entity.auc_attr2));

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    
    us_instid = 0xff77;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xffe0;
    HI_OS_MEMCPY_S(st_entity.auc_attr2, sizeof(st_entity.auc_attr2), tmp0xff77, sizeof(st_entity.auc_attr2));

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);


    us_instid = 0xffa6;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xe000;
    HI_OS_MEMCPY_S(st_entity.auc_attr2, sizeof(st_entity.auc_attr2), tmp0xffa6, sizeof(st_entity.auc_attr2));

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    
    us_instid = 0xff68;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xff80;
    for(i=0; i<3; i++){
        st_entity.auc_attr2[i]=0x57;
    }
    for(i=4; i<8; i++){
        st_entity.auc_attr2[i]=0x53;
    }
    st_entity.auc_attr2[8] = 0x71;

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    
    us_instid = 0xff58;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xfff0;
    for(i=0; i<12; i++){
        st_entity.auc_attr2[i]=0x57;
    }
    st_entity.auc_attr2[4] = st_entity.auc_attr2[5] = 0x53;
    st_entity.auc_attr2[10] = st_entity.auc_attr2[11] = 0x71;

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    us_instid = 0xffa7;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0x8000;
    st_entity.auc_attr2[0]=0x53;

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);


    us_instid = 0x0032;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xe000;
    
    st_entity.auc_attr2[0] = 0x71;
    st_entity.auc_attr2[1] = 0x53;
    st_entity.auc_attr2[2] = 0x73;

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    
    us_instid = 0xff91;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xfe00;
    for(i=0; i<7; i++){
        st_entity.auc_attr2[i]=0x53;
    }
    st_entity.auc_attr2[1]=0x51;
    st_entity.auc_attr2[2]=0x51;

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);


    us_instid = 0xff70;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xff00;
    for(i=0; i<8; i++){
        st_entity.auc_attr2[i]=0x57;
    }

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);
    

    us_instid = 0xff97;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0x8000;
    st_entity.auc_attr2[0]=0x53;

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);


    us_instid = 0xff96;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xffff;
    for(i=0; i<0x10; i++){
      st_entity.auc_attr2[i]=0x51;
    }
    st_entity.auc_attr2[12]=0x53;

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);


    us_instid = 0xff65;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xffe0;
    st_entity.auc_attr2[0]=st_entity.auc_attr2[1]=0x57;
    st_entity.auc_attr2[2]=0x53;
    for(i=3; i<11; i++){
        st_entity.auc_attr2[i]=0x51;
    }

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);
    
    us_instid = 0xff83;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xffff;
    for(i=0; i<0x10; i++){
        st_entity.auc_attr2[i]=0x53;
    }
    st_entity.auc_attr2[1]=0x73;
    st_entity.auc_attr2[9] = st_entity.auc_attr2[12] =0x51;

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);


    us_instid = 0xff90;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xfc00;
    for(i=0; i<4; i++){
        st_entity.auc_attr2[i]=0x51;
    }
    st_entity.auc_attr2[4] = st_entity.auc_attr2[5] =0x71;

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);


    us_instid = 0x16f;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xf800;
    for(i=0; i<4; i++){
        st_entity.auc_attr2[i]=0x53;
    }
    st_entity.auc_attr2[4] = 0x73;

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);


    us_instid = 0xff87;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xe000;
    HI_OS_MEMCPY_S(st_entity.auc_attr2, sizeof(st_entity.auc_attr2), tmp0xff87, sizeof(st_entity.auc_attr2));

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    us_instid = 0xff89;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xff00;
    for(i=0; i<8; i++){
        st_entity.auc_attr2[i]=0x53;
    }
    
    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    us_instid = 0xffac;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0x8000;
    st_entity.auc_attr2[0]=0x57;
    
    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    
    hi_uchar8 tmp0xffad[0x10]={0x53, 0x53, 0x73};
    us_instid = 0xffad;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xe000;
    HI_OS_MEMCPY_S(st_entity.auc_attr2, sizeof(st_entity.auc_attr2), tmp0xffad, sizeof(st_entity.auc_attr2));

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    us_instid = 0xff92;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xc000;
    st_entity.auc_attr2[0]=0x53;
    st_entity.auc_attr2[1]=0x53;

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);


    us_instid = 0xffa9;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xe000;
    st_entity.auc_attr2[0]=0x57;
    st_entity.auc_attr2[1]= st_entity.auc_attr2[2] =0x71;

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    
    us_instid = 0xffab;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xe000;
    st_entity.auc_attr2[0] =st_entity.auc_attr2[1] =0x57;
    st_entity.auc_attr2[2] =0x53;
    
    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);


    us_instid = 0xffad;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xe000;
    st_entity.auc_attr2[0] =st_entity.auc_attr2[1] =0x53;
    st_entity.auc_attr2[2] =0x73;
    
    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);


    us_instid = 0xff78;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0x8000;
    st_entity.auc_attr2[0] =0x57;
    
    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);


    us_instid = 0x178;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0x8000;
    st_entity.auc_attr2[0] =0x71;
    
    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);


    us_instid = 0x0179;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xffff;
    for(i=0; i<15; i++){
        st_entity.auc_attr2[i]=0x51;
    }
    
    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);


    us_instid = 0x161;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xff00;
    for(i=0; i<8; i++){
        st_entity.auc_attr2[i]=0x51;
    }
    st_entity.auc_attr2[4] = 0x53;
    st_entity.auc_attr2[7] = 0x71;
    
    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);


    us_instid = 0xff75;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xff80;
    for(i=0; i<9; i++){
      st_entity.auc_attr2[i]=0x57;
    }
    st_entity.auc_attr2[7] = 0x71;
    
    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);


    us_instid = 0xffa4;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0x8000;
    st_entity.auc_attr2[0] = 0x71;
    
    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);


    us_instid = 0xffae;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xfe00;
    st_entity.auc_attr2[0]=0x53;
    st_entity.auc_attr2[1]=0x53;
    for(i=2; i<7; i++){
        st_entity.auc_attr2[i]=0x51;
    }
    
    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    
    us_instid = 0xffaa;
    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    st_entity.us_attr1 = 0xfc00;
    for(i=0; i<6; i++){
        st_entity.auc_attr2[i]=0x51;
    }
    
    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_65427_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_65427_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}
