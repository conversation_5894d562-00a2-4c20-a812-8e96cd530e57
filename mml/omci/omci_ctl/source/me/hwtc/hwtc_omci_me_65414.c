/******************************************************************************

                  Copyright (C), 2024-2025 VSOL

 ******************************************************************************
  Filename   : hwtc_omci_me_65414.c
  Version    : 1.0
  Author     : Bohannon
  Creation   : 2025-09-05 15:38:43
  Meid       : 65414
  Description: 65414
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
*****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

hi_ushort16 us_attr7;
hi_ushort16 us_attr8;
hi_uint32 ui_attr9;
hi_uint32 ui_attr10;
hi_ushort16 us_attr11;
hi_ushort16 us_attr12;

hi_int32 hwtc_omci_me_65414_init()
{
    hi_ushort16 meid = HWTC_OMCI_PRO_ME_65414;
    hi_ushort16 us_instid = 0;
    hwtc_omci_me_65414_s st_entity;
    hi_int32 i_ret = HI_RET_SUCC;
    char attr6[] = {0x00, 0x00, 0x00, 0x00, 0x01, 0x04, 0x01, 0xff, 
                 0xff, 0x4c, 0x54, 0x59, 0x39, 0x37, 0x37, 0x35};
    char attr13[] = {0xff, 0x8b, 0xb3, 0x8e, 0x0b, 0x52, 0xde, 0x24};

    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;

    i_ret = hwtc_omci_me_65414_get(&st_entity, sizeof(st_entity), NULL);
    HI_OMCI_RET_CHECK(i_ret);

    st_entity.uc_attr1 = 0xff;
    st_entity.uc_attr2 = 0xff;
    st_entity.uc_attr3 = 0xff;
    st_entity.uc_attr4 = 0x00;
    st_entity.uc_attr5 = 0xf0;
    HI_OS_MEMCPY_S(st_entity.auc_attr6, sizeof(st_entity.auc_attr6), attr6, sizeof(st_entity.auc_attr6));
    st_entity.us_attr7 = 0x2d4d;
    st_entity.us_attr8 = 0x4843;
    st_entity.ui_attr9 = 0x20202647;
    st_entity.ui_attr10 = 0x80ffff20;
    st_entity.us_attr11 = 0xff80;
    st_entity.us_attr12 = 0xffff;
    HI_OS_MEMCPY_S(st_entity.auc_attr3, sizeof(st_entity.auc_attr3), attr13, sizeof(st_entity.auc_attr3));

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}
    
hi_int32 hwtc_omci_me_65414_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_65414_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

