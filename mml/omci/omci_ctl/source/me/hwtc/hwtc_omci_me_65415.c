/******************************************************************************

                  Copyright (C), 2024-2025 VSOL

 ******************************************************************************
  Filename   : hwtc_omci_me_65415.c
  Version    : 1.0
  Author     : Bohannon
  Creation   : 2025-09-09 15:41:42
  Meid       : 65415
  Description: 65415
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
*****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"
/* added by lch for ipv6wanInfo */
#include <arpa/inet.h> // 用于IPv6相关操作（可选，用于验证）
/* end of lch added  */
extern hi_uint32 g_table_avail_size;
extern uint32_t g_ext_tab_avail_size;

hi_int32 hwtc_omci_me_65415_init()
{
    hi_ushort16 meid = HWTC_OMCI_PRO_ME_65415;
    hi_ushort16 us_instid = 0;
    hwtc_omci_me_65415_s st_entity;
    hi_int32 i_ret = HI_RET_SUCC;

    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;

    i_ret = hwtc_omci_me_65415_get(&st_entity, sizeof(st_entity), NULL);
    HI_OMCI_RET_CHECK(i_ret);
    st_entity.uc_attr1=0x04;

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

/* added by lch  */

/**
 * @brief 将二进制格式的IPv6地址转换为可读字符串（用于调试打印）
 * @param bin_addr 二进制格式的IPv6地址（16字节）
 * @param str_addr 输出的字符串缓冲区（至少需要INET6_ADDRSTRLEN=46字节）
 */
void bin_to_ipv6_string(const hi_uchar8 *bin_addr, char *str_addr) {
    struct in6_addr addr;
    memcpy(addr.s6_addr, bin_addr, 16);
    inet_ntop(AF_INET6, &addr, str_addr, INET6_ADDRSTRLEN);
}


/**
 * @brief 从IPv6地址和前缀长度计算网络前缀
 * @param ipv6_address 输入的IPv6地址（二进制形式，16字节）
 * @param prefix_len 前缀长度 (0-128)
 * @param ipv6_prefix 输出的网络前缀（二进制形式，16字节）
 */
void calculate_ipv6_prefix(const hi_uchar8 *ipv6_address, hi_uchar8 prefix_len, hi_uchar8 *ipv6_prefix) {
    if (ipv6_address == NULL || ipv6_prefix == NULL) {
        return; // 错误处理：空指针
    }

    // 1. 首先将整个地址复制到前缀数组中
    memcpy(ipv6_prefix, ipv6_address, 16);

    // 2. 如果前缀长度是0，则前缀为全零（::/0）
    if (prefix_len == 0) {
        memset(ipv6_prefix, 0, 16);
        return;
    }

    // 3. 如果前缀长度是128，则前缀就是地址本身（不需要处理主机位）
    if (prefix_len == 128) {
        // 已经通过memcpy完成了
        return;
    }

    // 4. 处理一般情况：将主机位清零
    // 计算需要保留的完整字节数
    int full_bytes = prefix_len / 8;
    // 计算剩余需要处理的比特数（在当前字节内）
    int remaining_bits = prefix_len % 8;

    // 5. 先将完整字节之后的所有字节清零（这些字节完全属于主机位）
    if (full_bytes < 15) { // 确保不会越界
        memset(ipv6_prefix + full_bytes + 1, 0, 16 - full_bytes - 1);
    }

    // 6. 处理当前字节中剩余的需要部分保留的比特
    if (remaining_bits > 0) {
        // 创建一个位掩码，用于保留前 `remaining_bits` 位，清零后面的位
        // 例如，remaining_bits=5，则掩码为 11111000 (0xF8)
        hi_uchar8 mask = (0xFF << (8 - remaining_bits)) & 0xFF;
        ipv6_prefix[full_bytes] &= mask; // 应用掩码，清零主机位
    } else {
        // 如果没有剩余比特，则当前字节是完整保留的，无需处理
    }
}
/* end of calculate_ipv6_prefix */

static void translateWanInfo(me65415_waninfo_t *pWanInfo, vs_omci_tapi_wan_entity_s *pVsWanCfg, int wanIdx)
{
    // 1.WAN序号，从1开始，占1个字节
    pWanInfo->wan_index = (wanIdx+1);
    
    //2.wan name:占32个字节 (注意：原结构WanName是40字节，这里只取32字节)
    HI_OS_STRCPY_S(pWanInfo->wan_name, sizeof(pWanInfo->wan_name), pVsWanCfg->auc_wanName1);
    HI_OS_STRCAT_S(pWanInfo->wan_name, sizeof(pWanInfo->wan_name), pVsWanCfg->auc_wanName2);

    //3.Service type 占1个字节, 0:INTERNET, 1:TR069, 2: voip, 8:OTHER, 
    if(pVsWanCfg->uc_wanConnMode == 0) //INTERNET
        pWanInfo->service_type = MIB_ME65415_SERVICE_TYPE_INTERNET;
    else if(pVsWanCfg->uc_wanConnMode == 1 || pVsWanCfg->uc_wanConnMode == 100) //OTHER
        pWanInfo->service_type = MIB_ME65415_SERVICE_TYPE_OTHER;
    else if(pVsWanCfg->uc_wanConnMode == 2) //TR069
        pWanInfo->service_type = MIB_ME65415_SERVICE_TYPE_TR069;
    else if(pVsWanCfg->uc_wanConnMode == 3) //INTERNET AND TR069
        pWanInfo->service_type = MIB_ME65415_SERVICE_TYPE_INTERNET_TR069;
    else if(pVsWanCfg->uc_wanConnMode == 4) //TR069 AND VOICE
        pWanInfo->service_type = MIB_ME65415_SERVICE_TYPE_TR069_VOIP;
    else if(pVsWanCfg->uc_wanConnMode == 5) //INTERNET AND VOICE
        pWanInfo->service_type = MIB_ME65415_SERVICE_TYPE_INTERNET_VOIP;
    else if(pVsWanCfg->uc_wanConnMode == 6) //INTERNET AND TR069 AND VOICE
        pWanInfo->service_type = MIB_ME65415_SERVICE_TYPE_INTERNET_TR069_VOIP;
    else if(pVsWanCfg->uc_wanConnMode == 7) //VOICE
        pWanInfo->service_type = MIB_ME65415_SERVICE_TYPE_VOIP;
    else //OTHER
        pWanInfo->service_type = MIB_ME65415_SERVICE_TYPE_OTHER;
    
    //3. Connection type 占1个字节, 0:unconfiguration, 0x0001:IP routed, 0x0002:IP bridge
    if (pVsWanCfg->uc_wanConnType == 1) //IP routed
        pWanInfo->conn_type = 0x01;
    else //IP bridge
        pWanInfo->conn_type = 0x02;

    
    //4.IPv4 Connection status 占一个字节, 0:unconfiguration, 0x01:connecting, 0x02:connected
    if (pVsWanCfg->uc_wanConnStatus == 1) //Connected
        pWanInfo->ipv4_conn_status = 0x02;
    else if (pVsWanCfg->uc_wanConnStatus == 0 && (pVsWanCfg->uc_wanIpProtocol == 0 || pVsWanCfg->uc_wanIpProtocol == 2)) //disconnected
        pWanInfo->ipv4_conn_status = 0x01;
    else
        pWanInfo->ipv4_conn_status = 0x00; //TODO: other status
    
    //5.IPv4 access type 占一个字节，0：-， 1：dhcp ， 2：static， 3：PPPoE
    if (pVsWanCfg->uc_wanConnType == 1) { //IP routed
        if (pVsWanCfg->uc_wanAchieveAddrMode == 0) //dhcp
            pWanInfo->ipv4_access_type = 0x01;
        else if (pVsWanCfg->uc_wanAchieveAddrMode == 1) //static
            pWanInfo->ipv4_access_type = 0x02;
        else if (pVsWanCfg->uc_wanAchieveAddrMode == 2) //PPPoE
            pWanInfo->ipv4_access_type = 0x03;
    } else { //IP bridge
        pWanInfo->ipv4_access_type = 0x00;
    }
    
    //6: IP address 占4个字节   MASK:4个字节 gateway:4个字节
    if (pVsWanCfg->uc_wanConnType == 1) { //IP routed
        // 使用网络字节序转换，防止字节序问题
        pWanInfo->ipv4_address = htonl(pVsWanCfg->ui_wanIpAddress);
        pWanInfo->ipv4_mask = htonl(pVsWanCfg->ui_wanIpMask);
        pWanInfo->ipv4_gateway = htonl(pVsWanCfg->ui_wanGateway);
    } else { //IP bridge
        pWanInfo->ipv4_address=0;
        pWanInfo->ipv4_mask=0;
        pWanInfo->ipv4_gateway=0;
    }
    
    //7.vlan id 占2个字节
    // ptr[49]和ptr[50]存储pri+vlanid，前3位为uc_wanPriority，第4位为0，后12位为us_wanVlanId
    pWanInfo->vlan_priority = (hi_uchar8)(((pVsWanCfg->uc_wanPriority & 0x07) << 5) | ((pVsWanCfg->us_wanVlanId >> 8) & 0x0F));
    pWanInfo->vlan_id_low = (hi_uchar8)(pVsWanCfg->us_wanVlanId & 0xFF);
    
    //8.Option60 占1个字节
    pWanInfo->option60 = 0x00;//NO
    
    //9.Switch 占1个字节
    pWanInfo->switch_en = (hi_uchar8)(pVsWanCfg->uc_wanNatEnable);
    
    //10.wan mac address 占6个字节
    HI_OS_MEMCPY_S(pWanInfo->wan_mac, 6, pVsWanCfg->auc_wanMac, 6);
    
    //11.uc_wanPriority policy 占1个字节, 不清楚作用
    pWanInfo->priority_policy = 0x01;
    
    //12.L2 encap-type 占1个字节 0x01 IpoE 0x02 pppoe 
    if(pVsWanCfg->uc_wanConnType == 1 &&  (pVsWanCfg->uc_wanAchieveAddrMode == 2) ){ //IP routed and pppoe
        pWanInfo->l2_encap_type = 0x02;
    }else if(pVsWanCfg->uc_wanConnType == 1){ //ip routed dhcp/static
        pWanInfo->l2_encap_type = 0x01;
    }else{
        pWanInfo->l2_encap_type=0; //invalid
    }
    
    //13.IPv4 switch 占1个字节 
    if (pVsWanCfg->uc_wanIpProtocol == 0 || pVsWanCfg->uc_wanIpProtocol == 2)//only ipv4 and ipv4/ipv6
    {
        pWanInfo->ipv4_switch = 0x01;
    }
    else
    {
        pWanInfo->ipv4_switch = 0;
        pWanInfo->ipv4_conn_status=0;
    }

/* added by lch for ipv6wanInfo */
    //14.IPv6 switch 占1个字节 
    if (pVsWanCfg->uc_wanIpProtocol == 1 || pVsWanCfg->uc_wanIpProtocol == 2)  //only ipv6 and ipv4/ipv6
    {
        pWanInfo->ipv6_switch = 0x01;//IPv6 switch
    
        //15.跳过71个字节，ipv6相关的
        if (pVsWanCfg->uc_wanConnType == 1) //ip route
        {
            /* IPv6连接状态  
                huawei: 0:unconfiguration, 0x01:connecting, 0x02:connected 0x03 pending, 0x04 desconnecting 0x05 disconnected
                vsol：for detail , search "uword8 ucIPv6ConnectionStatus; //当前IPv6连接状态 " in hi_odl_tab_wan_conn.h
            */
           switch (pVsWanCfg->uc_IPv6ConnectionStatus)
           {
           case 0:
                pWanInfo->ipv6_conn_status = 0; //0:unconfiguration
            break;
           case 1:
           case 2:
                pWanInfo->ipv6_conn_status = 0x01; //0x01:connecting
            break;
           default:
                pWanInfo->ipv6_conn_status = pVsWanCfg->uc_IPv6ConnectionStatus - 1; 
           break;
           }
            /*  DS-Lite模式
                huawei：0x01 off, 0x02 Static, 0x03 Dynamic 
            */
           
           if(pWanInfo->ipv6_conn_status==0x02) //ipv6已连接;dslite依赖于一条可用的ipv6 wan
           {
                pWanInfo->ds_lite_mode = pVsWanCfg->uc_dsliteMode;

                /* DS-Lite对端地址  */
                if(pWanInfo->ds_lite_mode==0x02)    //static
                {

                    // 1. 检查源字符串是否有有效内容
                    // 避免转换空字符串或未初始化的数据
                    if (strlen((const char*)pVsWanCfg->uc_wan_DS_Lite_address) == 0) {
                        printf("Error: AFTR address string is empty!\n");
                        memset(pWanInfo->ds_lite_peer, 0, 16); // 确保目标字段是清零的
                    }

                    // 2. 使用临时变量存储二进制结果
                    struct in6_addr bin_addr;

                    // 3. 调用 inet_pton 进行转换
                    int success = inet_pton(AF_INET6, 
                                        (const char *)pVsWanCfg->uc_wan_DS_Lite_address, // 源：文本地址
                                        &bin_addr);                                     // 目标：临时二进制变量

                    // 4. 检查转换结果
                    if (success != 1) {
                        // 转换失败处理
                        printf("Error: Invalid AFTR address format: '%s'. Clearing target field.\n", 
                            (const char *)pVsWanCfg->uc_wan_DS_Lite_address);
                        memset(pWanInfo->ds_lite_peer, 0, 16); 
                    } else {
                        // 5. 转换成功，将二进制数据复制到目标字段
                        memcpy(pWanInfo->ds_lite_peer, &bin_addr, 16); 
                        printf("Success: AFTR address '%s' converted and stored in binary form.\n", 
                            (const char *)pVsWanCfg->uc_wan_DS_Lite_address);
                    }
                }
                else if(pWanInfo->ds_lite_mode==0x03)   //dynamic
                {
                    //don't support dynamic
                }
           }
           else
           {
                pWanInfo->ds_lite_mode = 0x01;
           }

            /* 前缀获取模式:
            //prefix_mode huawei：0x01 AutoConfigured, 0x02 PrefixDelegation, 0x03 RouterAdvertisement , 0x04 static ,0x05 none 
            */
            if (pVsWanCfg->uc_wanDhcpv6ClientRequestPrefix) { //enable pd
                pWanInfo->prefix_mode = 0x2;//enable pd
            } else {
                //prefix access mode
                pWanInfo->prefix_mode = 0x1;//AutoConfigured
            }

            /* IPv6地址获取模式：
                0：DHCPv6 (有状态)​​：设备从DHCPv6服务器​​直接获取地址和其他配置​​（如DNS）。
                1：SLAAC (无状态地址自动配置)​​：设备根据路由器通告（RA）中的前缀主机​​自己生成地址​​。  
                2: Static (静态)​​：手动配置所有IPv6参数。[not support]

                //huawei：0x01 AutoConfigured, 0x02 DHCPv6, 0x03 Static, 0x04 none 

            */
            if (pVsWanCfg->uc_wanAchieveAddrMode == 1) //static
                pWanInfo->ipv6_access_mode = 0x03;//static
            else if (pVsWanCfg->uc_wanIpv6AddressMode == 1) //slaac
                pWanInfo->ipv6_access_mode = 0x01;//slaac, AutoConfigured
            else
                pWanInfo->ipv6_access_mode = 0x02;//dhcpv6
            /* 存储IPv6地址  */
            HI_OS_MEMCPY_S(pWanInfo->ipv6_address, 16, pVsWanCfg->auc_wanIPv6Address, 16);

            /* IPv6地址状态：
                ipv6_addr_status huawei：0x01 Preferred , 0x02 NoPrefix
            */
            if (pVsWanCfg->ui_wanIpv6Mask) {
                /* IPv6前缀长度  */
                pWanInfo->prefix_len = pVsWanCfg->ui_wanIpv6Mask;
                /* 存储IPv6地址前缀  */
                calculate_ipv6_prefix(pWanInfo->ipv6_address, pWanInfo->prefix_len, pWanInfo->ipv6_prefix);
                //IPv6 address prefix status 
                pWanInfo->ipv6_addr_status = 0x01;//Preferred
            }else{
                //IPv6 address prefix status 
                pWanInfo->ipv6_addr_status = 0x02;//noprefix 
                pWanInfo->prefix_len = 0;
            }

            /* IPv6主DNS服务器地址  */
            HI_OS_MEMCPY_S(pWanInfo->ipv6_primary_dns, 16, pVsWanCfg->auc_wanIpv6PrimaryDns, 16);

            /* IPv6备DNS服务器地址  */
            HI_OS_MEMCPY_S(pWanInfo->ipv6_secondary_dns, 16, pVsWanCfg->auc_wanIpv6SecondaryDns, 16);


            // /* 存储IPv6前缀  */
            // HI_OS_MEMCPY_S(pWanInfo->ipv6_prefix, 16, pVsWanCfg->auc_IPv6Prefix, 16);

            /* 前缀优选生存时间、前缀有效生存时间
               IPv6地址优选生存时间、IPv6地址有效生存时间  
               
               对于通过​​SLAAC（无状态地址自动配置）​​ 生成的地址，其生存时间通常直接继承自其所属前缀的生存时间。
               对于通过​​DHCPv6​​有状态获取的地址，其生存时间则由DHCPv6服务器单独指定。
               对于通过STATIC设置的地址，其生存时间一般为永久。
            */
           if(pWanInfo->ipv6_access_mode == 0x01) //SLAAC
           {
                pWanInfo->prefix_pref_time = htonl(pVsWanCfg->ui_IPv6PrefixPltime);
                pWanInfo->prefix_valid_time = htonl(pVsWanCfg->ui_IPv6PrefixPltime);

                pWanInfo->ipv6_pref_time = htonl(pVsWanCfg->ui_IPv6PrefixPltime);
                pWanInfo->ipv6_valid_time = htonl(pVsWanCfg->ui_IPv6PrefixPltime);
           }
           else if(pWanInfo->ipv6_access_mode == 0x03)  //STATIC
           {    
                pWanInfo->prefix_pref_time = htonl(0XFFFFFFFF);
                pWanInfo->prefix_valid_time = htonl(0XFFFFFFFF);

                pWanInfo->ipv6_pref_time = htonl(0XFFFFFFFF);
                pWanInfo->ipv6_valid_time = htonl(0XFFFFFFFF);
           }
           else{ // DHCPv6
                pWanInfo->prefix_pref_time = htonl(pVsWanCfg->ui_IPv6PrefixPltime);
                pWanInfo->prefix_valid_time = htonl(pVsWanCfg->ui_IPv6PrefixPltime);
                
                //Temporarily not supported pref_time/valid_time get from DHCPv6 server

           }

        }
    }
    else    //非ipv6
    {
        pWanInfo->ipv6_switch = 0x0;//IPv6 switch
    }
/* end of ipv6wanInfo  */
    //16.ipv4 mvlan 占2个字节
    pWanInfo->ipv4_mvlan_high = (hi_uchar8)(pVsWanCfg->us_wanVlanId >> 8);
    pWanInfo->ipv4_mvlan_low = (hi_uchar8)(pVsWanCfg->us_wanVlanId);
    
    //17.NAT switch 占1个字节
    pWanInfo->nat_switch = (hi_uchar8)(pVsWanCfg->uc_wanNatEnable);
    
    //18.ipv6 mlvan 占2个字节
    pWanInfo->ipv6_mvlan_high = (hi_uchar8)(pVsWanCfg->us_wanVlanId >> 8);
    pWanInfo->ipv6_mvlan_low = (hi_uchar8)(pVsWanCfg->us_wanVlanId);
    
    if (pVsWanCfg->uc_wanConnType == 1) { //IP routed
        // 使用网络字节序转换，防止字节序问题
        pWanInfo->ipv4_primary_dns = htonl(pVsWanCfg->ui_wanPrimaryDns);
        pWanInfo->ipv4_secondary_dns = htonl(pVsWanCfg->ui_wanSecondaryDns);
    } else { //IP bridge
        pWanInfo->ipv4_primary_dns=0;
        pWanInfo->ipv4_secondary_dns=0;
    }

}

hi_int32 hwtc_omci_me_65415_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hwtc_omci_me_65415_s  *pst_entity = (hwtc_omci_me_65415_s *)pv_data;
    vs_omci_tapi_wan_config_s  st_data;
    me65415_waninfo_t wanInfo={0};
    hi_uchar8 *pst_table = NULL, *pst=NULL;
    hi_ushort16 meid = HWTC_OMCI_PRO_ME_65415;
    hi_ushort16 us_instid, us_attrmask;
    hi_int32    i_ret = HI_RET_SUCC;
    hi_int32    i;
    hi_uchar8   uc_wan_number;
    hi_int32    tab_size=0;

    us_instid   = pst_entity->st_msghead.us_instid;
    us_attrmask = pst_entity->st_msghead.us_attmask;

    hi_omci_debug("[INFO]:us_instid=0x%x, attrmask=0x%x\n", us_instid, us_attrmask);

    if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_attrmask, HI_OMCI_ATTR2)) 
    {
        HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
        st_data.uc_cfgMode = VS_CONFIG_MODE_GET_FROM_FLASH;
        i_ret = vs_omci_tapi_wan_config_get(&st_data);
        hi_omci_debug("vs_omci_tapi_wan_config_get ret=%d, st_data.uc_wan_number=%d\n", i_ret, st_data.uc_wan_number);
        if (HI_RET_SUCC != i_ret || st_data.uc_wan_number ==0) {
            g_table_avail_size = g_ext_tab_avail_size = 0;
            hi_omci_systrace(i_ret, 0, 0, 0, 0);
            return i_ret;
        }
        uc_wan_number = st_data.uc_wan_number;
        tab_size = sizeof(me65415_waninfo_t) * uc_wan_number + 2;
        pst = (hi_uchar8 *)hi_os_malloc(tab_size);
        if (HI_NULL == pst) {
            hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
            return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
        }
        g_table_avail_size = g_ext_tab_avail_size = tab_size;
        pst_table = pst;
        pst[0] = 0x04;
        pst[1] = uc_wan_number;
        pst += 2;
        
        for (i = 0; i < uc_wan_number; i++) {
            HI_OS_MEMSET_S(pst, sizeof(me65415_waninfo_t), 0, sizeof(me65415_waninfo_t));
            HI_OS_MEMSET_S(&wanInfo, sizeof(me65415_waninfo_t), 0, sizeof(me65415_waninfo_t));
            translateWanInfo(&wanInfo, &st_data.wanCfg[i], i);
            HI_OS_MEMCPY_S(pst, sizeof(me65415_waninfo_t), (hi_uchar8 *)&wanInfo, sizeof(me65415_waninfo_t));
            pst += sizeof(me65415_waninfo_t);
        }
        
        i_ret = hi_omci_ext_set_attr(meid, us_instid, HI_OMCI_ATTR2, pst_table);
        hi_os_free(pst_table);
        HI_OMCI_RET_CHECK(i_ret);
    }

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_65415_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

