/******************************************************************************

                  Copyright (C), 2024-2025 VSOL

 ******************************************************************************
  Filename   : hwtc_omci_me_65398.c
  Version    : 1.0
  Author     : Bohannon
  Creation   : 2025-09-04 19:16:00
  Meid       : 65398
  Description: 65398
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
*****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

hi_void vs_omci_me_access_control_thread_handler(hi_void *arg)
{
    hi_int32 i_ret = HI_RET_SUCC;
    vs_omci_tapi_access_control_s st_tapi;

    HI_OS_MEMSET_S(&st_tapi,sizeof(st_tapi),0,sizeof(st_tapi));

    pthread_mutex_lock(&g_vsol_mutex);
    st_tapi.uc_cfgMode = VS_CONFIG_MODE_SET_TO_FLASH;
    i_ret = vs_omci_tapi_access_control_set(&st_tapi, 0xff);
    if (HI_RET_SUCC != i_ret)
    {
        PRT("vs_omci_tapi_access_control_set fail\n");
    }
    pthread_mutex_unlock(&g_vsol_mutex);
    pthread_exit(NULL); 
}


static hi_void vs_omci_me_access_control_timer_handler(hi_void *pv_cookie)
{
    hi_int32 i_ret = HI_RET_SUCC;
    pthread_t thread_id;

    i_ret = vs_omci_phtread_create(&thread_id, (VS_CALLBACK)vs_omci_me_access_control_thread_handler, NULL);
    if (HI_RET_SUCC != i_ret)
    {
        PRT("vs_omci_phtread_create fail\n");
    }

    i_ret = vs_omci_timer_delete(VS_OMCI_TIMER_MASK_ACCESS_CONTROL);
    if (HI_RET_SUCC != i_ret)
    {
        PRT("vs_omci_timer_delete fail\n");
    }
}

hi_int32 hwtc_omci_me_65398_init()
{
    hi_ushort16 meid = HWTC_OMCI_PRO_ME_65398;
    hi_ushort16 us_instid = 0;
    hwtc_omci_me_65398_s st_entity;
    hi_int32 i_ret = HI_RET_SUCC;

    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;

    i_ret = hwtc_omci_me_65398_get(&st_entity, sizeof(st_entity), NULL);
    HI_OMCI_RET_CHECK(i_ret);

    st_entity.uc_attr11=0x02;

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_65398_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_65398_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hwtc_omci_me_65398_s *pst_entity = (hwtc_omci_me_65398_s *)pv_data;
    vs_omci_tapi_access_control_s st_data;
    hi_ushort16 us_attrmask;
    hi_uint32 i_ret = HI_RET_SUCC;
    hi_uchar8 uc_service_type;
    
    us_attrmask = pst_entity->st_msghead.us_attmask;

    hi_omci_debug("[INFO]:attrmask=0x%x\n", us_attrmask);
    //wan http
    if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_attrmask, HI_OMCI_ATTR2))
    {
        HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
        st_data.uc_cfgMode = VS_CONFIG_MODE_GET_FROM_FLASH;
        uc_service_type = VS_ACCESS_CONTROL_HTTP;
        i_ret = vs_omci_tapi_access_control_get(&st_data, uc_service_type);
        HI_OMCI_RET_CHECK(i_ret);
        
        st_data.uc_wan_enable = pst_entity->uc_attr2?1:0;
        st_data.uc_access_control_enable = 1;
        hi_omci_debug("uc_service_type=%d, st_data.uc_wan_enable=%d\n",uc_service_type, st_data.uc_wan_enable);
    }
    //lan telnet
    if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_attrmask, HI_OMCI_ATTR3))
    {
        HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
        st_data.uc_cfgMode = VS_CONFIG_MODE_GET_FROM_FLASH;
        uc_service_type = VS_ACCESS_CONTROL_TELNET;
        i_ret = vs_omci_tapi_access_control_get(&st_data, uc_service_type);
        HI_OMCI_RET_CHECK(i_ret);
        
        st_data.uc_lan_enable = pst_entity->uc_attr3?1:0;
        st_data.uc_access_control_enable = 1;
        hi_omci_debug("uc_service_type=%d, st_data.uc_lan_enable=%d\n",uc_service_type, st_data.uc_lan_enable);
    }
    //lan http
    if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_attrmask, HI_OMCI_ATTR4))
    {
        HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
        st_data.uc_cfgMode = VS_CONFIG_MODE_GET_FROM_FLASH;
        uc_service_type = VS_ACCESS_CONTROL_HTTP;
        i_ret = vs_omci_tapi_access_control_get(&st_data, uc_service_type);
        HI_OMCI_RET_CHECK(i_ret);
        
        st_data.uc_lan_enable = pst_entity->uc_attr4?1:0;
        st_data.uc_access_control_enable = 1;
        hi_omci_debug("uc_service_type=%d, st_data.uc_lan_enable=%d\n",uc_service_type, st_data.uc_lan_enable);
    }
    
    if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_attrmask, HI_OMCI_ATTR10))
    {
        HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
        st_data.uc_cfgMode = VS_CONFIG_MODE_GET_FROM_FLASH;
        uc_service_type = VS_ACCESS_CONTROL_HTTP;
        i_ret = vs_omci_tapi_access_control_get(&st_data, uc_service_type);
        HI_OMCI_RET_CHECK(i_ret);
        
        if(pst_entity->us_attr10>0){
            st_data.uc_wan_enable = 1;
            st_data.uc_access_control_enable = 1;
            st_data.us_port=7017;
        }else{
            st_data.uc_wan_enable = 0;
            st_data.us_port=80;
            st_data.uc_access_control_enable = 1;
        }
        hi_omci_debug("uc_service_type=%d, st_data.uc_wan_enable=%d, st_data.us_port=%d\n",uc_service_type, st_data.uc_wan_enable, st_data.us_port);
    }

    /* force firewall level to low */
    if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_attrmask, HI_OMCI_ATTR2) || HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_attrmask, HI_OMCI_ATTR10))
    {
        vs_omci_tapi_firewall_level_s st_data;
        
        st_data.uc_cfgMode = VS_CONFIG_MODE_SET_TO_FLASH;
        st_data.uc_firewall_level = VS_FIREWALL_LEVEL_LOW;
        vs_omci_tapi_firewall_level_set(&st_data);
        hi_omci_debug("set firewall low\n");
    }

    st_data.uc_cfgMode = VS_CONFIG_MODE_SET_TO_RAM;
    i_ret = vs_omci_tapi_access_control_set(&st_data, uc_service_type);
    HI_OMCI_RET_CHECK(i_ret);
    if (HI_NULL == vs_omci_timer_search(VS_OMCI_TIMER_MASK_ACCESS_CONTROL))
    {
        i_ret = vs_omci_timer_create(vs_omci_me_access_control_timer_handler, VS_OMCI_TIMER_MASK_ACCESS_CONTROL, VS_OMCI_TIMER_TIMEOUT);
        if (HI_RET_SUCC != i_ret)
        {
            hi_omci_debug("vs_omci_me_access_control create timer fail\n");
            HI_OMCI_RET_CHECK(i_ret);
        }
    }
    else
    {
        i_ret = vs_omci_timer_restart(VS_OMCI_TIMER_MASK_ACCESS_CONTROL);
        if (HI_RET_SUCC != i_ret)
        {
            hi_omci_debug("vs_omci_me_access_control restart timer fail\n");
            HI_OMCI_RET_CHECK(i_ret);
        } 
    }

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}


