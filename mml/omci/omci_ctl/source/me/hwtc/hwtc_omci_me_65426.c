/******************************************************************************

                  Copyright (C), 2024-2025 VSOL

 ******************************************************************************
  Filename   : hwtc_omci_me_65426.c
  Version    : 1.0
  Author     : Bohannon
  Creation   : 2025-09-10 10:37:10
  Meid       : 65426
  Description: 65426
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
*****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

//move hwtc_omci_me_65426_init to hi_omci_me_lib.h
hi_int32 hwtc_omci_me_65426_init()
{
    hi_ushort16 meid = HWTC_OMCI_PRO_ME_65426;
    hi_ushort16 us_instid = 1;
    hwtc_omci_me_65426_s st_entity;
    hi_int32 i_ret = HI_RET_SUCC;

    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;

    i_ret = hwtc_omci_me_65426_get(&st_entity, sizeof(st_entity), NULL);
    HI_OMCI_RET_CHECK(i_ret);

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}
    
hi_int32 hwtc_omci_me_65426_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hwtc_omci_me_65426_s  *pst_entity = (hwtc_omci_me_65426_s *)pv_data;
    hwtc_omci_me_65426_s  st_entity;
    hi_ushort16 meid = HWTC_OMCI_PRO_ME_65426;
    hi_ushort16 us_instid, us_attrmask;
    hi_uint32   i_ret = HI_RET_SUCC;

    us_instid   = pst_entity->st_msghead.us_instid;
    us_attrmask = pst_entity->st_msghead.us_attmask;

    hi_omci_debug("[INFO]:us_instid=0x%x, attrmask=0x%x\n", us_instid, us_attrmask);
    
    i_ret = hi_omci_ext_get_inst(meid, us_instid, &st_entity);
    if (HI_RET_SUCC != i_ret) {
        hi_omci_systrace(i_ret, 0, 0, 0, 0);
        return i_ret;
    }

    if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_attrmask, HI_OMCI_ATTR1)) 
    {
        st_entity.us_attr1 = 0x00;
    }

    if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_attrmask, HI_OMCI_ATTR2)) 
    {
        st_entity.us_attr2 = 0xffff;
    }

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_65426_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}
