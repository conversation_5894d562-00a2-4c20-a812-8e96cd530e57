/******************************************************************************

                  Copyright (C), 2024-2025 VSOL

 ******************************************************************************
  Filename   : hwtc_omci_me_65423.c
  Version    : 1.0
  Author     : Bohannon
  Creation   : 2025-09-05 08:34:34
  Meid       : 65423
  Description: 65423
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
*****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

hi_int32 hwtc_omci_me_65423_init()
{
    hi_ushort16 meid = HWTC_OMCI_PRO_ME_65423;
    hi_ushort16 us_instid = 0;
    hwtc_omci_me_65423_s st_entity;
    hi_omci_tapi_sysinfo_s sy_info;
    hi_omci_tapi_sn_loid_s sn_info;
    hi_int32 i_ret = HI_RET_SUCC;
    
    unsigned char attr6[]={0x07, 0xe7, 0x05, 0x06, 0x07, 0x15, 0x36, 0x00};

    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;

    i_ret = hwtc_omci_me_65423_get(&st_entity, sizeof(st_entity), NULL);
    HI_OMCI_RET_CHECK(i_ret);
    st_entity.uc_attr1 = 0x01;
    st_entity.uc_attr2 = 0x01;
    st_entity.uc_attr3 = 0x00;
    st_entity.uc_attr4 = 0x01;
    st_entity.ui_attr5=0x0001c200;
    HI_OS_MEMCPY_S(st_entity.auc_attr6, sizeof(st_entity.auc_attr6), attr6, sizeof(st_entity.auc_attr6));
    st_entity.uc_attr7=0x01;
    
    i_ret = hi_omci_tapi_sysinfo_get(&sy_info);
    HI_OMCI_RET_CHECK(i_ret);
    HI_OS_STRNCPY_S((hi_char8 *)st_entity.auc_attr8, sizeof(st_entity.auc_attr8), (hi_char8 *)sy_info.auc_equid, sizeof(st_entity.auc_attr8));

    i_ret = hi_omci_tapi_sn_loid_get(&sn_info);
    HI_OMCI_RET_CHECK(i_ret);
    HI_OS_MEMCPY_S(st_entity.auc_attr9, sizeof(st_entity.auc_attr9), sn_info.auc_mac , sizeof(st_entity.auc_attr9));

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_65423_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_65423_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

