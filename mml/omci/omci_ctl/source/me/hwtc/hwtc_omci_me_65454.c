/******************************************************************************

                  Copyright (C), 2024-2025 VSOL

 ******************************************************************************
  Filename   : hwtc_omci_me_65454.c
  Version    : 1.0
  Author     : Bohannon
  Creation   : 2025-09-10 14:00:03
  Meid       : 65454
  Description: 65454
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
*****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

hi_int32 hwtc_omci_me_65454_init(hi_ushort16 port_id)
{
    hi_ushort16 meid = HWTC_OMCI_PRO_ME_65454;
    hi_ushort16 us_instid = HI_OMCI_ME_WIFI_INSTID+port_id;
    hwtc_omci_me_65454_s st_entity;
    hi_int32 i_ret = HI_RET_SUCC;

    if(hwtc_omci_tapi_is_valid_ssid_idx(port_id) != HI_RET_SUCC){
        hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
        return HI_RET_SUCC;
    }

    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;
    
    i_ret = hi_omci_ext_get_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    st_entity.us_attr2 = WLAN_AP_STA_INFO_ENTRY_MAX; // max sta num

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

//TODO: AX ?
static hi_ushort16 translate_wlan_std(int wlan_std)
{
    switch(wlan_std)
    {
        case VS_WIFI_STANDARD_B:
            return MEHW65454_ATTR1_11B;
        case VS_WIFI_STANDARD_G:
            return MEHW65454_ATTR1_11G;
        case VS_WIFI_STANDARD_BG:
            return MEHW65454_ATTR1_11B|MEHW65454_ATTR1_11G;
        case VS_WIFI_STANDARD_N:
        case VS_WIFI_STANDARD_AC_N:
            return MEHW65454_ATTR1_11N;
        case VS_WIFI_STANDARD_BGN:
            return MEHW65454_ATTR1_11B|MEHW65454_ATTR1_11G|MEHW65454_ATTR1_11N;
        case VS_WIFI_STANDARD_AC:
            return MEHW65454_ATTR1_11AC;
        case VS_WIFI_STANDARD_AC_A:
            return MEHW65454_ATTR1_11A;
        case VS_WIFI_STANDARD_AC_A_N:
            return MEHW65454_ATTR1_11A|MEHW65454_ATTR1_11N;
        case VS_WIFI_STANDARD_AC_N_AC:
            return MEHW65454_ATTR1_11N|MEHW65454_ATTR1_11AC;
        case VS_WIFI_STANDARD_AC_A_N_AC:
            return MEHW65454_ATTR1_11A|MEHW65454_ATTR1_11N|MEHW65454_ATTR1_11AC;
        case VS_WIFI_STANDARD_GN:
            return MEHW65454_ATTR1_11G|MEHW65454_ATTR1_11N;
        default:
            return MEHW65454_ATTR1_11B|MEHW65454_ATTR1_11G|MEHW65454_ATTR1_11N;
    };
    /* default b/g/n*/
    return MEHW65454_ATTR1_11B|MEHW65454_ATTR1_11G|MEHW65454_ATTR1_11N;
}

hi_int32 hwtc_omci_me_65454_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hwtc_omci_me_65454_s  *pst_entity = (hwtc_omci_me_65454_s *)pv_data;
    hwtc_omci_me_65454_s  st_entity;
    vs_omci_tapi_wifi_control_s  wc_data;
    hwtc_omci_me_65454_sta_info_s  sta_data;
    hi_ushort16 meid = HWTC_OMCI_PRO_ME_65454;
    hi_ushort16 us_instid, us_attrmask;
    hi_uint32   i_ret = HI_RET_SUCC;
    hi_uchar8   uc_wifiInterface=0;
    hi_uchar8   uc_ssidIdx=0;
    
    us_instid   = pst_entity->st_msghead.us_instid;
    us_attrmask = pst_entity->st_msghead.us_attmask;

    hi_omci_debug("[INFO]:us_instid=0x%x, attrmask=0x%x\n", us_instid, us_attrmask);
    
    i_ret = hi_omci_ext_get_inst(meid, us_instid, &st_entity);
    if (HI_RET_SUCC != i_ret) {
        hi_omci_systrace(i_ret, 0, 0, 0, 0);
        return i_ret;
    }
    
    uc_ssidIdx = uc_wifiInterface = us_instid & 0xff;
    uc_wifiInterface /=5; /* 1 ~ 4 -> 0 -> 5G wifi;  5 ~ -> 1 -> 2.4G */
    uc_ssidIdx -= 1; /* 801 ~ 808 -> 0 ~ 7 */
    
    if(uc_wifiInterface >= VS_OMCI_TAPI_WIFI_NUM){
        hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
        return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
    }
    
    //Wireless Standard
    if(HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_attrmask, HI_OMCI_ATTR1)){
        HI_OS_MEMSET_S(&wc_data, sizeof(wc_data), 0, sizeof(wc_data));
        wc_data.uc_cfgMode = VS_CONFIG_MODE_GET_FROM_FLASH;
        wc_data.uc_wifiInterface = uc_wifiInterface;
        vs_omci_tapi_wifi_control_get(&wc_data);
        st_entity.us_attr1 = translate_wlan_std(wc_data.wifiGlobalCfg[uc_wifiInterface].uc_wifiStandard);
    }
    
    //Current associate number
    if(HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_attrmask, HI_OMCI_ATTR3)){
        HI_OS_MEMSET_S(&wc_data, sizeof(wc_data), 0, sizeof(wc_data));
        sta_data.uc_ssidIdx = uc_ssidIdx;
        hwtc_omci_tapi_stainfo_get(&sta_data);
        st_entity.us_attr3 = sta_data.ui_staNum;
    }

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_65454_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

