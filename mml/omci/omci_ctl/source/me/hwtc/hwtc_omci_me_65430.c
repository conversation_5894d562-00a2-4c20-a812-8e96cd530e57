/******************************************************************************

                  Copyright (C), 2024-2025 VSOL

 ******************************************************************************
  Filename   : hwtc_omci_me_65430.c
  Version    : 1.0
  Author     : Bohannon
  Creation   : 2025-09-10 11:20:55
  Meid       : 65430
  Description: 65430
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
*****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


//move hwtc_omci_me_65430_init to hi_omci_me_lib.h
hi_int32 hwtc_omci_me_65430_init()
{
    hi_ushort16 meid = HWTC_OMCI_PRO_ME_65430;
    hi_ushort16 us_instid = 0;
    hwtc_omci_me_65430_s st_entity;
    hi_int32 i_ret = HI_RET_SUCC;

    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;

    i_ret = hwtc_omci_me_65430_get(&st_entity, sizeof(st_entity), NULL);
    HI_OMCI_RET_CHECK(i_ret);

    st_entity.uc_attr1 = 0x01;
    st_entity.uc_attr2 = 0x01;
    st_entity.uc_attr3 = 0x00;
    st_entity.uc_attr4 = 0x05;
    st_entity.uc_attr5 = 0x02;
    st_entity.uc_attr6 = 0x14;
    st_entity.uc_attr7 = 0x0a;
    st_entity.uc_attr8 = 0x12;
    st_entity.uc_attr9 = 0x0a;
    st_entity.uc_attr10 = 0x05;
    st_entity.uc_attr11 = 0x05;
    st_entity.uc_attr12 = 0x00;
    st_entity.uc_attr13 = 0x00;
    st_entity.uc_attr14 = 0x01;
    st_entity.uc_attr15 = 0x01;
    st_entity.uc_attr16 = 0x01;

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}
    
hi_int32 hwtc_omci_me_65430_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_65430_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}
