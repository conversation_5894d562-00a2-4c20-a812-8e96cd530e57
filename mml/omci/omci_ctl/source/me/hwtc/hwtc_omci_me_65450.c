/******************************************************************************

                  Copyright (C), 2024-2025 VSOL

 ******************************************************************************
  Filename   : hwtc_omci_me_65450.c
  Version    : 1.0
  Author     : Bohannon
  Creation   : 2025-09-10 11:42:59
  Meid       : 65450
  Description: 65450
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
*****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"
#include "hi_timer.h"

extern hi_uchar8 g_uc_omci_testresultbuf[HI_OMCI_PROC_MSG_MAXLEN];

hi_int32 hwtc_omci_me_65450_init()
{
    hi_ushort16 meid = HWTC_OMCI_PRO_ME_65450;
    hi_ushort16 us_instid = 0;
    hwtc_omci_me_65450_s st_entity;
    hi_int32 i_ret = HI_RET_SUCC;

    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;

    i_ret = hwtc_omci_me_65450_get(&st_entity, sizeof(st_entity), NULL);
    HI_OMCI_RET_CHECK(i_ret);
    
    st_entity.uc_attr1=0x03;
    st_entity.uc_attr2=0x02;
    st_entity.uc_attr3=0x00;
    st_entity.uc_attr4=0x01;
    st_entity.uc_attr5=0x00;

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}
    
hi_int32 hwtc_omci_me_65450_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_65450_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

static hi_ushort16 get_total_ap_num(void)
{
    hi_omci_tapi_sysinfo_s st_info;
    hi_uint32 ap_num, i;
    hi_int32 i_ret = HI_RET_SUCC;
    
    i_ret = hi_omci_tapi_sysinfo_get(&st_info);
    HI_OMCI_RET_CHECK(i_ret);

    ap_num=0;
    for (i=0; i<st_info.ui_ssid_num; i++)
    {
        if(hwtc_omci_tapi_is_valid_ssid_idx(i) == HI_RET_SUCC){
            ap_num++;
        }
    }
    hi_omci_debug("ap_num=0x%x\n", ap_num);

    return ap_num;
}

static hi_uint32 mehw65450_test_handler_basic(hi_ushort16 gpbPointer, mehw65450_test_resp_basic_hdr_t* basic_info_hdr)
{
    mehw65450_test_resp_basic_t basic_info;
    hi_omci_tapi_sysinfo_s st_info;
    hi_uchar8 *tbl_ptr=NULL;
    hi_uchar8 *ptr_hdr=NULL;
    hi_int32 i_ret = HI_RET_SUCC;
    hi_int32 tbl_size=0, tbl_maxsize=0;
    hi_ushort16 port_id;
    hi_uint32 ui_tabnum;
    vs_omci_tapi_ssid_s  st_data;
    hwtc_omci_me_65450_ssid_pktstat_s ssid_pktstat={0};
    
    i_ret = hi_omci_tapi_sysinfo_get(&st_info);
    HI_OMCI_RET_CHECK(i_ret);
    tbl_size = sizeof(mehw65450_test_resp_basic_hdr_t) + sizeof(mehw65450_test_resp_basic_t)*(basic_info_hdr->apNums);
    
    i_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_GPB, HI_OMCI_ATTR2, &ui_tabnum);
    HI_OMCI_RET_CHECK(i_ret);
    tbl_maxsize = ui_tabnum*32; // 32 is from omci db file
    tbl_size = tbl_size<tbl_maxsize?tbl_size:tbl_maxsize;
    
    hi_omci_debug("tbl_size=%d\n", tbl_size);
    tbl_ptr = (hi_uchar8 *)hi_os_malloc(tbl_size);
    if (HI_NULL == tbl_ptr) {
        hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
        return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
    }
    ptr_hdr = tbl_ptr;
    memcpy(tbl_ptr, basic_info_hdr, sizeof(mehw65450_test_resp_basic_hdr_t));
    tbl_ptr += sizeof(mehw65450_test_resp_basic_hdr_t);

    
    for(port_id=0; port_id<st_info.ui_ssid_num; port_id++){
        if(tbl_ptr-ptr_hdr > tbl_size){
            break;
        }
        
        if(hwtc_omci_tapi_is_valid_ssid_idx(port_id) != HI_RET_SUCC){
            continue;
        }
        
        memset(&basic_info, 0, sizeof(basic_info));
        HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
        st_data.uc_wifiSsidIndex = port_id;
        i_ret = vs_omci_tapi_ssid_get(&st_data);
        if(i_ret == HI_RET_SUCC){
            HI_OS_STRCPY_S((hi_char8 *)basic_info.ssidName, sizeof(basic_info.ssidName), st_data.ssidData[port_id].auc_ssidName1);
            HI_OS_STRCAT_S((hi_char8 *)basic_info.ssidName, sizeof(basic_info.ssidName), st_data.ssidData[port_id].auc_ssidName2);
            hi_omci_debug("basic_info.ssidName=%s\n", basic_info.ssidName);
            ssid_pktstat.ulSSIDIndex=port_id;
            hwtc_omci_tapi_ssid_pktstat_get(&ssid_pktstat);
            basic_info.ssidIdx= (port_id+1);
            basic_info.transmissionQualityLevel=QUALIRY_LEVEL_GOOD;
            basic_info.sentPackets = htonl(ssid_pktstat.ulTotalPacketsSent);
            basic_info.sentErrPackets = htonl(ssid_pktstat.ulErrorPacketsSent);
            basic_info.sentDiscardPackets = htonl(ssid_pktstat.ulDroppedPacketsSent);
            basic_info.recvPackets = htonl(ssid_pktstat.ulTotalPacketsReceived);
            basic_info.recvErrPackets = htonl(ssid_pktstat.ulErrorPacketsReceived);
            basic_info.recvDiscardPackets = htonl(ssid_pktstat.ulDroppedPacketsReceived);
            memcpy(tbl_ptr, &basic_info, sizeof(basic_info));
            tbl_ptr+=sizeof(basic_info);
        }
    }
    
    i_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_GPB, gpbPointer, HI_OMCI_ATTR2, ptr_hdr);
    HI_OMCI_RET_CHECK(i_ret);
    i_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_GPB, gpbPointer, HI_OMCI_ATTR3, &tbl_size);
    HI_OMCI_RET_CHECK(i_ret);

    if(ptr_hdr != NULL){
        hi_os_free(ptr_hdr);
    }
    return i_ret;
}

static hi_uint32 mehw65450_test_handler_sta_info(hi_ushort16 gpbPointer, mehw65450_test_resp_sta_info_hdr_t* sta_info_hdr)
{
    hi_uchar8 *tbl_ptr=NULL;
    hi_uchar8 *ptr_hdr=NULL;
    hi_int32 i_ret = HI_RET_SUCC;
    hi_int32 tbl_size=0, tbl_maxsize=0;
    hi_ushort16 port_id;
    hi_uint32 ui_tabnum;
    unsigned short staNums=0;
    mehw65450_test_resp_sta_info_t sta_info;
    hwtc_omci_me_65454_sta_info_s *psta_data=NULL, *sta_data=NULL;
    vs_omci_tapi_ssid_s  st_data;
    hi_omci_tapi_sysinfo_s st_info;
    hi_uint32 i;
    
    i_ret = hi_omci_tapi_sysinfo_get(&st_info);
    HI_OMCI_RET_CHECK(i_ret);
    
    psta_data = (hwtc_omci_me_65454_sta_info_s *)hi_os_malloc(sizeof(hwtc_omci_me_65454_sta_info_s)*st_info.ui_ssid_num);
    if(NULL == psta_data){
        hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
        return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
    }
    hi_os_memset(psta_data, 0, (sizeof(hwtc_omci_me_65454_sta_info_s)*st_info.ui_ssid_num));
    
    for(port_id=0; port_id<st_info.ui_ssid_num; port_id++){
        //get ssid name
        if(hwtc_omci_tapi_is_valid_ssid_idx(port_id) != HI_RET_SUCC){
            continue;
        }
        
        //get sta data info of each ssid 
        sta_data = (hwtc_omci_me_65454_sta_info_s *)(psta_data+port_id);
        sta_data->uc_ssidIdx = port_id;
        hwtc_omci_tapi_stainfo_get(sta_data);
        
        for(i=0; i<sta_data->ui_staNum; i++){
            staNums++;
        }
    }
    
    hi_omci_debug("staNums=%d\n", staNums);
    tbl_size = sizeof(mehw65450_test_resp_sta_info_hdr_t) + sizeof(mehw65450_test_resp_sta_info_t)*staNums;
    
    i_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_GPB, HI_OMCI_ATTR2, &ui_tabnum);
    HI_OMCI_RET_CHECK(i_ret);
    tbl_maxsize = ui_tabnum*32; // 32 is from omci db file
    tbl_size = tbl_size<tbl_maxsize?tbl_size:tbl_maxsize;
    
    hi_omci_debug("tbl_size=%d\n", tbl_size);
    tbl_ptr = (hi_uchar8 *)hi_os_malloc(tbl_size);
    if (HI_NULL == tbl_ptr) {
        hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
        return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
    }
    ptr_hdr = tbl_ptr;

    //update sta_info_hdr.staNums
    sta_info_hdr->staNums = htons(staNums);
    //copy sta info hdr first
    memcpy(tbl_ptr, sta_info_hdr, sizeof(mehw65450_test_resp_sta_info_hdr_t));
    tbl_ptr += sizeof(mehw65450_test_resp_sta_info_hdr_t);

    for(port_id=0; port_id<st_info.ui_ssid_num; port_id++){
        sta_data = (hwtc_omci_me_65454_sta_info_s *)(psta_data+port_id);
        
        HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
        st_data.uc_wifiSsidIndex = port_id;
        i_ret = vs_omci_tapi_ssid_get(&st_data);
        
        for(i=0; i<sta_data->ui_staNum; i++){
            if(tbl_ptr-ptr_hdr > tbl_size){
                break;
            }
            hi_os_memset(&sta_info, 0, sizeof(sta_info));
            
            HI_OS_STRCPY_S((hi_char8 *)sta_info.ssidName, sizeof(sta_info.ssidName), (hi_char8 *)st_data.ssidData[port_id].auc_ssidName1);
            HI_OS_STRCAT_S((hi_char8 *)sta_info.ssidName, sizeof(sta_info.ssidName), (hi_char8 *)st_data.ssidData[port_id].auc_ssidName2);
            hi_omci_debug("sta_info.ssidName=%s\n", sta_info.ssidName);
            sta_info.ssidIdx = (port_id+1);
            memcpy(sta_info.staMac, sta_data->sta_info[i].auc_macaddr ,6);
            sta_info.txRate = htons((hi_ushort16)sta_data->sta_info[i].tx_oprate);
            sta_info.rxRate = htons((hi_ushort16)sta_data->sta_info[i].rx_oprate);
            sta_info.rssi= sta_data->sta_info[i].rssi-100;
            sta_info.snr = sta_data->sta_info[i].snr;
            sta_info.transmissionQualityLevel=QUALIRY_LEVEL_GOOD; //TODO: should depend on rssi and snr value;
            memcpy(tbl_ptr, &sta_info, sizeof(sta_info));
            tbl_ptr+=sizeof(sta_info);
        }
    }
    
    i_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_GPB, gpbPointer, HI_OMCI_ATTR2, ptr_hdr);
    HI_OMCI_RET_CHECK(i_ret);
    i_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_GPB, gpbPointer, HI_OMCI_ATTR3, &tbl_size);
    HI_OMCI_RET_CHECK(i_ret);

    if(ptr_hdr != NULL){
        hi_os_free(ptr_hdr);
    }
    if(psta_data != NULL){
        hi_os_free(psta_data);
    }
    return i_ret;
}

static hi_uint32 mehw65450_test_handler_chan_info(hi_ushort16 gpbPointer, mehw65450_test_resp_channel_info_hdr_t *chan_info_hdr)
{
    hi_uchar8 *tbl_ptr=NULL;
    hi_uchar8 *ptr_hdr=NULL;
    hi_int32 i_ret = HI_RET_SUCC;
    hi_int32 tbl_size=0, tbl_maxsize=0;
    hi_uint32 ui_tabnum;
    
    tbl_size = sizeof(mehw65450_test_resp_channel_info_hdr_t) + sizeof(mehw65450_test_resp_channel_info_t)*(chan_info_hdr->channelNums);
    
    i_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_GPB, HI_OMCI_ATTR2, &ui_tabnum);
    HI_OMCI_RET_CHECK(i_ret);
    tbl_maxsize = ui_tabnum*32; // 32 is from omci db file
    tbl_size = tbl_size<tbl_maxsize?tbl_size:tbl_maxsize;
    
    hi_omci_debug("tbl_size=%d\n", tbl_size);
    tbl_ptr = (hi_uchar8 *)hi_os_malloc(tbl_size);
    if (HI_NULL == tbl_ptr) {
        hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
        return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
    }
    ptr_hdr = tbl_ptr;
    memcpy(tbl_ptr, chan_info_hdr, sizeof(mehw65450_test_resp_channel_info_hdr_t));
    
    i_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_GPB, gpbPointer, HI_OMCI_ATTR2, ptr_hdr);
    HI_OMCI_RET_CHECK(i_ret);
    i_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_GPB, gpbPointer, HI_OMCI_ATTR3, &tbl_size);
    HI_OMCI_RET_CHECK(i_ret);

    if(ptr_hdr != NULL){
        hi_os_free(ptr_hdr);
    }
    
    return i_ret;
}

static void *hwtc_omci_me_65450_test_handler(hi_void *pv_cookie)
{
    hi_ushort16 testType,gpbPointer;
    hi_uchar8 *content = HI_NULL;
    hi_uchar8 tst_rslt[HI_OMCI_PROC_MSGSTCONTEXT_LEN + HI_OMCI_PROC_MSGSTTAIL_LEN]={0};
    hi_char8 *apID="ONU";
    hi_char8 uc_devid;
    
    hi_uchar8 * pv_data = (hi_uchar8*)pv_cookie;
    
    uc_devid = (hi_uchar8)HI_OMCI_PROC_GET_DEVID((hi_uchar8 *)pv_data);

    mehw65450_test_resp_basic_hdr_t basic_info_hdr;
    mehw65450_test_resp_sta_info_hdr_t sta_info_hdr;
    mehw65450_test_resp_channel_info_hdr_t chan_info_hdr;

    if(uc_devid == HI_OMCI_PROC_DEVICEID_STD){
        content = HI_OMCI_PROC_STD_GET_CONTENT((hi_uchar8 *)pv_data);
    }else{
        content = HI_OMCI_PROC_EXT_GET_CONTENT((hi_uchar8 *)pv_data);
    }
    
    testType = ntohs(*(hi_ushort16 *)content);
    gpbPointer = ntohs(*(hi_ushort16 *)(content + 2));
    hi_omci_debug("testType=0x%x, gpbPointer=0x%x\n", testType, gpbPointer);
    
    switch(testType){
        case MEHW65450_TEST_WLAN_STATUS_BASIC:
            basic_info_hdr.testType = htons(MEHW65450_TEST_WLAN_STATUS_BASIC);
            basic_info_hdr.resv1 = htons(0x6701);
            memcpy(basic_info_hdr.apID, apID,3);
            memset(basic_info_hdr.resv2, 0, 28);
            basic_info_hdr.apNums=htons(get_total_ap_num());
            mehw65450_test_handler_basic(gpbPointer, &basic_info_hdr);
            break;
        case MEHW65450_TEST_WLAN_STATUS_STA_INFO:
            sta_info_hdr.testType = htons(MEHW65450_TEST_WLAN_STATUS_STA_INFO);
            sta_info_hdr.resv1 = htons(0x3201);
            memcpy(sta_info_hdr.apID, apID,3);
            memset(sta_info_hdr.resv2, 0, 28);
            sta_info_hdr.staNums=0;
            mehw65450_test_handler_sta_info(gpbPointer, &sta_info_hdr);
            break;
        //TODO: suppot scanning neighbor channel info later 
        case MEHW65450_TEST_WLAN_STATUS_CHANNEL_INFO:
            chan_info_hdr.testType = htons(MEHW65450_TEST_WLAN_STATUS_CHANNEL_INFO);
            chan_info_hdr.resv1 = htons(0x0401);
            memcpy(chan_info_hdr.apID, apID,3);
            memset(chan_info_hdr.resv2, 0, 28);
            chan_info_hdr.channelNums=0;
            mehw65450_test_handler_chan_info(gpbPointer, &chan_info_hdr);
            break;
        default:
            break;
    }
    
    *(hi_ushort16 *)&tst_rslt[0] = htons(testType&0xff00);
    *(hi_ushort16 *)&tst_rslt[2] = htons(gpbPointer);
    memcpy(g_uc_omci_testresultbuf, (hi_void *)&tst_rslt[0], HI_OMCI_PROC_MSGSTCONTEXT_LEN + HI_OMCI_PROC_MSGSTTAIL_LEN);
    
    if(pv_data){
        hi_os_free(pv_data);
    }
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);

    return NULL;
}

hi_int32 hwtc_omci_me_65450_test(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_uchar8 *pv_cookie=NULL;
    
    pv_cookie = (hi_uchar8 *)hi_os_malloc(ui_inlen);
    if(NULL != pv_cookie){
        memcpy(pv_cookie, (hi_uchar8 *)pv_data, ui_inlen);
        hwtc_omci_me_65450_test_handler(pv_cookie);
    }
    
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

