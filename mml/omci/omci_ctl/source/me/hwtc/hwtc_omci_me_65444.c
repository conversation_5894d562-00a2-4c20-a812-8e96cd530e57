/******************************************************************************

                  Copyright (C), 2024-2025 VSOL

 ******************************************************************************
  Filename   : hwtc_omci_me_65444.c
  Version    : 1.0
  Author     : Bohannon
  Creation   : 2025-09-10 11:36:33
  Meid       : 65444
  Description: 65444
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
*****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

hi_int32 hwtc_omci_me_65444_init(hi_ushort16 port_id)
{
    hi_ushort16 meid = HWTC_OMCI_PRO_ME_65444;
    hi_ushort16 us_instid = HI_OMCI_ME_WIFI_INSTID+port_id;
    hwtc_omci_me_65444_s st_entity;
    hi_int32 i_ret = HI_RET_SUCC;
    
    if(hwtc_omci_tapi_is_valid_ssid_idx(port_id) != HI_RET_SUCC){
        hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
        return HI_RET_SUCC;
    }

    i_ret = hi_omci_ext_create_inst(meid, us_instid);
    HI_OMCI_RET_CHECK(i_ret);

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;
    st_entity.st_msghead.us_attmask = 0xffff;

    i_ret = hwtc_omci_me_65444_get(&st_entity, sizeof(st_entity), NULL);
    HI_OMCI_RET_CHECK(i_ret);

    i_ret = hi_omci_ext_set_inst(meid, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);

    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}
    
hi_int32 hwtc_omci_me_65444_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}

hi_int32 hwtc_omci_me_65444_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
    hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
    return HI_RET_SUCC;
}
