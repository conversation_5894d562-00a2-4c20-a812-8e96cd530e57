/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_alarm.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-16
  Description: ME告警定时器
******************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"

#include "hi_timer.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
typedef struct {
	hi_omci_me_alarm_timer_s st_timer;
	hi_uint32 ui_tid;
	hi_list_head st_listhead;
} hi_omci_me_alarm_timer_entity_s;

/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
static hi_list_head g_st_alarm_timer_list;

/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/******************************************************************************
 Function    : __omci_me_alarm_proc
 Description : 告警定时器超时处理函数
 Input Parm  : 无
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
static hi_void __omci_me_alarm_proc(hi_void *pv_data)
{
	hi_omci_me_alarm_timer_entity_s *pst_timer_entity = (hi_omci_me_alarm_timer_entity_s *)pv_data;
	hi_void *pv_callback;
	hi_uint32 ui_addr;
	hi_uint32 ui_outlen;
	hi_int32 i_ret;

	/* 读数据库，取得ME告警回调函数指针 */
	i_ret = hi_omci_sql_entity_alarm_get(pst_timer_entity->st_timer.ui_meid, &ui_addr);
	if (i_ret != HI_RET_SUCC) {
		hi_omci_systrace(i_ret, 0, 0, 0, 0);
		return;
	}

	/* 执行回调函数 */
	pv_callback = (hi_void *)ui_addr;
	i_ret = ((HI_FUNCCALLBACK_EXT)pv_callback)(&pst_timer_entity->st_timer, sizeof(pst_timer_entity->st_timer),
			&ui_outlen);
	if (i_ret != HI_RET_SUCC) {
		hi_omci_systrace(i_ret, 0, 0, 0, 0);
		return;
	}

	/* 启动下一次定时 */
	i_ret = hi_timer_mod_wo_lock(pst_timer_entity->ui_tid, pst_timer_entity->st_timer.ui_timeout);
	if (i_ret != HI_RET_SUCC) {
		hi_omci_systrace(i_ret, 0, 0, 0, 0);
		return;
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return;
}

/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_alarm_start
 Description : 开启统计定时器
 Input Parm  : hi_uint32 ui_meid,
               hi_uint32 ui_instid
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_alarm_start(hi_omci_me_alarm_timer_s *pst_timer)
{
	hi_omci_me_alarm_timer_entity_s *pst_timer_entity;
	hi_int32 i_ret;

	pst_timer_entity = (hi_omci_me_alarm_timer_entity_s *)hi_os_malloc(sizeof(hi_omci_me_alarm_timer_entity_s));
	if (HI_NULL == pst_timer_entity) {
		hi_omci_systrace(HI_RET_MALLOC_FAIL, 0, 0, 0, 0);
		return HI_RET_MALLOC_FAIL;
	}

	HI_OS_MEMCPY_S(&pst_timer_entity->st_timer, sizeof(pst_timer_entity->st_timer), pst_timer,
		       sizeof(pst_timer_entity->st_timer));

	i_ret = hi_timer_create(__omci_me_alarm_proc, pst_timer_entity, &pst_timer_entity->ui_tid);
	if (i_ret != HI_RET_SUCC) {
		hi_os_free(pst_timer_entity);
		hi_omci_systrace(i_ret, 0, 0, 0, 0);
		return i_ret;
	}

	i_ret = hi_timer_mod(pst_timer_entity->ui_tid, pst_timer_entity->st_timer.ui_timeout);
	if (i_ret != HI_RET_SUCC) {
		hi_os_free(pst_timer_entity);
		hi_omci_systrace(i_ret, 0, 0, 0, 0);
		return i_ret;
	}

	hi_list_add_tail(&pst_timer_entity->st_listhead, &g_st_alarm_timer_list);

	hi_omci_debug("ME[%08u] inst[%08u] alarm timer start ...\n", pst_timer->ui_meid, pst_timer->ui_instid);
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_alarm_stop
 Description : 关闭统计定时器
 Input Parm  : hi_uint32 ui_meid,
               hi_uint32 ui_instid
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_alarm_stop(hi_omci_me_alarm_timer_s *pst_timer)
{
	hi_list_head *pst_list_pos = HI_NULL;
	hi_list_head *pst_list_next = HI_NULL;
	hi_omci_me_alarm_timer_entity_s *pst_timer_entity = HI_NULL;
	hi_int32 i_ret;

	hi_list_for_each_safe(pst_list_pos, pst_list_next, &g_st_alarm_timer_list) {
		pst_timer_entity = (hi_omci_me_alarm_timer_entity_s *)hi_list_entry(pst_list_pos, hi_omci_me_alarm_timer_entity_s,
				   st_listhead);
		if (HI_NULL != pst_timer_entity
		    && pst_timer_entity->st_timer.ui_meid == pst_timer->ui_meid
		    && pst_timer_entity->st_timer.ui_instid == pst_timer->ui_instid) {
			i_ret = hi_timer_destroy(pst_timer_entity->ui_tid);
			HI_OMCI_RET_CHECK(i_ret);

			hi_list_del(&pst_timer_entity->st_listhead);
			hi_os_free(pst_timer_entity);

			break;
		}
	}

	hi_omci_debug("ME[%08u] inst[%08u] alarm timer stop ...\n", pst_timer->ui_meid, pst_timer->ui_instid);
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_alarm_init
 Description : 告警模块初始化
 Input Parm  : 无
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_alarm_init()
{
	hi_list_init_head(&g_st_alarm_timer_list);
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_alarm_exit
 Description : 统计模块注销，释放所有定时器
 Input Parm  : 无
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_alarm_exit(hi_uint32 flag)
{
	hi_list_head *pst_list_pos = HI_NULL;
	hi_list_head *pst_list_next = HI_NULL;
	hi_omci_me_alarm_timer_entity_s *pst_timer_entity = HI_NULL;
	hi_int32 i_ret;

	/* 释放所有定时器 */
	hi_list_for_each_safe(pst_list_pos, pst_list_next, &g_st_alarm_timer_list) {
		pst_timer_entity = (hi_omci_me_alarm_timer_entity_s *)hi_list_entry(pst_list_pos, hi_omci_me_alarm_timer_entity_s,
											st_listhead);
		if (pst_timer_entity == HI_NULL)
			continue;

		if (pst_timer_entity->ui_tid != 0) {
			i_ret = hi_timer_destroy(pst_timer_entity->ui_tid);
			pst_timer_entity->ui_tid = 0;
			HI_OMCI_RET_CHECK(i_ret);
		}

		if (((HI_DISABLE == pst_timer_entity->st_timer.ui_local) || (flag == HI_TRUE))) {
			hi_list_del(&pst_timer_entity->st_listhead);
			hi_os_free(pst_timer_entity);
		}
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_local_alarm_restart(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_list_head *pst_list_pos = HI_NULL;
	hi_list_head *pst_list_next = HI_NULL;
	hi_omci_me_alarm_timer_entity_s *pst_timer_entity = HI_NULL;
	hi_int32 i_ret;

	hi_omci_set_mibreset_flag(HI_TRUE);
	hi_list_for_each_safe(pst_list_pos, pst_list_next, &g_st_alarm_timer_list) {
		pst_timer_entity = (hi_omci_me_alarm_timer_entity_s *)hi_list_entry(pst_list_pos, hi_omci_me_alarm_timer_entity_s,
											st_listhead);
		if (pst_timer_entity == HI_NULL)
			continue;

		i_ret = hi_timer_create(__omci_me_alarm_proc, pst_timer_entity, &pst_timer_entity->ui_tid);
		if (i_ret != HI_RET_SUCC) {
			hi_os_free(pst_timer_entity);
			hi_omci_systrace(i_ret, 0, 0, 0, 0);
			return i_ret;
		}

		i_ret = hi_timer_mod(pst_timer_entity->ui_tid, pst_timer_entity->st_timer.ui_timeout);
		if (i_ret != HI_RET_SUCC) {
			hi_os_free(pst_timer_entity);
			hi_omci_systrace(i_ret, 0, 0, 0, 0);
			return i_ret;
		}
	}
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
