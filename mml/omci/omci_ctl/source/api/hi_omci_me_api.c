
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

hi_uint32 vs_omci_api_verify_olt_vid(hi_uint32 vid)
{
    hi_omci_me_olt_g_s st_entity;
    hi_int32 i_ret;
    hi_ushort16 us_instid = 0;

    HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
    st_entity.st_msghead.us_instid = us_instid;

    i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_OLT_G_E, us_instid, &st_entity);
    HI_OMCI_RET_CHECK(i_ret);
    if(st_entity.ui_oltvendorid == vid){
        return HI_RET_SUCC;
    }

    return HI_RET_FAIL;
}

