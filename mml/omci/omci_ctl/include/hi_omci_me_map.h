/******************************************************************************
                  Copyright (C), 2012-2013, HSAN
 ******************************************************************************
  Filename    : hi_omci_map.c
  Version     : 初稿
  Author      : owen
  Creation    : 2013-10-9
  Description : OMCI映射状态机头文件
******************************************************************************/


#ifndef __HI_OMCI_ME_MAP_H__
#define __HI_OMCI_ME_MAP_H__

/*----------------------------------------------*
 * 外部变量说明                                 *
 *----------------------------------------------*/

/*----------------------------------------------*
 * 外部函数原型说明                             *
 *----------------------------------------------*/

/*----------------------------------------------*
 * 内部函数原型说明                             *
 *----------------------------------------------*/

/*----------------------------------------------*
 * 全局变量                                     *
 *----------------------------------------------*/

/*----------------------------------------------*
 * 模块级变量                                   *
 *----------------------------------------------*/

/*----------------------------------------------*
 * 常量定义                                     *
 *----------------------------------------------*/
typedef enum {
	HI_OMCI_MAP_EVENT_BP_TO_8021P_E = 0,          /* bridge port绑定802.1p profile */
	HI_OMCI_MAP_EVENT_BP_TO_GEMIWTP_E,            /* bridge port绑定 GEM IWTP */
	HI_OMCI_MAP_EVENT_8021P_TO_GEMIWTP_E,         /* 802.1p绑定GEM IWTP */
	HI_OMCI_MAP_EVENT_BP_TO_VF_E,                 /* Bridge port关联VLAN filter */
	HI_OMCI_MAP_EVENT_8021P_TO_SAME_GEMIWTP_E,    /* 802.1p绑定同一个GEM IWTP */
	HI_OMCI_MAP_EVENT_8021P_RELEASE_E,            /* 802.1p的优先级都指示无效的GEM IWTP */
	HI_OMCI_MAP_EVENT_VF_RELEASE_E,               /* VLAN filter删除 */
	HI_OMCI_MAP_EVENT_BP_RELEASE_E,               /* 删除bridge port */
	HI_OMCI_MAP_EVENT_RELEASE,
} hi_omci_map_event_e;

typedef enum {
	HI_OMCI_MAP_STATE_INIT = 0,
	HI_OMCI_MAP_STATE_PRI_PREVIEW,
	HI_OMCI_MAP_STATE_VLAN_PREVIEW,
	HI_OMCI_MAP_STATE_VLAN_PRI_PREVIEW,
	HI_OMCI_MAP_STATE_PRI,
	HI_OMCI_MAP_STATE_VLAN,
	HI_OMCI_MAP_STATE_VLAN_PRI,
	HI_OMCI_MAP_STATE_NUM,
} hi_omci_map_state_e;

typedef struct {
	hi_uint32 ui_bp_id;
	hi_void *pv_data;
	hi_omci_map_state_e em_state;
	hi_list_head st_listhead;
} hi_omci_map_state_s;

/*----------------------------------------------*
 * 宏定义                                       *
 *----------------------------------------------*/

extern hi_void hi_omci_map_init(hi_uint32 ui_mode);
extern hi_void hi_omci_map_exit(hi_void);
extern hi_int32 hi_omci_map_start(hi_uint32 ui_bp_id);
extern hi_int32 hi_omci_map_stop(hi_uint32 ui_bp_id);
extern hi_void hi_omci_map_port_enable(hi_uint32 ui_enable);
extern hi_int32 hi_omci_map_msg_hander(hi_uint32 ui_bp_id, hi_omci_map_event_e em_event, hi_void *pv_data);



#endif /* __HI_OMCI_ME_MAP_H__ */

