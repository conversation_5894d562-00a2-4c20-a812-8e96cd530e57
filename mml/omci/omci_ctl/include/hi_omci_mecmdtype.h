/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_mecmdtype.h
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_11_07

******************************************************************************/
#ifndef __HI_OMCI_MECMDTYPE_H__
#define __HI_OMCI_MECMDTYPE_H__


#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

typedef enum {
	HI_OMCI_ME_CMDTYPE_ETH_PPTP_SET_E     = (HI_SUBMODULE_OMCI_ME_ETH | 0x01),
	HI_OMCI_ME_CMDTYPE_ETH_PPTP_GET_E,
	HI_OMCI_ME_CMDTYPE_ETH_PM_GET_E,
	HI_OMCI_ME_CMDTYPE_ETH_PM2_GET_E,
	HI_OMCI_ME_CMDTYPE_ETH_PM3_GET_E,
	HI_OMCI_ME_CMDTYPE_ETH_PM4_GET_E,
	HI_OMCI_ME_CMDTYPE_ETH_PM_GETCURRENT_E,
	HI_OMCI_ME_CMDTYPE_ETH_PM2_GETCURRENT_E,
	HI_OMCI_ME_CMDTYPE_ETH_PM3_GETCURRENT_E,
	HI_OMCI_ME_CMDTYPE_ETH_PM4_GETCURRENT_E,

	HI_OMCI_ME_CMDTYPE_MACPORTPM_GET_E,
	HI_OMCI_ME_CMDTYPE_MACPORTPM_GETCURRENT_E,
} hi_omci_me_cmdtype_eth_e;

typedef enum {
	HI_OMCI_ME_CMDTYPE_ANI_TCONT_SET_E          = (HI_SUBMODULE_OMCI_ME_ANI  | 0x01),
	HI_OMCI_ME_CMDTYPE_ANI_TCONT_GET_E,

	HI_OMCI_ME_CMDTYPE_ANI_G_GET_E,
	HI_OMCI_ME_CMDTYPE_ANI_G_SET_E,
	HI_OMCI_ME_CMDTYPE_ANI_G_TEST_E,
	HI_OMCI_ME_CMDTYPE_ANI_G_TEST_RESULT_E,

	HI_OMCI_ME_CMDTYPE_ANI_GAL_ETH_CREATE_E,
	HI_OMCI_ME_CMDTYPE_ANI_GAL_ETH_DEL_E,
	HI_OMCI_ME_CMDTYPE_ANI_GAL_ETH_GET_E,
	HI_OMCI_ME_CMDTYPE_ANI_GAL_ETH_SET_E,

	HI_OMCI_ME_CMDTYPE_ANI_GEM_IWTP_CREATE_E,
	HI_OMCI_ME_CMDTYPE_ANI_GEM_IWTP_DEL_E,
	HI_OMCI_ME_CMDTYPE_ANI_GEM_IWTP_GET_E,
	HI_OMCI_ME_CMDTYPE_ANI_GEM_IWTP_SET_E,

	HI_OMCI_ME_CMDTYPE_ANI_GEM_PORT_CREATE_E,
	HI_OMCI_ME_CMDTYPE_ANI_GEM_PORT_DEL_E,
	HI_OMCI_ME_CMDTYPE_ANI_GEM_PORT_GET_E,
	HI_OMCI_ME_CMDTYPE_ANI_GEM_PORT_SET_E,

	HI_OMCI_ME_CMDTYPE_ANI_MULTI_GEM_IWTP_CREATE_E,
	HI_OMCI_ME_CMDTYPE_ANI_MULTI_GEM_IWTP_DEL_E,
	HI_OMCI_ME_CMDTYPE_ANI_MULTI_GEM_IWTP_SET_E,

	HI_OMCI_ME_CMDTYPE_ANI_PQ_GET_E,
	HI_OMCI_ME_CMDTYPE_ANI_PQ_SET_E,

	HI_OMCI_ME_CMDTYPE_ANI_TRAFFIC_SCH_GET_E,
	HI_OMCI_ME_CMDTYPE_ANI_TRAFFIC_SCH_SET_E,

	HI_OMCI_ME_CMDTYPE_ANI_TRAFFIC_DESC_CREATE_E,
	HI_OMCI_ME_CMDTYPE_ANI_TRAFFIC_DESC_DEL_E,
	HI_OMCI_ME_CMDTYPE_ANI_TRAFFIC_DESC_GET_E,
	HI_OMCI_ME_CMDTYPE_ANI_TRAFFIC_DESC_SET_E,

	HI_OMCI_ME_CMDTYPE_ANI_FEC_PM_GETCURRENT_E,
	HI_OMCI_ME_CMDTYPE_ANI_FEC_PM_GET_E,
	HI_OMCI_ME_CMDTYPE_ANI_GEM_CTP_CREATE_E,
	HI_OMCI_ME_CMDTYPE_ANI_GEM_CTP_SET_E,

} hi_omci_me_cmdtype_ani_e;

typedef enum {
	HI_OMCI_ME_CMDTYPE_LAYER2_8021P_SET_E     = (HI_SUBMODULE_OMCI_ME_LAYER2 | 0x01),

	HI_OMCI_ME_CMDTYPE_LAYER2_EXVLANTAG_CREATE_E,
	HI_OMCI_ME_CMDTYPE_LAYER2_EXVLANTAG_DEL_E,
	HI_OMCI_ME_CMDTYPE_LAYER2_EXVLANTAG_SET_E,

	HI_OMCI_ME_CMDTYPE_LAYER2_HWEXVLANTAG_CREATE_E,
	HI_OMCI_ME_CMDTYPE_LAYER2_HWEXVLANTAG_DEL_E,
	HI_OMCI_ME_CMDTYPE_LAYER2_HWEXVLANTAG_SET_E,

	HI_OMCI_ME_CMDTYPE_LAYER2_MAC_SERV_CREATE_E,
	HI_OMCI_ME_CMDTYPE_LAYER2_MAC_SERV_SET_E,
	HI_OMCI_ME_CMDTYPE_LAYER2_MAC_SERV_DEL_E,
	HI_OMCI_ME_CMDTYPE_LAYER2_MAC_DESIGN_GET_E,

	HI_OMCI_ME_CMDTYPE_LAYER2_MAC_CFG_CREATE_E,
	HI_OMCI_ME_CMDTYPE_LAYER2_MAC_CFG_DEL_E,

	HI_OMCI_ME_CMDTYPE_LAYER2_VLAN_FILT_SET_BEFORE_E,
	HI_OMCI_ME_CMDTYPE_LAYER2_VLAN_FILT_SET_E,
	HI_OMCI_ME_CMDTYPE_LAYER2_VLAN_FILT_CREATE_E,
	HI_OMCI_ME_CMDTYPE_LAYER2_MACPORTCFG_CREATE_E,
	HI_OMCI_ME_CMDTYPE_LAYER2_MACPORTCFG_DEL_E,
	HI_OMCI_ME_CMDTYPE_LAYER2_MACPORTCFG_SET_E,

	HI_OMCI_ME_CMDTYPE_LAYER2_MACPORTPM_GET_E,
	HI_OMCI_ME_CMDTYPE_LAYER2_MACPORTPM_GETCUR_E,

	HI_OMCI_ME_CMDTYPE_LAYER2_VLAN_TAG_SET_E,
	HI_OMCI_ME_CMDTYPE_LAYER2_VLAN_TAG_DEL_E,
	HI_OMCI_ME_CMDTYPE_LAYER2_VLAN_TAG_CREATE_E,

	HI_OMCI_ME_CMDTYPE_LAYER2_MULTI_OPER_CREATE_E,
	HI_OMCI_ME_CMDTYPE_LAYER2_MULTI_OPER_SET_E,

	HI_OMCI_ME_CMDTYPE_LAYER2_MACPORT_TABLE_SET_E,
} hi_omci_me_cmdtype_layer2_e;

typedef enum {
	HI_OMCI_ME_CMDTYPE_EQUIP_EXT_ONTABILITY_SET_E     = (HI_SUBMODULE_OMCI_ME_EQUIP | 0x01),
	HI_OMCI_ME_CMDTYPE_EQUIP_ONU_G_GET_E,
	HI_OMCI_ME_CMDTYPE_EQUIP_SOFTIMAGE_GET_E,
	HI_OMCI_ME_CMDTYPE_EQUIP_SOFTIMAGE_ACTIVATE_E,
	HI_OMCI_ME_CMDTYPE_EQUIP_SOFTIMAGE_COMMIT_E,
	HI_OMCI_ME_CMDTYPE_EQUIP_REMOTE_DEBUG_SET_E,
} hi_omci_me_cmdtype_equip_e;

typedef enum {
	HI_OMCI_ME_CMDTYPE_LAYER3_IPHOST_SET_E            = (HI_SUBMODULE_OMCI_ME_LAYER3 | 0x01),
	HI_OMCI_ME_CMDTYPE_LAYER3_IPHOST_GET_E,
} hi_omci_me_cmdtype_layer3_e;

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif /* __HI_OMCI_MECMDTYPE_H__ */
