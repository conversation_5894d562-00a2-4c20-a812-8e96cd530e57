/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_me_api.h
  版 本 号   : 初稿
  作    者   : y00185833
  生成日期   : D2012_08_22

******************************************************************************/
#ifndef __HI_OMCI_ME_API_H__
#define __HI_OMCI_ME_API_H__


#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

typedef hi_int32(* hi_omci_me_callback)(hi_void);

typedef struct {
	hi_omci_me_callback create;         /* ME创建回调函数 */
	hi_omci_me_callback modify;         /* ME修改回调函数 */
	hi_omci_me_callback reg_proc;       /* ME处理注册回调函数 */
	hi_omci_me_callback release_proc;   /* ME处理注销回调函数 */
} hi_omci_reg_callback;

/* olt vendor id*/
#define VS_OLT_VID_HWTC     0x48575443 //HWTC
#define VS_OLT_VID_ZTEG      0x5a544547 //ZTEG
#define VS_OLT_VID_CDAT     0x43444154 //CDAT
#define VS_OLT_VID_OPTLINK  0x46434f4d //FCOM
#define VS_OLT_VID_DSAN     0x44534e57 //DSNW
#define VS_OLT_VID_NOKIA    0x414c434c //ALCL/NOKIA
#define VS_OLT_VID_OTHER    0x00000000

extern hi_uint32 vs_omci_api_verify_olt_vid(hi_uint32 vid);

extern hi_uint32 hi_omci_mib_recv_msg(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_dbg_set_upgradefile(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

//pptp eth uni
extern hi_int32 hi_omci_me_pptpethuni_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_pptpethuni_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_pptpethuni_alarm_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
/* Bohannon */
extern hi_int32 hi_omci_me_pptp_video_uni_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_pptp_video_uni_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_pptp_80211_uni_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_pptp_80211_uni_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_ethpm_alarm_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethpm_stat_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethpm_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethpm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethpm_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethpm_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_ethpm3_alarm_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethpm3_stat_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethpm3_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethpm3_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethpm3_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethpm3_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_veip_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
//layer2
extern hi_uint32 hi_omci_me_8021p_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_exvlantag_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_exvlantag_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_exvlantag_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_uint32 hi_omci_me_maccfg_create_after(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_maccfg_del_before(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_uint32 hi_omci_me_mac_serv_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_mac_serv_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_mac_serv_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_uint32 hi_omci_me_vlan_filt_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_vlan_filt_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_vlan_filt_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_uint32 hi_omci_me_macportcfg_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_macportcfg_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_macportcfg_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_macportcfg_get_portid(hi_ushort16 us_instid, hi_uchar8 *puc_portid);

extern hi_uint32 hi_omci_me_multi_oper_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_multi_oper_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_multi_oper_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_uint32 hi_omci_me_multisubs_cfg_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_multisubs_cfg_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_multisubs_cfg_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_uint32 hi_omci_me_macport_table_create(hi_ushort16 us_instid);
extern hi_uint32 hi_omci_me_macport_table_delete(hi_ushort16 us_instid);
extern hi_int32 hi_omci_me_macport_table_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

extern hi_uint32 hi_omci_me_macportfilt_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_macportfilt_create(hi_ushort16 us_instid);
extern hi_uint32 hi_omci_me_macportfilt_delete(hi_ushort16 us_instid);

extern hi_int32 hi_omci_me_bridgepm_alarm_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_bridgepm_stat_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_bridgepm_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_bridgepm_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_brportpm_alarm_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_brportpm_stat_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_brportpm_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_brportpm_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_brportpm_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_ethextpm_alarm_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethextpm_stat_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethextpm_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethextpm_stat_getcurrent(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethextpm_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethextpm_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_ethextpm_64b_alarm_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethextpm_64b_stat_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethextpm_64b_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethextpm_64b_stat_getcurrent(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethextpm_64b_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethextpm_64b_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_ethfrmpm_up_alarm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethfrmpm_up_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethfrmpm_up_stat_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethfrmpm_up_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethfrmpm_up_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_ethfrmpm_down_alarm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethfrmpm_down_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethfrmpm_down_stat_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethfrmpm_down_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethfrmpm_down_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
//ANI
extern hi_int32 hi_omci_me_tcont_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_uint32 hi_omci_me_multiwtp_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_multiwtp_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_multiwtp_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_anig_alarm_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_anig_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_anig_test(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_anig_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_fecpm_alarm_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_fecpm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_fecpm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_fecpm_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_fecpm_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_fecpm_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_uint32 hi_omci_me_gem_ctp_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_gem_ctp_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_gem_ctp_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_gem_iwtp_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_traffic_desc_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_traffic_desc_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_traffic_desc_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_traffic_desc_carid_get(hi_uint32 ui_instid, hi_uint32 *pui_carid);
extern hi_int32 hi_omci_me_traffic_desc_init();
extern hi_int32 hi_omci_me_traffic_desc_exit();

extern hi_int32 hi_omci_me_pq_pri_get(hi_ushort16 us_instid, hi_uint32 *ui_egr, hi_uint32 *pui_pri);
extern hi_uint32 hi_omci_me_pq_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_gemctppm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_gemctppm_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_gemctppm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_gemctppm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_gemctppm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_galethpm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_galethpm_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_galethpm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_galethpm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_galethpm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

//equip
extern hi_uint32 hi_omci_me_softimage_activate(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_softimage_get_bef(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_softimage_commit(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_softwareimage_init(hi_uint32 ui_index);
extern hi_int32 hi_omci_me_softwareimage_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_rmtdbg_set_after(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_onudata_reset(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_iphost_reset();
extern hi_int32 hi_omci_me_powermng_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_loid_auth_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_loid_auth_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_onug_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_onug_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_onu2g_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

//layer3

/* 二次开发ME接口 */
extern hi_int32 hi_omci_me_reg_service(hi_uint32 ui_meid, hi_omci_proc_msg_type_e em_msg_type,
				       hi_uint32 ui_before, HI_FUNCCALLBACK_EXT pf_callback, hi_char8 *pc_funcname);
extern hi_int32 hi_omci_me_release_service(hi_uint32 ui_meid, hi_omci_proc_msg_type_e em_msg_type);
extern hi_int32 hi_omci_me_reg_alarm(hi_uint32 ui_meid, HI_FUNCCALLBACK_EXT callback);
extern hi_int32 hi_omci_me_release_alarm(hi_uint32 ui_meid);
extern hi_int32 hi_omci_me_reg_stat(hi_uint32 ui_meid, HI_FUNCCALLBACK_EXT callback);
extern hi_int32 hi_omci_me_release_stat(hi_uint32 ui_meid);

//voip
extern hi_int32 hi_omci_me_pptp_pots_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_pptp_pots_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_pptp_pots_test(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_cfg_data_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_cfg_data_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_iptochar(hi_uchar8 *puc_ipdigital, hi_char8 *pc_ipchar, hi_uint32 ui_ipchar_len);
extern hi_void hi_omci_me_set_iphost_attr(hi_void *pv_data, hi_uint32 ui_instid);
extern hi_int32 hi_omci_me_iphost_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_iphost_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_dial_plan_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_dial_plan_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_dial_plan_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_media_profile_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_media_profile_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_media_profile_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_user_data_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_rtp_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_rtp_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_agent_data_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_agent_data_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_voice_ctp_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_line_status_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_rtppm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_rtppm_stat_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_rtppm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_rtppm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_large_string_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_agent_data_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_agent_data_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_tr069_management_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_agentpm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_agentpm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_agentpm_stat_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_agentpm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_callinitpm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_callinitpm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_callinitpm_stat_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_callinitpm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_call_control_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_call_control_pm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_call_control_stat_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_call_control_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_user_data_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_user_data_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
#if 0
extern hi_int32 hi_omci_me_sip_user_data_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_agent_data_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_voice_ctp_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_voice_ctp_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_voice_ctp_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_auth_security_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_auth_security_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
#endif
extern hi_int32 hi_omci_me_auth_security_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_xgpontcpm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgpontcpm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgpontcpm_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgpontcpm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgpontcpm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_xgpondownmngpm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgpondownmngpm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgpondownmngpm_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgpondownmngpm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgpondownmngpm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_xgponupmngpm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgponupmngpm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgponupmngpm_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgponupmngpm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgponupmngpm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

/*vsol*/
extern hi_int32 vs_omci_me_pri_cap1_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_dhcp_server_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_dhcp_server_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_dhcpv6_server1_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_dhcpv6_server1_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_dhcpv6_server2_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_dhcpv6_server2_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_dhcpv6_server3_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_dhcpv6_server3_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_login_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_login_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_firewall_level_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_firewall_level_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_access_control_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_access_control_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_tr069_acs_server_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_tr069_acs_server_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_tr069_config_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_tr069_config_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_tr069_middleware_config_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_tr069_middleware_config_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_tr069_reverse_connect_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_tr069_reverse_connect_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_tr069_stun_config_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_tr069_stun_config_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wan_number_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wan_number_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wan_6rd_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wan_6rd_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wan_config_data_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wan_config_data_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wan_config_pppoe_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wan_config_pppoe_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wan_dhcp_ipv6_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wan_dhcp_ipv6_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wan_ds_lite_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wan_ds_lite_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wan_port_bind_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wan_port_bind_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wan_static_ip_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wan_static_ip_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wan_static_ipv6_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wan_static_ipv6_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wan_vlan_mode_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wan_vlan_mode_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wifi_channel_width_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wifi_channel_width_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wifi_control_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wifi_control_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wifi_mesh_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wifi_mesh_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wifi_ssid_data1_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wifi_ssid_data1_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wifi_ssid_data2_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wifi_ssid_data2_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wifi_ssid_data3_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_wifi_ssid_data3_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_igmp_config_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_igmp_config_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_factory_reset_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_factory_reset_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_mac_aging_time_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_mac_aging_time_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_mac_limit_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_mac_limit_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_onu_catv_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_onu_catv_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_pon_speed_limit_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_pon_speed_limit_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_auth_key_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_auth_key_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_pri_auth_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_pri_auth_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_mac_acl_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_mac_acl_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_mac_acl_list_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_mac_acl_list_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

extern hi_int32 vs_omci_me_ont_voipngncfg_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_ont_voipngncfg_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_ont_voippotscfg_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_ont_voippotscfg_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_ont_voippotsstatus_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_ont_voippotsstatus_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_pots_config_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_pots_config_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_sip_config1_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_sip_config1_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_sip_config2_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_sip_config2_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_voip_digitmap_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_voip_digitmap_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_voip_globalconfig_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_voip_globalconfig_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_voip_globalconfig2_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_voip_globalconfig2_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

extern hi_int32 vs_omci_me_eth_loopDetect_alarm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_eth_loop_detect_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_eth_loop_detect_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_onu3g_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_iphost_timer_update();
extern hi_int32 vs_omci_me_extended_iphost_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_extended_iphost_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_extended_iphost_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_extended_iphost_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_ipv6_iphost_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 vs_omci_me_ipv6_iphost_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
/*vsol end*/

/* hwtc externed me */
extern hi_int32 hwtc_omci_me_ctc_extended_onu_g_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_ctc_extended_onu_g_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_350_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_350_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_373_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_373_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65417_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65417_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65427_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65427_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65398_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65398_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65423_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65423_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65447_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65447_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65431_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65431_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65414_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65414_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65415_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65415_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65426_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65426_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65425_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65425_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65430_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65430_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65444_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65444_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65450_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65450_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65450_test(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

extern hi_int32 hwtc_omci_me_65451_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65451_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65451_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65451_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65452_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65452_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65452_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65452_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65453_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65453_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65454_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_65454_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_GeneralPurposeBuffer_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_GeneralPurposeBuffer_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_GeneralPurposeBuffer_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_GeneralPurposeBuffer_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

extern hi_int32 hwtc_omci_me_Ieee80211StaMgmData1_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hwtc_omci_me_Ieee80211StaMgmData1_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
//automatic tool identification marks, do not delete this line

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif /* __HI_OMCI_ME_API_H__ */
