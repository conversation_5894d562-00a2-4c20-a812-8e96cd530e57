/******************************************************************************

Copyright (c) vsol. 2024-2025. All rights reserved.

 ******************************************************************************
  文 件 名   : vs_omci_me_vsol_def.h
  版 本 号   : V1.0
  作   者	: fyy
  生成日期   :  20240625

******************************************************************************/
#ifndef __OMCI_ME_VSOL_DEF_H__
#define __OMCI_ME_VSOL_DEF_H__


#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#pragma pack(1)

/************************VSOL private capacity support 1*******************************/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_ge_ports;
    hi_uchar8 uc_fe_ports;
    hi_uchar8 uc_pots_ports;
    hi_uchar8 uc_wifi_ssid_number;
    hi_uchar8 uc_catv_number;
    hi_ushort16 us_support_catv;
    hi_uchar8 uc_support_private;
    hi_ushort16 us_support_wan;
    hi_ushort16 us_support_wifi;
    hi_ushort16 us_support_dhcp;
    hi_ushort16 us_support_security;
    hi_ushort16 us_support_voip;
    hi_ushort16 us_support_port;
    hi_ushort16 us_support_control;
    hi_ushort16 us_support_rstp;
    hi_ushort16 us_support_application;
} __attribute__((packed))vs_omci_me_pri_cap1_s;

/************************VSOL dhcp server*******************************/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_ip_type;
    hi_uchar8 auc_lan_ip_address[16];
    hi_uint32 ui_lan_subnet_mask;
    hi_uint32 ui_lease_time;
    hi_uchar8 uc_enable_dhcp_server;
    hi_uchar8 auc_dhcp_pool_start[16];
    hi_uchar8 auc_dhcp_pool_end[16];
    hi_uchar8 uc_dhcp_pool_type;
    hi_uchar8 auc_dhcp_pri_dns[16];
    hi_uchar8 auc_dhcp_sec_dns[16];
    hi_uchar8 auc_dhcp_gateway[16];
    hi_uchar8 auc_dhcp_server_ip[16];
    hi_uchar8 uc_dhcp_dns_mode;
} __attribute__((packed))vs_omci_me_dhcp_server_s;

/************************VSOL dhcpv6 server1*******************************/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_lanIPv6PrefixMode;
    hi_uchar8 auc_lanIPv6Prefix[16];
    hi_uchar8 uc_dhcpDnsMode;
    hi_uchar8 uc_raEnable;
    hi_uchar8 uc_raManageConfigFlag;
    hi_uchar8 uc_raOtherConfigFlag;
    hi_uint32 ui_raMaxInterval;
    hi_uint32 ui_raMinInterval;
    hi_uchar8 uc_lanIPv6PrefixLength;
    hi_uchar8 uc_dhcpDnsWanInterface;
} __attribute__((packed))vs_omci_me_dhcpv6_server1_s;
/************************VSOL dhcpv6 server2*******************************/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_ipV6Type;
    hi_uchar8 auc_lanIpaddressIPv6[16];
    hi_uchar8 uc_lanNetMaskIPv6;
    hi_uint32 ui_leaseTimeIPv6;
    hi_uchar8 uc_enableDhcpServerIPv6;
    hi_uchar8 auc_dhcpPoolStartIPv6[16];
    hi_uchar8 auc_dhcpPoolEndIPv6[16];
    hi_uchar8 uc_dhcpPoolTypeIPv6;
    hi_uchar8 auc_dhcpPriDnsIPv6[16];
    hi_uchar8 auc_dhcpSecDnsIPv6[16];
    hi_uchar8 auc_dhcpGatewayIPv6[16];
    hi_uchar8 auc_dhcpServerIpAddr[16];
    hi_uint32 ui_preferenceLifeTime;
    hi_uint32 ui_validLifeTime;
} __attribute__((packed))vs_omci_me_dhcpv6_server2_s;
/************************VSOL dhcpv6 server3*******************************/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_supportInterface;
    hi_uchar8 uc_interface;
} __attribute__((packed))vs_omci_me_dhcpv6_server3_s;
/************************VSOL login*******************************/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_char8 auc_login_username1[22];
    hi_char8 auc_login_username2[10];
    hi_char8 auc_login_password1[22];
    hi_char8 auc_login_password2[10];
    hi_char8 uc_login_change;
} __attribute__((packed))vs_omci_me_login_s;
/************************VSOL firewall level*******************************/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_firewall_level;
} __attribute__((packed))vs_omci_me_firewall_level_s;
/************************VSOL access control*******************************/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_access_control_enable;
    hi_uchar8 uc_lan_enable;
    hi_uchar8 uc_wan_enable;
    hi_uchar8 uc_wan_ipv4_control;
    hi_uint32 ui_wan_ipv4_addr;
    hi_uint32 ui_wan_ipv4_mask;
    hi_uchar8 uc_wan_ipv6_control;
    hi_uchar8 auc_wan_ipv6_addr[16];
    hi_uint32 ui_wan_ipv6_mask;
    hi_ushort16 us_port;
} __attribute__((packed))vs_omci_me_access_control_s;

/************************VSOL tr069 config*******************************/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_tr069_enable;
    hi_uchar8 uc_inform_enable;
    hi_uint32 ui_inform_interval;
    hi_uchar8 uc_Certificate_enable;
    hi_char8  auc_project_id[20];
} __attribute__((packed))vs_omci_me_tr069_config_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_char8 auc_Acs_server_url1[22];
    hi_char8 auc_Acs_server_url2[22];
    hi_char8 auc_Acs_server_url3[22];
    hi_char8 auc_Acs_server_url4[22];
    hi_char8 auc_Acs_server_url5[22];
    hi_char8 auc_Acs_server_url6[18];
    hi_char8 auc_Acs_server_username1[22];
    hi_char8 auc_Acs_server_username2[22];
    hi_char8 auc_Acs_server_username3[20];
    hi_char8 auc_Acs_server_Password1[22];
    hi_char8 auc_Acs_server_Password2[22];
    hi_char8 auc_Acs_server_Password3[20];
} __attribute__((packed))vs_omci_me_tr069_acs_server_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_ushort16 us_reverse_connect_port;
    hi_char8 auc_reverse_connect_username1[22];
    hi_char8 auc_reverse_connect_username2[22];
    hi_char8 auc_reverse_connect_username3[20];
    hi_char8 auc_reverse_connect_password1[22];
    hi_char8 auc_reverse_connect_password2[22];
    hi_char8 auc_reverse_connect_password3[20];
} __attribute__((packed))vs_omci_me_tr069_reverse_connect_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_middleware_enable;
    hi_ushort16 us_middleware_port;
    hi_char8 auc_middleware_url1[22];
    hi_char8 auc_middleware_url2[22];
    hi_char8 auc_middleware_url3[22];
    hi_char8 auc_middleware_url4[22];
    hi_char8 auc_middleware_url5[22];
    hi_char8 auc_middleware_url6[18];
} __attribute__((packed))vs_omci_me_tr069_middleware_config_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_stun_status;
    hi_uchar8 auc_stun_server_address1[22];
    hi_uchar8 auc_stun_server_address2[22];
    hi_uchar8 auc_stun_server_address3[22];
    hi_uchar8 auc_stun_server_address4[22];
    hi_uchar8 auc_stun_server_address5[22];
    hi_uchar8 auc_stun_server_address6[18];
    hi_ushort16 us_stun_server_port;
    hi_uchar8 auc_stun_server_username1[22];
    hi_uchar8 auc_stun_server_username2[22];
    hi_uchar8 auc_stun_server_username3[20];
    hi_uchar8 auc_stun_server_password1[22];
    hi_uchar8 auc_stun_server_password2[22];
    hi_uchar8 auc_stun_server_password3[20];
} __attribute__((packed))vs_omci_me_tr069_stun_config_s;
/************************VSOL wan config*******************************/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_wan_number;
} __attribute__((packed))vs_omci_me_wan_number_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_wanIndex;
    hi_char8 auc_wanName1[20];
    hi_char8 auc_wanName2[20];
    hi_uchar8 uc_wanConnMode;
    hi_uchar8 uc_wanConnType;
    hi_ushort16 us_wanVlanId;
    hi_uchar8 uc_wanPriority;
    hi_uchar8 uc_wanNatEnable;
    hi_uchar8 uc_wanAchieveAddrMode;
    hi_uchar8 uc_wanQosEnable;
    hi_uchar8 uc_wanConnStatus;
    hi_ushort16 us_wanMtu;
    hi_uchar8 uc_wanIpProtocol;
    hi_uchar8 auc_wanMac[6];
} __attribute__((packed))vs_omci_me_wan_config_data_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_ppppoeProxyEnable;
    hi_uchar8 uc_ppppoeDialPattern;
    hi_char8 auc_pppoeUsername1[16];
    hi_char8 auc_pppoeUsername2[16];
    hi_char8 auc_pppoePassword1[16];
    hi_char8 auc_pppoePassword2[16];
    hi_char8 auc_pppoeServiceName1[16];
    hi_char8 auc_pppoeServiceName2[16];
    hi_char8 auc_pppoeUsername3[16];
    hi_char8 auc_pppoeUsername4[16];
    hi_char8 auc_pppoePassword3[16];
    hi_char8 auc_pppoePassword4[16];
    hi_char8 auc_pppoeServiceName3[16];
    hi_char8 auc_pppoeServiceName4[16];
} __attribute__((packed))vs_omci_me_wan_config_pppoe_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_lanBind;
    hi_uchar8 uc_ssidBind;
    hi_uchar8 uc_lanBind1;
    hi_uchar8 uc_ssidBind1;
} __attribute__((packed))vs_omci_me_wan_port_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uint32 ui_wanIpAddress;
    hi_uint32 ui_wanIpMask;
    hi_uint32 ui_wanGateway;
    hi_uint32 ui_wanPrimaryDns;
    hi_uint32 ui_wanSecondaryDns;
} __attribute__((packed))vs_omci_me_wan_static_ip_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 auc_wanIPv6Address[16];
    hi_uint32 ui_wanIpv6Mask;
    hi_uchar8 auc_wanIpv6Gateway[16];
    hi_uchar8 uc_wanRequestDnsMode;
    hi_uchar8 auc_wanIpv6PrimaryDns[16];
    hi_uchar8 auc_wanIpv6SecondaryDns[16];
} __attribute__((packed))vs_omci_me_wan_static_ipv6_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_wanIpv6AddressMode;
    hi_uchar8 uc_wanDhcpv6ClientRequestAddress;
    hi_uchar8 uc_wanDhcpv6ClientRequestPrefix;
} __attribute__((packed))vs_omci_me_wan_dhcp_ipv6_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_wanDSLiteEnable;
    hi_uchar8 uc_wanDSLiteAFTRMode;
    hi_uchar8 uc_wanDSLiteAddressType;
    hi_char8 auc_wanDSLiteAddress1[22];
    hi_char8 auc_wanDSLiteAddress2[22];
    hi_char8 auc_wanDSLiteAddress3[22];
    hi_char8 auc_wanDSLiteAddress4[22];
    hi_char8 auc_wanDSLiteAddress5[22];
    hi_char8 auc_wanDSLiteAddress6[18];
} __attribute__((packed))vs_omci_me_wan_ds_lite_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_wan6RDEnable;
    hi_uint32 ui_wan6RDIpv4Address;
    hi_uint32 ui_wan6RDIpv4Mask;
    hi_uchar8 auc_wan6RDIpv6Prefix[16];
    hi_uint32 ui_wan6RDIpv6PrefixLen;
} __attribute__((packed))vs_omci_me_wan_6rd_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_ushort16 us_wanVlanMode;
    hi_uchar8 uc_wanTranslationEnable;
    hi_ushort16 us_wanTranslationvlan;
    hi_ushort16 us_wanTranslationvlanCos;
    hi_uchar8 uc_wanQinqEnable;
    hi_ushort16 us_wanTpid;
    hi_ushort16 us_wanSvlan;
    hi_ushort16 us_wanSvlanCos;
} __attribute__((packed))vs_omci_me_wan_vlan_mode_s;
/************************VSOL wifi config*******************************/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_wifiApModuleEnable;
    hi_uchar8 uc_wifiCountry;
    hi_uchar8 uc_wifiChannel;
    hi_uchar8 uc_wifiStandard;
    hi_uchar8 uc_wifiPower;
} __attribute__((packed))vs_omci_me_wifi_control_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_char8 auc_ssidName1[16];
    hi_char8 auc_ssidName2[16];
    hi_uchar8 uc_ssidEnable;
    hi_uchar8 uc_ssidHide;
    hi_uchar8 uc_ssidAuthMode;
    hi_uchar8 uc_ssidEncryptionType;
    hi_char8 auc_ssidPreshareKey1[22];
    hi_char8 auc_ssidPreshareKey2[22];
    hi_char8 auc_ssidPreshareKey3[20];
    hi_uint32 ui_ssidWpaRekeyInterval;
} __attribute__((packed))vs_omci_me_wifi_ssid_data1_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_ssidRadiusServerIPType;
    hi_uchar8 uc_ssidRadiusServerIPLen;
    hi_char8 auc_ssidRadiusServerIP1[22];
    hi_char8 auc_ssidRadiusServerIP2[22];
    hi_char8 auc_ssidRadiusServerIP3[22];
    hi_char8 auc_ssidRadiusServerIP4[22];
    hi_char8 auc_ssidRadiusServerIP5[22];
    hi_char8 auc_ssidRadiusServerIP6[18];
    hi_uint32 ui_ssidRadiusServerIPPrefixLen;
    hi_ushort16 us_ssidRadiusServerPort;
    hi_char8 auc_ssidRadiusKey1[16];
    hi_char8 auc_ssidRadiusKey2[16];
} __attribute__((packed))vs_omci_me_wifi_ssid_data2_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_ssidWEPEncryptionLevel;
    hi_uchar8 uc_ssidWEPKeyIndex;
    hi_char8 auc_ssidWEPKey1_1[16];
    hi_char8 auc_ssidWEPKey1_2[16];
    hi_char8 auc_ssidWEPKey2_1[16];
    hi_char8 auc_ssidWEPKey2_2[16];
    hi_char8 auc_ssidWEPKey3_1[16];
    hi_char8 auc_ssidWEPKey3_2[16];
    hi_char8 auc_ssidWEPKey4_1[16];
    hi_char8 auc_ssidWEPKey4_2[16];
} __attribute__((packed))vs_omci_me_wifi_ssid_data3_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_wifiChannelWidth;
} __attribute__((packed))vs_omci_me_wifi_channel_width_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_wifiMeshEnable;
} __attribute__((packed))vs_omci_me_wifi_mesh_s;
/************************VSOL misc config*******************************/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_igmpConfigEnable;
} __attribute__((packed))vs_omci_me_igmp_config_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_factoryResetEnable;
} __attribute__((packed))vs_omci_me_factory_reset_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_onuCatv;
} __attribute__((packed))vs_omci_me_onu_catv_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uint32 ui_uploadSpeedLimit;
    hi_uint32 ui_downloadSpeedLimit;
} __attribute__((packed))vs_omci_me_pon_speed_limit_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_ushort16 us_macAgeTime;
} __attribute__((packed))vs_omci_me_mac_aging_time_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_ponMacLimitEn;
    hi_ushort16 us_ponMacLimitCnt;
    hi_uchar8 uc_lanMacLimitEn;
    hi_ushort16 us_lanMacLimitCnt;
} __attribute__((packed))vs_omci_me_mac_limit_s;
/************************VSOL auth key config*******************************/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 auc_priVer[6];
    hi_uchar8 auc_priAuthKey1[22];
    hi_uchar8 auc_priAuthKey2[10];
    hi_uchar8 auc_priResv[16];
} __attribute__((packed))vs_omci_me_pri_auth_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 auc_priONUMac[6];
    hi_uchar8 auc_priAuthKey1[22];
    hi_uchar8 auc_priAuthKey2[10];
} __attribute__((packed))vs_omci_me_auth_key_s;

/************************VSOL MAC ACL config*******************************/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_macAclStatus;
    hi_uchar8 uc_macAclMode;
} __attribute__((packed))vs_omci_me_mac_acl_s;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_macAclAction;
    hi_uchar8 uc_macAclGroupNum;
    hi_uchar8 auc_macAclAddr[6];
    hi_uchar8 auc_macAclAddrMask[6];
} __attribute__((packed))vs_omci_me_mac_acl_list_s;
/************************VSOL VOIP CONFIG*******************************/
/*MEID:30203*/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_VAD;
    hi_uchar8 uc_EchoCancel;
    hi_uchar8 uc_InputGain;
    hi_uchar8 uc_OutputGain;
    hi_uchar8 uc_DtmfMode;
} __attribute__((packed))vs_omci_me_ont_voippotscfg_s;
/*MEID:30205*/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_PotsID;
    hi_uchar8 uc_Action;
} __attribute__((packed))vs_omci_me_ont_voipngncfg_s;
/*MEID:30208*/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uint32 ui_PotsStatus;
    hi_uchar8 auc_TerminationID[20];
    hi_uchar8 auc_RTPName[16];
    hi_ushort16 us_RTPPort;
} __attribute__((packed))vs_omci_me_ont_voippotsstatus_s;
/*MEID:30209*/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_IpConfigMode;
    hi_uint32 ui_Ip;
    hi_uint32 ui_Mask;
    hi_uint32 ui_Gateway;
    hi_uint32 ui_RtpIp;
    hi_uint32 ui_RtpIpMask;
    hi_uint32 ui_RtpGateway;
    hi_uint32 ui_PrimaryDns;
    hi_uint32 ui_SecondaryDns;
    hi_uchar8 auc_PppoeUsername1[16];
    hi_uchar8 auc_PppoeUsername2[16];
    hi_uchar8 auc_PppoePassword1[16];
    hi_uchar8 auc_PppoePassword2[16];
    hi_uchar8 uc_PppoeAuth;
    hi_uchar8 uc_DhcpOp60En;
} __attribute__((packed))vs_omci_me_voip_globalconfig_s;
/*MEID:30210*/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 auc_DhcpOp60Base1[16];
    hi_uchar8 auc_DhcpOp60Base2[16];
    hi_uchar8 auc_DhcpOp60Base3[16];
    hi_uchar8 auc_DhcpOp60Base4[16];
    hi_ushort16 us_SvlanTpid;
    hi_ushort16 us_SvlanId;
    hi_uchar8 uc_SvlanCos;
    hi_ushort16 us_CvlanTpid;
    hi_ushort16 us_CvlanId;
    hi_uchar8 uc_CvlanCos;
    hi_ushort16 us_RtpSvlanTpid;
    hi_ushort16 us_RtpSvlanId;
    hi_uchar8 uc_RtpSvlanCos;
    hi_ushort16 us_RtpCvlanTpid;
    hi_ushort16 us_RtpCvlanId;
    hi_uchar8 uc_RtpCvlanCos;
} __attribute__((packed))vs_omci_me_voip_globalconfig2_s;
/*MEID:30213*/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_ushort16 us_SipMgPortNo;
    hi_char8 auc_SipProxyServIp1[22];
    hi_char8 auc_SipProxyServIp2[22];
    hi_char8 auc_SipProxyServIp3[20];
    hi_ushort16 us_SipProxyServComPortNo;
    hi_char8 auc_BackupSipProxyServIp1[22];
    hi_char8 auc_BackupSipProxyServIp2[22];
    hi_char8 auc_BackupSipProxyServIp3[20];
    hi_ushort16 us_BackupSipProxyServComPortNo;
    hi_char8 auc_ActiveSipProxyServer1[22];
    hi_char8 auc_ActiveSipProxyServer2[22];
    hi_char8 auc_ActiveSipProxyServer3[20];
    hi_char8 auc_SipRegServIp1[22];
    hi_char8 auc_SipRegServIp2[22];
    hi_char8 auc_SipRegServIp3[20];
    hi_ushort16 us_SipRegServComPortNo;
} __attribute__((packed))vs_omci_me_sip_config1_s;
/*MEID:30214*/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_char8 auc_BackupSipRegServIp1[22];
    hi_char8 auc_BackupSipRegServIp2[22];
    hi_char8 auc_BackupSipRegServIp3[20];
    hi_ushort16 us_BackupSipRegServComPortNo;
    hi_char8 auc_OutBoundServIP1[22];
    hi_char8 auc_OutBoundServIP2[22];
    hi_char8 auc_OutBoundServIP3[20];
    hi_ushort16 us_OutBoundServPortNo;
    hi_uint32 ui_SipRegInterval;
    hi_uchar8 uc_HeartbeatSwitch;
    hi_ushort16 us_HeartbeatCycle;
    hi_ushort16 us_HeartbeatCount;
} __attribute__((packed))vs_omci_me_sip_config2_s;
/*MEID:30216*/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_manage;
    hi_uchar8 auc_account[16];
    hi_uchar8 auc_password[16];
    hi_uchar8 auc_name1[16];
    hi_uchar8 auc_name2[16];
    hi_uchar8 auc_tidName1[16];
    hi_uchar8 auc_tidName2[16];
} __attribute__((packed))vs_omci_me_pots_config_s;
/*MEID:30217*/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_digitenable;
    hi_uchar8 auc_DiditMap[28];
} __attribute__((packed))vs_omci_me_voip_digitmap_s;
/*MEID:253*/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_AdminState;
    hi_uchar8 uc_Arc;
    hi_uchar8 uc_ArcInterval;
    hi_uchar8 uc_EthLoopDetectCfg;
} __attribute__((packed))vs_omci_me_eth_loop_detect_s;
/*Add by fyy for iphost*/
typedef enum {
    IP_HOST_CFG_DATA_IP_OPTIONS_DISABLE_OPTIONS     = 0x00,
    IP_HOST_CFG_DATA_IP_OPTIONS_ENABLE_DHCP			= 0x01,
    IP_HOST_CFG_DATA_IP_OPTIONS_RESPOND_PING		= 0x02,
    IP_HOST_CFG_DATA_IP_OPTIONS_RESPOND_TRACEROUTE	= 0x04,
    IP_HOST_CFG_DATA_IP_OPTIONS_ENABLE_IP_STACK		= 0x08,
} vs_ip_host_cfg_data_ip_options_t;

typedef enum
{
    VS_OMCI_WAN_MODE_BRIDGED = 1,
    VS_OMCI_WAN_MODE_DHCP,
    VS_OMCI_WAN_MODE_STATIC,
    VS_OMCI_WAN_MODE_PPPOE,
    VS_OMCI_WAN_MODE_END
}vs_omci_me_wan_mode_t;

typedef enum {
    VCD_SIG_PROTOCOL_USED_NONE		= 0,
    VCD_SIG_PROTOCOL_USED_SIP		= 1,
    VCD_SIG_PROTOCOL_USED_H248		= 2,
    VCD_SIG_PROTOCOL_USED_MGCP		= 3,
    VCD_SIG_PROTOCOL_USED_NON_OMCI	= 0xFF
} vcd_attr_sig_protocol_used_t;

#define VS_OMCI_X_CT_SRV_TR069		0x01
#define VS_OMCI_X_CT_SRV_INTERNET	0x02
#define VS_OMCI_X_CT_SRV_OTHER		0x04
#define VS_OMCI_X_CT_SRV_VOICE		0x08

/*MEID:250*/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_NatStatus;
    hi_uchar8 uc_Mode;
    hi_uchar8 uc_Connect;
    hi_ushort16 us_ReleaseTime;
    hi_uchar8 auc_UserName[25];
    hi_uchar8 auc_Password[25];
    hi_uchar8 uc_State;
    hi_uint32 ui_OnlineDuration;
} __attribute__((packed))vs_omci_me_extended_iphost_s;

/*MEID:347*/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_ipoptions;
    hi_uchar8 auc_macaddress[6];
    hi_uchar8 auc_OnuIdentifier[25];
    hi_uchar8 auc_Ipv6LinkLocalAddress[16];
    hi_uchar8 auc_Ipv6address[16];
    hi_uchar8 auc_defaultrouter[16];
    hi_uchar8 auc_primarydns[16];
    hi_uchar8 auc_secondarydns[16];
    hi_uchar8 uc_currentaddresstable;
    hi_uchar8 uc_crrentdefaultroutertable;
    hi_uchar8 uc_currentdnstable;
    hi_uchar8 auc_DUID[25];
    hi_uchar8 auc_onlinkprefix[17];
    hi_uchar8 uc_currentonlinkprefixtable;
    hi_ushort16 us_relayagentoptions;
} __attribute__((packed))vs_omci_me_ipv6_iphost_s;

#pragma pack()
#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif /* __OMCI_ME_VSOL_DEF_H__ */

