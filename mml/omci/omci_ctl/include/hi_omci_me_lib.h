/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_lib.h
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-16
  Description: ME内部公共函数头文件
******************************************************************************/

#ifndef __HI_OMCI_ME_LIB_H__
#define __HI_OMCI_ME_LIB_H__

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#include "hi_omci_me_map.h"

/*****************************************************************************
 *                                DEFINE                                     *
 *****************************************************************************/
#define HI_OMCI_ME_ALARM_BITMAP(index) (1 << (15 - (index)))

#define HI_OMCI_ME_ALARM_COMM_TIMEOUT (15 * 60 * 1000 + 1000)    /* 15分钟，后延1秒 */


/*****************************************************************************
 *                                TYPEDEF                                    *
 *****************************************************************************/
typedef struct {
	hi_uint32 ui_meid;
	hi_uint32 ui_instid;
} hi_omci_me_stat_timer_s;

typedef struct {
	hi_uint32 ui_meid;
	hi_uint32 ui_instid;
	hi_uint32 ui_local;     /* 1表示定时器是随ONT本地实体创建的;0表示是随OLT下发实体创建的 */
	hi_uint32 ui_timeout;
} hi_omci_me_alarm_timer_s;

/*fyy for vsol me*/
typedef void *(*VS_CALLBACK)(void *);
typedef hi_void (*vs_timer_call_f)(hi_void *pv_cookie);
#define VS_OMCI_TIMER_TIMEOUT (1000)  //1000ms
#define VS_OMCI_ME_WAN_INSTANCE_MAX 4

typedef struct {
    hi_uint32 ui_timer_mask;
	hi_uint32 ui_timer_id;
    hi_uint32 ui_timeout; //ms
    vs_timer_call_f timer_handler;
	hi_list_head st_listhead;
} vs_omci_timer_entity_s;

typedef enum {
    VS_OMCI_TIMER_MASK_MIN = 0,
    VS_OMCI_TIMER_MASK_DHCP_SERVER,
    VS_OMCI_TIMER_MASK_DHCPV6_SERVER,
    VS_OMCI_TIMER_MASK_LOGIN,
    VS_OMCI_TIMER_MASK_ACCESS_CONTROL,
    VS_OMCI_TIMER_MASK_TR069,
    VS_OMCI_TIMER_MASK_WAN,
    VS_OMCI_TIMER_MASK_WIFI,
    VS_OMCI_TIMER_MASK_SSID,
    VS_OMCI_TIMER_MASK_MAC_ACL,
    VS_OMCI_TIMER_MASK_VOIP,
    VS_OMCI_TIMER_MASK_IPHOST,
    VS_OMCI_TIMER_MASK_FACTORY,
    VS_OMCI_TIMER_MASK_MAX
} VS_OMCI_TIMER_MASK;
/*vsol omci me end*/

/*****************************************************************************
 *                                FUNCTION                                   *
 *****************************************************************************/
extern hi_int32 hi_omci_me_init();
extern hi_int32 hi_omci_me_exit();
extern hi_int32 hi_omci_me_reset();
extern hi_int32 hi_omci_me_deactive_exit();

extern hi_int32 hi_omci_me_pptpethuni_init(hi_uint32 ui_portid);
extern hi_int32 hi_omci_me_pptpethuni_exit(hi_uint32 ui_portid);
extern hi_int32 hi_omci_me_veip_init();
extern  hi_int32 hi_omci_me_tr069_management_init();
extern hi_int32 hi_omci_me_ip_host_init();
extern hi_int32 hi_omci_ext_ont_ability_init();
extern hi_int32 hi_omci_me_unig_init(hi_uint32 ui_portid);

extern hi_int32 hi_omci_me_tcont_init(hi_uint32 ui_tcontid);
extern hi_int32 hi_omci_me_anig_init();
extern hi_int32 hi_omci_me_pq_init(hi_uint32 ui_direction, hi_uint32 ui_tcont, hi_uint32 portid, hi_uint32 pri);

extern hi_int32 hi_omci_me_rmtdbg_init();
extern hi_int32 hi_omci_me_onudata_init();
extern hi_int32 hi_omci_me_oltg_init();
extern hi_int32 hi_omci_me_onug_init();
extern hi_int32 hi_omci_me_onu2g_init();
extern hi_int32 hi_omci_me_cardholder_init(hi_uint32 ui_isup, hi_uint32 ui_index, hi_uint32 ui_ishgu);
extern hi_int32 hi_omci_me_circuitpack_init(hi_uint32 ui_isup, hi_uint32 ui_index, hi_uint32 ui_ishgu);
extern hi_int32 hi_omci_me_onu_capability_init();
extern hi_int32 hi_omci_me_loid_auth_init();
extern hi_int32 hi_omci_me_powermng_init();
int32_t hi_omci_me_onu_optical_info_init();

extern hi_int32 hi_omci_me_stat_start(hi_omci_me_stat_timer_s *pst_timer);
extern hi_int32 hi_omci_me_stat_stop(hi_omci_me_stat_timer_s *pst_timer);
extern hi_int32 hi_omci_me_stat_init();
extern hi_int32 hi_omci_me_stat_exit();

extern hi_int32 hi_omci_me_alarm_msg_send(hi_uint32 ui_meid, hi_uint32 ui_instid, hi_uint32 ui_bitmap);
extern hi_int32 hi_omci_me_alarm_start(hi_omci_me_alarm_timer_s *pst_timer);
extern hi_int32 hi_omci_me_alarm_stop(hi_omci_me_alarm_timer_s *pst_timer);
extern hi_int32 hi_omci_me_alarm_init();
extern hi_int32 hi_omci_me_alarm_exit(hi_uint32 flag);
extern hi_int32 hi_omci_me_local_alarm_restart(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_void hi_omci_set_mibreset_flag(hi_uint32 flag);

extern hi_int32 hi_omci_me_pptp_pots_init(hi_uint32 ui_pots);
extern hi_int32 hi_omci_me_voip_line_status_init(hi_uint32 ui_pots);
extern hi_int32 hi_omci_me_iphost_init(hi_uint32 ui_stacknum);
extern hi_int32 hi_omci_me_voip_cfg_data_init();
extern hi_int32 hi_omci_voip_msg();
hi_void hi_omci_voip_msg_eixt();
extern hi_int32 hi_omci_me_esc_init();
extern hi_uint32 hi_omci_me_esc_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_oltg_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_gemctppm_init();

/*vsol omci me*/
extern pthread_mutex_t g_vsol_mutex;
extern vs_omci_timer_entity_s *vs_omci_timer_search(VS_OMCI_TIMER_MASK timer_mask);
extern hi_int32 vs_omci_timer_create(vs_timer_call_f timer_handler, VS_OMCI_TIMER_MASK timer_mask, hi_uint32 timeout);
extern hi_int32 vs_omci_timer_restart(VS_OMCI_TIMER_MASK timer_mask);
extern hi_int32 vs_omci_timer_delete(VS_OMCI_TIMER_MASK timer_mask);
extern hi_int32 vs_omci_phtread_create(pthread_t *tid, VS_CALLBACK func, hi_void *arg);
extern hi_int32 vs_omci_me_init();
extern hi_int32 vs_omci_me_exit();
extern hi_int32 vs_omci_me_pri_cap1_init();
extern hi_int32 vs_omci_me_dhcp_server_init();
extern hi_int32 vs_omci_me_dhcpv6_server1_init();
extern hi_int32 vs_omci_me_dhcpv6_server2_init();
extern hi_int32 vs_omci_me_dhcpv6_server3_init();
extern hi_int32 vs_omci_me_login_init();
extern hi_int32 vs_omci_me_firewall_level_init();
extern hi_int32 vs_omci_me_access_control_init();
extern hi_int32 vs_omci_me_tr069_acs_server_init();
extern hi_int32 vs_omci_me_tr069_config_init();
extern hi_int32 vs_omci_me_tr069_middleware_config_init();
extern hi_int32 vs_omci_me_tr069_reverse_connect_init();
extern hi_int32 vs_omci_me_tr069_stun_config_init();
extern hi_int32 vs_omci_me_wan_number_init();
extern hi_int32 vs_omci_me_wan_6rd_init(hi_ushort16 us_instid);
extern hi_int32 vs_omci_me_wan_config_data_init(hi_ushort16 us_instid);
extern hi_int32 vs_omci_me_wan_config_pppoe_init(hi_ushort16 us_instid);
extern hi_int32 vs_omci_me_wan_dhcp_ipv6_init(hi_ushort16 us_instid);
extern hi_int32 vs_omci_me_wan_ds_lite_init(hi_ushort16 us_instid);
extern hi_int32 vs_omci_me_wan_port_bind_init(hi_ushort16 us_instid);
extern hi_int32 vs_omci_me_wan_static_ip_init(hi_ushort16 us_instid);
extern hi_int32 vs_omci_me_wan_static_ipv6_init(hi_ushort16 us_instid);
extern hi_int32 vs_omci_me_wan_vlan_mode_init(hi_ushort16 us_instid);
extern hi_int32 vs_omci_me_wifi_control_init(hi_ushort16 us_instid);
extern hi_int32 vs_omci_me_wifi_channel_width_init(hi_ushort16 us_instid);
extern hi_int32 vs_omci_me_wifi_mesh_init(hi_ushort16 us_instid);
extern hi_int32 vs_omci_me_wifi_ssid_data1_init(hi_ushort16 us_instid);
extern hi_int32 vs_omci_me_wifi_ssid_data2_init(hi_ushort16 us_instid);
extern hi_int32 vs_omci_me_wifi_ssid_data3_init(hi_ushort16 us_instid);
extern hi_int32 vs_omci_me_igmp_config_init();
extern hi_int32 vs_omci_me_factory_reset_init();
extern hi_int32 vs_omci_me_mac_aging_time_init();
extern hi_int32 vs_omci_me_mac_limit_init();
extern hi_int32 vs_omci_me_onu_catv_init();
extern hi_int32 vs_omci_me_pon_speed_limit_init();
extern hi_int32 vs_omci_me_auth_key_init();
extern hi_int32 vs_omci_me_pri_auth_init();
extern hi_int32 vs_omci_me_mac_acl_init();
extern hi_int32 vs_omci_me_mac_acl_list_init();

extern hi_int32 vs_omci_me_ont_voipngncfg_init();
extern hi_int32 vs_omci_me_ont_voippotscfg_init(hi_ushort16 us_instid);
extern hi_int32 vs_omci_me_ont_voippotsstatus_init(hi_ushort16 us_instid);
extern hi_int32 vs_omci_me_pots_config_init(hi_ushort16 us_instid);
extern hi_int32 vs_omci_me_sip_config1_init();
extern hi_int32 vs_omci_me_sip_config2_init();
extern hi_int32 vs_omci_me_voip_digitmap_init();
extern hi_int32 vs_omci_me_voip_globalconfig_init();
extern hi_int32 vs_omci_me_voip_globalconfig2_init();

extern hi_int32 hi_omci_me_pptp_video_uni_init();
extern hi_int32 hi_omci_me_pptp_80211_uni_init(hi_ushort16 port_id);

extern hi_int32 vs_omci_me_eth_loop_detect_init(hi_uint32 ui_portid);
extern hi_int32 vs_omci_me_eth_loop_detect_exit(hi_uint32 ui_portid);
extern hi_int32 vs_omci_me_extended_iphost_init();
extern hi_int32 vs_omci_me_ipv6_iphost_init(hi_ushort16 port_id);
/*vsol omci me end*/

/* hwtc externed me */
extern hi_int32 hwtc_omci_me_init();
extern hi_int32 hwtc_omci_me_ctc_extended_onu_g_init();
extern hi_int32 hwtc_omci_me_65417_init(hi_ushort16 entity_id);
extern hi_int32 hwtc_omci_me_350_init();
extern hi_int32 hwtc_omci_me_373_init();
extern hi_int32 hwtc_omci_me_65427_init();
extern hi_int32 hwtc_omci_me_65398_init();
extern hi_int32 hwtc_omci_me_65423_init();
extern hi_int32 hwtc_omci_me_65447_init();
extern hi_int32 hwtc_omci_me_65431_init();
extern hi_int32 hwtc_omci_me_65414_init();
extern hi_int32 hwtc_omci_me_65415_init();
extern hi_int32 hwtc_omci_me_65426_init();
extern hi_int32 hwtc_omci_me_65425_init();
extern hi_int32 hwtc_omci_me_65430_init();
extern hi_int32 hwtc_omci_me_65444_init(hi_ushort16 port_id);
extern hi_int32 hwtc_omci_me_65450_init();
extern hi_int32 hwtc_omci_me_65451_init();
extern hi_int32 hwtc_omci_me_65452_init();
extern hi_int32 hwtc_omci_me_65453_init();
extern hi_int32 hwtc_omci_me_65454_init(hi_ushort16 port_id);
extern hi_int32 hwtc_omci_me_GeneralPurposeBuffer_init();
extern hi_int32 hwtc_omci_me_Ieee80211StaMgmData1_init(hi_ushort16 port_id);
//automatic tool identification marks, do not delete this line

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_OMCI_ME_LIB_H__*/
