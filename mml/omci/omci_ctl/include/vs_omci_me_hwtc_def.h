/******************************************************************************

Copyright (c) vsol. 2024-2025. All rights reserved.

 ******************************************************************************
  文 件 名   : vs_omci_me_hwtc_def.h
  版 本 号   : V1.0

******************************************************************************/
#ifndef __OMCI_ME_HWTC_DEF_H__
#define __OMCI_ME_HWTC_DEF_H__


#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#pragma pack(1)

/*MEID:308 G.988 9.12.12 General Purpose Buffer */
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uint32 ui_MaxSize;
    hi_uchar8 uc_BufferTbl[25];
    hi_uint32 ui_TblSize;
} __attribute__((packed))hwtc_omci_me_GeneralPurposeBuffer_s;

/*MEID:65398 HW Extend Ont G2, relate to access control */
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_attr1;
    hi_uchar8 uc_attr2; //&diagnose> ont wan-access http enable/disable
    hi_uchar8 uc_attr3; //&diagnose> ont lan-access telnet enable/disable
    hi_uchar8 uc_attr4; //&diagnose> ont lan-access http enable/disable
    hi_uchar8 uc_attr5;
    hi_uchar8 uc_attr6;
    hi_uchar8 uc_attr7;
    //used when "diagnose%%>remote-http" setup username, point to meclass(157 large string) entryID
    hi_ushort16 us_attr8;
    //used when "diagnose%%>remote-http" setup password, point to meclass(157 large string) entryID
    hi_ushort16 us_attr9;
    //used when "diagnose%%>remote-http" setup duration, unit 'minute'
    hi_ushort16 us_attr10;
    hi_uchar8 uc_attr11;
    hi_uchar8 uc_attr12;
    hi_uchar8 uc_attr13;
    hi_uchar8 auc_attr14[25];
    hi_uchar8 uc_attr15;
    hi_uchar8 uc_attr16;
} __attribute__((packed))hwtc_omci_me_65398_s;

/*MEID:65408 ctc/huawei externed ont ability */
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_ResetDefault;
    hi_uchar8 attr2;
    hi_uchar8 attr3;
    hi_uchar8 attr4;
    hi_ushort16 attr5;
    hi_uint32 attr6;
    hi_uchar8 attr7[0x10]; // huawei ont version product id, huawei onu 8415 is159D, display ont version 0 1 
    hi_uchar8 attr8;
    hi_uchar8 attr9[0x18]; // huawei ont custom info, huawei onu 8415  CONVERGEICT, but olt only display max 24 chars
    hi_uchar8 attr10[0x10];
    hi_uchar8 attr11;
    hi_uchar8 attr12[0x19]; // huawei onu @CN#Common& 
    hi_uchar8 attr13;
    hi_uchar8 attr14;
    hi_uint32 attr15;
    hi_uint32 attr16;
} __attribute__((packed))hwtc_omci_me_ctc_extended_onu_g_s;

/*MEID:65414 huawei OPTICAL DATA */
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_attr1;
    hi_uchar8 uc_attr2;
    hi_uchar8 uc_attr3;
    hi_uchar8 uc_attr4;
    hi_uchar8 uc_attr5;
    hi_uchar8 auc_attr6[16];
    hi_ushort16 us_attr7;
    hi_ushort16 us_attr8;
    hi_uint32 ui_attr9;
    hi_uint32 ui_attr10;
    hi_ushort16 us_attr11;
    hi_ushort16 us_attr12;
    hi_uchar8 auc_attr3[8];
} __attribute__((packed))hwtc_omci_me_65414_s;

typedef enum {
    MIB_ME65415_SERVICE_TYPE_INTERNET = 0,
    MIB_ME65415_SERVICE_TYPE_TR069 = 1,
    MIB_ME65415_SERVICE_TYPE_VOIP = 2,
    MIB_ME65415_SERVICE_TYPE_INTERNET_TR069 = 3,
    MIB_ME65415_SERVICE_TYPE_TR069_VOIP = 4, 
    MIB_ME65415_SERVICE_TYPE_INTERNET_TR069_VOIP = 5,
    MIB_ME65415_SERVICE_TYPE_INTERNET_VOIP = 6,
    MIB_ME65415_SERVICE_TYPE_IPTV = 7,
    MIB_ME65415_SERVICE_TYPE_OTHER = 8,
} mib_me65415_service_type_e;

typedef struct {
    /*(0-32) */
    hi_uchar8   wan_index;
    hi_char8    wan_name[32];

    /* byte (33-36) */
    hi_uchar8   service_type;       // byte 33: enum MIB_ME65415_SERVICE_TYPE
    //0:unconfiguration, 0x01:IP routed, 0x02:IP bridge, 0x03 pppoe bridge
    hi_uchar8   conn_type;          // byte 34
    //0:unconfiguration, 0x01:connecting, 0x02:connected 0x03 pending, 0x04 desconnecting 0x05 disconnected
    hi_uchar8   ipv4_conn_status;   // byte 35
    //0:invalid, 1:dhcp , 2:static, 3:PPPoE
    hi_uchar8   ipv4_access_type;   // byte 36: IPv4

    /* IPv4 info (37-48) */
    hi_uint32  ipv4_address;
    hi_uint32  ipv4_mask;
    hi_uint32  ipv4_gateway;

    /* VLAN and pri, byte (49-52) */
    hi_uchar8   vlan_priority;
    hi_uchar8   vlan_id_low;
    hi_uchar8   option60;
    hi_uchar8   switch_en;

    /* MA address , byte(53-58) */
    hi_uchar8   wan_mac[6];

    /*  (59-62) */
    //0x01 Specified, 0x02 CopyFromIPPrecedence
    hi_uchar8   priority_policy;
    // 0x01 IpoE 0x02 pppoe 
    hi_uchar8   l2_encap_type;
    hi_uchar8   ipv4_switch;
    hi_uchar8   ipv6_switch;

    /* IPv6, byte (63-132) */
    hi_uchar8   ipv6_prefix[16];
    hi_uchar8   prefix_len;
    //0x01 AutoConfigured, 0x02 PrefixDelegation, 0x03 RouterAdvertisement , 0x04 static ,0x05 none 
    hi_uchar8   prefix_mode;
    hi_uint32  prefix_pref_time;
    hi_uint32  prefix_valid_time;
    hi_uchar8   ipv6_address[16];
    //0x01 Preferred , 0x02 NoPrefix
    hi_uchar8   ipv6_addr_status;
    //0x01 AutoConfigured, 0x02 DHCPv6, 0x03 Static, 0x04 none 
    hi_uchar8   ipv6_access_mode;
    hi_uint32  ipv6_pref_time;
    hi_uint32  ipv6_valid_time;
    //0x01 off, 0x02 Static, 0x03 Dynamic
    hi_uchar8   ds_lite_mode;
    hi_uchar8   ds_lite_peer[16];
    //0:unconfiguration, 0x01:connecting, 0x02:connected 0x03 pending, 0x04 desconnecting 0x05 disconnected
    hi_uchar8   ipv6_conn_status;

    /* mcast VLAN, bute(133-137) */
    hi_uchar8   ipv4_mvlan_high;
    hi_uchar8   ipv4_mvlan_low;
    hi_uchar8   nat_switch;
    hi_uchar8   ipv6_mvlan_high;
    hi_uchar8   ipv6_mvlan_low;

    /* DNS, byte (138-145) */
    hi_uint32  ipv4_primary_dns;
    hi_uint32  ipv4_secondary_dns;

    /* IPv6 DNS, byte (146-177) */
    hi_uchar8   ipv6_primary_dns[16];
    hi_uchar8   ipv6_secondary_dns[16];
}__attribute__((__packed__))  me65415_waninfo_t;

/*MEID:65415 huawei extended wan info */
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_attr1;
    hi_uchar8 uc_attr2[24]; //wan info table
    hi_uchar8 uc_attr3;
} __attribute__((packed))hwtc_omci_me_65415_s;

/*MEID:65417 Extended IP HOST Config Data -> pppoe config */
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_Enable;
    hi_uchar8 auc_UserName[25];
    hi_uchar8 auc_attr3[25];
    hi_uchar8 auc_attr4[25];
    hi_uchar8 auc_Password[25];
    hi_uchar8 auc_attr6[25];
    hi_uchar8 auc_attr7[25];
    hi_uchar8 uc_attr8;
} __attribute__((packed))hwtc_omci_me65417_s;

/*MEID:65423 HW Extend Ont3 Ability*/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_attr1;
    hi_uchar8 uc_attr2;
    hi_uchar8 uc_attr3;
    hi_uchar8 uc_attr4;
    hi_uint32 ui_attr5;
    hi_uchar8 auc_attr6[8];
    hi_uchar8 uc_attr7;
    hi_uchar8 auc_attr8[25];
    hi_uchar8 auc_attr9[6];
    hi_uint32 ui_attr10;
    hi_uchar8 uc_attr11;
    hi_uchar8 uc_attr12;
    hi_uchar8 uc_attr13;
    hi_uchar8 uc_attr14;
    hi_uchar8 uc_attr15;
    hi_ushort16 us_attr16;
} __attribute__((packed))hwtc_omci_me_65423_s;

/*MEID:65425 HW Extend CPU */
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_attr1;
    hi_uint32 ui_attr2;
    hi_uint32 ui_attr3;
    hi_uint32 ui_attr4;
    hi_uint32 ui_attr5;
    hi_ushort16 us_attr6;
    hi_uchar8 uc_attr7;
} __attribute__((packed))hwtc_omci_me_65425_s;

/*MEID:65426 HW Maintenance*/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_ushort16 us_attr1;
    hi_ushort16 us_attr2;
} __attribute__((packed))hwtc_omci_me_65426_s;

/*MEID:65427 huawei proprietary me */
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_ushort16 us_attr1;
    hi_uchar8 auc_attr2[16];
    hi_uchar8 uc_table1;
    hi_uchar8 uc_table2;
} __attribute__((packed))hwtc_omci_me_65427_s;

/*MEID:65430 HW extended Type*/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_attr1;
    hi_uchar8 uc_attr2;
    hi_uchar8 uc_attr3;
    hi_uchar8 uc_attr4;
    hi_uchar8 uc_attr5;
    hi_uchar8 uc_attr6;
    hi_uchar8 uc_attr7;
    hi_uchar8 uc_attr8;
    hi_uchar8 uc_attr9;
    hi_uchar8 uc_attr10;
    hi_uchar8 uc_attr11;
    hi_uchar8 uc_attr12;
    hi_uchar8 uc_attr13;
    hi_uchar8 uc_attr14;
    hi_uchar8 uc_attr15;
    hi_uchar8 uc_attr16;
} __attribute__((packed))hwtc_omci_me_65430_s;

/*MEID:65431 HW Extended OLT */
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uint32 ui_attr1;
} __attribute__((packed))hwtc_omci_me_65431_s;

/*MEID:65444 Hw extern 802.11*/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_ulong64 ul_attr1;
} __attribute__((packed))hwtc_omci_me_65444_s;

/*MEID:65447 huawei Extended MAC*/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_attr1;
} __attribute__((packed))hwtc_omci_me_65447_s;

/*MEID:65450 HW Extern 802.11 Inteface*/
/* Table MeHw65450 test response struct */
#define MEHW65450_TEST_WLAN_STATUS_BASIC 0x0003 //don't know lower 0x03 meaning
#define MEHW65450_TEST_WLAN_STATUS_STA_INFO 0x0102 //don't know lower 0x02 meaning 
#define MEHW65450_TEST_WLAN_STATUS_CHANNEL_INFO 0x0201 //don't know lower 0x01 meaning

typedef enum{
    QUALIRY_LEVEL_BAD=0x1,
    QUALIRY_LEVEL_POOR1=0x2,
    QUALIRY_LEVEL_POOR2=0x3,
    QUALIRY_LEVEL_GOOD=0x4,
} quality_level_t;

typedef struct{
    unsigned short testType; //must be MEHW65450_TEST_WLAN_STATUS_BASIC
    unsigned short resv1; //must be 0x6701
    unsigned char apID[3]; // usually be string "ONU"
    unsigned char resv2[28]; //all zero
    unsigned short apNums; //number of wlan interface
 }__attribute__((packed)) mehw65450_test_resp_basic_hdr_t;

typedef struct{
    unsigned char ssidIdx;
    unsigned int sentPackets;
    unsigned int sentErrPackets;
    unsigned int sentDiscardPackets;
    unsigned int recvPackets;
    unsigned int recvErrPackets;
    unsigned int recvDiscardPackets;
    unsigned char transmissionQualityLevel;
    unsigned char ssidName[32];
    unsigned char resv1[8]; //usually all 0xff
    unsigned char bandWidthStr[5]; // "20MHz" or "40MHz" or "80MHz", etc.
    unsigned char resv2[32]; //usually all 0x00
} __attribute__((packed)) mehw65450_test_resp_basic_t;

typedef struct{
    unsigned short testType; //must be MEHW65450_TEST_WLAN_STATUS_STA_INFO
    unsigned short resv1; //must be 0x3201
    unsigned char apID[3]; // usually be string "ONU"
    unsigned char resv2[28]; //all zero
    unsigned short staNums; //number of sta
 }__attribute__((packed)) mehw65450_test_resp_sta_info_hdr_t;

typedef struct{
    unsigned char ssidIdx;
    unsigned char staMac[6]; // hex array mac
    unsigned short txRate;
    unsigned short rxRate;
    char rssi;
    char snr;
    unsigned char transmissionQualityLevel;
    unsigned char ssidName[32];
    unsigned char resv1[4]; //can be all zero
} __attribute__((packed)) mehw65450_test_resp_sta_info_t;

typedef struct{
    unsigned short testType; //must be MEHW65450_TEST_WLAN_STATUS_CHANNEL_INFO & 0xff00
    unsigned short resv1; //must be 0x0401
    unsigned char apID[3]; // usually be string "ONU"
    unsigned char resv2[28]; //all zero
    unsigned short channelNums; //number of reported channel info
 }__attribute__((packed)) mehw65450_test_resp_channel_info_hdr_t;

 typedef struct{
     unsigned char channelNum;
     unsigned char apNum;
     unsigned short intfDegree; //Interference degree
  }__attribute__((packed)) mehw65450_test_resp_channel_info_t;

typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_attr1;
    hi_uchar8 uc_attr2;
    hi_uchar8 uc_attr3;
    hi_uchar8 uc_attr4;
    hi_uchar8 uc_attr5;
} __attribute__((packed))hwtc_omci_me_65450_s;

/*MEID:65451 HW extended WAN Config, olt create/delete */
typedef struct {
    hi_omci_me_msg_head_s st_msghead; //instid point to iphost with the same entry id
    //set by create
    hi_uchar8 uc_attr1; //conn_type-> 0:router; 1:bridge
    //set by create
    hi_uchar8 uc_attr2; //enable_NAT
    hi_ushort16 us_attr3;
} __attribute__((packed))hwtc_omci_me_65451_s;

/*MEID:65452 olt create/delete */
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    //set by create
    hi_ushort16 us_attr1; //point to iphost wan, set iphost wan service type INTERNET
} __attribute__((packed))hwtc_omci_me_65452_s;

/*MEID:65453 HW extended WAN Data3*/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_uchar8 uc_attr1;
    hi_uchar8 uc_attr2;
    hi_uchar8 auc_attr3[12];
} __attribute__((packed))hwtc_omci_me_65453_s;

//attr1 Wireless Standard
#define MEHW65454_ATTR1_11A 0x1
#define MEHW65454_ATTR1_11B 0x2
#define MEHW65454_ATTR1_11G 0x4
#define MEHW65454_ATTR1_11N 0x8
#define MEHW65454_ATTR1_11AC 0x10

/*MEID:65454 HW Extend 80211 Mgment*/
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_ushort16 us_attr1; //wlan standard
    hi_ushort16 us_attr2; // max sta num
    hi_ushort16 us_attr3; //current sta num
    hi_uint32 ui_attr4;
    hi_uchar8 auc_attr5[8];
    hi_uchar8 auc_attr6[8];
    hi_uchar8 auc_attr7[8];
} __attribute__((packed))hwtc_omci_me_65454_s;

/*MEID:92 Ieee80211StaMgmData1 */
typedef struct {
    hi_omci_me_msg_head_s st_msghead;
    hi_ushort16 us_attr1;//dot11 medium occupancy limit
    hi_uchar8 uc_attr2;//dot11 CF pollable
    hi_uchar8 uc_attr3;//dot11 CFP period
    hi_ushort16 us_attr4;//dot11 CFP max duration
    hi_uint32 ui_attr5;//dot11 authentication response timeout
    hi_uchar8 uc_attr6;//dot11 privacy option implemented
    hi_uchar8 uc_attr7;//dot11 power management mode
    hi_uchar8 auc_attr8[16];//dot11 desired SSID1
    hi_uchar8 auc_attr9[16];//dot11 desired SSID2
    hi_uchar8 uc_attr10;//
    hi_uchar8 auc_attr11[8];////dot11 operational rate set
    hi_ushort16 us_attr12;//dot11 beacon period
    hi_uchar8 uc_attr13;//dot11 DTIM period
    hi_uint32 ui_attr14;//dot11 association response timeout
    hi_uint32 ui_attr15;//dot11 authentication algorithm
    hi_uint32 ui_attr16;//dot11 authentication algorithms enable
} __attribute__((packed))hwtc_omci_me_Ieee80211StaMgmData1_s;

#pragma pack()
#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif /* __OMCI_ME_HWTC_DEF_H__ */

