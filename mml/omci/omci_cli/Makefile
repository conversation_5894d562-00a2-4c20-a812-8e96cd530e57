include $(HI_EXT_CONFIG)
#===============================================================================
# export sub dir
#===============================================================================
#HI_SUB_DIR :=
#===============================================================================
# export lib
#===============================================================================
HI_LOC_LIB += -lhi_basic -lhi_ioreactor -lhi_util -lhi_ipc
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/ssf/ipc/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/basic/include/os
#
#===============================================================================
# target
#===============================================================================
TARGET 		= omci_cli
TARGET_TYPE	= app

include $(HI_HISI_GW_APP_SCRIPT_DIR)/app.mk