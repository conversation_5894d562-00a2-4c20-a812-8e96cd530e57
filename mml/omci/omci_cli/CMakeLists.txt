include(${CONFIG_CMAKE_DIR}/toolchains/${CONFIG_TOOLCHAIN_LINUX_APP}.cmake)

# 组件名称
set(USERAPP_NAME omcicli)

# 组件类型
set(USERAPP_TARGET_TYPE bin)

# 依赖的源文件
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR} SOURCE_DIR)

set(USERAPP_PRIVATE_SRC
    ${SOURCE_DIR}
)

# 依赖的头文件
set(USERAPP_PRIVATE_INC
    ${HGW_BASIC_DIR}/include
    ${HGW_FWK_DIR}/include
    ${HGW_FWK_DIR}/ipc/include
)

# 依赖的库
set(USERAPP_PRIVATE_LIB
    hi_basic
    hi_util
    hi_ipc
    hi_ioreactor
    hi_owal_ssf
    openwrt_lib
)

build_app_feature()