/******************************************************************************

******************************************************************************/
#include "hi_uspace.h"
#include "hi_basic.h"
#include "hi_ipc.h"

hi_int32 main(hi_int32 argc, hi_char8 *argv[])
{
	hi_ushort16  v_data;
	char *endptr;
	hi_long64 num;

	if (argc<3) {
		goto error;
	}
	num = strtol(argv[2], &endptr, 10);
	if (*endptr != '\0') {
		// 转换失败处理
		goto error;
	}
	v_data = num;

	if(strncmp(argv[1],"get",3) == 0)
	{
		HI_IPC_CALL("hi_omci_mib_dump_meid", &v_data, sizeof(v_data));
	}else if (strncmp(argv[1],"set",3) == 0)
	{
		//HI_IPC_CALL("hi_omci_mib_dump_meid", &v_data, sizeof(v_data));
	}

	return 0;
error:
//	hi_os_printf("\r\n omcicli get meid[0-65535] ");// 参数错误处理
	
	return -1;
}
