include(${CONFIG_CMAKE_DIR}/linux_mod_complie_cfg/sdk_public_cfg.cmake)

set(MODULE_NAME hi_koam)
set(LIST_MOD_SOURCES
    source/hi_kernel_ctcoam_reg.c
    source/hi_kernel_ctcoam_main.c
    source/hi_kernel_stdoam_reg.c
    source/hi_kernel_oam_filelog.c
)

set(LIST_MOD_PRIVATE_INC
    ${CMAKE_CURRENT_SOURCE_DIR}/source
    ${CMAKE_CURRENT_SOURCE_DIR}/../oam_service/include
    ${HGW_FWK_DIR}/include
    ${HGW_FWK_DIR}/ipc/include
    ${HGW_FWK_DIR}/msgcenter/include
    ${HGW_BASIC_DIR}/include
    ${CONFIG_LINUX_DRV_DIR}/net/pon/include
)

set(LIST_MOD_PRIVATE_DEF)

set(LIST_MOD_PRIVATE_DEPEND
    hi_kpon_core
    hi_kmsgcenter
)

build_kernel_module()
