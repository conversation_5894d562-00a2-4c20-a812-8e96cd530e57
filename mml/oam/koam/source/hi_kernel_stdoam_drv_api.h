/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  File Name     : hi_kernel_stdoam_drv_api.h
  Version       : Initial Draft
  Created       : 2013/6/19
  Description   : common header file for oam
******************************************************************************/
#ifndef __HI_KERNEL_STDOAM_DRV_API_H__
#define __HI_KERNEL_STDOAM_DRV_API_H__

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

typedef hi_uint32(* HI_KERNEL_STDOAM_EXTOAM_MSG_PFN)(hi_uint32 ui_llidindex, hi_void *pv_msg, hi_uint32 ui_msgsize, hi_uint32 ui_msgtype);

typedef enum {
    HI_KERNEL_STDOAM_TYPE_RX_EXTOAM_E   = 0,
    HI_KERNEL_STDOAM_TYPE_NOTISFY_DISCOVERY_E,
    HI_KERNEL_STDOAM_TYPE_NUM_E

} HI_KERNEL_STDOAM_TYPE_CALLBACK_E;

hi_void hi_kernel_stdoam_callback_register(HI_KERNEL_STDOAM_TYPE_CALLBACK_E em_type, HI_KERNEL_STDOAM_EXTOAM_MSG_PFN pfn_handler);
hi_uint32 hi_kernel_stdoam_drvapi_txmsgproc(hi_uint32 ui_llidindex, hi_void *pv_msg, hi_uint32 ui_msglen, hi_uint32 ui_msgtype);
hi_uint32 hi_kernel_stdoam_drvapi_setoamoui(hi_uchar8 *puc_oui);
hi_uint32 hi_kernel_stdoam_drvapi_getregstate(hi_uint32 ui_llidindex, hi_uchar8 *puc_state);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /*__HI_KERNEL_STDOAM_DRV_API_H__*/

