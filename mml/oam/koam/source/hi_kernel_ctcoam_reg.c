/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  File Name     : hi_kernel_ctcoam_reg.c
  Version       : Initial Draft
  Created       : 2013/6/19
  Last Modified :
  Description   :  CTCOAM register function
******************************************************************************/
#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */
#include "hi_oam_common.h"

#include "hi_kernel_epon_protocol_inc.h"
#include "hi_kernel_stdoam_drv_api.h"
#include "hi_kernel_ctcoam_main.h"
#include "hi_kernel_ctcoam_reg.h"
#include "hi_pon_core_epon.h"

/* 软件能力 */
hi_kernel_epon_sw_capability_s g_st_ont_sw_capability;

/* 注册相关全局数据 */
hi_epon_llid_exoam_gloabal_data_s g_ast_extoam_global_data[HI_MAX_LLID_NUM];

static hi_kernel_oam_report_s g_st_ctcoam_report_msg;

/* Ctcoam模块状态入口函数 */
HI_KERNEL_EPON_CTCOAM_STATE_PFN g_apfn_ctcoam_proc_state[] = {
    hi_kernel_ctcoam_reg_proc_passive_wait_state,
    hi_kernel_ctcoam_reg_proc_send_ext_info_state,
    hi_kernel_ctcoam_reg_proc_ack_state,
    hi_kernel_ctcoam_reg_proc_send_any_state,
};

hi_kernel_oam_queue_head_s *g_pst_oamqueue = HI_NULL;
struct task_struct *g_s_oamtask;

static hi_int32 hi_kernel_mpcp_state_move_callback(hi_void *pv_data, hi_uint32 ui_len)
{
    struct hi_epon_llidstate *pst_state = (struct hi_epon_llidstate *)pv_data;
    return (hi_int32)hi_kernel_ctcoam_mpcpstate_notify(pst_state->index, pst_state->state);
}
static hi_int32 hi_kernel_mpcp_discov_info_callback(hi_void *pv_data, hi_uint32 ui_len)
{
    struct hi_epon_disinfo *pst_info = (struct hi_epon_disinfo *)pv_data;
    return (hi_int32)hi_kernel_ctcoam_mpcp_disinfo_notify(pst_info->index, pst_info->info);
}

#if 0
static hi_int32 hi_kernel_oam_eps_alarm(hi_uchar8 uc_alarmstate, hi_uint32 ui_alarminfo)
{
    hi_int32 i_ret = HI_RET_SUCC;
    hi_kernel_oam_report_s *pst_reportmsg = HI_NULL;
    hi_oam_alarm_report_s *pst_alarm;

    pst_reportmsg = hi_kernel_malloc(sizeof(hi_kernel_oam_report_s));
    if (HI_NULL == pst_reportmsg) {
        return HI_RET_FAIL;
    }

    pst_reportmsg->ui_msgtype = HI_OAM_MPCPREG_STATE_E;
    pst_reportmsg->ui_len = HI_OAM_PMU_ALARM_E;
    pst_alarm = (hi_oam_alarm_report_s *)pst_reportmsg->uc_content;
    pst_alarm->uc_alarm_state = uc_alarmstate;
    pst_alarm->ui_alarminfo = ui_alarminfo;

    i_ret = hi_kernel_oam_queue_send(g_pst_oamqueue, (hi_void *)pst_reportmsg, sizeof(hi_kernel_oam_report_s));
    if (i_ret != HI_RET_SUCC) {
        hi_kernel_ctcoam_dbg_err_print("\r\n OAM send queue fail(%d).", i_ret);
    }

    kfree(pst_reportmsg);
    return i_ret;
}
#endif

static hi_void hi_kernel_ctcoam_ver_init(hi_void)
{
    g_st_ont_sw_capability.auc_ctcver[0x0] = HI_KERNEL_EPON_CTCOAM_VER_BEGIN_BYTE; /* CTC 2.0 */
    g_st_ont_sw_capability.auc_ctcver[0x1] = HI_KERNEL_EPON_CTCOAM_VER_21_BYTE; /* CTC 2.1 */
    g_st_ont_sw_capability.auc_ctcver[0x2] = HI_KERNEL_EPON_CUOAM_VER_C1_BYTE; /* CU C1 */
    g_st_ont_sw_capability.auc_ctcver[0x3] = HI_KERNEL_EPON_CUOAM_VER_C2_BYTE; /* CU C2 */
    g_st_ont_sw_capability.auc_ctcver[0x4] = HI_KERNEL_EPON_CTCOAM_VER_30_BYTE;
    g_st_ont_sw_capability.auc_ctcver[0x5] = HI_KERNEL_EPON_CUOAM_VER_C3_BYTE; /* CU C3 */
    g_st_ont_sw_capability.auc_ctcver[0x6] = HI_KERNEL_EPON_CUOAM_VER_C4_BYTE; /* CU C4 */
    g_st_ont_sw_capability.auc_ctcver[0x7] = HI_KERNEL_EPON_CUOAM_VER_C5_BYTE; /* CU C5 */
}

/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_event_init
 Description  : initialize something about event
 Input        : hi_void
 Output       : None
 Return Value : hi_void
*****************************************************************************/
hi_void hi_kernel_ctcoam_event_init(hi_void)
{
    /*initial global para*/
    memset(g_ast_extoam_global_data, 0, sizeof(g_ast_extoam_global_data));

    /*register callback function */
    hi_kernel_stdoam_callback_register(HI_KERNEL_STDOAM_TYPE_RX_EXTOAM_E, hi_kernel_ctcoam_callback_rx_msg);
    hi_kernel_stdoam_callback_register(HI_KERNEL_STDOAM_TYPE_NOTISFY_DISCOVERY_E, hi_kernel_ctcoam_callback_start_discovery);

    /* initial supported version of ctc oam */
	hi_kernel_ctcoam_ver_init();

    /* initial number of port */
    g_st_ont_sw_capability.uc_support_pon_num  = 1;
    g_st_ont_sw_capability.uc_support_voip_num = 0;
    g_st_ont_sw_capability.uc_support_e1_num   = 0;

    /* initial capbility of igmp */
    g_st_ont_sw_capability.ui_igmp_not_fast_leave_ability = HI_DISABLE;
    g_st_ont_sw_capability.ui_igmp_fast_leave_ability    = HI_DISABLE;
    g_st_ont_sw_capability.ui_ctc_ctrl_not_fast_leave_ability = HI_DISABLE;
    g_st_ont_sw_capability.ui_ctc_ctrl_fast_leave_ability    = HI_DISABLE;

    /* initial capbility of extend oam */
    /* TEKNOVUS---not support */
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_TEKNOVUS_E].uc_isenable        = HI_DISABLE;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_TEKNOVUS_E].uc_version         = HI_KERNEL_EPON_DEFAULT_OAM_VER;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_TEKNOVUS_E].auc_oui[0] = HI_EPON_TEKNOVUS_OUI_1ST;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_TEKNOVUS_E].auc_oui[1] = HI_EPON_TEKNOVUS_OUI_2ND;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_TEKNOVUS_E].auc_oui[2] = HI_EPON_TEKNOVUS_OUI_3RD;

    /* CTC---support */
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_CTC_E].uc_isenable     = HI_ENABLE;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_CTC_E].uc_version      = HI_KERNEL_EPON_DEFAULT_OAM_VER;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_CTC_E].auc_oui[0] = HI_EPON_CTC_OUI_1ST;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_CTC_E].auc_oui[1] = HI_EPON_CTC_OUI_2ND;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_CTC_E].auc_oui[2] = HI_EPON_CTC_OUI_3RD;

    /* DASAN---not support */
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_DASAN_E].uc_isenable     = HI_DISABLE;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_DASAN_E].uc_version      = HI_KERNEL_EPON_DEFAULT_OAM_VER;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_DASAN_E].auc_oui[0] = HI_EPON_DASAN_OUI_1ST;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_DASAN_E].auc_oui[1] = HI_EPON_DASAN_OUI_2ND;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_DASAN_E].auc_oui[2] = HI_EPON_DASAN_OUI_3RD;

    /* KT---not support */
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_KT_E].uc_isenable     = HI_DISABLE;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_KT_E].uc_version      = HI_KERNEL_EPON_DEFAULT_OAM_VER;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_KT_E].auc_oui[0] = HI_EPON_KT_OUI_1ST;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_KT_E].auc_oui[1] = HI_EPON_KT_OUI_2ND;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_KT_E].auc_oui[2] = HI_EPON_KT_OUI_3RD;

    /* PMC---not support */
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_PMC_E].uc_isenable     = HI_DISABLE;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_PMC_E].uc_version      = HI_KERNEL_EPON_DEFAULT_OAM_VER;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_PMC_E].auc_oui[0] = HI_EPON_PMC_OUI_1ST;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_PMC_E].auc_oui[1] = HI_EPON_PMC_OUI_2ND;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_PMC_E].auc_oui[2] = HI_EPON_PMC_OUI_3RD;

    /* HW---support */
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_HW_E].uc_isenable     = HI_ENABLE;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_HW_E].uc_version      = HI_KERNEL_EPON_DEFAULT_OAM_VER;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_HW_E].auc_oui[0] = HI_EPON_HW_OUI_1ST;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_HW_E].auc_oui[1] = HI_EPON_HW_OUI_2ND;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_HW_E].auc_oui[2] = HI_EPON_HW_OUI_3RD;

    /* VSOL---support */
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_VSOL_E].uc_isenable     = HI_ENABLE;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_VSOL_E].uc_version      = HI_KERNEL_EPON_DEFAULT_OAM_VER;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_VSOL_E].auc_oui[0] = HI_EPON_VSOL_OUI_1ST;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_VSOL_E].auc_oui[1] = HI_EPON_VSOL_OUI_2ND;
    g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_VSOL_E].auc_oui[2] = HI_EPON_VSOL_OUI_3RD;

    /* initial OUI to be CTC */
    g_st_ont_sw_capability.uc_ownerexoam = HI_EXOAM_TYPE_CTC_E;
    hi_kernel_stdoam_drvapi_setoamoui(g_st_ont_sw_capability.ast_oamcapability[g_st_ont_sw_capability.uc_ownerexoam].auc_oui);

    if (HI_NULL == g_s_oamtask) {
        g_s_oamtask = (struct task_struct *)kthread_run(hi_kernel_oam_queue_proc, HI_NULL, "ctcoam_queue_proc");
        if (HI_NULL == g_s_oamtask) {
            hi_kernel_ctcoam_event_exit();
        }
    }

    hi_epon_core_regsta_hook(hi_kernel_mpcp_state_move_callback);
    hi_epon_core_disinfo_hook(hi_kernel_mpcp_discov_info_callback);
    //hi_kernel_pmu_epon_ps_alarm_reg(hi_kernel_oam_eps_alarm);

    return;
}

/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_event_exit
 Description  : do something when event process exit
 Input        : hi_void
 Output       : None
*****************************************************************************/
hi_void hi_kernel_ctcoam_event_exit(hi_void)
{
    /*initial global para*/
    memset(g_ast_extoam_global_data, 0, sizeof(g_ast_extoam_global_data));
    memset(&g_st_ont_sw_capability, 0, sizeof(g_st_ont_sw_capability));

    /*deregister callback function */
    hi_kernel_stdoam_callback_register(HI_KERNEL_STDOAM_TYPE_RX_EXTOAM_E, HI_NULL);
    hi_kernel_stdoam_callback_register(HI_KERNEL_STDOAM_TYPE_NOTISFY_DISCOVERY_E, HI_NULL);
    hi_epon_core_regsta_hook(HI_NULL);
    hi_epon_core_disinfo_hook(HI_NULL);
    //hi_kernel_pmu_epon_ps_alarm_dereg();
    if (HI_NULL != g_s_oamtask) {
        kthread_stop(g_s_oamtask);
        g_s_oamtask = HI_NULL;
    }

    if (HI_NULL != g_pst_oamqueue) {
        hi_kernel_oam_queue_free(&g_pst_oamqueue);
    }

    return;
}

/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_callback_rx_msg
 Description  : callback function of receive extoam message
 Input        : hi_uint32 ui_llidindex
                hi_void *pv_msg
                hi_uint32 ui_msgsize
                hi_uint32 ui_msgtype
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_kernel_ctcoam_callback_rx_msg(hi_uint32 ui_llidindex, hi_void *pv_msg, hi_uint32 ui_msgsize, hi_uint32 ui_msgtype)
{
    if ((HI_MAX_LLID_NUM <= ui_llidindex) || (HI_NULL == pv_msg)
        || (HI_KERNEL_EPON_EXTOAM_MIN_DATA > ui_msgsize)
        || (HI_KERNEL_CTC_MSG_EXTOAM_E < ui_msgtype)) {
        return HI_RET_INVALID_PARA;
    }
    (hi_void)hi_kernel_ctcoam_event_proc(ui_msgtype, ui_llidindex, pv_msg, ui_msgsize);

    return HI_RET_SUCC;

}

/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_callback_start_discovery
 Description  : callback function of start extoam discovery
 Input        : hi_uint32 ui_llidindex
                hi_void *pv_msg
                hi_uint32 ui_msgsize
                hi_uint32 ui_msgtype
 Output       : None
 Return Value : hi_uint32
*****************************************************************************/
hi_uint32 hi_kernel_ctcoam_callback_start_discovery(hi_uint32 ui_llidindex, hi_void *pv_msg,
        hi_uint32 ui_msgsize, hi_uint32 ui_msgtype)
{
    if ((HI_MAX_LLID_NUM <= ui_llidindex)
        || (HI_KERNEL_CTC_START_REGISTER_E != ui_msgtype)) {
        return HI_RET_INVALID_PARA;
    }

    /*initial llid register data*/
    hi_kernel_ctcoam_init_llid_reg((hi_ushort16)ui_llidindex);

    return hi_kernel_ctcoam_reg_new_llid_proc(ui_llidindex, HI_KERNEL_EPON_CTCOAM_START_REG);

}


/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_callback_set_reg_state
 Description  : callback function of set register state
 Input        : hi_uint32 ui_llidindex
                hi_uchar8 uc_state
 Output       : None
*****************************************************************************/
hi_uint32 hi_kernel_ctcoam_callback_set_reg_state(hi_uint32 ui_llidindex, hi_uchar8 uc_state)
{
    hi_uint32 ui_ret;

    if ((HI_MAX_LLID_NUM <= ui_llidindex) || (HI_KERNEL_CTCOAM_STATE_END_E <= uc_state)) {
        return HI_RET_INVALID_PARA;
    }

    ui_ret = hi_kernel_ctcoam_state_move((hi_ushort16)ui_llidindex, uc_state);
    if (HI_RET_SUCC != ui_ret) {
        hi_kernel_ctcoam_dbg_err_print("LLID(%d) Move to O%u State Fail(%d)", ui_llidindex, uc_state + 1, ui_ret);
    }

    return HI_RET_SUCC;
}

/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_event_proc
 Description  :
 Input        : hi_uint32 ulEventId
                hi_uint32 ui_llidindex
                hi_void *pv_msg
                hi_uint32 ui_msglen
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_kernel_ctcoam_event_proc(hi_uint32 ulEventId, hi_uint32 ui_llidindex, hi_void *pv_msg, hi_uint32 ui_msglen)
{
    hi_uint32 ui_llidstate;
    //hi_uint32 ui_ret;
    hi_kernel_orgnization_info_tlv_s *pst_infotlv = (hi_kernel_orgnization_info_tlv_s *)pv_msg;
    hi_uchar8 uc_ouisupport;

    /*check input para over*/
    if (HI_NULL == pv_msg) {
        hi_kernel_ctcoam_dbg_err_print("\r\n[STDOAM]Null Pointer.");
        return HI_RET_NULLPTR;
    }

    ui_llidstate = HI_KERNEL_CTCOAM_GET_LLID_STATE(ui_llidindex);

    /*check if state of register and llid to be valid*/
    if ((HI_KERNEL_CTCOAM_STATE_END_E <= ui_llidstate)
        || (HI_MAX_LLID_NUM <= ui_llidindex)) {
        hi_kernel_ctcoam_dbg_err_print("\r\nInvalid LLID(%d) State(%d).",
                                       ui_llidindex, ui_llidstate);
        return HI_RET_INVALID_PARA;
    }

    hi_kernel_ctcoam_is_tkoam(pst_infotlv->auc_oui, &uc_ouisupport);
    if (HI_ENABLE == uc_ouisupport) {
        (hi_void)hi_kernel_ctcoam_send_any_state_keepalive(ui_llidindex, pv_msg, ui_msglen);
        return HI_RET_SUCC;
    }

    if (ulEventId == HI_KERNEL_CTC_MSG_EXTOAM_E) {
        /*
        [0xc5509f80] : 11 11 11 03 c7 00 a1 0c 00 00 00 00 00 11 11 01
        [0xc5509f90] : 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
        [0xc5509fa0] : 00 00 00 00 00 00 00 00 00 00
        */
        if ((*((hi_char8 *)pv_msg + 5) == (hi_char8)0x0) && (*((hi_char8 *)pv_msg + 6) == (hi_char8)0xa1)) {
            ui_llidstate = HI_KERNEL_CTCOAM_SEND_ANY_E;//让广播llid报文走到用户侧去处理。
        }
    }

    return g_apfn_ctcoam_proc_state[ui_llidstate](ui_llidindex,
            (hi_void *)pv_msg,
            ui_msglen,
            ulEventId);
}

/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_reg_new_llid_proc
 Description  : register new llid or reregister this llid
 Input        : hi_uint32 ui_llidindex
                hi_uint32 ui_start_or_reregister
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_kernel_ctcoam_reg_new_llid_proc(hi_uint32 ui_llidindex, hi_uint32 ui_start_or_reregister)
{
    hi_uint32 ui_ret;

    /* check if this llid to be right */
    if (ui_llidindex >= HI_MAX_LLID_NUM) {
        return HI_RET_INVALID_PARA;
    }

    if (HI_KERNEL_EPON_CTCOAM_START_REG == ui_start_or_reregister) {
        /* move to SEND_EXT_INFO state */
        ui_ret = hi_kernel_ctcoam_state_move((hi_ushort16)ui_llidindex, HI_KERNEL_CTCOAM_SEND_EXT_INFO_E);
        if (HI_RET_SUCC != ui_ret) {
            hi_kernel_ctcoam_dbg_err_print("\r\nmove to SEND_EXT_INFO fail(%d)", ui_ret);
            return ui_ret;
        }
    } else if (HI_KERNEL_EPON_CTCOAM_RE_REG == ui_start_or_reregister) {
        /*reregister this llid, then move to PASSIVE_WAIT state*/
        ui_ret = hi_kernel_ctcoam_state_move((hi_ushort16)ui_llidindex, HI_KERNEL_CTCOAM_PASSIVE_WAIT_E);
        if (HI_RET_SUCC != ui_ret) {
            hi_kernel_ctcoam_dbg_err_print("\r\nmove to PASSIVE_WAIT fail(%d)", ui_ret);
            return ui_ret;
        }
    } else {
        hi_kernel_ctcoam_dbg_err_print("\r\nInvalid opcode(%d)", ui_start_or_reregister);
        return HI_RET_INVALID_PARA;
    }

    return HI_RET_SUCC;
}

/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_reg_proc_passive_wait_state
 Description  : process function in passive_wait state
 Input        : hi_uint32 ui_llidindex
                const hi_void *pv_msg
                hi_uint32 ui_msglen
                hi_uint32 ui_msgtype
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_kernel_ctcoam_reg_proc_passive_wait_state(hi_uint32 ui_llidindex, const hi_void *pv_msg,
        hi_uint32 ui_msglen, hi_uint32 ui_msgtype)
{
    (hi_void)pv_msg;
    return HI_RET_FAIL;
}

/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_reg_proc_send_ext_info_state
 Description  : process function in send_ext_info state
 Input        : hi_uint32 ui_llidindex
                const hi_void *pv_msg
                hi_uint32 ui_msglen
                hi_uint32 ui_msgtype
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_kernel_ctcoam_reg_proc_send_ext_info_state(hi_uint32 ui_llidindex, const hi_void *pv_msg,
        hi_uint32 ui_msglen, hi_uint32 ui_msgtype)
{
    hi_uint32 ui_ret;

    /* 参数检查,其他参数合法性由上层保证 */

    /* 只处理Information TLV */
    switch (ui_msgtype) {
        case HI_KERNEL_CTC_MSG_INFO_E:

            ui_ret = hi_kernel_ctcoam_send_ext_info_state_info_proc(ui_llidindex, pv_msg, ui_msglen);
            if (HI_RET_SUCC != ui_ret) {
                hi_kernel_ctcoam_dbg_err_print("\r\nProccess ExtOAM_INFO fail(%d)", ui_ret);
                return ui_ret;
            }
            break;

        default:
            return HI_RET_FAIL;
    }

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_reg_proc_ack_state
 Description  : process function in ack state
 Input        : hi_uint32 ui_llidindex
                const hi_void *pv_msg
                hi_uint32 ui_msglen
                hi_uint32 ui_msgtype
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_kernel_ctcoam_reg_proc_ack_state(hi_uint32 ui_llidindex, const hi_void *pv_msg,
        hi_uint32 ui_msglen, hi_uint32 ui_msgtype)
{
    hi_uint32 ui_ret;
    /* 参数检查,其他参数合法性由上层保证 */

    /* 只处理Information TLV */
    switch (ui_msgtype) {
        case HI_KERNEL_CTC_MSG_INFO_E:

            ui_ret = hi_kernel_ctcoam_ack_state_info_proc(ui_llidindex, pv_msg, ui_msglen);
            if (HI_RET_SUCC != ui_ret) {
                hi_kernel_ctcoam_dbg_err_print("\r\nProccess ExtOAM_INFO fail(%d)", ui_ret);
                return ui_ret;
            }
            break;

        default:
            return HI_RET_FAIL;
    }

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_reg_proc_send_any_state
 Description  : process function in send any state
 Input        : hi_uint32 ui_llidindex
                const hi_void *pv_msg
                hi_uint32 ui_msglen
                hi_uint32 ui_msgtype
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_kernel_ctcoam_reg_proc_send_any_state(hi_uint32 ui_llidindex, const hi_void *pv_msg,
        hi_uint32 ui_msglen, hi_uint32 ui_msgtype)
{
    hi_uint32 ui_ret;
    /* 参数检查,其他参数合法性由上层保证 */
    /* 只处理Organization Specific TLV */
    switch (ui_msgtype) {
        case HI_KERNEL_CTC_MSG_EXTOAM_E:

            ui_ret = hi_kernel_ctcoam_send_any_state_ex_oam_proc(ui_llidindex, pv_msg, ui_msglen);
            if (HI_RET_SUCC != ui_ret) {
                hi_kernel_ctcoam_dbg_err_print(" Proccess ExtOAM_INFO fail(%d)", ui_ret);
                return ui_ret;
            }
            break;

        case HI_KERNEL_CTC_MSG_INFO_E:

            ui_ret = hi_kernel_ctcoam_send_any_state_keepalive(ui_llidindex, pv_msg, ui_msglen);
            if (HI_RET_SUCC != ui_ret) {
                hi_kernel_ctcoam_dbg_err_print(" Proccess ORGNIZATION_INFO fail(%d)", ui_ret);
                return ui_ret;
            }
            break;

        default:
            return HI_RET_FAIL;
    }

    return HI_RET_SUCC;
}

/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_state_notify
 Description  : 扩展OAM注册中,状态变迁时向应用层报告状态
 Input        : hi_ushort16 us_llidindex
                hi_uchar8 uc_newstate
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_kernel_ctcoam_state_notify(hi_ushort16 us_llidindex, hi_uchar8 uc_newstate)
{
    hi_uint32 ui_ret = HI_RET_SUCC;
    hi_kernel_oam_report_s *pst_reportmsg = HI_NULL;
    hi_oam_status_reports_s *pst_status;

    pst_reportmsg = (hi_kernel_oam_report_s *)kmalloc(sizeof(hi_kernel_oam_report_s), GFP_ATOMIC);
    if (HI_NULL == pst_reportmsg) {
        return HI_RET_FAIL;
    }

    pst_reportmsg->ui_msgtype = HI_OAM_OAMREG_STATE_E;
    pst_reportmsg->ui_len = HI_KERNEL_OAM_REPORT_LEN;
    pst_status = (hi_oam_status_reports_s *)pst_reportmsg->uc_content;
    pst_status->uc_llid = (hi_uchar8)us_llidindex;
    pst_status->uc_state = uc_newstate;

    ui_ret = hi_kernel_oam_queue_send(g_pst_oamqueue, (hi_void *)pst_reportmsg, sizeof(hi_kernel_oam_report_s));
    if (ui_ret != HI_RET_SUCC) {
        hi_kernel_ctcoam_dbg_err_print("\r\n OAM send queue fail(%d).", ui_ret);
    }

    kfree(pst_reportmsg);
    return ui_ret;
}

hi_uint32 hi_kernel_ctcoam_mpcpstate_notify(hi_ushort16 us_llidindex, hi_uchar8 uc_newstate)
{
    hi_uint32 ui_ret = HI_RET_SUCC;
    hi_kernel_oam_report_s *pst_reportmsg = HI_NULL;
    hi_oam_status_reports_s *pst_status;

    pst_reportmsg = (hi_kernel_oam_report_s *)kmalloc(sizeof(hi_kernel_oam_report_s), GFP_ATOMIC);
    if (HI_NULL == pst_reportmsg) {
        return HI_RET_FAIL;
    }

    pst_reportmsg->ui_msgtype = HI_OAM_MPCPREG_STATE_E;
    pst_reportmsg->ui_len = HI_KERNEL_OAM_REPORT_LEN;
    pst_status = (hi_oam_status_reports_s *)pst_reportmsg->uc_content;
    pst_status->uc_llid = (hi_uchar8)us_llidindex;
    pst_status->uc_state = uc_newstate;

    ui_ret = hi_kernel_oam_queue_send(g_pst_oamqueue, (hi_void *)pst_reportmsg, sizeof(hi_kernel_oam_report_s));
    if (ui_ret != HI_RET_SUCC) {
        hi_kernel_ctcoam_dbg_err_print("\r\n OAM send queue fail(%d).", ui_ret);
    }

    kfree(pst_reportmsg);
    return ui_ret;
}

hi_uint32 hi_kernel_ctcoam_mpcp_disinfo_notify(hi_ushort16 us_llidindex, hi_ushort16 us_disinfo)
{
    hi_uint32 ui_ret = HI_RET_SUCC;
    hi_kernel_oam_report_s *pst_reportmsg = HI_NULL;
    hi_oam_disinfo_reports_s *pst_status;

    pst_reportmsg = (hi_kernel_oam_report_s *)kmalloc(sizeof(hi_kernel_oam_report_s), GFP_ATOMIC);
    if (HI_NULL == pst_reportmsg) {
        return HI_RET_FAIL;
    }

    pst_reportmsg->ui_msgtype = HI_OAM_MPCPREG_DISINFO_E;
    pst_reportmsg->ui_len = HI_KERNEL_OAM_REPORT_LEN;
    pst_status = (hi_oam_disinfo_reports_s *)pst_reportmsg->uc_content;
    pst_status->uc_llid = (hi_uchar8)us_llidindex;
    pst_status->us_disinfo = us_disinfo;

    ui_ret = hi_kernel_oam_queue_send(g_pst_oamqueue, (hi_void *)pst_reportmsg, sizeof(hi_kernel_oam_report_s));
    if (ui_ret != HI_RET_SUCC) {
        hi_kernel_ctcoam_dbg_err_print("\r\n OAM send queue fail(%d).", ui_ret);
    }

    kfree(pst_reportmsg);
    return ui_ret;
}

/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_state_move
 Description  : 扩展OAM注册中,状态变迁
 Input        : hi_ushort16 us_llidindex
                hi_uchar8 uc_newstate
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_kernel_ctcoam_state_move(hi_ushort16 us_llidindex, hi_uchar8 uc_newstate)
{
    /* 参数检查 */
    if ((us_llidindex >= HI_MAX_LLID_NUM)
        || ((HI_KERNEL_CTCOAM_PASSIVE_WAIT_E != uc_newstate)
            && (HI_KERNEL_CTCOAM_SEND_EXT_INFO_E != uc_newstate)
            && (HI_KERNEL_CTCOAM_SEND_OAM_EXT_INFO_ACK_E != uc_newstate)
            && (HI_KERNEL_CTCOAM_SEND_ANY_E != uc_newstate))) {
        return HI_RET_INVALID_PARA;
    }

    if (uc_newstate != g_ast_extoam_global_data[us_llidindex].uc_llidstate) {
        printk("\r\n[CTCOAM]LLID(%u) O%u>>>O%u", us_llidindex,
               g_ast_extoam_global_data[us_llidindex].uc_llidstate + 1, uc_newstate + 1);
        /* 修改相应全局变量 */
        g_ast_extoam_global_data[us_llidindex].uc_llidstate = uc_newstate;
        (hi_void)hi_kernel_ctcoam_state_notify(us_llidindex, uc_newstate);
    }

    if (HI_KERNEL_CTCOAM_PASSIVE_WAIT_E == uc_newstate) {
        hi_kernel_ctcoam_init_llid_reg(us_llidindex);
    }

    return HI_RET_SUCC;
}

/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_init_llid_reg
 Description  : 初始化LLID的注册
 Input        : hi_uint32 ui_llidindex
 Output       : None
 Return Value :
*****************************************************************************/
hi_void hi_kernel_ctcoam_init_llid_reg(hi_uint32 ui_llidindex)
{
    /* 参数合法性由上层保证 */

    g_ast_extoam_global_data[ui_llidindex].uc_negotiatever = 0;
    g_ast_extoam_global_data[ui_llidindex].ui_lastkeyindex = HI_PON_MAX_ULONG;
    return;
}

/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_is_tkoam
 Description  : 检查是否是TKOAM
 Input        : hi_uchar8 auc_oui[HI_EPON_OUI_LEN]
                hi_uchar8 *puc_ctcoam_mark
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_kernel_ctcoam_is_tkoam(hi_uchar8 *puc_oui, hi_uchar8 *puc_tkoam_mark)
{
    hi_uint32 ui_cmp;

    /* 参数检查 */
    if ((HI_NULL == puc_oui)
        || (HI_NULL == puc_tkoam_mark)) {
        hi_kernel_ctcoam_dbg_err_print("\r\nNull Ptr.");
        return HI_RET_NULLPTR;
    }

    /* 默认为不支持 */
    *puc_tkoam_mark = HI_DISABLE;

    /* 检查TK OUI是否支持 */
    ui_cmp = memcmp(puc_oui, (g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_TEKNOVUS_E].auc_oui),
                    HI_EPON_OUI_LEN * sizeof(hi_uchar8));
    if (0 == ui_cmp) {
        *puc_tkoam_mark = HI_ENABLE;
        (hi_void)hi_kernel_stdoam_drvapi_setoamoui(puc_oui);
    }

    return HI_RET_SUCC;
}

/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_is_ctcoam
 Description  : chech over if to be ctcoam
 Input        : hi_uchar8 auc_oui[EPONDRV_OUI_LEN]
                hi_uchar8 *puc_ctcoam_mark
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_kernel_ctcoam_is_ctcoam(hi_uchar8 *puc_oui, hi_uchar8 *puc_ctcoam_mark)
{
    hi_uint32 ui_cmp;

    /* 参数检查 */
    if ((HI_NULL == puc_oui)
        || (HI_NULL == puc_ctcoam_mark)) {
        hi_kernel_ctcoam_dbg_err_print("\r\nNull Ptr.");
        return HI_RET_NULLPTR;
    }

    /* 默认为不支持 */
    *puc_ctcoam_mark = HI_DISABLE;

    /* 检查CTC OUI是否支持 */
    ui_cmp = memcmp(puc_oui, (g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_CTC_E].auc_oui),
                    HI_EPON_OUI_LEN * sizeof(hi_uchar8));
    if (0 == ui_cmp) {
        *puc_ctcoam_mark = HI_ENABLE;
        (hi_void)hi_kernel_stdoam_drvapi_setoamoui(puc_oui);
    }

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_is_hwoam
 Description  : 检查是否是华为扩展OAM
 Input        : hi_uchar8 auc_oui[EPONDRV_OUI_LEN]
                hi_uchar8 *puc_ctcoam_mark
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_kernel_ctcoam_is_hwoam(hi_uchar8 *puc_oui, hi_uchar8 *puc_hwoam_mark)
{
    hi_uint32 ui_cmp;

    /* 参数检查 */
    if ((HI_NULL == puc_oui)
        || (HI_NULL == puc_hwoam_mark)) {
        hi_kernel_ctcoam_dbg_err_print("\r\nNull Ptr.");
        return HI_RET_NULLPTR;
    }

    /* 默认为不支持 */
    *puc_hwoam_mark = HI_DISABLE;

    /* 检查CTC OUI是否支持 */
    ui_cmp = memcmp(puc_oui, (g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_HW_E].auc_oui),
                    HI_EPON_OUI_LEN * sizeof(hi_uchar8));
    if (0 == ui_cmp) {
        *puc_hwoam_mark = HI_ENABLE;
        (hi_void)hi_kernel_stdoam_drvapi_setoamoui(puc_oui);
    }

    return HI_RET_SUCC;
}

/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_is_vsoloam
 Description  : 检查是否是VSOL扩展OAM
 Input        : hi_uchar8 auc_oui[EPONDRV_OUI_LEN]
                hi_uchar8 *puc_ctcoam_mark
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_kernel_ctcoam_is_vsoloam(hi_uchar8 *puc_oui, hi_uchar8 *puc_vsoloam_mark)
{
    hi_uint32 ui_cmp;

    /* 参数检查 */
    if ((HI_NULL == puc_oui)
        || (HI_NULL == puc_vsoloam_mark)) {
        hi_kernel_ctcoam_dbg_err_print("\r\nNull Ptr.");
        return HI_RET_NULLPTR;
    }

    /* 默认为不支持 */
    *puc_vsoloam_mark = HI_DISABLE;

    /* 检查CTC OUI是否支持 */
    ui_cmp = memcmp(puc_oui, (g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_VSOL_E].auc_oui),
                    HI_EPON_OUI_LEN * sizeof(hi_uchar8));
    if (0 == ui_cmp) {
        *puc_vsoloam_mark = HI_ENABLE;
        (hi_void)hi_kernel_stdoam_drvapi_setoamoui(puc_oui);
    }

    return HI_RET_SUCC;
}

/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_chk_oui_support
 Description  : check over this OUI to be supported
 Input        : hi_uchar8 auc_oui[EPONDRV_OUI_LEN]
                hi_uchar8 *puc_support_mark
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_kernel_ctcoam_chk_oui_support(hi_uchar8 *puc_oui, hi_uchar8 *puc_support_mark)
{
    hi_uint32 ui_ret;
    hi_uchar8 uc_ctcoam_mark;

    /* 参数检查 */
    if ((HI_NULL == puc_oui)
        || (HI_NULL == puc_support_mark)) {
        hi_kernel_ctcoam_dbg_err_print("\r\nNull Ptr.");
        return HI_RET_NULLPTR;

    }

    /* 检查CTC OUI是否支持 */
    ui_ret = hi_kernel_ctcoam_is_ctcoam(puc_oui, &uc_ctcoam_mark);
    if (HI_RET_SUCC != ui_ret) {
        hi_kernel_ctcoam_dbg_err_print("\r\nCheck over oamtype fail(%d).", ui_ret);
        return ui_ret;
    }

    if ((HI_ENABLE != uc_ctcoam_mark)
        || (HI_ENABLE != g_st_ont_sw_capability.ast_oamcapability[HI_EXOAM_TYPE_CTC_E].uc_isenable)) {
        *puc_support_mark = HI_DISABLE;
    } else {
        *puc_support_mark = HI_ENABLE;
    }

    return HI_RET_SUCC;
}

/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_chk_oui_ver_support
 Description  : check over this version to be support
 Input        : hi_uchar8 uc_ouiver
                hi_uchar8 *puc_support_mark
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_kernel_ctcoam_chk_oui_ver_support(hi_uchar8 uc_ouiver, hi_uchar8 *puc_support_mark)
{
    hi_uint32 ui_loop = 0;

    /* 参数检查 */
    if (HI_NULL == puc_support_mark) {
        hi_kernel_ctcoam_dbg_err_print("\r\nNull Ptr.");
        return HI_RET_NULLPTR;
    }

    /* 默认为不支持 */
    *puc_support_mark = HI_DISABLE;

    while ((ui_loop < HI_KERNEL_EPON_MAX_SUPPORT_CTC_VER_NUM)
           && (HI_KERNEL_EPON_INVALID_CTC_VER != g_st_ont_sw_capability.auc_ctcver[ui_loop])) {
        if (uc_ouiver == g_st_ont_sw_capability.auc_ctcver[ui_loop]) {
            *puc_support_mark = HI_ENABLE;
            break;
        }

        ui_loop++;
    }

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_oui_proc
 Description  : OUI process of this llid
 Input        : hi_uint32 ui_llidindex
                const hi_void *pv_msg
                hi_uint32 ui_msglen
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_kernel_ctcoam_oui_proc(hi_uint32 ui_llidindex, const hi_void *pv_msg, hi_uint32 ui_msglen)
{
    hi_uint32 ui_ret;
    hi_uchar8 uc_ouisupport;
    hi_uchar8 uc_ouiverSupport;
    hi_kernel_ctcoam_info_tlv_s *pst_infotlv = (hi_kernel_ctcoam_info_tlv_s *)pv_msg;

    /* 参数合法性由上层保证 */
    if (HI_NULL == pv_msg) {
        hi_kernel_ctcoam_dbg_err_print("\r\nNull Ptr.");
        return HI_RET_NULLPTR;
    }

    /* 检查OUI是否支持,结果放在uc_ouisupport中 */
    ui_ret = hi_kernel_ctcoam_chk_oui_support(pst_infotlv->auc_oui, &uc_ouisupport);
    if (HI_RET_SUCC != ui_ret) {
        hi_kernel_ctcoam_dbg_err_print("\r\nCheck OUI Fail(%d).", ui_ret);
        return ui_ret;
    }

    /* 如果该OUI不支持,则返回失败 */
    if (HI_DISABLE == uc_ouisupport) {
        hi_kernel_ctcoam_dbg_err_print("\r\nNot Support this OUI.");
        return HI_RET_NOTSUPPORT;
    }

    /* 检查局端设置的CTC版本是否支持 */
    if (HI_DISABLE == pst_infotlv->uc_extsupport) {
        hi_kernel_ctcoam_dbg_err_print("\r\nNot Support Olt OUI.");
        return HI_RET_NOTSUPPORT;
    }

    ui_ret = hi_kernel_ctcoam_chk_oui_ver_support(pst_infotlv->uc_negotiatever,
             &uc_ouiverSupport);
    if (HI_RET_SUCC != ui_ret) {
        hi_kernel_ctcoam_dbg_err_print("\r\nCheck over Version Fail(%d).", ui_ret);
        return HI_RET_NOTSUPPORT;
    }

    if (HI_DISABLE == uc_ouiverSupport) {
        hi_kernel_ctcoam_dbg_err_print("\r\nNot Support Olt Ver.");
        return HI_RET_NOTSUPPORT;
    }

    /* 将OLT下发的协商后的版本号设置到全局变量中 */
    g_ast_extoam_global_data[ui_llidindex].uc_negotiatever = pst_infotlv->uc_negotiatever;

    return HI_RET_SUCC;

}

/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_send_ext_info_state_info_proc
 Description  : SenExtInfo状态,Information TLV消息处理函数
 Input        : hi_uint32 ui_llidindex
                const hi_void *pv_msg
                hi_uint32 ui_msglen
 Output       : None
 Return Value :
 *****************************************************************************/
hi_uint32 hi_kernel_ctcoam_send_ext_info_state_info_proc(hi_uint32 ui_llidindex, const hi_void *pv_msg, hi_uint32 ui_msglen)
{
    hi_uint32 ui_ret;
    hi_uint32 ui_ctcver_num;
    hi_uint32 ui_ctcver_num_tmp;
    hi_uint32 ui_response_oamlen;
    hi_uchar8 uc_ouisupport;
    hi_kernel_ctcoam_info_tlv_s *pst_infotlv = (hi_kernel_ctcoam_info_tlv_s *)pv_msg;
    hi_kernel_ctcoam_info_tlv_s *pst_response_oam_tmp = HI_NULL;
    hi_kernel_ctcoam_ver_list_s *pst_ctcver = HI_NULL;
    hi_void *pv_response_oam = HI_NULL;

    /* 参数合法性由上层保证 */
    if (HI_NULL == pv_msg) {
        hi_kernel_ctcoam_dbg_err_print("\r\nNull Ptr.");
        return HI_RET_NULLPTR;
    }

    /* 检查OUI是否支持,结果放在uc_ouisupport中 */
    ui_ret = hi_kernel_ctcoam_chk_oui_support(pst_infotlv->auc_oui, &uc_ouisupport);
    if (HI_RET_SUCC != ui_ret) {
        return ui_ret;
    }

    /* 申请动态内存,填充回应消息
       回应消息包括 以太头 + Local_Info_TLV + Remote_Info_TLV
     + CTC扩展版本号 + 2个字节的Information TLV结束符 */
    /* 1.检查当前支持CTC版本号 */
    for (ui_ctcver_num = 0; ui_ctcver_num < HI_KERNEL_EPON_MAX_SUPPORT_CTC_VER_NUM; ui_ctcver_num++) {
        if (HI_KERNEL_EPON_INVALID_CTC_VER == g_st_ont_sw_capability.auc_ctcver[ui_ctcver_num]) {
            break;
        }
    }

    /* 2.计算支持的CTC版本数 */
    ui_response_oamlen = sizeof(hi_epon_oam_s) + 2 * HI_KERNEL_EPON_INFO_TLV_LEN
                         + sizeof(hi_kernel_ctcoam_info_tlv_s) + ui_ctcver_num * sizeof(hi_kernel_ctcoam_ver_list_s)
                         + 2 * sizeof(hi_uchar8);
    if (HI_EPON_OAM_MIN_LEN > ui_response_oamlen) {
        ui_response_oamlen = HI_EPON_OAM_MIN_LEN;
    }

    /* 3.申请动态内存 */
    pv_response_oam = (hi_void *)kmalloc(ui_response_oamlen, GFP_ATOMIC);
    if (HI_NULL == pv_response_oam) {
        hi_kernel_ctcoam_dbg_err_print("\r\nMalloc Fail.");
        return HI_RET_MALLOC_FAIL;
    }

    memset(pv_response_oam, 0, ui_response_oamlen);

    /* 只填充CTC Information TLV部分 */
    pst_response_oam_tmp = (hi_kernel_ctcoam_info_tlv_s *)((hi_void *)(((hi_uchar8 *)pv_response_oam)
                           + sizeof(hi_epon_oam_s) + 2 * HI_KERNEL_EPON_INFO_TLV_LEN));

    pst_response_oam_tmp->uc_infotype = HI_KERNEL_INFO_TYPE_ORGANIZATION_INFO_E;
    pst_response_oam_tmp->uc_infolen  = (hi_uchar8)(sizeof(hi_kernel_ctcoam_info_tlv_s)
                                        + ui_ctcver_num * sizeof(hi_kernel_ctcoam_ver_list_s));
    pst_response_oam_tmp->auc_oui[0]  = HI_EPON_CTC_OUI_1ST;
    pst_response_oam_tmp->auc_oui[1]  = HI_EPON_CTC_OUI_2ND;
    pst_response_oam_tmp->auc_oui[2]  = HI_EPON_CTC_OUI_3RD;
    pst_response_oam_tmp->uc_extsupport = uc_ouisupport;
    pst_response_oam_tmp->uc_negotiatever
        = g_ast_extoam_global_data[ui_llidindex].uc_negotiatever;

    pst_ctcver = (hi_kernel_ctcoam_ver_list_s *)((hi_void *)(pst_response_oam_tmp + 1));

    for (ui_ctcver_num_tmp = 0; ui_ctcver_num_tmp < ui_ctcver_num; ui_ctcver_num_tmp++) {
        pst_ctcver->auc_oui[0]  = HI_EPON_CTC_OUI_1ST;
        pst_ctcver->auc_oui[1]  = HI_EPON_CTC_OUI_2ND;
        pst_ctcver->auc_oui[2]  = HI_EPON_CTC_OUI_3RD;
        pst_ctcver->uc_ver      = g_st_ont_sw_capability.auc_ctcver[ui_ctcver_num_tmp];
        pst_ctcver++;
    }

    /* 填写Information TLV结束符(只有2字节) */
    pst_response_oam_tmp = (hi_kernel_ctcoam_info_tlv_s *)pst_ctcver;
    pst_response_oam_tmp->uc_infotype = HI_KERNEL_INFO_TYPE_END_MAKER_E;
    pst_response_oam_tmp->uc_infolen  = 0;

    /* 发送INFO TLV 消息 */
    ui_ret = hi_kernel_stdoam_drvapi_txmsgproc(ui_llidindex, pv_response_oam, ui_response_oamlen, HI_OAM_CODE_INFO_E);
    if (HI_RET_SUCC != ui_ret) {
        hi_kernel_ctcoam_dbg_err_print("\r\nTx Info_TLV Fail(%d).", ui_ret);
        kfree(pv_response_oam);
        pv_response_oam = HI_NULL;
        return ui_ret;
    }

    kfree(pv_response_oam);
    pv_response_oam = HI_NULL;

    /* 迁到E_CTCOAM_SEND_OAM_EXT_INFO_ACK状态 */
    ui_ret = hi_kernel_ctcoam_state_move((hi_ushort16)ui_llidindex, HI_KERNEL_CTCOAM_SEND_OAM_EXT_INFO_ACK_E);
    if (HI_RET_SUCC != ui_ret) {
        hi_kernel_ctcoam_dbg_err_print("\r\nMove to SEND_OAM_EXT_INFO_ACK state(%d).", ui_ret);
        return ui_ret;
    }

    return HI_RET_SUCC;
}

/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_ack_state_info_proc
 Description  : AckState状态下,Information TLV处理函数
 Input        : hi_uint32 ui_llidindex
                const hi_void *pv_msg
                hi_uint32 ui_msglen
 Output       : None
*****************************************************************************/
hi_uint32 hi_kernel_ctcoam_ack_state_info_proc(hi_uint32 ui_llidindex, const hi_void *pv_msg, hi_uint32 ui_msglen)
{
    hi_uint32 ui_ret;
    hi_uint32 ui_support;
    hi_uint32 ui_response_oamlen;
    hi_kernel_ctcoam_info_tlv_s *pst_response_oam_tmp = HI_NULL;
    hi_void *pv_response_oam = HI_NULL;

    /* 参数合法性由上层保证 */
    if (HI_NULL == pv_msg) {
        hi_kernel_ctcoam_dbg_err_print("\r\nNull Ptr.");
        return HI_RET_NULLPTR;
    }

    ui_ret = hi_kernel_ctcoam_oui_proc(ui_llidindex, pv_msg, ui_msglen);
    if (((hi_uint32)HI_RET_NOTSUPPORT != ui_ret)
        && (HI_RET_SUCC != ui_ret)) {
        hi_kernel_ctcoam_dbg_err_print("\r\nOUI Process Fail(%d).", ui_ret);
        return ui_ret;
    }

    /* 保存支持与否的标志,以及迁移的目标状态 */
    ui_support = (((hi_uint32)HI_RET_NOTSUPPORT == ui_ret) ? HI_DISABLE : HI_ENABLE);

    /* 申请动态内存,拷贝对应版本号 */
    /* 回应消息包括 以太头 + Local_Info_TLV + Remote_Info_TLV
     + CTC扩展版本号 + 2个字节的Information TLV结束符 */
    ui_response_oamlen = sizeof(hi_epon_oam_s) + 2 * HI_KERNEL_EPON_INFO_TLV_LEN
                         + sizeof(hi_kernel_ctcoam_info_tlv_s) + 2 * sizeof(hi_uchar8);
    if (HI_EPON_OAM_MIN_LEN > ui_response_oamlen) {
        ui_response_oamlen = HI_EPON_OAM_MIN_LEN;
    }
    pv_response_oam = (hi_void *)kmalloc(ui_response_oamlen, GFP_ATOMIC);
    if (HI_NULL == pv_response_oam) {
        hi_kernel_ctcoam_dbg_err_print("\r\nMalloc Fail.");
        return HI_RET_MALLOC_FAIL;
    }

    /* 填充动态内存 */
    pst_response_oam_tmp = (hi_kernel_ctcoam_info_tlv_s *)((hi_void *)(((hi_uchar8 *)pv_response_oam)
                           + sizeof(hi_epon_oam_s) + 2 * HI_KERNEL_EPON_INFO_TLV_LEN));

    pst_response_oam_tmp->uc_infotype = HI_KERNEL_INFO_TYPE_ORGANIZATION_INFO_E;
    pst_response_oam_tmp->uc_infolen  = sizeof(hi_kernel_ctcoam_info_tlv_s);
    pst_response_oam_tmp->auc_oui[0]  = HI_EPON_CTC_OUI_1ST;
    pst_response_oam_tmp->auc_oui[1]  = HI_EPON_CTC_OUI_2ND;
    pst_response_oam_tmp->auc_oui[2]  = HI_EPON_CTC_OUI_3RD;
    pst_response_oam_tmp->uc_extsupport = (hi_uchar8)ui_support;
    pst_response_oam_tmp->uc_negotiatever
        = g_ast_extoam_global_data[ui_llidindex].uc_negotiatever;

    /* 发送INFO TLV 消息 */
    ui_ret = hi_kernel_stdoam_drvapi_txmsgproc(ui_llidindex, pv_response_oam, ui_response_oamlen, HI_OAM_CODE_INFO_E);
    if (HI_RET_SUCC != ui_ret) {
        hi_kernel_ctcoam_dbg_err_print("\r\nTx Info_TLV Fail(%d).", ui_ret);
        kfree(pv_response_oam);
        pv_response_oam = HI_NULL;
        return ui_ret;
    }

    kfree(pv_response_oam);
    pv_response_oam = HI_NULL;

    /* 状态迁移 */
    if (HI_DISABLE == ui_support) {
        /* 注册失败,状态迁移到HI_KERNEL_CTCOAM_SEND_EXT_INFO_E */
        ui_ret = hi_kernel_ctcoam_state_move((hi_ushort16)ui_llidindex, HI_KERNEL_CTCOAM_SEND_EXT_INFO_E);
        if (HI_RET_SUCC != ui_ret) {
            hi_kernel_ctcoam_dbg_err_print("\r\nmove to PASSIVE_WAIT Fail(%d).", ui_ret);
            return ui_ret;
        }
    } else {
        /* 注册成功,状态迁移到E_CTCOAM_SEND_ANY */
        ui_ret = hi_kernel_ctcoam_state_move((hi_ushort16)ui_llidindex, HI_KERNEL_CTCOAM_SEND_ANY_E);
        if (HI_RET_SUCC != ui_ret) {
            hi_kernel_ctcoam_dbg_err_print("\r\nmove to SEND_ANY Fail(%d).", ui_ret);
            return ui_ret;
        }
        printk("\r\n[EPON]LLID(%u) Online!\r\n", ui_llidindex);
        /* 将localPdu改为E_LOCAL_PDU_ANY(CTC扩展OAM协议中要求进行这个操作,
           但我们代码在CTCOAM注册过程中不修改E_LOCAL_PDU_ANY) */
    }

    return HI_RET_SUCC;
}

/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_send_any_state_ex_oam_proc
 Description  : extoam message process in sendany state
 Input        : hi_uint32 ui_llidindex
                const hi_void *pv_msg
                hi_uint32 ui_msglen
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_kernel_ctcoam_send_any_state_ex_oam_proc(hi_uint32 ui_llidindex, const hi_void *pv_msg, hi_uint32 ui_msglen)
{
    hi_uint32 ui_ret;
    hi_uint32 ui_exoam_type;
    hi_uchar8 uc_ctcoam_mark;
    hi_uchar8 uc_hwoam_mark;
    hi_uchar8 uc_vsoloam_mark;
    hi_ctcoam_msg_head_s *pst_ctcoamhead = (hi_ctcoam_msg_head_s *)pv_msg;
    hi_ctcoam_msg_s *pst_msg = (hi_ctcoam_msg_s *)g_st_ctcoam_report_msg.uc_content;

    /* 参数合法性由上层保证 */
    if ((HI_NULL == pv_msg)
        || (ui_msglen <= sizeof(hi_ctcoam_msg_head_s))) {
        hi_kernel_ctcoam_dbg_err_print("\r\nInvalid Para.");
        return HI_RET_INVALID_PARA;
    }

    /* 检查是否为CTC的EXOAM */
    ui_ret = hi_kernel_ctcoam_is_ctcoam(pst_ctcoamhead->auc_oui, &uc_ctcoam_mark);
    if (HI_RET_SUCC != ui_ret) {
        hi_kernel_ctcoam_dbg_err_print("\r\nCheck over OUI fail(%d).", ui_ret);
        return ui_ret;
    }

    /* 检查是否为华为的EXOAM */
    ui_ret = hi_kernel_ctcoam_is_hwoam(pst_ctcoamhead->auc_oui, &uc_hwoam_mark);
    if (HI_RET_SUCC != ui_ret) {
        hi_kernel_ctcoam_dbg_err_print("\r\nCheck over OUI fail(%d).", ui_ret);
        return ui_ret;
    }

    /* 检查是否为VSOL的EXOAM */
    ui_ret = hi_kernel_ctcoam_is_vsoloam(pst_ctcoamhead->auc_oui, &uc_vsoloam_mark);
    if (HI_RET_SUCC != ui_ret) {
        hi_kernel_ctcoam_dbg_err_print("\r\nCheck over OUI fail(%d).", ui_ret);
        return ui_ret;
    }
	
    /* 如果不是CTCOAM或TK,则忽略不处理 */
    if (HI_ENABLE == uc_ctcoam_mark) {
        ui_exoam_type = HI_EXOAM_TYPE_CTC_E;
    } else if (HI_ENABLE == uc_hwoam_mark) {
        ui_exoam_type = HI_EXOAM_TYPE_HW_E;
    } else if (HI_ENABLE == uc_vsoloam_mark) {
        ui_exoam_type = HI_EXOAM_TYPE_VSOL_E;
    } else {
        /* 其余OAM消息暂不支持,忽略之 */
        hi_kernel_ctcoam_dbg_err_print("\r\nCheck over OUI fail(%d).", ui_ret);
        return HI_RET_NOTSUPPORT;
    }

    g_st_ctcoam_report_msg.ui_msgtype = HI_OAM_ORGANIZE_SPECIFIC_E;
    g_st_ctcoam_report_msg.ui_len = HI_KERNEL_OAM_REPORT_LEN;
    pst_msg->ui_llidindex  = ui_llidindex;
    pst_msg->ui_msglen     = ui_msglen - sizeof(hi_ctcoam_msg_head_s);
    pst_msg->ui_exoam_type = ui_exoam_type;
    pst_msg->uc_oper       = pst_ctcoamhead->uc_opercode;

    memcpy(&(pst_msg->st_msg), (hi_void *)(pst_ctcoamhead + 1), pst_msg->ui_msglen);
    ui_ret = hi_kernel_oam_queue_send(g_pst_oamqueue, (hi_void *)&g_st_ctcoam_report_msg, sizeof(hi_kernel_oam_report_s));
    if (ui_ret != HI_RET_SUCC) {
        hi_kernel_ctcoam_dbg_err_print("\r\n OAM send queue fail(%d).", ui_ret);
    }
    return ui_ret;
}


/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_send_any_state_keepalive
 Description  : 扩展心跳帧处理
 Input        : hi_uint32 ui_llidindex,
                const hi_void *pv_msg,
                hi_uint32 ui_msglen
 Output       : None
 Return Value : hi_uint32
*****************************************************************************/
hi_uint32 hi_kernel_ctcoam_send_any_state_keepalive(hi_uint32 ui_llidindex, const hi_void *pv_msg, hi_uint32 ui_msglen)
{
    hi_uint32 ui_ret;
    hi_uint32 ui_response_oamlen;
    hi_uchar8 uc_ouisupport;
    hi_kernel_orgnization_info_tlv_s *pst_infotlv = (hi_kernel_orgnization_info_tlv_s *)pv_msg;
    hi_void *pv_response_oam = HI_NULL;

    /* 参数合法性由上层保证 */

    /* 检查是否为TK的EXOAM */
    ui_ret = hi_kernel_ctcoam_is_tkoam(pst_infotlv->auc_oui, &uc_ouisupport);
    if (HI_RET_SUCC != ui_ret) {
        hi_kernel_ctcoam_dbg_err_print("\r\nCheck over OUI fail(%d).", ui_ret);
        return ui_ret;
    }

    /* 申请动态内存,填充回应消息
       回应消息包括 以太头 + Local_Info_TLV + Remote_Info_TLV
     + Organization Specific Information TLV(0xFE+len+OUI+value)  */
    /* 1. 计算消息回复长度 */
    ui_response_oamlen = sizeof(hi_epon_oam_s) + 2 * HI_KERNEL_EPON_INFO_TLV_LEN; //modify by zhangli 11-08-08
    if (HI_EPON_OAM_MIN_LEN > ui_response_oamlen) {
        ui_response_oamlen = HI_EPON_OAM_MIN_LEN;
    }

    /* 1.申请动态内存 */
    pv_response_oam = (hi_void *)kmalloc(ui_response_oamlen, GFP_ATOMIC);
    if (HI_NULL == pv_response_oam) {
        hi_kernel_ctcoam_dbg_err_print("\r\nMalloc Fail.");
        return HI_RET_MALLOC_FAIL;
    }

    memset(pv_response_oam, 0, ui_response_oamlen);

    /* 2. 发送INFO TLV 消息 */
    ui_ret = hi_kernel_stdoam_drvapi_txmsgproc(ui_llidindex, pv_response_oam, ui_response_oamlen, HI_OAM_CODE_INFO_E);
    if (HI_RET_SUCC != ui_ret) {
        hi_kernel_ctcoam_dbg_err_print("\r\nTx Info_TLV Fail(%d).", ui_ret);
        kfree(pv_response_oam);
        pv_response_oam = HI_NULL;
        return ui_ret;
    }

    kfree(pv_response_oam);
    pv_response_oam = HI_NULL;

    return HI_RET_SUCC;

}

/*****************************************************************************
 Prototype    : hi_kernel_ctcoam_state_timeout
 Description  : 注册的中间状态超时
 Input        : hi_uint32 ui_data
 Output       : None
 Return Value :
*****************************************************************************/
hi_void hi_kernel_ctcoam_state_timeout(hi_uint32 ui_data)
{
    hi_uint32 ui_ret;
    hi_ushort16 us_llidindex;
    hi_uchar8 uc_state;

    uc_state = (hi_uchar8)(ui_data & HI_PON_BYTE_BITMASK);
    us_llidindex = (hi_ushort16)(ui_data >> HI_PON_BITS_B);

    /* 参数检查,已上线状态不会起定时器 */
    if ((us_llidindex >= HI_MAX_LLID_NUM)
        || (uc_state >= HI_KERNEL_CTCOAM_SEND_ANY_E)) {
        hi_kernel_ctcoam_dbg_err_print("\r\nInvalid Para.");
        return;
    }

    /* 如果状态已经变迁,则直接返回成功 */
    if (g_ast_extoam_global_data[us_llidindex].uc_llidstate != uc_state) {
        return;
    }
    /* 向g_ulEmacAlarmTaskID任务写事件,告知已收到Alarm消息 */
    ui_ret = hi_epon_core_write_stdoamregfailevn((hi_uchar8)us_llidindex);
    if (HI_RET_SUCC != ui_ret) {
        hi_kernel_ctcoam_dbg_err_print("\r\nWrite RegEvent Fail(%d).", ui_ret);
        return;
    }
    return;
}

hi_int32 hi_kernel_oam_queue_proc(hi_void *pv_data)
{
    hi_uint32 ui_ret;
    /*消息可用全局变量*/
    hi_kernel_oam_report_s *pst_msg;


    current->flags |= PF_NOFREEZE;
    set_user_nice(current, -20);

    pst_msg = (hi_kernel_oam_report_s *)kmalloc(sizeof(*pst_msg), GFP_ATOMIC);
    if (HI_NULL == pst_msg) {
        return (hi_int32)HI_RET_FAIL;
    }

    ui_ret = hi_kernel_oam_queue_init(&g_pst_oamqueue, HI_KERNEL_OAM_MAX_CNT);
    if (HI_RET_SUCC != ui_ret) {
        g_s_oamtask = HI_NULL;
        kfree(pst_msg);
        return ui_ret;
    }

    while (!kthread_should_stop()) {
        memset(pst_msg, 0, sizeof(hi_kernel_oam_report_s));
        /* synread msg queue,never timeout but interrupted by signal*/
        ui_ret = hi_kernel_oam_queue_recv(g_pst_oamqueue, pst_msg, sizeof(hi_kernel_oam_report_s));
        if (HI_RET_SUCC != ui_ret) {
            continue;
        }

        ui_ret = hi_netlink_senddata((hi_uint32)HI_SRCMODULE_CMS_OAM_K,
                                            pst_msg, sizeof(hi_kernel_oam_report_s) - HI_KERNEL_OAM_REPORT_LEN + pst_msg->ui_len);
        if (HI_RET_SUCC != ui_ret) {
            hi_kernel_ctcoam_dbg_err_print("\r\nNetlink Senddata Fail(%d).", ui_ret);
        }
    }

    /* clear msg queue*/
    hi_kernel_oam_queue_free(&g_pst_oamqueue);
    kfree(pst_msg);
    return HI_RET_SUCC;
}

hi_uint32 hi_kernel_oam_queue_send(hi_kernel_oam_queue_head_s *pst_queuehead, hi_void *pv_data, hi_uint32 ui_len)
{
    hi_kernel_oam_queue_items_s *pst_item = HI_NULL;
    hi_ulong32  ui_flag;

    if ((HI_NULL == pst_queuehead) || (HI_NULL == pv_data) || (0 == ui_len)) {
        return HI_RET_NULLPTR;
    }
    /* check if is full*/
    if (pst_queuehead->ui_cntcurr >= pst_queuehead->ui_cntmax) {
        pst_queuehead->ui_cntdrop++;
        printk("QUE IS FULL! CNT=%d\r\n", pst_queuehead->ui_cntcurr);
        return HI_RET_ITEM_FULL;
    }

    pst_item = (hi_kernel_oam_queue_items_s *)kmalloc(sizeof(hi_kernel_oam_queue_items_s) + ui_len, GFP_ATOMIC);
    if (HI_NULL == pst_item) {
        return HI_RET_MALLOC_FAIL;
    }
    pst_item->ui_len = ui_len;
    memcpy(pst_item->uc_text, pv_data, ui_len);

    spin_lock_irqsave(&pst_queuehead->st_spinlock, ui_flag);

    /* add msg to the tail of list */
    hi_list_add_tail(&pst_item->st_listhead, &pst_queuehead->st_listhead);

    /* current cnt of msg in queue add one*/
    pst_queuehead->ui_cntcurr++;

    spin_unlock_irqrestore(&pst_queuehead->st_spinlock, ui_flag);

    /* wake up waiting queue if they were previously empty*/
    if (1 == pst_queuehead->ui_cntcurr) {
        wake_up_interruptible(&pst_queuehead->st_waitqueuehead);
    }

    return HI_RET_SUCC;
}

hi_uint32 hi_kernel_oam_queue_recv(hi_kernel_oam_queue_head_s *pst_queuehead, hi_void *pv_data, hi_uint32 ui_maxlen)
{
    struct hi_list_head *pst_list    = HI_NULL;
    hi_kernel_oam_queue_items_s *pst_item = HI_NULL;

    if ((HI_NULL == pst_queuehead) || (HI_NULL == pv_data) || (0 == ui_maxlen)) {
        return HI_RET_INVALID_PARA;
    }

    /* thread will be sleep if queue is empty*/
    if (wait_event_interruptible(pst_queuehead->st_waitqueuehead,  kthread_should_stop() || pst_queuehead->ui_cntcurr)) {
        return HI_RET_SIGNAL;
    }

    spin_lock_irq(&pst_queuehead->st_spinlock);

    /* take out msg*/
    pst_list = hi_list_getnext(&pst_queuehead->st_listhead);
    if (HI_NULL == pst_list) {
        spin_unlock_irq(&pst_queuehead->st_spinlock);
        /* queue invalid if pionter of msg is null */
        return HI_RET_DEVEMPTY;
    }

    /* delete this msg from list*/
    hi_list_del(pst_list);

    /* current cnt of msg subtract one*/
    pst_queuehead->ui_cntcurr--;

    spin_unlock_irq(&pst_queuehead->st_spinlock);

    /* bring the head pointer of msg*/
    pst_item = hi_list_entry(pst_list, hi_kernel_oam_queue_items_s, st_listhead);  /*lint !e413*/
    if (HI_NULL == pst_item) {
        /* queue invalid if pionter of msg is null */
        return HI_RET_ITEM_EXCEPT;
    }

    /*copy data*/
    memcpy(pv_data, pst_item->uc_text, HI_MIN(ui_maxlen, pst_item->ui_len));
    kfree(pst_item);
    return HI_RET_SUCC;
}

hi_uint32 hi_kernel_oam_queue_init(hi_kernel_oam_queue_head_s **ppst_queuehead, hi_uint32 ui_maxlen)
{
    hi_kernel_oam_queue_head_s *pst_queue = HI_NULL;

    if (HI_NULL == ppst_queuehead) {
        return HI_RET_NULLPTR;
    }

    pst_queue = (hi_kernel_oam_queue_head_s *)kmalloc(sizeof(hi_kernel_oam_queue_head_s), GFP_ATOMIC);
    if (HI_NULL == pst_queue) {
        return HI_RET_MALLOC_FAIL;
    }

    hi_list_init_head(&pst_queue->st_listhead);
    init_waitqueue_head(&pst_queue->st_waitqueuehead); /*lint !e86*/
    spin_lock_init(&pst_queue->st_spinlock);

    pst_queue->ui_cntcurr = 0;
    pst_queue->ui_cntmax  = ui_maxlen;
    pst_queue->ui_cntdrop = 0;
    *ppst_queuehead = pst_queue;

    return HI_RET_SUCC;
}

hi_uint32 hi_kernel_oam_queue_free(hi_kernel_oam_queue_head_s **ppst_queuehead)
{
    struct hi_list_head *pst_msg_list    = HI_NULL;
    struct hi_list_head *pst_list_next   = HI_NULL;
    hi_kernel_oam_queue_items_s *pst_item  = HI_NULL;
    hi_kernel_oam_queue_head_s  *pst_queue = *ppst_queuehead;

    if (HI_NULL == pst_queue) {
        return HI_RET_NULLPTR;
    }
    pst_queue->ui_cntmax  = 0;
    pst_queue->ui_cntdrop = 0;
    pst_queue->ui_cntcurr = 0;

    /* free all msg in queue*/
    hi_list_for_each_safe(pst_msg_list, pst_list_next, &pst_queue->st_listhead) {
        pst_item = hi_list_entry(pst_msg_list, hi_kernel_oam_queue_items_s, st_listhead); /*lint !e413*/
        if (HI_NULL != pst_item) {
            hi_list_del(pst_msg_list);
            kfree(pst_item);
        }
    }

    kfree(pst_queue);

    *ppst_queuehead = HI_NULL;
    return HI_RET_SUCC;
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
