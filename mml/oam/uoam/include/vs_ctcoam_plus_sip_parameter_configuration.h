#ifndef __VS_CTCOAM_PLUS_SIP_PARAMETER_CONFIGURATION_H__
#define __VS_CTCOAM_PLUS_SIP_PARAMETER_CONFIGURATION_H__


#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "hi_uspace.h"
#include "hi_timer.h"
#include "hi_sysinfo.h"
#include "hi_oam_common.h"
#include "hi_oam_api.h"
#include "hi_oam_adapter.h"
#include "hi_ctcoam_proc.h"
#include "hi_ctcoam_tree.h"
#include "hi_ctcoam_manage.h"
#include "hi_ctcoam_performance.h"
#include "hi_ctcoam_alarm.h"
#include "hi_ipc.h"
#include "hi_oam_logger.h"
#include "igdCmModulePub.h"
#include "vs_ctcoam_common_inc.h"
#include <math.h>


// Copy the following macro definition to the appropriate location for hi_ctcoam_tree.h
// #define VS_CTCOAM_LEAF_PLUS_SIP_PARAMETER_CONFIGURATION  0x000e


typedef struct {
    unsigned short sip_local_port;
    unsigned char  primary_sip_proxy_service_ip[64];
    unsigned short primary_sip_proxy_service_port;
    unsigned char  backup_sip_proxy_service_ip[64];
    unsigned short backup_sip_proxy_service_port;
    unsigned char  active_sip_proxy_service_ip[64];
    unsigned char  primary_sip_register_service_ip[64];
    unsigned short primary_sip_register_service_port;
    unsigned char  backup_sip_register_service_ip[64];
    unsigned short backup_sip_register_service_port;  
    unsigned char  primary_outbound_proxy_service_ip[64];
    unsigned short primary_outbound_proxy_service_port;
    unsigned int   sip_register_expire;
    unsigned char  heartbeat_switch;
    unsigned short heartbeat_cycle;
    unsigned short heartbeat_count;
    unsigned char  reserved[128];
} __attribute__((__packed__)) vs_ctcoam_plus_sip_parameter_configuration_s;


uint32_t vs_ctcoam_get_plus_sip_parameter_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg);
uint32_t vs_ctcoam_set_plus_sip_parameter_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg);


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

#endif //__VS_CTCOAM_PLUS_SIP_PARAMETER_CONFIGURATION_H__
