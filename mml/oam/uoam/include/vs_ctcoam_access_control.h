#ifndef __VS_CTCOAM_ACCESS_CONTROL_H__
#define __VS_CTCOAM_ACCESS_CONTROL_H__


#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "hi_uspace.h"
#include "hi_timer.h"
#include "hi_sysinfo.h"
#include "hi_oam_common.h"
#include "hi_oam_api.h"
#include "hi_oam_adapter.h"
#include "hi_ctcoam_proc.h"
#include "hi_ctcoam_tree.h"
#include "hi_ctcoam_manage.h"
#include "hi_ctcoam_performance.h"
#include "hi_ctcoam_alarm.h"
#include "hi_ipc.h"
#include "hi_oam_logger.h"
#include "igdCmModulePub.h"
#include "vs_ctcoam_common_inc.h"
#include <math.h>


// Copy the following macro definition to the appropriate location for hi_ctcoam_tree.h
// #define VS_CTCOAM_LEAF_ACCESS_CONTROL  0x2028


typedef struct {
    unsigned char  telnet_control;//0-disable, 1-enable
    unsigned char  telnet_lan;//0-disable, 1-enable
    unsigned char  telnet_wan;//0-disable, 1-enable
    unsigned char  telnet_wan_ipv4_control;//0-disable, 1-enable
    unsigned char  telnet_wan_ipv4_address[4];
    unsigned char  telnet_wan_ipv4_mask[4];
    unsigned char  telnet_wan_ipv6_control;//0-disable, 1-enable
    unsigned char  telnet_wan_ipv6_address[16];
    unsigned char  telnet_wan_ipv6_mask[4];
    unsigned short telnet_port;
    unsigned char  ftp_control;//0-disable, 1-enable
    unsigned char  ftp_lan;//0-disable, 1-enable
    unsigned char  ftp_wan;//0-disable, 1-enable
    unsigned char  ftp_wan_ipv4_control;//0-disable, 1-enable
    unsigned char  ftp_wan_ipv4_address[4];
    unsigned char  ftp_wan_ipv4_mask[4];
    unsigned char  ftp_wan_ipv6_control;//0-disable, 1-enable
    unsigned char  ftp_wan_ipv6_address[16];
    unsigned char  ftp_wan_ipv6_mask[4];
    unsigned short ftp_port;   
    unsigned char  http_control;//0-disable, 1-enable
    unsigned char  http_lan;//0-disable, 1-enable
    unsigned char  http_wan;//0-disable, 1-enable
    unsigned char  http_wan_ipv4_control;//0-disable, 1-enable
    unsigned char  http_wan_ipv4_address[4];
    unsigned char  http_wan_ipv4_mask[4];
    unsigned char  http_wan_ipv6_control;//0-disable, 1-enable
    unsigned char  http_wan_ipv6_address[16];
    unsigned char  http_wan_ipv6_mask[4];
    unsigned short http_port;    
    unsigned char  https_control;//0-disable, 1-enable
    unsigned char  https_lan;//0-disable, 1-enable
    unsigned char  https_wan;//0-disable, 1-enable
    unsigned char  https_wan_ipv4_control;//0-disable, 1-enable
    unsigned char  https_wan_ipv4_address[4];
    unsigned char  https_wan_ipv4_mask[4];
    unsigned char  https_wan_ipv6_control;//0-disable, 1-enable
    unsigned char  https_wan_ipv6_address[16];
    unsigned char  https_wan_ipv6_mask[4];
    unsigned short https_port;    
    unsigned char  tftp_control;//0-disable, 1-enable
    unsigned char  tftp_lan;//0-disable, 1-enable
    unsigned char  tftp_wan;//0-disable, 1-enable
    unsigned char  tftp_wan_ipv4_control;//0-disable, 1-enable
    unsigned char  tftp_wan_ipv4_address[4];
    unsigned char  tftp_wan_ipv4_mask[4];
    unsigned char  tftp_wan_ipv6_control;//0-disable, 1-enable
    unsigned char  tftp_wan_ipv6_address[16];
    unsigned char  tftp_wan_ipv6_mask[4];
    unsigned short tftp_port;  
    unsigned char  ping_control;//0-disable, 1-enable
    unsigned char  ping_lan;//0-disable, 1-enable
    unsigned char  ping_wan;//0-disable, 1-enable
    unsigned char  ping_wan_ipv4_control;//0-disable, 1-enable
    unsigned char  ping_wan_ipv4_address[4];
    unsigned char  ping_wan_ipv4_mask[4];
    unsigned char  ping_wan_ipv6_control;//0-disable, 1-enable
    unsigned char  ping_wan_ipv6_address[16];
    unsigned char  ping_wan_ipv6_mask[4];
    unsigned char  reserved[2]; 
    unsigned char  ssh_control;//0-disable, 1-enable
    unsigned char  ssh_lan;//0-disable, 1-enable
    unsigned char  ssh_wan;//0-disable, 1-enable
    unsigned char  ssh_wan_ipv4_control;//0-disable, 1-enable
    unsigned char  ssh_wan_ipv4_address[4];
    unsigned char  ssh_wan_ipv4_mask[4];
    unsigned char  ssh_wan_ipv6_control;//0-disable, 1-enable
    unsigned char  ssh_wan_ipv6_address[16];
    unsigned char  ssh_wan_ipv6_mask[4];
    unsigned short ssh_port;    
} __attribute__((__packed__)) vs_ctcoam_access_control_s;

typedef struct {
    unsigned char telnet;
    unsigned char ftp;
    unsigned char http;
    unsigned char https;
    unsigned char tftp;
    unsigned char ping;
    unsigned char ssh;
}vs_ctcoam_access_control_state_s;

uint32_t vs_ctcoam_get_access_control(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg);
uint32_t vs_ctcoam_set_access_control(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg);


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

#endif //__VS_CTCOAM_ACCESS_CONTROL_H__
