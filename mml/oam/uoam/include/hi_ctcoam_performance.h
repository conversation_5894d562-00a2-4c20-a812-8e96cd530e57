/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  File Name     : hi_ctcoam_performance.h
  Version       : Initial Draft
  Created       : 2013/9/30
  Last Modified :
  Description   : ctcoam performance header
  Function List :
******************************************************************************/
#ifndef __HI_CTCOAM_PERFORMANCE_H__
#define __HI_CTCOAM_PERFORMANCE_H__

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

typedef struct {
    hi_uint32 ui_port;
    hi_uint32 ui_data;
} hi_ctcoam_additional_data_s;

#define HI_HTONL64(x) ((hi_ulong64)(((hi_ulong64)(x) >> 56) \
                               | ((hi_ulong64)(x) << 56)\
                               | (((hi_ulong64)(x) >> 40)  & 0x000000000000ff00 ) \
                               | (((hi_ulong64)(x) << 40 ) & 0x00ff000000000000 )\
                               | (((hi_ulong64)(x) >> 24)  & 0x0000000000ff0000 )\
                               | (((hi_ulong64)(x) << 24)  & 0x0000ff0000000000 )\
                               | (((hi_ulong64)(x) >> 8  )  & 0x00000000ff000000 )\
                               | (((hi_ulong64)(x) << 8  )  & 0x000000ff00000000 )))
extern hi_void hi_ctcoam_performance_monitoring_thread_proc(hi_uint32 ui_port);
extern hi_uint32 hi_ctcoam_get_cur_acc_statistics_data(hi_uint32 ui_port, hi_oam_monitoring_data_s *pst_data);
extern hi_uint32 hi_ctcoam_get_statistics_realtime_data(hi_uint32 ui_port, hi_oam_monitoring_data_s *pst_data);
extern hi_uint32 hi_ctcoam_get_statistics_data_for_alarm(hi_uint32 ui_port, hi_oam_monitoring_data_s *pst_data);
extern hi_void hi_ctcoam_stat_data_to_report_data(hi_oam_monitoring_data_s *pst_src_data,
        hi_ctcoam_monitoring_report_data_s *pst_dst_data, hi_ushort16 us_leaf);
extern hi_uint32 hi_ctcoam_performance_dump(hi_uint32 em_port);
#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_CTCOAM_PERFORMANCE_H__ */
