#ifndef __VS_CTCOAM_WAN_CONFIGURATION_H__
#define __VS_CTCOAM_WAN_CONFIGURATION_H__

#ifdef __cplusplus
#if __cplusplus
extern C
{
#endif
#endif //__cplusplus

#include "hi_uspace.h"
#include "hi_timer.h"
#include "hi_sysinfo.h"
#include "hi_oam_common.h"
#include "hi_oam_api.h"
#include "hi_oam_adapter.h"
#include "hi_ctcoam_proc.h"
#include "hi_ctcoam_tree.h"
#include "hi_ctcoam_manage.h"
#include "hi_ctcoam_performance.h"
#include "hi_ctcoam_alarm.h"
#include "hi_ipc.h"
#include "hi_oam_logger.h"
#include "igdCmModulePub.h"
#include "vs_ctcoam_common_inc.h"
#include <math.h>

// Copy the following macro definition to the appropriate location for hi_ctcoam_tree.h
// #define VS_CTCOAM_LEAF_WAN_CONFIGURATION  0x22ff

extern hi_uchar8 wan_config_running;

typedef struct
{
    unsigned short  wan_index;
    unsigned char   binding_lan;
    unsigned char   binding_ssid;
    unsigned short  wan_service_mode;//0-internet, 1-multicast, 2-tr069, 3-tr069_internet, 4-tr069_voip, 5-voip_internet, 6-tr069_voip_internet, 7-voip, 100-other
    unsigned short  wan_type;//0-bridge, 1-route
    unsigned short  wan_vlan_id;
    unsigned short  wan_cos_8021p;
    unsigned short  wan_ip_protocol;
    unsigned char   wan_NAT_enable;//0-disable, 1-enable
    unsigned short  wan_connection_mode;//0-dhcp, 1-static, 2-pppoe
    unsigned char   wan_ipv4_address[4];//ipv4 ip
    unsigned int    wan_ipv4_mask;//ipv4 ip mask
    unsigned char   wan_ipv4_default_gateway[4];
    unsigned char   wan_ipv4_request_dns_mode;//0-auto, 1-static
    unsigned char   wan_ipv4_master_dns[4];
    unsigned char   wan_ipv4_slave_dns[4];
    unsigned char   wan_ipv6_address[16];//ipv6 ip
    unsigned int    wan_ipv6_mask;//ipv6 ip mask
    unsigned char   wan_ipv6_default_gateway[16];
    unsigned char   wan_ipv6_request_dns_mode;//0-auto, 1-static
    unsigned char   wan_ipv6_master_dns[16];
    unsigned char   wan_ipv6_slave_dns[16];
    unsigned char   wan_pppoe_proxy_enable;//0-disable, 1-enable
    unsigned char   wan_pppoe_username[32];
    unsigned char   wan_pppoe_password[32];
    unsigned char   wan_pppoe_servicename[32];
    unsigned short  wan_pppoe_mode;//0-auto connect, 1-connect when have payload
    unsigned char   wan_ipv6_address_mode;//0-dhcp, 1-slacc
    unsigned char   wan_dhcpv6_client_request_address_enable;//0-disable, 1-enable
    unsigned char   wan_dhcpv6_client_request_prefix_enable;//0-disable, 1-enable
    unsigned char   wan_DS_Lite_enable;//0-disable, 1-enable
    unsigned char   wan_DS_Lite_AFTR_mode;//0-dhcpv6, 1-manual
    unsigned char   wan_DS_Lite_address_type;//0-ipv6, 1-dns
    unsigned char   wan_DS_Lite_address[128];
    unsigned char   wan_6rd_enable;//0-disable, 1-enable
    unsigned char   wan_6rd_router_address[4];
    unsigned int    wan_6rd_ipv4_mask_len;
    unsigned char   wan_6rd_ipv6_prefix[16];
    unsigned int    wan_6rd_ipv6_prefix_len;
    unsigned char   wan_qos_enable;//0-disable, 1-enable
    unsigned short  vlan_mode;//1-tag, 2-transparent
    unsigned char   translation_enable;//0-disable, 1-enable
    unsigned short  translation_vlan;
    unsigned short  translation_cos;
    unsigned char   QinQ_enable;//0-disable, 1-enable
    unsigned short  TPID;
    unsigned short  wan_svlan_id;
    unsigned short  wan_svlan_cos;
    unsigned char   wan_name[64];
    unsigned short  wan_state;
    unsigned char   wan_mac[6];
    unsigned char   binding_ssid2;
    unsigned short  MTU;
    unsigned char   reserved[29];
} __attribute__((__packed__)) vs_ctcoam_wan_dual_stack_configuration_s;

uint32_t vs_ctcoam_set_wan_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s * pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

#endif //__VS_CTCOAM_WAN_CONFIGURATION_H__
