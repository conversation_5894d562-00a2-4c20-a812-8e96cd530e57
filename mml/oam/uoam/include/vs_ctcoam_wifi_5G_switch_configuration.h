#ifndef __VS_CTCOAM_WIFI_5G_SWITCH_CONFIGURATION_H__
#define __VS_CTCOAM_WIFI_5G_SWITCH_CONFIGURATION_H__


#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "hi_uspace.h"
#include "hi_timer.h"
#include "hi_sysinfo.h"
#include "hi_oam_common.h"
#include "hi_oam_api.h"
#include "hi_oam_adapter.h"
#include "hi_ctcoam_proc.h"
#include "hi_ctcoam_tree.h"
#include "hi_ctcoam_manage.h"
#include "hi_ctcoam_performance.h"
#include "hi_ctcoam_alarm.h"
#include "hi_ipc.h"
#include "hi_oam_logger.h"
#include "igdCmModulePub.h"
#include "vs_ctcoam_common_inc.h"
#include <math.h>


// Copy the following macro definition to the appropriate location for hi_ctcoam_tree.h
// #define VS_CTCOAM_LEAF_WIFI_5G_SWITCH_CONFIGURATION  0x2208


typedef struct {
    unsigned char  enable;//0-disable, 1-enable
    unsigned short country;
    unsigned short channel;
    unsigned short standard;
    unsigned short transmission_power;
    unsigned short channel_width;
    unsigned char  mesh_enable;//0-disable, 1-enable
    unsigned char  reserved[29];
} __attribute__((__packed__)) vs_ctcoam_wifi_5G_switch_configuration_s;


uint32_t vs_ctcoam_get_wifi_5G_switch_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg);
uint32_t vs_ctcoam_set_wifi_5G_switch_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg);


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

#endif //__VS_CTCOAM_WIFI_5G_SWITCH_CONFIGURATION_H__
