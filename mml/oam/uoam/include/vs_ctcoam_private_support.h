#ifndef __VS_CTCOAM_PRIVATE_SUPPORT_H__
#define __VS_CTCOAM_PRIVATE_SUPPORT_H__


#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus

#include "hi_uspace.h"
#include "hi_timer.h"
#include "hi_sysinfo.h"
#include "hi_oam_common.h"
#include "hi_oam_api.h"
#include "hi_oam_adapter.h"
#include "hi_ctcoam_proc.h"
#include "hi_ctcoam_tree.h"
#include "hi_ctcoam_manage.h"
#include "hi_ctcoam_performance.h"
#include "hi_ctcoam_alarm.h"
#include "hi_ipc.h"
#include "hi_oam_logger.h"
#include "igdCmModulePub.h"
#include "vs_ctcoam_common_inc.h"
#include "bcrypt.h"
#include <math.h>

// Copy the following macro definition to the appropriate location for hi_ctcoam_tree.h
// #define VS_CTCOAM_LEAF_PRIVATE_SUPPORT  0x220b


typedef struct {
    hi_uchar8    ge_port_num;                   //GE port number
    hi_uchar8    fe_port_num;                   //FE port number
    hi_uchar8    pots_port_num;                 //POTS port number
    hi_uchar8    wifi_ssid_num;                 //wifi ssid number
    hi_uchar8    catv_num;                      //CATV number
    hi_ushort16  support_catv_management;       //supports CATV management by OLT
    hi_uchar8    support_private;               //support private
    hi_ushort16  support_wan;                   //support wan
    hi_ushort16  support_wifi;                  //support wifi
    hi_ushort16  support_dhcp;                  //support dhcp
    hi_ushort16  support_security;              //support security
    hi_ushort16  support_voip;                  //support voip
    hi_ushort16  support_port;                  //support port
    hi_ushort16  support_control;               //support control
    hi_ushort16  support_rstp;                  //support rstp
    hi_ushort16  support_application;           //support application
    hi_ushort16  support_tr069;                 //support tr069
    hi_ushort16  support_onu_self_diagnosis;    //support onu self diagnosis
    hi_ushort16  support_system_time;           //support system time
    hi_uchar8    reserve[26];                   //reserve
} __attribute__((__packed__)) vs_ctcoam_private_support_s;


uint32_t vs_ctcoam_get_private_support(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg);


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

#endif //__VS_CTCOAM_PRIVATE_SUPPORT_H__
