#ifndef __VS_CTCOAM_COMMON_INC_H__
#define __VS_CTCOAM_COMMON_INC_H__


#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#include "vs_ctcoam_lan_dhcpv4_server.h"
#include "vs_ctcoam_lan_dhcpv6_server.h"
#include "vs_ctcoam_private_support.h"
#include "vs_ctcoam_firewall_level.h"
#include "vs_ctcoam_user_and_password.h"
#include "vs_ctcoam_access_control.h"
#include "vs_ctcoam_tr069_global_management.h"
#include "vs_ctcoam_tr069_stun_management.h"
#include "vs_ctcoam_wan_configuration.h"
#include "vs_ctcoam_wan_dual_stack.h"
#include "vs_ctcoam_wifi_2_4G_switch_configuration.h"
#include "vs_ctcoam_wifi_5G_switch_configuration.h"
#include "vs_ctcoam_wifi_ssid_configuration.h"
#include "vs_ctcoam_restore_factory.h"
#include "vs_ctcoam_fax_modem_configuration.h"
#include "vs_ctcoam_voip_wan_global_configuration.h"
#include "vs_ctcoam_ge_port_type.h"
#include "vs_ctcoam_sw_hw.h"
#include "vs_ctcoam_catv_configuration.h"
#include "vs_ctcoam_iad_information.h"
#include "vs_ctcoam_sip_parameter_configuration.h"
#include "vs_ctcoam_plus_sip_parameter_configuration.h"
#include "vs_ctcoam_sip_pots_status.h"
#include "vs_ctcoam_sip_user_parameter_configuration.h"
#include "vs_ctcoam_sip_digital_map.h"
#include "vs_ctcoam_mac_limit.h"
#include "vs_ctcoam_mac_info.h"
#include "vs_ctcoam_private_oam_define.h"
#include "vs_ctcoam_wan_nat_type.h"
//automatic tool identification marks, do not delete this line

typedef enum 
{
    VS_WAN_BRIDGE,
    VS_WAN_ROUTE
}VS_OAM_WAN_CONNECTION_TYPE;

typedef enum
{
    VS_WAN_CONNECTION_ADDRESSING_TYPE_DHCP,
    VS_WAN_CONNECTION_ADDRESSING_TYPE_STATIC,
    VS_WAN_CONNECTION_ADDRESSING_TYPE_PPPOE
}VS_OAM_WAN_CONNECTION_ADDRESSING_TYPE;

typedef enum
{
    VS_WAN_IPV4,
    VS_WAN_IPV6,
    VS_WAN_IPV4_AND_IPV6
}VS_OAM_WAN_IP_PROTOCOL;

typedef enum 
{
    VS_WAN_PPPOE_DIAL_MODE_ALWAYS_ON,
    VS_WAN_PPPOE_DIAL_MODE_ON_CONMAND
}VS_OAM_WAN_PPPOE_DIAL_MODE;

typedef enum
{
    VS_WAN_VLAN_MODE_DIABLE,
    VS_WAN_VLAN_MODE_TAG,
    VS_WAN_VLAN_MODE_TRANSPARENT
}VS_OAM_WAN_VLAN_MODE;

typedef enum
{
    VS_WAN_DNSV6_ATUO,
    VS_WAN_DNSV6_STATIC
}VS_OAM_WAN_DNSV6_MODE;

typedef enum
{
    VS_WAN_IPV6_ADDRESS_MODE_DHCP,
    VS_WAN_IPV6_ADDRESS_MODE_SLAAC
}VS_OAM_WAN_IPV6_ADDRESS_MODE;

typedef enum
{
    VS_WAN_DSLITE_AFTR_MODE_DHCPV6,
    VS_WAN_DSLITE_AFTR_MODE_MANUAL
}VS_OAM_WAN_DSLITE_AFTR_MODE;

typedef enum
{
    VS_WAN_SERVICE_INTERNET = 0,
    VS_WAN_SERVICE_TR069 = 2,
    VS_WAN_SERVICE_TR069_INTERNET = 3,
    VS_WAN_SERCICE_TR069_VOIP = 4,
    VS_WAN_SERVICE_INTERNET_VOIP = 5,
    VS_WAN_SERVICE_TR069_INTERNET_VOIP = 6,
    VS_WAN_SERVICE_VOIP = 7,
    VS_WAN_SERVICE_OTHER = 100
}VS_OAM_WAN_SERVICE_TYPE;

typedef enum
{
    REGION_ETSI = 0,
    REGION_FCC,
    REGION_IC,
    REGION_SPAIN,
    REGION_FRANCE,
    REGION_MKK,
    REGION_ISREAL,
    REGION_MKK2,
    REGION_MKK3,
    REGION_RUSSIAN,
    REGION_CN,
    REGION_GLOBAL,
    REGION_WORLD_WIDE,
    REGION_MKK1,
    REGION_NCC = 14
}VS_OAM_REGION;

typedef enum
{
    VS_OAM_2_4G_WIFI_CHANNEL_WIDTH_20M,
    VS_OAM_2_4G_WIFI_CHANNEL_WIDTH_40M,
    VS_OAM_2_4G_WIFI_CHANNEL_WIDTH_20M_40M=3,
}VS_OAM_2_4G_WIFI_CHANNEL_WIDTH;

typedef enum
{
    VS_OAM_5G_WIFI_CHANNEL_WIDTH_20M,
    VS_OAM_5G_WIFI_CHANNEL_WIDTH_40M,
    VS_OAM_5G_WIFI_CHANNEL_WIDTH_80M,
    VS_OAM_5G_WIFI_CHANNEL_WIDTH_20M_40M,
    VS_OAM_5G_WIFI_CHANNEL_WIDTH_20M_40M_80M,
    VS_OAM_5G_WIFI_CHANNEL_WIDTH_160M
}VS_OAM_5G_WIFI_CHANNEL_WIDTH;

typedef enum
{
    VS_OAM_2_4G_WIFI_STANDARD_B,
    VS_OAM_2_4G_WIFI_STANDARD_G,
    VS_OAM_2_4G_WIFI_STANDARD_BG,
    VS_OAM_2_4G_WIFI_STANDARD_N,
    VS_OAM_2_4G_WIFI_STANDARD_BGN,
    VS_OAM_2_4G_WIFI_STANDARD_AX,
    VS_OAM_2_4G_WIFI_STANDARD_BGN_AX,
    //OLT does not have the following definitions
    VS_OAM_2_4G_WIFI_STANDARD_AC,
    VS_OAM_2_4G_WIFI_STANDARD_AC_A,
    VS_OAM_2_4G_WIFI_STANDARD_AC_A_N,
    VS_OAM_2_4G_WIFI_STANDARD_AC_A_N_AC,
    VS_OAM_2_4G_WIFI_STANDARD_AC_AX,
    VS_OAM_2_4G_WIFI_STANDARD_AC_A_N_AC_AX,
    VS_OAM_2_4G_WIFI_STANDARD_GN
}VS_OAM_2_4G_WIFI_STANDARD;

typedef enum
{
    VS_OAM_5G_WIFI_STANDARD_B,
    VS_OAM_5G_WIFI_STANDARD_G,
    VS_OAM_5G_WIFI_STANDARD_BG,
    VS_OAM_5G_WIFI_STANDARD_N,
    VS_OAM_5G_WIFI_STANDARD_BGN,
    VS_OAM_5G_WIFI_STANDARD_AC,
    VS_OAM_5G_WIFI_STANDARD_AC_A,
    VS_OAM_5G_WIFI_STANDARD_AC_N,
    VS_OAM_5G_WIFI_STANDARD_AC_N_AC,
    VS_OAM_5G_WIFI_STANDARD_AC_A_N_AC = 9,
    VS_OAM_5G_WIFI_STANDARD_AC_A_N,
    VS_OAM_5G_WIFI_STANDARD_AC_AX,
    VS_OAM_5G_WIFI_STANDARD_AC_A_N_AC_AX,
    //OLT does not have the following definitions
    VS_OAM_5G_WIFI_STANDARD_AX,
    VS_OAM_5G_WIFI_STANDARD_BGN_AX,
    VS_OAM_5G_WIFI_STANDARD_GN
}VS_OAM_5G_WIFI_STANDARD;

typedef enum
{
    VS_WEB_ENCRYPTION_LEVEL_64 = 1,
    VS_WEB_ENCRYPTION_LEVEL_128 = 2
}VS_OAM_TAPI_WEB_ENCRYPTION_LEVEL;
typedef enum
{
    VS_AUTH_MODE_UNKNOWN,
    VS_AUTH_MODE_OPEN,
    VS_AUTH_MODE_SHARED,
    VS_AUTH_MODE_WEPAUTO,
    VS_AUTH_MODE_WPAPSK,
    VS_AUTH_MODE_WPA,
    VS_AUTH_MODE_WPA2PSK,
    VS_AUTH_MODE_WPA2,
    VS_AUTH_MODE_WPA_WPA2,
    VS_AUTH_MODE_WPAPSK_WPA2PSK,
    VS_AUTH_MODE_WPA3PSK,
    VS_AUTH_MODE_WPA2PSK_WPA3PSK 
}VS_OAM_WLAN_AUTH_MODE;

typedef enum
{
    VS_ENCRYPTION_TYPE_NONE = 1,
    VS_ENCRYPTION_TYPE_WEP,
    VS_ENCRYPTION_TYPE_TKIP,
    VS_ENCRYPTION_TYPE_AES,
    VS_ENCRYPTION_TYPE_TKIP_AES,
}VS_OAM_WLAN_ENCRYPTION_TYPE;

typedef enum
{
    VS_IP_TYPE_UNKNOWN,
    VS_IP_TYPE_IPV4,
    VS_IP_TYPE_IPV6,
    VS_IP_TYPE_IPV4Z,
    VS_IP_TYPE_IPV6Z,
    VS_IP_TYPE_DOMAIN = 16
}VS_OAM_TAPI_IP_TYPE;

int packet_slicing_for_send(void *input_data, unsigned int max_size, unsigned char branch, unsigned short leaf);
int packet_slicing_for_receive(void *save_date, unsigned int save_date_size, const void *msg, unsigned int max_size, unsigned char branch, unsigned short leaf);
int packet_slicing_for_receive_digital_map(void *save_data, const void *msg, unsigned int max_size, unsigned char branch, unsigned short leaf);
int calculate_packet_length(const void *msg, unsigned int max_size, unsigned char branch, unsigned short leaf);
int calculate_mask_bits(unsigned int u32Val);
void printf_oam_hex(const void *msg, size_t length);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif /* __VS_CTCOAM_COMMON_INC_H__ */
