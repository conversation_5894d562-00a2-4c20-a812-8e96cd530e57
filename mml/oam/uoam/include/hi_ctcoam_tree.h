/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  File Name     : hi_ctcoam_tree.h
  Version       : Initial Draft
  Created       : 2013/6/19
******************************************************************************/
#ifndef __HI_CTCOAM_TREE_H__
#define __HI_CTCOAM_TREE_H__

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_typedef.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/* pad填充值 */
#define HI_CTCOAM_PAD_VALUE       0x00

/* Branch定义 */
#define HI_CTCOAM_BRANCH_STD_ATTR    0x07 /* 标准属性 */
#define HI_CTCOAM_BRANCH_STD_OPER    0x09 /* 标准操作 */
#define HI_CTCOAM_BRANCH_EXT_ATTR    0xC7 /* 扩展属性 */
#define HI_CTCOAM_BRANCH_EXT_OPER    0xC9 /* 扩展操作 */
#define HI_CTCOAM_BRANCH_INSTANCE    0x36 /* 实例索引，2.0以下版本 */
#define HI_CTCOAM_BRANCH_INSTANCE_21 0x37 /* 实例索引，2.1以上版本 */
#define HI_CTCOAM_BRANCH_HW          0xe7 /* 华为扩展 */
#define HI_CTCOAM_BRANCH_VSOL_VOIP   0x02 /* vsol voip */
/* Leaf定义 */


/*******************************************************************************/
/* 标准属性 Leaf定义 */

/* 查询以太网端口的状态 */
#define HI_CTCOAM_LEAF_STD_PHY_ADMIN_STATE         0x0025

/* 查询以太网端口的状态（自协商） */
#define HI_CTCOAM_LEAF_STD_NEG_ADMIN_STATE         0x004F

/* 查询actual port capabilities 属性 */
#define HI_CTCOAM_LEAF_STD_NEG_TECH_ABILITY        0x0052

/* 端口自协商能力通告 */
#define HI_CTCOAM_LEAF_STD_NEG_ADVER_ABILITY       0x0053

/* TK私有同步以太网功能,发送时间戳 */
#define HI_TKOAM_LEAF_SEND_TIME_STAMP              0x00B6

/* TK私有同步以太网功能,发送RTT */
#define HI_TKOAM_LEAF_SEND_RTT                     0x00B7

/* TK私有同步以太网功能,打开关闭同步以太网功能 */
#define HI_TKOAM_LEAF_SYNC_ETH_SWITCH              0x00E3

/* TK私有同步以太网功能，TOD传送帧 */
#define HI_TKOAM_LEAF_SEND_TOD                    0x00C5

/* aFECAbility,FEC能力查询 */
#define HI_CTCOAM_LEAF_FEC_ABILITY                 0x0139

/* 查询和设置双向FEC功能的打开/关闭 */
#define HI_CTCOAM_LEAF_STD_FEC_MODE                0x013A


/* 标准操作 Leaf定义 */

/* 设置以太网端口的状态 */
#define HI_CTCOAM_LEAF_STD_ACT_PHY_ADMIN_CTRL     0x0005

/* 强制链路重新协商 */
#define HI_CTCOAM_LEAF_STD_ACT_NEG_RESTART_CONFIG 0x000B

/* 打开或者关闭PHY端口的自协商功能 */
#define HI_CTCOAM_LEAF_STD_ACT_NEG_ADMIN_CTRL     0x000C

/****************************************************************************/
/* 扩展属性 Leaf定义 */

/* ONU的序列号 */
#define HI_CTCOAM_LEAF_ONU_SN                0x0001

/* 查询芯片的固件版本 */
#define HI_CTCOAM_LEAF_ONU_FIRMWARE_VER      0x0002

/* 获取ONU所采用的PON芯片 */
#define HI_CTCOAM_LEAF_ONU_CHIPSET           0x0003

/* 描述ONU支持的主要功能 */
#define HI_CTCOAM_LEAF_ONU_CAPABILITIES_1    0x0004

/*光路接收诊断*/
#define HI_CTCOAM_LEAF_OPTICAL_DIAGNOSE      0x0005

/*服务SLA*/
#define HI_CTCOAM_LEAF_SERVICE_SLA           0x0006

/* 描述ONU支持的主要功能 */
#define HI_CTCOAM_LEAF_ONU_CAPABILITIES_2    0x0007

/* HoldoverConfig*/
#define HI_CTCOAM_LEAF_ONU_HOLDOVER_CONFIG    0x0008

/*Active PON_IFAdminstate*/
#define HI_CTCOAM_LEAF_ONU_PON_IF_ADMIN    0x000b

/* 描述ONU支持的主要功能 */
#define HI_CTCOAM_LEAF_ONU_CAPABILITIES_3    0x000c

#define HI_CTCOAM_LEAF_POWER_SAVING_CAPABILITIES  0x000d

#define HI_CTCOAM_LEAF_POWER_SAVING_CFG      0x000e

#define HI_CTCOAM_LEAF_CONF_CNT       0x000f

/* 查询以太网端口的链路运行状态 */
#define HI_CTCOAM_LEAF_ETH_LINK_STATE        0x0011

/* 查询与设置以太网端口的流控功能 */
#define HI_CTCOAM_LEAF_ETH_PAUSE             0x0012

/* 查询与设置以太网端口的上行限速功能 */
#define HI_CTCOAM_LEAF_ETHPORT_US_POLICING   0x0013

/* VoIP端口的管理（打开或者关闭） */
#define HI_CTCOAM_LEAF_VOIP_PORT_ACTIVE      0x0014

/* E1端口管理 */
#define HI_CTCOAM_LEAF_TDM_PORT_CFG          0x0015

/* 以太网端口的下行速率限制功能 */
#define HI_CTCOAM_LEAF_ETHPORT_DS_RATE_LIMIT 0x0016

/* 端口环路检测使能 */
#define HI_CTCOAM_LEAF_PORT_LOOP_DETECT      0x0017

/* 关闭出现环回的端口 */
#define HI_CTCOAM_LEAF_PORT_DISABLE_LOOP     0x0018

/* ONU的以太网端口双工速率状态查询 */
#define HI_CTCOAM_LEAF_PORT_MODE_STATUS      0x001e

/* 以太网端口的VLAN配置和查询 */
#define HI_CTCOAM_LEAF_VLAN_CFG              0x0021

/* ONU上特定以太网端口的上行业务流分类、映射和以太网优先级（IEEE 802.1D）标
   记规则查询和设置 */
#define HI_CTCOAM_LEAF_FLOW_CLASS_AND_MARK   0x0031

/* ONU的以太网端口的组播VLAN配置 */
#define HI_CTCOAM_LEAF_ADD_DEL_MULTI_VLAN    0x0041

/* ONU对下行Multicast数据报文的VLAN TAG处理的查询与设置 */
#define HI_CTCOAM_LEAF_ADD_MULTI_TAG_STRIPE  0x0042

/* 定义组播协议开关 */
#define HI_CTCOAM_LEAF_MULTICAST_SWITCH      0x0043

/* OLT利用MulticastControl OAMPDU对ONU的本地组播控制表进行动态管理 */
#define HI_CTCOAM_LEAF_MULTICAST_CONTROL     0x0044

/* ONU的以太网端口同时支持的组播组数量 */
#define HI_CTCOAM_LEAF_MULTI_GROUP_NUM_MAX   0x0045

/* 查询ONU的以太网端口的组播Fast Leave能力 */
#define  HI_CTCOAM_LEAF_FAST_LEAVE_ABILITY   0x0046

/* 查询ONU的以太网端口的组播Fast Leave状态 */
#define  HI_CTCOAM_LEAF_FAST_LEAVE_STATE     0x0047


/* ONU LLID 队列配置 */
#define  HI_CTCOAM_LEAF_LLID_QUEUE_CONFIG    0x0051

#define  HI_CTCOAM_LEAF_ALARM_ADMIN_STATE    0x0081

#define  HI_CTCOAM_LEAF_ALARM_THRESHOLD      0x0082


#define  HI_CTCOAM_LEAF_AUTH_PASSWORD        0x00a0

#define  HI_CTCOAM_LEAF_UNKNOW_2             0x0005

#define  HI_CTCOAM_LEAF_TX_POWER_CONTROL     0x00a1

/* 设置MAC老化时间*/
#define HI_CTCOAM_LEAF_PORT_MAC_AGING_TIME   0x00A4

#define HI_CTCOAM_LEAF_ONU_PROTECTION_PARAMETERS 0x00A6

#define  HI_CTCOAM_LEAF_UNKNOW_6             0x0100

/* ONU 性能统计状态和统计周期*/
#define  HI_CTCOAM_LEAF_STATISTICS_STATE     0x00b1

/* ONU 性能统计，返回当前周期数据或者重置统计*/
#define  HI_CTCOAM_LEAF_STATISTICS_CUR_DATA  0x00b2

/* ONU 性能统计，返回当前周期数据或者重置统计*/
#define HI_CTCOAM_LEAF_STATISTICS_PRE_DATA  0x00b3

/* plus sip parameter configuration */
#define VS_CTCOAM_LEAF_PLUS_SIP_PARAMETER_CONFIGURATION  0x000e

/* iad information */
#define VS_CTCOAM_LEAF_IAD_INFORMATION  0x0061

/* voip wan configuration */
#define VS_CTCOAM_LEAF_VOIP_WAN_GLOBAL_CONFIGURATION  0x0062

/* sip parameter configuration */
#define VS_CTCOAM_LEAF_SIP_PARAMETER_CONFIGURATION  0x0067

/* sip user parameter configuration */
#define VS_CTCOAM_LEAF_SIP_USER_PARAMETER_CONFIGURATION  0x0068

/* fax/modem configuration */
#define VS_CTCOAM_LEAF_FAX_MODEM_CONFIGURATION  0x0069

/* sip pots status */
#define VS_CTCOAM_LEAF_SIP_POTS_STATUS  0x006b

/* ONU 软件和硬件版本号 */
#define VS_CTCOAM_LEAF_SW_HW  0x2001

/* ONU Catv configuration */
#define VS_CTCOAM_LEAF_CATV_CONFIGURATION  0x2022

/* User name and admin password */
#define VS_CTCOAM_LEAF_USER_AND_PASSWORD  0x2026

/* Firewall level */
#define VS_CTCOAM_LEAF_FIREWALL_LEVEL  0x2027

/* Access control */
#define VS_CTCOAM_LEAF_ACCESS_CONTROL  0x2028

/* tr069 global management */
#define VS_CTCOAM_LEAF_TR069_GLOBAL_MANAGEMENT  0x2029

/* wan nat type*/
#define VS_CTCOAM_LEAF_WAN_NAT_TYPE  0x202b

/* ge port type and number */
#define VS_CTCOAM_LEAF_GE_PORT_TYPE  0x2033

/* tr069 stun management */
#define VS_CTCOAM_LEAF_TR069_STUN_MANAGEMENT  0x202a

/* port mac limit */
#define VS_CTCOAM_LEAF_MAC_LIMIT  0x2102

/* port mac address info */
#define VS_CTCOAM_LEAF_MAC_INFO  0x2106

/* WiFi-2.4G enable */
#define VS_CTCOAM_LEAF_WIFI_2_4G_SWITCH_CONFIGURATION  0x2201

/* WiFi SSID configuration */
#define VS_CTCOAM_LEAF_WIFI_SSID_CONFIGURATION  0x2202

/* restore factory */
#define VS_CTCOAM_LEAF_RESTORE_FACTORY  0x2204

/* LAN DHCPv4 configuration */
#define VS_CTCOAM_LEAF_LAN_DHCPV4_SERVER  0x2207

/* WiFi-5G enable */
#define VS_CTCOAM_LEAF_WIFI_5G_SWITCH_CONFIGURATION  0x2208

/* WAN ipv4/ipv6 configuration */
#define VS_CTCOAM_LEAF_WAN_DUAL_STACK  0x2209

/* LAN DHCPv6 configuration */
#define VS_CTCOAM_LEAF_LAN_DHCPV6_SERVER  0x220a

/* VSOL private support information */
#define VS_CTCOAM_LEAF_PRIVATE_SUPPORT  0x220b

/* WAN configuration compressed data */
#define VS_CTCOAM_LEAF_WAN_CONFIGURATION  0x22ff

#define VS_CTCOAM_LEAF_PRIVATE_OAM_DEFINE  0x2041

/* Optical Transceiver Information */
#define HI_CTCOAM_LEAF_OPT_TRANS_INFO       0x7003

/* ONU光模块的诊断参数越限告警和警示功能,
   OLT利用本属性查询和设置ONU光收发机的重要参数越限告警和警示功能（打开/关闭）*/
#define HI_CTCOAM_LEAF_OPT_TX_AlARM_ENABLE   0x0006

/*******************************************************************************/
/* 扩展操作 Leaf定义 */

/* 重启ONU */
#define HI_CTCOAM_LEAF_ACT_RESET_ONU             0x0001

/* onu进入睡眠模式 */
#define HI_CTCOAM_LEAF_ACT_SLEEP_ONU             0x0002

/* sip digital map */
#define VS_CTCOAM_LEAF_SIP_DIGITAL_MAP           0x006d

/* 多LLID控制*/
#define HI_CTCOAM_LEAF_MULTI_LLID_CTRL           0x0202

/* 设置或者更改ONU以太网端口的组播Fast Leave状态 */
#define HI_CTCOAM_LEAF_ACT_SET_FAST_LEAVE_CTRL   0x0048

/*华为扩展leaf定义*/
#define HI_CTCOAM_LEAF_HW_EXT_1                  0x0001

/* 非法TLV长度 */
#define HI_CTCOAM_INVALID_TLV                    0xFF

/* 变长消息长度 */
#define HI_CTCOAM_CHANGING_TLV_LEN 0xFF

#define HI_CTCOAM_STATISTICS_TLV_LEN 0x00

#define HI_CTCOAM_STATISTICS_MSG_LEN (128+132+44)

/* 没有Length和value的OAM消息 */
#define HI_CTCOAM_NO_LENTH_VALUE   0

#define HI_CTCOAM_SLEEP_CTRL_LEN   0x0a

/* Ctc oam实例 */
typedef struct {
    hi_ushort16 us_instancetype; /* 使用E_CTC_INSTANCE_PORT解析 */
    hi_uchar8 uc_length;
    hi_uchar8 uc_rsvd;

    union {
        hi_uchar8 uc_value;
        hi_uint32 ui_value;
        hi_void *pv_value; /* 当四字节的ULONG放不下时，使用pValue */
    } un_value;
} __attribute__((packed)) hi_ctcoam_instance_s;


/* CTC OAM消息处理钩子,处理具体分割后的OAM块 */
typedef hi_uint32(*hi_ctcoam_proc_pfn)(hi_uint32 ui_llidindex,
                                       hi_ctcoam_instance_s *pst_instance, const hi_void *pv_inmsg,
                                       hi_uchar8 *puc_changing_msg_len, hi_void *pv_outmsg);

/* CTC OAM树形结构,由于属于稀疏矩阵,参考R52的调试命令行实现 */
typedef struct {
    hi_uint32 ui_id; /* Branch或Leaf编号 */
    hi_uchar8 uc_tlvlen; /* 处理的CTC OAM的TLV长度,只针对Leaf有效.对于设置操作,
                            其表示输入的TLV值长度,Get操作表示获取到的TLV长度 */
    hi_uchar8 uc_needinstance;
    hi_ushort16 us_rsvd;
    hi_uint32 ui_exoam_type; /* 扩展OAM类型,使用EPONDRV_EXOAM_TYPE_ENUM解析 */
    hi_void   *pv_subhead; /* 下一级头结点数据,只针对Branch有效,使用HI_KERNEL_CTCOAM_HEAD_STRU解析 */
    hi_ctcoam_proc_pfn pfn_getproc; /* 对于Leaf的Get处理函数,如果不支持Get则填NULL */
    hi_ctcoam_proc_pfn pfn_setproc; /* 对于Leaf的Set处理函数,如果不支持Set则填NULL */
} __attribute__((packed)) hi_ctcoam_tree_node_s;


/* 本级Branch或Leaf的头指针 */
typedef struct {
    hi_uint32 ui_nodenum; /* 本级节点的个数 */
    hi_ctcoam_tree_node_s *pst_firstnode; /* 本级的头结点 */
} __attribute__((packed)) hi_ctcoam_head_s;

/*----------------------------------------------*
* routines' implementations                    *
*----------------------------------------------*/
extern hi_uint32 hi_ctcoam_init_oam_tree(hi_void);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_CTCOAM_TREE_H__ */
