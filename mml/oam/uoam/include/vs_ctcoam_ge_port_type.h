#ifndef __VS_CTCOAM_GE_PORT_TYPE_H__
#define __VS_CTCOAM_GE_PORT_TYPE_H__


#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "hi_uspace.h"
#include "hi_timer.h"
#include "hi_sysinfo.h"
#include "hi_oam_common.h"
#include "hi_oam_api.h"
#include "hi_oam_adapter.h"
#include "hi_ctcoam_proc.h"
#include "hi_ctcoam_tree.h"
#include "hi_ctcoam_manage.h"
#include "hi_ctcoam_performance.h"
#include "hi_ctcoam_alarm.h"
#include "hi_ipc.h"
#include "hi_oam_logger.h"
#include "igdCmModulePub.h"
#include "vs_ctcoam_common_inc.h"
#include <math.h>


// Copy the following macro definition to the appropriate location for hi_ctcoam_tree.h
// #define VS_CTCOAM_LEAF_GE_PORT_TYPE  0x2033


typedef struct {
	hi_uchar8    ge_1;                   	//GE port number
    hi_uchar8    ge_2_5;                   	//2.5GE port number
    hi_uchar8    ge_5;                   	//5GE port number
    hi_uchar8    ge_10;                   	//10GE port number
    hi_uchar8    ge_25;                  	//25GE port number
    hi_uchar8    ge_40;                   	//40GE port number
    hi_uchar8    reserve[10];                   //reserve
} __attribute__((__packed__)) vs_ctcoam_ge_port_type_s;


uint32_t vs_ctcoam_get_ge_port_type(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg);


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

#endif //__VS_CTCOAM_GE_PORT_TYPE_H__
