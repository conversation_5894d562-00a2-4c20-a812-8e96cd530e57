#ifndef __VS_CTCOAM_WIFI_SSID_CONFIGURATION_H__
#define __VS_CTCOAM_WIFI_SSID_CONFIGURATION_H__


#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "hi_uspace.h"
#include "hi_timer.h"
#include "hi_sysinfo.h"
#include "hi_oam_common.h"
#include "hi_oam_api.h"
#include "hi_oam_adapter.h"
#include "hi_ctcoam_proc.h"
#include "hi_ctcoam_tree.h"
#include "hi_ctcoam_manage.h"
#include "hi_ctcoam_performance.h"
#include "hi_ctcoam_alarm.h"
#include "hi_ipc.h"
#include "hi_oam_logger.h"
#include "igdCmModulePub.h"
#include "vs_ctcoam_common_inc.h"
#include <math.h>


// Copy the following macro definition to the appropriate location for hi_ctcoam_tree.h
// #define VS_CTCOAM_LEAF_WIFI_SSID_CONFIGURATION  0x2202


typedef struct {
    unsigned char  ssid_name[32];
    unsigned char  ssid_enable;
    unsigned char  ssid_hide_enable;
    unsigned short auth_mode;
    unsigned short encryption_type;
    unsigned char  preshared_key[64];
    unsigned int   wpa_update_key_interval;
    unsigned int   radius_server_ip_type;//0-unknown, 1-ipv4, 2-ipv6, 3-ipv4z, 4-ipv6z, 16-dns domain
    unsigned int   radius_server_ip_len;//ipv4=4, ipv6=16, ipv4z=8, ipv6z=20, dns domain=1~255
    unsigned char  radius_server_ip[255];
    unsigned int   radius_server_ip_prefix_len;//ipv4=0~32, ipv6=0~128, ipv4z=0~32, ipv6z=0~128, dns domain=1~255
    unsigned short radius_server_port;
    unsigned char  radius_server_key[32];
    unsigned short wep_encryption_level;
    unsigned short wep_key_index;
    unsigned char  wep_key1[32];
    unsigned char  wep_key2[32];
    unsigned char  wep_key3[32];
    unsigned char  wep_key4[32];
    unsigned char  reserved[128];
} __attribute__((__packed__)) vs_ctcoam_wifi_ssid_configuration_s;


uint32_t vs_ctcoam_get_wifi_ssid_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg);
uint32_t vs_ctcoam_set_wifi_ssid_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg);


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

#endif //__VS_CTCOAM_WIFI_SSID_CONFIGURATION_H__
