#ifndef __VS_CTCOAM_LAN_DHCPV4_SERVER_H__
#define __VS_CTCOAM_LAN_DHCPV4_SERVER_H__


#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus

#include "hi_uspace.h"
#include "hi_timer.h"
#include "hi_sysinfo.h"
#include "hi_oam_common.h"
#include "hi_oam_api.h"
#include "hi_oam_adapter.h"
#include "hi_ctcoam_proc.h"
#include "hi_ctcoam_tree.h"
#include "hi_ctcoam_manage.h"
#include "hi_ctcoam_performance.h"
#include "hi_ctcoam_alarm.h"
#include "hi_ipc.h"
#include "hi_oam_logger.h"
#include "igdCmModulePub.h"
#include "vs_ctcoam_common_inc.h"
#include <math.h>

// Copy the following macro definition to the appropriate location for hi_ctcoam_tree.h
// #define VS_CTCOAM_LEAF_LAN_DHCPV4_SERVER  0x2207


typedef struct {
    unsigned int  itemNum;	
    unsigned short  ipType;	
    unsigned char lanIPaddress[16];	
    unsigned char lanSubnetMask[4];	
    unsigned int leaseTime;	
    unsigned short  enableDhcpServer;	
    unsigned char dhcpPoolStart[16];	
    unsigned char dhcpPoolEnd[16];	
    unsigned short dhcpPoolType;	
    unsigned short reserved;	
    unsigned char dhcpPriDNS[16];	
    unsigned char dhcpSecDNS[16];	
    unsigned char dhcpGateway[16];	
    unsigned char dhcpServerIPaddress[16];
} __attribute__((__packed__)) vs_ctcoam_lan_dhcpv4_server_s;


uint32_t vs_ctcoam_get_lan_dhcpv4_server(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg);
uint32_t vs_ctcoam_set_lan_dhcpv4_server(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg);


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

#endif //__VS_CTCOAM_LAN_DHCPV4_SERVER_H__
