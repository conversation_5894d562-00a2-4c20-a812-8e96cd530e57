#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "vs_ctcoam_plus_sip_parameter_configuration.h"

int vs_oam_plus_sip_param_cfg_to_ram(vs_ctcoam_plus_sip_parameter_configuration_s *cfg)
{
    int ret = 0;
    IgdVoiceSipBasicAttrConfTab data;
    IgdVoiceAdvancedAttrConfTab data2;

    if (cfg == NULL)
        return HI_RET_INVALID_PARA;

    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));
    HI_OS_MEMSET_S(&data2, sizeof(data2), 0, sizeof(data2));

    data.ulBitmap  |= SIP_BASIC_ATTR_MASK_ALL;
    data.ulBitmap1 |= SIP_BASIC_ATTR_MASK1_ALL;
    ret = igdCmConfGet(IGD_VOICE_SIP_BASIC_ATTR_TAB, (unsigned char *)&data, sizeof(data));
    if(ret != 0)
    {
        printf("[%s,%d] igdCmConfGet IGD_VOICE_SIP_BASIC_ATTR_TAB ret : %d \r\n", __FUNCTION__, __LINE__, ret);
        return ret;
    }

    data2.ulBitmap  |= VOICE_ADV_ATTR_MASK_ALL;
    ret = igdCmConfGet(IGD_VOICE_ADVANCED_ATTR_TAB, (unsigned char *)&data2, sizeof(data2));
    if(ret != 0)
    {
        printf("[%s,%d] igdCmConfGet IGD_VOICE_ADVANCED_ATTR_TAB ret : %d \r\n", __FUNCTION__, __LINE__, ret);
        return ret;
    }

    cfg->sip_local_port = data2.ulSipLocalPort;

    HI_OS_MEMCPY_S(&cfg->primary_sip_proxy_service_ip, sizeof(cfg->primary_sip_proxy_service_ip), data.aucProxyServer, sizeof(cfg->primary_sip_proxy_service_ip));
    cfg->primary_sip_proxy_service_port = data.ulProxyServerPort;

    HI_OS_MEMCPY_S(&cfg->backup_sip_proxy_service_ip, sizeof(cfg->backup_sip_proxy_service_ip), data.aucStandbyProxyServer, sizeof(cfg->backup_sip_proxy_service_ip));
    cfg->backup_sip_proxy_service_port = data.ulStandbyProxyServerPort;

    HI_OS_MEMCPY_S(&cfg->primary_sip_register_service_ip, sizeof(cfg->primary_sip_register_service_ip), data.aucRegistrarServer, sizeof(cfg->primary_sip_register_service_ip));
    cfg->primary_sip_register_service_port = data.ulRegistrarServerPort;

    HI_OS_MEMCPY_S(&cfg->backup_sip_register_service_ip, sizeof(cfg->backup_sip_register_service_ip), data.aucStandbyRegistrarServer, sizeof(cfg->backup_sip_register_service_ip));
    cfg->backup_sip_register_service_port = data.ulStandbyRegistrarServerPort;

    HI_OS_MEMCPY_S(&cfg->primary_outbound_proxy_service_ip, sizeof(cfg->primary_outbound_proxy_service_ip), data.aucOutBoundProxyServer, sizeof(cfg->primary_outbound_proxy_service_ip));
    cfg->primary_outbound_proxy_service_port = data.ulOutBoundProxyServerPort;

    cfg->sip_register_expire = data2.ulRegisterExpireTime;
    cfg->heartbeat_switch = data.ucHeartbeatSwitch;
    cfg->heartbeat_cycle = data.lHeartbeatCycle;
    cfg->heartbeat_count = data.llHeartbeatCount;

    return HI_RET_SUCC;
}

uint32_t vs_ctcoam_get_plus_sip_parameter_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    unsigned int struct_size = 0;
    unsigned int add_len = 0;
    hi_ushort16 *pus_msglen = NULL;
    hi_ushort16 tlv_leaf = 0;
    hi_ctcoam_tlv_s *tlv_head = (hi_ctcoam_tlv_s *)(pv_inmsg - 4);
    vs_ctcoam_plus_sip_parameter_configuration_s *cfg = NULL;
    IgdVoiceSipBasicAttrConfTab data;
    IgdVoiceAdvancedAttrConfTab data2;

    if (pv_outmsg == NULL)
		return HI_RET_INVALID_PARA;

    cfg = (vs_ctcoam_plus_sip_parameter_configuration_s *)pv_outmsg;

    HI_OS_MEMSET_S(cfg, sizeof(vs_ctcoam_plus_sip_parameter_configuration_s), 0, sizeof(vs_ctcoam_plus_sip_parameter_configuration_s));
    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));
    HI_OS_MEMSET_S(&data2, sizeof(data2), 0, sizeof(data2));

    data.ulBitmap  |= SIP_BASIC_ATTR_MASK_ALL;
    data.ulBitmap1 |= SIP_BASIC_ATTR_MASK1_ALL;
    ret = igdCmConfGet(IGD_VOICE_SIP_BASIC_ATTR_TAB, (unsigned char *)&data, sizeof(data));
    if(ret != 0)
    {
        printf("[%s,%d] igdCmConfGet IGD_VOICE_SIP_BASIC_ATTR_TAB ret : %d \r\n", __FUNCTION__, __LINE__, ret);
        return ret;
    }

    data2.ulBitmap  |= VOICE_ADV_ATTR_MASK_ALL;
    ret = igdCmConfGet(IGD_VOICE_ADVANCED_ATTR_TAB, (unsigned char *)&data2, sizeof(data2));
    if(ret != 0)
    {
        printf("[%s,%d] igdCmConfGet IGD_VOICE_ADVANCED_ATTR_TAB ret : %d \r\n", __FUNCTION__, __LINE__, ret);
        return ret;
    }

    cfg->sip_local_port = data2.ulSipLocalPort;

    HI_OS_MEMCPY_S(&cfg->primary_sip_proxy_service_ip, sizeof(cfg->primary_sip_proxy_service_ip), data.aucProxyServer, sizeof(cfg->primary_sip_proxy_service_ip));
    cfg->primary_sip_proxy_service_port = data.ulProxyServerPort;

    HI_OS_MEMCPY_S(&cfg->backup_sip_proxy_service_ip, sizeof(cfg->backup_sip_proxy_service_ip), data.aucStandbyProxyServer, sizeof(cfg->backup_sip_proxy_service_ip));
    cfg->backup_sip_proxy_service_port = data.ulStandbyProxyServerPort;

    HI_OS_MEMCPY_S(&cfg->primary_sip_register_service_ip, sizeof(cfg->primary_sip_register_service_ip), data.aucRegistrarServer, sizeof(cfg->primary_sip_register_service_ip));
    cfg->primary_sip_register_service_port = data.ulRegistrarServerPort;

    HI_OS_MEMCPY_S(&cfg->backup_sip_register_service_ip, sizeof(cfg->backup_sip_register_service_ip), data.aucStandbyRegistrarServer, sizeof(cfg->backup_sip_register_service_ip));
    cfg->backup_sip_register_service_port = data.ulStandbyRegistrarServerPort;

    HI_OS_MEMCPY_S(&cfg->primary_outbound_proxy_service_ip, sizeof(cfg->primary_outbound_proxy_service_ip), data.aucOutBoundProxyServer, sizeof(cfg->primary_outbound_proxy_service_ip));
    cfg->primary_outbound_proxy_service_port = data.ulOutBoundProxyServerPort;

    cfg->sip_register_expire = data2.ulRegisterExpireTime;
    cfg->heartbeat_switch = data.ucHeartbeatSwitch;
    cfg->heartbeat_cycle = data.lHeartbeatCycle;
    cfg->heartbeat_count = data.llHeartbeatCount;

    /* net address change */
    cfg->sip_local_port = (hi_ushort16)htons(cfg->sip_local_port);
    cfg->primary_sip_proxy_service_port = (hi_ushort16)htons(cfg->primary_sip_proxy_service_port);
    cfg->backup_sip_proxy_service_port = (hi_ushort16)htons(cfg->backup_sip_proxy_service_port);
    cfg->primary_sip_register_service_port = (hi_ushort16)htons(cfg->primary_sip_register_service_port);
    cfg->backup_sip_register_service_port = (hi_ushort16)htons(cfg->backup_sip_register_service_port);
    cfg->primary_outbound_proxy_service_port = (hi_ushort16)htons(cfg->primary_outbound_proxy_service_port);
    cfg->sip_register_expire = htonl(cfg->sip_register_expire);
    cfg->heartbeat_cycle = (hi_ushort16)htons(cfg->heartbeat_cycle);
    cfg->heartbeat_count = (hi_ushort16)htons(cfg->heartbeat_count);

    /* slicing the packets and add tlv header to the data */
    tlv_leaf = tlv_head->us_leaf;
    tlv_leaf = (hi_ushort16)htons(tlv_leaf);
	struct_size = sizeof(vs_ctcoam_plus_sip_parameter_configuration_s);
    if(struct_size > 0 && struct_size <= 128)
    {
        *puc_changingmsglen = struct_size;
        HI_OS_MEMCPY_S(pv_outmsg, sizeof(vs_ctcoam_plus_sip_parameter_configuration_s), cfg, *puc_changingmsglen);
    }
	else if(struct_size > 128 && struct_size <= (255 - 4))//langer than 128, need to slicing the packets
	{
        add_len = packet_slicing_for_send(cfg, sizeof(vs_ctcoam_plus_sip_parameter_configuration_s), tlv_head->uc_branch, tlv_leaf);
        add_len = (add_len - 1) * 4;
        pus_msglen = (hi_ushort16 *)(puc_changingmsglen + 1);
        *pus_msglen = 0;
        *puc_changingmsglen = struct_size + add_len;//send msg will use this len value
    }
    else if(struct_size > (255 - 4) && struct_size <= 1496)
    {
        add_len = packet_slicing_for_send(cfg, sizeof(vs_ctcoam_plus_sip_parameter_configuration_s), tlv_head->uc_branch, tlv_leaf);
        add_len = (add_len - 1) * 4;
        pus_msglen = (hi_ushort16 *)(puc_changingmsglen + 1);
        *pus_msglen = struct_size + add_len;//send msg will use this len value
        *puc_changingmsglen = 0;
    }
    else
    {
        return HI_RET_FAIL;
    }

    return HI_RET_SUCC;
}

uint32_t vs_ctcoam_set_plus_sip_parameter_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    char ip_char[16];
    unsigned char cfg_change = 0;
    hi_ushort16 tlv_leaf = 0;
    hi_ctcoam_tlv_s *tlv_head = (hi_ctcoam_tlv_s *)(pv_inmsg - 4);
    IgdVoiceSipBasicAttrConfTab data;
    IgdVoiceAdvancedAttrConfTab data2;
    vs_ctcoam_plus_sip_parameter_configuration_s cfg_data, ram_cfg; 
    vs_ctcoam_plus_sip_parameter_configuration_s *cfg = &cfg_data;

    if (pv_inmsg == NULL)
        return HI_RET_INVALID_PARA;

    tlv_leaf = tlv_head->us_leaf;
    tlv_leaf = (hi_ushort16)htons(tlv_leaf);
    packet_slicing_for_receive(&cfg_data, sizeof(vs_ctcoam_plus_sip_parameter_configuration_s), pv_inmsg, 1496, tlv_head->uc_branch, tlv_leaf);

    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));
    HI_OS_MEMSET_S(&data2, sizeof(data2), 0, sizeof(data2));

    data.ulBitmap  |= SIP_BASIC_ATTR_MASK_BIT0_PROXY_SERVER|SIP_BASIC_ATTR_MASK_BIT1_PROXY_SERVER_PORT|
                      SIP_BASIC_ATTR_MASK_BIT3_REG_SERVER|SIP_BASIC_ATTR_MASK_BIT4_REG_SERVER_PORT|
                      SIP_BASIC_ATTR_MASK_BIT6_OUTBOUND_PROXY_SERVER|SIP_BASIC_ATTR_MASK_BIT7_OUTBOUND_PROXY_SERVER_PORT|
                      SIP_BASIC_ATTR_MASK_BIT9_STANDBY_PROXY_SERVER|SIP_BASIC_ATTR_MASK_BIT10_STANDBY_PROXY_SERVER_PORT|
                      SIP_BASIC_ATTR_MASK_BIT12_STANDBY_REG_SERVER|SIP_BASIC_ATTR_MASK_BIT13_STANDBY_REG_SERVER_PORT|
                      SIP_BASIC_ATTR_MASK_BIT24_HEARTBEAT_SWITCH|SIP_BASIC_ATTR_MASK_BIT25_HEARTBEAT_CYCLE|
                      SIP_BASIC_ATTR_MASK_BIT26_HEARTBEAT_COUNT;
    data2.ulBitmap |= VOICE_ADV_ATTR_MASK_BIT4_SIP_LOCAL_PORT|VOICE_ADV_ATTR_MASK_BIT5_REG_EXPIRE_TIME;

    /* net address change */
    cfg->sip_local_port = (hi_ushort16)ntohs(cfg->sip_local_port);
    cfg->primary_sip_proxy_service_port = (hi_ushort16)ntohs(cfg->primary_sip_proxy_service_port);
    cfg->backup_sip_proxy_service_port = (hi_ushort16)ntohs(cfg->backup_sip_proxy_service_port);
    cfg->primary_sip_register_service_port = (hi_ushort16)ntohs(cfg->primary_sip_register_service_port);
    cfg->backup_sip_register_service_port = (hi_ushort16)ntohs(cfg->backup_sip_register_service_port);
    cfg->primary_outbound_proxy_service_port = (hi_ushort16)ntohs(cfg->primary_outbound_proxy_service_port);
    cfg->sip_register_expire = ntohl(cfg->sip_register_expire);
    cfg->heartbeat_cycle = (hi_ushort16)ntohs(cfg->heartbeat_cycle);
    cfg->heartbeat_count = (hi_ushort16)ntohs(cfg->heartbeat_count);

    /* compare cfg */
    vs_oam_plus_sip_param_cfg_to_ram(&ram_cfg);
    if(memcmp(&ram_cfg, cfg, sizeof(vs_ctcoam_plus_sip_parameter_configuration_s)) != 0)
        cfg_change = 1;

    /* if cfg change, set to mib */
    if(cfg_change == 1)
    {
        data2.ulSipLocalPort = cfg->sip_local_port;

        if(strlen((char *)cfg->primary_sip_proxy_service_ip) == 4)//ip address len is 4
        {
            igdCmApiIptoChar(cfg->primary_sip_proxy_service_ip, ip_char, sizeof(ip_char));
            HI_OS_MEMCPY_S(data.aucProxyServer, sizeof(data.aucProxyServer), ip_char, sizeof(ip_char));
            data.aucProxyServer[sizeof(data.aucProxyServer)-1] = '\0';
            //printf("aucProxyServer = %s, ip_char = %s\n", data.aucProxyServer, ip_char);
        }
        else
        {
            HI_OS_MEMCPY_S(data.aucProxyServer, sizeof(data.aucProxyServer), cfg->primary_sip_proxy_service_ip, sizeof(cfg->primary_sip_proxy_service_ip));
            data.aucProxyServer[sizeof(data.aucProxyServer)-1] = '\0';
        }
        data.ulProxyServerPort = cfg->primary_sip_proxy_service_port;


        if(strlen((char *)cfg->backup_sip_proxy_service_ip) == 4)//ip address len is 4
        {

            igdCmApiIptoChar(cfg->backup_sip_proxy_service_ip, ip_char, sizeof(ip_char));
            HI_OS_MEMCPY_S(data.aucStandbyProxyServer, sizeof(data.aucStandbyProxyServer), ip_char, sizeof(ip_char));
            data.aucStandbyProxyServer[sizeof(data.aucStandbyProxyServer)-1] = '\0';
            //printf("aucStandbyProxyServer = %s, ip_char = %s\n", data.aucStandbyProxyServer, ip_char);
        }
        else
        {
            HI_OS_MEMCPY_S(data.aucStandbyProxyServer, sizeof(data.aucStandbyProxyServer), cfg->backup_sip_proxy_service_ip, sizeof(cfg->backup_sip_proxy_service_ip));
            data.aucStandbyProxyServer[sizeof(data.aucStandbyProxyServer)-1] = '\0';
            //printf("aucStandbyProxyServer = %s\n", data.aucStandbyProxyServer);
        }
        data.ulStandbyProxyServerPort = cfg->backup_sip_proxy_service_port;

        if(strlen((char *)cfg->primary_sip_register_service_ip) == 4)//ip address len is 4
        {

            igdCmApiIptoChar(cfg->primary_sip_register_service_ip, ip_char, sizeof(ip_char));
            HI_OS_MEMCPY_S(data.aucRegistrarServer, sizeof(data.aucRegistrarServer), ip_char, sizeof(ip_char));
            data.aucRegistrarServer[sizeof(data.aucRegistrarServer)-1] = '\0';
            //printf("aucRegistrarServer = %s, ip_char = %s\n", data.aucRegistrarServer, ip_char);
        }
        else
        {
            HI_OS_MEMCPY_S(data.aucRegistrarServer, sizeof(data.aucRegistrarServer), cfg->primary_sip_register_service_ip, sizeof(cfg->primary_sip_register_service_ip));
            data.aucRegistrarServer[sizeof(data.aucRegistrarServer)-1] = '\0';
            //printf("aucRegistrarServer = %s\n", data.aucRegistrarServer);
        }
        data.ulRegistrarServerPort = cfg->primary_sip_register_service_port;

        if(strlen((char *)cfg->backup_sip_register_service_ip) == 4)//ip address len is 4
        {

            igdCmApiIptoChar(cfg->backup_sip_register_service_ip, ip_char, sizeof(ip_char));
            HI_OS_MEMCPY_S(data.aucStandbyRegistrarServer, sizeof(data.aucStandbyRegistrarServer), ip_char, sizeof(ip_char));
            data.aucStandbyRegistrarServer[sizeof(data.aucStandbyRegistrarServer)-1] = '\0';
            //printf("aucStandbyRegistrarServer = %s, ip_char = %s\n", data.aucStandbyRegistrarServer, ip_char);
        }
        else
        {
            HI_OS_MEMCPY_S(data.aucStandbyRegistrarServer, sizeof(data.aucStandbyRegistrarServer), cfg->backup_sip_register_service_ip, sizeof(cfg->backup_sip_register_service_ip));
            data.aucStandbyRegistrarServer[sizeof(data.aucStandbyRegistrarServer)-1] = '\0';
            //printf("aucStandbyRegistrarServer = %s\n", data.aucStandbyRegistrarServer);
        }
        data.ulStandbyRegistrarServerPort = cfg->backup_sip_register_service_port;

        if(strlen((char *)cfg->primary_outbound_proxy_service_ip) == 4)//ip address len is 4
        {

            igdCmApiIptoChar(cfg->primary_outbound_proxy_service_ip, ip_char, sizeof(ip_char));
            HI_OS_MEMCPY_S(data.aucOutBoundProxyServer, sizeof(data.aucOutBoundProxyServer), ip_char, sizeof(ip_char));
            data.aucOutBoundProxyServer[sizeof(data.aucOutBoundProxyServer)-1] = '\0';
            //printf("aucOutBoundProxyServer = %s, ip_char = %s\n", data.aucOutBoundProxyServer, ip_char);
        }
        else
        {
            HI_OS_MEMCPY_S(data.aucOutBoundProxyServer, sizeof(data.aucOutBoundProxyServer), cfg->primary_outbound_proxy_service_ip, sizeof(cfg->primary_outbound_proxy_service_ip));
            data.aucOutBoundProxyServer[sizeof(data.aucOutBoundProxyServer)-1] = '\0';
            //printf("aucOutBoundProxyServer = %s\n", data.aucOutBoundProxyServer);
        }
        data.ulOutBoundProxyServerPort = cfg->primary_outbound_proxy_service_port;

        data2.ulRegisterExpireTime = cfg->sip_register_expire;
        data.ucHeartbeatSwitch = cfg->heartbeat_switch;
        data.lHeartbeatCycle = cfg->heartbeat_cycle;
        data.llHeartbeatCount = cfg->heartbeat_count;

        ret = igdCmConfSet(IGD_VOICE_SIP_BASIC_ATTR_TAB, (unsigned char *)&data, sizeof(data));
        if(ret != 0)
        {
            printf("[%s,%d] igdCmConfSet IGD_VOICE_SIP_BASIC_ATTR_TAB ret : %d \r\n", __FUNCTION__, __LINE__, ret);
            return ret;
        }

        ret = igdCmConfSet(IGD_VOICE_ADVANCED_ATTR_TAB, (unsigned char *)&data2, sizeof(data2));
        if(ret != 0)
        {
            printf("[%s,%d] igdCmConfSet IGD_VOICE_ADVANCED_ATTR_TAB ret : %d \r\n", __FUNCTION__, __LINE__, ret);
            return ret;
        }
    }

    return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

