#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "vs_ctcoam_wan_dual_stack.h"
#include "vs_ctcoam_wan_configuration.h"
#include "hi_sml_wan.h"
#include "zlib.h"

uint32_t get_wan_info(hi_uchar8 *output_data, hi_uint32 *output_data_len, hi_ushort16 *output_wan_num)
{
    hi_uchar8 i = 0;
    hi_int32 ret = 0;
    hi_uint32 listnum = 0, uncompressed_data_len = 0;
    hi_ushort16 wan_total_num = 0;
    hi_uchar8 *uncompressed_data = output_data;
    hi_uchar8 *ptr = NULL;
    hi_uint32 u32Val;
    hi_char8 pri_dns[16] = {0};
    hi_char8 sec_dns[16] = {0};
    hi_char8 third_dns[16] = {0};
    hi_char8 pri_dnsv6[48] = {0};
    hi_char8 sec_dnsv6[48] = {0};
    hi_char8 third_dnsv6[48] = {0};
    vs_ctcoam_wan_dual_stack_configuration_s wan_cfg;
    IgdWanConnectionAttrConfTab data;
    IgdWanConnectionStateInfoTab wanStateData;
    /* Bohannon, get the total wan num, and their wan conn id*/
    syswan_wan_listinfo_t list_info={0};

    if(output_data == NULL)
        return HI_RET_FAIL;

#if defined(CONFIG_PLATFORM_OPENWRT)
    HI_IPC_CALL("hi_sml_wan_connection_attr_tab_get_listnum", &list_info);
#else
    igdCmConfGetEntryNum(IGD_WAN_CONNECTION_ATTR_TAB, &listnum);
#endif
    /* Bohannon, get the total wan num, and their wan conn id*/
    listnum = list_info.list_num;
    if((0 < listnum) && (listnum < 8))
    {
        IgdWanConnectionIndexAttrTab wan_conn_indexobj[IGD_WAN_CONNECTION_RECORD_NUM];
        HI_OS_MEMSET_S(wan_conn_indexobj,sizeof(wan_conn_indexobj),0,sizeof(wan_conn_indexobj));

        ret = igdCmConfGetAllEntry(IGD_WAN_CONNECTION_INDEX_ATTR_TAB,(unsigned char *)wan_conn_indexobj,sizeof(wan_conn_indexobj));
        if(ret != 0)
        {
            printf("igdCmConfGetAllEntry IGD_WAN_CONNECTION_INDEX_ATTR_TAB ret : %d \r\n", ret);
            return HI_RET_FAIL;
        }

        *output_wan_num = listnum;
        wan_total_num = listnum;
        wan_total_num = (unsigned short)htons(wan_total_num);
        HI_OS_MEMCPY_S(uncompressed_data, sizeof(wan_total_num), &wan_total_num, sizeof(wan_total_num));
        ptr = uncompressed_data + 2; 
        uncompressed_data_len += 2;
        for(i = 0; i < listnum; i++)
        {
            HI_OS_MEMSET_S(&wan_cfg, sizeof(wan_cfg), 0, sizeof(wan_cfg));
            HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));
            data.ulBitmap |= WAN_CONNECTION_ATTR_MASK_ALL;
            data.ulBitmap1 |= WAN_CONNECTION_ATTR_MASK1_ALL;

            data.ucGlobalIndex = wan_conn_indexobj[i].ucGlobalIndex;
            data.ucConDevIndex = wan_conn_indexobj[i].ucConDevIndex;
            data.ucXPConIndex = wan_conn_indexobj[i].ucXPConIndex;
            HI_OS_STRCPY_S((char *)data.aucWanName, sizeof(data.aucWanName),wan_conn_indexobj[i].aucWanName);

#if defined(CONFIG_PLATFORM_OPENWRT)
            data.ucGlobalIndex = list_info.list_wanid[i];
            ret = HI_IPC_CALL("hi_sml_wan_connection_attr_tab_get", &data);
#else
            ret = igdCmConfGet(IGD_WAN_CONNECTION_ATTR_TAB, (unsigned char *)&data, sizeof(data));
#endif
            if(ret != 0)
            {
                printf("igdCmConfGet IGD_WAN_CONNECTION_ATTR_TAB ret : %d \n", ret);
                continue;
            }

            HI_OS_MEMSET_S(&wanStateData, sizeof(wanStateData), 0, sizeof(wanStateData));
            wanStateData.ucGlobalIndex = wan_conn_indexobj[i].ucGlobalIndex;
            HI_OS_STRCPY_S((char *)wanStateData.aucWanName, sizeof(wanStateData.aucWanName),wan_conn_indexobj[i].aucWanName);
            wanStateData.ulBitmap |= WAN_CONNECTION_STATE_MASK_ALL;
#if defined(CONFIG_PLATFORM_OPENWRT)
            wanStateData.ucGlobalIndex = list_info.list_wanid[i];
            ret = HI_IPC_CALL("hi_sml_wan_connection_state_tab_get", &wanStateData);
#else
            ret = igdCmConfGet(IGD_WAN_CONNECTION_STATE_TAB,(unsigned char *)&wanStateData,sizeof(wanStateData));
#endif
            if(ret != 0)
            {
                printf("igdCmConfGet IGD_WAN_CONNECTION_STATE_TAB ret : %d \n", ret);
                continue;
            }

            /*wan index and name*/
            wan_cfg.wan_index = i + 1;
            HI_OS_MEMCPY_S(wan_cfg.wan_name, sizeof(wan_cfg.wan_name), data.aucWanName, sizeof(data.aucWanName));

            /*wan service mode*/
            switch (data.ucServiceList)
            {
                case WAN_CONNECTION_SERVICE_INTERNET: 
                    wan_cfg.wan_service_mode = VS_WAN_SERVICE_INTERNET;
                    break;
                case WAN_CONNECTION_SERVICE_TR069: 
                    wan_cfg.wan_service_mode = VS_WAN_SERVICE_TR069;
                    break;
                case (WAN_CONNECTION_SERVICE_TR069 | WAN_CONNECTION_SERVICE_INTERNET): 
                    wan_cfg.wan_service_mode = VS_WAN_SERVICE_TR069_INTERNET;
                    break;
                case (WAN_CONNECTION_SERVICE_TR069 | WAN_CONNECTION_SERVICE_VOIP): 
                    wan_cfg.wan_service_mode = VS_WAN_SERCICE_TR069_VOIP;
                    break; 
                case (WAN_CONNECTION_SERVICE_INTERNET | WAN_CONNECTION_SERVICE_VOIP): 
                    wan_cfg.wan_service_mode = VS_WAN_SERVICE_INTERNET_VOIP;
                    break;    
                case (WAN_CONNECTION_SERVICE_TR069 | WAN_CONNECTION_SERVICE_INTERNET | WAN_CONNECTION_SERVICE_VOIP): 
                    wan_cfg.wan_service_mode = VS_WAN_SERVICE_TR069_INTERNET_VOIP;
                    break;   
                case WAN_CONNECTION_SERVICE_VOIP: 
                    wan_cfg.wan_service_mode = VS_WAN_SERVICE_VOIP;
                    break;
                case WAN_CONNECTION_SERVICE_OTHER: 
                    wan_cfg.wan_service_mode = VS_WAN_SERVICE_OTHER;
                    break;
                default:    
                    printf("data.ucServiceList[%d], unknown service mode\n", data.ucServiceList);             
                    break;
            }

            /*vlan info*/
            if (data.ucVlanMode == WAN_CONNECTION_VLAN_OVERRIDE)
            {
                wan_cfg.vlan_mode = VS_WAN_VLAN_MODE_TAG;
                wan_cfg.wan_vlan_id = data.ulVlanIdMark?data.ulVlanIdMark:0xFFFF;
                if(data.uc8021pMarkEnable)
                    wan_cfg.wan_cos_8021p = data.uc8021pMark;
                else
                    wan_cfg.wan_cos_8021p = 0xFFFF;
            } 
            else if (data.ucVlanMode == WAN_CONNECTION_VLAN_RESERVE)
            {
                wan_cfg.vlan_mode = VS_WAN_VLAN_MODE_TRANSPARENT;
                wan_cfg.wan_vlan_id = data.ulVlanIdMark?data.ulVlanIdMark:0xFFFF;
                if (data.uc8021pMarkEnable)
                    wan_cfg.wan_cos_8021p = data.uc8021pMark;
                else
                    wan_cfg.wan_cos_8021p = 0xFFFF;
            }
            else
            {
                wan_cfg.vlan_mode = VS_WAN_VLAN_MODE_DIABLE;
                wan_cfg.wan_vlan_id = 0xFFFF;
                wan_cfg.wan_cos_8021p = 0xFFFF;
            }
            
            /*translation vlan*/
            wan_cfg.translation_enable = 0;
            wan_cfg.translation_vlan = 0xFFFF;
            wan_cfg.translation_cos = 0xFFFF;
            /*QinQ*/
            wan_cfg.QinQ_enable = 0;
            wan_cfg.TPID = 0x8100;
            wan_cfg.wan_svlan_id = 0xFFFF;
            wan_cfg.wan_svlan_cos = 0;
            /*Qos*/
            wan_cfg.wan_qos_enable = 0;//don not find the param
            /*wan mac*/
            HI_OS_MEMCPY_S(wan_cfg.wan_mac, sizeof(wan_cfg.wan_mac), data.aucMACAddress, sizeof(data.aucMACAddress));
            /*MTU*/
            wan_cfg.MTU = data.ulMaxMTUSize;
            /*wan connection type*/
            if ((data.ucConnectionType == WAN_CONNECTION_TYPE_IP_BRIDGED) || (data.ucConnectionType == WAN_CONNECTION_TYPE_PPPOE_BRIDGED))
            {
                wan_cfg.wan_type = VS_WAN_BRIDGE;
            }
            else
            {
                wan_cfg.wan_type = VS_WAN_ROUTE;
            }
            /*binding lan*/
            wan_cfg.binding_lan |= data.ulBindPort & WAN_CONNECTION_BIND_PORT_LAN1;
            wan_cfg.binding_lan |= data.ulBindPort & WAN_CONNECTION_BIND_PORT_LAN2;
            wan_cfg.binding_lan |= data.ulBindPort & WAN_CONNECTION_BIND_PORT_LAN3;
            wan_cfg.binding_lan|= data.ulBindPort & WAN_CONNECTION_BIND_PORT_LAN4;
            /*binding ssid*/
            wan_cfg.binding_ssid |= ((data.ulBindPort & WAN_CONNECTION_BIND_PORT_SSID1) >> 4);
            wan_cfg.binding_ssid |= ((data.ulBindPort & WAN_CONNECTION_BIND_PORT_SSID2) >> 4);
            wan_cfg.binding_ssid |= ((data.ulBindPort & WAN_CONNECTION_BIND_PORT_SSID3) >> 4);
            wan_cfg.binding_ssid |= ((data.ulBindPort & WAN_CONNECTION_BIND_PORT_SSID4) >> 4);
            wan_cfg.binding_ssid |= ((data.ulBindPort & WAN_CONNECTION_BIND_PORT_SSID5) >> 12);
            wan_cfg.binding_ssid |= ((data.ulBindPort & WAN_CONNECTION_BIND_PORT_SSID6) >> 12);
            wan_cfg.binding_ssid |= ((data.ulBindPort & WAN_CONNECTION_BIND_PORT_SSID7) >> 12);
            wan_cfg.binding_ssid |= ((data.ulBindPort & WAN_CONNECTION_BIND_PORT_SSID8) >> 12);

            /*ip protocal and state*/
            switch (data.ucIPMode)
            {
                case WAN_CONNECTION_IP_MODE_V4:
                    wan_cfg.wan_ip_protocol = VS_WAN_IPV4;
                    wan_cfg.wan_state = (wanStateData.ucConnectionStatus == WAN_CONNECTION_STATUS_CONNECTED) ? 1 : 0;
                    if (data.ucConnectionType == WAN_CONNECTION_TYPE_IP_ROUTED)
                        wan_cfg.wan_NAT_enable = data.ucNATEnabled;
                    break;
                case WAN_CONNECTION_IP_MODE_V6:
                    wan_cfg.wan_ip_protocol = VS_WAN_IPV6;
                    wan_cfg.wan_state = (wanStateData.ucIPv6ConnectionStatus == WAN_CONNECTION_IPV6_STATUS_CONNECTED) ? 1 : 0;
                    break;
                case WAN_CONNECTION_IP_MODE_V4_AND_V6:
                    wan_cfg.wan_ip_protocol  = VS_WAN_IPV4_AND_IPV6;
                    if (data.ucConnectionType == WAN_CONNECTION_TYPE_IP_ROUTED)
                        wan_cfg.wan_NAT_enable = data.ucNATEnabled;
                    if (wanStateData.ucConnectionStatus == WAN_CONNECTION_STATUS_CONNECTED || wanStateData.ucIPv6ConnectionStatus == WAN_CONNECTION_IPV6_STATUS_CONNECTED)
                        wan_cfg.wan_state = 1;
                    break;
                default:
                    printf("data.ucIPMode[%d], unknown ip protocol type\n", data.ucIPMode);
                    break;
            }

            /*ip info*/
            if (wan_cfg.wan_type  == VS_WAN_ROUTE)
            {
                if ((data.ucIPMode & WAN_CONNECTION_IP_MODE_V6) == WAN_CONNECTION_IP_MODE_V6)
                {
                    if (data.ucDsliteEnable)
                    {
                        wan_cfg.wan_DS_Lite_enable = data.ucDsliteEnable;
                        if (data.ucAftrMode == WAN_CONNECTION_AFTR_MODE_STATIC)
                        {
                            wan_cfg.wan_DS_Lite_AFTR_mode = VS_WAN_DSLITE_AFTR_MODE_MANUAL;
                            wan_cfg.wan_DS_Lite_address_type = 0;/*0:ipv6 1:dns*/
                            HI_OS_MEMCPY_S(wan_cfg.wan_DS_Lite_address, sizeof(wan_cfg.wan_DS_Lite_address), data.aucAftr, sizeof(data.aucAftr));
                        }
                        else
                        {
                            wan_cfg.wan_DS_Lite_AFTR_mode = VS_WAN_DSLITE_AFTR_MODE_DHCPV6;
                        }
                    }
                    wan_cfg.wan_6rd_enable = 0;//don not support
                }
                switch (data.ucAddressingType)
                {
                    case WAN_CONNECTION_ADDRESSING_TYPE_STATIC: 
                        wan_cfg.wan_connection_mode = VS_WAN_CONNECTION_ADDRESSING_TYPE_STATIC;
                        if ((data.ucIPMode & WAN_CONNECTION_IP_MODE_V4) == WAN_CONNECTION_IP_MODE_V4)
                        {
                            HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_address, CM_IP_ADDR_LEN, data.aucExternalIPAddress, CM_IP_ADDR_LEN);

                            HI_OS_MEMCPY_S(&u32Val, CM_IP_ADDR_LEN, data.aucSubnetMask, CM_IP_ADDR_LEN);

                            wan_cfg.wan_ipv4_mask = calculate_mask_bits(u32Val);

                            HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_default_gateway, CM_IP_ADDR_LEN, data.aucDefaultGateway, CM_IP_ADDR_LEN);

                            if (data.ucDNSEnabled)
                            {
                                sscanf(data.aucDNSServers, "%15[^,],%15[^,],%15[^,]", pri_dns, sec_dns, third_dns);
                                //printf("pri_dns=%s, sec_dns=%s, third_dns=%s\n", pri_dns, sec_dns, third_dns);
                                u32Val = igdCmApiChartoIp(pri_dns);
                                HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_master_dns, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
                                u32Val = igdCmApiChartoIp(sec_dns);
                                HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_slave_dns, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
                            }
                        }
                        break;
                    case WAN_CONNECTION_ADDRESSING_TYPE_DHCP:
                        wan_cfg.wan_connection_mode = VS_WAN_CONNECTION_ADDRESSING_TYPE_DHCP;
                        if ((data.ucIPMode & WAN_CONNECTION_IP_MODE_V4) == WAN_CONNECTION_IP_MODE_V4)
                        {
                            u32Val = igdCmApiChartoIp(wanStateData.aucIPv4Address);
                            HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_address, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
                            u32Val = igdCmApiChartoIp(wanStateData.aucIPv4Mask);
                            wan_cfg.wan_ipv4_mask = calculate_mask_bits(u32Val);

                            u32Val = igdCmApiChartoIp(wanStateData.aucIPv4Gateway);
                            HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_default_gateway, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);

                            wan_cfg.wan_ipv4_request_dns_mode = (data.ucDNSOverrideAllowed == 1)?WAN_CONNECTION_DNS_OVERRIDE_ALLOWED_ENABLE:WAN_CONNECTION_DNS_OVERRIDE_ALLOWED_DISABLE;
                            if(data.ucDNSOverrideAllowed == WAN_CONNECTION_DNS_OVERRIDE_ALLOWED_ENABLE)
                            {
                                sscanf(data.aucDNSServers, "%15[^,],%15[^,],%15[^,]", pri_dns, sec_dns, third_dns);
                                //printf("pri_dns=%s, sec_dns=%s, third_dns=%s\n", pri_dns, sec_dns, third_dns);
                                u32Val = igdCmApiChartoIp(pri_dns);
                                HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_master_dns, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
                                u32Val = igdCmApiChartoIp(sec_dns);
                                HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_slave_dns, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
                            }
                            else
                            {
                                u32Val = igdCmApiChartoIp(wanStateData.aucIPv4DnsPrimary);
                                HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_master_dns, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
                                u32Val = igdCmApiChartoIp(wanStateData.aucIPv4DnsBackup);
                                HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_slave_dns, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
                            }  
                        }

                        if ((data.ucIPMode & WAN_CONNECTION_IP_MODE_V6) == WAN_CONNECTION_IP_MODE_V6)
                        {
                            wan_cfg.wan_ipv6_request_dns_mode = data.ucDNSAchieve;
                            if(data.ucDNSAchieve == WAN_CONNECTION_DNS_ACHIEVE_STATIC)
                            {
                                sscanf(data.aucIPv6DNSServers, "%47[^,],%47[^,],%47[^,]", pri_dnsv6, sec_dnsv6, third_dnsv6);
                                //printf("pri_dnsv6=%s, sec_dnsv6=%s\n", pri_dnsv6, sec_dnsv6);
                                igdCmApiChartoIpv6((void *)wan_cfg.wan_ipv6_master_dns, pri_dnsv6);
                                igdCmApiChartoIpv6((void *)wan_cfg.wan_ipv6_slave_dns, sec_dnsv6);
                            }
                        }
                        break;
                    case WAN_CONNECTION_ADDRESSING_TYPE_PPPOE:
                        wan_cfg.wan_connection_mode  = VS_WAN_CONNECTION_ADDRESSING_TYPE_PPPOE;
                        wan_cfg.MTU = data.ulMaxMRUSize;
                        wan_cfg.wan_pppoe_proxy_enable = data.ucProxyEnable;

                        if (data.ucConnectionTrigger == WAN_CONNECTION_TRIGGER_ALWAYS_ON)
                            wan_cfg.wan_pppoe_mode = VS_WAN_PPPOE_DIAL_MODE_ALWAYS_ON;
                        else
                            wan_cfg.wan_pppoe_mode  = VS_WAN_PPPOE_DIAL_MODE_ON_CONMAND;
                        
                        HI_OS_MEMCPY_S(wan_cfg.wan_pppoe_username, sizeof(wan_cfg.wan_pppoe_username), data.aucUsername, sizeof(data.aucUsername));
                        HI_OS_MEMCPY_S(wan_cfg.wan_pppoe_password, sizeof(wan_cfg.wan_pppoe_password), data.aucPassword, sizeof(data.aucPassword));
                        HI_OS_MEMCPY_S(wan_cfg.wan_pppoe_servicename, sizeof(wan_cfg.wan_pppoe_servicename), data.aucPppoeServiceName, sizeof(data.aucPppoeServiceName));

                        if ((data.ucIPMode & WAN_CONNECTION_IP_MODE_V4) == WAN_CONNECTION_IP_MODE_V4)
                        {
                            HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_address, CM_IP_ADDR_LEN, wanStateData.aucExternalIPAddress, CM_IP_ADDR_LEN);
                            u32Val = igdCmApiChartoIp(wanStateData.aucIPv4Mask);
                            wan_cfg.wan_ipv4_mask = calculate_mask_bits(u32Val);
                            u32Val = igdCmApiChartoIp(wanStateData.aucIPv4Gateway);
                            HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_default_gateway, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
                            u32Val = igdCmApiChartoIp(wanStateData.aucIPv4DnsPrimary);
                            HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_master_dns, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
                            u32Val = igdCmApiChartoIp(wanStateData.aucIPv4DnsBackup);
                            HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_slave_dns, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
                        }
                        break;
                    default:    
                        printf("data.ucAddressingType[%d], unknown address type\n", data.ucAddressingType);             
                        break;
                }

                if ((data.ucIPMode & WAN_CONNECTION_IP_MODE_V6) == WAN_CONNECTION_IP_MODE_V6)
                {
                    // if(wan_cfg.wan_connection_mode != VS_WAN_CONNECTION_ADDRESSING_TYPE_STATIC)//dhcp and pppoe take effect
                    //     wan_cfg.wan_ipv6_address_mode = (data.ucIPv6AddressOrigin == WAN_CONNECTION_IPV6_ADDR_ORIGIN_DHCPV6)?VS_WAN_IPV6_ADDRESS_MODE_DHCP:VS_WAN_IPV6_ADDRESS_MODE_SLAAC;
                    
                    // if (wan_cfg.wan_ipv6_address_mode == VS_WAN_IPV6_ADDRESS_MODE_DHCP)
                    // {
                    //     //wan_cfg.wan_dhcpv6_client_request_address_enable = (data.ucIPv6AddressOrigin==WAN_CONNECTION_IPV6_ADDR_ORIGIN_NONE)?0:1;
                    //     wan_cfg.wan_dhcpv6_client_request_prefix_enable = (data.ucIPv6PrefixOrigin==WAN_CONNECTION_IPV6_PREFIX_ORIGIN_PREFIX_DELEGATION)?1:0;/*DHCP PD*/
                    // }
                    // else
                    // {
                    //     //wan_cfg.wan_dhcpv6_client_request_address_enable = (data.ucIPv6AddressOrigin==WAN_CONNECTION_IPV6_ADDR_ORIGIN_NONE)?0:1;
                    //     wan_cfg.wan_dhcpv6_client_request_prefix_enable = (data.ucIPv6PrefixOrigin==WAN_CONNECTION_IPV6_PREFIX_ORIGIN_RA)?1:0;/*DHCP PD*/   
                    // }

                    /* for openwrt */
                    wan_cfg.wan_ipv6_address_mode = VS_WAN_IPV6_ADDRESS_MODE_SLAAC;
                    wan_cfg.wan_dhcpv6_client_request_address_enable = (data.ucIPv6AddressOrigin == WAN_CONNECTION_IPV6_ADDR_ORIGIN_NONE)? 0 : 1;
                    wan_cfg.wan_dhcpv6_client_request_prefix_enable = (data.ucIPv6PrefixOrigin == WAN_CONNECTION_IPV6_PREFIX_ORIGIN_PREFIX_DELEGATION)? 1 : 0;/*DHCP PD*/

                    if(data.ucIPv6AddressOrigin == WAN_CONNECTION_IPV6_ADDR_ORIGIN_STATIC)
                    {
                        
                        HI_OS_MEMCPY_S(wan_cfg.wan_ipv6_address, sizeof(wan_cfg.wan_ipv6_address), data.aucIPv6Address, sizeof(data.aucIPv6Address));
                        wan_cfg.wan_ipv6_mask = data.ucIPv6AddrPrefix;
                        //printf("ucIPv6AddrPrefix = %d\n", data.ucIPv6AddrPrefix);     
                        igdCmApiChartoIpv6((void*)wan_cfg.wan_ipv6_default_gateway, data.aucDefaultIPv6Gateway);
 
                        //pst_data->wanCfg[wanIndex].uc_wanRequestDnsMode = VS_WAN_DNSV6_STATIC;
                        sscanf(data.aucIPv6DNSServers, "%47[^,],%47[^,],%47[^,]", pri_dnsv6, sec_dnsv6, third_dnsv6);
                        //printf("pri_dnsv6=%s, sec_dnsv6=%s\n", pri_dnsv6, sec_dnsv6);
                        igdCmApiChartoIpv6((void *)wan_cfg.wan_ipv6_master_dns, pri_dnsv6);

                        igdCmApiChartoIpv6((void *)wan_cfg.wan_ipv6_slave_dns, sec_dnsv6);
                    }
                    else
                    {
                        if(data.ucDNSAchieve != WAN_CONNECTION_DNS_ACHIEVE_STATIC)
                        {
                            if (strlen(wanStateData.aucIpv6Address) != 0) 
                            {
                                igdCmApiChartoIpv6((void *)wan_cfg.wan_ipv6_address, wanStateData.aucIpv6Address);
                                wan_cfg.wan_ipv6_mask = wanStateData.ulIpv6Mask;
                            }
                            if (strlen(wanStateData.aucIpv6Gateway) != 0)
                            {
                                igdCmApiChartoIpv6((void *)wan_cfg.wan_ipv6_default_gateway, wanStateData.aucIpv6Gateway);
                            }
                            if (strlen(wanStateData.aucIpv6DnsPrimary) != 0)
                            {
                                igdCmApiChartoIpv6((void *)wan_cfg.wan_ipv6_master_dns, wanStateData.aucIpv6DnsPrimary);
                            }
                            if (strlen(wanStateData.aucIpv6DnsBackup) != 0)
                            {
                                igdCmApiChartoIpv6((void *)wan_cfg.wan_ipv6_slave_dns, wanStateData.aucIpv6DnsBackup);
                            }
                        }
                    }
                }
            }

            /* change net address */
            wan_cfg.wan_index = (hi_ushort16)htons(wan_cfg.wan_index);
            wan_cfg.wan_service_mode = (hi_ushort16)htons(wan_cfg.wan_service_mode);
            wan_cfg.wan_type = (hi_ushort16)htons(wan_cfg.wan_type);
            wan_cfg.wan_vlan_id = (hi_ushort16)htons(wan_cfg.wan_vlan_id);
            wan_cfg.wan_cos_8021p = (hi_ushort16)htons(wan_cfg.wan_cos_8021p);
            wan_cfg.wan_ip_protocol = (hi_ushort16)htons(wan_cfg.wan_ip_protocol);
            wan_cfg.wan_connection_mode = (hi_ushort16)htons(wan_cfg.wan_connection_mode);
            wan_cfg.wan_ipv4_mask = htonl(wan_cfg.wan_ipv4_mask);
            wan_cfg.wan_ipv6_mask = htonl(wan_cfg.wan_ipv6_mask);
            wan_cfg.wan_pppoe_mode = (hi_ushort16)htons(wan_cfg.wan_pppoe_mode);
            wan_cfg.wan_6rd_ipv4_mask_len = htonl(wan_cfg.wan_6rd_ipv4_mask_len);
            wan_cfg.wan_6rd_ipv6_prefix_len = htonl(wan_cfg.wan_6rd_ipv6_prefix_len);
            wan_cfg.vlan_mode = (hi_ushort16)htons(wan_cfg.vlan_mode);
            wan_cfg.translation_vlan = (hi_ushort16)htons(wan_cfg.translation_vlan);
            wan_cfg.translation_cos = (hi_ushort16)htons(wan_cfg.translation_cos);
            wan_cfg.TPID = (hi_ushort16)htons(wan_cfg.TPID);
            wan_cfg.wan_svlan_id = (hi_ushort16)htons(wan_cfg.wan_svlan_id);
            wan_cfg.wan_svlan_cos = (hi_ushort16)htons(wan_cfg.wan_svlan_cos);
            wan_cfg.wan_state = (hi_ushort16)htons(wan_cfg.wan_state);
            wan_cfg.MTU = (hi_ushort16)htons(wan_cfg.MTU);

            HI_OS_MEMCPY_S(ptr, sizeof(wan_cfg), &wan_cfg, sizeof(wan_cfg));
            ptr += sizeof(wan_cfg);
            uncompressed_data_len += sizeof(wan_cfg);
        }//for(i = 0; i < listnum; i++)
    }
    *output_data_len = uncompressed_data_len;

    return HI_RET_SUCC;
}


uint32_t vs_ctcoam_get_wan_dual_stack(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    vs_ctcoam_wan_dual_stack_configuration_s wan_cfg;
    hi_ctcoam_tlv_s *tlv_head = (hi_ctcoam_tlv_s *)(pv_inmsg - 4);
    hi_uchar8 add_len = 0, packet_slicing = 0;
    hi_uchar8 uncompressed_data[10*1024], compressed_data[1024];
    hi_ushort16 tlv_leaf = 0, wan_total_num = 0;
    hi_ushort16 *pus_msglen = NULL;
    hi_uint32 uncompressed_data_len = 0;
    hi_ulong32 compressed_data_len = sizeof(compressed_data);
    hi_int32 err = 0, struct_size = 0;
    
    
    if (pv_outmsg == NULL)
		return HI_RET_INVALID_PARA;

    HI_OS_MEMSET_S(&uncompressed_data, sizeof(uncompressed_data), 0, sizeof(uncompressed_data));
    HI_OS_MEMSET_S(&wan_cfg, sizeof(wan_cfg), 0, sizeof(wan_cfg));

    /* After OLT set wan cfg , OLT will get wan cfg immediately . 
        In this moment , we can't  refresh WanNumber before ONU set wan cfg into flash*/
    if(wan_config_running)
        return HI_RET_SUCC;

    
    /* 1. read the mib of wan and combined wan data */
    err = get_wan_info(uncompressed_data, &uncompressed_data_len, &wan_total_num);
    if(err != HI_RET_SUCC)
        printf("get_wan_info error\n");


    /* 2. add tlv header to the data */
    tlv_leaf = tlv_head->us_leaf;
    tlv_leaf = (hi_ushort16)htons(tlv_leaf);
    packet_slicing = packet_slicing_for_send(&uncompressed_data, uncompressed_data_len, tlv_head->uc_branch, tlv_leaf);
    if(packet_slicing >= 1)
        uncompressed_data_len += (packet_slicing - 1)*4;//add tlv header number

    memmove(uncompressed_data + 4, uncompressed_data, uncompressed_data_len);//move backwards by 4 bytes
    uncompressed_data_len += 4;
    uncompressed_data[0] = tlv_head->uc_branch;
    uncompressed_data[1] = (tlv_leaf >> 8) & 0xff;
    uncompressed_data[2] = tlv_leaf & 0xff;
    if(wan_total_num > 0)
        uncompressed_data[3] = 0x00;
    else
        uncompressed_data[3] = 0x02;

    /* 3. compress the data */
    HI_OS_MEMSET_S(&compressed_data, sizeof(compressed_data), 0, sizeof(compressed_data));
    err = compress(compressed_data, (uLongf *)&compressed_data_len, uncompressed_data, uncompressed_data_len);
    if(err != Z_OK) 
        printf("Compress failed! err = %d\n", err);

    /* 4. add tlv header to the compressed data, the tlv header is c7 22 ff xx */
    struct_size = compressed_data_len;
    if(struct_size > 0 && struct_size <= 128)
    {
        *puc_changingmsglen = struct_size;
        HI_OS_MEMCPY_S(pv_outmsg, sizeof(compressed_data), compressed_data, *puc_changingmsglen);
        printf_oam_hex(pv_outmsg, *puc_changingmsglen);
    }
	else if(struct_size > 128 && struct_size <= (255 - 4))//langer than 128, need to slicing the packets
	{
		add_len = packet_slicing_for_send(&compressed_data, compressed_data_len, tlv_head->uc_branch, 0x22ff);
        add_len = (add_len - 1) * 4;
        pus_msglen = (hi_ushort16 *)(puc_changingmsglen  + 1);
        *pus_msglen = 0;
		*puc_changingmsglen = struct_size + add_len;//send msg will use this len value     
        HI_OS_MEMCPY_S(pv_outmsg, sizeof(compressed_data), compressed_data, *puc_changingmsglen); 
        printf_oam_hex(pv_outmsg, *puc_changingmsglen);
	}
	else if(struct_size > (255 - 4) && struct_size <= 1496)
	{

		add_len = packet_slicing_for_send(&compressed_data, compressed_data_len, tlv_head->uc_branch, 0x22ff);
        add_len = (add_len - 1) * 4;
		pus_msglen = (hi_ushort16 *)(puc_changingmsglen  + 1);
		*pus_msglen = struct_size + add_len;//send msg will use this len value
		*puc_changingmsglen = 0;
        HI_OS_MEMCPY_S(pv_outmsg, sizeof(compressed_data), compressed_data, *pus_msglen);
        printf_oam_hex(pv_outmsg, *pus_msglen);
	}
	else
	{
		return HI_RET_FAIL;
	}
   
    return HI_RET_SUCC;
}



#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

