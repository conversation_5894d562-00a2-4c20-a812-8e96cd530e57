#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "vs_ctcoam_sip_digital_map.h"



int vs_oam_sip_digital_map_to_ram(unsigned char *cfg, unsigned int max_len)
{
    int ret = 0;
    IgdVoiceGeneralAttrConfTab data;

    if (cfg == NULL)
        return HI_RET_INVALID_PARA;

    HI_OS_MEMSET_S(cfg, max_len, 0, max_len);
    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));

    data.ulBitmap |= VOICE_GENTRAL_ATTR_MASK_BIT12_DIGIT_MAP;
    ret = igdCmConfGet(IGD_VOICE_GENERAL_ATTR_TAB, (unsigned char *)&data, sizeof(data));
    if(ret != 0)
    {
        printf("[%s,%d] igdCmConfGet IGD_VOICE_GENERAL_ATTR_TAB ret : %d \r\n", __FUNCTION__, __LINE__, ret);
        return HI_RET_FAIL;
    }

    HI_OS_MEMCPY_S(cfg, max_len, data.aucDigitMap, max_len);
    cfg[max_len - 1] = '\0';

    return HI_RET_SUCC;
}

uint32_t vs_ctcoam_set_sip_digital_map(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0, data_len = 0;
    unsigned char cfg_data[1496], ram_data[1496];
    unsigned char cfg_change = 0;
    hi_ushort16 tlv_leaf = 0;
    hi_uchar8 tlv_length = 0;
    hi_uchar8 packets_num = 0;
    const unsigned char *p_data = (const unsigned char *)pv_inmsg;
    hi_ctcoam_tlv_s *tlv_head = (hi_ctcoam_tlv_s *)(pv_inmsg - 4);
    IgdVoiceGeneralAttrConfTab data;

    if (pv_inmsg == NULL)
        return HI_RET_INVALID_PARA;

    HI_OS_MEMSET_S(cfg_data, sizeof(cfg_data), 0, sizeof(cfg_data));
    HI_OS_MEMSET_S(ram_data, sizeof(ram_data), 0, sizeof(ram_data));
    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));

    //printf("\n*(p_data-4)=0x%x,*(p_data-3)=0x%x,*(p_data-2)=0x%x,*(p_data-1)=0x%x,*p_data=0x%x,*(p_data+1)=0x%x,*(p_data+2)=0x%x\n", *(p_data-4),*(p_data-3),*(p_data-2),*(p_data-1),*p_data,*(p_data+1),*(p_data+2));
    if(*p_data == 0 || *(p_data + 1) != 1)//not begin with the first packets
        return HI_RET_SUCC;

    packets_num = *p_data;
    tlv_leaf = tlv_head->us_leaf;
    tlv_leaf = (hi_ushort16)htons(tlv_leaf);
    tlv_length = tlv_head->uc_length;

    if(packets_num == 1 || tlv_length != 0x7f)//not need to packet slicing
    {
        data_len = tlv_length;
        HI_OS_MEMCPY_S(cfg_data, sizeof(cfg_data), p_data + 2, data_len);
        cfg_data[sizeof(cfg_data)-1] = '\0';
    }
    else
    {
        data_len = packet_slicing_for_receive_digital_map(cfg_data, pv_inmsg, 1496, tlv_head->uc_branch, tlv_leaf);
        cfg_data[sizeof(cfg_data)-1] = '\0';
        if(data_len <= 0)
            return HI_RET_FAIL;
    }

    /* compare cfg */
    vs_oam_sip_digital_map_to_ram(ram_data, sizeof(ram_data));
    if(memcmp(ram_data, cfg_data, sizeof(ram_data)) != 0)
        cfg_change = 1;

    /* if cfg change, set to mib */
    if(cfg_change == 1)
    {
        data.ulBitmap |= VOICE_GENTRAL_ATTR_MASK_BIT1_DIGIT_MAP_ENABLE|VOICE_GENTRAL_ATTR_MASK_BIT12_DIGIT_MAP;
        HI_OS_STRCPY_S((char *)data.aucDigitMap, sizeof(data.aucDigitMap), (char *)cfg_data);
        data.ucDigitMapEnable = VOICE_DIGIT_MAP_ENABLE;
        ret = igdCmConfSet(IGD_VOICE_GENERAL_ATTR_TAB, (unsigned char *)&data, sizeof(data));
        if(ret != 0)
        {
            printf("[%s,%d] igdCmConfSet IGD_VOICE_GENERAL_ATTR_TAB ret : %d \r\n", __FUNCTION__, __LINE__, ret);
            return HI_RET_FAIL;
        }
    }

    return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

