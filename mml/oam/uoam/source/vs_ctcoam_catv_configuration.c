#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "vs_ctcoam_catv_configuration.h"


uint32_t vs_ctcoam_get_catv_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
	vs_ctcoam_catv_configuration_s *catv_cfg =HI_NULL;
    IgdCatvTab IgdCatvObject;
    int ret;
		
	if (pv_outmsg == HI_NULL)
		return HI_RET_INVALID_PARA;
	catv_cfg = (vs_ctcoam_catv_configuration_s *)pv_outmsg;

    memset_s((int8_t *)&IgdCatvObject, sizeof(IgdCatvObject), 0,sizeof(IgdCatvObject));
    
	ret = igdCmConfGet(IGD_CATV_ATTR_TAB, (uint8_t *)&IgdCatvObject, sizeof(IgdCatvObject));
	if (ret != HI_RET_SUCC) {
		printf("igdCmConfGet IGD_CATV_ATTR_TAB ret : %d \r\n", ret);
        return HI_RET_INVALID_PARA;
	}
	catv_cfg->catvEnable = IgdCatvObject.uc_catv_enable;

    printf("[%s] catv_cfg->catvEnable=%d\n", __FUNCTION__, catv_cfg->catvEnable);
    return HI_RET_SUCC;
}

uint32_t vs_ctcoam_set_catv_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
	vs_ctcoam_catv_configuration_s *date_cfg = HI_NULL;
    IgdCatvTab IgdCatvObject;
    int ret;

	if(pv_inmsg == HI_NULL)
		return HI_RET_INVALID_PARA;
	
	date_cfg = (vs_ctcoam_catv_configuration_s *)pv_inmsg;

	printf("catv configuration enable:%d\n", date_cfg->catvEnable);

    memset_s((int8_t *)&IgdCatvObject, sizeof(IgdCatvObject), 0,sizeof(IgdCatvObject));
    
	ret = igdCmConfGet(IGD_CATV_ATTR_TAB, (uint8_t *)&IgdCatvObject, sizeof(IgdCatvObject));
	if (ret != HI_RET_SUCC) {
		printf("igdCmConfGet IGD_CATV_ATTR_TAB ret : %d \r\n", ret);
        return HI_RET_INVALID_PARA;
	}

    IgdCatvObject.uc_catv_enable = date_cfg->catvEnable;
	ret = igdCmConfSet(IGD_CATV_ATTR_TAB, (uint8_t *)&IgdCatvObject, sizeof(IgdCatvObject));
	if (ret != HI_RET_SUCC) {
		printf("igdCmConfSet IGD_CATV_ATTR_TAB ret : %d \r\n", ret);
        return HI_RET_INVALID_PARA;
	}
    return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

