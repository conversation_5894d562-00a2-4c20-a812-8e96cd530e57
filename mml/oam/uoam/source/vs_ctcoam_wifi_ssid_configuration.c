#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "vs_ctcoam_wifi_ssid_configuration.h"
#include <regex.h>

void printf_wifi_data(vs_ctcoam_wifi_ssid_configuration_s *data)
{
    unsigned char printf_enable = 0;
    unsigned char i = 0;
    if(printf_enable)
    {
        printf("\n=========================== wifi ssid data ===========================\n");
        printf("ssid_name = %s\n", data->ssid_name);
        printf("ssid_enable = %d\n", data->ssid_enable);
        printf("ssid_hide_enable = %d\n", data->ssid_hide_enable);
        printf("auth_mode = %d\n", data->auth_mode);
        printf("encryption_type = %d\n", data->encryption_type);
        printf("preshared_key = %s\n", data->preshared_key);
        printf("wpa_update_key_interval = %d\n", data->wpa_update_key_interval);
        printf("radius_server_ip_type = %d\n", data->radius_server_ip_type);
        printf("radius_server_ip_len = %d\n", data->radius_server_ip_len);
        printf("radius_server_ip = %s\n", data->radius_server_ip);
        printf("radius_server_ip = ");
        for(i = 0; i < 255; i++)
        {
            printf(" 0x%x,", data->radius_server_ip[i]);
        }printf("\n");
        printf("radius_server_ip_prefix_len = %d\n", data->radius_server_ip_prefix_len);
        printf("radius_server_port = %d\n", data->radius_server_port);
        printf("radius_server_key = %s\n", data->radius_server_key);
        printf("wep_encryption_level = %d\n", data->wep_encryption_level);
        printf("wep_key_index = %d\n", data->wep_key_index);
        printf("wep_key1 = ");
        for(i = 0; i < 32; i++)
        {
            printf(" 0x%x,", data->wep_key1[i]);
        }printf("\n");
        printf("wep_key2 = ");
        for(i = 0; i < 32; i++)
        {
            printf(" 0x%x,", data->wep_key2[i]);
        }printf("\n");
        printf("wep_key3 = ");
        for(i = 0; i < 32; i++)
        {
            printf(" 0x%x,", data->wep_key3[i]);
        }printf("\n");
        printf("wep_key4 = ");
        for(i = 0; i < 32; i++)
        {
            printf(" 0x%x,", data->wep_key4[i]);
        }printf("\n");
        printf("======================================================================\n");
    }
}

hi_int32 oam_ssid_check_ip_address(const char *ip) 
{
    struct sockaddr_in sa;
    struct sockaddr_in6 sa6;
    char ip_copy[40];
    char *zone;
    regex_t regex;
    int ret;

    strncpy(ip_copy, ip, sizeof(ip_copy) - 1);
    ip_copy[sizeof(ip_copy) - 1] = '\0';

    // Check for zone index
    zone = strchr(ip_copy, '%');
    if (zone != NULL) 
    {
        *zone = '\0'; // Null-terminate the IP part
        zone++; // Move to the zone part

        if (inet_pton(AF_INET, ip_copy, &(sa.sin_addr)) == 1) 
        {
            return VS_IP_TYPE_IPV6Z;
        }

        else if (inet_pton(AF_INET6, ip_copy, &(sa6.sin6_addr)) == 1) 
        {
            return VS_IP_TYPE_IPV6Z;
        }
    } 
    else 
    {
        if (inet_pton(AF_INET, ip, &(sa.sin_addr)) == 1) 
        {
            return VS_IP_TYPE_IPV4;
        }
        else if (inet_pton(AF_INET6, ip, &(sa6.sin6_addr)) == 1) 
        {
            return VS_IP_TYPE_IPV6;
        }
    }

    // Regular expression for a valid domain name
    const char *domain_regex = "^[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\\.?$";

    ret = regcomp(&regex, domain_regex, REG_EXTENDED);
    if (ret) 
    {
        fprintf(stderr, "Could not compile regex\n");
        return VS_IP_TYPE_UNKNOWN;
    }

    ret = regexec(&regex, ip, 0, NULL, 0);
    regfree(&regex);

    if (!ret) 
    {
        return VS_IP_TYPE_DOMAIN;
    }
    return VS_IP_TYPE_UNKNOWN;
}

int vs_oam_wifi_ssid_cfg_to_ram(vs_ctcoam_wifi_ssid_configuration_s *cfg, hi_uchar8 ssid_index)
{
    int ret = 0;
    IgdWLANSsidAttrCfgTab data;
#if defined(CONFIG_NC_WPA_ENTERPRISE)
    unsigned int u32Val = 0;
    char ipv6_char[48];
#endif

    if (cfg == NULL)
        return HI_RET_INVALID_PARA;

    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));
    HI_OS_MEMSET_S(cfg, sizeof(vs_ctcoam_wifi_ssid_configuration_s), 0, sizeof(vs_ctcoam_wifi_ssid_configuration_s));

    data.ulSSIDIndex = ssid_index;
    data.ulBitmap1 = WLAN_SSID_CFG_ATTR_MASK_SSIDSTATUS;

    //The WiFi has not started initialization yet. Here, check if the crond process number file exists to determine if the WiFi initialization has ended
    if (access("/var/run/crond.pid", F_OK) != 0)
        return HI_RET_SUCC;

#ifdef CONFIG_PLATFORM_OPENWRT
    ret = HI_IPC_CALL("hi_sml_igd_wlan_ssid_cfg_attr_tab_get", &data);
#else
    ret = igdCmConfGet(IGD_WLAN_SSID_CFG_ATTR_TAB,(unsigned char *)&data,sizeof(data));
#endif
    if(ret != 0)
    {
        printf("ret igdCmConfGet IGD_WLAN_SSID_CFG_ATTR_TAB ret %d \r\n", ret);
        return HI_RET_FAIL;
    }

    HI_OS_MEMCPY_S(cfg->ssid_name, sizeof(cfg->ssid_name), data.aucSSIDName, sizeof(cfg->ssid_name));
    cfg->ssid_enable = data.ucSSIDEnable;
    cfg->ssid_hide_enable = data.ucSSIDHide;

    //printf("ssid_index=%d, ucSSIDEnable=%d, aucSSIDName=%s, cfg->ssid_name=%s, ucAuthType=%d\n", ssid_index, data.ucSSIDEnable, data.aucSSIDName, cfg->ssid_name, data.ucAuthType);
    
    switch(data.ucAuthType)
    {
        case WLAN_SSID_NC_AUTH_TYPE_OPEN:
            cfg->auth_mode = VS_AUTH_MODE_OPEN;
            //cfg->wpa_update_key_interval = data.ulWPAUpdateTime;
            if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_WEP)
            {
                cfg->encryption_type = VS_ENCRYPTION_TYPE_WEP;
                if (data.ucWEPEncryptionLevel == WLAN_SSID_WEP_ENC_LEVEL_104BIT)
                {
                    cfg->wep_encryption_level = VS_WEB_ENCRYPTION_LEVEL_128;
                    cfg->wep_key_index = data.ucWEPKeyIndex;
                    HI_OS_MEMCPY_S(cfg->wep_key1, 32, data.aucWEPKey[0], 32);
                    HI_OS_MEMCPY_S(cfg->wep_key2, 32, data.aucWEPKey[1], 32);
                    HI_OS_MEMCPY_S(cfg->wep_key3, 32, data.aucWEPKey[2], 32);
                    HI_OS_MEMCPY_S(cfg->wep_key4, 32, data.aucWEPKey[3], 32);
                    //printf("wep_key1 = %s, wep_key2 = %s, wep_key3 = %s, wep_key4 = %s\n", cfg->wep_key1, cfg->wep_key2, cfg->wep_key3, cfg->wep_key4);
                }
                else if (data.ucWEPEncryptionLevel == WLAN_SSID_WEP_ENC_LEVEL_40BIT)
                {
                    cfg->wep_encryption_level = VS_WEB_ENCRYPTION_LEVEL_64;
                    cfg->wep_key_index = data.ucWEPKeyIndex;
                    HI_OS_MEMCPY_S(cfg->wep_key1, 32, data.aucWEPKey[0], 32);
                    HI_OS_MEMCPY_S(cfg->wep_key2, 32, data.aucWEPKey[1], 32);
                    HI_OS_MEMCPY_S(cfg->wep_key3, 32, data.aucWEPKey[2], 32);
                    HI_OS_MEMCPY_S(cfg->wep_key4, 32, data.aucWEPKey[3], 32);
                    //printf("wep_key1 = %s, wep_key2 = %s, wep_key3 = %s, wep_key4 = %s\n", cfg->wep_key1, cfg->wep_key2, cfg->wep_key3, cfg->wep_key4);
                }
                else
                {
                    cfg->encryption_type = VS_ENCRYPTION_TYPE_NONE;
                }
            }
            else
            {
                cfg->encryption_type = VS_ENCRYPTION_TYPE_NONE;
            }
            break;
        case WLAN_SSID_NC_AUTH_TYPE_SHARED:
            cfg->auth_mode = VS_AUTH_MODE_SHARED;
            cfg->encryption_type = VS_ENCRYPTION_TYPE_WEP;
            //cfg->wpa_update_key_interval = data.ulWPAUpdateTime;
            if (data.ucWEPEncryptionLevel == WLAN_SSID_WEP_ENC_LEVEL_104BIT)
            {
                cfg->wep_encryption_level = VS_WEB_ENCRYPTION_LEVEL_128;
                cfg->wep_key_index = data.ucWEPKeyIndex;
                HI_OS_MEMCPY_S(cfg->wep_key1, 32, data.aucWEPKey[0], 32);
                HI_OS_MEMCPY_S(cfg->wep_key2, 32, data.aucWEPKey[1], 32);
                HI_OS_MEMCPY_S(cfg->wep_key3, 32, data.aucWEPKey[2], 32);
                HI_OS_MEMCPY_S(cfg->wep_key4, 32, data.aucWEPKey[3], 32);
                //printf("wep_key1 = %s, wep_key2 = %s, wep_key3 = %s, wep_key4 = %s\n", cfg->wep_key1, cfg->wep_key2, cfg->wep_key3, cfg->wep_key4);            
            }
            else if (data.ucWEPEncryptionLevel == WLAN_SSID_WEP_ENC_LEVEL_40BIT)
            {
                cfg->wep_encryption_level = VS_WEB_ENCRYPTION_LEVEL_64;
                cfg->wep_key_index = data.ucWEPKeyIndex;
                HI_OS_MEMCPY_S(cfg->wep_key1, 32, data.aucWEPKey[0], 32);
                HI_OS_MEMCPY_S(cfg->wep_key2, 32, data.aucWEPKey[1], 32);
                HI_OS_MEMCPY_S(cfg->wep_key3, 32, data.aucWEPKey[2], 32);
                HI_OS_MEMCPY_S(cfg->wep_key4, 32, data.aucWEPKey[3], 32);
                //printf("wep_key1 = %s, wep_key2 = %s, wep_key3 = %s, wep_key4 = %s\n", cfg->wep_key1, cfg->wep_key2, cfg->wep_key3, cfg->wep_key4);
            }
            else
            {
                cfg->encryption_type = VS_ENCRYPTION_TYPE_NONE;
            }
            break;
        case WLAN_SSID_NC_AUTH_TYPE_WPAPSK:
            cfg->auth_mode = VS_AUTH_MODE_WPAPSK;
            cfg->wpa_update_key_interval = data.ulWPAUpdateTime;
            if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_TKIP)
            {
                cfg->encryption_type = VS_ENCRYPTION_TYPE_TKIP;
            }
            else if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_AES)
            {
                cfg->encryption_type = VS_ENCRYPTION_TYPE_AES;
            }
            else
            {
                cfg->encryption_type = VS_ENCRYPTION_TYPE_TKIP_AES;
            }
            HI_OS_MEMCPY_S(cfg->preshared_key, 64, data.aucPreSharedKey[0], 64);
            //printf("cfg->preshared_key = %s\n", cfg->preshared_key);
            break;
        case WLAN_SSID_NC_AUTH_TYPE_WPA2PSK:
            cfg->auth_mode = VS_AUTH_MODE_WPA2PSK;
            cfg->wpa_update_key_interval = data.ulWPAUpdateTime;
            if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_TKIP)
            {
                cfg->encryption_type = VS_ENCRYPTION_TYPE_TKIP;
            }
            else if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_AES)
            {
                cfg->encryption_type = VS_ENCRYPTION_TYPE_AES;
            }
            else
            {
                cfg->encryption_type = VS_ENCRYPTION_TYPE_TKIP_AES;
            }
            HI_OS_MEMCPY_S(cfg->preshared_key, 64, data.aucPreSharedKey[0], 64);
            //printf("cfg->preshared_key = %s\n", cfg->preshared_key);
            break; 
        case WLAN_SSID_NC_AUTH_TYPE_WPA_WPA2_PSK:
            cfg->auth_mode = VS_AUTH_MODE_WPAPSK_WPA2PSK;
            cfg->wpa_update_key_interval = data.ulWPAUpdateTime;
            if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_TKIP)
            {
                cfg->encryption_type = VS_ENCRYPTION_TYPE_TKIP;
            }
            else if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_AES)
            {
                cfg->encryption_type = VS_ENCRYPTION_TYPE_AES;
            }
            else
            {
                cfg->encryption_type = VS_ENCRYPTION_TYPE_TKIP_AES;
            }
            HI_OS_MEMCPY_S(cfg->preshared_key, 64, data.aucPreSharedKey[0], 64);
            //printf("cfg->preshared_key = %s\n", cfg->preshared_key);
            break;   
        case WLAN_SSID_NC_AUTH_TYPE_WPA3SAE:
            cfg->auth_mode = VS_AUTH_MODE_WPA3PSK;
            cfg->wpa_update_key_interval = data.ulWPAUpdateTime;
            cfg->encryption_type = VS_ENCRYPTION_TYPE_TKIP_AES;
            HI_OS_MEMCPY_S(cfg->preshared_key, 64, data.aucPreSharedKey[0], 64);
            //printf("cfg->preshared_key = %s\n", cfg->preshared_key);
            break;
        case WLAN_SSID_NC_AUTH_TYPE_WPA2_WPA3:
            cfg->auth_mode = VS_AUTH_MODE_WPA2PSK_WPA3PSK;
            cfg->wpa_update_key_interval = data.ulWPAUpdateTime;
            cfg->encryption_type = VS_ENCRYPTION_TYPE_TKIP_AES;
            HI_OS_MEMCPY_S(cfg->preshared_key, 64, data.aucPreSharedKey[0], 64);
            //printf("cfg->preshared_key = %s\n", cfg->preshared_key);
            break;
#if defined(CONFIG_NC_WPA_ENTERPRISE)
        case WLAN_SSID_NC_AUTH_TYPE_WPA_ENTERPRISE:
            cfg->encryption_type = VS_AUTH_MODE_WPA;
            if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_TKIP)
            {
                cfg->encryption_type = VS_ENCRYPTION_TYPE_TKIP;
            }
            else if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_AES)
            {
                cfg->encryption_type = VS_ENCRYPTION_TYPE_AES;
            }
            else
            {
                cfg->encryption_type = VS_ENCRYPTION_TYPE_TKIP_AES;
            }

            cfg->radius_server_ip_type = oam_ssid_check_ip_address(data.aucRadiusServerIP);
            cfg->radius_server_port = data.ulRadiusServerPort;
            switch (cfg->radius_server_ip_type)
            {
                case VS_IP_TYPE_IPV4:
                    cfg->radius_server_ip_len = 4;
                    u32Val = igdCmApiChartoIp(data.aucRadiusServerIP);
                    HI_OS_MEMCPY_S(cfg->radius_server_ip, 4, &u32Val, 4);
                    break;
                case VS_IP_TYPE_IPV6:
                    cfg->radius_server_ip_len = 16;
                    igdCmApiChartoIpv6((void*)data.aucRadiusServerIP, ipv6_char);
                    HI_OS_MEMCPY_S(cfg->radius_server_ip, 16, ipv6_char, 16); 
                    break;   
                case VS_IP_TYPE_IPV4Z:
                    u32Val = igdCmApiChartoIp(data.aucRadiusServerIP);
                    HI_OS_MEMCPY_S(cfg->radius_server_ip, 4, &u32Val, 4);
                    break;
                case VS_IP_TYPE_IPV6Z:
                    cfg->radius_server_ip_len = 20;
                    igdCmApiChartoIpv6((void*)data.aucRadiusServerIP, ipv6_char);
                    HI_OS_MEMCPY_S(cfg->radius_server_ip, 16, ipv6_char, 16); 
                    break;  
                case VS_IP_TYPE_DOMAIN:
                    cfg->radius_server_ip_len = 64;
                    HI_OS_MEMCPY_S(cfg->radius_server_ip, 64, data.aucRadiusServerIP, 64); 
                    break;           
                default:
                    printf("cfg->radius_server_ip_type[%d], unknown ip type\n", cfg->radius_server_ip_type);
                    break;
            }
            HI_OS_MEMCPY_S(cfg->radius_server_key, 32, data.aucRadiusSharedKey, 32);
            //printf("cfg->radius_server_ip=%s, cfg->radius_server_key=%s\n", cfg->radius_server_ip, cfg->radius_server_key);
            break;
        case WLAN_SSID_NC_AUTH_TYPE_WPA2_ENTERPRISE:
            cfg->encryption_type = VS_AUTH_MODE_WPA2;
            if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_TKIP)
            {
                cfg->encryption_type = VS_ENCRYPTION_TYPE_TKIP;
            }
            else if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_AES)
            {
                cfg->encryption_type = VS_ENCRYPTION_TYPE_AES;
            }
            else
            {
                cfg->encryption_type = VS_ENCRYPTION_TYPE_TKIP_AES;
            }
            cfg->radius_server_ip_type = oam_ssid_check_ip_address(data.aucRadiusServerIP);
            cfg->radius_server_port = data.ulRadiusServerPort;
            switch (cfg->radius_server_ip_type)
            {
                case VS_IP_TYPE_IPV4:
                    cfg->radius_server_ip_len = 4;
                    u32Val = igdCmApiChartoIp(data.aucRadiusServerIP);
                    HI_OS_MEMCPY_S(cfg->radius_server_ip, 4, &u32Val, 4);
                    break;
                case VS_IP_TYPE_IPV6:
                    cfg->radius_server_ip_len = 16;
                    igdCmApiChartoIpv6((void*)data.aucRadiusServerIP, ipv6_char);
                    HI_OS_MEMCPY_S(cfg->radius_server_ip, 16, ipv6_char, 16); 
                    break;   
                case VS_IP_TYPE_IPV4Z:
                    u32Val = igdCmApiChartoIp(data.aucRadiusServerIP);
                    HI_OS_MEMCPY_S(cfg->radius_server_ip, 4, &u32Val, 4);
                    break;
                case VS_IP_TYPE_IPV6Z:
                    cfg->radius_server_ip_len = 20;
                    igdCmApiChartoIpv6((void*)data.aucRadiusServerIP, ipv6_char);
                    HI_OS_MEMCPY_S(cfg->radius_server_ip, 16, ipv6_char, 16); 
                    break;  
                case VS_IP_TYPE_DOMAIN:
                    cfg->radius_server_ip_len = 64;
                    HI_OS_MEMCPY_S(cfg->radius_server_ip, 64, data.aucRadiusServerIP, 64); 
                    break;        
                default:
                    printf("cfg->radius_server_ip_type[%d], unknown ip type\n", cfg->radius_server_ip_type);
                    break;
            }
            HI_OS_MEMCPY_S(cfg->radius_server_key, 32, data.aucRadiusSharedKey, 32);
            //printf("cfg->radius_server_ip=%s, cfg->radius_server_key=%s\n", cfg->radius_server_ip, cfg->radius_server_key);
            break;
        case WLAN_SSID_NC_AUTH_TYPE_WPA_WPA2_ENTERPRISE:
            cfg->encryption_type = VS_AUTH_MODE_WPA_WPA2;
            if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_TKIP)
            {
                cfg->encryption_type = VS_ENCRYPTION_TYPE_TKIP;
            }
            else if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_AES)
            {
                cfg->encryption_type = VS_ENCRYPTION_TYPE_AES;
            }
            else
            {
                cfg->encryption_type = VS_ENCRYPTION_TYPE_TKIP_AES;
            }
            cfg->radius_server_ip_type = oam_ssid_check_ip_address(data.aucRadiusServerIP);
            cfg->radius_server_port = data.ulRadiusServerPort;
            switch (cfg->radius_server_ip_type)
            {
                case VS_IP_TYPE_IPV4:
                    cfg->radius_server_ip_len = 4;
                    u32Val = igdCmApiChartoIp(data.aucRadiusServerIP);
                    HI_OS_MEMCPY_S(cfg->radius_server_ip, 4, &u32Val, 4);
                    break;
                case VS_IP_TYPE_IPV6:
                    cfg->radius_server_ip_len = 16;
                    igdCmApiChartoIpv6((void*)data.aucRadiusServerIP, ipv6_char);
                    HI_OS_MEMCPY_S(cfg->radius_server_ip, 16, ipv6_char, 16); 
                    break;   
                case VS_IP_TYPE_IPV4Z:
                    u32Val = igdCmApiChartoIp(data.aucRadiusServerIP);
                    HI_OS_MEMCPY_S(cfg->radius_server_ip, 4, &u32Val, 4);
                    break;
                case VS_IP_TYPE_IPV6Z:
                    cfg->radius_server_ip_len = 20;
                    igdCmApiChartoIpv6((void*)data.aucRadiusServerIP, ipv6_char);
                    HI_OS_MEMCPY_S(cfg->radius_server_ip, 16, ipv6_char, 16); 
                    break;  
                case VS_IP_TYPE_DOMAIN:
                    cfg->radius_server_ip_len = 64;
                    HI_OS_MEMCPY_S(cfg->radius_server_ip, 64, data.aucRadiusServerIP, 64); 
                    break;         
                default:
                    printf("cfg->radius_server_ip_type[%d], unknown ip type\n", cfg->radius_server_ip_type);
                    break;
            }
            HI_OS_MEMCPY_S(cfg->radius_server_key, 32, data.aucRadiusSharedKey, 32);
            //printf("cfg->radius_server_ip=%s, cfg->radius_server_key=%s\n", cfg->radius_server_ip, cfg->radius_server_key);
            break;
#endif
        default:
            printf("data.ucAuthType[%d], unknown auth type\n", data.ucAuthType);
            break;
    }
    printf_wifi_data(cfg);
    return HI_RET_SUCC;
}

//epon ssid_index: 1-4=>2.4G  5-8=>5G
hi_int32 vs_oam_ssid_config_get(hi_uchar8 ssid_index, vs_ctcoam_wifi_ssid_configuration_s *ssidData)
{
    IgdWLANSsidAttrCfgTab data;
    int ret;
#if defined(CONFIG_NC_WPA_ENTERPRISE)
    unsigned int u32Val = 0;
    char ipv6_char[48];
#endif

    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));
    
    data.ulSSIDIndex = ssid_index;
    data.ulBitmap1 = WLAN_SSID_CFG_ATTR_MASK_SSIDSTATUS;

    //The WiFi has not started initialization yet. Here, check if the crond process number file exists to determine if the WiFi initialization has ended
    if (access("/var/run/crond.pid", F_OK) != 0)
        return HI_RET_SUCC;

#ifdef CONFIG_PLATFORM_OPENWRT
    ret = HI_IPC_CALL("hi_sml_igd_wlan_ssid_cfg_attr_tab_get", &data);
#else
    ret = igdCmConfGet(IGD_WLAN_SSID_CFG_ATTR_TAB,(unsigned char *)&data,sizeof(data));
#endif
    if(ret != 0)
    {
        printf("ret igdCmConfGet IGD_WLAN_SSID_CFG_ATTR_TAB ret %d \r\n", ret);
        return HI_RET_FAIL;
    }

    HI_OS_MEMCPY_S(ssidData->ssid_name, sizeof(ssidData->ssid_name), data.aucSSIDName, sizeof(ssidData->ssid_name));
    ssidData->ssid_enable = data.ucSSIDEnable;
    ssidData->ssid_hide_enable = data.ucSSIDHide;

    //printf("ssid_index=%d, ucSSIDEnable=%d, aucSSIDName=%s, ssidData->ssid_name=%s, ucAuthType=%d\n", ssid_index, data.ucSSIDEnable, data.aucSSIDName, ssidData->ssid_name, data.ucAuthType);
    
    switch(data.ucAuthType)
    {
        case WLAN_SSID_NC_AUTH_TYPE_OPEN:
            ssidData->auth_mode = VS_AUTH_MODE_OPEN;
            ssidData->wpa_update_key_interval = data.ulWPAUpdateTime;
            if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_WEP)
            {
                ssidData->encryption_type = VS_ENCRYPTION_TYPE_WEP;
                if (data.ucWEPEncryptionLevel == WLAN_SSID_WEP_ENC_LEVEL_104BIT)
                {
                    ssidData->wep_encryption_level = VS_WEB_ENCRYPTION_LEVEL_128;
                    ssidData->wep_key_index = data.ucWEPKeyIndex;
                    HI_OS_MEMCPY_S(ssidData->wep_key1, 32, data.aucWEPKey[0], 32);
                    HI_OS_MEMCPY_S(ssidData->wep_key2, 32, data.aucWEPKey[1], 32);
                    HI_OS_MEMCPY_S(ssidData->wep_key3, 32, data.aucWEPKey[2], 32);
                    HI_OS_MEMCPY_S(ssidData->wep_key4, 32, data.aucWEPKey[3], 32);
                    //printf("wep_key1 = %s, wep_key2 = %s, wep_key3 = %s, wep_key4 = %s\n", ssidData->wep_key1, ssidData->wep_key2, ssidData->wep_key3, ssidData->wep_key4);
                }
                else if (data.ucWEPEncryptionLevel == WLAN_SSID_WEP_ENC_LEVEL_40BIT)
                {
                    ssidData->wep_encryption_level = VS_WEB_ENCRYPTION_LEVEL_64;
                    ssidData->wep_key_index = data.ucWEPKeyIndex;
                    HI_OS_MEMCPY_S(ssidData->wep_key1, 32, data.aucWEPKey[0], 32);
                    HI_OS_MEMCPY_S(ssidData->wep_key2, 32, data.aucWEPKey[1], 32);
                    HI_OS_MEMCPY_S(ssidData->wep_key3, 32, data.aucWEPKey[2], 32);
                    HI_OS_MEMCPY_S(ssidData->wep_key4, 32, data.aucWEPKey[3], 32);
                    //printf("wep_key1 = %s, wep_key2 = %s, wep_key3 = %s, wep_key4 = %s\n", ssidData->wep_key1, ssidData->wep_key2, ssidData->wep_key3, ssidData->wep_key4);
                }
                else
                {
                    ssidData->encryption_type = VS_ENCRYPTION_TYPE_NONE;
                }
            }
            else
            {
                ssidData->encryption_type = VS_ENCRYPTION_TYPE_NONE;
            }
            break;
        case WLAN_SSID_NC_AUTH_TYPE_SHARED:
            ssidData->auth_mode = VS_AUTH_MODE_SHARED;
            ssidData->encryption_type = VS_ENCRYPTION_TYPE_WEP;
            ssidData->wpa_update_key_interval = data.ulWPAUpdateTime;
            if (data.ucWEPEncryptionLevel == WLAN_SSID_WEP_ENC_LEVEL_104BIT)
            {
                ssidData->wep_encryption_level = VS_WEB_ENCRYPTION_LEVEL_128;
                ssidData->wep_key_index = data.ucWEPKeyIndex;
                HI_OS_MEMCPY_S(ssidData->wep_key1, 32, data.aucWEPKey[0], 32);
                HI_OS_MEMCPY_S(ssidData->wep_key2, 32, data.aucWEPKey[1], 32);
                HI_OS_MEMCPY_S(ssidData->wep_key3, 32, data.aucWEPKey[2], 32);
                HI_OS_MEMCPY_S(ssidData->wep_key4, 32, data.aucWEPKey[3], 32);
                //printf("wep_key1 = %s, wep_key2 = %s, wep_key3 = %s, wep_key4 = %s\n", ssidData->wep_key1, ssidData->wep_key2, ssidData->wep_key3, ssidData->wep_key4);            
            }
            else if (data.ucWEPEncryptionLevel == WLAN_SSID_WEP_ENC_LEVEL_40BIT)
            {
                ssidData->wep_encryption_level = VS_WEB_ENCRYPTION_LEVEL_64;
                ssidData->wep_key_index = data.ucWEPKeyIndex;
                HI_OS_MEMCPY_S(ssidData->wep_key1, 32, data.aucWEPKey[0], 32);
                HI_OS_MEMCPY_S(ssidData->wep_key2, 32, data.aucWEPKey[1], 32);
                HI_OS_MEMCPY_S(ssidData->wep_key3, 32, data.aucWEPKey[2], 32);
                HI_OS_MEMCPY_S(ssidData->wep_key4, 32, data.aucWEPKey[3], 32);
                //printf("wep_key1 = %s, wep_key2 = %s, wep_key3 = %s, wep_key4 = %s\n", ssidData->wep_key1, ssidData->wep_key2, ssidData->wep_key3, ssidData->wep_key4);
            }
            else
            {
                ssidData->encryption_type = VS_ENCRYPTION_TYPE_NONE;
            }
            break;
        case WLAN_SSID_NC_AUTH_TYPE_WPAPSK:
            ssidData->auth_mode = VS_AUTH_MODE_WPAPSK;
            ssidData->wpa_update_key_interval = data.ulWPAUpdateTime;
            if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_TKIP)
            {
                ssidData->encryption_type = VS_ENCRYPTION_TYPE_TKIP;
            }
            else if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_AES)
            {
                ssidData->encryption_type = VS_ENCRYPTION_TYPE_AES;
            }
            else
            {
                ssidData->encryption_type = VS_ENCRYPTION_TYPE_TKIP_AES;
            }
            HI_OS_MEMCPY_S(ssidData->preshared_key, 64, data.aucPreSharedKey[0], 64);
            //printf("ssidData->preshared_key = %s\n", ssidData->preshared_key);
            break;
        case WLAN_SSID_NC_AUTH_TYPE_WPA2PSK:
            ssidData->auth_mode = VS_AUTH_MODE_WPA2PSK;
            ssidData->wpa_update_key_interval = data.ulWPAUpdateTime;
            if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_TKIP)
            {
                ssidData->encryption_type = VS_ENCRYPTION_TYPE_TKIP;
            }
            else if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_AES)
            {
                ssidData->encryption_type = VS_ENCRYPTION_TYPE_AES;
            }
            else
            {
                ssidData->encryption_type = VS_ENCRYPTION_TYPE_TKIP_AES;
            }
            HI_OS_MEMCPY_S(ssidData->preshared_key, 64, data.aucPreSharedKey[0], 64);
            //printf("ssidData->preshared_key = %s\n", ssidData->preshared_key);
            break; 
        case WLAN_SSID_NC_AUTH_TYPE_WPA_WPA2_PSK:
            ssidData->auth_mode = VS_AUTH_MODE_WPAPSK_WPA2PSK;
            ssidData->wpa_update_key_interval = data.ulWPAUpdateTime;
            if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_TKIP)
            {
                ssidData->encryption_type = VS_ENCRYPTION_TYPE_TKIP;
            }
            else if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_AES)
            {
                ssidData->encryption_type = VS_ENCRYPTION_TYPE_AES;
            }
            else
            {
                ssidData->encryption_type = VS_ENCRYPTION_TYPE_TKIP_AES;
            }
            HI_OS_MEMCPY_S(ssidData->preshared_key, 64, data.aucPreSharedKey[0], 64);
            //printf("ssidData->preshared_key = %s\n", ssidData->preshared_key);
            break;   
        case WLAN_SSID_NC_AUTH_TYPE_WPA3SAE:
            ssidData->auth_mode = VS_AUTH_MODE_WPA3PSK;
            ssidData->wpa_update_key_interval = data.ulWPAUpdateTime;
            ssidData->encryption_type = VS_ENCRYPTION_TYPE_TKIP_AES;
            HI_OS_MEMCPY_S(ssidData->preshared_key, 64, data.aucPreSharedKey[0], 64);
            //printf("ssidData->preshared_key = %s\n", ssidData->preshared_key);
            break;
        case WLAN_SSID_NC_AUTH_TYPE_WPA2_WPA3:
            ssidData->auth_mode = VS_AUTH_MODE_WPA2PSK_WPA3PSK;
            ssidData->wpa_update_key_interval = data.ulWPAUpdateTime;
            ssidData->encryption_type = VS_ENCRYPTION_TYPE_TKIP_AES;
            HI_OS_MEMCPY_S(ssidData->preshared_key, 64, data.aucPreSharedKey[0], 64);
            //printf("ssidData->preshared_key = %s\n", ssidData->preshared_key);
            break;
#if defined(CONFIG_NC_WPA_ENTERPRISE)
        case WLAN_SSID_NC_AUTH_TYPE_WPA_ENTERPRISE:
            ssidData->encryption_type = VS_AUTH_MODE_WPA;
            if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_TKIP)
            {
                ssidData->encryption_type = VS_ENCRYPTION_TYPE_TKIP;
            }
            else if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_AES)
            {
                ssidData->encryption_type = VS_ENCRYPTION_TYPE_AES;
            }
            else
            {
                ssidData->encryption_type = VS_ENCRYPTION_TYPE_TKIP_AES;
            }

            ssidData->radius_server_ip_type = oam_ssid_check_ip_address(data.aucRadiusServerIP);
            ssidData->radius_server_port = data.ulRadiusServerPort;
            switch (ssidData->radius_server_ip_type)
            {
                case VS_IP_TYPE_IPV4:
                    ssidData->radius_server_ip_len = 4;
                    u32Val = igdCmApiChartoIp(data.aucRadiusServerIP);
                    HI_OS_MEMCPY_S(ssidData->radius_server_ip, 4, &u32Val, 4);
                    break;
                case VS_IP_TYPE_IPV6:
                    ssidData->radius_server_ip_len = 16;
                    igdCmApiChartoIpv6((void*)data.aucRadiusServerIP, ipv6_char);
                    HI_OS_MEMCPY_S(ssidData->radius_server_ip, 16, ipv6_char, 16); 
                    break;   
                case VS_IP_TYPE_IPV4Z:
                    u32Val = igdCmApiChartoIp(data.aucRadiusServerIP);
                    HI_OS_MEMCPY_S(ssidData->radius_server_ip, 4, &u32Val, 4);
                    break;
                case VS_IP_TYPE_IPV6Z:
                    ssidData->radius_server_ip_len = 20;
                    igdCmApiChartoIpv6((void*)data.aucRadiusServerIP, ipv6_char);
                    HI_OS_MEMCPY_S(ssidData->radius_server_ip, 16, ipv6_char, 16); 
                    break;  
                case VS_IP_TYPE_DOMAIN:
                    ssidData->radius_server_ip_len = 64;
                    HI_OS_MEMCPY_S(ssidData->radius_server_ip, 64, data.aucRadiusServerIP, 64); 
                    break;           
                default:
                    printf("ssidData->radius_server_ip_type[%d], unknown ip type\n", ssidData->radius_server_ip_type);
                    break;
            }
            HI_OS_MEMCPY_S(ssidData->radius_server_key, 32, data.aucRadiusSharedKey, 32);
            //printf("ssidData->radius_server_ip=%s, ssidData->radius_server_key=%s\n", ssidData->radius_server_ip, ssidData->radius_server_key);
            break;
        case WLAN_SSID_NC_AUTH_TYPE_WPA2_ENTERPRISE:
            ssidData->encryption_type = VS_AUTH_MODE_WPA2;
            if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_TKIP)
            {
                ssidData->encryption_type = VS_ENCRYPTION_TYPE_TKIP;
            }
            else if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_AES)
            {
                ssidData->encryption_type = VS_ENCRYPTION_TYPE_AES;
            }
            else
            {
                ssidData->encryption_type = VS_ENCRYPTION_TYPE_TKIP_AES;
            }
            ssidData->radius_server_ip_type = oam_ssid_check_ip_address(data.aucRadiusServerIP);
            ssidData->radius_server_port = data.ulRadiusServerPort;
            switch (ssidData->radius_server_ip_type)
            {
                case VS_IP_TYPE_IPV4:
                    ssidData->radius_server_ip_len = 4;
                    u32Val = igdCmApiChartoIp(data.aucRadiusServerIP);
                    HI_OS_MEMCPY_S(ssidData->radius_server_ip, 4, &u32Val, 4);
                    break;
                case VS_IP_TYPE_IPV6:
                    ssidData->radius_server_ip_len = 16;
                    igdCmApiChartoIpv6((void*)data.aucRadiusServerIP, ipv6_char);
                    HI_OS_MEMCPY_S(ssidData->radius_server_ip, 16, ipv6_char, 16); 
                    break;   
                case VS_IP_TYPE_IPV4Z:
                    u32Val = igdCmApiChartoIp(data.aucRadiusServerIP);
                    HI_OS_MEMCPY_S(ssidData->radius_server_ip, 4, &u32Val, 4);
                    break;
                case VS_IP_TYPE_IPV6Z:
                    ssidData->radius_server_ip_len = 20;
                    igdCmApiChartoIpv6((void*)data.aucRadiusServerIP, ipv6_char);
                    HI_OS_MEMCPY_S(ssidData->radius_server_ip, 16, ipv6_char, 16); 
                    break;  
                case VS_IP_TYPE_DOMAIN:
                    ssidData->radius_server_ip_len = 64;
                    HI_OS_MEMCPY_S(ssidData->radius_server_ip, 64, data.aucRadiusServerIP, 64); 
                    break;        
                default:
                    printf("ssidData->radius_server_ip_type[%d], unknown ip type\n", ssidData->radius_server_ip_type);
                    break;
            }
            HI_OS_MEMCPY_S(ssidData->radius_server_key, 32, data.aucRadiusSharedKey, 32);
            //printf("ssidData->radius_server_ip=%s, ssidData->radius_server_key=%s\n", ssidData->radius_server_ip, ssidData->radius_server_key);
            break;
        case WLAN_SSID_NC_AUTH_TYPE_WPA_WPA2_ENTERPRISE:
            ssidData->encryption_type = VS_AUTH_MODE_WPA_WPA2;
            if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_TKIP)
            {
                ssidData->encryption_type = VS_ENCRYPTION_TYPE_TKIP;
            }
            else if (data.ucEncryptType == WLAN_SSID_NC_ENC_MODE_AES)
            {
                ssidData->encryption_type = VS_ENCRYPTION_TYPE_AES;
            }
            else
            {
                ssidData->encryption_type = VS_ENCRYPTION_TYPE_TKIP_AES;
            }
            ssidData->radius_server_ip_type = oam_ssid_check_ip_address(data.aucRadiusServerIP);
            ssidData->radius_server_port = data.ulRadiusServerPort;
            switch (ssidData->radius_server_ip_type)
            {
                case VS_IP_TYPE_IPV4:
                    ssidData->radius_server_ip_len = 4;
                    u32Val = igdCmApiChartoIp(data.aucRadiusServerIP);
                    HI_OS_MEMCPY_S(ssidData->radius_server_ip, 4, &u32Val, 4);
                    break;
                case VS_IP_TYPE_IPV6:
                    ssidData->radius_server_ip_len = 16;
                    igdCmApiChartoIpv6((void*)data.aucRadiusServerIP, ipv6_char);
                    HI_OS_MEMCPY_S(ssidData->radius_server_ip, 16, ipv6_char, 16); 
                    break;   
                case VS_IP_TYPE_IPV4Z:
                    u32Val = igdCmApiChartoIp(data.aucRadiusServerIP);
                    HI_OS_MEMCPY_S(ssidData->radius_server_ip, 4, &u32Val, 4);
                    break;
                case VS_IP_TYPE_IPV6Z:
                    ssidData->radius_server_ip_len = 20;
                    igdCmApiChartoIpv6((void*)data.aucRadiusServerIP, ipv6_char);
                    HI_OS_MEMCPY_S(ssidData->radius_server_ip, 16, ipv6_char, 16); 
                    break;  
                case VS_IP_TYPE_DOMAIN:
                    ssidData->radius_server_ip_len = 64;
                    HI_OS_MEMCPY_S(ssidData->radius_server_ip, 64, data.aucRadiusServerIP, 64); 
                    break;         
                default:
                    printf("ssidData->radius_server_ip_type[%d], unknown ip type\n", ssidData->radius_server_ip_type);
                    break;
            }
            HI_OS_MEMCPY_S(ssidData->radius_server_key, 32, data.aucRadiusSharedKey, 32);
            //printf("ssidData->radius_server_ip=%s, ssidData->radius_server_key=%s\n", ssidData->radius_server_ip, ssidData->radius_server_key);
            break;
#endif
        default:
            printf("data.ucAuthType[%d], unknown auth type\n", data.ucAuthType);
            break;
    }
    printf_wifi_data(ssidData);
    ssidData->auth_mode = (hi_ushort16)htons(ssidData->auth_mode);
    ssidData->encryption_type = (hi_ushort16)htons(ssidData->encryption_type);
    ssidData->wpa_update_key_interval = htonl(ssidData->wpa_update_key_interval);
    ssidData->radius_server_ip_type = htonl(ssidData->radius_server_ip_type);
    ssidData->radius_server_ip_len = htonl(ssidData->radius_server_ip_len);
    ssidData->radius_server_ip_prefix_len = htonl(ssidData->radius_server_ip_prefix_len);
    ssidData->radius_server_port = (hi_ushort16)htons(ssidData->radius_server_port);
    ssidData->wep_encryption_level = (hi_ushort16)htons(ssidData->wep_encryption_level);
    ssidData->wep_key_index = (hi_ushort16)htons(ssidData->wep_key_index);

    return HI_RET_SUCC;
}

//ssid_index: 1-4=>2.4G  5-8=>5G
hi_int32 vs_oam_ssid_config_set(hi_uchar8 ssid_index, vs_ctcoam_wifi_ssid_configuration_s *ssidData)
{
    IgdWLANSsidAttrCfgTab data;
    int ret;
#if defined(CONFIG_NC_WPA_ENTERPRISE)
    hi_char8 ipv4_char[16]={0}, ipv4z_char[16]={0}, ipv6_char[48]={0}, ipv6z_char[48]={0};
#endif
    
    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));

    data.ulSSIDIndex = ssid_index;

    //The WiFi has not started initialization yet. Here, check if the crond process number file exists to determine if the WiFi initialization has ended
    if (access("/var/run/crond.pid", F_OK) != 0)
        return HI_RET_SUCC;

    data.ulBitmap |= WLAN_SSID_CFG_ATTR_MASK_SSIDNAME | WLAN_SSID_CFG_ATTR_MASK_SSIDENABLE;

    HI_OS_MEMCPY_S(data.aucSSIDName, 32, ssidData->ssid_name, 32);
    data.aucSSIDName[sizeof(data.aucSSIDName)-1] = '\0';

    data.ucSSIDEnable = ssidData->ssid_enable;
    if (data.ucSSIDEnable)
    {
        data.ulBitmap |= WLAN_SSID_CFG_ATTR_MASK_SSIDHIDE
                    |WLAN_SSID_CFG_ATTR_MASK_NCAUTHTYPE
                    |WLAN_SSID_CFG_ATTR_MASK_NCENCRYPTTYPE
                    |WLAN_SSID_CFG_ATTR_MASK_WPAAUTHMODE
                    |WLAN_SSID_CFG_ATTR_MASK_WPAENCRYPTMODE
                    |WLAN_SSID_CFG_ATTR_MASK_WEPENCRYPTIONLEVEL
                    |WLAN_SSID_CFG_ATTR_MASK_WPAENCRYPTIONLEVEL
                    |WLAN_SSID_CFG_ATTR_MASK_WPAUPDATETIME
                    |WLAN_SSID_CFG_ATTR_MASK_WEPKEYINDEX
                    |WLAN_SSID_CFG_ATTR_MASK_WEPKEY
                    |WLAN_SSID_CFG_ATTR_MASK_PRESHAREDKEY;
#if defined(CONFIG_NC_WPA_ENTERPRISE)
        data.ulBitmap |= WLAN_SSID_CFG_ATTR_MASK_NCRADIUSSERVERIP|WLAN_SSID_CFG_ATTR_MASK_NCRADIUSSERVERPORT;
#endif
        data.ucSSIDHide = ssidData->ssid_hide_enable;

        switch (ssidData->auth_mode)
        {
            case VS_AUTH_MODE_OPEN:
                data.ucAuthType = WLAN_SSID_NC_AUTH_TYPE_OPEN;
                data.ulWPAUpdateTime = 86400;
                if (ssidData->encryption_type == VS_ENCRYPTION_TYPE_WEP)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_WEP;
                    if (ssidData->wep_encryption_level == VS_WEB_ENCRYPTION_LEVEL_128)
                    {
                        data.ucWEPEncryptionLevel = WLAN_SSID_WEP_ENC_LEVEL_104BIT;
                        data.ucWEPKeyIndex = ssidData->wep_key_index;
                        HI_OS_STRCPY_S((char *)data.aucWEPKey[0], sizeof(data.aucWEPKey[0]), (char *)ssidData->wep_key1);
                        HI_OS_STRCPY_S((char *)data.aucWEPKey[1], sizeof(data.aucWEPKey[1]), (char *)ssidData->wep_key2);
                        HI_OS_STRCPY_S((char *)data.aucWEPKey[2], sizeof(data.aucWEPKey[2]), (char *)ssidData->wep_key3);
                        HI_OS_STRCPY_S((char *)data.aucWEPKey[3], sizeof(data.aucWEPKey[3]), (char *)ssidData->wep_key4);
                        //printf("aucWEPKey[%d]=%s\n", data.ucWEPKeyIndex, data.aucWEPKey[data.ucWEPKeyIndex-1]);
                    }
                    else if (ssidData->wep_encryption_level == VS_WEB_ENCRYPTION_LEVEL_64)
                    {
                        data.ucWEPEncryptionLevel = WLAN_SSID_WEP_ENC_LEVEL_40BIT;
                        data.ucWEPKeyIndex = ssidData->wep_key_index;
                        HI_OS_STRCPY_S((char *)data.aucWEPKey[0], sizeof(data.aucWEPKey[0]), (char *)ssidData->wep_key1);
                        HI_OS_STRCPY_S((char *)data.aucWEPKey[1], sizeof(data.aucWEPKey[1]), (char *)ssidData->wep_key2);
                        HI_OS_STRCPY_S((char *)data.aucWEPKey[2], sizeof(data.aucWEPKey[2]), (char *)ssidData->wep_key3);
                        HI_OS_STRCPY_S((char *)data.aucWEPKey[3], sizeof(data.aucWEPKey[3]), (char *)ssidData->wep_key4);
                        //printf("aucWEPKey[%d]=%s\n", data.ucWEPKeyIndex, data.aucWEPKey[data.ucWEPKeyIndex-1]);
                    }
                }
                else
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_NONE;
                }
                break;
            case VS_AUTH_MODE_SHARED:
                data.ucAuthType = WLAN_SSID_NC_AUTH_TYPE_SHARED;
                data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_WEP;
                data.ulWPAUpdateTime = 86400;
                if (ssidData->wep_encryption_level == VS_WEB_ENCRYPTION_LEVEL_128)
                {
                    data.ucWEPEncryptionLevel = WLAN_SSID_WEP_ENC_LEVEL_104BIT;
                    data.ucWEPKeyIndex = ssidData->wep_key_index;
                    HI_OS_STRCPY_S((char *)data.aucWEPKey[0], sizeof(data.aucWEPKey[0]), (char *)ssidData->wep_key1);
                    HI_OS_STRCPY_S((char *)data.aucWEPKey[1], sizeof(data.aucWEPKey[1]), (char *)ssidData->wep_key2);
                    HI_OS_STRCPY_S((char *)data.aucWEPKey[2], sizeof(data.aucWEPKey[2]), (char *)ssidData->wep_key3);
                    HI_OS_STRCPY_S((char *)data.aucWEPKey[3], sizeof(data.aucWEPKey[3]), (char *)ssidData->wep_key4);
                    //printf("aucWEPKey[%d]=%s\n", data.ucWEPKeyIndex, data.aucWEPKey[data.ucWEPKeyIndex-1]);
                }
                else if (ssidData->wep_encryption_level == VS_WEB_ENCRYPTION_LEVEL_64)
                {
                    data.ucWEPEncryptionLevel = WLAN_SSID_WEP_ENC_LEVEL_40BIT;
                    data.ucWEPKeyIndex = ssidData->wep_key_index;
                    HI_OS_STRCPY_S((char *)data.aucWEPKey[0], sizeof(data.aucWEPKey[0]), (char *)ssidData->wep_key1);
                    HI_OS_STRCPY_S((char *)data.aucWEPKey[1], sizeof(data.aucWEPKey[1]), (char *)ssidData->wep_key2);
                    HI_OS_STRCPY_S((char *)data.aucWEPKey[2], sizeof(data.aucWEPKey[2]), (char *)ssidData->wep_key3);
                    HI_OS_STRCPY_S((char *)data.aucWEPKey[3], sizeof(data.aucWEPKey[3]), (char *)ssidData->wep_key4);
                    //printf("aucWEPKey[%d]=%s\n", data.ucWEPKeyIndex, data.aucWEPKey[data.ucWEPKeyIndex-1]);
                }
                break;          
            case VS_AUTH_MODE_WPAPSK:
                data.ucAuthType = WLAN_SSID_NC_AUTH_TYPE_WPAPSK;
                data.ulWPAUpdateTime = ssidData->wpa_update_key_interval;
                if (ssidData->encryption_type == VS_ENCRYPTION_TYPE_TKIP)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_TKIP;
                }
                else if (ssidData->encryption_type == VS_ENCRYPTION_TYPE_AES)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_AES;
                }
                else if (ssidData->encryption_type == VS_ENCRYPTION_TYPE_TKIP_AES)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_TKIP_AES;
                }
                HI_OS_MEMCPY_S(data.aucPreSharedKey[0], 64, ssidData->preshared_key, 64);
                data.aucPreSharedKey[0][sizeof(data.aucPreSharedKey[0])-1] = '\0';
                //printf("data.aucPreSharedKey[0] = %s\n", data.aucPreSharedKey[0]);
                break;
            case VS_AUTH_MODE_WPA2PSK:
                data.ucAuthType = WLAN_SSID_NC_AUTH_TYPE_WPA2PSK;
                data.ulWPAUpdateTime = ssidData->wpa_update_key_interval;
                if (ssidData->encryption_type == VS_ENCRYPTION_TYPE_TKIP)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_TKIP;
                }
                else if (ssidData->encryption_type == VS_ENCRYPTION_TYPE_AES)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_AES;
                }
                else if (ssidData->encryption_type == VS_ENCRYPTION_TYPE_TKIP_AES)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_TKIP_AES;
                }
                HI_OS_MEMCPY_S(data.aucPreSharedKey[0], 64, ssidData->preshared_key, 64);
                data.aucPreSharedKey[0][sizeof(data.aucPreSharedKey[0])-1] = '\0';
                //printf("data.aucPreSharedKey[0] = %s\n", data.aucPreSharedKey[0]);
                break;
            case VS_AUTH_MODE_WPAPSK_WPA2PSK:
                data.ucAuthType = WLAN_SSID_NC_AUTH_TYPE_WPA_WPA2_PSK;
                data.ulWPAUpdateTime = ssidData->wpa_update_key_interval;
                if (ssidData->encryption_type == VS_ENCRYPTION_TYPE_TKIP)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_TKIP;
                }
                else if (ssidData->encryption_type == VS_ENCRYPTION_TYPE_AES)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_AES;
                }
                else if ( ssidData->encryption_type == VS_ENCRYPTION_TYPE_TKIP_AES)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_TKIP_AES;
                }
                HI_OS_MEMCPY_S(data.aucPreSharedKey[0], 64, ssidData->preshared_key, 64);
                data.aucPreSharedKey[0][sizeof(data.aucPreSharedKey[0])-1] = '\0';
                //printf("data.aucPreSharedKey[0] = %s\n", data.aucPreSharedKey[0]);
                break;
            case VS_AUTH_MODE_WPA3PSK:
                data.ucAuthType = WLAN_SSID_NC_AUTH_TYPE_WPA3SAE;
                data.ulWPAUpdateTime = ssidData->wpa_update_key_interval;
                if (ssidData->encryption_type == VS_ENCRYPTION_TYPE_TKIP)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_TKIP;
                }
                else if (ssidData->encryption_type == VS_ENCRYPTION_TYPE_AES)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_AES;
                }
                else if ( ssidData->encryption_type == VS_ENCRYPTION_TYPE_TKIP_AES)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_TKIP_AES;
                }
                HI_OS_MEMCPY_S(data.aucPreSharedKey[0], 64, ssidData->preshared_key, 64);
                data.aucPreSharedKey[0][sizeof(data.aucPreSharedKey[0])-1] = '\0';
                //printf("data.aucPreSharedKey[0] = %s\n", data.aucPreSharedKey[0]);
                break;
            /*Add by fyy for bug#15772*/
            case VS_AUTH_MODE_WPA2PSK_WPA3PSK:
                data.ucAuthType = WLAN_SSID_NC_AUTH_TYPE_WPA2_WPA3;
                data.ulWPAUpdateTime = ssidData->wpa_update_key_interval;
                if (ssidData->encryption_type == VS_ENCRYPTION_TYPE_TKIP)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_TKIP;
                }
                else if (ssidData->encryption_type == VS_ENCRYPTION_TYPE_AES)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_AES;
                }
                else if ( ssidData->encryption_type == VS_ENCRYPTION_TYPE_TKIP_AES)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_TKIP_AES;
                }
                HI_OS_MEMCPY_S(data.aucPreSharedKey[0], 64, ssidData->preshared_key, 64);
                data.aucPreSharedKey[0][sizeof(data.aucPreSharedKey[0])-1] = '\0';
                break;
#if defined(CONFIG_NC_WPA_ENTERPRISE)
            case VS_AUTH_MODE_WPA:
                data.ucAuthType = WLAN_SSID_NC_AUTH_TYPE_WPA_ENTERPRISE;
                if (ssidData->encryption_type == VS_ENCRYPTION_TYPE_TKIP)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_TKIP;
                }
                else if (ssidData->encryption_type == VS_ENCRYPTION_TYPE_AES)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_AES;
                }
                else if ( ssidData->encryption_type == VS_ENCRYPTION_TYPE_TKIP_AES)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_TKIP_AES;
                }
                data.ulRadiusServerPort = ssidData->radius_server_port;
                switch (ssidData->radius_server_ip_type)
                {
                    case VS_IP_TYPE_IPV4:
                        igdCmApiIptoChar(ssidData->radius_server_ip, ipv4_char, sizeof(ipv4_char));
                        HI_OS_MEMCPY_S(data.aucRadiusServerIP, sizeof(ipv4_char), ipv4_char, sizeof(ipv4_char));
                        data.aucRadiusServerIP[sizeof(data.aucRadiusServerIP)-1] = '\0';
                        break;
                    case VS_IP_TYPE_IPV6:
                        igdCmApiIpv6toChar(ssidData->radius_server_ip, ipv6_char);
                        HI_OS_MEMCPY_S(data.aucRadiusServerIP, sizeof(ipv6_char), ipv6_char, sizeof(ipv6_char)); 
                        data.aucRadiusServerIP[sizeof(data.aucRadiusServerIP)-1] = '\0';
                        break;   
                    case VS_IP_TYPE_IPV4Z:
                        igdCmApiIptoChar(ssidData->radius_server_ip, ipv4z_char, sizeof(ipv4z_char));
                        HI_OS_MEMCPY_S(data.aucRadiusServerIP, sizeof(ipv4z_char), ipv4z_char, sizeof(ipv4z_char)); 
                        data.aucRadiusServerIP[sizeof(data.aucRadiusServerIP)-1] = '\0';
                        break;
                    case VS_IP_TYPE_IPV6Z:
                        igdCmApiIpv6toChar(ssidData->radius_server_ip, ipv6z_char);
                        HI_OS_MEMCPY_S(data.aucRadiusServerIP, sizeof(ipv6z_char), ipv6z_char, sizeof(ipv6z_char));
                        data.aucRadiusServerIP[sizeof(data.aucRadiusServerIP)-1] = '\0';
                        break;  
                    case VS_IP_TYPE_DOMAIN:
                        HI_OS_MEMCPY_S(data.aucRadiusServerIP, sizeof(data.aucRadiusServerIP), ssidData->radius_server_ip, sizeof(data.aucRadiusServerIP));
                        data.aucRadiusServerIP[sizeof(data.aucRadiusServerIP)-1] = '\0';
                        break;           
                    default:
                        printf("ssidData->radius_server_ip_type[%d], unknown ip type\n", ssidData->radius_server_ip_type);
                        break;
                }
                HI_OS_MEMCPY_S(data.aucRadiusSharedKey, 32, ssidData->radius_server_key, 32);
                data.aucRadiusSharedKey[sizeof(data.aucRadiusSharedKey)-1] = '\0';
                //printf("ipv4_char=%s, ipv4z_char=%s, ipv6_char=%s, ipv6z_char=%s, aucRadiusServerIP=%s, aucRadiusSharedKey=%s\n", ipv4_char, ipv4z_char, ipv6_char, ipv6z_char, data.aucRadiusServerIP, data.aucRadiusSharedKey);
                break;
            case VS_AUTH_MODE_WPA2:
                data.ucAuthType = WLAN_SSID_NC_AUTH_TYPE_WPA2_ENTERPRISE;
                if (ssidData->encryption_type == VS_ENCRYPTION_TYPE_TKIP)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_TKIP;
                }
                else if (ssidData->encryption_type == VS_ENCRYPTION_TYPE_AES)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_AES;
                }
                else if (ssidData->encryption_type == VS_ENCRYPTION_TYPE_TKIP_AES)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_TKIP_AES;
                }
                data.ulRadiusServerPort = ssidData->radius_server_port;
                switch (ssidData->radius_server_ip_type)
                {
                    case VS_IP_TYPE_IPV4:
                        igdCmApiIptoChar(ssidData->radius_server_ip, ipv4_char, sizeof(ipv4_char));
                        HI_OS_MEMCPY_S(data.aucRadiusServerIP, sizeof(ipv4_char), ipv4_char, sizeof(ipv4_char));
                        data.aucRadiusServerIP[sizeof(data.aucRadiusServerIP)-1] = '\0';
                        break;
                    case VS_IP_TYPE_IPV6:
                        igdCmApiIpv6toChar(ssidData->radius_server_ip, ipv6_char);
                        HI_OS_MEMCPY_S(data.aucRadiusServerIP, sizeof(ipv6_char), ipv6_char, sizeof(ipv6_char)); 
                        data.aucRadiusServerIP[sizeof(data.aucRadiusServerIP)-1] = '\0';
                        break;   
                    case VS_IP_TYPE_IPV4Z:
                        igdCmApiIptoChar(ssidData->radius_server_ip, ipv4z_char, sizeof(ipv4z_char));
                        HI_OS_MEMCPY_S(data.aucRadiusServerIP, sizeof(ipv4z_char), ipv4z_char, sizeof(ipv4z_char)); 
                        data.aucRadiusServerIP[sizeof(data.aucRadiusServerIP)-1] = '\0';
                        break;
                    case VS_IP_TYPE_IPV6Z:
                        igdCmApiIpv6toChar(ssidData->radius_server_ip, ipv6z_char);
                        HI_OS_MEMCPY_S(data.aucRadiusServerIP, sizeof(ipv6z_char), ipv6z_char, sizeof(ipv6z_char));
                        data.aucRadiusServerIP[sizeof(data.aucRadiusServerIP)-1] = '\0';
                        break;  
                    case VS_IP_TYPE_DOMAIN:
                        HI_OS_MEMCPY_S(data.aucRadiusServerIP, sizeof(data.aucRadiusServerIP), ssidData->radius_server_ip, sizeof(data.aucRadiusServerIP));
                        data.aucRadiusServerIP[sizeof(data.aucRadiusServerIP)-1] = '\0';
                        break;         
                    default:
                        printf("ssidData->radius_server_ip_type[%d], unknown ip type\n", ssidData->radius_server_ip_type);
                        break;
                }
                HI_OS_MEMCPY_S(data.aucRadiusSharedKey, 32, ssidData->radius_server_key, 32);
                data.aucRadiusSharedKey[sizeof(data.aucRadiusSharedKey)-1] = '\0';
                //printf("ipv4_char=%s, ipv4z_char=%s, ipv6_char=%s, ipv6z_char=%s, aucRadiusServerIP=%s, aucRadiusSharedKey=%s\n", ipv4_char, ipv4z_char, ipv6_char, ipv6z_char, data.aucRadiusServerIP, data.aucRadiusSharedKey);
                break;
            case VS_AUTH_MODE_WPA_WPA2:
                data.ucAuthType = WLAN_SSID_NC_AUTH_TYPE_WPA_WPA2_ENTERPRISE;
                if (ssidData->encryption_type == VS_ENCRYPTION_TYPE_TKIP)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_TKIP;
                }
                else if (ssidData->encryption_type == VS_ENCRYPTION_TYPE_AES)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_AES;
                }
                else if ( ssidData->encryption_type == VS_ENCRYPTION_TYPE_TKIP_AES)
                {
                    data.ucEncryptType = WLAN_SSID_NC_ENC_MODE_TKIP_AES;
                }
                data.ulRadiusServerPort = ssidData->radius_server_port;
                switch (ssidData->radius_server_ip_type)
                {
                    case VS_IP_TYPE_IPV4:
                        igdCmApiIptoChar(ssidData->radius_server_ip, ipv4_char, sizeof(ipv4_char));
                        HI_OS_MEMCPY_S(data.aucRadiusServerIP, sizeof(ipv4_char), ipv4_char, sizeof(ipv4_char));
                        data.aucRadiusServerIP[sizeof(data.aucRadiusServerIP)-1] = '\0';
                        break;
                    case VS_IP_TYPE_IPV6:
                        igdCmApiIpv6toChar(ssidData->radius_server_ip, ipv6_char);
                        HI_OS_MEMCPY_S(data.aucRadiusServerIP, sizeof(ipv6_char), ipv6_char, sizeof(ipv6_char)); 
                        data.aucRadiusServerIP[sizeof(data.aucRadiusServerIP)-1] = '\0';
                        break;   
                    case VS_IP_TYPE_IPV4Z:
                        igdCmApiIptoChar(ssidData->radius_server_ip, ipv4z_char, sizeof(ipv4z_char));
                        HI_OS_MEMCPY_S(data.aucRadiusServerIP, sizeof(ipv4z_char), ipv4z_char, sizeof(ipv4z_char)); 
                        data.aucRadiusServerIP[sizeof(data.aucRadiusServerIP)-1] = '\0';
                        break;
                    case VS_IP_TYPE_IPV6Z:
                        igdCmApiIpv6toChar(ssidData->radius_server_ip, ipv6z_char);
                        HI_OS_MEMCPY_S(data.aucRadiusServerIP, sizeof(ipv6z_char), ipv6z_char, sizeof(ipv6z_char));
                        data.aucRadiusServerIP[sizeof(data.aucRadiusServerIP)-1] = '\0';
                        break;  
                    case VS_IP_TYPE_DOMAIN:
                        HI_OS_MEMCPY_S(data.aucRadiusServerIP, sizeof(data.aucRadiusServerIP), ssidData->radius_server_ip, sizeof(data.aucRadiusServerIP));
                        data.aucRadiusServerIP[sizeof(data.aucRadiusServerIP)-1] = '\0';
                        break;            
                    default:
                        printf("ssidData->radius_server_ip_type[%d], unknown ip type\n", ssidData->radius_server_ip_type);
                        break;
                }
                HI_OS_MEMCPY_S(data.aucRadiusSharedKey, 32, ssidData->radius_server_key, 32);
                data.aucRadiusSharedKey[sizeof(data.aucRadiusSharedKey)-1] = '\0';
                //printf("ipv4_char=%s, ipv4z_char=%s, ipv6_char=%s, ipv6z_char=%s, aucRadiusServerIP=%s, aucRadiusSharedKey=%s\n", ipv4_char, ipv4z_char, ipv6_char, ipv6z_char, data.aucRadiusServerIP, data.aucRadiusSharedKey);
                break;
#endif
            default:
                printf("ssidData->auth_mode[%d], unknown auth type\n", ssidData->auth_mode);
                return HI_RET_FAIL;
        }
    }
#ifdef CONFIG_PLATFORM_OPENWRT
    ret = HI_IPC_CALL("hi_sml_igd_wlan_ssid_cfg_attr_tab_set", &data);
#else
    ret = igdCmConfSet(IGD_WLAN_SSID_CFG_ATTR_TAB,(unsigned char *)&data,sizeof(data));
#endif
    if(ret != 0)
    {
        printf("igdCmConfSet IGD_WLAN_SSID_CFG_ATTR_TAB ret : %d \r\n", ret);
        return HI_RET_FAIL;
    }

    return HI_RET_SUCC;
}

uint32_t vs_ctcoam_get_wifi_ssid_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    hi_ushort16 tlv_leaf = 0;
    unsigned int struct_size = 0;
    unsigned int add_len = 0;
    unsigned char ssid_index = 0;
    hi_ushort16 *pus_msglen = NULL;
    const unsigned char *p_data = (const unsigned char *)pv_inmsg;
    hi_ctcoam_tlv_s *tlv_head = (hi_ctcoam_tlv_s *)(pv_inmsg - 4);
    vs_ctcoam_wifi_ssid_configuration_s *ssid_cfg = NULL;

    if (pv_outmsg == NULL)
        return HI_RET_INVALID_PARA;

    ssid_cfg = (vs_ctcoam_wifi_ssid_configuration_s *)pv_outmsg;

    ssid_index = *(p_data - 5);
    ssid_index %= 100; 

    vs_oam_ssid_config_get(ssid_index, ssid_cfg);

    /* slicing the packets and add tlv header to the data */
    tlv_leaf = tlv_head->us_leaf;
    tlv_leaf = (hi_ushort16)htons(tlv_leaf);
    struct_size = sizeof(vs_ctcoam_wifi_ssid_configuration_s);
    if(struct_size > 0 && struct_size <= 128)
    {
        *puc_changingmsglen = struct_size;
        HI_OS_MEMCPY_S(pv_outmsg, sizeof(ssid_cfg), ssid_cfg, *puc_changingmsglen);
    }
    else if(struct_size > 128 && struct_size <= (255 - 4))//langer than 128, need to slicing the packets
    {
        add_len = packet_slicing_for_send(ssid_cfg, sizeof(vs_ctcoam_wifi_ssid_configuration_s), tlv_head->uc_branch, tlv_leaf);
        add_len = (add_len - 1) * 4;
        pus_msglen = (hi_ushort16 *)(puc_changingmsglen + 1);
        *pus_msglen = 0;
        *puc_changingmsglen = struct_size + add_len;//send msg will use this len value
    }
    else if(struct_size > (255 - 4) && struct_size <= 1496)
    {
        add_len = packet_slicing_for_send(ssid_cfg, sizeof(vs_ctcoam_wifi_ssid_configuration_s), tlv_head->uc_branch, tlv_leaf);
        add_len = (add_len - 1) * 4;
        pus_msglen = (hi_ushort16 *)(puc_changingmsglen + 1);
        *pus_msglen = struct_size + add_len;//send msg will use this len value
        *puc_changingmsglen = 0;
    }
    else
    {
        return HI_RET_FAIL;
    }

    return HI_RET_SUCC;
}

uint32_t vs_ctcoam_set_wifi_ssid_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    hi_ushort16 tlv_leaf = 0;
    unsigned char ssid_index = 0, cfg_change = 0;
    const unsigned char *p_data = (const unsigned char *)pv_inmsg;
    hi_ctcoam_tlv_s *tlv_head = (hi_ctcoam_tlv_s *)(pv_inmsg - 4);
    vs_ctcoam_wifi_ssid_configuration_s ssid_cfg_data, ram_cfg;
    vs_ctcoam_wifi_ssid_configuration_s *ssid_cfg = &ssid_cfg_data;

    if (pv_inmsg == NULL)
        return HI_RET_INVALID_PARA;
    
    ssid_index = *(p_data - 5);
    ssid_index %= 100;
    tlv_leaf = tlv_head->us_leaf;
    tlv_leaf = (hi_ushort16)htons(tlv_leaf);
    packet_slicing_for_receive(&ssid_cfg_data, sizeof(vs_ctcoam_wifi_ssid_configuration_s), pv_inmsg, 1496, tlv_head->uc_branch, tlv_leaf);

    ssid_cfg->auth_mode = (hi_ushort16)ntohs(ssid_cfg->auth_mode);
    ssid_cfg->encryption_type = (hi_ushort16)ntohs(ssid_cfg->encryption_type);
    ssid_cfg->wpa_update_key_interval = ntohl(ssid_cfg->wpa_update_key_interval);
    ssid_cfg->radius_server_ip_type = ntohl(ssid_cfg->radius_server_ip_type);
    ssid_cfg->radius_server_ip_len = ntohl(ssid_cfg->radius_server_ip_len);
    ssid_cfg->radius_server_ip_prefix_len = ntohl(ssid_cfg->radius_server_ip_prefix_len);
    ssid_cfg->radius_server_port = (hi_ushort16)ntohs(ssid_cfg->radius_server_port);
    ssid_cfg->wep_encryption_level = (hi_ushort16)ntohs(ssid_cfg->wep_encryption_level);
    ssid_cfg->wep_key_index = (hi_ushort16)ntohs(ssid_cfg->wep_key_index);

    ssid_cfg->ssid_name[sizeof(ssid_cfg->ssid_name)-1] = '\0';
    ssid_cfg->preshared_key[sizeof(ssid_cfg->preshared_key)-1] = '\0';
    ssid_cfg->radius_server_ip[sizeof(ssid_cfg->radius_server_ip)-1] = '\0';
    ssid_cfg->radius_server_key[sizeof(ssid_cfg->radius_server_key)-1] = '\0';
    ssid_cfg->wep_key1[sizeof(ssid_cfg->wep_key1)-1] = '\0';
    ssid_cfg->wep_key2[sizeof(ssid_cfg->wep_key2)-1] = '\0';
    ssid_cfg->wep_key3[sizeof(ssid_cfg->wep_key3)-1] = '\0';
    ssid_cfg->wep_key4[sizeof(ssid_cfg->wep_key4)-1] = '\0';

    printf_wifi_data(ssid_cfg);

    /* compare cfg */
    vs_oam_wifi_ssid_cfg_to_ram(&ram_cfg, ssid_index);
    if(memcmp(&ram_cfg, ssid_cfg, sizeof(vs_ctcoam_wifi_ssid_configuration_s)) != 0)
        cfg_change = 1;

    /* if cfg change, set to mib */
    if(cfg_change == 1)
    {
        ret = vs_oam_ssid_config_set(ssid_index, ssid_cfg);
        if(ret != HI_RET_SUCC)
        {
            printf("vs_oam_ssid_config_set fail\n");
            return HI_RET_FAIL;
        }
    }

    return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

