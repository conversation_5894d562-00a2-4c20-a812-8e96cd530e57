/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_proc.c
  版 本 号   : 初稿
  生成日期   : D2013_07_01
******************************************************************************/

#include "hi_uspace.h"
#include "hi_sml_epon.h"
#include "hi_sysinfo.h"
#include "hi_notifier.h"
#include "hi_oam_common.h"
#include "hi_oam_notify.h"
#include "hi_oam_api.h"
#include "hi_oam_adapter.h"

#include "hi_ctcoam_proc.h"
#include "hi_ctcoam_tree.h"
#include "hi_ctcoam_manage.h"
#include "hi_ctcoam_alarm.h"
#include "hi_ctcoam_call.h"

#include "hi_pon_epon_api.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */
#if 1
typedef enum {
    HI_CTCOAM_DISINFO_1G = 1,
    HI_CTCOAM_DISINFO_10G,
    HI_CTCOAM_DISINFO_1G10G,
} hi_ctcoam_disinfo_e;

typedef enum {
    HI_MPCP_STATE_INITIAL_E = 0,
    HI_MPCP_STATE_REGISTER_REQ_E,
    HI_MPCP_STATE_REGISTERED_E,
    HI_MPCP_STATE_HANGUP_E,
    HI_MPCP_STATE_MAX_E,
    HI_MPCP_STATE_LOS_E,
    HI_MPCP_STATE_SD_E
} hi_ctcoam_mpcp_regstate_e;

typedef enum {
    HI_CTCOAM_PASSIVE_WAIT_E = 0,
    HI_CTCOAM_SEND_EXT_INFO_E,
    HI_CTCOAM_SEND_OAM_EXT_INFO_ACK_E,
    HI_CTCOAM_SEND_ANY_E,
    HI_CTCOAM_STATE_END_E
} HI_CTCOAM_STATE_E;
#endif
extern hi_ctcoam_head_s g_st_branch_head;
static hi_uchar8 g_uc_instance_tlv_branch = HI_CTCOAM_BRANCH_INSTANCE_21;
static hi_uchar8 g_uc_instance_tlv_id = HI_FALSE;
static hi_uchar8 g_uc_reg_flag = HI_FALSE;

hi_uint32 g_i_ctcoam_llid;
#ifdef CONFIG_PLATFORM_OPENWRT
static hi_ctcoam_onu_loid_s g_oam_loid_info = {0};
#endif
extern hi_uint32 hi_ctcoam_software_upgrade(hi_uint32 ui_llidindex, hi_void *pv_inmsg, hi_uint32 ui_inmsglen, hi_uint32 ui_exoam_type);
extern hi_int32 hi_oam_init_sampleoam(hi_uint32 ui_llid);


/*****************************************************************************
 Prototype    : hi_oam_api_txmsgproc
 Description  :
 Input        : hi_uint32 ui_llid
                hi_void *pv_msg
                hi_uint32 ui_msglen
                hi_uint32 ui_msgtype
 Output       : None
*****************************************************************************/
hi_uint32 hi_oam_api_txmsgproc(hi_uint32 ui_llid, hi_void *pv_msg,
                               hi_uint32 ui_msglen, hi_uint32 ui_msgtype)
{
    hi_ctcoam_send_ctrl_s st_ctrl;
    st_ctrl.ui_llid    = ui_llid;
    st_ctrl.ui_msglen  = ui_msglen;
    st_ctrl.ui_msgtype = ui_msgtype;
    st_ctrl.pv_msg     = pv_msg;

    return hi_ctcoam_cmd_proc(CTCOAM_IOCS_SEND_DATA, (void *)&st_ctrl);
}


/*****************************************************************************
 Prototype    : hi_oam_api_send_ctcoampdu
 Description  :
 Input        : hi_uint32 ui_llid
                hi_void *pv_msg
                hi_uint32 ui_msglen
                hi_uint32 ui_msgtype
 Output       : None
*****************************************************************************/
hi_uint32 hi_oam_api_send_ctcoampdu(hi_uint32 ui_llid, hi_void *pv_msg,
                                    hi_uint32 ui_msglen, hi_uint32 ui_msgtype)
{
    hi_ctcoam_send_ctrl_s st_ctrl;

    st_ctrl.ui_llid    = ui_llid;
    st_ctrl.ui_msglen  = ui_msglen;
    st_ctrl.ui_msgtype = ui_msgtype;
    st_ctrl.pv_msg     = pv_msg;

    return hi_ctcoam_cmd_proc(CTCOAM_IOCS_RCV_CTCOAMPDU, (void *)&st_ctrl);
}

/*****************************************************************************
 Prototype    : hi_ctcoam_get_global_data
 Description  : get global data
 Input        : hi_epon_llid_get_exoam_gloabal_data_ctrl *pst_ctrl
 Output       : None
*****************************************************************************/
hi_uint32 hi_ctcoam_get_global_data(hi_epon_llid_get_exoam_gloabal_data_ctrl *pst_ctrl)
{
    return hi_ctcoam_cmd_proc(CTCOAM_IOCG_GLOBAL_DATA, (void *)pst_ctrl);
}


/*****************************************************************************
 Prototype    : hi_ctcoam_set_global_data
 Description  : set global data
 Input        : hi_epon_llid_get_exoam_gloabal_data_ctrl *pst_ctrl
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_global_data(hi_epon_llid_get_exoam_gloabal_data_ctrl *pst_ctrl)
{
    return hi_ctcoam_cmd_proc(CTCOAM_IOCS_GLOBAL_DATA, (void *)pst_ctrl);
}

/*****************************************************************************
 Prototype    : hi_ctcoam_xepon_calc_churing_key
 Description  : get new key
 Input        : hi_uchar8 *pauc_key
 Output       : None
*****************************************************************************/
hi_uint32 hi_ctcoam_xepon_calc_churing_key(hi_uchar8 *pauc_key)
{
    hi_uint32 ui_seed1;
    hi_uint32 ui_seed2;
    hi_uint32 ui_seed3;
    /* check if input para to be null*/
    if (HI_NULL == pauc_key) {
        //hi_os_printf("\r\nNull Ptr.");
        return HI_RET_NULLPTR;
    }

    srand(time(0));
    ui_seed1 = rand();

    *pauc_key = (hi_uchar8)(ui_seed1 & HI_PON_BYTE_BITMASK);
    *(pauc_key + 1) = (hi_uchar8)((ui_seed1 >> HI_PON_BITSHF_B) & HI_PON_BYTE_BITMASK);
    *(pauc_key + 2) = (hi_uchar8)((ui_seed1 >> HI_PON_BITSHF_W) & HI_PON_BYTE_BITMASK);

    ui_seed2 = rand();
    *(pauc_key + 3) = (hi_uchar8)(ui_seed2 & HI_PON_BYTE_BITMASK);
    *(pauc_key + 4) = (hi_uchar8)((ui_seed2 >> HI_PON_BITSHF_B) & HI_PON_BYTE_BITMASK);
    *(pauc_key + 5) = (hi_uchar8)((ui_seed2 >> HI_PON_BITSHF_W) & HI_PON_BYTE_BITMASK);

    ui_seed3 = rand();
    *(pauc_key + 6) = (hi_uchar8)(ui_seed3 & HI_PON_BYTE_BITMASK);
    *(pauc_key + 7) = (hi_uchar8)((ui_seed3 >> HI_PON_BITSHF_B) & HI_PON_BYTE_BITMASK);
    *(pauc_key + 8) = (hi_uchar8)((ui_seed3 >> HI_PON_BITSHF_W) & HI_PON_BYTE_BITMASK);

    return HI_RET_SUCC;
}

/*****************************************************************************
 Prototype    : hi_ctcoam_calc_churing_key
 Description  : get new key
 Input        : hi_uchar8 *pauc_key
 Output       : None
*****************************************************************************/
hi_uint32 hi_ctcoam_calc_churing_key(hi_uchar8 *pauc_key)
{
    hi_uint32 ui_seed;
    /* check if input para to be null*/
    if (HI_NULL == pauc_key) {
        //hi_os_printf("\r\nNull Ptr.");
        return HI_RET_NULLPTR;
    }

    srand(time(0));
    ui_seed = rand();

    *pauc_key = (hi_uchar8)(ui_seed & HI_PON_BYTE_BITMASK);
    *(pauc_key + 1) = (hi_uchar8)((ui_seed >> HI_PON_BITSHF_B) & HI_PON_BYTE_BITMASK);
    *(pauc_key + 2) = (hi_uchar8)((ui_seed >> HI_PON_BITSHF_W) & HI_PON_BYTE_BITMASK);

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_filloui
 Description  :
 Input        : hi_uint32 ui_exoam_type
                hi_uchar8 auc_oui[HI_EPON_OUI_LEN]
 Output       : None
*****************************************************************************/
hi_uint32 hi_ctcoam_filloui(hi_uint32 ui_exoam_type, hi_uchar8 auc_oui[HI_EPON_OUI_LEN])
{
    if (HI_NULL == auc_oui) {
        hi_ctcoam_dbg_err_print("Null Ptr.");
        return HI_RET_NULLPTR;
    }

    /* 目前只支持以下几种OUI */
    switch (ui_exoam_type) {
        case HI_EXOAM_TYPE_TEKNOVUS_E:
            auc_oui[0] = HI_EPON_TEKNOVUS_OUI_1ST;
            auc_oui[1] = HI_EPON_TEKNOVUS_OUI_2ND;
            auc_oui[2] = HI_EPON_TEKNOVUS_OUI_3RD;

            break;

        case HI_EXOAM_TYPE_CTC_E:
            auc_oui[0] = HI_EPON_CTC_OUI_1ST;
            auc_oui[1] = HI_EPON_CTC_OUI_2ND;
            auc_oui[2] = HI_EPON_CTC_OUI_3RD;

            break;

        case HI_EXOAM_TYPE_DASAN_E:
            auc_oui[0] = HI_EPON_DASAN_OUI_1ST;
            auc_oui[1] = HI_EPON_DASAN_OUI_2ND;
            auc_oui[2] = HI_EPON_DASAN_OUI_3RD;

            break;

        case HI_EXOAM_TYPE_KT_E:
            auc_oui[0] = HI_EPON_KT_OUI_1ST;
            auc_oui[1] = HI_EPON_KT_OUI_2ND;
            auc_oui[2] = HI_EPON_KT_OUI_3RD;

            break;

        case HI_EXOAM_TYPE_PMC_E:
            auc_oui[0] = HI_EPON_PMC_OUI_1ST;
            auc_oui[1] = HI_EPON_PMC_OUI_2ND;
            auc_oui[2] = HI_EPON_PMC_OUI_3RD;

            break;

        case HI_EXOAM_TYPE_HW_E:
            auc_oui[0] = HI_EPON_HW_OUI_1ST;
            auc_oui[1] = HI_EPON_HW_OUI_2ND;
            auc_oui[2] = HI_EPON_HW_OUI_3RD;

            break;

        case HI_EXOAM_TYPE_VSOL_E:
            auc_oui[0] = HI_EPON_VSOL_OUI_1ST;
            auc_oui[1] = HI_EPON_VSOL_OUI_2ND;
            auc_oui[2] = HI_EPON_VSOL_OUI_3RD;

            break;

        default:
            hi_ctcoam_dbg_err_print("Non-support OUI Type.");
            return HI_RET_INVALID_PARA;
    }

    return HI_RET_SUCC;
}

static void hi_ctcoam_instance_proc(uint16_t leaf, uint8_t *needinstance)
{
    hi_epon_llid_get_exoam_gloabal_data_ctrl ctrl;
    hi_sysinfo_data_s sysinfo;

    if (HI_IPC_CALL("hi_sysinfo_data_get", &sysinfo) != HI_RET_SUCC) {
        return;
    }
    if ((hi_os_strcasecmp(sysinfo.ac_operator, "CTC") == 0) &&
        (leaf == HI_CTCOAM_LEAF_OPTICAL_DIAGNOSE)) {
        *needinstance = HI_FALSE;
    }
	ctrl.ui_llidindex = 0;
    if (hi_ctcoam_get_global_data(&ctrl) != HI_RET_SUCC) {
        return;
    }
    if ((ctrl.st_data.uc_negotiatever == HI_EPON_CUOAM_VER_C4_BYTE) &&
        (leaf == HI_CTCOAM_LEAF_OPT_TRANS_INFO)) {
        *needinstance = HI_FALSE;
    }
}

/*****************************************************************************
 Prototype    : hi_ctcoam_search_ex_oam_tree
 Description  : search ctcoam tree
 Input        : hi_void *pv_tlv
                hi_uchar8 uc_oper
                hi_ctcoam_proc_pfn *pfn_proc
                hi_uchar8 *puc_valuelen
                hi_uchar8 *puc_needinstance
 Output       : None
*****************************************************************************/
hi_uint32 hi_ctcoam_search_ex_oam_tree(hi_void *pv_tlv, hi_uchar8 uc_oper,
                                       hi_ctcoam_proc_pfn *pfn_proc,
                                       hi_uchar8 *puc_valuelen, hi_uchar8 *puc_needinstance,
                                       hi_uint32 *pui_exoamtype)
{
    hi_uint32 ui_loop;
    hi_uint32 ui_nodenum;
    hi_ushort16 us_leaf;
    hi_uchar8 uc_branch;
    hi_ctcoam_tree_node_s *pst_node = g_st_branch_head.pst_firstnode;

    /* 参数检查 */
    if ((HI_NULL == pv_tlv) || (HI_NULL == pfn_proc) || (HI_NULL == puc_valuelen)
        || (HI_NULL == puc_needinstance) || (HI_NULL == pui_exoamtype)) {
        hi_ctcoam_dbg_err_print("\r\nNull Ptr.");
        return HI_RET_NULLPTR;
    }

    uc_branch = ((hi_ctcoam_tlv_s *)pv_tlv)->uc_branch;
    us_leaf = (hi_ushort16)ntohs(((hi_ctcoam_tlv_s *)pv_tlv)->us_leaf);

    /* 寻找对应的Branch */
    HI_EPON_SEARCH_TREE(uc_branch, g_st_branch_head.ui_nodenum, pst_node, ui_loop)

    if (ui_loop >= g_st_branch_head.ui_nodenum) {
        hi_ctcoam_dbg_err_print("Invalid Branch Noodnum(%d-%d).", ui_loop, g_st_branch_head.ui_nodenum);
        return HI_RET_INVALID_PARA;
    }

    /* 找到对应Branch的第一个Leaf结点 */
    pst_node = ((hi_ctcoam_head_s *)((g_st_branch_head.pst_firstnode)[ui_loop].pv_subhead))->pst_firstnode;
    ui_nodenum = ((hi_ctcoam_head_s *)((g_st_branch_head.pst_firstnode)[ui_loop].pv_subhead))->ui_nodenum;

    /* 寻找对应的Leaf */
    HI_EPON_SEARCH_TREE(us_leaf, ui_nodenum, pst_node, ui_loop)

    if (ui_loop >= ui_nodenum) {
        hi_ctcoam_dbg_err_print("Invalid Leaf Noodnum(%d-%d).", ui_loop, ui_nodenum);
        return HI_RET_INVALID_PARA;
    }

    /* 回填对应的处理函数 */
    switch (uc_oper) {
        case HI_CTC_OPER_GET_E:
            if (HI_NULL == pst_node[ui_loop].pfn_getproc) {
                hi_ctcoam_dbg_err_print("Invalid Process function.");
                return HI_RET_NOTSUPPORT;
            }

            *pfn_proc = pst_node[ui_loop].pfn_getproc;

            break;

        case HI_CTC_OPER_SET_E:
            if (HI_NULL == pst_node[ui_loop].pfn_setproc) {
                hi_ctcoam_dbg_err_print("Invalid Process function.");
                return HI_RET_NOTSUPPORT;
            }

            *pfn_proc = pst_node[ui_loop].pfn_setproc;

            break;

        default:
            return HI_RET_FAIL;
    }

    *puc_valuelen = pst_node[ui_loop].uc_tlvlen;
    *puc_needinstance = pst_node[ui_loop].uc_needinstance;
    *pui_exoamtype = pst_node[ui_loop].ui_exoam_type;
	hi_ctcoam_instance_proc(us_leaf, puc_needinstance);
    if ((us_leaf == HI_CTCOAM_LEAF_ALARM_ADMIN_STATE)
        || (us_leaf == HI_CTCOAM_LEAF_ALARM_THRESHOLD)) {
        hi_uchar8 *puc_data = (hi_uchar8 *)pv_tlv;
        hi_ushort16 *pus_alarmid;
        puc_data += sizeof(hi_ctcoam_tlv_s);
        pus_alarmid = (hi_ushort16 *)puc_data;
        if (ntohs(*pus_alarmid) < HI_NNI_PORT_RX_OPTICAL_POWER_HIGH_ALARM_E) {
            *puc_needinstance = HI_FALSE;
        }
    }

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_proc
 Description  : Get PROCESS
 Input        : hi_uint32 ui_llidindex     llid
                hi_void *pv_localinstance  本地实例索引
                hi_uchar8 *puc_lastportid  上一次处理的实例索引端口号
                hi_void *pv_tlv            待处理的属性TLV
                hi_uint32 ui_exoam_type,
                hi_void **ppv_fillback     回填指针
                hi_uint32 *pui_fillmsglen  回填消息的长度
 Output       : None
*****************************************************************************/
hi_uint32 hi_ctcoam_get_proc(hi_uint32 ui_llidindex, hi_void *pv_localinstance, hi_uchar8 *puc_lastportid,
                             hi_void *pv_tlv, hi_uint32 ui_exoam_type, hi_void **ppv_fillback, hi_uint32 *pui_fillmsglen)
{
    if ((HI_NULL == pv_localinstance)
        || (HI_NULL == ppv_fillback)
        || (HI_NULL == *ppv_fillback)
        || (HI_NULL == puc_lastportid)
        || (HI_NULL == pui_fillmsglen)) {
        hi_ctcoam_dbg_err_print("Null Ptr.");
        return HI_RET_NULLPTR;
    }

    hi_uint32 ui_ret;
    hi_uint32 ui_startloop;
    hi_uint32 ui_endloop;
    hi_uint32 ui_loop;
    hi_uint32 ui_exoamtype_tmp;
    hi_uint32 ui_txmsglen;
    hi_uchar8 uc_needinstance;
    hi_uchar8 uc_txvaluelen;
    hi_uchar8 *puc_value = HI_NULL;
    hi_uint32 *pui_value = HI_NULL;
    //    hi_uchar8 *puc_txvlauelen = HI_NULL;
    hi_uint32  ui_reportmsglen = 0;
    hi_ctcoam_instance_s st_localinstance_tmp;
    hi_ctcoam_instance_s *pst_localinstance = (hi_ctcoam_instance_s *)pv_localinstance;
    hi_ctcoam_tlv_s *pst_tlv = (hi_ctcoam_tlv_s *)pv_tlv;
    hi_ctcoam_tlv_s *pst_tlvresponse = (hi_ctcoam_tlv_s *)(*ppv_fillback);
    hi_ctcoam_proc_pfn pfn_proc = HI_NULL;
    hi_uchar8 uc_outmsg_len[3];

    //hi_uint32 ui_proc_allport = HI_FALSE;


    /* 检查合法性 */
    if (ui_llidindex >= HI_MAX_LLID_NUM) {
        hi_ctcoam_dbg_err_print("Invalid LLID(%d).", ui_llidindex);
        return HI_RET_INVALID_PARA;
    }

    HI_OS_MEMSET_S((hi_void *)&st_localinstance_tmp, sizeof(hi_ctcoam_instance_s), 0, sizeof(hi_ctcoam_instance_s));

    /* 搜索到对应的处理函数 */
    ui_ret = hi_ctcoam_search_ex_oam_tree((hi_void *)pst_tlv,
                                          HI_CTC_OPER_GET_E, &pfn_proc,
                                          &uc_txvaluelen, &uc_needinstance, &ui_exoamtype_tmp);
    if (HI_RET_SUCC != ui_ret) {
        hi_ctcoam_dbg_err_print("Search Extoam Tree Fail(%d).", ui_ret);
        return ui_ret;
    }

    if (HI_NULL == pfn_proc) {
        hi_ctcoam_dbg_err_print("Null Process Function.");
        return HI_RET_NOTSUPPORT;
    }

    if (ui_exoamtype_tmp != ui_exoam_type) {
        hi_ctcoam_dbg_err_print("Invalid Type.");
        return HI_RET_INVALID_PARA;
    }

    if (HI_CTC_INSTANCE_PORT_E == pst_localinstance->us_instancetype) {
        if (HI_CTCOAM_BRANCH_INSTANCE == g_uc_instance_tlv_branch) {
            if (HI_EPON_ONT_ALL_ETH_PORT == pst_localinstance->un_value.uc_value) {
                /* 则循环所有的Eth端口 */
                ui_startloop = HI_EPON_ONT_FIRST_ETH_PORT;
                ui_endloop   = hi_oam_get_eth_port_num();
                ui_endloop  += ui_startloop - 1;
                //ui_proc_allport = HI_TRUE;
            } else {
                /* 只处理一个端口 */
                ui_startloop = pst_localinstance->un_value.uc_value;
                ui_endloop   = ui_startloop;
            }
        } else if (HI_CTCOAM_BRANCH_INSTANCE_21 == g_uc_instance_tlv_branch) {
            if (HI_EPON_ONT_ALL_ETH_PORT_21 == (pst_localinstance->un_value.ui_value & 0xFFFF)) {
                /* 则循环所有的Eth端口 */
                ui_startloop = HI_EPON_ONT_FIRST_ETH_PORT;
                ui_endloop   = hi_oam_get_eth_port_num();
                ui_endloop  += ui_startloop - 1;
                //ui_proc_allport = HI_TRUE;
            } else {
                /* 只处理一个端口 */
                ui_startloop = pst_localinstance->un_value.ui_value & 0XFFFF;
                ui_endloop   = ui_startloop;
            }
        } else {
            hi_ctcoam_dbg_err_print("unknown branch(%d).", g_uc_instance_tlv_branch);
            return HI_RET_FAIL;
        }
    } else if (HI_CTC_INSTANCE_PON_IF_E == pst_localinstance->us_instancetype) {
        if ((HI_CTCOAM_BRANCH_INSTANCE == g_uc_instance_tlv_branch)
            || (HI_CTCOAM_BRANCH_INSTANCE_21 == g_uc_instance_tlv_branch)) {
            if ((0 != pst_localinstance->un_value.uc_value)
                && (1 != pst_localinstance->un_value.uc_value)) {
                ui_startloop = 0;
                ui_endloop = ui_startloop;
            } else {
                /* 只处理一个端口 */
                ui_startloop = pst_localinstance->un_value.uc_value;
                ui_endloop   = ui_startloop;
            }
        } else {
            hi_ctcoam_dbg_err_print("unknown branch(%d).", g_uc_instance_tlv_branch);
            return HI_RET_FAIL;
        }
    } else {
        /* 只处理一个端口 */
        ui_startloop = pst_localinstance->un_value.uc_value;
        ui_endloop   = ui_startloop;
    }


    ui_txmsglen = 0;
    st_localinstance_tmp.us_instancetype = pst_localinstance->us_instancetype;
    st_localinstance_tmp.uc_length = pst_localinstance->uc_length;

    for (ui_loop = ui_startloop; ui_loop <= ui_endloop; ui_loop++) {
        st_localinstance_tmp.un_value.ui_value = ui_loop;
        /* 当回应消息需要实例索引,同时本实例索引和上一次处理的实例索引不同时,
           则需要在回应消息中添加实例索引 */
        if ((HI_TRUE == uc_needinstance || HI_TRUE == g_uc_instance_tlv_id)
            && (*puc_lastportid != st_localinstance_tmp.un_value.uc_value)) {
            pst_tlvresponse->us_leaf   = (hi_ushort16)htons(pst_localinstance->us_instancetype);
            pst_tlvresponse->uc_length = pst_localinstance->uc_length;
            if (g_uc_instance_tlv_branch == HI_CTCOAM_BRANCH_INSTANCE) {
                pst_tlvresponse->uc_branch = HI_CTCOAM_BRANCH_INSTANCE;
                puc_value  = (hi_uchar8 *)(pst_tlvresponse + 1);
                *puc_value = pst_localinstance->un_value.uc_value;
                *puc_lastportid = *puc_value;
                pst_tlvresponse = (hi_ctcoam_tlv_s *)(puc_value + 1);

                ui_txmsglen += (sizeof(hi_ctcoam_tlv_s) + sizeof(hi_uchar8));
            } else if (g_uc_instance_tlv_branch == HI_CTCOAM_BRANCH_INSTANCE_21) {
                pst_tlvresponse->uc_branch = HI_CTCOAM_BRANCH_INSTANCE_21;

                pui_value  = (hi_uint32 *)(pst_tlvresponse + 1);
                *pui_value = pst_localinstance->un_value.ui_value;
                *puc_lastportid = *pui_value  & 0xff;
                *pui_value = htonl(*pui_value);
                pst_tlvresponse = (hi_ctcoam_tlv_s *)(pui_value + 1);

                ui_txmsglen += (sizeof(hi_ctcoam_tlv_s) + sizeof(hi_uint32));
            } else {
                return HI_RET_FAIL;
            }
        } else if ((HI_TRUE == uc_needinstance)
                   && (*puc_lastportid == st_localinstance_tmp.un_value.uc_value)) {
            /* do nothing */
        } else {
            /* 将上一次处理的PortID改为初始值 */
            *puc_lastportid = HI_EPON_ONT_ALL_ETH_PORT;
        }

        /* 填充回应消息的Branch和Leaf */
        pst_tlvresponse->uc_branch = pst_tlv->uc_branch;
        pst_tlvresponse->us_leaf   = pst_tlv->us_leaf;

        /*ponif接口，将端口号转发为内部pon口id 5 */
        if (st_localinstance_tmp.us_instancetype == HI_CTC_INSTANCE_PON_IF_E) {
            st_localinstance_tmp.un_value.uc_value = HI_LSW_PORT_NNI_PON_E;
        }

        /* 调用处理函数处理 */
        ui_ret = pfn_proc(ui_llidindex, &st_localinstance_tmp, (hi_void *)(pst_tlv + 1), &uc_outmsg_len[0],
                          (hi_void *)(pst_tlvresponse + 1));

        if (HI_CTCOAM_CHANGING_TLV_LEN == uc_txvaluelen) {
            pst_tlvresponse->uc_length = uc_outmsg_len[0];
        } else {
            pst_tlvresponse->uc_length = uc_txvaluelen;
        }

        if (HI_RET_SUCC != ui_ret) {
            hi_ctcoam_dbg_err_print("Process Fail(%d).", ui_ret);
            return ui_ret;
        }

        /* 回填指针后移 */
        ui_reportmsglen = pst_tlvresponse->uc_length;
        if (pst_tlv->uc_branch == HI_CTCOAM_BRANCH_EXT_ATTR) {
            hi_ushort16 us_leaf = (hi_ushort16)ntohs(pst_tlv->us_leaf);
            hi_ushort16 *pus_reportlen;
            switch (us_leaf) {
                case HI_CTCOAM_LEAF_STATISTICS_CUR_DATA:
                case HI_CTCOAM_LEAF_STATISTICS_PRE_DATA:

                case HI_CTCOAM_LEAF_FLOW_CLASS_AND_MARK:
                    pus_reportlen = (hi_ushort16 *)&uc_outmsg_len[1];
                    ui_reportmsglen = *pus_reportlen;
                    break;

                case VS_CTCOAM_LEAF_LAN_DHCPV4_SERVER:
                case VS_CTCOAM_LEAF_LAN_DHCPV6_SERVER:
                case VS_CTCOAM_LEAF_ACCESS_CONTROL:
                case VS_CTCOAM_LEAF_TR069_GLOBAL_MANAGEMENT:
                case VS_CTCOAM_LEAF_TR069_STUN_MANAGEMENT:
                case VS_CTCOAM_LEAF_WAN_DUAL_STACK:
                case VS_CTCOAM_LEAF_WAN_CONFIGURATION:
                case VS_CTCOAM_LEAF_WIFI_SSID_CONFIGURATION:
                case VS_CTCOAM_LEAF_MAC_INFO:
                    pus_reportlen = (hi_ushort16 *)&uc_outmsg_len[1];
                    if(*pus_reportlen > 255 && *pus_reportlen <= 1496)//msg len larger than 255 
                    {
                        ui_reportmsglen = *pus_reportlen;
                        pst_tlvresponse->uc_length = 0;//set the beginning packet length identifier to 0
                    }
                    else
                    {
                        ui_reportmsglen = uc_outmsg_len[0];
                        if(ui_reportmsglen > 128)
                            pst_tlvresponse->uc_length = 0;//set the beginning packet length identifier to 0
                        else
                            pst_tlvresponse->uc_length = ui_reportmsglen;
                    }
                    if(us_leaf == VS_CTCOAM_LEAF_WAN_DUAL_STACK)
                    {
                        pst_tlvresponse->us_leaf = VS_CTCOAM_LEAF_WAN_CONFIGURATION;
                        pst_tlvresponse->us_leaf = (hi_ushort16)htons(pst_tlvresponse->us_leaf);
                    }
                    break;
                default:
                    break;
            }
        } else if (pst_tlv->uc_branch == HI_CTCOAM_BRANCH_VSOL_VOIP) {
            hi_ushort16 us_leaf = (hi_ushort16)ntohs(pst_tlv->us_leaf);
            hi_ushort16 *pus_reportlen;
            switch (us_leaf) {
                case VS_CTCOAM_LEAF_PLUS_SIP_PARAMETER_CONFIGURATION:
                    pus_reportlen = (hi_ushort16 *)&uc_outmsg_len[1];
                    if(*pus_reportlen > 255 && *pus_reportlen <= 1496)//msg len larger than 255 
                    {
                        ui_reportmsglen = *pus_reportlen;
                        pst_tlvresponse->uc_length = 0;//set the beginning packet length identifier to 0
                    }
                    else
                    {
                        ui_reportmsglen = uc_outmsg_len[0];
                        if(ui_reportmsglen > 128)
                            pst_tlvresponse->uc_length = 0;//set the beginning packet length identifier to 0
                        else
                            pst_tlvresponse->uc_length = ui_reportmsglen;
                    }
                    break;
                default:
                    break;
            }
        }

        ui_txmsglen += (sizeof(hi_ctcoam_tlv_s) + ui_reportmsglen);
        pst_tlvresponse = (hi_ctcoam_tlv_s *)((hi_uchar8 *)((hi_void *)pst_tlvresponse)
                                              + sizeof(hi_ctcoam_tlv_s) + ui_reportmsglen);
    }

    *pui_fillmsglen = ui_txmsglen;
    *ppv_fillback = pst_tlvresponse;
    g_uc_instance_tlv_id = HI_FALSE;

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_renew_local_instance
 Description  : 刷新本地实例索引
 Input        : hi_void *pv_instancemsg
                hi_void *pv_localinstance
 Output       : None
*****************************************************************************/
hi_uint32 hi_ctcoam_renew_local_instance(hi_void *pv_instancemsg, hi_void *pv_localinstance)
{
    hi_ushort16 us_leafTmp;
    hi_uchar8 *puc_value = HI_NULL;
    hi_ctcoam_tlv_s *pst_instancetlv = (hi_ctcoam_tlv_s *)pv_instancemsg;
    hi_ctcoam_instance_s *pst_localinstance = (hi_ctcoam_instance_s *)pv_localinstance;

    if ((HI_NULL == pv_instancemsg) || (HI_NULL == pv_localinstance)) {
        hi_ctcoam_dbg_err_print("Null Ptr.");
        return HI_RET_NULLPTR;
    }

    /* 刷新本地实例索引 */
    us_leafTmp = (hi_ushort16)ntohs(pst_instancetlv->us_leaf);
    switch (us_leafTmp) {
        case HI_CTC_INSTANCE_PORT_E:
            pst_localinstance->us_instancetype = us_leafTmp;
            pst_localinstance->uc_length = pst_instancetlv->uc_length;
            puc_value = (hi_uchar8 *)(pst_instancetlv + 1);
            pst_localinstance->un_value.uc_value = *puc_value;
            break;

        default:
            hi_ctcoam_dbg_err_print("Invalid Leaf(%u).", us_leafTmp);
            return HI_RET_INVALID_PARA;
    }

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_renew_local_instance_21
 Description  : 刷新本地实例索引，电信oam 2.1以上版本
 Input        : hi_void *pv_instancemsg
                hi_void *pv_localinstance
 Output       : None
*****************************************************************************/
hi_uint32 hi_ctcoam_renew_local_instance_21(hi_void *pv_instancemsg, hi_void *pv_localinstance)
{
    hi_ushort16 us_leafTmp;
    hi_uint32 *pui_value = HI_NULL;
    hi_ctcoam_tlv_s *pst_instancetlv = (hi_ctcoam_tlv_s *)pv_instancemsg;
    hi_ctcoam_instance_s *pst_localinstance = (hi_ctcoam_instance_s *)pv_localinstance;

    if ((HI_NULL == pv_instancemsg) || (HI_NULL == pv_localinstance)) {
        hi_ctcoam_dbg_err_print("Null Ptr.");
        return HI_RET_NULLPTR;
    }

    /* 刷新本地实例索引 */
    us_leafTmp = (hi_ushort16)ntohs(pst_instancetlv->us_leaf);
    switch (us_leafTmp) {
        case HI_CTC_INSTANCE_PORT_E:
        case HI_CTC_INSTANCE_PON_IF_E:
            pst_localinstance->us_instancetype = us_leafTmp;
            pst_localinstance->uc_length = pst_instancetlv->uc_length;
            pui_value = (hi_uint32 *)(pst_instancetlv + 1);
            pst_localinstance->un_value.ui_value = ntohl(*pui_value);
            break;

        default:
            hi_ctcoam_dbg_err_print("Invalid Leaf(%u).", us_leafTmp);
            return HI_RET_INVALID_PARA;
    }

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_deliver
 Description  : CTC OAM中get消息分发
 Input        : hi_uint32 ui_llidindex
                hi_void *pv_inmsg
                hi_uint32 ui_inmsglen
                hi_uint32 ui_exoam_type
 Output       : None
*****************************************************************************/
hi_uint32 hi_ctcoam_get_deliver(hi_uint32 ui_llidindex, hi_void *pv_inmsg, hi_uint32 ui_inmsglen, hi_uint32 ui_exoam_type)
{
    hi_uint32 ui_ret;
    hi_uint32 ui_rxmsglen_tmp = 0; /* 已处理的接收消息 */
    hi_uint32 ui_txmsglen = 0;
    hi_uint32 ui_txmsglen_tmp = 0;
    hi_uchar8 uc_rxvaluelen;
    hi_uchar8 uc_lastportid; /* 上一次的实例索引PortID */
    hi_uchar8 *puc_response = HI_NULL;
    hi_uchar8 *puc_responsetmp = HI_NULL;
    hi_ctcoam_instance_s st_localinstance; /* 本地实例索引 */
    hi_ctcoam_tlv_s *pst_tlv = (hi_ctcoam_tlv_s *)pv_inmsg;
    hi_ctcoam_tlv_s *pst_tlvresponse = HI_NULL;
    hi_ctcoam_msg_head_s *pst_ctcoamhead = HI_NULL;

    /* 参数检查 */
    if (HI_NULL == pv_inmsg) {
        hi_ctcoam_dbg_err_print("Null Ptr.");
        return HI_RET_NULLPTR;
    }

    /* 检查合法性 */
    if (ui_llidindex >= HI_MAX_LLID_NUM) {
        hi_ctcoam_dbg_err_print("Invalid LLID(%d).", ui_llidindex);
        return HI_RET_INVALID_PARA;
    }

    g_i_ctcoam_llid = ui_llidindex;

    /* 申请一个最大的动态内存 */
    puc_response = (hi_uchar8 *)hi_os_malloc(HI_EPON_MAX_OAM_LEN);
    if (HI_NULL == puc_response) {
        hi_ctcoam_dbg_err_print("Malloc fail.");
        return HI_RET_MALLOC_FAIL;
    }

    HI_OS_MEMSET_S(puc_response, HI_EPON_MAX_OAM_LEN, 0, HI_EPON_MAX_OAM_LEN);
    HI_OS_MEMSET_S((hi_void *)&st_localinstance, sizeof(hi_ctcoam_instance_s), 0, sizeof(hi_ctcoam_instance_s));

    /* 跳到填充位置,从OUI开始由本函数填充 */
    puc_responsetmp = puc_response;
    puc_responsetmp += sizeof(hi_epon_oam_s); /* 跳过以太头 */
    pst_ctcoamhead = (hi_ctcoam_msg_head_s *)((hi_void *)puc_responsetmp);

    /* 填充CTC OUI和Get回应标志 */
    ui_ret = hi_ctcoam_filloui(ui_exoam_type, pst_ctcoamhead->auc_oui);
    if (HI_RET_SUCC != ui_ret) {
        hi_ctcoam_dbg_err_print("Fill OUI Fail(%d).", ui_ret);
        hi_os_free(puc_response);
        puc_response = HI_NULL;
        return ui_ret;
    }

    pst_ctcoamhead->uc_opercode = HI_CTC_OPER_GET_RESPOND_E;

    /* 默认为不含实例索引 */
    st_localinstance.us_instancetype = HI_PON_MAX_USHORT;
    uc_lastportid   = HI_EPON_ONT_ALL_ETH_PORT;
    pst_tlvresponse = (hi_ctcoam_tlv_s *)((hi_void *)(pst_ctcoamhead + 1));


    while (ui_rxmsglen_tmp < ui_inmsglen) {
        if (HI_CTCOAM_PAD_VALUE == pst_tlv->uc_branch) {
            /* 如果是pad,直接退出,发送前面填充的OAM消息 */
            break;
        } else if (HI_CTCOAM_BRANCH_INSTANCE == pst_tlv->uc_branch) {
            g_uc_instance_tlv_branch = HI_CTCOAM_BRANCH_INSTANCE;
            /* 检查实例索引的长度是否正常 */
            if ((HI_CTC_INSTANCE_PORT_E == pst_tlv->us_leaf)
                && (sizeof(hi_uchar8) != pst_tlv->uc_length)) {
                /* 退出循环,将前面部分的消息发送出去 */
                break;
            }

            /* 将指针移到下一个TLV开始处 */
            uc_rxvaluelen = sizeof(hi_ctcoam_tlv_s) + sizeof(hi_uchar8);

            if ((ui_rxmsglen_tmp + uc_rxvaluelen) > ui_inmsglen) {
                /* 退出循环,将前面部分的消息发送出去 */
                break;
            }

            /* 刷新本地实例索引 */
            ui_ret = hi_ctcoam_renew_local_instance(pst_tlv, (hi_void *)&st_localinstance);
            if (HI_RET_SUCC != ui_ret) {
                hi_ctcoam_dbg_err_print("Renew Local Instance fail(%d).", ui_ret);
                /* 将前面已填充的消息发送出去 */
                break;
            }
            g_uc_instance_tlv_id = HI_TRUE;
        } else if (HI_CTCOAM_BRANCH_INSTANCE_21 == pst_tlv->uc_branch) {
            g_uc_instance_tlv_branch = HI_CTCOAM_BRANCH_INSTANCE_21;
            if (sizeof(hi_uint32) != pst_tlv->uc_length) {
                break;
            }

            /* 将指针移到下一个TLV开始处 */
            uc_rxvaluelen = sizeof(hi_ctcoam_tlv_s) + sizeof(hi_uint32);

            if ((ui_rxmsglen_tmp + uc_rxvaluelen) > ui_inmsglen) {
                /* 退出循环,将前面部分的消息发送出去 */
                break;
            }

            /* 刷新本地实例索引 */
            ui_ret = hi_ctcoam_renew_local_instance_21(pst_tlv, (hi_void *)&st_localinstance);
            if (HI_RET_SUCC != ui_ret) {
                hi_ctcoam_dbg_err_print("Renew Local Instance fail(%d).", ui_ret);
                /* 将前面已填充的消息发送出去 */
                break;
            }
            g_uc_instance_tlv_id = HI_TRUE;
        } else {
            /* Get消息一般只含branch和leaf,不含length和value */
            uc_rxvaluelen = sizeof(hi_ctcoam_tlv_s) - sizeof(hi_uchar8);

            /*含length和value的get消息处理*/
            if (pst_tlv->uc_branch == HI_CTCOAM_BRANCH_EXT_ATTR) {
                switch (ntohs(pst_tlv->us_leaf)) {
                    case HI_CTCOAM_LEAF_ALARM_ADMIN_STATE:
                        uc_rxvaluelen = sizeof(hi_ctcoam_tlv_s) + sizeof(hi_ctcoam_alarm_admin_state_s);
                        break;
                    case HI_CTCOAM_LEAF_ALARM_THRESHOLD:
                        uc_rxvaluelen = sizeof(hi_ctcoam_tlv_s) + sizeof(hi_ctcoam_alarm_threshold_s);
                        break;
                    default:
                        break;
                }

            }

            if ((ui_rxmsglen_tmp + uc_rxvaluelen) > ui_inmsglen) {
                /* 退出循环,将前面部分的消息发送出去 */
                break;
            }
            ui_ret = hi_ctcoam_get_proc(ui_llidindex, (hi_void *)&st_localinstance,
                                        &uc_lastportid, (hi_void *)pst_tlv, ui_exoam_type,
                                        (hi_void **)&pst_tlvresponse, &ui_txmsglen_tmp);
            if (HI_RET_SUCC != ui_ret) {
                /* 退出循环,将前面部分的消息发送出去 */
                ui_txmsglen += sizeof(hi_ctcoam_tlv_s);
                pst_tlvresponse->uc_branch = pst_tlv->uc_branch;
                pst_tlvresponse->us_leaf   = pst_tlv->us_leaf;
                pst_tlvresponse->uc_length = HI_OAM_VAR_INDI_NO_RESPONSE;
                hi_ctcoam_dbg_err_print("Get Process Fail(%d).", ui_ret);
                break;
            }
        }

        pst_tlv = (hi_ctcoam_tlv_s *)((hi_uchar8 *)pst_tlv + uc_rxvaluelen);
        ui_rxmsglen_tmp += uc_rxvaluelen;
        ui_txmsglen += ui_txmsglen_tmp;
    }

    /* 发送扩展OAM消息 */
    ui_ret = hi_oam_api_txmsgproc(ui_llidindex, (hi_void *)puc_response,
                                  (ui_txmsglen + sizeof(hi_epon_oam_s) + sizeof(hi_ctcoam_msg_head_s)),
                                  HI_OAM_CODE_ORGANIZATION_SPEC_E);
    if (HI_RET_SUCC != ui_ret) {
        hi_ctcoam_dbg_err_print("Send extoam fail(%d)", ui_ret);
    }

    hi_os_free(puc_response);
    puc_response = HI_NULL;

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_set_proc
 Description  : set process
 Input        : hi_uint32 ui_llidindex
                hi_void *pv_localinstance
                hi_uchar8 *puc_lastportid
                hi_void *pv_tlv
                hi_uint32 ui_exoam_type,
                hi_void **ppv_fillback
                hi_uint32 *pui_fillmsglen
 Output       : None
*****************************************************************************/
hi_uint32 hi_ctcoam_set_proc(hi_uint32 ui_llidindex, hi_void *pv_localinstance, hi_uchar8 *puc_lastportid,
                             hi_void *pv_tlv, hi_uint32 ui_exoam_type, hi_void **ppv_fillback, hi_uint32 *pui_fillmsglen)
{
    if ((HI_NULL == pv_localinstance)
        || (HI_NULL == ppv_fillback)
        || (HI_NULL == *ppv_fillback)
        || (HI_NULL == puc_lastportid)
        || (HI_NULL == pui_fillmsglen)) {
        hi_ctcoam_dbg_err_print("Null Ptr.");
        return HI_RET_NULLPTR;
    }
    
    hi_uint32 ui_ret;
    hi_uint32 ui_startloop;
    hi_uint32 ui_endloop;
    hi_uint32 ui_loop;
    hi_uint32 ui_exoamtype_tmp;
    hi_uint32 ui_txmsglen;
    hi_uchar8 uc_needinstance;
    hi_uchar8 uc_txvaluelen;
    hi_uchar8 *puc_value = HI_NULL;
    hi_uint32 *pui_value = HI_NULL;
    hi_uchar8 *puc_txvlauelen = HI_NULL;
    //hi_uint32 ui_proc_allport = HI_FALSE;
    hi_ctcoam_instance_s st_localinstance_tmp;
    hi_ctcoam_instance_s *pst_localinstance = (hi_ctcoam_instance_s *)pv_localinstance;
    hi_ctcoam_tlv_s *pst_tlv = (hi_ctcoam_tlv_s *)pv_tlv;
    hi_ctcoam_tlv_s *pst_tlvresponse = (hi_ctcoam_tlv_s *)(*ppv_fillback);
    hi_ctcoam_proc_pfn pfn_proc = HI_NULL;


    /* 检查合法性 */
    if (ui_llidindex >= HI_MAX_LLID_NUM) {
        hi_ctcoam_dbg_err_print("Invalid LLID(%d).", ui_llidindex);
        return HI_RET_INVALID_PARA;
    }

    HI_OS_MEMSET_S((hi_void *)&st_localinstance_tmp, sizeof(hi_ctcoam_instance_s), 0, sizeof(hi_ctcoam_instance_s));

    /* 搜索到对应的处理函数 */
    ui_ret = hi_ctcoam_search_ex_oam_tree((hi_void *)pst_tlv, HI_CTC_OPER_SET_E, &pfn_proc,
                                          &uc_txvaluelen, &uc_needinstance, &ui_exoamtype_tmp);
    if (HI_RET_SUCC != ui_ret) {
        hi_ctcoam_dbg_err_print("Search Exoam Tree Fail(%d).", ui_ret);
        pst_tlvresponse->uc_branch = pst_tlv->uc_branch;
        pst_tlvresponse->us_leaf = pst_tlv->us_leaf;
        pst_tlvresponse->uc_length = HI_CTCOAM_VAR_BAD_PARA_E;
        *pui_fillmsglen = sizeof(hi_ctcoam_tlv_s);
        return ui_ret;
    }

    if (HI_NULL == pfn_proc) {
        hi_ctcoam_dbg_err_print("Null Process Function.");
        return HI_RET_NOTSUPPORT;
    }

    if (ui_exoamtype_tmp != ui_exoam_type) {
        hi_ctcoam_dbg_err_print("Invalid Type.");
        return HI_RET_INVALID_PARA;
    }
    if (HI_CTC_INSTANCE_PORT_E == pst_localinstance->us_instancetype) {
        if (HI_CTCOAM_BRANCH_INSTANCE == g_uc_instance_tlv_branch) {
            if (HI_EPON_ONT_ALL_ETH_PORT == pst_localinstance->un_value.uc_value) {
                /* 则循环所有的Eth端口 */
                ui_startloop = HI_EPON_ONT_FIRST_ETH_PORT;
                ui_endloop   = hi_oam_get_eth_port_num();
                ui_endloop  += ui_startloop - 1;
                //ui_proc_allport = HI_TRUE;
            } else {
                /* 只处理一个端口 */
                ui_startloop = pst_localinstance->un_value.uc_value;
                ui_endloop   = ui_startloop;
            }
        } else if (HI_CTCOAM_BRANCH_INSTANCE_21 == g_uc_instance_tlv_branch) {
            if (HI_EPON_ONT_ALL_ETH_PORT_21 == (pst_localinstance->un_value.ui_value & 0XFFFF)) {
                /* 则循环所有的Eth端口 */
                ui_startloop = HI_EPON_ONT_FIRST_ETH_PORT;
                ui_endloop   = hi_oam_get_eth_port_num();
                ui_endloop  += ui_startloop - 1;
                //ui_proc_allport = HI_TRUE;
            } else {
                /* 只处理一个端口 */
                ui_startloop = pst_localinstance->un_value.ui_value & 0XFFFF;
                ui_endloop   = ui_startloop;
            }
        } else {
            hi_ctcoam_dbg_err_print("unknown branch(%d).", g_uc_instance_tlv_branch);
            return HI_RET_FAIL;
        }
    } else if (HI_CTC_INSTANCE_PON_IF_E == pst_localinstance->us_instancetype) {
        if ((HI_CTCOAM_BRANCH_INSTANCE == g_uc_instance_tlv_branch)
            || (HI_CTCOAM_BRANCH_INSTANCE_21 == g_uc_instance_tlv_branch)) {
            if ((0 != pst_localinstance->un_value.uc_value)
                && (1 != pst_localinstance->un_value.uc_value)) {
                ui_startloop = 0;
                ui_endloop = ui_startloop;
            } else {
                /* 只处理一个端口 */
                ui_startloop = pst_localinstance->un_value.uc_value;
                ui_endloop   = ui_startloop;
            }
        } else {
            hi_ctcoam_dbg_err_print("unknown branch(%d).", g_uc_instance_tlv_branch);
            return HI_RET_FAIL;
        }
    } else {
        /* 只处理一个端口 */
        ui_startloop = pst_localinstance->un_value.uc_value;
        ui_endloop   = ui_startloop;
    }

    ui_txmsglen = 0;
    st_localinstance_tmp.us_instancetype = pst_localinstance->us_instancetype;
    st_localinstance_tmp.uc_length = pst_localinstance->uc_length;

    for (ui_loop = ui_startloop; ui_loop <= ui_endloop; ui_loop++) {
        st_localinstance_tmp.un_value.ui_value = ui_loop;
        /* 当回应消息需要实例索引,同时本实例索引和上一次处理的实例索引不同时,
           则需要在回应消息中添加实例索引 */
        if ((HI_TRUE == uc_needinstance)
            && (*puc_lastportid != st_localinstance_tmp.un_value.uc_value)) {
            pst_tlvresponse->us_leaf   = (hi_ushort16)htons(pst_localinstance->us_instancetype);
            pst_tlvresponse->uc_length = pst_localinstance->uc_length;
            if (g_uc_instance_tlv_branch == HI_CTCOAM_BRANCH_INSTANCE) {
                pst_tlvresponse->uc_branch = HI_CTCOAM_BRANCH_INSTANCE;
                puc_value  = (hi_uchar8 *)(pst_tlvresponse + 1);
                *puc_value = pst_localinstance->un_value.uc_value;
                *puc_lastportid = *puc_value;
                pst_tlvresponse = (hi_ctcoam_tlv_s *)(puc_value + 1);

                ui_txmsglen += (sizeof(hi_ctcoam_tlv_s) + sizeof(hi_uchar8));
            } else if (g_uc_instance_tlv_branch == HI_CTCOAM_BRANCH_INSTANCE_21) {
                pst_tlvresponse->uc_branch = HI_CTCOAM_BRANCH_INSTANCE_21;
                pui_value  = (hi_uint32 *)(pst_tlvresponse + 1);
                *pui_value = pst_localinstance->un_value.ui_value;
                *puc_lastportid = *pui_value  & 0xff;
                *pui_value = htonl(*pui_value);
                pst_tlvresponse = (hi_ctcoam_tlv_s *)(pui_value + 1);

                ui_txmsglen += (sizeof(hi_ctcoam_tlv_s) + sizeof(hi_uint32));
            } else {
                return HI_RET_FAIL;
            }
        } else if ((HI_TRUE == uc_needinstance)
                   && (*puc_lastportid == st_localinstance_tmp.un_value.uc_value)) {
            /* do nothing */
        } else {
            /* 将上一次处理的PortID改为初始值 */
            *puc_lastportid = HI_EPON_ONT_ALL_ETH_PORT;
        }

        /* 填充回应消息的Branch和Leaf */
        pst_tlvresponse->uc_branch = pst_tlv->uc_branch;
        pst_tlvresponse->us_leaf = pst_tlv->us_leaf;
        hi_ushort16 uc_leaf = (hi_ushort16)htons(pst_tlvresponse->us_leaf);
        if(pst_tlvresponse->uc_branch == HI_CTCOAM_BRANCH_EXT_ATTR && uc_leaf == VS_CTCOAM_LEAF_WAN_CONFIGURATION)
        {
            hi_ushort16 send_leaf = VS_CTCOAM_LEAF_WAN_DUAL_STACK;
            send_leaf = (hi_ushort16)ntohs(send_leaf);
            pst_tlvresponse->us_leaf = send_leaf;
        }

        if (HI_CTCOAM_CHANGING_TLV_LEN == uc_txvaluelen) {
            /* 对于变长消息,实际的长度需要从Set OAM中获取 */
            puc_txvlauelen = &(pst_tlv->uc_length);
        } else {
            /* 对于定长消息,其长度作为输入 */
            puc_txvlauelen = &uc_txvaluelen;
        }

        /*ponif接口，将端口号转发为内部pon口id 5 */
        if (st_localinstance_tmp.us_instancetype == HI_CTC_INSTANCE_PON_IF_E) {
            st_localinstance_tmp.un_value.uc_value = HI_LSW_PORT_NNI_PON_E;
        }

        /* 调用处理函数处理 */
        ui_ret = pfn_proc(ui_llidindex, &st_localinstance_tmp, (hi_void *)(pst_tlv + 1),
                          puc_txvlauelen, HI_NULL);
        if ((HI_RET_SUCC != ui_ret)
            && ((hi_uint32)HI_RET_INVALID_PARA != ui_ret)
            && ((hi_uint32)HI_RET_NOTSUPPORT != ui_ret)) {
            hi_ctcoam_dbg_err_print("Process Fail(%d).", ui_ret);
            return ui_ret;
        }

        if (HI_RET_SUCC == ui_ret) {
            /* Set回应在Length域位置填充返回码 */
            pst_tlvresponse->uc_length = HI_CTCOAM_SET_OK_E;
        } else if ((hi_uint32)HI_RET_INVALID_PARA == ui_ret) {
            /* Set回应在Length域位置填充返回码 */
            pst_tlvresponse->uc_length = HI_CTCOAM_VAR_BAD_PARA_E;
        } else {
            /* Set回应在Length域位置填充返回码 */
            pst_tlvresponse->uc_length = HI_CTCOAM_VAR_NO_RESOURCE_E;
        }

        /* 回填指针后移 */
        pst_tlvresponse++;
        ui_txmsglen += sizeof(hi_ctcoam_tlv_s);
    }

    *pui_fillmsglen = ui_txmsglen;
    *ppv_fillback = pst_tlvresponse;

    return HI_RET_SUCC;
}

static hi_uint32 hi_ctcoam_set_deliver_loop1(hi_ctcoam_tlv_s *pst_tlv, hi_uchar8 *uc_rxvaluelen,
        hi_uint32 ui_rxmsglen_tmp, hi_uint32 ui_inmsglen, hi_ctcoam_instance_s *st_localinstance)
{
    hi_uint32 ui_ret;
    if (pst_tlv->uc_branch == HI_CTCOAM_PAD_VALUE)
        /* 如果是pad,直接退出,发送前面填充的OAM消息 */
        return HI_RET_LOOP_BREAK;

    if (pst_tlv->uc_branch == HI_CTCOAM_BRANCH_INSTANCE) {
        g_uc_instance_tlv_branch = HI_CTCOAM_BRANCH_INSTANCE;
        /* 检查实例索引的长度是否正常 */
        if ((pst_tlv->us_leaf == HI_CTC_INSTANCE_PORT_E)
            && (pst_tlv->uc_length != sizeof(hi_uchar8)))
            /* 退出循环,将前面部分的消息发送出去 */
            return HI_RET_LOOP_BREAK;

        /* 将指针移到下一个TLV开始处 */
        *uc_rxvaluelen = HI_CTCOAM_INSTANCT_INDEX_TLV_LENGTH;

        if ((ui_rxmsglen_tmp + *uc_rxvaluelen) > ui_inmsglen)
            /* 退出循环,将前面部分的消息发送出去 */
            return HI_RET_LOOP_BREAK;

        /* 刷新本地实例索引 */
        ui_ret = hi_ctcoam_renew_local_instance(pst_tlv, (hi_void *)st_localinstance);
        if (ui_ret != HI_RET_SUCC)
            /* 将前面已填充的消息发送出去 */
            return HI_RET_LOOP_BREAK;
    } else if (pst_tlv->uc_branch == HI_CTCOAM_BRANCH_INSTANCE_21) {
        g_uc_instance_tlv_branch = HI_CTCOAM_BRANCH_INSTANCE_21;
        if (pst_tlv->uc_length != sizeof(hi_uint32))
            return HI_RET_LOOP_BREAK;

        /* 将指针移到下一个TLV开始处 */
        *uc_rxvaluelen = sizeof(hi_ctcoam_tlv_s) + sizeof(hi_uint32);
        if ((ui_rxmsglen_tmp + *uc_rxvaluelen) > ui_inmsglen)
            /* 退出循环,将前面部分的消息发送出去 */
            return HI_RET_LOOP_BREAK;

        /* 刷新本地实例索引 */
        ui_ret = hi_ctcoam_renew_local_instance_21(pst_tlv, (hi_void *)st_localinstance);
        if (ui_ret != HI_RET_SUCC) {
            hi_ctcoam_dbg_err_print("Renew Local Instance fail(%d).", ui_ret);
            /* 将前面已填充的消息发送出去 */
            return HI_RET_LOOP_BREAK;
        }
    }
    return HI_RET_LOOP_SUCC;
}

static hi_uint32 hi_ctcoam_set_deliver_loop2(hi_uint32 ui_llidindex, hi_ctcoam_tlv_s *pst_tlv, hi_uchar8 *uc_rxvaluelen,
        hi_uint32 ui_rxmsglen_tmp, hi_uint32 ui_inmsglen)
{
    hi_uint32 ui_ret, ui_state;
    hi_oam_notifier_data_s st_notifier_data;
    struct hi_epon_llidstate st_sta = {0};
    if (pst_tlv->uc_branch == HI_CTCOAM_BRANCH_EXT_ATTR) {
        HI_IPC_CALL("hi_epon_get_llidstate", &st_sta, sizeof(st_sta));
        if ((g_uc_reg_flag == HI_FALSE) && (st_sta.regflag == HI_TRUE)) {
            st_notifier_data.em_type = HI_OAM_NOTIFIY_OAMREG_STATE_E;
            st_notifier_data.ui_llid = 0;
            st_notifier_data.ui_state = HI_OAM_NOTIFY_REG_SUCC_E;
#ifdef CONFIG_PLATFORM_OPENWRT
            HI_IPC_CALL("hi_sml_owal_led_epon_proc", &st_notifier_data);
#else
            ui_ret = hi_notifier_call(HI_OAM_NOTIFIY_NAME, &st_notifier_data);
            if (ui_ret != HI_RET_SUCC)
                return ui_ret;
#endif
            g_uc_reg_flag = HI_TRUE;
            ui_state = HI_FALSE;
            ui_ret = HI_IPC_CALL("hi_pon_set_fail_regstate", &ui_state);
            if (ui_ret != HI_RET_SUCC)
                return ui_ret;
        }
    }
    if (((pst_tlv->uc_branch == HI_CTCOAM_BRANCH_EXT_OPER) 
            && (htons(pst_tlv->us_leaf) == HI_CTCOAM_LEAF_ACT_RESET_ONU))
        || ((pst_tlv->uc_branch == HI_CTCOAM_BRANCH_STD_OPER)
            && (htons(pst_tlv->us_leaf) == HI_CTCOAM_LEAF_STD_ACT_NEG_RESTART_CONFIG)))
        /* Set中有两个特例"Reset ONU"和"重新自协商",
            只有Branch和Leaf,没有Length和value */
        *uc_rxvaluelen = (sizeof(hi_ctcoam_tlv_s) - sizeof(hi_uchar8));
    else
        /* Set消息只含branch和leaf,也含有length和value */
        *uc_rxvaluelen = (sizeof(hi_ctcoam_tlv_s) + pst_tlv->uc_length);

    if ((ui_rxmsglen_tmp + *uc_rxvaluelen) > ui_inmsglen)
        /* 退出循环,将前面部分的消息发送出去 */
        return HI_RET_LOOP_BREAK;

    return HI_RET_LOOP_SUCC;
}

static void hi_ctcoam_puc_response_free(hi_uchar8 **p)
{
    if (p == NULL || *p == NULL)
        return;
    hi_os_free(*p);
    *p = NULL;
}

/*****************************************************************************
 Prototype    : hi_ctcoam_set_deliver
 Description  : set process for deliver
 Input        : hi_uint32 ui_llidindex
                hi_void *pv_inmsg
                hi_uint32 ui_inmsglen
 Output       : None
*****************************************************************************/
hi_uint32 hi_ctcoam_set_deliver(hi_uint32 ui_llidindex, hi_void *pv_inmsg, hi_uint32 ui_inmsglen, hi_uint32 ui_exoam_type)
{
    hi_uint32 ui_ret;
    hi_uint32 ui_rxmsglen_tmp = 0; /* 已处理的接收消息 */
    hi_uint32 ui_txmsglen    = 0;
    hi_uint32 ui_txmsglen_tmp = 0;
    hi_uchar8 uc_rxvaluelen = 0;
    hi_uchar8 uc_lastportid; /* 上一次的实例索引PortID */
    hi_uchar8 *puc_response = HI_NULL;
    hi_uchar8 *puc_responsetmp = HI_NULL;
    hi_ctcoam_instance_s st_localinstance; /* 本地实例索引 */
    hi_ctcoam_tlv_s *pst_tlv = (hi_ctcoam_tlv_s *)pv_inmsg;
    hi_ctcoam_tlv_s *pst_tlvresponse = HI_NULL;
    hi_ctcoam_msg_head_s *pst_ctcoamhead = HI_NULL;

    /* 参数检查 */
    if (pv_inmsg == HI_NULL) {
        hi_ctcoam_dbg_err_print("Null Ptr.");
        return HI_RET_NULLPTR;
    }

    /* 申请一个最大的动态内存 */
    puc_response = (hi_uchar8 *)hi_os_malloc(HI_EPON_MAX_OAM_LEN);
    if (puc_response == HI_NULL) {
        hi_ctcoam_dbg_err_print("Malloc Fail.");
        return HI_RET_MALLOC_FAIL;
    }

    HI_OS_MEMSET_S(puc_response, HI_EPON_MAX_OAM_LEN, 0, HI_EPON_MAX_OAM_LEN);
    HI_OS_MEMSET_S((hi_void *)&st_localinstance, sizeof(hi_ctcoam_instance_s), 0, sizeof(hi_ctcoam_instance_s));

    /* 跳到填充位置,从OUI开始由本函数填充 */
    puc_responsetmp = puc_response;
    puc_responsetmp += sizeof(hi_epon_oam_s); /* 跳过以太头 */
    pst_ctcoamhead  = (hi_ctcoam_msg_head_s *)((hi_void *)puc_responsetmp);

    /* 填充CTC OUI和Get回应标志 */
    ui_ret = hi_ctcoam_filloui(ui_exoam_type, pst_ctcoamhead->auc_oui);
    if (ui_ret != HI_RET_SUCC) {
        hi_ctcoam_dbg_err_print("Fill OUI Fail(%d).", ui_ret);
        hi_ctcoam_puc_response_free(&puc_response);
        return ui_ret;
    }

    pst_ctcoamhead->uc_opercode = HI_CTC_OPER_SET_RESPOND_E;

    /* 默认为不含实例索引 */
    st_localinstance.us_instancetype = HI_PON_MAX_USHORT;
    uc_lastportid   = HI_EPON_ONT_ALL_ETH_PORT;
    pst_tlvresponse = (hi_ctcoam_tlv_s *)((hi_void *)(pst_ctcoamhead + 1));

    while (ui_rxmsglen_tmp < ui_inmsglen) {
        if ((pst_tlv->uc_branch == HI_CTCOAM_PAD_VALUE) || (pst_tlv->uc_branch == HI_CTCOAM_BRANCH_INSTANCE)
            || (pst_tlv->uc_branch == HI_CTCOAM_BRANCH_INSTANCE_21)) {
            ui_ret = hi_ctcoam_set_deliver_loop1(pst_tlv, &uc_rxvaluelen, ui_rxmsglen_tmp,
                                                ui_inmsglen, &st_localinstance);
            if (ui_ret == HI_RET_LOOP_BREAK)
                break;
        } else {
            ui_ret = hi_ctcoam_set_deliver_loop2(ui_llidindex, pst_tlv, &uc_rxvaluelen, ui_rxmsglen_tmp, ui_inmsglen);
            if (ui_ret == HI_RET_LOOP_BREAK) {
                break;
            } else if (ui_ret != HI_RET_LOOP_SUCC) {
                hi_ctcoam_puc_response_free(&puc_response);
                return ui_ret;
            }

            ui_ret = hi_ctcoam_set_proc(ui_llidindex, (hi_void *)&st_localinstance,
                                        &uc_lastportid, (hi_void *)pst_tlv, ui_exoam_type,
                                        (hi_void **)&pst_tlvresponse, &ui_txmsglen_tmp);
            if (ui_ret != HI_RET_SUCC) {
                hi_ctcoam_dbg_err_print("Set Process Fail(%d).", ui_ret);
                ui_txmsglen += ui_txmsglen_tmp;
                /* 退出循环,将前面部分的消息发送出去 */
                break;
            }
        }
        pst_tlv = (hi_ctcoam_tlv_s *)((hi_uchar8 *)pst_tlv + uc_rxvaluelen);
        ui_rxmsglen_tmp += uc_rxvaluelen;
        ui_txmsglen += ui_txmsglen_tmp;
    }

    /* 发送扩展OAM消息 */
    ui_ret = hi_oam_api_txmsgproc(ui_llidindex, (hi_void *)puc_response,
                                  (ui_txmsglen + sizeof(hi_epon_oam_s) + sizeof(hi_ctcoam_msg_head_s)),
                                  HI_OAM_CODE_ORGANIZATION_SPEC_E);
    if (ui_ret != HI_RET_SUCC)
        hi_ctcoam_dbg_err_print("Send OAM Fail(%d).", ui_ret);

    hi_ctcoam_puc_response_free(&puc_response);

    /* 由于消息已经写入STD OAM模块的队列,不能释放 */

    return ui_ret;
}

static uint32_t hi_ctcoam_get_loid(hi_uchar8 *puc_responsetmp, hi_uchar8 *puc_response, hi_uint32 *ui_txmsglen)
{
	uint32_t ret = EOK;
	uint32_t ui_padlen;
#ifndef CONFIG_PLATFORM_OPENWRT
	hi_sysinfo_data_s st_info;
    if (HI_IPC_CALL("hi_sysinfo_data_get", &(st_info)) != HI_RET_SUCC)
        return HI_RET_FAIL;
    /*填充loid和password*/
    if (strlen(st_info.ac_loid) < HI_CTC_ONU_AUTH_LOID_LEN) {
        ui_padlen = HI_CTC_ONU_AUTH_LOID_LEN - strlen(st_info.ac_loid);
        ret |= memset_s(puc_responsetmp, ui_padlen, 0, ui_padlen);
        ret |= memcpy_s(puc_responsetmp + ui_padlen, HI_EPON_MAX_OAM_LEN - (puc_responsetmp - puc_response) - ui_padlen,
                       st_info.ac_loid, strlen(st_info.ac_loid));
    } else
        ret |= memcpy_s(puc_responsetmp, HI_EPON_MAX_OAM_LEN - (puc_responsetmp - puc_response),
                       st_info.ac_loid, sizeof(st_info.ac_loid));
#else
    if (strlen(g_oam_loid_info.loid) < HI_CTC_ONU_AUTH_LOID_LEN) {
        ui_padlen = HI_CTC_ONU_AUTH_LOID_LEN - strlen(g_oam_loid_info.loid);
        ret |= memset_s(puc_responsetmp, ui_padlen, 0, ui_padlen);
        ret |= memcpy_s(puc_responsetmp + ui_padlen, HI_EPON_MAX_OAM_LEN - (puc_responsetmp - puc_response) - ui_padlen,
                       g_oam_loid_info.loid, strlen(g_oam_loid_info.loid));
    } else
        ret |= memcpy_s(puc_responsetmp, HI_EPON_MAX_OAM_LEN - (puc_responsetmp - puc_response),
						g_oam_loid_info.loid, sizeof(g_oam_loid_info.loid));
#endif
    puc_responsetmp += HI_CTC_ONU_AUTH_LOID_LEN;
    *ui_txmsglen     += HI_CTC_ONU_AUTH_LOID_LEN;
#ifndef CONFIG_PLATFORM_OPENWRT
    if (strlen(st_info.ac_lopwd) < HI_CTC_ONU_AUTH_PASSWORD_LEN) {
        ui_padlen = HI_CTC_ONU_AUTH_PASSWORD_LEN - strlen(st_info.ac_lopwd);
        ret |= memset_s(puc_responsetmp, ui_padlen, 0, ui_padlen);
        ret |= memcpy_s(puc_responsetmp + ui_padlen, HI_EPON_MAX_OAM_LEN - (puc_responsetmp - puc_response) - ui_padlen,
                       st_info.ac_lopwd, strlen(st_info.ac_lopwd));
    } else
        ret |= memcpy_s(puc_responsetmp, HI_EPON_MAX_OAM_LEN - (puc_responsetmp - puc_response),
                       st_info.ac_lopwd, sizeof(st_info.ac_lopwd));
#else
    if (strlen(g_oam_loid_info.password) < HI_CTC_ONU_AUTH_PASSWORD_LEN) {
        ui_padlen = HI_CTC_ONU_AUTH_PASSWORD_LEN - strlen(g_oam_loid_info.password);
        ret |= memset_s(puc_responsetmp, ui_padlen, 0, ui_padlen);
        ret |= memcpy_s(puc_responsetmp + ui_padlen, HI_EPON_MAX_OAM_LEN - (puc_responsetmp - puc_response) - ui_padlen,
                       g_oam_loid_info.password, strlen(g_oam_loid_info.password));
    } else
        ret |= memcpy_s(puc_responsetmp, HI_EPON_MAX_OAM_LEN - (puc_responsetmp - puc_response),
                       g_oam_loid_info.password, sizeof(g_oam_loid_info.password));
#endif
	if (ret != EOK) {
		hi_ctcoam_dbg_err_print("memcpy Fail");
		return HI_RET_FAIL;
	}
    puc_responsetmp += HI_CTC_ONU_AUTH_PASSWORD_LEN;
    *ui_txmsglen += HI_CTC_ONU_AUTH_PASSWORD_LEN;
	return HI_RET_SUCC;
}

static hi_uint32 hi_ctcoam_onu_auth_rsp(hi_uint32 ui_llidindex, hi_void *pv_inmsg, hi_uint32 ui_inmsglen, hi_uint32 ui_exoam_type)
{
    hi_uint32 ui_ret = HI_RET_SUCC;
    hi_uint32 ui_txmsglen = 0;
    hi_uchar8 *puc_response = HI_NULL;
    hi_uchar8 *puc_responsetmp = HI_NULL;
    hi_ctcoam_msg_head_s *pst_ctcoamhead = HI_NULL;

    /* 申请一个最大的动态内存 */
    puc_response = (hi_uchar8 *)hi_os_malloc(HI_EPON_MAX_OAM_LEN);
    if (HI_NULL == puc_response) {
        hi_ctcoam_dbg_err_print("Malloc Fail.");
        return HI_RET_MALLOC_FAIL;
    }

    /* 跳到填充位置,从OUI开始由本函数填充 */
    puc_responsetmp = puc_response;
    puc_responsetmp += sizeof(hi_epon_oam_s); /* 跳过以太头 */
    ui_txmsglen = sizeof(hi_epon_oam_s);
    pst_ctcoamhead = (hi_ctcoam_msg_head_s *)((hi_void *)puc_responsetmp);

    /* 填充CTC OUI和Get回应标志 */
    ui_ret = hi_ctcoam_filloui(ui_exoam_type, pst_ctcoamhead->auc_oui);
    if (HI_RET_SUCC != ui_ret) {
        hi_ctcoam_dbg_err_print("Fill OUI Fail(%d).", ui_ret);
        hi_os_free(puc_response);
        puc_response = HI_NULL;
        return ui_ret;
    }

    pst_ctcoamhead->uc_opercode = HI_CTC_OPER_ONU_AUTH_E;
    puc_responsetmp += sizeof(hi_ctcoam_msg_head_s);
    ui_txmsglen += sizeof(hi_ctcoam_msg_head_s);

    ((hi_ctcoam_onuauth_head_s *)puc_responsetmp)->uc_authcode = HI_CTC_AUTH_RESPONSE_E;
    ((hi_ctcoam_onuauth_head_s *)puc_responsetmp)->us_lenght   = (hi_ushort16)htons(HI_CTC_ONU_AUTH_RSP_LEN);/*lint !e572*/
    ((hi_ctcoam_onuauth_head_s *)puc_responsetmp)->uc_authtype = HI_CTC_ONU_AUTH_TYPE;
    puc_responsetmp += sizeof(hi_ctcoam_onuauth_head_s);
    ui_txmsglen += sizeof(hi_ctcoam_onuauth_head_s);

	ui_ret = hi_ctcoam_get_loid(puc_responsetmp, puc_response, &ui_txmsglen);
	if (ui_ret != HI_RET_SUCC) {
        hi_os_free(puc_response);
        puc_response = HI_NULL;
        return ui_ret;
	}

    /* 发送扩展OAM消息 */
    ui_ret = hi_oam_api_txmsgproc(ui_llidindex, (hi_void *)puc_response, ui_txmsglen, HI_OAM_CODE_ORGANIZATION_SPEC_E);
    if (HI_RET_SUCC != ui_ret) {
        hi_ctcoam_dbg_err_print("Send TxMsg Fail(%d).", ui_ret);
    }

    hi_os_free(puc_response);
    puc_response = HI_NULL;

    return ui_ret;
}

#ifdef CONFIG_PLATFORM_OPENWRT
HI_DEF_IPC(hi_oam_set_loid, hi_ctcoam_onu_loid_s *, loid_info)
{
	(void)memcpy_s(&g_oam_loid_info, sizeof(hi_ctcoam_onu_loid_s), loid_info, sizeof(hi_ctcoam_onu_loid_s));
	return HI_RET_SUCC;
}
#endif


hi_uint32 hi_ctcoam_onu_auth(hi_uint32 ui_llidindex, hi_void *pv_inmsg, hi_uint32 ui_inmsglen, hi_uint32 ui_exoam_type)
{
    hi_uint32 ui_ret = HI_RET_SUCC;
    hi_uint32 ui_state;
    hi_uchar8 uc_authcode;
    hi_ctcoam_onuauth_head_s *pst_content = HI_NULL;
    hi_oam_notifier_data_s st_notifier_data;

    /* 参数检查 */
    if (HI_NULL == pv_inmsg) {
        hi_ctcoam_dbg_err_print("Null Ptr.");
        return HI_RET_NULLPTR;
    }

    pst_content = (hi_ctcoam_onuauth_head_s *)pv_inmsg;
    uc_authcode = pst_content->uc_authcode;

    switch (uc_authcode) {
        case HI_CTC_AUTH_REQUEST_E:
            ui_ret = hi_ctcoam_onu_auth_rsp(ui_llidindex, pv_inmsg, ui_inmsglen, ui_exoam_type);
            break;

        case HI_CTC_AUTH_SUCCESS_E:
            break;
        case HI_CTC_AUTH_FAIL_E:
            st_notifier_data.em_type = HI_OAM_NOTIFIY_LOIDREG_STATE_E;
            st_notifier_data.ui_llid = 0;
            st_notifier_data.ui_state = HI_CTC_AUTH_FAIL_E;
#ifdef CONFIG_PLATFORM_OPENWRT
            HI_IPC_CALL("hi_sml_owal_led_epon_proc", &st_notifier_data);
#else
            ui_ret = hi_notifier_call(HI_OAM_NOTIFIY_NAME, &st_notifier_data);
            if (HI_RET_SUCC != ui_ret) {
                return ui_ret;
            }
#endif
            ui_state = HI_TRUE;
            ui_ret = HI_IPC_CALL("hi_pon_set_fail_regstate", &ui_state);
            if (HI_RET_SUCC != ui_ret) {
                            return ui_ret;
            }
            break;
        default:
            return HI_RET_MSG_UNKNOWN;
    }

    return ui_ret;
}

/*****************************************************************************
 Prototype    : hi_ctcoam_xepon_churning
 Description  : CTCOAM三层搅动密钥交换
 Input        : hi_uint32 ui_llidindex
                hi_void *pv_inmsg
                hi_uint32 ui_inmsglen
                hi_uint32 ui_exoam_type
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_xepon_churning(hi_uint32 ui_llidindex, hi_void *pv_inmsg, hi_uint32 ui_inmsglen, hi_uint32 ui_exoam_type)
{
    hi_uint32 ui_ret;
    hi_uint32 ui_txmsglen = 0;
    hi_uint32 ui_keylen   = 0;
    hi_uint32 ui_respcode = 0;
    hi_uchar8 uc_keyindex;
    hi_uchar8 uc_newkeyindex;
    hi_uint32 i;
    hi_uchar8 auc_newkey[16];
    hi_uchar8 *puc_response = HI_NULL;
    hi_uchar8 *puc_responsetmp = HI_NULL;
    hi_uint32 ui_code;
    hi_ctcoam_churning_code_s *pst_rxchurningcode = HI_NULL;
    hi_ctcoam_msg_head_s *pst_ctcoamhead = HI_NULL;
    hi_epon_llid_get_exoam_gloabal_data_ctrl st_getdatactrl;

    /* 参数检查,LLID的正确性由上层保证 */
    if (HI_NULL == pv_inmsg) {
        hi_ctcoam_dbg_err_print("Null Ptr.");
        return HI_RET_NULLPTR;
    }

    HI_OS_MEMSET_S(auc_newkey, sizeof(auc_newkey), 0, sizeof(auc_newkey));
    pst_rxchurningcode = (hi_ctcoam_churning_code_s *)pv_inmsg;
    ui_code = pst_rxchurningcode->uc_code;

    /* 检查输入消息 */
    if ((HI_CTC_CHURNING_REQUEST_E != ui_code)
        && (HI_CTC_CHURNING_AES_REQUEST_E != ui_code)
        && (HI_CTC_CHURNING_ACK_E != ui_code)) {
        hi_ctcoam_dbg_err_print("Invalid Code(%u).", ui_code);
        return HI_RET_INVALID_PARA;
    }

    /* 记录OLT目前使用的KeyIndex */
    uc_keyindex = *((hi_uchar8 *)pv_inmsg + sizeof(hi_ctcoam_churning_code_s));
    switch (uc_keyindex) {
        case HI_CTC_CHURNING_KEY0_E:
            uc_newkeyindex = HI_CTC_CHURNING_KEY1_E;
            break;

        case HI_CTC_CHURNING_KEY1_E:
            uc_newkeyindex = HI_CTC_CHURNING_KEY0_E;
            break;

        default:
            return HI_RET_INVALID_PARA;
    }

    HI_OS_MEMSET_S(&st_getdatactrl, sizeof(st_getdatactrl), 0, sizeof(st_getdatactrl));
    st_getdatactrl.ui_llidindex = ui_llidindex;
    hi_ctcoam_get_global_data(&st_getdatactrl);

    /* 如果是ACK消息，则配置上行加密表，HI5681只有10G才支持上行加密 */
    if (HI_CTC_CHURNING_ACK_E == ui_code) {
        hi_uint32 ui_key;
        hi_uchar8 auc_encipherkey[16];

        ui_key = st_getdatactrl.st_data.ui_lastkeyindex;

        ui_key = (0 == ui_key) ? 1 : 0;

        /* 先不判断10G模式，当前只有10G支持 */
        //if ( hi_kernel_adapt_is_xepon() )
        {
            /* 配置上行加密表，直接从下行解密表中复制过来 */
            /* get  */
            ui_ret = hi_oam_get_churning_key(ui_llidindex, ui_key, auc_encipherkey);

            hi_oam_set_llid_encipher_key(ui_llidindex, ui_key, auc_encipherkey);
        }

        /* 直接返回OK */
        return HI_RET_SUCC;
    }

    /* 下面是处理CTC、AES密钥请求 */
    if (HI_CTC_CHURNING_REQUEST_E == ui_code) {
        ui_keylen = HI_EPON_CTC_XEPON_CHURNING_KEY_LEN;
        ui_respcode = HI_CTC_CHURNING_RESPONSE_E;

        /* 配置加密模式为三重搅动 */
        hi_oam_set_cipher_mode(ui_llidindex, HI_CTCOAM_DECIPHER_CHURN_E);

    } else if (HI_CTC_CHURNING_AES_REQUEST_E == ui_code) {
        ui_keylen = 16;/* 128bit */
        ui_respcode = HI_CTC_CHURNING_AES_RESPONSE_E;

        /* 配置加密模式为AES */
        hi_oam_set_cipher_mode(ui_llidindex, HI_CTCOAM_DECIPHER_AES_E);
    }

    /* 申请一个最大的动态内存 */
    puc_response = (hi_uchar8 *)hi_os_malloc(HI_EPON_OAM_MIN_LEN);
    if (HI_NULL == puc_response) {
        hi_ctcoam_dbg_err_print("Malloc Fail.");
        return HI_RET_MALLOC_FAIL;
    }

    HI_OS_MEMSET_S(puc_response, HI_EPON_OAM_MIN_LEN, 0, HI_EPON_OAM_MIN_LEN);

    /* 跳到填充位置,从OUI开始由本函数填充 */
    puc_responsetmp = puc_response;
    puc_responsetmp += sizeof(hi_epon_oam_s); /* 跳过以太头 */
    ui_txmsglen = sizeof(hi_epon_oam_s);
    pst_ctcoamhead  = (hi_ctcoam_msg_head_s *)((hi_void *)puc_responsetmp);

    /* 填充CTC OUI和Get回应标志 */
    ui_ret = hi_ctcoam_filloui(ui_exoam_type, pst_ctcoamhead->auc_oui);
    if (HI_RET_SUCC != ui_ret) {
        hi_ctcoam_dbg_err_print("Fill OUI Fail(%d).", ui_ret);
        hi_os_free(puc_response);
        puc_response = HI_NULL;
        return ui_ret;
    }

    pst_ctcoamhead->uc_opercode = HI_CTC_OPER_CHURNING_E;
    puc_responsetmp += sizeof(hi_ctcoam_msg_head_s);
    ui_txmsglen += sizeof(hi_ctcoam_msg_head_s);

    /* 填充返回的密钥消息 */
    ((hi_ctcoam_churning_code_s *)puc_responsetmp)->uc_code = ui_respcode;
    puc_responsetmp += sizeof(hi_ctcoam_churning_code_s);
    *puc_responsetmp = uc_newkeyindex;
    puc_responsetmp += sizeof(hi_uchar8);
    ui_txmsglen += 2 * sizeof(hi_uchar8);

    #if 0
    /* XEPON 组播16-31，EPON组播8-15 */
    if ((HI_NNI_UPMODE_10GEPON_1G_E == ui_xepon_flag)
        || (HI_NNI_UPMODE_10GEPON_SYM_E == ui_xepon_flag)
        || (HI_NNI_UPMODE_10GEPON_U2DOT5G_E == ui_xepon_flag)) {
        ui_mc_offset = 16;
    } else {
        ui_mc_offset = 8;
    }
    #endif

    /* 获取三层搅动的密钥 */
    if (HI_FALSE == st_getdatactrl.st_data.uc_churning_test) {
        /* 如果请求的密钥与上一次不相同,则产生随机密钥 */
        if (st_getdatactrl.st_data.ui_lastkeyindex != uc_keyindex) {
            st_getdatactrl.st_data.ui_lastkeyindex = uc_keyindex;

            /*计算新密钥*/
            ui_ret = hi_ctcoam_xepon_calc_churing_key(auc_newkey);
            if (HI_RET_SUCC != ui_ret) {
                hi_ctcoam_dbg_err_print("Calc Key Fail(%d).", ui_ret);
                hi_os_free(puc_response);
                puc_response = HI_NULL;
                return ui_ret;
            }

            /* 设置解搅动寄存器 */
            ui_ret = hi_oam_set_llid_key(ui_llidindex, uc_newkeyindex, auc_newkey);
            if (0 != ui_ret) {
                hi_ctcoam_dbg_err_print("set emac llid table Fail(%d).", ui_ret);
                hi_os_free(puc_response);
                puc_response = HI_NULL;
                return HI_RET_REG_WRITE_ERR;
            }

            /* 同时配置组播密钥 */
#if 0 // by zhangli
            ui_ret = hi_oam_set_llid_key(ui_llidindex + ui_mc_offset, uc_newkeyindex, auc_newkey);
            if (0 != ui_ret) {
                hi_ctcoam_dbg_err_print("set emac llid table Fail(%d).", ui_ret);
                hi_os_free(puc_response);
                puc_response = HI_NULL;
                return HI_RET_REG_WRITE_ERR;
            }
#endif
        } else {
            /* 如果请求的密钥还和上一次相同,则还返回上一次的密钥 */
            ui_ret = hi_oam_get_churning_key(ui_llidindex, uc_newkeyindex, auc_newkey);
            if (0 != ui_ret) {
                hi_ctcoam_dbg_err_print("get emac llid table Fail(%d).", ui_ret);
                hi_os_free(puc_response);
                puc_response = HI_NULL;
                return HI_RET_REG_READ_ERR;
            }

            /* 规避第一次上来就走到这里，再st一下 */
            ui_ret = hi_oam_set_llid_key(ui_llidindex, uc_newkeyindex, auc_newkey);
            if (0 != ui_ret) {
                hi_ctcoam_dbg_err_print("get emac llid table Fail(%d).", ui_ret);
                hi_os_free(puc_response);
                puc_response = HI_NULL;
                return HI_RET_REG_READ_ERR;
            }

            /* 同时配置组播密钥 */
#if 0 // by zhangli
            ui_ret = hi_oam_set_llid_key(ui_llidindex + ui_mc_offset, uc_newkeyindex, auc_newkey);
            if (0 != ui_ret) {
                hi_ctcoam_dbg_err_print("get emac llid table Fail(%d).", ui_ret);
                hi_os_free(puc_response);
                puc_response = HI_NULL;
                return HI_RET_REG_READ_ERR;
            }
#endif
        }
    } else {
        /* 三层搅动测试模式 */
        auc_newkey[0] = st_getdatactrl.st_data.auc_churning_key[0];
        auc_newkey[1] = st_getdatactrl.st_data.auc_churning_key[1];
        auc_newkey[2] = st_getdatactrl.st_data.auc_churning_key[2];
    }

    hi_ctcoam_set_global_data(&st_getdatactrl);

    /* 填充消息并发送 */
    //hi_os_memcpy(puc_responsetmp, auc_newkey, HI_EPON_CTC_XEPON_CHURNING_KEY_LEN);
    /* 三重搅动还得按三组密钥进行 */
    if (HI_CTC_CHURNING_REQUEST_E == ui_code) {
        /* 第一组密钥 */
        puc_responsetmp[0] = auc_newkey[2];
        puc_responsetmp[1] = auc_newkey[1];
        puc_responsetmp[2] = auc_newkey[0];
        /* 第二组密钥 */
        puc_responsetmp[3] = auc_newkey[5];
        puc_responsetmp[4] = auc_newkey[4];
        puc_responsetmp[5] = auc_newkey[3];
        /* 第三组密钥 */
        puc_responsetmp[6] = auc_newkey[8];
        puc_responsetmp[7] = auc_newkey[7];
        puc_responsetmp[8] = auc_newkey[6];
    } else if (HI_CTC_CHURNING_AES_REQUEST_E == ui_code) {
        hi_uint32 *pulTemp = (hi_uint32 *)auc_newkey;
        /* 先不反位序 */
        for (i = 0; i < 4; i++) {
            *pulTemp = ((*pulTemp & 0xff000000) >> 24)
                       | ((*pulTemp & 0x00ff0000) >> 8)
                       | ((*pulTemp & 0x0000ff00) << 8)
                       | ((*pulTemp & 0x000000ff) << 24);
            pulTemp++;
        }

        HI_OS_MEMCPY_S(puc_responsetmp, HI_EPON_OAM_MIN_LEN - (puc_responsetmp - puc_response), auc_newkey, 16);
    }

    //ui_txmsglen += HI_EPON_CTC_XEPON_CHURNING_KEY_LEN;
    ui_txmsglen += ui_keylen;

    /* 发送扩展OAM消息 */
    ui_ret = hi_oam_api_txmsgproc(ui_llidindex, (hi_void *)puc_response, ui_txmsglen,
                                  HI_OAM_CODE_ORGANIZATION_SPEC_E);
    if (HI_RET_SUCC != ui_ret) {
        hi_ctcoam_dbg_err_print("Send TxMsg Fail(%d).", ui_ret);
    }

    hi_os_free(puc_response);
    puc_response = HI_NULL;
    return ui_ret;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_churning
 Description  : CTCOAM三层搅动密钥交换
 Input        : hi_uint32 ui_llidindex
                hi_void *pv_inmsg
                hi_uint32 ui_inmsglen
                hi_uint32 ui_exoam_type
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_churning(hi_uint32 ui_llidindex, hi_void *pv_inmsg, hi_uint32 ui_inmsglen, hi_uint32 ui_exoam_type)
{
    hi_uint32 ui_ret;
    hi_uint32 ui_txmsglen = 0;
    hi_uchar8 uc_keyindex;
    hi_uchar8 uc_newkeyindex;
    hi_uchar8 auc_newkey[HI_EPON_CTC_CHURNING_KEY_LEN];
    hi_uchar8 *puc_response = HI_NULL;
    hi_uchar8 *puc_responsetmp = HI_NULL;
    hi_ctcoam_churning_code_s *pst_rxchurningcode = HI_NULL;
    hi_ctcoam_msg_head_s *pst_ctcoamhead = HI_NULL;
    hi_epon_llid_get_exoam_gloabal_data_ctrl st_getdatactrl;

    /* 参数检查,LLID的正确性由上层保证 */
    if (HI_NULL == pv_inmsg) {
        hi_ctcoam_dbg_err_print("Null Ptr.");
        return HI_RET_NULLPTR;
    }

    HI_OS_MEMSET_S(auc_newkey, HI_EPON_CTC_CHURNING_KEY_LEN, 0, HI_EPON_CTC_CHURNING_KEY_LEN);
    pst_rxchurningcode = (hi_ctcoam_churning_code_s *)pv_inmsg;

    /* 检查输入消息 */
    if (HI_CTC_CHURNING_REQUEST_E != pst_rxchurningcode->uc_code) {
        hi_ctcoam_dbg_err_print("Invalid Code(%u).", pst_rxchurningcode->uc_code);
        return HI_RET_INVALID_PARA;
    }

    /* 记录OLT目前使用的KeyIndex */
    uc_keyindex = *((hi_uchar8 *)pv_inmsg + sizeof(hi_ctcoam_churning_code_s));
    switch (uc_keyindex) {
        case HI_CTC_CHURNING_KEY0_E:
            uc_newkeyindex = HI_CTC_CHURNING_KEY1_E;
            break;

        case HI_CTC_CHURNING_KEY1_E:
            uc_newkeyindex = HI_CTC_CHURNING_KEY0_E;
            break;

        default:
            return HI_RET_INVALID_PARA;
    }

    /* 申请一个最大的动态内存 */
    puc_response = (hi_uchar8 *)hi_os_malloc(HI_EPON_OAM_MIN_LEN);
    if (HI_NULL == puc_response) {
        hi_ctcoam_dbg_err_print("Malloc Fail.");
        return HI_RET_MALLOC_FAIL;
    }

    HI_OS_MEMSET_S(puc_response, HI_EPON_OAM_MIN_LEN, 0, HI_EPON_OAM_MIN_LEN);

    /* 跳到填充位置,从OUI开始由本函数填充 */
    puc_responsetmp = puc_response;
    puc_responsetmp += sizeof(hi_epon_oam_s); /* 跳过以太头 */
    ui_txmsglen = sizeof(hi_epon_oam_s);
    pst_ctcoamhead  = (hi_ctcoam_msg_head_s *)((hi_void *)puc_responsetmp);

    /* 填充CTC OUI和Get回应标志 */
    ui_ret = hi_ctcoam_filloui(ui_exoam_type, pst_ctcoamhead->auc_oui);
    if (HI_RET_SUCC != ui_ret) {
        hi_ctcoam_dbg_err_print("Fill OUI Fail(%d).", ui_ret);
        hi_os_free(puc_response);
        puc_response = HI_NULL;
        return ui_ret;
    }

    pst_ctcoamhead->uc_opercode = HI_CTC_OPER_CHURNING_E;
    puc_responsetmp += sizeof(hi_ctcoam_msg_head_s);
    ui_txmsglen += sizeof(hi_ctcoam_msg_head_s);

    /* 填充返回的密钥消息 */
    ((hi_ctcoam_churning_code_s *)puc_responsetmp)->uc_code = HI_CTC_CHURNING_RESPONSE_E;
    puc_responsetmp += sizeof(hi_ctcoam_churning_code_s);
    *puc_responsetmp = uc_newkeyindex;
    puc_responsetmp += sizeof(hi_uchar8);
    ui_txmsglen += 2 * sizeof(hi_uchar8);

    HI_OS_MEMSET_S(&st_getdatactrl, sizeof(st_getdatactrl), 0, sizeof(st_getdatactrl));
    st_getdatactrl.ui_llidindex = ui_llidindex;
    hi_ctcoam_get_global_data(&st_getdatactrl);

    /* 获取三层搅动的密钥 */
    if (HI_FALSE == st_getdatactrl.st_data.uc_churning_test) {
        /* 如果请求的密钥与上一次不相同,则产生随机密钥 */
        if (st_getdatactrl.st_data.ui_lastkeyindex != uc_keyindex) {
            st_getdatactrl.st_data.ui_lastkeyindex = uc_keyindex;

            /*计算新密钥*/
            ui_ret = hi_ctcoam_calc_churing_key(auc_newkey);
            if (HI_RET_SUCC != ui_ret) {
                hi_ctcoam_dbg_err_print("Calc Key Fail(%d).", ui_ret);
                hi_os_free(puc_response);
                puc_response = HI_NULL;
                return ui_ret;
            }

            /* 设置解搅动寄存器 */
            ui_ret = hi_oam_set_churning_key(ui_llidindex, uc_newkeyindex, auc_newkey);
            if (0 != ui_ret) {
                hi_ctcoam_dbg_err_print("set emac llid table Fail(%d).", ui_ret);
                hi_os_free(puc_response);
                puc_response = HI_NULL;
                return HI_RET_REG_WRITE_ERR;
            }
        } else {
            /* 如果请求的密钥还和上一次相同,则还返回上一次的密钥 */
            ui_ret = hi_oam_get_churning_key(ui_llidindex, uc_newkeyindex, auc_newkey);
            if (0 != ui_ret) {
                hi_ctcoam_dbg_err_print("get emac llid table Fail(%d).", ui_ret);
                hi_os_free(puc_response);
                puc_response = HI_NULL;
                return HI_RET_REG_READ_ERR;
            }
        }
    } else {
        /* 三层搅动测试模式 */
        auc_newkey[0] = st_getdatactrl.st_data.auc_churning_key[0];
        auc_newkey[1] = st_getdatactrl.st_data.auc_churning_key[1];
        auc_newkey[2] = st_getdatactrl.st_data.auc_churning_key[2];
    }

    hi_ctcoam_set_global_data(&st_getdatactrl);

    /* 填充消息并发送 */
    HI_OS_MEMCPY_S(puc_responsetmp, HI_EPON_OAM_MIN_LEN - (puc_responsetmp - puc_response), auc_newkey, HI_EPON_CTC_CHURNING_KEY_LEN);
    ui_txmsglen += HI_EPON_CTC_CHURNING_KEY_LEN;

    /* 发送扩展OAM消息 */
    ui_ret = hi_oam_api_txmsgproc(ui_llidindex, (hi_void *)puc_response, ui_txmsglen,
                                  HI_OAM_CODE_ORGANIZATION_SPEC_E);
    if (HI_RET_SUCC != ui_ret) {
        hi_ctcoam_dbg_err_print("Send TxMsg Fail(%d).", ui_ret);
    }

    hi_os_free(puc_response);
    puc_response = HI_NULL;
    return ui_ret;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_dbacfg
 Description  : DBA相关消息
 Input        : hi_uint32 ui_llidindex
             hi_void *pv_inmsg     剥除了OUI和Ext_oper
             hi_uint32 ui_inmsglen 剥除后的消息长度
             hi_uint32 ui_exoam_type 使用EPONDRV_EXOAM_TYPE_ENUM解析
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_dbacfg(hi_uint32 ui_llidindex, hi_void *pv_inmsg, hi_uint32 ui_inmsglen, hi_uint32 ui_exoam_type)
{
    hi_uint32 ui_ret;
    hi_uchar8 *puc_dbaopercode = HI_NULL;

    FUNC_LOG;

    /* 参数检查,LLID的正确性由上层保证 */
    if (HI_NULL == pv_inmsg) {
        hi_ctcoam_dbg_err_print("Null Ptr.");
        return HI_RET_NULLPTR;
    }

    puc_dbaopercode = (hi_uchar8 *)pv_inmsg;

    /* 根据操作码分发 */
    switch (*puc_dbaopercode) {
        case HI_DBA_OPERCODE_GET_DBA_REQUEST_E:
            ui_ret = hi_ctcoam_get_dba(ui_llidindex, (hi_void *)(puc_dbaopercode + 1),
                                       (ui_inmsglen - 1), ui_exoam_type);
            if (HI_RET_SUCC != ui_ret) {
                return ui_ret;
            }

            break;

        case HI_DBA_OPERCODE_SET_DBA_REQUEST_E:
            ui_ret = hi_ctcoam_set_dba(ui_llidindex, (hi_void *)(puc_dbaopercode + 1),
                                       (ui_inmsglen - 1), ui_exoam_type);
            if (HI_RET_SUCC != ui_ret) {
                return ui_ret;
            }

            break;

        default:
            return HI_RET_INVALID_PARA;
    }

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_event_cfg
 Description  : 告警相关消息
 Input        : hi_uint32 ui_llidindex
             hi_void *pv_inmsg     剥除了OUI和Ext_oper
             hi_uint32 ui_inmsglen 剥除后的消息长度
             hi_uint32 ui_exoam_type 使用EPONDRV_EXOAM_TYPE_ENUM解析
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_event_cfg(hi_uint32 ui_llidindex, hi_void *pv_inmsg, hi_uint32 ui_inmsglen, hi_uint32 ui_exoam_type)
{
    hi_uint32 ui_ret;
    hi_uchar8 *puc_eventopercode = HI_NULL;
    hi_uchar8 *puc_response;
    hi_uchar8 *puc_responsetmp;
    hi_ctcoam_event_head_s *pst_ctcoamhead;
    hi_uint32  ui_outmsglen;

    /* 参数检查,LLID的正确性由上层保证 */
    if (HI_NULL == pv_inmsg) {
        hi_ctcoam_dbg_err_print("Null Ptr.");
        return HI_RET_NULLPTR;
    }

    /* 检查合法性 */
    if (ui_llidindex >= HI_MAX_LLID_NUM) {
        hi_ctcoam_dbg_err_print("Invalid LLID(%d).", ui_llidindex);
        return HI_RET_INVALID_PARA;
    }

    g_i_ctcoam_llid = ui_llidindex;

    /* 申请一个最大的动态内存 */
    puc_response = (hi_uchar8 *)hi_os_malloc(HI_EPON_MAX_OAM_LEN);
    if (HI_NULL == puc_response) {
        hi_ctcoam_dbg_err_print("Malloc fail.");
        return HI_RET_MALLOC_FAIL;
    }
    HI_OS_MEMSET_S(puc_response, HI_EPON_MAX_OAM_LEN, 0, HI_EPON_MAX_OAM_LEN);

    /* 跳到填充位置,从OUI开始由本函数填充 */
    puc_responsetmp  = puc_response + sizeof(hi_epon_oam_s);
    pst_ctcoamhead   = (hi_ctcoam_event_head_s *)((hi_void *)puc_responsetmp);

    /* 填充CTC OUI和Get回应标志 */
    ui_ret = hi_ctcoam_filloui(ui_exoam_type, pst_ctcoamhead->auc_oui);
    if (HI_RET_SUCC != ui_ret) {
        hi_ctcoam_dbg_err_print("Fill OUI Fail(%d).", ui_ret);
        hi_os_free(puc_response);
        puc_response = HI_NULL;
        return ui_ret;
    }

    puc_eventopercode = (hi_uchar8 *)pv_inmsg;

    pst_ctcoamhead->uc_opercode = HI_CTC_OPER_ALARM_E;
    if (*puc_eventopercode <= HI_EVENT_STATUS_SET_E) {
        pst_ctcoamhead->uc_subtype = HI_EVENT_STATUS_RESPONSE_E;
    } else {
        pst_ctcoamhead->uc_subtype = HI_EVENT_THRESHOLD_RESPONSE_E;
    }

    /* 根据操作码分发 */
    switch (*puc_eventopercode) {
        case HI_EVENT_STATUS_REQUEST_E:
            ui_ret = hi_ctcoam_get_event_status(ui_llidindex, (hi_void *)(puc_eventopercode + 1),
                                                (ui_inmsglen - 1), (hi_void *)(pst_ctcoamhead + 1),
                                                &ui_outmsglen, ui_exoam_type);
            if (HI_RET_SUCC != ui_ret) {
                return ui_ret;
            }

            break;

        case HI_EVENT_STATUS_SET_E:
            ui_ret = hi_ctcoam_set_event_status(ui_llidindex, (hi_void *)(puc_eventopercode + 1),
                                                (ui_inmsglen - 1), (hi_void *)(pst_ctcoamhead + 1),
                                                &ui_outmsglen, ui_exoam_type);
            if (HI_RET_SUCC != ui_ret) {
                return ui_ret;
            }

            break;

        case HI_EVENT_THRESHOLD_REQUEST_E:
            ui_ret = hi_ctcoam_get_event_threshold(ui_llidindex, (hi_void *)(puc_eventopercode + 1),
                                                   (ui_inmsglen - 1), (hi_void *)(pst_ctcoamhead + 1),
                                                   &ui_outmsglen, ui_exoam_type);
            if (HI_RET_SUCC != ui_ret) {
                return ui_ret;
            }

            break;

        case HI_EVENT_THRESHOLD_SET_E:
            ui_ret = hi_ctcoam_set_event_threshold(ui_llidindex, (hi_void *)(puc_eventopercode + 1),
                                                   (ui_inmsglen - 1), (hi_void *)(pst_ctcoamhead + 1),
                                                   &ui_outmsglen, ui_exoam_type);
            if (HI_RET_SUCC != ui_ret) {
                return ui_ret;
            }

            break;

        default:
            return HI_RET_INVALID_PARA;

    }

    ui_outmsglen += sizeof(hi_epon_oam_s) + sizeof(hi_ctcoam_event_head_s);

    /* 发送扩展OAM消息 */
    ui_ret = hi_oam_api_txmsgproc(ui_llidindex, (hi_void *)puc_response,
                                  ui_outmsglen, HI_OAM_CODE_ORGANIZATION_SPEC_E);
    if (HI_RET_SUCC != ui_ret) {
        hi_ctcoam_dbg_err_print("Tx ExtOam_TLV Fail(%d).", ui_ret);
        return ui_ret;
    }

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_deliver
 Description  : CTC OAM消息分发
 Input        : hi_uint32 ui_llidindex
                hi_uchar8 uc_oper
                hi_void *pv_msg
                hi_uint32 ui_msglen
                hi_uint32 ui_exoam_type
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_deliver(hi_uint32 ui_llidindex, hi_uchar8 uc_oper,
                            hi_void *pv_msg, hi_uint32 ui_msglen, hi_uint32 ui_exoam_type)
{
    hi_uint32 ui_ret;
    hi_sml_epon_mode_e upmode;

    /* 参数检查 */
    if (HI_NULL == pv_msg) {
        hi_ctcoam_dbg_err_print("Null Ptr.");
        return HI_RET_NULLPTR;
    }

    /* 根据CTC扩展操作码分别处理 */
    switch (uc_oper) {
        case HI_CTC_OPER_GET_E: /* Get */
            ui_ret = hi_ctcoam_get_deliver(ui_llidindex, pv_msg, ui_msglen, ui_exoam_type);
            if (HI_RET_SUCC != ui_ret) {
                hi_ctcoam_dbg_err_print("Get Operation Fail(%d).", ui_ret);
                return ui_ret;
            }

            break;

        case HI_CTC_OPER_SET_E: /* Set */
            ui_ret = hi_ctcoam_set_deliver(ui_llidindex, pv_msg, ui_msglen, ui_exoam_type);
            if (HI_RET_SUCC != ui_ret) {
                hi_ctcoam_dbg_err_print("Set Operation Fail(%d).", ui_ret);
                return ui_ret;
            }
            break;

        case HI_CTC_OPER_ONU_AUTH_E: /*loid 认证*/
            ui_ret = hi_ctcoam_onu_auth(ui_llidindex, pv_msg, ui_msglen, ui_exoam_type);
            if (HI_RET_SUCC != ui_ret) {
                hi_ctcoam_dbg_err_print(" loid authentication Fail(%d).", ui_ret);
                return ui_ret;
            }

            break;

        case HI_CTC_OPER_SOFTWARE_UPGRADE_E:
            //     hi_os_printf("\r\n %s %d\n", __func__, __LINE__);
            ui_ret = hi_ctcoam_software_upgrade(ui_llidindex, pv_msg, ui_msglen, ui_exoam_type);

            break;

        case HI_CTC_OPER_CHURNING_E: /* 密钥交换 */
            ui_ret = HI_IPC_CALL("hi_sml_epon_mode_get", &upmode);
            if (HI_RET_SUCC != ui_ret) {
                hi_ctcoam_dbg_err_print("Churning Operation Fail(%d).", ui_ret);
                return ui_ret;
            }
            if ((HI_SML_10GEPON_1G == upmode)
                || (HI_SML_10GEPON_U2DOT5G == upmode)
                || (HI_SML_10GEPON_SYM == upmode)) {
                ui_ret = hi_ctcoam_xepon_churning(ui_llidindex, pv_msg, ui_msglen, ui_exoam_type);
                if (HI_RET_SUCC != ui_ret) {
                    hi_ctcoam_dbg_err_print("Churning Operation Fail(%d).", ui_ret);
                    return ui_ret;
                }
            } else {
                ui_ret = hi_ctcoam_churning(ui_llidindex, pv_msg, ui_msglen, ui_exoam_type);
                if (HI_RET_SUCC != ui_ret) {
                    hi_ctcoam_dbg_err_print("Churning Operation Fail(%d).", ui_ret);
                    return ui_ret;
                }
            }

            break;

        case HI_CTC_OPER_DBA_E:      /* DBA相关参数 */
            ui_ret = hi_ctcoam_dbacfg(ui_llidindex, pv_msg, ui_msglen, ui_exoam_type);
            if (HI_RET_SUCC != ui_ret) {
                hi_ctcoam_dbg_err_print("Dba Operation Fail(%d).", ui_ret);
                return ui_ret;
            }

            break;

        case HI_CTC_OPER_ALARM_E: /* 告警状态及门限的配置与查询 */
            ui_ret = hi_ctcoam_event_cfg(ui_llidindex, pv_msg, ui_msglen, ui_exoam_type);
            if (HI_RET_SUCC != ui_ret) {
                hi_ctcoam_dbg_err_print("Event Operation Fail(%d).", ui_ret);
                return ui_ret;
            }

            break;

        default:
            return HI_RET_FAIL;
    }

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_epon_status_change_proc
 Description  : CTC OAM状态变化，通知节能模块
 Input        : hi_uint32 ui_llidindex
                hi_uchar8 uc_oper
                hi_void *pv_msg
                hi_uint32 ui_msglen
                hi_uint32 ui_exoam_type
 Output       : None
 Return Value :
*****************************************************************************/
hi_void hi_ctcoam_epon_status_change_proc(hi_uchar8 uc_status)
{
    static hi_uint32 ui_epon_online_flg = HI_FALSE;

    if (HI_MPCP_STATE_REGISTERED_E == uc_status) {
        if (ui_epon_online_flg) {
            ui_epon_online_flg = HI_FALSE;
            //hi_pmu_epon_status_proc(ui_epon_online_flg);
        }
    } else if (HI_MPCP_STATE_HANGUP_E == uc_status) {
        if (!ui_epon_online_flg) {
            ui_epon_online_flg = HI_TRUE;
            //hi_pmu_epon_status_proc(ui_epon_online_flg);
        }
    }

    return;
}

hi_int32 __ctcoam_disinfo_proc(hi_void *pv_data)
{
    hi_int32 i_ret;
    hi_oam_disinfo_reports_s *pst_disinfo = (hi_oam_disinfo_reports_s *)pv_data;
    hi_int32 i_fd;
    hi_uint32 ui_disinfo = 0;
    hi_uint32 ui_xgpon_upcap;
    hi_char8 ac_cmd[128] = {0};

    if (0 != pst_disinfo->uc_llid) {
        return HI_RET_SUCC;
    }

    ui_xgpon_upcap = pst_disinfo->us_disinfo >> 4;

    /* 配置XPON配置文件中的10G EPON上行能力 */
    i_fd = hi_os_open(HI_XGEPON_UPCAP_FILEPATH, HI_O_CREAT | HI_O_RDWR, 0666);
    if (-1 == i_fd) {
        return HI_RET_FILE_OPEN_FAIL;
    }

    if (hi_os_read(i_fd, ac_cmd, 1)) {
        hi_os_atoi(ac_cmd, (hi_int32 *)&ui_disinfo);
        if (ui_xgpon_upcap == ui_disinfo) {
            hi_os_close(i_fd);
            return HI_RET_SUCC;
        }
    }

    HI_OS_SPRINTF_S(ac_cmd, sizeof(ac_cmd), "cat /dev/null > %s", HI_XGEPON_UPCAP_FILEPATH);
    hi_os_system(ac_cmd);

    HI_OS_SPRINTF_S(ac_cmd, sizeof(ac_cmd), "%d", ui_xgpon_upcap);
    i_ret = hi_os_write(i_fd, ac_cmd, 1);
    hi_os_close(i_fd);
    if (i_ret < 0) {
        return HI_RET_FILE_WRITE_FAIL;
    }

    return HI_RET_SUCC;
}

static int32_t oam_proc_oamreg_state(hi_oam_report_s st_msg)
{
	int32_t ret;
	hi_int32 ui_state;
	hi_oam_status_reports_s st_rpt;
    hi_oam_status_reports_s *pst_rpt = &st_rpt;
	hi_oam_notifier_data_s st_notifier_data;

	if (memcpy_s(&st_rpt, sizeof(st_rpt), &st_msg.uc_content[0], sizeof(st_rpt)) != EOK)
		return HI_RET_FAIL;
	hi_os_printf("\r\n ctcoam llid(%d) reg state :  %d \n", pst_rpt->uc_llid, pst_rpt->uc_state);

	if (pst_rpt->uc_state == HI_CTCOAM_SEND_ANY_E)
		hi_ctcoam_alarm_init();

	st_notifier_data.em_type = HI_OAM_NOTIFIY_OAMREG_STATE_E;
	st_notifier_data.ui_llid = pst_rpt->uc_llid;
	st_notifier_data.ui_state = pst_rpt->uc_state;
#ifdef CONFIG_PLATFORM_OPENWRT
	HI_IPC_CALL("hi_sml_owal_led_epon_proc", &st_notifier_data);
#else
	ret = hi_notifier_call(HI_OAM_NOTIFIY_NAME, &st_notifier_data);
	if (ret != HI_RET_SUCC)
		return ret;
#endif
	if (pst_rpt->uc_state == HI_OAM_NOTIFY_REG_SUCC_E) {
		ui_state = HI_FALSE;
		ret = HI_IPC_CALL("hi_pon_set_fail_regstate", &ui_state);
		if (ret != HI_RET_SUCC)
			return ret;
	}
	return HI_RET_SUCC;
}

static int32_t oam_proc_mpcpreg_state(hi_oam_report_s st_msg)
{
	int32_t ret;
    hi_oam_status_reports_s st_rpt;
    hi_oam_status_reports_s *pst_rpt = &st_rpt;
	hi_oam_notifier_data_s st_notifier_data;

	if (memcpy_s(&st_rpt, sizeof(st_rpt), &st_msg.uc_content[0], sizeof(st_rpt)) != EOK)
		return HI_RET_FAIL;
	hi_os_printf("\r\n MPCP llid(%d) reg state :  %d \n", pst_rpt->uc_llid, pst_rpt->uc_state);

	if ((pst_rpt->uc_state == HI_MPCP_STATE_REGISTER_REQ_E) || (pst_rpt->uc_state == HI_MPCP_STATE_INITIAL_E)) {
		hi_os_printf("init oam llid(%d)\n", pst_rpt->uc_llid);
		(hi_void)hi_ctcoam_init(pst_rpt->uc_llid);
		g_uc_reg_flag = HI_FALSE;
		ret = hi_oam_init_sampleoam(pst_rpt->uc_llid);
		if (ret != HI_RET_SUCC) {
			hi_os_printf("\r\n llid(%d)hi_oam_init_sampleoam fail,retcode %0x", pst_rpt->uc_llid, ret);
			return ret;
		}
	}

	if (pst_rpt->uc_state != HI_MPCP_STATE_REGISTERED_E)
		hi_ctcoam_alarm_exit();

	if (pst_rpt->uc_llid == HI_LLID_INDEX_0)
		hi_ctcoam_epon_status_change_proc(pst_rpt->uc_state);

	st_notifier_data.em_type = HI_OAM_NOTIFIY_MPCPREG_STATE_E;
	st_notifier_data.ui_llid = pst_rpt->uc_llid;
	st_notifier_data.ui_state = pst_rpt->uc_state;
#ifdef CONFIG_PLATFORM_OPENWRT
	HI_IPC_CALL("hi_sml_owal_led_epon_proc", &st_notifier_data);
#else
	ret = hi_notifier_call(HI_OAM_NOTIFIY_NAME, &st_notifier_data);
	if (HI_RET_SUCC != ret)
		return ret;
#endif
	return HI_RET_SUCC;
}

hi_int32 hi_oam_proc(hi_int32 i_fd)
{
    hi_int32 i_ret;
    hi_oam_report_s st_msg;
    hi_ctcoam_msg_s st_ctcoam ;
    hi_ctcoam_msg_s *pst_ctcoam = &st_ctcoam;
    hi_oam_alarm_report_s st_alm;
    hi_oam_alarm_report_s *pst_alm = &st_alm;
    hi_uchar8 *puc_msg = HI_NULL;
    hi_int32 i_recv;

	(void)memset_s(&st_msg, sizeof(hi_oam_report_s), 0, sizeof(hi_oam_report_s));

	i_recv = hi_os_netlink_recv(i_fd, &st_msg, sizeof(hi_oam_report_s), 0);
	if (0 > i_recv) {
		return HI_RET_SUCC;
	}

	switch (st_msg.ui_msgtype) {
		case HI_OAM_ORGANIZE_SPECIFIC_E :
			if (memcpy_s(&st_ctcoam, sizeof(st_ctcoam), &st_msg.uc_content[0], sizeof(hi_ctcoam_msg_s)) != EOK)
				return HI_RET_FAIL;
			puc_msg = st_msg.uc_content + sizeof(hi_ctcoam_msg_s) - sizeof(hi_ctcoam_tlv_s);
			hi_ctcoam_deliver(pst_ctcoam->ui_llidindex, pst_ctcoam->uc_oper, (hi_void *)(puc_msg), pst_ctcoam->ui_msglen, pst_ctcoam->ui_exoam_type);
			break;

		case HI_OAM_OAMREG_STATE_E :
			i_ret = oam_proc_oamreg_state(st_msg);
			if (i_ret != HI_RET_SUCC)
				return i_ret;
			break;

		case HI_OAM_MPCPREG_STATE_E:
			i_ret = oam_proc_mpcpreg_state(st_msg);
			if (i_ret != HI_RET_SUCC)
				return i_ret;
			break;

		case HI_OAM_MPCPREG_DISINFO_E:

			/* 配置XPON配置文件中的10G EPON上行能力 */
			i_ret = __ctcoam_disinfo_proc(st_msg.uc_content);
			if (i_ret != HI_RET_SUCC) {
				return i_ret;
			}

			break;

		case HI_OAM_PMU_ALARM_E:
			if (memcpy_s(&st_alm, sizeof(st_alm), &st_msg.uc_content[0], sizeof(hi_oam_alarm_report_s)) != EOK)
				return HI_RET_FAIL;
			hi_ctcoam_onu_sleepstatus_update_alarm(pst_alm->uc_alarm_state, pst_alm->ui_alarminfo);
			break;

		default:
			break;
	}
    return HI_RET_SUCC;
}


hi_void hi_oam_print_time(hi_uchar8 *puc_func, hi_uint32 ui_line, hi_uint32 ui_msgtype)
{
    hi_uint32 ui_ret;
    hi_os_timeval_s stSysTime;

    ui_ret = hi_log_print_on((hi_uint32)HI_SRCMODULE_CMS_OAM_U, HI_LOG_LEVEL_INFO_E);

    if (HI_TRUE != ui_ret) {
        return;
    }


    hi_os_gettimeofday(&stSysTime);

    printf("\n\r%u.%06u %s %d msgtype:%d", (hi_uint32)stSysTime.tv_sec, (hi_uint32)stSysTime.tv_usec, puc_func, ui_line, ui_msgtype);
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
