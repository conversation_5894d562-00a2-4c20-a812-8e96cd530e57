#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "vs_ctcoam_restore_factory.h"


uint32_t vs_ctcoam_set_restore_factory(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    //int ret = 0;
    vs_ctcoam_restore_factory_s *cfg = NULL;

    if (pv_inmsg == NULL)
        return HI_RET_INVALID_PARA;

    cfg = (vs_ctcoam_restore_factory_s *)pv_inmsg;
    
    if(cfg->reset == 1)
    {
        printf("[oam] ready to restore factory\n");
        system("firstboot -y && sync && reboot");//openwrt restore factory cmd
        // ret = igdCmOamCtrl(IGD_CM_CMD_COMPLETELY_RESTORE);
        // if(ret != HI_RET_SUCC)
        // {
        //     printf("igdCmOamCtrl IGD_CM_CMD_COMPLETELY_RESTORE fail\n");
        //     return HI_RET_FAIL;
        // }
    }

    return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

