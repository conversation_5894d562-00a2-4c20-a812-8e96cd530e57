#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus

#include "vs_ctcoam_lan_dhcpv6_server.h"

void printf_oam_dhcpv6_data(vs_ctcoam_lan_dhcpv6_server_s *data)
{
    unsigned char i = 0, printf_enable = 0;
    if(printf_enable)
    {
        printf("===================== dhcpv6 data =====================\n");
        printf("itemNum = %d\n", data->itemNum);
        printf("ipType = %d\n", data->ipType);
        printf("lanIPv6addr = ");
        for(i = 0; i < 16; i ++)
        {
            printf("0x%x,", data->lanIPv6addr[i]);
        }printf("\n");
        printf("lanPrefixlength = %d\n", data->lanPrefixlength);
        printf("lanIPv6PrefixMode = %d\n", data->lanIPv6PrefixMode);
        printf("lanIPv6Prefix = ");
        for(i = 0; i < 16; i ++)
        {
            printf("0x%x,", data->lanIPv6Prefix[i]);
        }printf("\n");
        printf("PreferenceLifeTime = %d\n", data->PreferenceLifeTime);
        printf("enableDhcpServer = %d\n", data->enableDhcpServer);
        printf("dhcpPoolStart = ");
        for(i = 0; i < 16; i ++)
        {
            printf("0x%x,", data->dhcpPoolStart[i]);
        }printf("\n");
        printf("dhcpPoolEnd = ");
        for(i = 0; i < 16; i ++)
        {
            printf("0x%x,", data->dhcpPoolEnd[i]);
        }printf("\n");
        printf("dhcpPoolType = %d\n", data->dhcpPoolType);
        printf("dhcpv6DnsMode = %d\n", data->dhcpv6DnsMode);
        printf("dhcpPriDnsIpv6 = ");
        for(i = 0; i < 16; i ++)
        {
            printf("0x%x,", data->dhcpPriDnsIpv6[i]);
        }printf("\n");
        printf("dhcpSecDnsIpv6 = ");
        for(i = 0; i < 16; i ++)
        {
            printf("0x%x,", data->dhcpSecDnsIpv6[i]);
        }printf("\n");
        printf("dhcpGatewayIpv6 = ");
        for(i = 0; i < 16; i ++)
        {
            printf("0x%x,", data->dhcpGatewayIpv6[i]);
        }printf("\n");
        printf("dhcpServerRelayIpv6Addr = ");
        for(i = 0; i < 16; i ++)
        {
            printf("0x%x,", data->dhcpServerRelayIpv6Addr[i]);
        }printf("\n");
        printf("RaEnable = %d\n", data->RaEnable);
        printf("RaManageConfig = %d\n", data->RaManageConfig);
        printf("RaOtherConfig = %d\n", data->RaOtherConfig);
        printf("RaMaxInterval = %d\n", data->RaMaxInterval);
        printf("RaMinInterval = %d\n", data->RaMinInterval);
        printf("ValidLifeTime = %d\n", data->ValidLifeTime);
        printf("==========================================\n");	
    }
}

int vs_oam_lan_dhcpv6_cfg_to_ram(vs_ctcoam_lan_dhcpv6_server_s *cfg)
{
    int ret = 0;
    hi_char8 aucPrifix[64] = {0};
    hi_char8 ac_ipv6[CM_IPV6_ADDR_LEN_MAX] = {0};
    hi_char8 pri_dns[CM_IPV6_ADDR_LEN_MAX] = {0};
    hi_char8 sec_dns[CM_IPV6_ADDR_LEN_MAX] = {0};
    IgdLanIPv6AttrConfTab data;

    if (cfg == NULL)
        return HI_RET_INVALID_PARA;
    
    HI_OS_MEMSET_S(cfg, sizeof(vs_ctcoam_lan_dhcpv6_server_s), 0, sizeof(vs_ctcoam_lan_dhcpv6_server_s));
    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));

    data.ulBitmap |= LAN_IPV6_ATTR_MASK_ALL;
    data.ulBitmap1 |= LAN_IPV6_ATTR_MASK1_ALL;
    //ret = igdCmConfGet(IGD_LAN_IPV6_ATTR_TAB,(unsigned char *)&data,sizeof(data));
    ret = HI_IPC_CALL("hi_sml_lan_ipv6_attr_get", &data);
    if(ret != 0)
    {
        printf("hi_sml_lan_ipv6_attr_get fail, ret : %d \r\n", ret);
        return HI_RET_FAIL;
    }

    cfg->itemNum = 1;
    cfg->ipType = 1;//ipv6

    HI_OS_MEMCPY_S((void *)cfg->lanIPv6addr, CM_IPV6_ADDR_LEN, (void *)data.aucIPv6Address, CM_IPV6_ADDR_LEN);

    if (data.ucPrifixSource == LAN_PREFIX_TYPE_WAN_DELEGATED)
    {
        cfg->lanIPv6PrefixMode = 0;
    }
    else if (data.ucPrifixSource == LAN_PREFIX_TYPE_WAN_STATIC)
    {
        cfg->lanIPv6PrefixMode = 1;
        sscanf(data.aucPrifix, "%47[^/]/%d", aucPrifix, &cfg->lanPrefixlength);
        cfg->lanPrefixlength = data.ucPrefixLength;//for openwrt
        //printf("data.aucPrifix=%s, aucPrifix=%s, cfg->lanPrefixlength=%d\n", data.aucPrifix, aucPrifix, cfg->lanPrefixlength);
        igdCmApiChartoIpv6((void *)cfg->lanIPv6Prefix, aucPrifix);
    }


    if (data.ucDnsSource == LAN_DNS_SOURCE_HGU_PROXY)
    {
        cfg->dhcpv6DnsMode = 2;
    }
    else if (data.ucDnsSource == LAN_DNS_SOURCE_WAN_CONNECTION)
    {
        cfg->dhcpv6DnsMode = 0;
    }
    else if (data.ucDnsSource == LAN_DNS_SOURCE_STATIC)
    {
        cfg->dhcpv6DnsMode = 1;

        sscanf(data.aucDnsServers, "%48[^,],%48s", pri_dns, sec_dns);
        //printf("data.aucDnsServers=%s, pri_dns=%s, sec_dns=%s\n", data.aucDnsServers, pri_dns, sec_dns);
        igdCmApiChartoIpv6((void *)cfg->dhcpPriDnsIpv6, pri_dns);
        igdCmApiChartoIpv6((void *)cfg->dhcpSecDnsIpv6, sec_dns);
    }

    if (data.ucDhcpV6Enable == 1)//dhcpv6 server enable
    {
        cfg->enableDhcpServer = 1;
        cfg->PreferenceLifeTime = data.ulPreferredLifeTime;
        cfg->ValidLifeTime = data.ulValidLifeTime;
        
        snprintf(ac_ipv6, sizeof(ac_ipv6), "::%.20s", data.aucMinAddr);
        igdCmApiChartoIpv6((void *)cfg->dhcpPoolStart, ac_ipv6);

        snprintf(ac_ipv6, sizeof(ac_ipv6), "::%.20s", data.aucMaxAddr);
        igdCmApiChartoIpv6((void *)cfg->dhcpPoolEnd, ac_ipv6);

        cfg->dhcpPoolType = 0;//for pc

        HI_OS_MEMCPY_S((void *)cfg->dhcpGatewayIpv6 , CM_IPV6_ADDR_LEN, (void *)data.aucIPv6Address, CM_IPV6_ADDR_LEN);
    }
    else if(data.ucDhcpV6Enable == 0)//dhcpv6 server disable
    {
        if (data.ucDhcpRelay)//dhcpv6 relay
        {
            cfg->enableDhcpServer = 2;
            igdCmApiChartoIpv6((void *)cfg->dhcpServerRelayIpv6Addr, data.aucDhcpRelayAddr);
        }
        else
        {
            cfg->enableDhcpServer = 0;
        }
    }

    if (data.ucRadvdEnable)
    {
        cfg->RaEnable = 1;
        cfg->RaManageConfig = data.ucAddrInfoFromDhcp;
        cfg->RaOtherConfig = data.ucOtherFromDhcp;
        cfg->RaMaxInterval = data.ulMaxRaTime;
        cfg->RaMinInterval = data.ulMinRaTime;
    }
    else
    {
        cfg->RaEnable = 0;
    }

    return HI_RET_SUCC;
}

uint32_t vs_ctcoam_get_lan_dhcpv6_server(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    unsigned int struct_size = 0;
    unsigned int add_len = 0;
    hi_ushort16 *pus_msglen = NULL;
    hi_char8 aucPrifix[64] = {0};
    hi_char8 ac_ipv6[CM_IPV6_ADDR_LEN_MAX] = {0};
    hi_char8 pri_dns[CM_IPV6_ADDR_LEN_MAX] = {0};
    hi_char8 sec_dns[CM_IPV6_ADDR_LEN_MAX] = {0};
    hi_ushort16 tlv_leaf = 0;
    hi_ctcoam_tlv_s *tlv_head = (hi_ctcoam_tlv_s *)(pv_inmsg - 4);
    vs_ctcoam_lan_dhcpv6_server_s *dhcpv6_cfg = NULL;
    IgdLanIPv6AttrConfTab data;

    if (pv_outmsg == NULL)
        return HI_RET_INVALID_PARA;

    dhcpv6_cfg = (vs_ctcoam_lan_dhcpv6_server_s *)pv_outmsg;

    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));
    data.ulBitmap |= LAN_IPV6_ATTR_MASK_ALL;
    data.ulBitmap1 |= LAN_IPV6_ATTR_MASK1_ALL;
    //ret = igdCmConfGet(IGD_LAN_IPV6_ATTR_TAB,(unsigned char *)&data,sizeof(data));
    ret = HI_IPC_CALL("hi_sml_lan_ipv6_attr_get", &data);
    if(ret != 0)
    {
        printf("hi_sml_lan_ipv6_attr_get fail, ret : %d \r\n", ret);
        return HI_RET_FAIL;
    }

    dhcpv6_cfg->itemNum = 1;
    dhcpv6_cfg->ipType = 1;//ipv6
    dhcpv6_cfg->ipType = (hi_ushort16)htons(dhcpv6_cfg->ipType);

    HI_OS_MEMCPY_S((void *)dhcpv6_cfg->lanIPv6addr, CM_IPV6_ADDR_LEN, (void *)data.aucIPv6Address, CM_IPV6_ADDR_LEN);

    if (data.ucPrifixSource == LAN_PREFIX_TYPE_WAN_DELEGATED)
    {
        dhcpv6_cfg->lanIPv6PrefixMode = 0;
    }
    else if (data.ucPrifixSource == LAN_PREFIX_TYPE_WAN_STATIC)
    {
        dhcpv6_cfg->lanIPv6PrefixMode = 1;
        sscanf(data.aucPrifix, "%47[^/]/%d", aucPrifix, &dhcpv6_cfg->lanPrefixlength);
        dhcpv6_cfg->lanPrefixlength = data.ucPrefixLength;//for openwrt
        //printf("data.aucPrifix=%s, aucPrifix=%s, dhcpv6_cfg->lanPrefixlength=%d\n", data.aucPrifix, aucPrifix, dhcpv6_cfg->lanPrefixlength);
        dhcpv6_cfg->lanPrefixlength = htonl(dhcpv6_cfg->lanPrefixlength);
        igdCmApiChartoIpv6((void *)dhcpv6_cfg->lanIPv6Prefix, aucPrifix);
    }


    if (data.ucDnsSource == LAN_DNS_SOURCE_HGU_PROXY)
    {
        dhcpv6_cfg->dhcpv6DnsMode = 2;
        dhcpv6_cfg->dhcpv6DnsMode = (hi_ushort16)htons(dhcpv6_cfg->dhcpv6DnsMode);
    }
    else if (data.ucDnsSource == LAN_DNS_SOURCE_WAN_CONNECTION)
    {
        dhcpv6_cfg->dhcpv6DnsMode = 0;
        dhcpv6_cfg->dhcpv6DnsMode = (hi_ushort16)htons(dhcpv6_cfg->dhcpv6DnsMode);
    }
    else if (data.ucDnsSource == LAN_DNS_SOURCE_STATIC)
    {
        dhcpv6_cfg->dhcpv6DnsMode = 1;
        dhcpv6_cfg->dhcpv6DnsMode = (hi_ushort16)htons(dhcpv6_cfg->dhcpv6DnsMode);

        sscanf(data.aucDnsServers, "%48[^,],%48s", pri_dns, sec_dns);
        //printf("data.aucDnsServers=%s, pri_dns=%s, sec_dns=%s\n", data.aucDnsServers, pri_dns, sec_dns);
        igdCmApiChartoIpv6((void *)dhcpv6_cfg->dhcpPriDnsIpv6, pri_dns);

        igdCmApiChartoIpv6((void *)dhcpv6_cfg->dhcpSecDnsIpv6, sec_dns);
    }

    if (data.ucDhcpV6Enable == 1)//dhcpv6 server enable
    {
        dhcpv6_cfg->enableDhcpServer = 1;
        dhcpv6_cfg->enableDhcpServer = (hi_ushort16)htons(dhcpv6_cfg->enableDhcpServer);
        dhcpv6_cfg->PreferenceLifeTime = data.ulPreferredLifeTime;
        dhcpv6_cfg->PreferenceLifeTime = htonl(dhcpv6_cfg->PreferenceLifeTime);
        dhcpv6_cfg->ValidLifeTime = data.ulValidLifeTime;
        dhcpv6_cfg->ValidLifeTime = htonl(dhcpv6_cfg->ValidLifeTime);
        
        snprintf(ac_ipv6, sizeof(ac_ipv6), "::%.20s", data.aucMinAddr);
        igdCmApiChartoIpv6((void *)dhcpv6_cfg->dhcpPoolStart, ac_ipv6);

        snprintf(ac_ipv6, sizeof(ac_ipv6), "::%.20s", data.aucMaxAddr);
        igdCmApiChartoIpv6((void *)dhcpv6_cfg->dhcpPoolEnd, ac_ipv6);

        dhcpv6_cfg->dhcpPoolType = 0;//for pc
        dhcpv6_cfg->dhcpPoolType = (hi_ushort16)htons(dhcpv6_cfg->dhcpPoolType);

        HI_OS_MEMCPY_S((void *)dhcpv6_cfg->dhcpGatewayIpv6 , CM_IPV6_ADDR_LEN, (void *)data.aucIPv6Address, CM_IPV6_ADDR_LEN);
    }
    else if(data.ucDhcpV6Enable == 0)//dhcpv6 server disable
    {
        if (data.ucDhcpRelay)//dhcpv6 relay
        {
            dhcpv6_cfg->enableDhcpServer = 2;
            dhcpv6_cfg->enableDhcpServer = (hi_ushort16)htons(dhcpv6_cfg->enableDhcpServer);

            igdCmApiChartoIpv6((void *)dhcpv6_cfg->dhcpServerRelayIpv6Addr, data.aucDhcpRelayAddr);
        }
        else
        {
            dhcpv6_cfg->enableDhcpServer = 0;
            dhcpv6_cfg->enableDhcpServer = (hi_ushort16)htons(dhcpv6_cfg->enableDhcpServer);
        }
    }

    if (data.ucRadvdEnable)
    {
        dhcpv6_cfg->RaEnable = 1;
        dhcpv6_cfg->RaManageConfig = data.ucAddrInfoFromDhcp;
        dhcpv6_cfg->RaOtherConfig = data.ucOtherFromDhcp;
        dhcpv6_cfg->RaMaxInterval = data.ulMaxRaTime;
        dhcpv6_cfg->RaMaxInterval = htonl(dhcpv6_cfg->RaMaxInterval);
        dhcpv6_cfg->RaMinInterval = data.ulMinRaTime;
        dhcpv6_cfg->RaMinInterval = htonl(dhcpv6_cfg->RaMinInterval);
    }
    else
    {
        dhcpv6_cfg->RaEnable = 0;
    }

    /* slicing the packets and add tlv header to the data */
    tlv_leaf = tlv_head->us_leaf;
    tlv_leaf = (hi_ushort16)htons(tlv_leaf);
    struct_size = sizeof(vs_ctcoam_lan_dhcpv6_server_s);
    if(struct_size > 0 && struct_size <= 128)
    {
        *puc_changingmsglen = struct_size;
        HI_OS_MEMCPY_S(pv_outmsg, sizeof(vs_ctcoam_lan_dhcpv6_server_s), dhcpv6_cfg, *puc_changingmsglen);
    }
    else if(struct_size > 128 && struct_size <= (255 - 4))//langer than 128, need to slicing the packets
    {
        add_len = packet_slicing_for_send(dhcpv6_cfg, sizeof(vs_ctcoam_lan_dhcpv6_server_s), tlv_head->uc_branch, tlv_leaf);
        add_len = (add_len - 1) * 4;
        pus_msglen = (hi_ushort16 *)(puc_changingmsglen + 1);
        *pus_msglen = 0;
        *puc_changingmsglen = struct_size + add_len;//send msg will use this len value
    }
    else if(struct_size > (255 - 4) && struct_size <= 1496)
    {
        add_len = packet_slicing_for_send(dhcpv6_cfg, sizeof(vs_ctcoam_lan_dhcpv6_server_s), tlv_head->uc_branch, tlv_leaf);
        add_len = (add_len - 1) * 4;
        pus_msglen = (hi_ushort16 *)(puc_changingmsglen + 1);
        *pus_msglen = struct_size + add_len;//send msg will use this len value
        *puc_changingmsglen = 0;
    }
    else
    {
        return HI_RET_FAIL;
    }

    return HI_RET_SUCC;
}

uint32_t vs_ctcoam_set_lan_dhcpv6_server(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    unsigned char cfg_change = 0;
    hi_char8 aucPrifix[CM_IPV6_ADDR_LEN_MAX] = {0};
    hi_char8 pri_dns[CM_IPV6_ADDR_LEN_MAX] = {0};
    hi_char8 sec_dns[CM_IPV6_ADDR_LEN_MAX] = {0};
    vs_ctcoam_lan_dhcpv6_server_s dhcpv6_cfg_data;
    vs_ctcoam_lan_dhcpv6_server_s ram_cfg;
    vs_ctcoam_lan_dhcpv6_server_s *dhcpv6_cfg = &dhcpv6_cfg_data;
    IgdLanIPv6AttrConfTab data;

    if (pv_inmsg == NULL)
        return HI_RET_INVALID_PARA;
    
    packet_slicing_for_receive(&dhcpv6_cfg_data, sizeof(vs_ctcoam_lan_dhcpv6_server_s), pv_inmsg, 1496, 0xc7, 0x220a);

    dhcpv6_cfg->itemNum = ntohl(dhcpv6_cfg->itemNum);
    dhcpv6_cfg->ipType = (hi_ushort16)ntohs(dhcpv6_cfg->ipType);
    dhcpv6_cfg->lanPrefixlength = ntohl(dhcpv6_cfg->lanPrefixlength);
    dhcpv6_cfg->dhcpv6DnsMode = (hi_ushort16)ntohs(dhcpv6_cfg->dhcpv6DnsMode);
    dhcpv6_cfg->enableDhcpServer = (hi_ushort16)ntohs(dhcpv6_cfg->enableDhcpServer);
    dhcpv6_cfg->PreferenceLifeTime = ntohl(dhcpv6_cfg->PreferenceLifeTime);
    dhcpv6_cfg->ValidLifeTime = ntohl(dhcpv6_cfg->ValidLifeTime);
    dhcpv6_cfg->RaMaxInterval = ntohl(dhcpv6_cfg->RaMaxInterval);
    dhcpv6_cfg->RaMinInterval = ntohl(dhcpv6_cfg->RaMinInterval);

    if (dhcpv6_cfg->ipType != 1)
    {
        printf("It is not an ipv4 parameter\n");
        return HI_RET_INVALID_PARA;
    }

    /* compare cfg */
    vs_oam_lan_dhcpv6_cfg_to_ram(&ram_cfg);
    if(memcmp(&ram_cfg, dhcpv6_cfg, sizeof(vs_ctcoam_lan_dhcpv6_server_s)) != 0)
        cfg_change = 1;

    /* if cfg change, set to mib */
    if(cfg_change == 1)
    {
        printf_oam_dhcpv6_data(&ram_cfg);
        printf_oam_dhcpv6_data(dhcpv6_cfg);

        HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));

        data.ulBitmap |= LAN_IPV6_ATTR_MASK_BIT0_IPV6_ADDR|LAN_IPV6_ATTR_MASK_BIT1_DNS_SOURCE|LAN_IPV6_ATTR_MASK_BIT2_DHCPV6_ENABLE\
                    |LAN_IPV6_ATTR_MASK_BIT4_LEASE_TIME|LAN_IPV6_ATTR_MASK_BIT5_DNS_SERVERS|LAN_IPV6_ATTR_MASK_BIT6_DNS_WAN_NAME\
                    |LAN_IPV6_ATTR_MASK_BIT8_MIN_ADDR|LAN_IPV6_ATTR_MASK_BIT9_MAX_ADDR|LAN_IPV6_ATTR_MASK_BIT10_RADVD_ENABLE\
                    |LAN_IPV6_ATTR_MASK_BIT12_OTHER_INFO_FROM|LAN_IPV6_ATTR_MASK_BIT13_MIN_RA_TIME|LAN_IPV6_ATTR_MASK_BIT14_MAX_RA_TIME\
                    |LAN_IPV6_ATTR_MASK_BIT11_ADDR_INFO_FROM|LAN_IPV6_ATTR_MASK_BIT16_PRIFIX_SOURCE|LAN_IPV6_ATTR_MASK_BIT17_PRIFIX\
                    |LAN_IPV6_ATTR_MASK_BIT18_DELEGATED_WAN_NAME|LAN_IPV6_ATTR_MASK_BIT19_PREFFERED_LIFETIME\
                    |LAN_IPV6_ATTR_MASK_BIT20_VALID_LIFETIME|LAN_IPV6_ATTR_MASK_BIT22_IPV6_POOL_ENABLE;
        data.ulBitmap1 |= LAN_IPV6_ATTR_MASK1_BIT8_DHCP_RELAY|LAN_IPV6_ATTR_MASK1_BIT9_DHCP_RELAY_ADDR|LAN_IPV6_ATTR_MASK1_BIT10_PRIFIX_LENGTH;

        HI_OS_MEMCPY_S((void *)data.aucIPv6Address, CM_IPV6_ADDR_LEN, (void *)dhcpv6_cfg->lanIPv6addr, CM_IPV6_ADDR_LEN);
        
        data.ucPrefixLength = dhcpv6_cfg->lanPrefixlength;//for openwrt

        if (dhcpv6_cfg->lanIPv6PrefixMode == 0)
        {
            //data.ucPrifixSource = LAN_PREFIX_TYPE_WAN_DELEGATED;
            data.ucPrifixSource = LAN_PREFIX_TYPE_AUTO;//for openwrt
        }
        else if (dhcpv6_cfg->lanIPv6PrefixMode == 1)
        {
            data.ucPrifixSource = LAN_PREFIX_TYPE_WAN_STATIC;
            igdCmApiIpv6toChar((void *)dhcpv6_cfg->lanIPv6Prefix, aucPrifix);
            //snprintf(data.aucPrifix, sizeof(data.aucPrifix), "%.40s/%d", aucPrifix, dhcpv6_cfg->lanPrefixlength);
            snprintf(data.aucPrifix, sizeof(data.aucPrifix), "%.40s", aucPrifix);//for openwrt
            //printf("data.aucPrifix=%s\n", data.aucPrifix);
        }

        if (dhcpv6_cfg->dhcpv6DnsMode == 2)
        {
            data.ucDnsSource = LAN_DNS_SOURCE_HGU_PROXY;
        }
        else if (dhcpv6_cfg->dhcpv6DnsMode == 0)
        {
            data.ucDnsSource = LAN_DNS_SOURCE_WAN_CONNECTION;
        }
        else if (dhcpv6_cfg->dhcpv6DnsMode == 1)
        {
            data.ucDnsSource = LAN_DNS_SOURCE_STATIC;
            igdCmApiIpv6toChar((void *)dhcpv6_cfg->dhcpPriDnsIpv6, pri_dns);
            igdCmApiIpv6toChar((void *)dhcpv6_cfg->dhcpSecDnsIpv6, sec_dns);
            snprintf(data.aucDnsServers, sizeof(data.aucDnsServers), "%s,%s", pri_dns, sec_dns);
            //printf("data.aucDnsServers=%s\n", data.aucDnsServers);
        }   

        if (dhcpv6_cfg->enableDhcpServer == 1)//dhcpv6 server enable
        {
            data.ucDhcpV6Enable = 1;
            data.ulPreferredLifeTime = dhcpv6_cfg->PreferenceLifeTime;
            data.ulValidLifeTime = dhcpv6_cfg->ValidLifeTime;
            sprintf(data.aucMinAddr, "%02x%02x:%02x%02x:%02x%02x:%02x%02x",\
                    dhcpv6_cfg->dhcpPoolStart[8],dhcpv6_cfg->dhcpPoolStart[9],dhcpv6_cfg->dhcpPoolStart[10],\
                    dhcpv6_cfg->dhcpPoolStart[11],dhcpv6_cfg->dhcpPoolStart[12],dhcpv6_cfg->dhcpPoolStart[13],\
                    dhcpv6_cfg->dhcpPoolStart[14],dhcpv6_cfg->dhcpPoolStart[15]);

            sprintf(data.aucMaxAddr, "%02x%02x:%02x%02x:%02x%02x:%02x%02x",\
                    dhcpv6_cfg->dhcpPoolEnd[8],dhcpv6_cfg->dhcpPoolEnd[9],dhcpv6_cfg->dhcpPoolEnd[10],\
                    dhcpv6_cfg->dhcpPoolEnd[11],dhcpv6_cfg->dhcpPoolEnd[12],dhcpv6_cfg->dhcpPoolEnd[13],\
                    dhcpv6_cfg->dhcpPoolEnd[14],dhcpv6_cfg->dhcpPoolEnd[15]);
            //printf("data.aucMinAddr=%s, data.aucMaxAddr=%s",data.aucMinAddr, data.aucMaxAddr);

            data.ucIPv6PoolEnable = 0;//for pc
            data.ucDhcpRelay = 0;   
        }
        else if (dhcpv6_cfg->enableDhcpServer == 0)//disable dhcp server
        {
            data.ucDhcpV6Enable = 0;
            data.ucDhcpRelay = 0;
        }
        else if (dhcpv6_cfg->enableDhcpServer == 2) //enable dhcp relay
        {
            data.ucDhcpRelay = 1;
            data.ucDhcpV6Enable = 0;
            igdCmApiIpv6toChar((void *)dhcpv6_cfg->dhcpServerRelayIpv6Addr, data.aucDhcpRelayAddr);
            //printf("data.aucDhcpRelayAddr=%s\n",data.aucDhcpRelayAddr);
        }

        if (dhcpv6_cfg->RaEnable)
        {
            data.ucRadvdEnable = 1;
            data.ucAddrInfoFromDhcp = dhcpv6_cfg->RaManageConfig;
            data.ucOtherFromDhcp = dhcpv6_cfg->RaOtherConfig;
            data.ulMaxRaTime = dhcpv6_cfg->RaMaxInterval;
            data.ulMinRaTime = dhcpv6_cfg->RaMinInterval;
        }
        else
        {
            data.ucRadvdEnable = 0;
        }

        //ret = igdCmConfSet(IGD_LAN_IPV6_ATTR_TAB,(unsigned char *)&data,sizeof(data));
        ret = HI_IPC_CALL("hi_sml_lan_ipv6_attr_set", &data);
        if(ret != 0)
        {
            printf("hi_sml_lan_ipv6_attr_set fail, ret : %d \r\n", ret);
            return HI_RET_FAIL;
        }
    }

    return HI_RET_SUCC;
}



#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

