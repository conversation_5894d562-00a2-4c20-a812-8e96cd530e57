#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "vs_ctcoam_tr069_stun_management.h"


uint32_t vs_ctcoam_get_tr069_stun_management(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    unsigned int struct_size = 0;
    unsigned char add_len = 0;
    hi_ushort16 *pus_msglen = NULL;
    hi_ushort16 tlv_leaf = 0;
    hi_ctcoam_tlv_s *tlv_head = (hi_ctcoam_tlv_s *)(pv_inmsg - 4);
    IgdRmtMgtTr069AttrConfTab data;
    vs_ctcoam_tr069_stun_management_s *tr069_stun_cfg = NULL;

    if (pv_outmsg == NULL)
		return HI_RET_INVALID_PARA;

    tr069_stun_cfg = (vs_ctcoam_tr069_stun_management_s *)pv_outmsg;

    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));

#if defined(CONFIG_PLATFORM_OPENWRT)
    // OpenWRT平台：使用IPC调用获取STUN配置
    ret = HI_IPC_CALL("hi_sml_tr069_stun_management_get", &data);
    if (ret != HI_RET_SUCC) {
        printf("HI_IPC_CALL hi_sml_tr069_stun_management_get ret : %d \r\n", ret);
        return ret;
    }
#else
    // 其他平台：使用传统的igdCmConfGet方式
    data.ulBitmap |= TR069_ATTR_MASK_ALL;
    ret = igdCmConfGet(IGD_REMOTEMGT_TR069_ATTR_TAB,(unsigned char *)&data,sizeof(data));
    if(ret != 0)
    {
        printf("igdCmConfGet IGD_REMOTEMGT_TR069_ATTR_TAB ret : %d \r\n", ret);
        return HI_RET_FAIL;
    }
#endif

    tr069_stun_cfg->tr069_stun_enable = data.ucStunEnable;
    tr069_stun_cfg->stun_server_port = data.ulStunServerPort;
    tr069_stun_cfg->stun_server_port = (hi_ushort16)htons(tr069_stun_cfg->stun_server_port);

    HI_OS_MEMCPY_S(tr069_stun_cfg->stun_server_address, 128, data.aucStunServerAddr, 128);
    HI_OS_MEMCPY_S(tr069_stun_cfg->stun_server_username, 64, data.aucStunUsername, 64);
    HI_OS_MEMCPY_S(tr069_stun_cfg->stun_server_password, 64, data.aucStunPassword, 64);

    /* slicing the packets and add tlv header to the data */
    tlv_leaf = tlv_head->us_leaf;
    tlv_leaf = (hi_ushort16)htons(tlv_leaf);
	struct_size = sizeof(vs_ctcoam_tr069_stun_management_s);
    if(struct_size > 0 && struct_size <= 128)
    {
        *puc_changingmsglen = struct_size;
        HI_OS_MEMCPY_S(pv_outmsg, sizeof(tr069_stun_cfg), tr069_stun_cfg, *puc_changingmsglen);
    }
	else if(struct_size > 128 && struct_size <= (255 - 4))//langer than 128, need to slicing the packets
	{
        add_len = packet_slicing_for_send(tr069_stun_cfg, sizeof(vs_ctcoam_tr069_stun_management_s), tlv_head->uc_branch, tlv_leaf);
        add_len = (add_len - 1) * 4;
		pus_msglen = (hi_ushort16 *)(puc_changingmsglen + 1);
		*pus_msglen = 0;
		*puc_changingmsglen = struct_size + add_len;//send msg will use this len value
	}
	else if(struct_size > (255 - 4) && struct_size <= 1496)
	{
        add_len = packet_slicing_for_send(tr069_stun_cfg, sizeof(vs_ctcoam_tr069_stun_management_s), tlv_head->uc_branch, tlv_leaf);
        add_len = (add_len - 1) * 4;
		pus_msglen = (hi_ushort16 *)(puc_changingmsglen + 1);
		*pus_msglen = struct_size + add_len;//send msg will use this len value
		*puc_changingmsglen = 0;
	}
	else
	{
		return HI_RET_FAIL;
	}
    
    return HI_RET_SUCC;
}

uint32_t vs_ctcoam_set_tr069_stun_management(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    vs_ctcoam_tr069_stun_management_s tr069_stun_cfg_date;
    vs_ctcoam_tr069_stun_management_s *tr069_stun_cfg = &tr069_stun_cfg_date;
    IgdRmtMgtTr069AttrConfTab data;

    if (pv_inmsg == NULL)
        return HI_RET_INVALID_PARA;
    
    packet_slicing_for_receive(&tr069_stun_cfg_date, sizeof(vs_ctcoam_tr069_stun_management_s), pv_inmsg, 1496, 0xc7, 0x202a);

    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));

#if defined(CONFIG_PLATFORM_OPENWRT)//Add by wzx for bug#18674
    // OpenWRT平台：使用IPC调用设置STUN配置
    data.ucStunEnable = (tr069_stun_cfg->tr069_stun_enable==1)?1:0;
    if (tr069_stun_cfg->tr069_stun_enable == 1)
    {
        tr069_stun_cfg->stun_server_port = (hi_ushort16)ntohs(tr069_stun_cfg->stun_server_port);
        data.ulStunServerPort = tr069_stun_cfg->stun_server_port;
        HI_OS_MEMCPY_S(data.aucStunServerAddr, 128, tr069_stun_cfg->stun_server_address, 128);
        HI_OS_MEMCPY_S(data.aucStunUsername, 64, tr069_stun_cfg->stun_server_username, 64);
        HI_OS_MEMCPY_S(data.aucStunPassword, 64, tr069_stun_cfg->stun_server_password, 64);
       
        data.aucStunServerAddr[sizeof(data.aucStunServerAddr)-1] = '\0';
        data.aucStunUsername[sizeof(data.aucStunUsername)-1] = '\0';
        data.aucStunPassword[sizeof(data.aucStunPassword)-1] = '\0';
    }   
    ret = HI_IPC_CALL("hi_sml_tr069_stun_management_set", &data);
    if (ret != HI_RET_SUCC) {
        printf("HI_IPC_CALL hi_sml_tr069_stun_management_set ret : %d \r\n", ret);
        return ret;
    }
#else
    data.ulBitmap |= TR069_ATTR_MASK_BIT4_STUN_ENABLE;
    data.ucStunEnable = (tr069_stun_cfg->tr069_stun_enable==1)?1:0;
    if (tr069_stun_cfg->tr069_stun_enable == 1)
    {
        data.ulBitmap |=TR069_ATTR_MASK_BIT15_STUN_SERVER_ADDR|TR069_ATTR_MASK_BIT16_STUN_SERVER_PORT\
                       |TR069_ATTR_MASK_BIT17_STUN_USERNAME|TR069_ATTR_MASK_BIT18_STUN_PASSWORD\
                       |TR069_ATTR_MASK_BIT19_STUN_MAX_KEEPALIVE_PERIOD|TR069_ATTR_MASK_BIT20_STUN_MIN_KEETPALIVE_PERIOD;

        tr069_stun_cfg->stun_server_port = (hi_ushort16)ntohs(tr069_stun_cfg->stun_server_port);
        data.ulStunServerPort = tr069_stun_cfg->stun_server_port;
        data.ulStunMinKeepAlivePeriod = 10; /* for bug#16258, private protocol do not have this param, set default here */
        data.lStunMaxKeepAlivePeriod = 60;  /* for bug#16258, private protocol do not have this param, set default here */
        HI_OS_MEMCPY_S(data.aucStunServerAddr, 128, tr069_stun_cfg->stun_server_address, 128);
        HI_OS_MEMCPY_S(data.aucStunUsername, 64, tr069_stun_cfg->stun_server_username, 64);
        HI_OS_MEMCPY_S(data.aucStunPassword, 64, tr069_stun_cfg->stun_server_password, 64);
       
        data.aucStunServerAddr[sizeof(data.aucStunServerAddr)-1] = '\0';
        data.aucStunUsername[sizeof(data.aucStunUsername)-1] = '\0';
        data.aucStunPassword[sizeof(data.aucStunPassword)-1] = '\0';
    }

    ret = igdCmConfSet(IGD_REMOTEMGT_TR069_ATTR_TAB,(unsigned char *)&data,sizeof(data));
    if(ret != 0)
    {
        printf("igdCmConfSet IGD_REMOTEMGT_TR069_ATTR_TAB ret : %d \r\n", ret);
        return HI_RET_FAIL;
    }
#endif

    return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

