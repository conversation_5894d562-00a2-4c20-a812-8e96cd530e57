#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "vs_ctcoam_firewall_level.h"

#define ICMP_ACCESSABLE 1
#define TELNET_ACCESSABLE 1
#define SSH_ACCESSABLE 1
#define FTP_ACCESSABLE 1
#define TFTP_ACCESSABLE 1
#define HTTP_ACCESSABLE 1
#define HTTPS_ACCESSABLE 1

typedef enum
{
    VS_OAM_FIREWALL_LEVEL_DISABLE,
    VS_OAM_FIREWALL_LEVEL_LOW,
    VS_OAM_FIREWALL_LEVEL_MIDDLE,
    VS_OAM_FIREWALL_LEVEL_HIGH
}VS_OAM_FIREWALL_LEVEL;

uint32_t vs_ctcoam_get_firewall_level(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    vs_ctcoam_firewall_level_s *firewall_cfg = NULL;
    IgdSecurFirewallAttrConfTab data;

    if (pv_outmsg == NULL)
        return HI_RET_INVALID_PARA;
    
    firewall_cfg = (vs_ctcoam_firewall_level_s *)pv_outmsg;

    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));
    data.ulBitmap |= FIREWALL_ATTR_MASK_ALL;
    //ret = igdCmConfGet(IGD_SECUR_URL_FIREWALL_ATTR_TAB, (unsigned char *)&data, sizeof(data));
    ret = HI_IPC_CALL("hi_sml_firewall_level_attr_get", &data);
    if(ret != 0)
    {
        printf("hi_sml_firewall_level_attr_get fail, ret : %d \r\n", ret);
        return HI_RET_FAIL;
    }

    if(data.ucFirlewallEnable != 0)
    {
        firewall_cfg->level = (data.ucFirewallLevel==FIREWALL_LEVEL_HIGH)?VS_OAM_FIREWALL_LEVEL_HIGH:VS_OAM_FIREWALL_LEVEL_LOW;
    }
    else
    {
        firewall_cfg->level = 0;
    }
    
    return HI_RET_SUCC;
}

uint32_t vs_ctcoam_set_firewall_level(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    uword32 mask = 0;
    vs_ctcoam_firewall_level_s *firewall_cfg = NULL;
    IgdSecurFirewallAttrConfTab data;
    IgdAppServiceManageAttrConfTab data2;

    if (pv_inmsg == NULL)
        return HI_RET_INVALID_PARA;

    firewall_cfg = (vs_ctcoam_firewall_level_s *)pv_inmsg;

    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));

    data.ulBitmap |= FIREWALL_ATTR_MASK_BIT0_ENALBE|FIREWALL_ATTR_MASK_BIT4_FIREWALLLEVEL;
    data.ucFirlewallEnable = 1;
    data.ucFirewallLevel = (firewall_cfg->level==VS_OAM_FIREWALL_LEVEL_HIGH)?FIREWALL_LEVEL_HIGH:FIREWALL_LEVEL_LOW;
    //ret = igdCmConfSet(IGD_SECUR_URL_FIREWALL_ATTR_TAB, (unsigned char *)&data, sizeof(data));
    ret = HI_IPC_CALL("hi_sml_firewall_level_attr_set", &data);
    if(ret != 0)
    {
        printf("hi_sml_firewall_level_attr_set fail, ret : %d \r\n", ret);
        return HI_RET_FAIL;
    }

    /* if set firewall to high level, disable telnet, ssh, tftp, ftp, http and https on the WAN side, */
    /* and only permit http or https on the LAN side */
    if (data.ucFirewallLevel == FIREWALL_LEVEL_HIGH)
    {
        HI_OS_MEMSET_S(&data2, sizeof(data2), 0, sizeof(data2));

        ret = igdCmConfGet(IGD_APP_SERVICE_MANAGE_ATTR_TAB, (unsigned char *)&data2, sizeof(data2));
        if(ret != 0)
        {
            printf("igdCmConfGet IGD_APP_SERVICE_MANAGE_ATTR_TAB ret : %d \r\n", ret);
            return HI_RET_FAIL;
        }

#if (TELNET_ACCESSABLE==1)
        data2.ucTelnetEnable = TELNET_SERVICE_DISABLE;
        data2.ucTelnetWANEnable = TELNET_SERVICE_DISABLE;
        data2.ulBitmap |= SERVICE_MANAGE_ATTR_MASK_BIT1_TELNET_ENABLE|SERVICE_MANAGE_ATTR_MASK_BIT18_TELNET_WAN_ENABLE;
#endif

#if (SSH_ACCESSABLE==1)
        if(access("/usr/sbin/dropbear", F_OK) == 0)
        {
            data2.ucSshEnable = SSH_SERVICE_DISABLE;
            data2.ulBitmap |= SERVICE_MANAGE_ATTR_MASK_BIT10_SSH_ENABLE;
        }
#endif

#if (FTP_ACCESSABLE==1)
        if(access("/usr/sbin/vsftpd",F_OK)==0)
        {
            data2.ucFtpEnable = FTP_SERVICE_DISABLE;
            data2.ulBitmap |= SERVICE_MANAGE_ATTR_MASK_BIT0_FTP_ENABLE;
        }
#endif

#if (TFTP_ACCESSABLE==1)
        if(access("/usr/sbin/tftpd",F_OK)==0)
        {
            data2.ucTftpEnable = TFTP_SERVICE_DISABLE;
            data2.ulBitmap |= SERVICE_MANAGE_ATTR_MASK_BIT11_TFTP_ENABLE;
        }
#endif    

#if (HTTP_ACCESSABLE==1)
        mask = ~(1 << (HTTP_SERVICE_ENABLE_REMOTE - 1));
        data2.ulHTTPEnable &= mask;
        data2.ulBitmap |= SERVICE_MANAGE_ATTR_MASK_BIT8_HTTP_ENABLE;
#endif

#if (HTTPS_ACCESSABLE==1)
        mask = ~(1 << (HTTPS_SERVICE_ENABLE_REMOTE - 1));
        data2.ucHttpsEnable &= mask;
        data2.ulBitmap1 |= SERVICE_MANAGE_ATTR_MASK1_BIT4_HTTPS_ENABLE;
#endif

#if (ICMP_ACCESSABLE==1)
        data2.ucIcmpWANEnable = ICMP_SERVICE_DISABLE;
        data2.ulBitmap |= SERVICE_MANAGE_ATTR_MASK_BIT28_ICMP_WAN_ENABLE;
#endif
        
        ret = igdCmConfSet(IGD_APP_SERVICE_MANAGE_ATTR_TAB, (unsigned char *)&data2, sizeof(data2));
        if(ret != 0)
        {
            printf("igdCmConfSet IGD_APP_SERVICE_MANAGE_ATTR_TAB ret : %d \r\n", ret);
            return HI_RET_FAIL;
        }
    }

    return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

