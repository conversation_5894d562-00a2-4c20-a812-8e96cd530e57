#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "vs_ctcoam_mac_info.h"
#include "hi_sml_br.h"
#include "hi_sml_common.h"

uint32_t vs_ctcoam_get_mac_info(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    hi_uint32 ui_port = pst_instance->un_value.ui_value & 0xff;
    hi_uint32 learn_mac_num = 0, report_mac_num = 0, max_mac_num = 180;
    hi_uint32 i = 0, data_len = 0;
    hi_uchar8 *ptr = NULL;
    hi_uchar8 send_data[1496];
    unsigned int struct_size = 0;
    unsigned int add_len = 0;
    hi_ushort16 *pus_msglen = NULL;
    hi_ushort16 tlv_leaf = 0;
    hi_ctcoam_tlv_s *tlv_head = (hi_ctcoam_tlv_s *)(pv_inmsg - 4);
    vs_ctcoam_mac_info_s mac_info;
    hi_sml_br_table_s st_table = {0};

    switch(ui_port)
    {
        case 1:
            st_table.em_port = HI_SML_ETH0_E;
            break;
        case 2:
            st_table.em_port = HI_SML_ETH1_E;
            break;
        case 3:
            st_table.em_port = HI_SML_ETH2_E;
            break;
        case 4:
            st_table.em_port = HI_SML_ETH3_E;
            break;    
        default:
            st_table.em_port = HI_SML_MAX_E;
            break;    
    }

    ret = HI_IPC_CALL("hi_sml_br_table_get", &st_table);
    if(ret != HI_RET_SUCC)
    {
        printf("hi_sml_br_table_get error! ui_port = %d\n", ui_port);
        return HI_RET_FAIL;
    }

    learn_mac_num = st_table.ui_cnt;
    learn_mac_num = htonl(learn_mac_num);
    HI_OS_MEMCPY_S(send_data, sizeof(send_data), &learn_mac_num, sizeof(learn_mac_num));

    report_mac_num = (st_table.ui_cnt > max_mac_num)? max_mac_num : st_table.ui_cnt;
    report_mac_num = htonl(report_mac_num);
    HI_OS_MEMCPY_S(send_data + 4, sizeof(send_data), &report_mac_num, sizeof(report_mac_num));
    report_mac_num = ntohl(report_mac_num);
    
    ptr = send_data + 8; 
    data_len += 8;
    for (i = 0; i < report_mac_num; i++) 
    {
        /*
        printf("st_table.st_item[%d].aui_mac = 0x%x:0x%x:0x%x:0x%x:0x%x:0x%x\n", i,
        st_table.st_item[i].aui_mac[0], st_table.st_item[i].aui_mac[1],
        st_table.st_item[i].aui_mac[2], st_table.st_item[i].aui_mac[3],
        st_table.st_item[i].aui_mac[4], st_table.st_item[i].aui_mac[5]);
        printf("st_table.st_item[%d].ui_age = %d, us_vlan = %d, us_vlan = 0x%x\n", i, st_table.st_item[i].ui_age, st_table.st_item[i].us_vlan, st_table.st_item[i].us_vlan);
        */

        HI_OS_MEMSET_S(&mac_info, sizeof(mac_info), 0, sizeof(mac_info));
        HI_OS_MEMCPY_S(mac_info.mac, sizeof(mac_info.mac), st_table.st_item[i].aui_mac, sizeof(st_table.st_item[i].aui_mac));
        mac_info.vlan = st_table.st_item[i].us_vlan;
        mac_info.vlan = (hi_ushort16)htons(mac_info.vlan);

        HI_OS_MEMCPY_S(ptr, sizeof(mac_info), &mac_info, sizeof(mac_info));
        ptr += sizeof(mac_info);
        data_len += sizeof(mac_info);
    }

    /* slicing the packets and add tlv header to the data */
    tlv_leaf = tlv_head->us_leaf;
    tlv_leaf = (hi_ushort16)htons(tlv_leaf);
    struct_size = data_len;
    if(struct_size > 0 && struct_size <= 128)
    {
        *puc_changingmsglen = struct_size;
        HI_OS_MEMCPY_S(pv_outmsg, sizeof(send_data), send_data, *puc_changingmsglen);
    }
    else if(struct_size > 128 && struct_size <= (255 - 4))//langer than 128, need to slicing the packets
    {
        add_len = packet_slicing_for_send(send_data, data_len, tlv_head->uc_branch, tlv_leaf);
        add_len = (add_len - 1) * 4;
        pus_msglen = (hi_ushort16 *)(puc_changingmsglen + 1);
        *pus_msglen = 0;
        *puc_changingmsglen = struct_size + add_len;//send msg will use this len value
        HI_OS_MEMCPY_S(pv_outmsg, sizeof(send_data), send_data, *puc_changingmsglen); 
    }
    else if(struct_size > (255 - 4) && struct_size <= 1496)
    {
        add_len = packet_slicing_for_send(send_data, data_len, tlv_head->uc_branch, tlv_leaf);
        add_len = (add_len - 1) * 4;
        pus_msglen = (hi_ushort16 *)(puc_changingmsglen + 1);
        *pus_msglen = struct_size + add_len;//send msg will use this len value
        *puc_changingmsglen = 0;
        HI_OS_MEMCPY_S(pv_outmsg, sizeof(send_data), send_data, *pus_msglen);
    }
    else
    {
        return HI_RET_FAIL;
    }

    return HI_RET_SUCC;
}



#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

