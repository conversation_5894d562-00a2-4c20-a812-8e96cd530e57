#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "vs_ctcoam_wifi_2_4G_switch_configuration.h"

int vs_oam_2_4G_switch_cfg_to_ram(vs_ctcoam_wifi_2_4G_switch_configuration_s *cfg)
{
    int ret = 0;
    IgdWLANGlobalAttrCfgTab data;

    if (cfg == NULL)
        return HI_RET_INVALID_PARA;

    HI_OS_MEMSET_S(cfg, sizeof(vs_ctcoam_wifi_2_4G_switch_configuration_s), 0, sizeof(vs_ctcoam_wifi_2_4G_switch_configuration_s));
    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));

    data.ulBitmap |= WLAN_GLOBAL_CFG_ATTR_MASK_RFBAND | WLAN_GLOBAL_CFG_ATTR_MASK_APMODULEENABLE
                        |WLAN_GLOBAL_CFG_ATTR_MASK_REGION|WLAN_GLOBAL_CFG_ATTR_MASK_CHANNEL
                        |WLAN_GLOBAL_CFG_ATTR_MASK_CHANNELWIDTH|WLAN_GLOBAL_CFG_ATTR_MASK_RFMODE
                        |WLAN_GLOBAL_CFG_ATTR_MASK_POWERLEVEL;

    data.ucRFBand = WLAN_RFBAND_2_4G;    
#ifdef CONFIG_PLATFORM_OPENWRT
    ret = HI_IPC_CALL("hi_sml_igd_wlan_global_cfg_attr_tab_get", &data);
#else
    ret = igdCmConfGet(IGD_WLAN_GLOBAL_CFG_ATTR_TAB, (unsigned char *)&data, sizeof(data));
#endif
    if(ret != 0)
    {
        printf("i_ret igdCmConfGet IGD_WLAN_GLOBAL_CFG_ATTR_TAB %d \n", ret);
        return HI_RET_FAIL;
    }
    
    cfg->enable = data.ucAPModuleEnable;
    cfg->channel = data.ucChannel;

    switch(data.ucRegion)
    {
        case WLAN_RF_REGION_CN: /**< China */
            cfg->country = REGION_CN;
            break;
        case WLAN_RF_REGION_US: /**< The United States of America */
            cfg->country = REGION_FCC;
            break;
        case WLAN_RF_REGION_AU: /**< Australia */
            cfg->country = REGION_FCC;
            break;
        case WLAN_RF_REGION_CA: /**< Canada */
            cfg->country = REGION_IC;
            break;
        case WLAN_RF_REGION_ES: /**< Spain */
            cfg->country = REGION_SPAIN;
            break;
        case WLAN_RF_REGION_RU: /**< Russian */
            cfg->country = REGION_RUSSIAN;
            break;
        case WLAN_RF_REGION_IN: /**< India */
            cfg->country = REGION_CN;
            break;
        default:
            cfg->country = REGION_CN;
            break;
    }

    switch (data.ucRFMode)
    {
        case WLAN_RF_WORK_MODE_A:
            cfg->standard = VS_OAM_5G_WIFI_STANDARD_AC_A;
            break;            
        case WLAN_RF_WORK_MODE_B:
            cfg->standard = VS_OAM_5G_WIFI_STANDARD_B;
            break;
        case WLAN_RF_WORK_MODE_G:
            cfg->standard = VS_OAM_5G_WIFI_STANDARD_G;
            break;                        
        case WLAN_RF_WORK_MODE_N:
            cfg->standard = VS_OAM_5G_WIFI_STANDARD_N;
            break;                      
        case WLAN_RF_WORK_MODE_BGN:
            cfg->standard = VS_OAM_5G_WIFI_STANDARD_BGN;
            break;                   
        case WLAN_RF_WORK_MODE_AC:
            cfg->standard = VS_OAM_5G_WIFI_STANDARD_AC;
            break;              
        case WLAN_RF_WORK_MODE_A_AC:
            cfg->standard = VS_OAM_5G_WIFI_STANDARD_AC_A;
            break;                  
        case WLAN_RF_WORK_MODE_A_N:
            cfg->standard = VS_OAM_5G_WIFI_STANDARD_AC_A_N_AC;
            break;       
        case WLAN_RF_WORK_MODE_BG:
            cfg->standard = VS_OAM_5G_WIFI_STANDARD_BG;
            break;           
        case WLAN_RF_WORK_MODE_GN:
            cfg->standard = VS_OAM_5G_WIFI_STANDARD_GN;
            break;                   
        case WLAN_RF_WORK_MODE_A_AC_N:
            cfg->standard = VS_OAM_5G_WIFI_STANDARD_AC_A_N;
            break;                      
        case WLAN_RF_WORK_MODE_AC_A_N:
            cfg->standard = VS_OAM_5G_WIFI_STANDARD_AC_N_AC;
            break;              
        case WLAN_RF_WORK_MODE_AC_N:
            cfg->standard = VS_OAM_5G_WIFI_STANDARD_AC_N;
            break; 
        case WLAN_RF_WORK_MODE_BGNAX:
        case WLAN_RF_WORK_MODE_BGNAXBE:
            cfg->standard = VS_OAM_5G_WIFI_STANDARD_BGN_AX;
            break;                 
        case WLAN_RF_WORK_MODE_ANACAX:
        case WLAN_RF_WORK_MODE_ANACAXBE:
            cfg->standard = VS_OAM_5G_WIFI_STANDARD_AC_A_N_AC_AX;
            break;        
        case WLAN_RF_WORK_MODE_N_AX:
            cfg->standard = VS_OAM_5G_WIFI_STANDARD_AX;
            break;                 
        case WLAN_RF_WORK_MODE_AC_AX:
            cfg->standard = VS_OAM_5G_WIFI_STANDARD_AC_AX;
            break;
        default:
            printf("cfg->standard[%d], unknown WIFI RF Mode\n", cfg->standard);
            break;         
    }

    if((data.ucPowerlevel > 20) || (data.ucPowerlevel < 0))
        cfg->transmission_power = 20;
    else
        cfg->transmission_power = data.ucPowerlevel;

    switch (data.ucChannelWidth)
    {
        case WLAN_CHANNELWIDTH_20M:
            cfg->channel_width = VS_OAM_2_4G_WIFI_CHANNEL_WIDTH_20M;
            break;
        case WLAN_CHANNELWIDTH_40M:
            cfg->channel_width = VS_OAM_2_4G_WIFI_CHANNEL_WIDTH_40M;
            break;
        case WLAN_CHANNELWIDTH_20_40M:
            cfg->channel_width = VS_OAM_2_4G_WIFI_CHANNEL_WIDTH_20M_40M;
            break;
        default:
            cfg->channel_width = VS_OAM_2_4G_WIFI_CHANNEL_WIDTH_20M_40M;
            break;
    }

    return HI_RET_SUCC;
}
uint32_t vs_ctcoam_get_wifi_2_4G_switch_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    vs_ctcoam_wifi_2_4G_switch_configuration_s *wifi_cfg = NULL;
    IgdWLANGlobalAttrCfgTab globalData;

    if (pv_outmsg == NULL)
        return HI_RET_INVALID_PARA;
    
    wifi_cfg = (vs_ctcoam_wifi_2_4G_switch_configuration_s *)pv_outmsg;

    HI_OS_MEMSET_S(&globalData, sizeof(globalData), 0, sizeof(globalData));

    globalData.ulBitmap |= WLAN_GLOBAL_CFG_ATTR_MASK_RFBAND | WLAN_GLOBAL_CFG_ATTR_MASK_APMODULEENABLE
                        |WLAN_GLOBAL_CFG_ATTR_MASK_REGION|WLAN_GLOBAL_CFG_ATTR_MASK_CHANNEL
                        |WLAN_GLOBAL_CFG_ATTR_MASK_CHANNELWIDTH|WLAN_GLOBAL_CFG_ATTR_MASK_RFMODE
                        |WLAN_GLOBAL_CFG_ATTR_MASK_POWERLEVEL;

    globalData.ucRFBand = WLAN_RFBAND_2_4G;    
#ifdef CONFIG_PLATFORM_OPENWRT
    ret = HI_IPC_CALL("hi_sml_igd_wlan_global_cfg_attr_tab_get", &globalData);
#else
    ret = igdCmConfGet(IGD_WLAN_GLOBAL_CFG_ATTR_TAB, (unsigned char *)&globalData, sizeof(globalData));
#endif
    if(ret != 0)
    {
        printf("i_ret igdCmConfGet IGD_WLAN_GLOBAL_CFG_ATTR_TAB %d \n", ret);
        return HI_RET_FAIL;
    }
    
    wifi_cfg->enable = globalData.ucAPModuleEnable;
    wifi_cfg->channel = globalData.ucChannel;

    switch(globalData.ucRegion)
    {
        case WLAN_RF_REGION_CN: /**< China */
            wifi_cfg->country = REGION_CN;
            break;
        case WLAN_RF_REGION_US: /**< The United States of America */
            wifi_cfg->country = REGION_FCC;
            break;
        case WLAN_RF_REGION_AU: /**< Australia */
            wifi_cfg->country = REGION_FCC;
            break;
        case WLAN_RF_REGION_CA: /**< Canada */
            wifi_cfg->country = REGION_IC;
            break;
        case WLAN_RF_REGION_ES: /**< Spain */
            wifi_cfg->country = REGION_SPAIN;
            break;
        case WLAN_RF_REGION_RU: /**< Russian */
            wifi_cfg->country = REGION_RUSSIAN;
            break;
        case WLAN_RF_REGION_IN: /**< India */
            wifi_cfg->country = REGION_CN;
            break;
        default:
            wifi_cfg->country = REGION_CN;
            break;
    }

    switch (globalData.ucRFMode)
    {
        case WLAN_RF_WORK_MODE_A:
            wifi_cfg->standard = VS_OAM_5G_WIFI_STANDARD_AC_A;
            break;            
        case WLAN_RF_WORK_MODE_B:
            wifi_cfg->standard = VS_OAM_5G_WIFI_STANDARD_B;
            break;
        case WLAN_RF_WORK_MODE_G:
            wifi_cfg->standard = VS_OAM_5G_WIFI_STANDARD_G;
            break;                        
        case WLAN_RF_WORK_MODE_N:
            wifi_cfg->standard = VS_OAM_5G_WIFI_STANDARD_N;
            break;                      
        case WLAN_RF_WORK_MODE_BGN:
            wifi_cfg->standard = VS_OAM_5G_WIFI_STANDARD_BGN;
            break;                   
        case WLAN_RF_WORK_MODE_AC:
            wifi_cfg->standard = VS_OAM_5G_WIFI_STANDARD_AC;
            break;              
        case WLAN_RF_WORK_MODE_A_AC:
            wifi_cfg->standard = VS_OAM_5G_WIFI_STANDARD_AC_A;
            break;                  
        case WLAN_RF_WORK_MODE_A_N:
            wifi_cfg->standard = VS_OAM_5G_WIFI_STANDARD_AC_A_N_AC;
            break;       
        case WLAN_RF_WORK_MODE_BG:
            wifi_cfg->standard = VS_OAM_5G_WIFI_STANDARD_BG;
            break;           
        case WLAN_RF_WORK_MODE_GN:
            wifi_cfg->standard = VS_OAM_5G_WIFI_STANDARD_GN;
            break;                   
        case WLAN_RF_WORK_MODE_A_AC_N:
            wifi_cfg->standard = VS_OAM_5G_WIFI_STANDARD_AC_A_N;
            break;                      
        case WLAN_RF_WORK_MODE_AC_A_N:
            wifi_cfg->standard = VS_OAM_5G_WIFI_STANDARD_AC_N_AC;
            break;              
        case WLAN_RF_WORK_MODE_AC_N:
            wifi_cfg->standard = VS_OAM_5G_WIFI_STANDARD_AC_N;
            break; 
        case WLAN_RF_WORK_MODE_BGNAX:
        case WLAN_RF_WORK_MODE_BGNAXBE:
            wifi_cfg->standard = VS_OAM_5G_WIFI_STANDARD_BGN_AX;
            break;                 
        case WLAN_RF_WORK_MODE_ANACAX:
        case WLAN_RF_WORK_MODE_ANACAXBE:
            wifi_cfg->standard = VS_OAM_5G_WIFI_STANDARD_AC_A_N_AC_AX;
            break;        
        case WLAN_RF_WORK_MODE_N_AX:
            wifi_cfg->standard = VS_OAM_5G_WIFI_STANDARD_AX;
            break;                 
        case WLAN_RF_WORK_MODE_AC_AX:
            wifi_cfg->standard = VS_OAM_5G_WIFI_STANDARD_AC_AX;
            break;
        default:
            printf("globalData.ucRFMode[%d], unknown WIFI RF Mode\n", globalData.ucRFMode);
            break;         
    }
    
    if((globalData.ucPowerlevel > 20) || (globalData.ucPowerlevel < 0))
        wifi_cfg->transmission_power = 20;
    else
        wifi_cfg->transmission_power = globalData.ucPowerlevel;

    switch (globalData.ucChannelWidth)
    {
        case WLAN_CHANNELWIDTH_20M:
            wifi_cfg->channel_width = VS_OAM_2_4G_WIFI_CHANNEL_WIDTH_20M;
            break;
        case WLAN_CHANNELWIDTH_40M:
            wifi_cfg->channel_width = VS_OAM_2_4G_WIFI_CHANNEL_WIDTH_40M;
            break;
        case WLAN_CHANNELWIDTH_20_40M:
            wifi_cfg->channel_width = VS_OAM_2_4G_WIFI_CHANNEL_WIDTH_20M_40M;
            break;
        default:
            wifi_cfg->channel_width = VS_OAM_2_4G_WIFI_CHANNEL_WIDTH_20M_40M;
            break;
    }

    //printf("\n[2.4G get]enable = %d, country = %d, channel = %d, standard = %d, transmission_power = %d, channel_width = %d\n", 
    //wifi_cfg->enable, wifi_cfg->country, wifi_cfg->channel, wifi_cfg->standard, wifi_cfg->transmission_power, wifi_cfg->channel_width);

    wifi_cfg->country = (hi_ushort16)htons(wifi_cfg->country);
    wifi_cfg->channel = (hi_ushort16)htons(wifi_cfg->channel);
    wifi_cfg->standard = (hi_ushort16)htons(wifi_cfg->standard);
    wifi_cfg->transmission_power = (hi_ushort16)htons(wifi_cfg->transmission_power);
    wifi_cfg->channel_width = (hi_ushort16)htons(wifi_cfg->channel_width);

    return HI_RET_SUCC;
}

uint32_t vs_ctcoam_set_wifi_2_4G_switch_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    unsigned char cfg_change = 0;
    vs_ctcoam_wifi_2_4G_switch_configuration_s ram_cfg;
    vs_ctcoam_wifi_2_4G_switch_configuration_s *wifi_cfg = NULL;
    IgdWLANGlobalAttrCfgTab globalData;

    if (pv_inmsg == NULL)
        return HI_RET_INVALID_PARA;

    wifi_cfg = (vs_ctcoam_wifi_2_4G_switch_configuration_s *)pv_inmsg;

    wifi_cfg->country = (hi_ushort16)ntohs(wifi_cfg->country);
    wifi_cfg->channel = (hi_ushort16)ntohs(wifi_cfg->channel);
    wifi_cfg->standard = (hi_ushort16)ntohs(wifi_cfg->standard);
    wifi_cfg->transmission_power = (hi_ushort16)ntohs(wifi_cfg->transmission_power);
    wifi_cfg->channel_width = (hi_ushort16)ntohs(wifi_cfg->channel_width);
    
    /* compare cfg */
    vs_oam_2_4G_switch_cfg_to_ram(&ram_cfg);
    if(memcmp(&ram_cfg, wifi_cfg, sizeof(vs_ctcoam_wifi_2_4G_switch_configuration_s)) != 0)
        cfg_change = 1;

    /* if cfg change, set to mib */
    if(cfg_change == 1)
    {
        printf("2.4G wifi switch param change\n");
        HI_OS_MEMSET_S(&globalData,sizeof(globalData),0,sizeof(globalData));

        globalData.ulBitmap |= WLAN_GLOBAL_CFG_ATTR_MASK_RFBAND | WLAN_GLOBAL_CFG_ATTR_MASK_APMODULEENABLE
                            |WLAN_GLOBAL_CFG_ATTR_MASK_REGION|WLAN_GLOBAL_CFG_ATTR_MASK_CHANNEL
                            |WLAN_GLOBAL_CFG_ATTR_MASK_CHANNELWIDTH|WLAN_GLOBAL_CFG_ATTR_MASK_RFMODE
                            |WLAN_GLOBAL_CFG_ATTR_MASK_POWERLEVEL;

        globalData.ucRFBand = WLAN_RFBAND_2_4G;
        
        globalData.ucAPModuleEnable = wifi_cfg->enable;
        globalData.ucChannel = wifi_cfg->channel;

        switch (wifi_cfg->country)
        {
            case REGION_ETSI:
            case REGION_FCC:
            case REGION_FRANCE:
            case REGION_MKK:
            case REGION_ISREAL:
            case REGION_MKK2:
            case REGION_MKK3:
                globalData.ucRegion = WLAN_RF_REGION_US;
                break;
            case REGION_RUSSIAN:
                globalData.ucRegion = WLAN_RF_REGION_RU;
                break;
            case REGION_IC:
                globalData.ucRegion = WLAN_RF_REGION_CA;
                break;
            case REGION_SPAIN:
                globalData.ucRegion = WLAN_RF_REGION_ES;
                break;
            case REGION_CN:
            case REGION_GLOBAL:
            case REGION_WORLD_WIDE:
            case REGION_MKK1:
            case REGION_NCC:
                globalData.ucRegion = WLAN_RF_REGION_CN;
                break;
            default:
                globalData.ucRegion = WLAN_RF_REGION_CN;
                break;
        }

        switch(wifi_cfg->standard)
        {
            case VS_OAM_5G_WIFI_STANDARD_AC_A:
                globalData.ucRFMode = WLAN_RF_WORK_MODE_A;
                break;
            case VS_OAM_5G_WIFI_STANDARD_B:
                globalData.ucRFMode = WLAN_RF_WORK_MODE_B;
                break;  
            case VS_OAM_5G_WIFI_STANDARD_G:
                globalData.ucRFMode = WLAN_RF_WORK_MODE_G;
                break; 
            case VS_OAM_5G_WIFI_STANDARD_N:
                globalData.ucRFMode = WLAN_RF_WORK_MODE_N;
                break;
            case VS_OAM_5G_WIFI_STANDARD_BGN:
                globalData.ucRFMode = WLAN_RF_WORK_MODE_BGN;
                break;
            case VS_OAM_5G_WIFI_STANDARD_AC:
                globalData.ucRFMode = WLAN_RF_WORK_MODE_AC;
                break;   
            // case VS_OAM_5G_WIFI_STANDARD_AC_A:
            //     globalData.ucRFMode = WLAN_RF_WORK_MODE_A_AC;
            //     break; 
            case VS_OAM_5G_WIFI_STANDARD_AC_A_N_AC:
                globalData.ucRFMode = WLAN_RF_WORK_MODE_A_N;
                break; 
            case VS_OAM_5G_WIFI_STANDARD_BG:
                globalData.ucRFMode = WLAN_RF_WORK_MODE_BG;
                break; 
            case VS_OAM_5G_WIFI_STANDARD_GN:
                globalData.ucRFMode = WLAN_RF_WORK_MODE_GN;
                break; 
            case VS_OAM_5G_WIFI_STANDARD_AC_N_AC:
                globalData.ucRFMode = WLAN_RF_WORK_MODE_AC_A_N;
                break; 
            case VS_OAM_5G_WIFI_STANDARD_AC_N:
                globalData.ucRFMode = WLAN_RF_WORK_MODE_AC_N;
                break; 
            case VS_OAM_5G_WIFI_STANDARD_AC_A_N:
                globalData.ucRFMode = WLAN_RF_WORK_MODE_A_AC_N;
                break; 
            case VS_OAM_5G_WIFI_STANDARD_BGN_AX:
                globalData.ucRFMode = WLAN_RF_WORK_MODE_BGNAX;
                break; 
            case VS_OAM_5G_WIFI_STANDARD_AC_A_N_AC_AX:
                globalData.ucRFMode = WLAN_RF_WORK_MODE_ANACAX;
                break; 
            case VS_OAM_5G_WIFI_STANDARD_AX:
                globalData.ucRFMode = WLAN_RF_WORK_MODE_N_AX;
                break; 
            case VS_OAM_5G_WIFI_STANDARD_AC_AX:
                globalData.ucRFMode = WLAN_RF_WORK_MODE_AC_AX;
                break; 
            default:
                printf("wifi_cfg->standard = %d, unknown WIFI RF Mode\n", wifi_cfg->standard);
                break;  
        }

        if((wifi_cfg->transmission_power > 20) || (wifi_cfg->transmission_power < 0))
            globalData.ucPowerlevel = 20;
        else
            globalData.ucPowerlevel = wifi_cfg->transmission_power;

        switch (wifi_cfg->channel_width)
        {
            case VS_OAM_2_4G_WIFI_CHANNEL_WIDTH_20M:
                globalData.ucChannelWidth = WLAN_CHANNELWIDTH_20M;
                break;
            case VS_OAM_2_4G_WIFI_CHANNEL_WIDTH_40M:
                globalData.ucChannelWidth = WLAN_CHANNELWIDTH_40M;
                break;
            case VS_OAM_2_4G_WIFI_CHANNEL_WIDTH_20M_40M:
                globalData.ucChannelWidth = WLAN_CHANNELWIDTH_20_40M;
                break;
            default:
                printf("wifi_cfg->channel_width = %d, unknown wifi channel width\n", wifi_cfg->channel_width);
                break;  
        }

#ifdef CONFIG_PLATFORM_OPENWRT
        ret = HI_IPC_CALL("hi_sml_igd_wlan_global_cfg_attr_tab_set", &globalData);
#else
        ret = igdCmConfSet(IGD_WLAN_GLOBAL_CFG_ATTR_TAB,(unsigned char *)&globalData,sizeof(globalData));
#endif
        if(ret != 0)
        {
            printf("i_ret igdCmConfSet IGD_WLAN_GLOBAL_CFG_ATTR_TAB %d \n",ret);
            return HI_RET_FAIL;
        }
    }

    return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

