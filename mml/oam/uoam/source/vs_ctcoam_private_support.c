#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus

#include "vs_ctcoam_private_support.h"

unsigned int search_oam_branch(hi_ctcoam_head_s *branch_head, unsigned int branch_id, unsigned int leaf_id) 
{
    int i = 0, j = 0;
    for(i = 0; i < branch_head->ui_nodenum; i++) 
    {
        hi_ctcoam_tree_node_s *branch_node = &(branch_head->pst_firstnode[i]);
        if(branch_node->ui_id == branch_id) 
        {
            hi_ctcoam_head_s *leaf_head = (hi_ctcoam_head_s *)(branch_node->pv_subhead);
            if(leaf_head != NULL) 
            {
                for(j = 0; j < leaf_head->ui_nodenum; j++) 
                {
                    hi_ctcoam_tree_node_s *child_node = &(leaf_head->pst_firstnode[j]);
                    if(child_node->ui_id == leaf_id) 
                    {
                        return 1;
                    }
                }
            }
        }
    }
    return 0;
}


uint32_t vs_ctcoam_get_private_support(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int32_t ret = 0;
    vs_ctcoam_private_support_s *vs_private = NULL;
    hi_oam_onu_capabilites_s onu_capabilites;
    hi_oam_onu_capabilites2_s onu_capabilites2;
    extern hi_ctcoam_head_s g_st_branch_head;

    if (pv_outmsg == NULL)
        return HI_RET_INVALID_PARA;

    ret = hi_oam_get_capabilities1(&onu_capabilites);
    if (HI_RET_SUCC != ret)
        return HI_RET_FAIL;

    ret = hi_oam_get_capabilities2(&onu_capabilites2);
    if (HI_RET_SUCC != ret)
        return HI_RET_FAIL;

    vs_private = (vs_ctcoam_private_support_s *)pv_outmsg;

    vs_private->ge_port_num = onu_capabilites.number_GE;
    vs_private->fe_port_num = onu_capabilites.number_FE;
    vs_private->pots_port_num = onu_capabilites.number_POTS;
    vs_private->wifi_ssid_num = onu_capabilites2.number_of_wlan_port;
    
    vs_private->support_private = 0x01;

    /* support CATV */
    if(search_oam_branch(&g_st_branch_head, 0xC7, 0x2022) == 1)
    {
        vs_private->catv_num = onu_capabilites2.number_of_catv_port;
        vs_private->support_catv_management |= 0x01;//VSOL CATV
    }

    /* support wan IPV4/IPV6 */
    if(search_oam_branch(&g_st_branch_head, 0xC7, 0x2209) == 1)
        //vs_private->support_wan |= 0x36;  //bit = 0011 0110
        vs_private->support_wan |= 0x76;  //bit = 0111 0110
    /* only support wan IPV4 */
    if(search_oam_branch(&g_st_branch_head, 0xC7, 0x2203) == 1)
        vs_private->support_wan |= 0x05;   //bit = 0101
    
    /* support wifi */
    if(search_oam_branch(&g_st_branch_head, 0xC7, 0x2201) == 1)//2.4G enbale
        vs_private->support_wifi |= 0x01;  
    if(search_oam_branch(&g_st_branch_head, 0xC7, 0x2208) == 1)//5G enable
        vs_private->support_wifi |= 0x02; 
    if(search_oam_branch(&g_st_branch_head, 0xC7, 0x2202) == 1)//SSID configure
        vs_private->support_wifi |= 0x04;
        
    if(access("/usr/local/factory/hi5620v100.cal", F_OK)==0)
        vs_private->support_wifi |= 0x11;//wifi 6 enable
    else
        vs_private->support_wifi |= 0x20;//wifi 6 enable

    /* support dhcp */
    if(search_oam_branch(&g_st_branch_head, 0xC7, 0x2207) == 1)//dhcpv4 configure
        vs_private->support_dhcp |= 0x01;
    if(search_oam_branch(&g_st_branch_head, 0xC7, 0x220a) == 1)//dhcpv6 configure
        vs_private->support_dhcp |= 0x02;
    if(search_oam_branch(&g_st_branch_head, 0xC7, 0x2004) == 1)//dhcp option 82
        vs_private->support_dhcp |= 0x04;
    if(search_oam_branch(&g_st_branch_head, 0xC7, 0x2005) == 1)//dhcp pppoe+
        vs_private->support_dhcp |= 0x08;
    if(search_oam_branch(&g_st_branch_head, 0xC7, 0x2006) == 1)//dhcp option 18/37
        vs_private->support_dhcp |= 0x10;
    if(search_oam_branch(&g_st_branch_head, 0xC7, 0x2007) == 1)//Line identification parameter
        vs_private->support_dhcp |= 0x20;
    if(search_oam_branch(&g_st_branch_head, 0xC7, 0x2008) == 1)//Line identification format
        vs_private->support_dhcp |= 0x40;

    /* support security */
    if(search_oam_branch(&g_st_branch_head, 0xC7, 0x2026) == 1)//user and password
        vs_private->support_security |= 0x01;
    if(search_oam_branch(&g_st_branch_head, 0xC7, 0x2027) == 1)//Firewall level
        vs_private->support_security |= 0x02;
    if(search_oam_branch(&g_st_branch_head, 0xC7, 0x2028) == 1)//Access control
    {
        vs_private->support_security |= 0x04;
        vs_private->support_security |= 0x08;//ping
        vs_private->support_security |= 0x10;//telnet
        if (onu_capabilites2.number_of_usb_port != 0) //no usb then no ftpd
        {
            vs_private->support_security |= 0x20;//ftp
        }
        vs_private->support_security |= 0x40;//http
        vs_private->support_security |= 0x80;//https
        //vs_private->support_security |= 0x100;//tftp
        vs_private->support_security |= 0x200;//ssh
    }

    /* support voip */
    if(search_oam_branch(&g_st_branch_head, 0x02, 0x0004) == 1)
        vs_private->support_voip |= 0x01;
    if(search_oam_branch(&g_st_branch_head, 0x02, 0x0007) == 1)
        vs_private->support_voip |= 0x02;
    if(search_oam_branch(&g_st_branch_head, 0x02, 0x0008) == 1)
        vs_private->support_voip |= 0x04;
    if(search_oam_branch(&g_st_branch_head, 0x02, 0x000c) == 1)
        vs_private->support_voip |= 0x08;
    if(search_oam_branch(&g_st_branch_head, 0x02, 0x000e) == 1)
        vs_private->support_voip |= 0x10;

    /* support port */
    if(search_oam_branch(&g_st_branch_head, 0xc7, 0x2101) == 1)
        vs_private->support_port |= 0x01;
    if(search_oam_branch(&g_st_branch_head, 0xc7, 0x2102) == 1)
        vs_private->support_port |= 0x02;
    if(search_oam_branch(&g_st_branch_head, 0xc7, 0x2106) == 1)
        vs_private->support_port |= 0x04;
    if(search_oam_branch(&g_st_branch_head, 0xc7, 0x2108) == 1)
        vs_private->support_port |= 0x08;
    if(search_oam_branch(&g_st_branch_head, 0xc7, 0x210c) == 1)
        vs_private->support_port |= 0x10;
    if(search_oam_branch(&g_st_branch_head, 0xc7, 0x210d) == 1)
        vs_private->support_port |= 0x20;
    if(search_oam_branch(&g_st_branch_head, 0xc7, 0x2009) == 1)
        vs_private->support_port |= 0x40;

	/* support control */
    if(search_oam_branch(&g_st_branch_head, 0xc7, 0x2020) == 1)
        vs_private->support_control |= 0x01;
    if(search_oam_branch(&g_st_branch_head, 0xc7, 0x2023) == 1)
        vs_private->support_control |= 0x02;
    if(search_oam_branch(&g_st_branch_head, 0xc7, 0x2024) == 1)
        vs_private->support_control |= 0x04;
    if(search_oam_branch(&g_st_branch_head, 0xc7, 0x2204) == 1)
        vs_private->support_control |= 0x08;

    /* support rstp */
    if(search_oam_branch(&g_st_branch_head, 0xc7, 0x200a) == 1)
        vs_private->support_rstp |= 0x01;
    if(search_oam_branch(&g_st_branch_head, 0xc7, 0x2011) == 1)
        vs_private->support_rstp |= 0x02;

    /* support application */
    if(search_oam_branch(&g_st_branch_head, 0xc7, 0x2021) == 1)//igmp
        vs_private->support_application |= 0x01;
    if(search_oam_branch(&g_st_branch_head, 0xc7, 0x202c) == 1)//UPnP
        vs_private->support_application |= 0x02;
    if(search_oam_branch(&g_st_branch_head, 0xc7, 0x202d) == 1)//HGU SFU device type switch
        vs_private->support_application |= 0x04;
    if(search_oam_branch(&g_st_branch_head, 0xc7, 0x2032) == 1)//MQTT
        vs_private->support_application |= 0x08;
    if(search_oam_branch(&g_st_branch_head, 0xc7, 0x2033) == 1)//GE number
        vs_private->support_application |= 0x10;

    /* support tr069 */
    if(search_oam_branch(&g_st_branch_head, 0xc7, 0x2029) == 1)//tr069 global
        vs_private->support_tr069 |= 0x01;
    if(search_oam_branch(&g_st_branch_head, 0xc7, 0x202a) == 1)//tr069 stun
        vs_private->support_tr069 |= 0x02;	

    /* support diagnosis */
    if(search_oam_branch(&g_st_branch_head, 0xc7, 0x2401) == 1)
        vs_private->support_onu_self_diagnosis |= 0x01;
    if(search_oam_branch(&g_st_branch_head, 0xc7, 0x2402) == 1)
        vs_private->support_onu_self_diagnosis |= 0x01;

    /* support system_time */
    if(search_oam_branch(&g_st_branch_head, 0xc7, 0x2031) == 1)
        vs_private->support_system_time |= 0x01;

    vs_private->support_wan = (hi_ushort16)htons(vs_private->support_wan);
    vs_private->support_wifi = (hi_ushort16)htons(vs_private->support_wifi);
    vs_private->support_dhcp = (hi_ushort16)htons(vs_private->support_dhcp);
    vs_private->support_security = (hi_ushort16)htons(vs_private->support_security);
    vs_private->support_voip = (hi_ushort16)htons(vs_private->support_voip);
    vs_private->support_port = (hi_ushort16)htons(vs_private->support_port);
    vs_private->support_control = (hi_ushort16)htons(vs_private->support_control);
    vs_private->support_rstp = (hi_ushort16)htons(vs_private->support_rstp);
    vs_private->support_application = (hi_ushort16)htons(vs_private->support_application);
    vs_private->support_tr069 = (hi_ushort16)htons(vs_private->support_tr069);
    vs_private->support_onu_self_diagnosis = (hi_ushort16)htons(vs_private->support_onu_self_diagnosis);
    vs_private->support_system_time = (hi_ushort16)htons(vs_private->support_system_time);

    return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

