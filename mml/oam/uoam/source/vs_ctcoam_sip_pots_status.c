#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "vs_ctcoam_sip_pots_status.h"


uint32_t vs_ctcoam_get_sip_pots_status(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    unsigned char i = 0, code_priority = 255;
    IgdVoiceLineCodecAttrConfTab data2;
    hi_uint32 ui_port = pst_instance->un_value.ui_value & 0xff;
    vs_ctcoam_sip_pots_status_s *cfg = NULL; 

    if (pv_outmsg == NULL)
        return HI_RET_INVALID_PARA;

    cfg = (vs_ctcoam_sip_pots_status_s *)pv_outmsg;

    cfg->port_status = igd_cm_voip_status(ui_port-1);

    /*Add by fyy for bug#16024*/
    if (cfg->port_status == IGD_CM_VS_CALL_EP_INACTIVED)
        cfg->port_status = 0x0A;
    else if (cfg->port_status == IGD_CM_VS_CALL_EP_REGISTER_FAIL)//for bug#16248
        cfg->port_status = 0x09;
    for(i = 1; i<= 4; i++)
    {
        HI_OS_MEMSET_S(&data2,sizeof(data2),0,sizeof(data2));
        data2.ulBitmap |= VOICE_LINE_CODEC_CAPBILITY_ATTR_MASK_BIT0_ENABLE|VOICE_LINE_CODEC_CAPBILITY_ATTR_MASK_BIT1_PRIORITY|VOICE_LINE_CODEC_CAPBILITY_ATTR_MASK_BIT2_TYPE|VOICE_LINE_CODEC_CAPBILITY_ATTR_MASK_BIT3_PACKETIZATION_PERIOD;
        data2.ucEntryID = i;

        ret = igdCmConfGet(IGD_VOICE_LINE_CODEC_ATTR_TAB,(unsigned char *)&data2,sizeof(data2));
        if(ret != 0)
        {
            printf("[%s,%d] igdCmConfGet IGD_VOICE_LINE_CODEC_ATTR_TAB ret : %d \r\n", __FUNCTION__, __LINE__, ret);
        }

        if(data2.ucEnable == 1)
        {
            if(code_priority > data2.ucPriority)//get the min number, the highest priority
            {
                code_priority = data2.ucPriority;
                if(data2.ucCodec == VOICE_LINE_CODEC_TYPE_G711A)//G.711A
                    cfg->code_mode = 0;
                else if(data2.ucCodec == VOICE_LINE_CODEC_TYPE_G711M)//G.711M is G.711U ?
                    cfg->code_mode = 2;
                else if(data2.ucCodec == VOICE_LINE_CODEC_TYPE_G729)//G.729
                    cfg->code_mode = 1;
                else if(data2.ucCodec == VOICE_LINE_CODEC_TYPE_G722)//G.722
                    cfg->code_mode = 6;//OLT has no G.722
            }
        }
    }

    cfg->service_state = 0x03;

    /* net address change */
    cfg->port_status = htonl(cfg->port_status);
    cfg->service_state = htonl(cfg->service_state);
    cfg->code_mode = htonl(cfg->code_mode);

    return HI_RET_SUCC;
}



#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

