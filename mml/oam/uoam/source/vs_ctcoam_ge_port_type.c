#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "vs_ctcoam_ge_port_type.h"
uint32_t hi_oam_uni_capability_get(vs_ctcoam_ge_port_type_s *onu_uni_porttype)
{
	int32_t ret = 0;
	hi_oam_onu_capabilites_s onu_capabilites;
	
	ret = hi_oam_get_capabilities1(&onu_capabilites);
    if (HI_RET_SUCC != ret)
		return HI_RET_FAIL;

	onu_uni_porttype->ge_1 = onu_capabilites.number_GE;
	onu_uni_porttype->ge_2_5 = 0;
	onu_uni_porttype->ge_5 = 0;
	onu_uni_porttype->ge_10 = 0;
	onu_uni_porttype->ge_25 = 0;
	onu_uni_porttype->ge_40 = 0;
	
     return HI_RET_SUCC;
   
}



uint32_t vs_ctcoam_get_ge_port_type(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
	int32_t ret = 0;
    vs_ctcoam_ge_port_type_s *vs_getype = NULL;
    
	vs_ctcoam_ge_port_type_s onu_uni_porttype = {0};
	
	if (pv_outmsg == NULL)
        return HI_RET_INVALID_PARA;
	vs_getype = (vs_ctcoam_ge_port_type_s *)pv_outmsg;
	
	ret = hi_oam_uni_capability_get(&onu_uni_porttype);
    if (HI_RET_SUCC != ret)
		return HI_RET_FAIL;
	

	vs_getype->ge_1 = onu_uni_porttype.ge_1;
	vs_getype->ge_2_5 = onu_uni_porttype.ge_2_5;
	vs_getype->ge_5 = onu_uni_porttype.ge_5;
	vs_getype->ge_10 = onu_uni_porttype.ge_10;
	vs_getype->ge_25 = onu_uni_porttype.ge_25;
	vs_getype->ge_40 = onu_uni_porttype.ge_40;
    return HI_RET_SUCC;
}



#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

