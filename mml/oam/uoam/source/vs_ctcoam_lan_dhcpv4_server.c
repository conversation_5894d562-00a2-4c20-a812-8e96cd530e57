#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus

#include "vs_ctcoam_lan_dhcpv4_server.h"
#include "hi_sml_wan.h"

void printf_oam_dhcpv4_data(vs_ctcoam_lan_dhcpv4_server_s *data)
{
    unsigned char i = 0, printf_enable = 0;
    if(printf_enable)
    {
        printf("===================== dhcpv4 date =====================\n");
        printf("itemNum = %d\n", data->itemNum);
        printf("ipType = %d\n", data->ipType);
        printf("lanIPaddress = ");
        for(i = 0; i < 16; i ++)
        {
            printf("0x%x,", data->lanIPaddress[i]);
        }printf("\n");
        printf("lanSubnetMask = ");
        for(i = 0; i < 4; i ++)
        {
            printf("0x%x,", data->lanSubnetMask[i]);
        }printf("\n");
        printf("leaseTime = %d\n", data->leaseTime);
        printf("enableDhcpServer = %d\n", data->enableDhcpServer);
        printf("dhcpPoolStart = ");
        for(i = 0; i < 16; i ++)
        {
            printf("0x%x,", data->dhcpPoolStart[i]);
        }printf("\n");
        printf("dhcpPoolEnd = ");
        for(i = 0; i < 16; i ++)
        {
            printf("0x%x,", data->dhcpPoolEnd[i]);
        }printf("\n");
        printf("dhcpPoolType = %d\n", data->dhcpPoolType);
        printf("dhcpPriDNS = ");
        for(i = 0; i < 16; i ++)
        {
            printf("0x%x,", data->dhcpPriDNS[i]);
        }printf("\n");
        printf("dhcpSecDNS = ");
        for(i = 0; i < 16; i ++)
        {
            printf("0x%x,", data->dhcpSecDNS[i]);
        }printf("\n");
        printf("dhcpGateway = ");
        for(i = 0; i < 16; i ++)
        {
            printf("0x%x,", data->dhcpGateway[i]);
        }printf("\n");
        printf("dhcpServerIPaddress = ");
        for(i = 0; i < 16; i ++)
        {
            printf("0x%x,", data->dhcpServerIPaddress[i]);
        }printf("\n");
        printf("==========================================\n");	
    }
}

int vs_oam_lan_dhcpv4_cfg_to_ram(vs_ctcoam_lan_dhcpv4_server_s *cfg)
{
    int ret = 0;
    hi_uint32 u32Val = 0;
    hi_char8 pri_dns[16] = {0}, sec_dns[16] = {0};
    IgdLanIPAddrAttrConfTab  ipaddrattrconf;
    IgdLanIPv4AttrConfTab    ipv4attrconf;

    if (cfg == NULL)
        return HI_RET_INVALID_PARA;
    
    HI_OS_MEMSET_S(cfg, sizeof(vs_ctcoam_lan_dhcpv4_server_s), 0, sizeof(vs_ctcoam_lan_dhcpv4_server_s));
    HI_OS_MEMSET_S(&ipaddrattrconf, sizeof(ipaddrattrconf),0,sizeof(ipaddrattrconf));
    HI_OS_MEMSET_S(&ipv4attrconf, sizeof(ipv4attrconf),0,sizeof(ipv4attrconf));

    ipaddrattrconf.ulBitmap |= LAN_IP_ADDR_ATTR_MASK_ALL;
#ifdef CONFIG_PLATFORM_OPENWRT
    IgdLanIPAddrAttrConfTab  *data = &ipaddrattrconf;
    ret = HI_IPC_CALL("hi_sml_lan_addr_attr_get", data);
#else
    ret = igdCmConfGet(IGD_LAN_IP_ADDR_TAB,(unsigned char *)&ipaddrattrconf,sizeof(ipaddrattrconf));
#endif
    if(ret != 0)
    {
        printf("igdCmConfGet IGD_LAN_IP_ADDR_TAB ret : %d \r\n", ret);
        return HI_RET_FAIL;
    }

    ipv4attrconf.ulBitmap |= LAN_IPV4_ATTR_MASK_ALL;
#ifdef CONFIG_PLATFORM_OPENWRT
    IgdLanIPv4AttrConfTab    *data1 = &ipv4attrconf;
    ret = HI_IPC_CALL("hi_sml_lan_ipv4_attr_get", data1);
#else
    ret = igdCmConfGet(IGD_LAN_IPV4_ATTR_TAB,(unsigned char *)&ipv4attrconf,sizeof(ipv4attrconf));
#endif
    if(ret != 0)
    {
        printf("igdCmConfGet IGD_LAN_IPV4_ATTR_TAB ret : %d \r\n", ret);
        return HI_RET_FAIL;
    }

    cfg->itemNum = 1;
    cfg->ipType = 0;//ipv4
    HI_OS_MEMCPY_S(cfg->lanIPaddress, CM_IP_ADDR_LEN, ipaddrattrconf.aucIPv4Addr, CM_IP_ADDR_LEN);//ip address
    HI_OS_MEMCPY_S(cfg->lanSubnetMask, CM_IP_ADDR_LEN, ipaddrattrconf.aucSubnetMask, CM_IP_ADDR_LEN);//ip subnet mask

    if(ipv4attrconf.ucDhcpServerEnable == 1)//dhcp server enable
    {
        cfg->enableDhcpServer = 1;
        cfg->dhcpPoolType = 0;//for PC
        cfg->leaseTime = (ipv4attrconf.lDhcpLeaseTime==-1)?4294967295:ipv4attrconf.lDhcpLeaseTime;
        HI_OS_MEMCPY_S(cfg->dhcpPoolStart, CM_IP_ADDR_LEN, ipv4attrconf.aucMinAddr, CM_IP_ADDR_LEN);//pool start
        HI_OS_MEMCPY_S(cfg->dhcpPoolEnd, CM_IP_ADDR_LEN, ipv4attrconf.aucMaxAddr, CM_IP_ADDR_LEN);//pool end

        if (ipv4attrconf.aucIpRouters[0] != '\0')
        {
            u32Val = igdCmApiChartoIp(ipv4attrconf.aucIpRouters);
            HI_OS_MEMCPY_S(cfg->dhcpGateway, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN); 
        }
        else
        {
            HI_OS_MEMCPY_S(cfg->dhcpGateway, CM_IP_ADDR_LEN, ipaddrattrconf.aucIPv4Addr, CM_IP_ADDR_LEN);
        } 

        if (ipv4attrconf.ucDnsSource == LAN_DNS_SOURCE_PROXY)
        {

        }
        else if (ipv4attrconf.ucDnsSource == LAN_DNS_SOURCE_WAN) 
        {

        }
        else if (ipv4attrconf.ucDnsSource == LAN_DNS_SOURCE_STATIC)
        {
            sscanf(ipv4attrconf.aucDnsServers, "%15[^,],%15s", pri_dns, sec_dns);
            u32Val = igdCmApiChartoIp(pri_dns);
            HI_OS_MEMCPY_S(cfg->dhcpPriDNS, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
            u32Val = igdCmApiChartoIp(sec_dns);
            HI_OS_MEMCPY_S(cfg->dhcpSecDNS, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);     
        }
    }
    else if(ipv4attrconf.ucDhcpServerEnable == 0)//dhcp server disable
    {
        cfg->enableDhcpServer = 0;
        if (ipv4attrconf.ucDhcpRelay) //enable dhcp relay
        {
            cfg->enableDhcpServer = 2;
            u32Val = igdCmApiChartoIp(ipv4attrconf.aucDhcpRelayAddr);
            HI_OS_MEMCPY_S(cfg->dhcpServerIPaddress, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
        }
    }

    return HI_RET_SUCC;
}

/*Add by fyy for bug#16276*/
hi_int32 vs_ctcoam_get_wan_dns(char *aucDnsWanName, hi_uchar8 *oam_pri_dns, hi_uchar8 *oam_sec_dns)
{
    hi_uchar8 i = 0;
    hi_int32 ret = 0;
    hi_uint32 listnum = 0;
    hi_uint32 u32Val;
    IgdWanConnectionAttrConfTab data;
    IgdWanConnectionStateInfoTab wanStateData;
    /* Bohannon, get the total wan num, and their wan conn id*/
    syswan_wan_listinfo_t list_info={0};

#if defined(CONFIG_PLATFORM_OPENWRT)
    HI_IPC_CALL("hi_sml_wan_connection_attr_tab_get_listnum", &list_info);
#else
    igdCmConfGetEntryNum(IGD_WAN_CONNECTION_ATTR_TAB, &listnum);
#endif
    /* Bohannon, get the total wan num, and their wan conn id*/
    listnum = list_info.list_num;
    if((0 < listnum) && (listnum <=8))
    {
        IgdWanConnectionIndexAttrTab wan_conn_indexobj[IGD_WAN_CONNECTION_RECORD_NUM];
        HI_OS_MEMSET_S(wan_conn_indexobj,sizeof(wan_conn_indexobj),0,sizeof(wan_conn_indexobj));

        ret = igdCmConfGetAllEntry(IGD_WAN_CONNECTION_INDEX_ATTR_TAB,(unsigned char *)wan_conn_indexobj,sizeof(wan_conn_indexobj));
        if(ret != 0)
        {
            printf("igdCmConfGetAllEntry IGD_WAN_CONNECTION_INDEX_ATTR_TAB ret : %d \r\n", ret);
            return HI_RET_FAIL;
        }

        for(i = 0; i < listnum; i++)
        {
            HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));
            data.ulBitmap |= WAN_CONNECTION_ATTR_MASK_ALL;
            data.ulBitmap1 |= WAN_CONNECTION_ATTR_MASK1_ALL;

            data.ucGlobalIndex = wan_conn_indexobj[i].ucGlobalIndex;
            data.ucConDevIndex = wan_conn_indexobj[i].ucConDevIndex;
            data.ucXPConIndex = wan_conn_indexobj[i].ucXPConIndex;
            HI_OS_STRCPY_S((char *)data.aucWanName, sizeof(data.aucWanName),wan_conn_indexobj[i].aucWanName);

#if defined(CONFIG_PLATFORM_OPENWRT)
            data.ucGlobalIndex = list_info.list_wanid[i];
            ret = HI_IPC_CALL("hi_sml_wan_connection_attr_tab_get", &data);
#else
            ret = igdCmConfGet(IGD_WAN_CONNECTION_ATTR_TAB, (unsigned char *)&data, sizeof(data));
#endif
            if(ret != 0)
            {
                printf("igdCmConfGet IGD_WAN_CONNECTION_ATTR_TAB ret : %d \n", ret);
                continue;
            }

            HI_OS_MEMSET_S(&wanStateData, sizeof(wanStateData), 0, sizeof(wanStateData));
            wanStateData.ucGlobalIndex = wan_conn_indexobj[i].ucGlobalIndex;
            HI_OS_STRCPY_S((char *)wanStateData.aucWanName, sizeof(wanStateData.aucWanName),wan_conn_indexobj[i].aucWanName);
            wanStateData.ulBitmap |= WAN_CONNECTION_STATE_MASK_ALL;
#if defined(CONFIG_PLATFORM_OPENWRT)
            wanStateData.ucGlobalIndex = list_info.list_wanid[i];
            ret = HI_IPC_CALL("hi_sml_wan_connection_state_tab_get", &wanStateData);
#else
            ret = igdCmConfGet(IGD_WAN_CONNECTION_STATE_TAB,(unsigned char *)&wanStateData,sizeof(wanStateData));
#endif
            if(ret != 0)
            {
                printf("igdCmConfGet IGD_WAN_CONNECTION_STATE_TAB ret : %d \n", ret);
                continue;
            }
            if (strcmp(data.aucWanName, aucDnsWanName) == 0) 
            {
                u32Val = igdCmApiChartoIp(wanStateData.aucIPv4DnsPrimary);
                HI_OS_MEMCPY_S(oam_pri_dns, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
                u32Val = igdCmApiChartoIp(wanStateData.aucIPv4DnsBackup);
                HI_OS_MEMCPY_S(oam_sec_dns, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
                break;
            }
        }
    }  
    return HI_RET_SUCC;  
}
/*End of Add*/

uint32_t vs_ctcoam_get_lan_dhcpv4_server(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    int bit_num = 0;
    unsigned int struct_size = 0;
    unsigned int temp = 0;
    unsigned int add_len = 0;
    unsigned char i = 0, j = 0;
    hi_uint32 u32Val = 0;
    hi_char8 pri_dns[16] = {0}, sec_dns[16] = {0};
    hi_ushort16 *pus_msglen = NULL;
    hi_ushort16 tlv_leaf = 0;
    hi_ctcoam_tlv_s *tlv_head = (hi_ctcoam_tlv_s *)(pv_inmsg - 4);
    vs_ctcoam_lan_dhcpv4_server_s *dhcpv4_cfg = NULL;
    IgdLanIPAddrAttrConfTab  ipaddrattrconf;
    IgdLanIPv4AttrConfTab    ipv4attrconf;

    if (pv_outmsg == NULL)
        return HI_RET_INVALID_PARA;
    
    dhcpv4_cfg = (vs_ctcoam_lan_dhcpv4_server_s *)pv_outmsg;

    HI_OS_MEMSET_S(&ipaddrattrconf, sizeof(ipaddrattrconf),0,sizeof(ipaddrattrconf));
    HI_OS_MEMSET_S(&ipv4attrconf, sizeof(ipv4attrconf),0,sizeof(ipv4attrconf));

    ipaddrattrconf.ulBitmap |= LAN_IP_ADDR_ATTR_MASK_ALL;
#ifdef CONFIG_PLATFORM_OPENWRT
    IgdLanIPAddrAttrConfTab  *data = &ipaddrattrconf;
    ret = HI_IPC_CALL("hi_sml_lan_addr_attr_get", data);
#else
    ret = igdCmConfGet(IGD_LAN_IP_ADDR_TAB,(unsigned char *)&ipaddrattrconf,sizeof(ipaddrattrconf));
#endif
    if(ret != 0)
    {
        printf("igdCmConfGet IGD_LAN_IP_ADDR_TAB ret : %d \r\n", ret);
        return HI_RET_FAIL;
    }

    ipv4attrconf.ulBitmap |= LAN_IPV4_ATTR_MASK_ALL;
#ifdef CONFIG_PLATFORM_OPENWRT
    IgdLanIPv4AttrConfTab    *data1 = &ipv4attrconf;
    ret = HI_IPC_CALL("hi_sml_lan_ipv4_attr_get", data1);
#else
    ret = igdCmConfGet(IGD_LAN_IPV4_ATTR_TAB,(unsigned char *)&ipv4attrconf,sizeof(ipv4attrconf));
#endif
    if(ret != 0)
    {
        printf("igdCmConfGet IGD_LAN_IPV4_ATTR_TAB ret : %d \r\n", ret);
        return HI_RET_FAIL;
    }

    dhcpv4_cfg->ipType = 0;//ipv4
    HI_OS_MEMCPY_S(dhcpv4_cfg->lanIPaddress, CM_IP_ADDR_LEN, ipaddrattrconf.aucIPv4Addr, CM_IP_ADDR_LEN);//ip address
    HI_OS_MEMCPY_S(dhcpv4_cfg->lanSubnetMask, CM_IP_ADDR_LEN, ipaddrattrconf.aucSubnetMask, CM_IP_ADDR_LEN);//ip subnet mask

    for(i = 0; i < CM_IP_ADDR_LEN; i++)
    {
        for(j = 0; j < 8; j++)      
        {           
            if(dhcpv4_cfg->lanSubnetMask[i] & (1<<j))              
            bit_num ++;       
        }
    }
	HI_OS_MEMSET_S(dhcpv4_cfg->lanSubnetMask, sizeof(dhcpv4_cfg->lanSubnetMask), 0, sizeof(dhcpv4_cfg->lanSubnetMask));
    dhcpv4_cfg->lanSubnetMask[0] = bit_num;

    temp |= dhcpv4_cfg->lanSubnetMask[0] << 24;
    temp |= dhcpv4_cfg->lanSubnetMask[1] << 16;
    temp |= dhcpv4_cfg->lanSubnetMask[2] << 8;
    temp |= dhcpv4_cfg->lanSubnetMask[3];
    temp = htonl(temp);

    dhcpv4_cfg->lanSubnetMask[0] = (temp >> 24) & 0xFF;
    dhcpv4_cfg->lanSubnetMask[1] = (temp >> 16) & 0xFF;
    dhcpv4_cfg->lanSubnetMask[2] = (temp >> 8) & 0xFF;
    dhcpv4_cfg->lanSubnetMask[3] = temp & 0xFF;

    if(ipv4attrconf.ucDhcpServerEnable == 1)//dhcp server enable
    {
        dhcpv4_cfg->itemNum = 1;
        dhcpv4_cfg->itemNum = htonl(dhcpv4_cfg->itemNum);
        dhcpv4_cfg->enableDhcpServer = 1;
        dhcpv4_cfg->enableDhcpServer = (hi_ushort16)htons(dhcpv4_cfg->enableDhcpServer);
        dhcpv4_cfg->dhcpPoolType = 0;//for PC
        dhcpv4_cfg->leaseTime = (ipv4attrconf.lDhcpLeaseTime==-1)?4294967295:ipv4attrconf.lDhcpLeaseTime;
        dhcpv4_cfg->leaseTime = htonl(dhcpv4_cfg->leaseTime);
        HI_OS_MEMCPY_S(dhcpv4_cfg->dhcpPoolStart, CM_IP_ADDR_LEN, ipv4attrconf.aucMinAddr, CM_IP_ADDR_LEN);//pool start
        HI_OS_MEMCPY_S(dhcpv4_cfg->dhcpPoolEnd, CM_IP_ADDR_LEN, ipv4attrconf.aucMaxAddr, CM_IP_ADDR_LEN);//pool end

        if (ipv4attrconf.aucIpRouters[0] != '\0')
        {
            u32Val = igdCmApiChartoIp(ipv4attrconf.aucIpRouters);
            HI_OS_MEMCPY_S(dhcpv4_cfg->dhcpGateway, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN); 
        }
        else
        {
            HI_OS_MEMCPY_S(dhcpv4_cfg->dhcpGateway, CM_IP_ADDR_LEN, ipaddrattrconf.aucIPv4Addr, CM_IP_ADDR_LEN);
        } 

        if (ipv4attrconf.ucDnsSource == LAN_DNS_SOURCE_PROXY)
        {
            /*for bug#16276*/
            HI_OS_MEMCPY_S(dhcpv4_cfg->dhcpPriDNS, CM_IP_ADDR_LEN, dhcpv4_cfg->lanIPaddress, CM_IP_ADDR_LEN);
        }
        else if (ipv4attrconf.ucDnsSource == LAN_DNS_SOURCE_WAN) 
        {
            /*for bug#16276*/
            vs_ctcoam_get_wan_dns(ipv4attrconf.aucDnsWanName, dhcpv4_cfg->dhcpPriDNS, dhcpv4_cfg->dhcpSecDNS);
        }
        else if (ipv4attrconf.ucDnsSource == LAN_DNS_SOURCE_STATIC)
        {
            sscanf(ipv4attrconf.aucDnsServers, "%15[^,],%15s", pri_dns, sec_dns);
            u32Val = igdCmApiChartoIp(pri_dns);
            HI_OS_MEMCPY_S(dhcpv4_cfg->dhcpPriDNS, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
            u32Val = igdCmApiChartoIp(sec_dns);
            HI_OS_MEMCPY_S(dhcpv4_cfg->dhcpSecDNS, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);     
        }
    }
    else if(ipv4attrconf.ucDhcpServerEnable == 0)//dhcp server disable
    {
        dhcpv4_cfg->enableDhcpServer = 0;
        dhcpv4_cfg->enableDhcpServer = (hi_ushort16)htons(dhcpv4_cfg->enableDhcpServer);
        dhcpv4_cfg->itemNum = 0;
        dhcpv4_cfg->itemNum = htonl(dhcpv4_cfg->itemNum);
        if (ipv4attrconf.ucDhcpRelay) //enable dhcp relay
        {
            dhcpv4_cfg->itemNum = 1;
            dhcpv4_cfg->itemNum = htonl(dhcpv4_cfg->itemNum);
            dhcpv4_cfg->enableDhcpServer = 2;
            dhcpv4_cfg->enableDhcpServer = (hi_ushort16)htons(dhcpv4_cfg->enableDhcpServer);
            u32Val = igdCmApiChartoIp(ipv4attrconf.aucDhcpRelayAddr);
            HI_OS_MEMCPY_S(dhcpv4_cfg->dhcpServerIPaddress, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
        }
    }
    
    /* slicing the packets and add tlv header to the data */
    tlv_leaf = tlv_head->us_leaf;
    tlv_leaf = (hi_ushort16)htons(tlv_leaf);
    struct_size = sizeof(vs_ctcoam_lan_dhcpv4_server_s);
    if(struct_size > 0 && struct_size <= 128)
    {
        *puc_changingmsglen = struct_size;
        HI_OS_MEMCPY_S(pv_outmsg, sizeof(vs_ctcoam_lan_dhcpv4_server_s), dhcpv4_cfg, *puc_changingmsglen);
    }
    else if(struct_size > 128 && struct_size <= (255 - 4))//langer than 128, need to slicing the packets
    {
        add_len = packet_slicing_for_send(dhcpv4_cfg, sizeof(vs_ctcoam_lan_dhcpv4_server_s), tlv_head->uc_branch, tlv_leaf);
        add_len = (add_len - 1) * 4;
        pus_msglen = (hi_ushort16 *)(puc_changingmsglen + 1);
        *pus_msglen = 0;
        *puc_changingmsglen = struct_size + add_len;//send msg will use this len value
    }
    else if(struct_size > (255 - 4) && struct_size <= 1496)
    {
        add_len = packet_slicing_for_send(dhcpv4_cfg, sizeof(vs_ctcoam_lan_dhcpv4_server_s), tlv_head->uc_branch, tlv_leaf);
        add_len = (add_len - 1) * 4;
        pus_msglen = (hi_ushort16 *)(puc_changingmsglen + 1);
        *pus_msglen = struct_size + add_len;//send msg will use this len value
        *puc_changingmsglen = 0;
    }
    else
    {
        return HI_RET_FAIL;
    }

    return HI_RET_SUCC;
}

uint32_t vs_ctcoam_set_lan_dhcpv4_server(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    unsigned int mask = 0;
    unsigned char cfg_change = 0;
    vs_ctcoam_lan_dhcpv4_server_s dhcpv4_cfg_data;
    vs_ctcoam_lan_dhcpv4_server_s *dhcpv4_cfg = &dhcpv4_cfg_data;
    vs_ctcoam_lan_dhcpv4_server_s ram_cfg;
    IgdLanIPAddrAttrConfTab ipaddrattrconf;
    IgdLanIPv4AttrConfTab ipv4attrconf;
    hi_char8 pri_dns[16] = {0};
    hi_char8 sec_dns[16] = {0};

    if (pv_inmsg == NULL)
        return HI_RET_INVALID_PARA;

    packet_slicing_for_receive(&dhcpv4_cfg_data, sizeof(vs_ctcoam_lan_dhcpv4_server_s), pv_inmsg, 1496, 0xc7, 0x2207);

    dhcpv4_cfg->itemNum = ntohl(dhcpv4_cfg->itemNum);
    dhcpv4_cfg->ipType = (hi_ushort16)ntohs(dhcpv4_cfg->ipType);
    mask = 0xFFFFFFFF << (32 - dhcpv4_cfg->lanSubnetMask[3]);
    dhcpv4_cfg->lanSubnetMask[0] = (mask >> 24) & 0xFF;
    dhcpv4_cfg->lanSubnetMask[1] = (mask >> 16) & 0xFF;
    dhcpv4_cfg->lanSubnetMask[2] = (mask >> 8) & 0xFF;
    dhcpv4_cfg->lanSubnetMask[3] = mask & 0xFF;
    dhcpv4_cfg->enableDhcpServer = (hi_ushort16)ntohs(dhcpv4_cfg->enableDhcpServer);
    dhcpv4_cfg->dhcpPoolType = (hi_ushort16)ntohs(dhcpv4_cfg->dhcpPoolType);
    dhcpv4_cfg->leaseTime = ntohl(dhcpv4_cfg->leaseTime);

    if (dhcpv4_cfg->ipType != 0)//not ipv4
    {
        printf("It is not an ipv4 parameter\n");
        return HI_RET_INVALID_PARA;
    }

    /* compare cfg */
    vs_oam_lan_dhcpv4_cfg_to_ram(&ram_cfg);
    if(memcmp(&ram_cfg, dhcpv4_cfg, sizeof(vs_ctcoam_lan_dhcpv4_server_s)) != 0)
        cfg_change = 1;
    
    /* if cfg change, set to mib */
    if(cfg_change == 1)
    {
        printf_oam_dhcpv4_data(&ram_cfg);
        printf_oam_dhcpv4_data(dhcpv4_cfg);

        HI_OS_MEMSET_S(&ipaddrattrconf,sizeof(ipaddrattrconf),0,sizeof(ipaddrattrconf));
        HI_OS_MEMSET_S(&ipv4attrconf,sizeof(ipv4attrconf),0,sizeof(ipv4attrconf));
        HI_OS_MEMCPY_S((void *)ipaddrattrconf.aucIPv4Addr, CM_IP_ADDR_LEN, (void *)dhcpv4_cfg->lanIPaddress, CM_IP_ADDR_LEN);
        HI_OS_MEMCPY_S((void *)ipaddrattrconf.aucSubnetMask, CM_IP_ADDR_LEN, (void *)&dhcpv4_cfg->lanSubnetMask, CM_IP_ADDR_LEN);
        
        ipaddrattrconf.ulBitmap  |= LAN_IP_ADDR_ATTR_MASK_BIT2_IP_ADDRESS|LAN_IP_ADDR_ATTR_MASK_BIT3_SUBNET_MASK;
#ifdef CONFIG_PLATFORM_OPENWRT
        IgdLanIPAddrAttrConfTab  *data = &ipaddrattrconf;
        ret = HI_IPC_CALL("hi_sml_lan_addr_attr_set",data);
#else
        ret = igdCmConfSet(IGD_LAN_IP_ADDR_TAB, (unsigned char *)&ipaddrattrconf, sizeof(ipaddrattrconf));
#endif
        if(ret != 0)
        {
            printf("igdCmConfSet IGD_LAN_IP_ADDR_TAB ret : %d \r\n", ret);
            return HI_RET_FAIL;
        }

        if (dhcpv4_cfg->enableDhcpServer == 1)//dhcp server enable
        {
            ipv4attrconf.ulBitmap |= LAN_IPV4_ATTR_MASK_BIT2_DHCP_RELAY|LAN_IPV4_ATTR_MASK_BIT3_DHCP_SERVER_ENABLE|LAN_IPV4_ATTR_MASK_BIT4_IPPOOL_ENABLE\
                    |LAN_IPV4_ATTR_MASK_BIT9_DHCP_LEASE_TIME|LAN_IPV4_ATTR_MASK_BIT10_MIN_ADDR|LAN_IPV4_ATTR_MASK_BIT11_MAX_ADDR\
                    |LAN_IPV4_ATTR_MASK_BIT22_DNS_SERVERS|LAN_IPV4_ATTR_MASK_BIT24_IP_ROUTERS|LAN_IPV4_ATTR_MASK_BIT13_SUB_MASK;
            //ipv4attrconf.ulBitmap1 |= LAN_IPV4_ATTR_MASK1_BIT2_DHCP_RELAY_ADDR|LAN_IPV4_ATTR_MASK1_BIT0_DNS_SOURCE;
            ipv4attrconf.ulBitmap1 |= LAN_IPV4_ATTR_MASK1_ALL;

            ipv4attrconf.ucDhcpRelay = 0;
            ipv4attrconf.ucDhcpServerEnable = 1;
            ipv4attrconf.ucIpPoolEnable  = 0;//for bug#16259, dhcpv4_cfg->dhcpPoolType;//0 for PC
            ipv4attrconf.lDhcpLeaseTime  = dhcpv4_cfg->leaseTime;
            HI_OS_MEMCPY_S((void *)ipv4attrconf.aucMinAddr, CM_IP_ADDR_LEN, (void *)dhcpv4_cfg->dhcpPoolStart,CM_IP_ADDR_LEN);
            HI_OS_MEMCPY_S((void *)ipv4attrconf.aucMaxAddr, CM_IP_ADDR_LEN, (void *)dhcpv4_cfg->dhcpPoolEnd,CM_IP_ADDR_LEN);
            HI_OS_MEMCPY_S((void *)ipv4attrconf.aucSubMask, CM_IP_ADDR_LEN, (void *)dhcpv4_cfg->lanSubnetMask, CM_IP_ADDR_LEN);//private oam doesn't has this param, use the lanSubnetMask

            //vsol private oam only has static mode
            ipv4attrconf.ucDnsSource = LAN_DNS_SOURCE_STATIC;
            igdCmApiIptoChar(dhcpv4_cfg->dhcpPriDNS, pri_dns, sizeof(pri_dns));
            igdCmApiIptoChar(dhcpv4_cfg->dhcpSecDNS, sec_dns, sizeof(sec_dns));
            snprintf(ipv4attrconf.aucDnsServers, sizeof(ipv4attrconf.aucDnsServers), "%s,%s", pri_dns, sec_dns);
            igdCmApiIptoChar(dhcpv4_cfg->dhcpGateway, ipv4attrconf.aucIpRouters, sizeof(ipv4attrconf.aucIpRouters));
        }
        else if (dhcpv4_cfg->enableDhcpServer == 0)//dhcp server disable 
        {
            ipv4attrconf.ulBitmap |= LAN_IPV4_ATTR_MASK_BIT3_DHCP_SERVER_ENABLE|LAN_IPV4_ATTR_MASK_BIT2_DHCP_RELAY\
                        |LAN_IPV4_ATTR_MASK_BIT4_IPPOOL_ENABLE;
            //ipv4attrconf.ulBitmap1 |= LAN_IPV4_ATTR_MASK1_BIT2_DHCP_RELAY_ADDR;
            ipv4attrconf.ulBitmap1 |= LAN_IPV4_ATTR_MASK1_ALL;
            ipv4attrconf.ucDhcpServerEnable = 0;
            ipv4attrconf.ucDhcpRelay = 0;
        }
        else if (dhcpv4_cfg->enableDhcpServer == 2) //dhcp relay enable 
        {
            ipv4attrconf.ulBitmap |= LAN_IPV4_ATTR_MASK_BIT4_IPPOOL_ENABLE|LAN_IPV4_ATTR_MASK_BIT3_DHCP_SERVER_ENABLE\
                        |LAN_IPV4_ATTR_MASK_BIT2_DHCP_RELAY;
            //ipv4attrconf.ulBitmap1 |= LAN_IPV4_ATTR_MASK1_BIT2_DHCP_RELAY_ADDR;
            ipv4attrconf.ulBitmap1 |= LAN_IPV4_ATTR_MASK1_ALL;
            ipv4attrconf.ucDhcpRelay = 1;
            ipv4attrconf.ucDhcpServerEnable = 0;
            igdCmApiIptoChar(dhcpv4_cfg->dhcpServerIPaddress, ipv4attrconf.aucDhcpRelayAddr, sizeof(ipv4attrconf.aucDhcpRelayAddr));
        }

#ifdef CONFIG_PLATFORM_OPENWRT
        IgdLanIPv4AttrConfTab  *data1 = &ipv4attrconf;
        ret = HI_IPC_CALL("hi_sml_lan_ipv4_attr_set", data1);
#else
        ret = igdCmConfSet(IGD_LAN_IPV4_ATTR_TAB,(unsigned char *)&ipv4attrconf,sizeof(ipv4attrconf));
#endif
        if(ret != 0)
        {
            printf("igdCmConfSet IGD_LAN_IPV4_ATTR_TAB ret : %d \r\n", ret);
            return HI_RET_FAIL;
        }
    }

    return HI_RET_SUCC;
}



#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

