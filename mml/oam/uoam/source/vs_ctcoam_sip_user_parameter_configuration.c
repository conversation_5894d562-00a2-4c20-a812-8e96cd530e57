#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "vs_ctcoam_sip_user_parameter_configuration.h"


void change_data_format(unsigned char *data, int max_len, unsigned char dir)
{
    int i = 0;
    int actual_len = 0;
    int move_len = 0;
    int length = 0;
    unsigned char temp[1024];

    for(i = 0; i < max_len; i++)//calculate char length
    {
        if(data[i] != 0) 
        {
            length++;
            if(length > actual_len) 
                actual_len = length;
        } 
        else
        {
            length = 0;
        }
    }
    move_len = max_len - actual_len;

    if(move_len <= 0) 
        return;
    
    if(dir == 0)//move to back
    {
        for(i = 0; i < actual_len; i++)
            temp[i] = data[i];
            
        for(i = 0; i < actual_len; i++)
            data[i + move_len] = temp[i];

        for(i = 0; i < move_len; i++)
            data[i] = 0;
    }
    else if(dir == 1)//move to front
    {
        for(i = 0; i < actual_len; i++)
            temp[i] = data[move_len + i];
            
        for(i = 0; i < actual_len; i++)
            data[i] = temp[i];

        for(i = 0; i < move_len; i++)
            data[i + actual_len] = 0;        
    }
}

int vs_oam_sip_user_param_cfg_to_ram(vs_ctcoam_sip_user_parameter_configuration_s *cfg, unsigned int ui_port)
{
    int ret = 0;
    IgdVoiceSipUserAttrConfTab data;

    if (cfg == NULL)
        return HI_RET_INVALID_PARA;
    
    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));
    data.ulBitmap |= SIP_USER_ATTR_MASK_BIT1_AUTH_USERNAME|SIP_USER_ATTR_MASK_BIT2_AUTH_PASSWORD|SIP_USER_ATTR_MASK_BIT3_NUMBER_URL;
    data.ucLineNum = ui_port;
        
    ret = igdCmConfGet(IGD_VOICE_SIP_USER_ATTR_TAB, (unsigned char *)&data, sizeof(data));
    if(ret != 0)
    {
        printf("[%s,%d] igdCmConfGet IGD_VOICE_SIP_USER_ATTR_TAB ret : %d \r\n", __FUNCTION__, __LINE__, ret);
        return HI_RET_FAIL;
    }

    HI_OS_MEMCPY_S(cfg->number, sizeof(cfg->number), data.aucNumberUrl, sizeof(cfg->number));
    HI_OS_MEMCPY_S(cfg->auth_username, sizeof(cfg->auth_username), data.aucAuthUsername, sizeof(cfg->auth_username));
    HI_OS_MEMCPY_S(cfg->password, sizeof(cfg->password), data.aucAuthPassword, sizeof(cfg->password));

    return HI_RET_SUCC;
}

uint32_t vs_ctcoam_get_sip_user_parameter_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    hi_uint32 ui_port = pst_instance->un_value.ui_value & 0xff;\
    int max_data_len = 0;
    unsigned char number[16];
    unsigned char auth_username[32];
    unsigned char password[16];
    IgdVoiceSipUserAttrConfTab data;
    vs_ctcoam_sip_user_parameter_configuration_s *cfg = NULL;

    if (pv_outmsg == NULL)
        return HI_RET_INVALID_PARA;

    cfg = (vs_ctcoam_sip_user_parameter_configuration_s *)pv_outmsg;

    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));
    data.ulBitmap |= SIP_USER_ATTR_MASK_BIT1_AUTH_USERNAME|SIP_USER_ATTR_MASK_BIT2_AUTH_PASSWORD|SIP_USER_ATTR_MASK_BIT3_NUMBER_URL;
    data.ucLineNum = ui_port;
        
    ret = igdCmConfGet(IGD_VOICE_SIP_USER_ATTR_TAB, (unsigned char *)&data, sizeof(data));
    if(ret != 0)
    {
        printf("[%s,%d] igdCmConfGet IGD_VOICE_SIP_USER_ATTR_TAB ret : %d \r\n", __FUNCTION__, __LINE__, ret);
        return HI_RET_FAIL;
    }

    HI_OS_MEMCPY_S(number, sizeof(number), data.aucNumberUrl, sizeof(number));
    max_data_len = sizeof(number);
    change_data_format(number, max_data_len, 0);
    HI_OS_MEMCPY_S(cfg->number, sizeof(cfg->number), number, sizeof(number));

    HI_OS_MEMCPY_S(auth_username, sizeof(auth_username), data.aucAuthUsername, sizeof(auth_username));
    max_data_len = sizeof(auth_username);
    change_data_format(auth_username, max_data_len, 0);
    HI_OS_MEMCPY_S(cfg->auth_username, sizeof(cfg->auth_username), auth_username, sizeof(auth_username));

    HI_OS_MEMCPY_S(password, sizeof(password), data.aucAuthPassword, sizeof(password));
    max_data_len = sizeof(password);
    change_data_format(password, max_data_len, 0);
    HI_OS_MEMCPY_S(cfg->password, sizeof(cfg->password), password, sizeof(password));

    return HI_RET_SUCC;
}

uint32_t vs_ctcoam_set_sip_user_parameter_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    int max_data_len = 0;
    unsigned char cfg_change = 0;
    unsigned char number[16];
    unsigned char auth_username[32];
    unsigned char password[16];
    hi_uint32 ui_port = pst_instance->un_value.ui_value & 0xff;
    IgdVoiceSipUserAttrConfTab data;
    vs_ctcoam_sip_user_parameter_configuration_s ram_cfg;
    vs_ctcoam_sip_user_parameter_configuration_s *cfg = NULL;

    if (pv_inmsg == NULL)
        return HI_RET_INVALID_PARA;

    cfg = (vs_ctcoam_sip_user_parameter_configuration_s *)pv_inmsg;
    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));

    HI_OS_MEMCPY_S(number, sizeof(number), cfg->number, sizeof(cfg->number));
    max_data_len = sizeof(number);
    change_data_format(number, max_data_len, 1);
    HI_OS_MEMCPY_S(cfg->number, sizeof(cfg->number), number, sizeof(number));

    HI_OS_MEMCPY_S(auth_username, sizeof(auth_username), cfg->auth_username, sizeof(cfg->auth_username));
    max_data_len = sizeof(auth_username);
    change_data_format(auth_username, max_data_len, 1);
    HI_OS_MEMCPY_S(cfg->auth_username, sizeof(cfg->auth_username), auth_username, sizeof(auth_username));

    HI_OS_MEMCPY_S(password, sizeof(password), cfg->password, sizeof(cfg->password));
    max_data_len = sizeof(password);
    change_data_format(password, max_data_len, 1);
    HI_OS_MEMCPY_S(cfg->password, sizeof(cfg->password), password, sizeof(password));

    /* compare cfg */
    vs_oam_sip_user_param_cfg_to_ram(&ram_cfg, ui_port);
    if(memcmp(&ram_cfg, cfg, sizeof(vs_ctcoam_sip_user_parameter_configuration_s)) != 0)
        cfg_change = 1;

    /* if cfg change, set to mib */
    if(cfg_change == 1)
    {
        printf("ready to apply sip user param\n");
        HI_OS_MEMCPY_S(data.aucNumberUrl, sizeof(data.aucNumberUrl), cfg->number, sizeof(cfg->number));
        data.aucNumberUrl[sizeof(data.aucNumberUrl)-1] = '\0';
        HI_OS_MEMCPY_S(data.aucAuthUsername, sizeof(data.aucAuthUsername), cfg->auth_username, sizeof(cfg->auth_username));
        data.aucAuthUsername[sizeof(data.aucAuthUsername)-1] = '\0';
        HI_OS_MEMCPY_S(data.aucAuthPassword, sizeof(data.aucAuthPassword), cfg->password, sizeof(cfg->password));
        data.aucAuthPassword[sizeof(data.aucAuthPassword)-1] = '\0';

        data.ulBitmap |= SIP_USER_ATTR_MASK_BIT1_AUTH_USERNAME|SIP_USER_ATTR_MASK_BIT2_AUTH_PASSWORD|SIP_USER_ATTR_MASK_BIT3_NUMBER_URL;
        data.ucLineNum = ui_port;
        ret = igdCmConfSet(IGD_VOICE_SIP_USER_ATTR_TAB, (unsigned char *)&data, sizeof(data));
        if(ret != 0)
        {
            printf("[%s,%d] igdCmConfSet IGD_VOICE_SIP_USER_ATTR_TAB ret : %d \r\n", __FUNCTION__, __LINE__, ret);
            return HI_RET_FAIL;
        }
    }

    return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

