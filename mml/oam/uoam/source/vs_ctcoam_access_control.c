#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "vs_ctcoam_access_control.h"

static vs_ctcoam_access_control_state_s access_control_state;

uint32_t vs_ctcoam_get_access_control(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    hi_uint32 u32Val_ip = 0, u32Val_mask = 0;
    unsigned int add_len = 0;
    unsigned int struct_size = 0;
    hi_ushort16 *pus_msglen = NULL;
    hi_ushort16 tlv_leaf = 0;
    hi_ctcoam_tlv_s *tlv_head = (hi_ctcoam_tlv_s *)(pv_inmsg - 4);
    vs_ctcoam_access_control_s *access_cfg = NULL;
    IgdAppServiceManageAttrConfTab data;

    if (pv_outmsg == NULL)
		return HI_RET_INVALID_PARA;
    
    access_cfg = (vs_ctcoam_access_control_s *)pv_outmsg;

    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));
    data.ulBitmap |= SERVICE_MANAGE_ATTR_MASK_ALL;
    data.ulBitmap1 |= SERVICE_MANAGE_ATTR_MASK1_ALL;
    //ret = igdCmConfGet(IGD_APP_SERVICE_MANAGE_ATTR_TAB,(unsigned char *)&data,sizeof(data));
    ret = HI_IPC_CALL("hi_sml_access_control_attr_get", &data);
    if(ret != 0)
    {
        printf("hi_sml_access_control_attr_get fail, ret : %d \r\n", ret);
        return HI_RET_FAIL;
    }

    //telnet
    access_cfg->telnet_control = access_control_state.telnet;
    access_cfg->telnet_lan = data.ucTelnetEnable;
    access_cfg->telnet_wan = data.ucTelnetWANEnable;
    access_cfg->telnet_port = data.ulTelnetWANPort;
    access_cfg->telnet_port = (hi_ushort16)htons(access_cfg->telnet_port);
    if(access_cfg->telnet_wan)
    {
        //ipv4
        u32Val_ip = igdCmApiChartoIp(data.acTelnetWanIpv4Addr);
        HI_OS_MEMCPY_S((void *)access_cfg->telnet_wan_ipv4_address, 4, &u32Val_ip, 4);
        //printf("return u32Val_ip=0x%x, access_cfg->telnet_wan_ipv4_address = %s\n", u32Val_ip, access_cfg->telnet_wan_ipv4_address);
        u32Val_mask = igdCmApiChartoIp(data.acTelnetWanIpv4Mask);
        HI_OS_MEMCPY_S((void *)access_cfg->telnet_wan_ipv4_mask, 4, &u32Val_mask, 4);
        //printf("return u32Val=0x%x, access_cfg->telnet_wan_ipv4_mask = %s\n", u32Val_mask, access_cfg->telnet_wan_ipv4_mask);
        if (u32Val_ip)
        {
            access_cfg->telnet_wan_ipv4_control = 1;
        }
    }

    //ftp
    access_cfg->ftp_control = access_control_state.ftp;
    access_cfg->ftp_lan = (data.ucFtpEnable & 0x01)?1:0;
    access_cfg->ftp_wan = (data.ucFtpEnable & 0x02)?1:0;
    access_cfg->ftp_port = data.ulFtpPort;
    access_cfg->ftp_port = (hi_ushort16)htons(access_cfg->ftp_port);
    if(access_cfg->ftp_wan)
    {
        //ipv4
        u32Val_ip = igdCmApiChartoIp(data.acFtpWanIpv4Addr);
        HI_OS_MEMCPY_S((void *)access_cfg->ftp_wan_ipv4_address, 4, &u32Val_ip, 4);
        //printf("return u32Val_ip=0x%x, access_cfg->ftp_wan_ipv4_address = %s\n", u32Val_ip, access_cfg->ftp_wan_ipv4_address);
        u32Val_mask = igdCmApiChartoIp(data.acFtpWanIpv4Mask);
        HI_OS_MEMCPY_S((void *)access_cfg->ftp_wan_ipv4_mask, 4, &u32Val_mask, 4);
        //printf("return u32Val_mask=0x%x, access_cfg->ftp_wan_ipv4_mask = %s\n", u32Val_mask, access_cfg->ftp_wan_ipv4_mask);
        if (u32Val_ip)
        {
            access_cfg->ftp_wan_ipv4_control = 1;
        }
    }

    //http
    access_cfg->http_control = access_control_state.http;
    access_cfg->http_lan = (data.ulHTTPEnable & 0x01)?1:0;
    access_cfg->http_wan = (data.ulHTTPEnable & 0x02)?1:0;
    access_cfg->http_port = data.ulHttpPort;
    access_cfg->http_port = (hi_ushort16)htons(access_cfg->http_port);
    if(access_cfg->http_wan)
    {
        //ipv4
        u32Val_ip = igdCmApiChartoIp(data.acHttpWanIpv4Addr);
        HI_OS_MEMCPY_S((void *)access_cfg->http_wan_ipv4_address, 4, &u32Val_ip, 4);
        //printf("return u32Val_ip=0x%x, access_cfg->http_wan_ipv4_address = %s\n", u32Val_ip, access_cfg->http_wan_ipv4_address);
        u32Val_mask = igdCmApiChartoIp(data.acHttpWanIpv4Mask);
        HI_OS_MEMCPY_S((void *)access_cfg->http_wan_ipv4_mask, 4, &u32Val_mask, 4);
        //printf("return u32Val_mask=0x%x, access_cfg->http_wan_ipv4_mask = %s\n", u32Val_mask, access_cfg->http_wan_ipv4_mask);
        if (u32Val_ip)
        {
            access_cfg->http_wan_ipv4_control = 1;
        }
    }

    //https
    access_cfg->https_control = access_control_state.https;
    access_cfg->https_lan = (data.ucHttpsEnable & 0x01)?1:0;
    access_cfg->https_wan = (data.ucHttpsEnable & 0x02)?1:0;
    access_cfg->https_port = data.ulHttpsPort;
    access_cfg->https_port = (hi_ushort16)htons(access_cfg->https_port);
    if(access_cfg->https_wan)
    {
        //ipv4
        u32Val_ip = igdCmApiChartoIp(data.acHttpsWanIpv4Addr);
        HI_OS_MEMCPY_S((void *)access_cfg->https_wan_ipv4_address, 4, &u32Val_ip, 4);
        //printf("return u32Val_ip=0x%x, access_cfg->https_wan_ipv4_address = %s\n", u32Val_ip, access_cfg->https_wan_ipv4_address);
        u32Val_mask = igdCmApiChartoIp(data.acHttpsWanIpv4Mask);
        HI_OS_MEMCPY_S((void *)access_cfg->https_wan_ipv4_mask, 4, &u32Val_mask, 4);
        //printf("return u32Val_mask=0x%x, access_cfg->https_wan_ipv4_mask = %s\n", u32Val_mask, access_cfg->https_wan_ipv4_mask);
        if (u32Val_ip)
        {
            access_cfg->https_wan_ipv4_control = 1;
        }
    }

    //tftp
    access_cfg->tftp_control = access_control_state.tftp;
    access_cfg->tftp_lan = (data.ucTftpEnable & 0x01)?1:0;
    access_cfg->tftp_wan = (data.ucTftpEnable & 0x02)?1:0;
    access_cfg->tftp_port = data.ulTftpPort;
    access_cfg->tftp_port = (hi_ushort16)htons(access_cfg->tftp_port);
    if(access_cfg->tftp_wan)
    {
        //ipv4
        u32Val_ip = igdCmApiChartoIp(data.acTftpWanIpv4Addr);
        HI_OS_MEMCPY_S((void *)access_cfg->tftp_wan_ipv4_address, 4, &u32Val_ip, 4);
        //printf("return u32Val_ip=0x%x, access_cfg->tftp_wan_ipv4_address = %s\n", u32Val_ip, access_cfg->tftp_wan_ipv4_address);
        u32Val_mask = igdCmApiChartoIp(data.acTftpWanIpv4Mask);
        HI_OS_MEMCPY_S((void *)access_cfg->tftp_wan_ipv4_mask, 4, &u32Val_mask, 4);
        //printf("return u32Val_mask=0x%x, access_cfg->tftp_wan_ipv4_mask = %s\n", u32Val_mask, access_cfg->tftp_wan_ipv4_mask);
        if (u32Val_ip)
        {
            access_cfg->tftp_wan_ipv4_control = 1;
        }
    }

    //ping
    access_cfg->ping_control = access_control_state.ping;
    access_cfg->ping_lan = 1;
    access_cfg->ping_wan = data.ucIcmpWANEnable;
    if(access_cfg->ping_wan)
    {
        //ipv4
        u32Val_ip = igdCmApiChartoIp(data.acIcmpWanIpv4Addr);
        HI_OS_MEMCPY_S((void *)access_cfg->ping_wan_ipv4_address, 4, &u32Val_ip, 4);
        //printf("return u32Val_ip=0x%x, access_cfg->ping_wan_ipv4_address = %s\n", u32Val_ip, access_cfg->ping_wan_ipv4_address);
        u32Val_mask = igdCmApiChartoIp(data.acIcmpWanIpv4Mask);
        HI_OS_MEMCPY_S((void *)access_cfg->ping_wan_ipv4_mask, 4, &u32Val_mask, 4);
        //printf("return u32Val_mask=0x%x, access_cfg->ping_wan_ipv4_mask = %s\n", u32Val_mask, access_cfg->ping_wan_ipv4_mask);
        if (u32Val_ip)
        {
            access_cfg->ping_wan_ipv4_control = 1;
        }
    }

    //ssh
    access_cfg->ssh_control = access_control_state.ssh;
    access_cfg->ssh_lan = (data.ucSshEnable & 0x01)?1:0;
    access_cfg->ssh_wan = (data.ucSshEnable & 0x02)?1:0;
    access_cfg->ssh_port = data.ulSshPort;
    access_cfg->ssh_port = (hi_ushort16)htons(access_cfg->ssh_port);
    if(access_cfg->ssh_wan)
    {
        //ipv4
        u32Val_ip = igdCmApiChartoIp(data.acSshWanIpv4Addr);
        HI_OS_MEMCPY_S((void *)access_cfg->ssh_wan_ipv4_address, 4, &u32Val_ip, 4);
        //printf("return u32Val_ip=0x%x, access_cfg->ssh_wan_ipv4_address = %s\n", u32Val_ip, access_cfg->ssh_wan_ipv4_address);
        u32Val_mask = igdCmApiChartoIp(data.acSshWanIpv4Mask);
        HI_OS_MEMCPY_S((void *)access_cfg->ssh_wan_ipv4_mask, 4, &u32Val_mask, 4);
        //printf("return u32Val_mask=0x%x, access_cfg->ssh_wan_ipv4_mask = %s\n", u32Val_mask, access_cfg->ssh_wan_ipv4_mask);
        if (u32Val_ip)
        {
            access_cfg->ssh_wan_ipv4_control = 1;
        }
    }

    /* slicing the packets and add tlv header to the data */
    tlv_leaf = tlv_head->us_leaf;
    tlv_leaf = (hi_ushort16)htons(tlv_leaf);
	struct_size = sizeof(vs_ctcoam_access_control_s);
    if(struct_size > 0 && struct_size <= 128)
    {
        *puc_changingmsglen = struct_size;
        HI_OS_MEMCPY_S(pv_outmsg, sizeof(access_cfg), access_cfg, *puc_changingmsglen);
    }
	else if(struct_size > 128 && struct_size <= (255 - 4))//langer than 128, need to slicing the packets
	{
		add_len = packet_slicing_for_send(access_cfg, sizeof(vs_ctcoam_access_control_s), tlv_head->uc_branch, tlv_leaf);
        add_len = (add_len - 1) * 4;
		pus_msglen = (hi_ushort16 *)(puc_changingmsglen + 1);
		*pus_msglen = 0;
		*puc_changingmsglen = struct_size + add_len;//send msg will use this len value
	}
	else if(struct_size > (255 - 4) && struct_size <= 1496)
	{
		add_len = packet_slicing_for_send(access_cfg, sizeof(vs_ctcoam_access_control_s), tlv_head->uc_branch, tlv_leaf);
        add_len = (add_len - 1) * 4;
		pus_msglen = (hi_ushort16 *)(puc_changingmsglen + 1);
		*pus_msglen = struct_size + add_len;//send msg will use this len value
		*puc_changingmsglen = 0;
	}
	else
	{
		return HI_RET_FAIL;
	}

    return HI_RET_SUCC;
}

uint32_t vs_ctcoam_set_access_control(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    hi_uint32 u32Val = 0;
    vs_ctcoam_access_control_s access_cfg_data;
    vs_ctcoam_access_control_s *access_cfg = &access_cfg_data;
    IgdAppServiceManageAttrConfTab data;
    IgdSecurFirewallAttrConfTab data2;

    if (pv_inmsg == NULL)
        return HI_RET_INVALID_PARA;
    
    packet_slicing_for_receive(&access_cfg_data, sizeof(vs_ctcoam_lan_dhcpv6_server_s), pv_inmsg, 1496, 0xc7, 0x2028);

    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));
    HI_OS_MEMSET_S(&data2,sizeof(data2),0,sizeof(data2));

    ret = igdCmConfGet(IGD_SECUR_URL_FIREWALL_ATTR_TAB, (unsigned char *)&data2, sizeof(data2));
    if(ret != 0)
    {
        printf("igdCmConfGet IGD_SECUR_URL_FIREWALL_ATTR_TAB ret : %d \r\n", ret);
        return HI_RET_FAIL;
    }

    /* if firewall level is high, can not set telnet, ssh, tftp, ftp, http and https */
    if (data2.ucFirewallLevel == FIREWALL_LEVEL_LOW)
    {
        //telnet
        access_control_state.telnet = access_cfg->telnet_control;
        if(access_cfg->telnet_control == 1)
        {
            data.ulBitmap |= SERVICE_MANAGE_ATTR_MASK_BIT1_TELNET_ENABLE|SERVICE_MANAGE_ATTR_MASK_BIT18_TELNET_WAN_ENABLE\
                            |SERVICE_MANAGE_ATTR_MASK_BIT6_TELNET_WANPORT;
            data.ulBitmap |= SERVICE_MANAGE_ATTR_MASK_BIT22_TELNET_WAN_IPV4_ADDR|SERVICE_MANAGE_ATTR_MASK_BIT23_TELNET_WAN_IPV4_MASK;
            data.ucTelnetEnable = access_cfg->telnet_lan;
            data.ucTelnetWANEnable = (access_cfg->telnet_wan == 1)?TELNET_SERVICE_REMOTE_ENABLE:TELNET_SERVICE_DISABLE;
            access_cfg->telnet_port = (hi_ushort16)ntohs(access_cfg->telnet_port);
            data.ulTelnetWANPort = access_cfg->telnet_port;

            if(access_cfg->telnet_wan == 1)
            {
                //ipv4
                if(access_cfg->telnet_wan_ipv4_control && access_cfg->telnet_wan_ipv4_address && access_cfg->telnet_wan_ipv4_mask)
                {
                    HI_OS_MEMCPY_S(&u32Val, 4, (void *)access_cfg->telnet_wan_ipv4_address, 4);
                    //printf("u32Val=0x%x\n", u32Val);
                    igdCmApiIptoChar((unsigned char*)&u32Val, data.acTelnetWanIpv4Addr, sizeof(data.acTelnetWanIpv4Addr));
                    HI_OS_MEMCPY_S(&u32Val, 4, (void *)access_cfg->telnet_wan_ipv4_mask, 4);
                    //printf("u32Val=0x%x\n", u32Val);
                    igdCmApiIptoChar((unsigned char*)&u32Val, data.acTelnetWanIpv4Mask, sizeof(data.acTelnetWanIpv4Mask));
                    //printf("data.acTelnetWanIpv4Addr=%s, data.acTelnetWanIpv4Mask=%s\n", data.acTelnetWanIpv4Addr, data.acTelnetWanIpv4Mask);
                }
                else
                {
                    strcpy(data.acTelnetWanIpv4Addr, "0.0.0.0");
                    strcpy(data.acTelnetWanIpv4Mask, "0.0.0.0");
                }

                //ipv6
                if(access_cfg->telnet_wan_ipv6_control && access_cfg->telnet_wan_ipv6_address && access_cfg->telnet_wan_ipv6_mask)
                {

                }
                else
                {

                }
            }
            else
            {
                strcpy(data.acTelnetWanIpv4Addr, "0.0.0.0");
                strcpy(data.acTelnetWanIpv4Mask, "0.0.0.0");  
            }
        }

        //ftp
        access_control_state.ftp = access_cfg->ftp_control;
        //printf("access_control_state.ftp=%d\n", access_control_state.ftp);
        if(access_cfg->ftp_control == 1)
        {
            data.ulBitmap |= SERVICE_MANAGE_ATTR_MASK_BIT0_FTP_ENABLE|SERVICE_MANAGE_ATTR_MASK_BIT2_FTP_PORT;
            data.ulBitmap |= SERVICE_MANAGE_ATTR_MASK_BIT24_FTP_WAN_IPV4_ADDR|SERVICE_MANAGE_ATTR_MASK_BIT25_FTP_WAN_IPV4_MASK;
            data.ucFtpEnable = (access_cfg->ftp_lan==1)?FTP_SERVICE_LOCAL_ENABLE:FTP_SERVICE_DISABLE;//ftp lan
            access_cfg->ftp_port = (hi_ushort16)ntohs(access_cfg->ftp_port);
            data.ulFtpPort = access_cfg->ftp_port;

            if(access_cfg->ftp_wan == 1)
            {
                data.ucFtpEnable |= FTP_SERVICE_REMOTE_ENABLE;//ftp wan enbale
                //ipv4
                if(access_cfg->ftp_wan_ipv4_control && access_cfg->ftp_wan_ipv4_address && access_cfg->ftp_wan_ipv4_mask)
                {
                    HI_OS_MEMCPY_S(&u32Val, 4, (void *)access_cfg->ftp_wan_ipv4_address, 4);
                    //printf("u32Val=0x%x\n", u32Val);
                    igdCmApiIptoChar((unsigned char*)&u32Val, data.acFtpWanIpv4Addr, sizeof(data.acFtpWanIpv4Addr));
                    HI_OS_MEMCPY_S(&u32Val, 4, (void *)access_cfg->ftp_wan_ipv4_mask, 4);
                    //printf("u32Val=0x%x\n", u32Val);
                    igdCmApiIptoChar((unsigned char*)&u32Val, data.acFtpWanIpv4Mask, sizeof(data.acFtpWanIpv4Mask));
                    //printf("data.acFtpWanIpv4Addr=%s, data.acFtpWanIpv4Mask=%s\n", data.acFtpWanIpv4Addr, data.acFtpWanIpv4Mask);
                }
                else
                {
                    strcpy(data.acFtpWanIpv4Addr, "0.0.0.0");
                    strcpy(data.acFtpWanIpv4Mask, "0.0.0.0");
                }

                //ipv6
                if(access_cfg->ftp_wan_ipv6_control && access_cfg->ftp_wan_ipv6_address && access_cfg->ftp_wan_ipv6_mask)
                {

                }
                else
                {

                }
            }
            else
            {
                strcpy(data.acFtpWanIpv4Addr, "0.0.0.0");
                strcpy(data.acFtpWanIpv4Mask, "0.0.0.0");
            }
        }

        //http
        access_control_state.http = access_cfg->http_control;
        //printf("access_control_state.http=%d\n", access_control_state.http);
        if(access_cfg->http_control == 1)
        {
            data.ulBitmap1 |= SERVICE_MANAGE_ATTR_MASK1_BIT8_HTTP_PORT;
            data.ulBitmap |= SERVICE_MANAGE_ATTR_MASK_BIT8_HTTP_ENABLE|SERVICE_MANAGE_ATTR_MASK_BIT29_HTTP_WAN_IPV4_ADDR
                            |SERVICE_MANAGE_ATTR_MASK_BIT30_HTTP_WAN_IPV4_MASK;
            data.ulHTTPEnable = (access_cfg->http_lan==1)?HTTP_SERVICE_ENABLE_LOCAL:HTTP_SERVICE_DISABLE;//http lan
            access_cfg->http_port = (hi_ushort16)ntohs(access_cfg->http_port);
            data.ulHttpPort = access_cfg->http_port;
            
            if(access_cfg->http_wan == 1)
            {
                data.ulHTTPEnable |= HTTP_SERVICE_ENABLE_REMOTE;//http wan enbale
                //ipv4
                if(access_cfg->http_wan_ipv4_control && access_cfg->http_wan_ipv4_address && access_cfg->http_wan_ipv4_mask)
                {
                    HI_OS_MEMCPY_S(&u32Val, 4, (void *)access_cfg->http_wan_ipv4_address, 4);
                    //printf("u32Val=0x%x\n", u32Val);
                    igdCmApiIptoChar((unsigned char*)&u32Val, data.acHttpWanIpv4Addr, sizeof(data.acHttpWanIpv4Addr));
                    HI_OS_MEMCPY_S(&u32Val, 4, (void *)access_cfg->http_wan_ipv4_mask, 4);
                    //printf("u32Val=0x%x\n", u32Val);
                    igdCmApiIptoChar((unsigned char*)&u32Val, data.acHttpWanIpv4Mask, sizeof(data.acHttpWanIpv4Mask));
                    //printf("data.acHttpWanIpv4Addr=%s, data.acHttpWanIpv4Mask=%s\n", data.acHttpWanIpv4Addr, data.acHttpWanIpv4Mask);
                }
                else
                {
                    strcpy(data.acHttpWanIpv4Addr, "0.0.0.0");
                    strcpy(data.acHttpWanIpv4Mask, "0.0.0.0");
                }

                //ipv6
                if(access_cfg->http_wan_ipv6_control && access_cfg->http_wan_ipv6_address && access_cfg->http_wan_ipv6_mask)
                {

                }
                else
                {

                }
            }
            else
            {
                strcpy(data.acHttpWanIpv4Addr, "0.0.0.0");
                strcpy(data.acHttpWanIpv4Mask, "0.0.0.0");
            }
        }

        //https
        access_control_state.https = access_cfg->https_control;
        if(access_cfg->https_control == 1)
        {
            data.ulBitmap1 |= SERVICE_MANAGE_ATTR_MASK1_BIT4_HTTPS_ENABLE|SERVICE_MANAGE_ATTR_MASK1_BIT7_HTTPS_PORT;
            data.ulBitmap1 |= SERVICE_MANAGE_ATTR_MASK1_BIT5_HTTPS_WAN_IPV4_ADDR|SERVICE_MANAGE_ATTR_MASK1_BIT6_HTTPS_WAN_IPV4_MASK;
            data.ucHttpsEnable = (access_cfg->https_lan==1)?HTTPS_SERVICE_ENABLE_LOCAL:HTTPS_SERVICE_DISABLE;//https lan
            access_cfg->https_port = (hi_ushort16)ntohs(access_cfg->https_port);
            data.ulHttpsPort = access_cfg->https_port;

            if(access_cfg->https_wan == 1)
            {
                data.ucHttpsEnable |= HTTPS_SERVICE_ENABLE_REMOTE;//https wan enbale
                //ipv4
                if(access_cfg->https_wan_ipv4_control && access_cfg->https_wan_ipv4_address && access_cfg->https_wan_ipv4_mask)
                {
                    HI_OS_MEMCPY_S(&u32Val, 4, (void *)access_cfg->https_wan_ipv4_address, 4);
                    //printf("u32Val=0x%x\n", u32Val);
                    igdCmApiIptoChar((unsigned char*)&u32Val, data.acHttpsWanIpv4Addr, sizeof(data.acHttpsWanIpv4Addr));
                    HI_OS_MEMCPY_S(&u32Val, 4, (void *)access_cfg->https_wan_ipv4_mask, 4);
                    //printf("u32Val=0x%x\n", u32Val);
                    igdCmApiIptoChar((unsigned char*)&u32Val, data.acHttpsWanIpv4Mask, sizeof(data.acHttpsWanIpv4Mask));
                    //printf("data.acHttpsWanIpv4Addr=%s, data.acHttpsWanIpv4Mask=%s\n", data.acHttpsWanIpv4Addr, data.acHttpsWanIpv4Mask);
                }
                else
                {
                    strcpy(data.acHttpsWanIpv4Addr, "0.0.0.0");
                    strcpy(data.acHttpsWanIpv4Mask, "0.0.0.0");
                }

                //ipv6
                if(access_cfg->https_wan_ipv6_control && access_cfg->https_wan_ipv6_address && access_cfg->https_wan_ipv6_mask)
                {

                }
                else
                {

                }
            }
            else
            {
                strcpy(data.acHttpsWanIpv4Addr, "0.0.0.0");
                strcpy(data.acHttpsWanIpv4Mask, "0.0.0.0");
            }
        }
        
        //tftp
        access_control_state.tftp = access_cfg->tftp_control;
        if(access_cfg->tftp_control == 1)
        {
            data.ulBitmap |= SERVICE_MANAGE_ATTR_MASK_BIT15_TFTP_PORT|SERVICE_MANAGE_ATTR_MASK_BIT11_TFTP_ENABLE;
            data.ulBitmap1 |= SERVICE_MANAGE_ATTR_MASK1_BIT2_TFTP_WAN_IPV4_ADDR|SERVICE_MANAGE_ATTR_MASK1_BIT3_TFTP_WAN_IPV4_MASK;
            data.ucTftpEnable = (access_cfg->tftp_lan==1)?TFTP_SERVICE_LOCAL_ENABLE:TFTP_SERVICE_DISABLE;
            access_cfg->tftp_port = (hi_ushort16)ntohs(access_cfg->tftp_port);
            data.ulTftpPort = access_cfg->tftp_port;

            if(access_cfg->tftp_wan == 1)
            {
                data.ucTftpEnable |= TFTP_SERVICE_REMOTE_ENABLE;//tftp wan enbale
                //ipv4
                if(access_cfg->tftp_wan_ipv4_control && access_cfg->tftp_wan_ipv4_address && access_cfg->tftp_wan_ipv4_mask)
                {
                    HI_OS_MEMCPY_S(&u32Val, 4, (void *)access_cfg->tftp_wan_ipv4_address, 4);
                    //printf("u32Val=0x%x\n", u32Val);
                    igdCmApiIptoChar((unsigned char*)&u32Val, data.acTftpWanIpv4Addr, sizeof(data.acTftpWanIpv4Addr));
                    HI_OS_MEMCPY_S(&u32Val, 4, (void *)access_cfg->tftp_wan_ipv4_mask, 4);
                    //printf("u32Val=0x%x\n", u32Val);
                    igdCmApiIptoChar((unsigned char*)&u32Val, data.acTftpWanIpv4Mask, sizeof(data.acTftpWanIpv4Mask));
                    //printf("data.acTftpWanIpv4Addr=%s, data.acTftpWanIpv4Mask=%s\n", data.acTftpWanIpv4Addr, data.acTftpWanIpv4Mask);
                }
                else
                {
                    strcpy(data.acTftpWanIpv4Addr, "0.0.0.0");
                    strcpy(data.acTftpWanIpv4Mask, "0.0.0.0");
                }

                //ipv6
                if(access_cfg->tftp_wan_ipv6_control && access_cfg->tftp_wan_ipv6_address && access_cfg->tftp_wan_ipv6_mask)
                {

                }
                else
                {

                }
            }
            else
            {
                strcpy(data.acTftpWanIpv4Addr, "0.0.0.0");
                strcpy(data.acTftpWanIpv4Mask, "0.0.0.0");
            }
        }

        //ping
        access_control_state.ping = access_cfg->ping_control;
        if(access_cfg->ping_control == 1)
        {
            data.ulBitmap |= SERVICE_MANAGE_ATTR_MASK_BIT26_ICMP_WAN_IPV4_ADDR|SERVICE_MANAGE_ATTR_MASK_BIT27_ICMP_WAN_IPV4_MASK\
                            |SERVICE_MANAGE_ATTR_MASK_BIT28_ICMP_WAN_ENABLE;

            if(access_cfg->ping_wan == 1)
            {
                data.ucIcmpWANEnable |= ICMP_SERVICE_REMOTE_ENABLE;//ping wan enable
                //ipv4
                if(access_cfg->ping_wan_ipv4_control && access_cfg->ping_wan_ipv4_address && access_cfg->ping_wan_ipv4_mask)
                {
                    HI_OS_MEMCPY_S(&u32Val, 4, (void *)access_cfg->ping_wan_ipv4_address, 4);
                    //printf("u32Val=0x%x\n", u32Val);
                    igdCmApiIptoChar((unsigned char*)&u32Val, data.acIcmpWanIpv4Addr, sizeof(data.acIcmpWanIpv4Addr));
                    HI_OS_MEMCPY_S(&u32Val, 4, (void *)access_cfg->ping_wan_ipv4_mask, 4);
                    //printf("u32Val=0x%x\n", u32Val);
                    igdCmApiIptoChar((unsigned char*)&u32Val, data.acIcmpWanIpv4Mask, sizeof(data.acIcmpWanIpv4Mask));
                    //printf("data.acIcmpWanIpv4Addr=%s, data.acIcmpWanIpv4Mask=%s\n", data.acIcmpWanIpv4Addr, data.acIcmpWanIpv4Mask);
                }
                else
                {
                    strcpy(data.acIcmpWanIpv4Addr, "0.0.0.0");
                    strcpy(data.acIcmpWanIpv4Mask, "0.0.0.0");
                }

                //ipv6
                if(access_cfg->ping_wan_ipv6_control && access_cfg->ping_wan_ipv6_address && access_cfg->ping_wan_ipv6_mask)
                {

                }
                else
                {

                }
            }
            else
            {
                strcpy(data.acIcmpWanIpv4Addr, "0.0.0.0");
                strcpy(data.acIcmpWanIpv4Mask, "0.0.0.0");
            }
        }

        //ssh
        access_control_state.ssh = access_cfg->ssh_control;
        if(access_cfg->ssh_control == 1)
        {
            data.ulBitmap |= SERVICE_MANAGE_ATTR_MASK_BIT10_SSH_ENABLE|SERVICE_MANAGE_ATTR_MASK_BIT12_SSH_PORT;
            data.ulBitmap1 |= SERVICE_MANAGE_ATTR_MASK1_BIT0_SSH_WAN_IPV4_ADDR|SERVICE_MANAGE_ATTR_MASK1_BIT1_SSH_WAN_IPV4_MASK;
            data.ucSshEnable = (access_cfg->ssh_lan==1)?SSH_SERVICE_LOCAL_ENABLE:SSH_SERVICE_DISABLE;//ssh lan
            access_cfg->ssh_port = (hi_ushort16)ntohs(access_cfg->ssh_port);
            data.ulSshPort = access_cfg->ssh_port;

            if(access_cfg->ssh_wan == 1)
            {
                data.ucSshEnable |= SSH_SERVICE_REMOTE_ENABLE;//ssh wan enable
                //ipv4
                if(access_cfg->ssh_wan_ipv4_control && access_cfg->ssh_wan_ipv4_address && access_cfg->ssh_wan_ipv4_mask)
                {
                    HI_OS_MEMCPY_S(&u32Val, 4, (void *)access_cfg->ssh_wan_ipv4_address, 4);
                    //printf("u32Val=0x%x\n", u32Val);
                    igdCmApiIptoChar((unsigned char*)&u32Val, data.acSshWanIpv4Addr, sizeof(data.acSshWanIpv4Addr));
                    HI_OS_MEMCPY_S(&u32Val, 4, (void *)access_cfg->ssh_wan_ipv4_mask, 4);
                    //printf("u32Val=0x%x\n", u32Val);
                    igdCmApiIptoChar((unsigned char*)&u32Val, data.acSshWanIpv4Mask, sizeof(data.acSshWanIpv4Mask));
                    //printf("data.acSshWanIpv4Addr=%s, data.acSshWanIpv4Mask=%s\n", data.acSshWanIpv4Addr, data.acSshWanIpv4Mask);
                }
                else
                {
                    strcpy(data.acSshWanIpv4Addr, "0.0.0.0");
                    strcpy(data.acSshWanIpv4Mask, "0.0.0.0");
                }

                //ipv6
                if(access_cfg->ssh_wan_ipv6_control && access_cfg->ssh_wan_ipv6_address && access_cfg->ssh_wan_ipv6_mask)
                {

                }
                else
                {

                }
            }
            else
            {
                strcpy(data.acSshWanIpv4Addr, "0.0.0.0");
                strcpy(data.acSshWanIpv4Mask, "0.0.0.0");
            }
        }
        
        //ret = igdCmConfSet(IGD_APP_SERVICE_MANAGE_ATTR_TAB,(unsigned char *)&data,sizeof(data));
        ret = HI_IPC_CALL("hi_sml_access_control_attr_set", &data);
        if(ret != 0)
        {
            printf("hi_sml_access_control_attr_set fail, ret : %d \r\n", ret);
            return HI_RET_FAIL;
        }
    }
    
    return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

