#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "vs_ctcoam_private_oam_define.h"

uint32_t vs_ctcoam_get_private_oam_define(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    vs_ctcoam_private_oam_define_s *private_oam = NULL;
   	int ret;
    char encStr[64] = {0};
    hi_uchar8  auc_llid_mac[6];

    if (pv_outmsg == NULL)
        return HI_RET_INVALID_PARA;

    private_oam = (vs_ctcoam_private_oam_define_s *)pv_outmsg;

    hi_emac_get_llid_mac(ui_llidindex, auc_llid_mac);

    ret = onu_pri_auth_key(auc_llid_mac, encStr);

    printf("ui_llidindex=%d, ret=%d, encStr=%s\n", ui_llidindex, ret, encStr);

    hi_os_memcpy(private_oam->key, encStr, sizeof(private_oam->key));

    return HI_RET_SUCC;
}



#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

