#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include <sys/time.h>  // 添加这行
#include "vs_ctcoam_wan_configuration.h"
#include "zlib.h"
#include "hi_sml_wan.h"
#define VS_OAM_WAN_MAX 8

hi_uchar8 wan_config_running = 0, global_wan_num = 0;
vs_ctcoam_wan_dual_stack_configuration_s wan_config[VS_OAM_WAN_MAX];
HI_PTHREAD_T wan_add_thread;
#define WAN_OAM_DEBUG 0

void printf_oam_wandata(vs_ctcoam_wan_dual_stack_configuration_s *data)
{
	unsigned char i = 0, printf_enable = WAN_OAM_DEBUG;
	if(printf_enable)
	{
	    printf("===================== wan date =====================\n");
	    printf("wan_index:   %d   \n",data->wan_index);
	    printf("binding_lan:   %d   \n",data->binding_lan);
	    printf("binding_ssid:   %d   \n",data->binding_ssid);
	    printf("wan_service_mode:   %d   \n",data->wan_service_mode);
	    printf("wan_type:   %d   \n",data->wan_type);
	    printf("wan_vlan_id:   %d   \n",data->wan_vlan_id);
	    printf("wan_cos_8021p:   %d   \n",data->wan_cos_8021p);
	    printf("wan_ip_protocol:   %d   \n",data->wan_ip_protocol);
	    printf("wan_NAT_enable:   %d   \n",data->wan_NAT_enable);
	    printf("wan_connection_mode:   %d   \n",data->wan_connection_mode);
	    printf("wan_ipv4_address: 0x%x,0x%x,0x%x,0x%x   \n",data->wan_ipv4_address[0],data->wan_ipv4_address[1],data->wan_ipv4_address[2],data->wan_ipv4_address[3]);
	    printf("wan_ipv4_mask:   0x%x   \n",data->wan_ipv4_mask);
	    printf("wan_ipv4_default_gateway: 0x%x,0x%x,0x%x,0x%x  \n",data->wan_ipv4_default_gateway[0],data->wan_ipv4_default_gateway[1],data->wan_ipv4_default_gateway[2],data->wan_ipv4_default_gateway[3]);
	    printf("wan_ipv4_request_dns_mode:   %d   \n",data->wan_ipv4_request_dns_mode);
	    printf("wan_ipv4_master_dns: 0x%x,0x%x,0x%x,0x%x    \n",data->wan_ipv4_master_dns[0],data->wan_ipv4_master_dns[1],data->wan_ipv4_master_dns[2],data->wan_ipv4_master_dns[3]);
	    printf("wan_ipv4_slave_dns: 0x%x,0x%x,0x%x,0x%x   \n",data->wan_ipv4_slave_dns[0],data->wan_ipv4_slave_dns[1],data->wan_ipv4_slave_dns[2],data->wan_ipv4_slave_dns[3]);
	    printf("wan_ipv6_address: ");
	    for(i = 0; i < 16; i ++)
	    {
	        printf("0x%x,", data->wan_ipv6_address[i]);
	    }printf("\n");
	    printf("wan_ipv6_mask:   0x%x   \n",data->wan_ipv6_mask);
	    printf("wan_ipv6_default_gateway: ");
	    for(i = 0; i < 16; i ++)
	    {
	        printf("0x%x,", data->wan_ipv6_default_gateway[i]);
	    }printf("\n");
	    printf("wan_ipv6_request_dns_mode:   %d   \n",data->wan_ipv6_request_dns_mode);
	    printf("wan_ipv6_master_dns: ");
	    for(i = 0; i < 16; i ++)
	    {
	        printf("0x%x,", data->wan_ipv6_master_dns[i]);
	    }printf("\n");
	    printf("wan_ipv6_slave_dns: ");
	    for(i = 0; i < 16; i ++)
	    {
	        printf("0x%x,", data->wan_ipv6_slave_dns[i]);
	    }printf("\n");
	    printf("wan_pppoe_proxy_enable:   %d   \n",data->wan_pppoe_proxy_enable);
	    printf("wan_pppoe_username: ");
	    for(i = 0; i < 32; i ++)
	    {
	        printf("0x%x,", data->wan_pppoe_username[i]);
	    }printf("\n");
	    printf("wan_pppoe_password: ");
	    for(i = 0; i < 32; i ++)
	    {
	        printf("0x%x,", data->wan_pppoe_password[i]);
	    }printf("\n");
	    printf("wan_pppoe_servicename: ");
	    for(i = 0; i < 32; i ++)
	    {
	        printf("0x%x,", data->wan_pppoe_servicename[i]);
	    }printf("\n");
	    printf("wan_pppoe_mode:   %d   \n",data->wan_pppoe_mode);
	    printf("wan_ipv6_address_mode:   %d   \n",data->wan_ipv6_address_mode);
	    printf("wan_dhcpv6_client_request_address_enable:   %d   \n",data->wan_dhcpv6_client_request_address_enable);
	    printf("wan_dhcpv6_client_request_prefix_enable:   %d   \n",data->wan_dhcpv6_client_request_prefix_enable);
	    printf("wan_DS_Lite_enable:   %d   \n",data->wan_DS_Lite_enable);
	    printf("wan_DS_Lite_AFTR_mode:   %d   \n",data->wan_DS_Lite_AFTR_mode);
	    printf("wan_DS_Lite_address_type:   %d   \n",data->wan_DS_Lite_address_type);
	    printf("wan_DS_Lite_address: ");
	    for(i = 0; i < 128; i ++)
	    {
	        printf("0x%x,", data->wan_DS_Lite_address[i]);
	    }printf("\n");
	    printf("wan_6rd_enable:   %d   \n",data->wan_6rd_enable);
	    printf("wan_6rd_router_address: ");
	    for(i = 0; i < 4; i ++)
	    {
	        printf("0x%x,", data->wan_6rd_router_address[i]);
	    }printf("\n");
	    printf("wan_6rd_ipv4_mask_len:   %d   \n",data->wan_6rd_ipv4_mask_len);
	    printf("wan_6rd_ipv6_prefix: ");
	    for(i = 0; i < 16; i ++)
	    {
	        printf("0x%x,", data->wan_6rd_ipv6_prefix[i]);
	    }printf("\n");
	    printf("wan_6rd_ipv6_prefix_len:   %d   \n",data->wan_6rd_ipv6_prefix_len);
	    printf("wan_qos_enable:   %d   \n",data->wan_qos_enable);
	    printf("vlan_mode:   %d   \n",data->vlan_mode);
	    printf("translation_enable:   %d   \n",data->translation_enable);
	    printf("translation_vlan:   %d   \n",data->translation_vlan);
	    printf("translation_cos:   %d   \n",data->translation_cos);
	    printf("QinQ_enable:   %d   \n",data->QinQ_enable);
	    printf("TPID:   %d   \n",data->TPID);
	    printf("wan_svlan_id:   %d   \n",data->wan_svlan_id);
	    printf("wan_svlan_cos:   %d   \n",data->wan_svlan_cos);
	    printf("wan_name:   %s   \n",data->wan_name);
	    printf("wan_state:   %d   \n",data->wan_state);
	    printf("wan_mac: ");
	    for(i = 0; i < 6; i ++)
	    {
	        printf("0x%x,", data->wan_mac[i]);
	    }printf("\n");
	    printf("binding_ssid2:   %d   \n",data->binding_ssid2);
	    printf("MTU:   %d   \n",data->MTU);
	    printf("==========================================\n");	
    }
}

int vs_oam_wan_cfg_to_ram(hi_ushort16 *total_num, vs_ctcoam_wan_dual_stack_configuration_s *ram_cfg)
{
    hi_uchar8 i = 0;
    hi_int32 ret = 0;
    hi_uint32 listnum = 0;
    hi_uint32 u32Val;
    hi_char8 pri_dns[16] = {0};
    hi_char8 sec_dns[16] = {0};
    hi_char8 third_dns[16] = {0};
    hi_char8 pri_dnsv6[48] = {0};
    hi_char8 sec_dnsv6[48] = {0};
    hi_char8 third_dnsv6[48] = {0};
    vs_ctcoam_wan_dual_stack_configuration_s wan_cfg;
    IgdWanConnectionAttrConfTab data;
    IgdWanConnectionStateInfoTab wanStateData;
    /* Bohannon, get the total wan num, and their wan conn id*/
    syswan_wan_listinfo_t list_info={0};

#if defined(CONFIG_PLATFORM_OPENWRT)
    HI_IPC_CALL("hi_sml_wan_connection_attr_tab_get_listnum", &list_info);
#else
    igdCmConfGetEntryNum(IGD_WAN_CONNECTION_ATTR_TAB, &listnum);
#endif
    if(ret != 0)
    {
        printf("igdCmConfGetEntryNum ret : %d \r\n", ret);
        return HI_RET_FAIL;
    }
    
    /* Bohannon, get the total wan num, and their wan conn id*/
    listnum = list_info.list_num;
    if(listnum > 0 && listnum <= VS_OAM_WAN_MAX)
    {
        IgdWanConnectionIndexAttrTab wan_conn_indexobj[IGD_WAN_CONNECTION_RECORD_NUM];
        HI_OS_MEMSET_S(wan_conn_indexobj,sizeof(wan_conn_indexobj),0,sizeof(wan_conn_indexobj));

        ret = igdCmConfGetAllEntry(IGD_WAN_CONNECTION_INDEX_ATTR_TAB,(unsigned char *)wan_conn_indexobj,sizeof(wan_conn_indexobj));
        if(ret != 0)
        {
            printf("igdCmConfGetAllEntry IGD_WAN_CONNECTION_INDEX_ATTR_TAB ret : %d \r\n", ret);
            return HI_RET_FAIL;
        }

        *total_num = listnum;
        for(i = 0; i < listnum; i++)
        {
            HI_OS_MEMSET_S(&wan_cfg, sizeof(wan_cfg), 0, sizeof(wan_cfg));
            HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));
            data.ulBitmap |= WAN_CONNECTION_ATTR_MASK_ALL;
            data.ulBitmap1 |= WAN_CONNECTION_ATTR_MASK1_ALL;

            data.ucGlobalIndex = wan_conn_indexobj[i].ucGlobalIndex;
            data.ucConDevIndex = wan_conn_indexobj[i].ucConDevIndex;
            data.ucXPConIndex = wan_conn_indexobj[i].ucXPConIndex;
            HI_OS_STRCPY_S((char *)data.aucWanName, sizeof(data.aucWanName),wan_conn_indexobj[i].aucWanName);

#if defined(CONFIG_PLATFORM_OPENWRT)
            data.ucGlobalIndex = list_info.list_wanid[i];
            ret = HI_IPC_CALL("hi_sml_wan_connection_attr_tab_get", &data);
#else
            ret = igdCmConfGet(IGD_WAN_CONNECTION_ATTR_TAB, (unsigned char *)&data, sizeof(data));
#endif
            if(ret != 0)
            {
                printf("igdCmConfGet IGD_WAN_CONNECTION_ATTR_TAB ret : %d \n", ret);
                continue;
            }

            HI_OS_MEMSET_S(&wanStateData, sizeof(wanStateData), 0, sizeof(wanStateData));
            wanStateData.ucGlobalIndex = wan_conn_indexobj[i].ucGlobalIndex;
            HI_OS_STRCPY_S((char *)wanStateData.aucWanName, sizeof(wanStateData.aucWanName),wan_conn_indexobj[i].aucWanName);
            wanStateData.ulBitmap |= WAN_CONNECTION_STATE_MASK_ALL;
#if defined(CONFIG_PLATFORM_OPENWRT)
            wanStateData.ucGlobalIndex = list_info.list_wanid[i];
            ret = HI_IPC_CALL("hi_sml_wan_connection_state_tab_get", &wanStateData);
#else
            ret = igdCmConfGet(IGD_WAN_CONNECTION_STATE_TAB,(unsigned char *)&wanStateData,sizeof(wanStateData));
#endif
            if(ret != 0)
            {
                printf("igdCmConfGet IGD_WAN_CONNECTION_STATE_TAB ret : %d \n", ret);
                continue;
            }

            /*wan index and name*/
            wan_cfg.wan_index = i + 1;
            //HI_OS_MEMCPY_S(wan_cfg.wan_name, sizeof(wan_cfg.wan_name), data.aucWanName, sizeof(data.aucWanName));//not need to copy to ram

            /*wan service mode*/
            switch (data.ucServiceList)
            {
                case WAN_CONNECTION_SERVICE_INTERNET: 
                    wan_cfg.wan_service_mode = VS_WAN_SERVICE_INTERNET;
                    break;
                case WAN_CONNECTION_SERVICE_TR069: 
                    wan_cfg.wan_service_mode = VS_WAN_SERVICE_TR069;
                    break;
                case (WAN_CONNECTION_SERVICE_TR069 | WAN_CONNECTION_SERVICE_INTERNET): 
                    wan_cfg.wan_service_mode = VS_WAN_SERVICE_TR069_INTERNET;
                    break;
                case (WAN_CONNECTION_SERVICE_TR069 | WAN_CONNECTION_SERVICE_VOIP): 
                    wan_cfg.wan_service_mode = VS_WAN_SERCICE_TR069_VOIP;
                    break; 
                case (WAN_CONNECTION_SERVICE_INTERNET | WAN_CONNECTION_SERVICE_VOIP): 
                    wan_cfg.wan_service_mode = VS_WAN_SERVICE_INTERNET_VOIP;
                    break;    
                case (WAN_CONNECTION_SERVICE_TR069 | WAN_CONNECTION_SERVICE_INTERNET | WAN_CONNECTION_SERVICE_VOIP): 
                    wan_cfg.wan_service_mode = VS_WAN_SERVICE_TR069_INTERNET_VOIP;
                    break;   
                case WAN_CONNECTION_SERVICE_VOIP: 
                    wan_cfg.wan_service_mode = VS_WAN_SERVICE_VOIP;
                    break;
                case WAN_CONNECTION_SERVICE_OTHER: 
                    wan_cfg.wan_service_mode = VS_WAN_SERVICE_OTHER;
                    break;
                default:    
                    printf("data.ucServiceList[%d], unknown service mode\n", data.ucServiceList);             
                    break;
            }

            /*vlan info*/
            if (data.ucVlanMode == WAN_CONNECTION_VLAN_OVERRIDE)
            {
                wan_cfg.vlan_mode = VS_WAN_VLAN_MODE_TAG;
                wan_cfg.wan_vlan_id = data.ulVlanIdMark?data.ulVlanIdMark:0xFFFF;
                if(data.uc8021pMarkEnable)
                    wan_cfg.wan_cos_8021p = data.uc8021pMark;
                else
                    wan_cfg.wan_cos_8021p = 0xFFFF;
            } 
            else if (data.ucVlanMode == WAN_CONNECTION_VLAN_RESERVE)
            {
                wan_cfg.vlan_mode = VS_WAN_VLAN_MODE_TRANSPARENT;
                wan_cfg.wan_vlan_id = data.ulVlanIdMark?data.ulVlanIdMark:0xFFFF;
                if (data.uc8021pMarkEnable)
                    wan_cfg.wan_cos_8021p = data.uc8021pMark;
                else
                    wan_cfg.wan_cos_8021p = 0xFFFF;
            }
            else
            {
                wan_cfg.vlan_mode = VS_WAN_VLAN_MODE_DIABLE;
                wan_cfg.wan_vlan_id = 0xFFFF;
                wan_cfg.wan_cos_8021p = 0xFFFF;
            }
            
            /*translation vlan*/
            wan_cfg.translation_enable = 0;
            wan_cfg.translation_vlan = 0xFFFF;
            wan_cfg.translation_cos = 0xFFFF;
            /*QinQ*/
            wan_cfg.QinQ_enable = 0;
            wan_cfg.TPID = 0x8100;
            wan_cfg.wan_svlan_id = 0xFFFF;
            wan_cfg.wan_svlan_cos = 0xFFFF;
            /*Qos*/
            wan_cfg.wan_qos_enable = 0;//don not find the param
            /*wan mac*/
            //HI_OS_MEMCPY_S(wan_cfg.wan_mac, sizeof(wan_cfg.wan_mac), data.aucMACAddress, sizeof(data.aucMACAddress));//not need to copy to ram
            /*MTU*/
            wan_cfg.MTU = data.ulMaxMTUSize;
            /*wan connection type*/
            if ((data.ucConnectionType == WAN_CONNECTION_TYPE_IP_BRIDGED) || (data.ucConnectionType == WAN_CONNECTION_TYPE_PPPOE_BRIDGED))
            {
                wan_cfg.wan_type = VS_WAN_BRIDGE;
            }
            else
            {
                wan_cfg.wan_type = VS_WAN_ROUTE;
            }
            /*binding lan*/
            wan_cfg.binding_lan |= data.ulBindPort & WAN_CONNECTION_BIND_PORT_LAN1;
            wan_cfg.binding_lan |= data.ulBindPort & WAN_CONNECTION_BIND_PORT_LAN2;
            wan_cfg.binding_lan |= data.ulBindPort & WAN_CONNECTION_BIND_PORT_LAN3;
            wan_cfg.binding_lan|= data.ulBindPort & WAN_CONNECTION_BIND_PORT_LAN4;
            /*binding ssid*/
            wan_cfg.binding_ssid |= ((data.ulBindPort & WAN_CONNECTION_BIND_PORT_SSID1) >> 4);
            wan_cfg.binding_ssid |= ((data.ulBindPort & WAN_CONNECTION_BIND_PORT_SSID2) >> 4);
            wan_cfg.binding_ssid |= ((data.ulBindPort & WAN_CONNECTION_BIND_PORT_SSID3) >> 4);
            wan_cfg.binding_ssid |= ((data.ulBindPort & WAN_CONNECTION_BIND_PORT_SSID4) >> 4);
            wan_cfg.binding_ssid |= ((data.ulBindPort & WAN_CONNECTION_BIND_PORT_SSID5) >> 4);
            wan_cfg.binding_ssid |= ((data.ulBindPort & WAN_CONNECTION_BIND_PORT_SSID6) >> 4);
            wan_cfg.binding_ssid |= ((data.ulBindPort & WAN_CONNECTION_BIND_PORT_SSID7) >> 4);
            wan_cfg.binding_ssid |= ((data.ulBindPort & WAN_CONNECTION_BIND_PORT_SSID8) >> 4);

            /*ip protocal and state*/
            switch (data.ucIPMode)
            {
                case WAN_CONNECTION_IP_MODE_V4:
                    wan_cfg.wan_ip_protocol = VS_WAN_IPV4;
                    //wan_cfg.wan_state = (wanStateData.ucConnectionStatus == WAN_CONNECTION_STATUS_CONNECTED) ? 1 : 0;//not need to copy to ram
                    if (data.ucConnectionType == WAN_CONNECTION_TYPE_IP_ROUTED)
                        wan_cfg.wan_NAT_enable = data.ucNATEnabled;
                    break;
                case WAN_CONNECTION_IP_MODE_V6:
                    wan_cfg.wan_ip_protocol = VS_WAN_IPV6;
                    //wan_cfg.wan_state = (wanStateData.ucIPv6ConnectionStatus == WAN_CONNECTION_IPV6_STATUS_CONNECTED) ? 1 : 0;//not need to copy to ram
                    break;
                case WAN_CONNECTION_IP_MODE_V4_AND_V6:
                    wan_cfg.wan_ip_protocol  = VS_WAN_IPV4_AND_IPV6;
                    if (data.ucConnectionType == WAN_CONNECTION_TYPE_IP_ROUTED)
                        wan_cfg.wan_NAT_enable = data.ucNATEnabled;
                    if (wanStateData.ucConnectionStatus == WAN_CONNECTION_STATUS_CONNECTED || wanStateData.ucIPv6ConnectionStatus == WAN_CONNECTION_IPV6_STATUS_CONNECTED)
                        //wan_cfg.wan_state = 1;//not need to copy to ram
                    break;
                default:
                    printf("data.ucIPMode[%d], unknown ip protocol type\n", data.ucIPMode);
                    break;
            }

            /*ip info*/
            if (wan_cfg.wan_type  == VS_WAN_ROUTE)
            {
                if ((data.ucIPMode & WAN_CONNECTION_IP_MODE_V6) == WAN_CONNECTION_IP_MODE_V6)
                {
                    if (data.ucDsliteEnable)
                    {
                        wan_cfg.wan_DS_Lite_enable = data.ucDsliteEnable;
                        if (data.ucAftrMode == WAN_CONNECTION_AFTR_MODE_STATIC)
                        {
                            wan_cfg.wan_DS_Lite_AFTR_mode = VS_WAN_DSLITE_AFTR_MODE_MANUAL;
                            wan_cfg.wan_DS_Lite_address_type = 0;/*0:ipv6 1:dns*/
                            HI_OS_MEMCPY_S(wan_cfg.wan_DS_Lite_address, sizeof(wan_cfg.wan_DS_Lite_address), data.aucAftr, sizeof(data.aucAftr));
                        }
                        else
                        {
                            wan_cfg.wan_DS_Lite_AFTR_mode = VS_WAN_DSLITE_AFTR_MODE_DHCPV6;
                        }
                    }
                    wan_cfg.wan_6rd_enable = 0;//don not support
                }
                switch (data.ucAddressingType)
                {
                    case WAN_CONNECTION_ADDRESSING_TYPE_STATIC: 
                        wan_cfg.wan_connection_mode = VS_WAN_CONNECTION_ADDRESSING_TYPE_STATIC;
                        if ((data.ucIPMode & WAN_CONNECTION_IP_MODE_V4) == WAN_CONNECTION_IP_MODE_V4)
                        {
                            HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_address, CM_IP_ADDR_LEN, data.aucExternalIPAddress, CM_IP_ADDR_LEN);

                            HI_OS_MEMCPY_S(&u32Val, CM_IP_ADDR_LEN, data.aucSubnetMask, CM_IP_ADDR_LEN);

                            wan_cfg.wan_ipv4_mask = calculate_mask_bits(u32Val);

                            HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_default_gateway, CM_IP_ADDR_LEN, data.aucDefaultGateway, CM_IP_ADDR_LEN);

                            if (data.ucDNSEnabled)
                            {
                                sscanf(data.aucDNSServers, "%15[^,],%15[^,],%15[^,]", pri_dns, sec_dns, third_dns);
                                //printf("pri_dns=%s, sec_dns=%s, third_dns=%s\n", pri_dns, sec_dns, third_dns);
                                u32Val = igdCmApiChartoIp(pri_dns);
                                HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_master_dns, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
                                u32Val = igdCmApiChartoIp(sec_dns);
                                HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_slave_dns, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
                            }
                        }
                        break;
                    case WAN_CONNECTION_ADDRESSING_TYPE_DHCP:
                        wan_cfg.wan_connection_mode  = VS_WAN_CONNECTION_ADDRESSING_TYPE_DHCP;
                        if ((data.ucIPMode & WAN_CONNECTION_IP_MODE_V4) == WAN_CONNECTION_IP_MODE_V4)
                        {
                            /* not need to copy to ram
                            u32Val = igdCmApiChartoIp(wanStateData.aucIPv4Address);
                            HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_address, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
                            u32Val = igdCmApiChartoIp(wanStateData.aucIPv4Mask);
                            wan_cfg.wan_ipv4_mask = calculate_mask_bits(u32Val);
                            u32Val = igdCmApiChartoIp(wanStateData.aucIPv4Gateway);
                            HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_default_gateway, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
                            */
                            wan_cfg.wan_ipv4_request_dns_mode = (data.ucDNSOverrideAllowed == 1)?WAN_CONNECTION_DNS_OVERRIDE_ALLOWED_ENABLE:WAN_CONNECTION_DNS_OVERRIDE_ALLOWED_DISABLE;
                            if(data.ucDNSOverrideAllowed == WAN_CONNECTION_DNS_OVERRIDE_ALLOWED_ENABLE)
                            {
                                sscanf(data.aucDNSServers, "%15[^,],%15[^,],%15[^,]", pri_dns, sec_dns, third_dns);
                                u32Val = igdCmApiChartoIp(pri_dns);
                                HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_master_dns, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
                                u32Val = igdCmApiChartoIp(sec_dns);
                                HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_slave_dns, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
                            }
                            /* not need to copy to ram
                            else
                            {
                                u32Val = igdCmApiChartoIp(wanStateData.aucIPv4DnsPrimary);
                                HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_master_dns, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
                                u32Val = igdCmApiChartoIp(wanStateData.aucIPv4DnsBackup);
                                HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_slave_dns, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
                            }
                            */  
                        }

                        if ((data.ucIPMode & WAN_CONNECTION_IP_MODE_V6) == WAN_CONNECTION_IP_MODE_V6)
                        {
                            if(data.ucDNSAchieve == WAN_CONNECTION_DNS_ACHIEVE_STATIC)
                            {
                                wan_cfg.wan_ipv6_request_dns_mode = WAN_CONNECTION_DNS_ACHIEVE_STATIC;
                                sscanf(data.aucIPv6DNSServers, "%47[^,],%47[^,],%47[^,]", pri_dnsv6, sec_dnsv6, third_dnsv6);
                                //printf("pri_dnsv6=%s, sec_dnsv6=%s\n", pri_dnsv6, sec_dnsv6);
                                igdCmApiChartoIpv6((void *)wan_cfg.wan_ipv6_master_dns, pri_dnsv6);
                                igdCmApiChartoIpv6((void *)wan_cfg.wan_ipv6_slave_dns, sec_dnsv6);
                            }
                        }
                        break;
                    case WAN_CONNECTION_ADDRESSING_TYPE_PPPOE:
                        wan_cfg.wan_connection_mode  = VS_WAN_CONNECTION_ADDRESSING_TYPE_PPPOE;
                        wan_cfg.MTU = data.ulMaxMRUSize;
                        wan_cfg.wan_pppoe_proxy_enable = data.ucProxyEnable;

                        if (data.ucConnectionTrigger == WAN_CONNECTION_TRIGGER_ALWAYS_ON)
                            wan_cfg.wan_pppoe_mode = VS_WAN_PPPOE_DIAL_MODE_ALWAYS_ON;
                        else
                            wan_cfg.wan_pppoe_mode  = VS_WAN_PPPOE_DIAL_MODE_ON_CONMAND;
                        
                        HI_OS_MEMCPY_S(wan_cfg.wan_pppoe_username, sizeof(wan_cfg.wan_pppoe_username), data.aucUsername, sizeof(data.aucUsername));
                        HI_OS_MEMCPY_S(wan_cfg.wan_pppoe_password, sizeof(wan_cfg.wan_pppoe_password), data.aucPassword, sizeof(data.aucPassword));
                        HI_OS_MEMCPY_S(wan_cfg.wan_pppoe_servicename, sizeof(wan_cfg.wan_pppoe_servicename), data.aucPppoeServiceName, sizeof(data.aucPppoeServiceName));

                        /* not need to copy to ram
                        if ((data.ucIPMode & WAN_CONNECTION_IP_MODE_V4) == WAN_CONNECTION_IP_MODE_V4)
                        {
                            HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_address, CM_IP_ADDR_LEN, wanStateData.aucExternalIPAddress, CM_IP_ADDR_LEN);
                            u32Val = igdCmApiChartoIp(wanStateData.aucIPv4Mask);
                            wan_cfg.wan_ipv4_mask = calculate_mask_bits(u32Val);
                            u32Val = igdCmApiChartoIp(wanStateData.aucIPv4Gateway);
                            HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_default_gateway, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
                            u32Val = igdCmApiChartoIp(wanStateData.aucIPv4DnsPrimary);
                            HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_master_dns, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
                            u32Val = igdCmApiChartoIp(wanStateData.aucIPv4DnsBackup);
                            HI_OS_MEMCPY_S(wan_cfg.wan_ipv4_slave_dns, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
                        }
                        */
                        break;
                    default:    
                        printf("data.ucAddressingType[%d], unknown address type\n", data.ucAddressingType);             
                        break;
                }

                if ((data.ucIPMode & WAN_CONNECTION_IP_MODE_V6) == WAN_CONNECTION_IP_MODE_V6)
                {
                    if(wan_cfg.wan_connection_mode != VS_WAN_CONNECTION_ADDRESSING_TYPE_STATIC)//dhcp and pppoe take effect
                        wan_cfg.wan_ipv6_address_mode = (data.ucIPv6AddressOrigin == WAN_CONNECTION_IPV6_ADDR_ORIGIN_DHCPV6)?VS_WAN_IPV6_ADDRESS_MODE_DHCP:VS_WAN_IPV6_ADDRESS_MODE_SLAAC;
                    
                    if (wan_cfg.wan_ipv6_address_mode == VS_WAN_IPV6_ADDRESS_MODE_DHCP)
                    {
                        //wan_cfg.wan_dhcpv6_client_request_address_enable = (data.ucIPv6AddressOrigin==WAN_CONNECTION_IPV6_ADDR_ORIGIN_NONE)?0:1;
                        wan_cfg.wan_dhcpv6_client_request_prefix_enable = (data.ucIPv6PrefixOrigin==WAN_CONNECTION_IPV6_PREFIX_ORIGIN_PREFIX_DELEGATION)?1:0;/*DHCP PD*/
                    }
                    else
                    {
                        //wan_cfg.wan_dhcpv6_client_request_address_enable = (data.ucIPv6AddressOrigin==WAN_CONNECTION_IPV6_ADDR_ORIGIN_NONE)?0:1;
                        wan_cfg.wan_dhcpv6_client_request_prefix_enable = (data.ucIPv6PrefixOrigin==WAN_CONNECTION_IPV6_PREFIX_ORIGIN_RA)?1:0;/*DHCP PD*/   
                    }

                    if(data.ucIPv6AddressOrigin == WAN_CONNECTION_IPV6_ADDR_ORIGIN_STATIC)
                    {
                        
                        HI_OS_MEMCPY_S(wan_cfg.wan_ipv6_address, sizeof(wan_cfg.wan_ipv6_address), data.aucIPv6Address, sizeof(data.aucIPv6Address));
                        wan_cfg.wan_ipv6_mask = data.ucIPv6AddrPrefix;
                        //printf("ucIPv6AddrPrefix = %d\n", data.ucIPv6AddrPrefix);     
                        igdCmApiChartoIpv6((void*)wan_cfg.wan_ipv6_default_gateway, data.aucDefaultIPv6Gateway);
 
                        //pst_data->wanCfg[wanIndex].uc_wanRequestDnsMode = VS_WAN_DNSV6_STATIC;
                        sscanf(data.aucIPv6DNSServers, "%47[^,],%47[^,],%47[^,]", pri_dnsv6, sec_dnsv6, third_dnsv6);
                        //printf("pri_dnsv6=%s, sec_dnsv6=%s\n", pri_dnsv6, sec_dnsv6);
                        igdCmApiChartoIpv6((void *)wan_cfg.wan_ipv6_master_dns, pri_dnsv6);

                        igdCmApiChartoIpv6((void *)wan_cfg.wan_ipv6_slave_dns, sec_dnsv6);
                    }
                    else
                    {
                        if(data.ucDNSAchieve != WAN_CONNECTION_DNS_ACHIEVE_STATIC)
                        {
                            if (strlen(wanStateData.aucIpv6Address) != 0) 
                            {
                                igdCmApiChartoIpv6((void *)wan_cfg.wan_ipv6_address, wanStateData.aucIpv6Address);
                                wan_cfg.wan_ipv6_mask = wanStateData.ulIpv6Mask;
                            }
                            if (strlen(wanStateData.aucIpv6Gateway) != 0)
                            {
                                igdCmApiChartoIpv6((void *)wan_cfg.wan_ipv6_default_gateway, wanStateData.aucIpv6Gateway);
                            }
                            if (strlen(wanStateData.aucIpv6DnsPrimary) != 0)
                            {
                                igdCmApiChartoIpv6((void *)wan_cfg.wan_ipv6_master_dns, wanStateData.aucIpv6DnsPrimary);
                            }
                            if (strlen(wanStateData.aucIpv6DnsBackup) != 0)
                            {
                                igdCmApiChartoIpv6((void *)wan_cfg.wan_ipv6_slave_dns, wanStateData.aucIpv6DnsBackup);
                            }
                        }
                    }
                }
            }

            HI_OS_MEMCPY_S(&ram_cfg[i], sizeof(vs_ctcoam_wan_dual_stack_configuration_s), &wan_cfg, sizeof(vs_ctcoam_wan_dual_stack_configuration_s));

        }//for(i = 0; i < listnum; i++)
    }
    else if(listnum == 0)
    {
        *total_num = 0;
        for(i = 0; i < VS_OAM_WAN_MAX; i++)
            HI_OS_MEMCPY_S(&ram_cfg[i], sizeof(vs_ctcoam_wan_dual_stack_configuration_s), &wan_cfg, sizeof(vs_ctcoam_wan_dual_stack_configuration_s));
    }
    else
    {
        printf("error wan number = %d\n", listnum);
        return HI_RET_FAIL;
    }

    return HI_RET_SUCC; 
}

hi_uint32 vs_oam_delete_all_wan()
{
    IgdWanConnectionAttrConfTab data;
    IgdWanConnectionIndexAttrTab wan_conn_indexobj[IGD_WAN_CONNECTION_RECORD_NUM];
    hi_uint32 listnum = 0;
    int ret = 0, i = 0;
    /* Bohannon, get the total wan num, and their wan conn id*/
    syswan_wan_listinfo_t list_info={0};

#ifdef CONFIG_PLATFORM_OPENWRT
	ret = HI_IPC_CALL("hi_sml_wan_connection_attr_tab_delall");
    if(ret == 0)
    {
        return HI_RET_SUCC;
    }
    return HI_RET_SUCC;
    printf("hi_sml_wan_connection_attr_tab_delall ret : %d \r\n", ret);
    
	ret = HI_IPC_CALL("hi_sml_wan_connection_attr_tab_get_listnum", &list_info);
    if(ret != 0)
    {
        printf("hi_sml_wan_connection_attr_tab_get_listnum ret : %d \r\n", ret);
        return HI_RET_FAIL;
    }

#else
    igdCmConfGetEntryNum(IGD_WAN_CONNECTION_ATTR_TAB,&listnum);
#endif

    HI_OS_MEMSET_S(wan_conn_indexobj,sizeof(wan_conn_indexobj),0,sizeof(wan_conn_indexobj));

    ret = igdCmConfGetAllEntry(IGD_WAN_CONNECTION_INDEX_ATTR_TAB,(unsigned char *)wan_conn_indexobj,sizeof(wan_conn_indexobj));
    if(ret != 0)
    {
        printf("igdCmConfGetAllEntry IGD_WAN_CONNECTION_INDEX_ATTR_TAB ret : %d \r\n", ret);
        return HI_RET_FAIL;
    }
    /* Bohannon, get the total wan num, and their wan conn id*/
    listnum = list_info.list_num;
    for (i=0; i<listnum; i++)
    {
        HI_OS_MEMSET_S(&data,sizeof(data),0,sizeof(data));
        data.ulBitmap |= WAN_CONNECTION_ATTR_MASK_ALL;
        data.ulBitmap1 |= WAN_CONNECTION_ATTR_MASK1_ALL;

        data.ucGlobalIndex = wan_conn_indexobj[i].ucGlobalIndex;
        data.ucConDevIndex = wan_conn_indexobj[i].ucConDevIndex;
        data.ucXPConIndex = wan_conn_indexobj[i].ucXPConIndex;
        HI_OS_STRCPY_S((char *)data.aucWanName, sizeof(data.aucWanName),wan_conn_indexobj[i].aucWanName);

#ifdef CONFIG_PLATFORM_OPENWRT
	    ret = HI_IPC_CALL("hi_sml_wan_connection_attr_tab_del", &list_info.list_wanid[i]);
#else
        ret = igdCmConfDel(IGD_WAN_CONNECTION_ATTR_TAB,(unsigned char *)&data,sizeof(data));
#endif
        if(ret != 0)
        {
            printf("igdCmConfDel IGD_WAN_CONNECTION_ATTR_TAB ret : %d\n", ret);
            return HI_RET_FAIL;
        }
        printf("delete wan connection (%s) success.\n",data.aucWanName);
    }

    return HI_RET_SUCC;
}

hi_int32 vs_oam_wan_add(vs_ctcoam_wan_dual_stack_configuration_s wan_cfg)
{
    IgdWanConnectionAttrConfTab data;
    int ret;
    hi_uint32 u32Val = 0;
    hi_char8 pri_dns[16] = {0};
    hi_char8 sec_dns[16] = {0};
    hi_char8 pri_dnsv6[48] = {0};
    hi_char8 sec_dnsv6[48] = {0};

    HI_OS_MEMSET_S(&data,sizeof(data),0,sizeof(data));

    data.ulBitmap |= WAN_CONNECTION_ATTR_MASK_BIT1_VLAN_MODE|WAN_CONNECTION_ATTR_MASK_BIT2_8021P_MARK
                |WAN_CONNECTION_ATTR_MASK_BIT5_VLANID_MARK|WAN_CONNECTION_ATTR_MASK_BIT6_ENABLE
                |WAN_CONNECTION_ATTR_MASK_BIT7_CONNECTION_TYPE|WAN_CONNECTION_ATTR_MASK_BIT8_ADDRESSING_TYPE
                |WAN_CONNECTION_ATTR_MASK_BIT9_NAT_ENABLE|WAN_CONNECTION_ATTR_MASK_BIT10_SERVERLIST
                |WAN_CONNECTION_ATTR_MASK_BIT13_BIND_PORT|WAN_CONNECTION_ATTR_MASK_BIT17_USERNAME
                |WAN_CONNECTION_ATTR_MASK_BIT18_PASSWORD|WAN_CONNECTION_ATTR_MASK_BIT20_PPPOE_SERVICE_NAME
                |WAN_CONNECTION_ATTR_MASK_BIT22_DIAL_DEMAND|WAN_CONNECTION_ATTR_MASK_BIT23_SUBNET_MASK
                |WAN_CONNECTION_ATTR_MASK_BIT24_DEFAULT_GATEWAY|WAN_CONNECTION_ATTR_MASK_BIT25_EXTERNAL_IP_ADDR
                |WAN_CONNECTION_ATTR_MASK_BIT27_DNS_ENABLED|WAN_CONNECTION_ATTR_MASK_BIT22_DIAL_DEMAND
                |WAN_CONNECTION_ATTR_MASK_BIT30_DNS_SERVERS|WAN_CONNECTION_ATTR_MASK_BIT26_MAX_MTU
                |WAN_CONNECTION_ATTR_MASK_BIT21_PPPOE_AUTH_PROTOCOL|WAN_CONNECTION_ATTR_MASK_BIT28_DNS_OVERRIDE_ALLOWED;

    data.ulBitmap1 |= WAN_CONNECTION_ATTR_MASK1_BIT6_IP_MODE|WAN_CONNECTION_ATTR_MASK1_BIT7_PROXY_ENABLE
                |WAN_CONNECTION_ATTR_MASK1_BIT10_IPV6_ENABLE|WAN_CONNECTION_ATTR_MASK1_BIT12_DNS_ACHIEVE
                |WAN_CONNECTION_ATTR_MASK1_BIT13_IPV6_ADDR_ORIGIN|WAN_CONNECTION_ATTR_MASK1_BIT14_IPV6_PREFIX_DELEGATION_ENABLED
                |WAN_CONNECTION_ATTR_MASK1_BIT16_AFTR_MODE|WAN_CONNECTION_ATTR_MASK1_BIT17_IPV6_ADDRESS
                |WAN_CONNECTION_ATTR_MASK1_BIT19_IPV6_DNS_SERVERS|WAN_CONNECTION_ATTR_MASK1_BIT21_IPV6_PREFIX
                |WAN_CONNECTION_ATTR_MASK1_BIT22_DEFAULT_IPV6_GATEWAY|WAN_CONNECTION_ATTR_MASK1_BIT24_AFTR
                |WAN_CONNECTION_ATTR_MASK1_BIT5_LAN_INTERFACE_DHCP_ENABLE|WAN_CONNECTION_ATTR_MASK1_BIT28_MAX_MRU_SIZE
                |WAN_CONNECTION_ATTR_MASK1_BIT0_CONNECTION_TRIGGER|WAN_CONNECTION_ATTR_MASK1_BIT20_IPV6_PREFIX_ORIGIN
                |WAN_CONNECTION_ATTR_MASK1_BIT29_8021P_MARK_ENABLE|WAN_CONNECTION_ATTR_MASK1_BIT9_MULTICAST_VLAN;

    data.ulBitmap2 |= WAN_CONNECTION_ATTR_MASK2_BIT0_NPTV6_ENABLE|WAN_CONNECTION_ATTR_MASK2_IPV6_NAT_ENABLE;
    data.ucEnable = 1;
    data.uc8021pMarkEnable = WAN_8021P_MARK_ENABLE;
    data.ucConnectionType = (wan_cfg.wan_type==VS_WAN_BRIDGE)?WAN_CONNECTION_TYPE_IP_BRIDGED:WAN_CONNECTION_TYPE_IP_ROUTED;

    switch (wan_cfg.wan_service_mode)
    {
        case VS_WAN_SERVICE_INTERNET:
            data.ucServiceList = WAN_CONNECTION_SERVICE_INTERNET;
            break;
        case VS_WAN_SERVICE_TR069:
            data.ucServiceList = WAN_CONNECTION_SERVICE_TR069;
            break;
        case VS_WAN_SERVICE_TR069_INTERNET:
            data.ucServiceList = (WAN_CONNECTION_SERVICE_TR069 | WAN_CONNECTION_SERVICE_INTERNET);
            break;
        case VS_WAN_SERCICE_TR069_VOIP:
            data.ucServiceList = (WAN_CONNECTION_SERVICE_TR069 | WAN_CONNECTION_SERVICE_VOIP);
            break;
        case VS_WAN_SERVICE_INTERNET_VOIP:
            data.ucServiceList = (WAN_CONNECTION_SERVICE_INTERNET | WAN_CONNECTION_SERVICE_VOIP);
            break;
        case VS_WAN_SERVICE_TR069_INTERNET_VOIP:
            data.ucServiceList = (WAN_CONNECTION_SERVICE_TR069 | WAN_CONNECTION_SERVICE_INTERNET | WAN_CONNECTION_SERVICE_VOIP);
            break;
        case VS_WAN_SERVICE_VOIP:
            data.ucServiceList = WAN_CONNECTION_SERVICE_VOIP;
            break;
        case VS_WAN_SERVICE_OTHER:
            data.ucServiceList = WAN_CONNECTION_SERVICE_OTHER;
            break;
        default:    
            printf("wan_cfg.wan_service_mode[%d], unknown service mode\n", wan_cfg.wan_service_mode);             
            break;
    }

    switch (wan_cfg.wan_ip_protocol)
    {
        case VS_WAN_IPV4:
            data.ucIPMode = WAN_CONNECTION_IP_MODE_V4;
            if (data.ucConnectionType == WAN_CONNECTION_TYPE_IP_ROUTED)
            {
                data.ucNATEnabled = wan_cfg.wan_NAT_enable;
                data.ucDNSEnabled = 1;
                data.ucLanInterfaceDHCPEnable = 1;
            }
            break;
        case VS_WAN_IPV6:
            data.ucIPMode = WAN_CONNECTION_IP_MODE_V6;
            if (data.ucConnectionType == WAN_CONNECTION_TYPE_IP_ROUTED)
            {
                data.ucNPTv6Enable = 1;
                data.ucLanInterfaceDHCPEnable = 1;
            }
            break;
        case VS_WAN_IPV4_AND_IPV6:
            data.ucIPMode = WAN_CONNECTION_IP_MODE_V4_AND_V6;
            if (data.ucConnectionType == WAN_CONNECTION_TYPE_IP_ROUTED)
            {
                data.ucNATEnabled = wan_cfg.wan_NAT_enable;
                data.ucNPTv6Enable = 1;
                data.ucDNSEnabled = 1;
                data.ucLanInterfaceDHCPEnable = 1;
            }
            break;
        default:
            printf("wan_cfg.wan_ip_protocol[%d], unknown ip protocol type\n", wan_cfg.wan_ip_protocol);
            break;
    }

    if ((wan_cfg.wan_vlan_id != 0xFFFF) && (wan_cfg.vlan_mode == VS_WAN_VLAN_MODE_TAG))
    {
        if (wan_cfg.wan_vlan_id == 0)
        {
            printf("vlanId param is between 1 and 4095\n");
            return HI_RET_FAIL; 
        }
        data.ucVlanMode = WAN_CONNECTION_VLAN_OVERRIDE;
        data.ulVlanIdMark = wan_cfg.wan_vlan_id;
        data.ulMulticastVlan = (wan_cfg.wan_vlan_id > WAN_CONNECTION_MULTICAST_VLAN_MAX)? WAN_CONNECTION_MULTICAST_VLAN_MAX : wan_cfg.wan_vlan_id;
        if (wan_cfg.wan_cos_8021p == 0xFF)
        {
            data.uc8021pMarkEnable = WAN_8021P_MARK_DISABLE;
        }
        else
        {
            data.uc8021pMarkEnable = WAN_8021P_MARK_ENABLE;
            data.uc8021pMark  = wan_cfg.wan_cos_8021p;
        }  
    }
    else if ((wan_cfg.wan_vlan_id != 0xFFFF) && (wan_cfg.vlan_mode == VS_WAN_VLAN_MODE_TRANSPARENT))
    {
        data.ucVlanMode = WAN_CONNECTION_VLAN_RESERVE;
        data.ulVlanIdMark = (wan_cfg.wan_vlan_id!=0xFFFF)?0:wan_cfg.wan_vlan_id;
        data.ulMulticastVlan = (wan_cfg.wan_vlan_id > WAN_CONNECTION_MULTICAST_VLAN_MAX)? WAN_CONNECTION_MULTICAST_VLAN_MAX : wan_cfg.wan_vlan_id;
        if (wan_cfg.wan_cos_8021p == 0xFF)
        {
            data.uc8021pMarkEnable = WAN_8021P_MARK_DISABLE;
        }
        else
        {
            data.uc8021pMarkEnable = WAN_8021P_MARK_ENABLE;
            data.uc8021pMark  = wan_cfg.wan_cos_8021p;
        }  
    }
    else
    {
        data.ulMulticastVlan = 0;
        data.ucVlanMode = WAN_CONNECTION_VLAN_DISABLE;
        data.uc8021pMarkEnable = WAN_8021P_MARK_DISABLE;
    }

    data.ulBindPort |=  (wan_cfg.binding_lan & 0xf);// LAN1-LAN4
    data.ulBindPort |= ((hi_uint32)(wan_cfg.binding_ssid << 4) & 0xff0); // SSID1-SSID8
    //printf("data.ulBindPort=0x%x", data.ulBindPort);

    if (data.ucConnectionType  == WAN_CONNECTION_TYPE_IP_ROUTED)
    {
        if ((data.ucIPMode & WAN_CONNECTION_IP_MODE_V6) == WAN_CONNECTION_IP_MODE_V6)
        {
            data.ucDsliteEnable = wan_cfg.wan_DS_Lite_enable;
            if (data.ucDsliteEnable)
            {
                data.ucAftrMode = (wan_cfg.wan_DS_Lite_AFTR_mode==VS_WAN_DSLITE_AFTR_MODE_MANUAL)?WAN_CONNECTION_AFTR_MODE_STATIC:WAN_CONNECTION_AFTR_MODE_AUTO;
                if (data.ucAftrMode == WAN_CONNECTION_AFTR_MODE_STATIC)
                {
                    //wan_cfg.wan_DS_Lite_address_type = 0;/*0:ipv6 1:dns*/
                    HI_OS_MEMCPY_S(data.aucAftr, 64, wan_cfg.wan_DS_Lite_address, 64);
                    //data.aucAftr[sizeof(data.aucAftr)-1] = '\0';
                }
            }
        }
        switch (wan_cfg.wan_connection_mode)
        {
            case VS_WAN_CONNECTION_ADDRESSING_TYPE_STATIC:
                data.ucAddressingType = WAN_CONNECTION_ADDRESSING_TYPE_STATIC;
                if ((data.ucIPMode & WAN_CONNECTION_IP_MODE_V4) == WAN_CONNECTION_IP_MODE_V4)
                {
                    HI_OS_MEMCPY_S(data.aucExternalIPAddress, CM_IP_ADDR_LEN, wan_cfg.wan_ipv4_address, CM_IP_ADDR_LEN);

                    //fyy for bug#15762
                    u32Val = ntohl(~((1u << (32 - wan_cfg.wan_ipv4_mask)) - 1));
                    HI_OS_MEMCPY_S(data.aucSubnetMask, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);

                    HI_OS_MEMCPY_S(data.aucDefaultGateway, CM_IP_ADDR_LEN, wan_cfg.wan_ipv4_default_gateway, CM_IP_ADDR_LEN);

                    igdCmApiIptoChar((hi_uchar8 *)&wan_cfg.wan_ipv4_master_dns, pri_dns, sizeof(pri_dns));
                    igdCmApiIptoChar((hi_uchar8 *)&wan_cfg.wan_ipv4_slave_dns, sec_dns, sizeof(sec_dns));

                    snprintf(data.aucDNSServers, sizeof(data.aucDNSServers), "%s,%s,", pri_dns, sec_dns);
                    //printf("data.aucDNSServers=%s\n", data.aucDNSServers);             
                }  

                if ((data.ucIPMode & WAN_CONNECTION_IP_MODE_V6) == WAN_CONNECTION_IP_MODE_V6)
                {

                    data.ucIPv6AddressOrigin = WAN_CONNECTION_IPV6_ADDR_ORIGIN_STATIC;
                    //printf("data.ucIPv6AddressOrigin=%d\n", data.ucIPv6AddressOrigin);
                    data.ucGetwayAchieve = WAN_CONNECTION_GETWAY_ACHIEVE_STATIC;
                    //printf("data.ucGetwayAchieve=%d\n", data.ucGetwayAchieve);
                    if (wan_cfg.wan_dhcpv6_client_request_prefix_enable)/*DHCP PD*/
                    {
                        data.ucIPv6PrefixDelegationEnabled = WAN_CONNECTION_IPV6_PREFIX_DELEGATION_ENABLE;
                        data.ucIPv6PrefixOrigin = WAN_CONNECTION_IPV6_PREFIX_ORIGIN_PREFIX_DELEGATION;
                    }
                    else
                    {
                        data.ucIPv6PrefixDelegationEnabled = WAN_CONNECTION_IPV6_PREFIX_DELEGATION_DISABLE;
                        data.ucIPv6PrefixOrigin = WAN_CONNECTION_IPV6_PREFIX_ORIGIN_NONE;    
                    }

                    HI_OS_MEMCPY_S(data.aucIPv6Address, 16, wan_cfg.wan_ipv6_address, 16);
                    //igdCmApiIpv6toChar((void *)wan_cfg.wan_ipv6_address, data.aucIPv6Address);
                    //printf("data.aucIPv6Address=%s\n", data.aucIPv6Address);
                    data.ucIPv6AddrPrefix = wan_cfg.wan_ipv6_mask;
                    //printf("data.ucIPv6AddrPrefix=%d\n", data.ucIPv6AddrPrefix);
                    igdCmApiIpv6toChar((void *)wan_cfg.wan_ipv6_default_gateway, data.aucDefaultIPv6Gateway);
                    //printf("data.aucDefaultIPv6Gateway=%s\n", data.aucDefaultIPv6Gateway);
                    data.ucDNSAchieve = WAN_CONNECTION_DNS_ACHIEVE_STATIC;
                    igdCmApiIpv6toChar((void *)wan_cfg.wan_ipv6_master_dns, pri_dnsv6);
                    igdCmApiIpv6toChar((void *)wan_cfg.wan_ipv6_slave_dns, sec_dnsv6);    

                    snprintf(data.aucIPv6DNSServers, sizeof(data.aucIPv6DNSServers), "%s,%s,", pri_dnsv6, sec_dnsv6);
                    //printf("pri_dnsv6=%s, sec_dnsv6=%s, data.aucIPv6DNSServers=%s\n", pri_dnsv6, sec_dnsv6, data.aucIPv6DNSServers);
                }              
                break;
            case VS_WAN_CONNECTION_ADDRESSING_TYPE_DHCP:
                data.ucAddressingType = WAN_CONNECTION_ADDRESSING_TYPE_DHCP;
                if ((data.ucIPMode & WAN_CONNECTION_IP_MODE_V4) == WAN_CONNECTION_IP_MODE_V4)
                {
                    data.ucDNSOverrideAllowed = (wan_cfg.wan_ipv4_request_dns_mode == 1)?WAN_CONNECTION_DNS_OVERRIDE_ALLOWED_ENABLE:WAN_CONNECTION_DNS_OVERRIDE_ALLOWED_DISABLE;
                    if(wan_cfg.wan_ipv4_request_dns_mode == WAN_CONNECTION_DNS_OVERRIDE_ALLOWED_ENABLE)
                    {
                        igdCmApiIptoChar((hi_uchar8 *)&wan_cfg.wan_ipv4_master_dns, pri_dns, sizeof(pri_dns));
                        igdCmApiIptoChar((hi_uchar8 *)&wan_cfg.wan_ipv4_slave_dns, sec_dns, sizeof(sec_dns));

                        snprintf(data.aucDNSServers, sizeof(data.aucDNSServers), "%s,%s,", pri_dns, sec_dns);
                        //printf("data.aucDNSServers=%s\n", data.aucDNSServers);   
                    }
                    data.ucDNSEnabled = data.ucDNSOverrideAllowed;
                }

                if ((data.ucIPMode & WAN_CONNECTION_IP_MODE_V6) == WAN_CONNECTION_IP_MODE_V6)
                {
                    if (wan_cfg.wan_ipv6_request_dns_mode == VS_WAN_DNSV6_STATIC)
                    {
                        data.ucDNSAchieve = WAN_CONNECTION_DNS_ACHIEVE_STATIC;
                        igdCmApiIpv6toChar((void *)wan_cfg.wan_ipv6_master_dns, pri_dnsv6);
                        igdCmApiIpv6toChar((void *)wan_cfg.wan_ipv6_slave_dns, sec_dnsv6);
                        snprintf(data.aucIPv6DNSServers, sizeof(data.aucIPv6DNSServers), "%s,%s,", pri_dnsv6, sec_dnsv6);
                        //printf("pri_dnsv6=%s, sec_dnsv6=%s, data.aucIPv6DNSServers=%s\n", pri_dnsv6, sec_dnsv6, data.aucIPv6DNSServers);
                    }
                    else
                    {
                        data.ucDNSAchieve = (wan_cfg.wan_ipv6_address_mode==VS_WAN_IPV6_ADDRESS_MODE_SLAAC)?WAN_CONNECTION_DNS_ACHIEVE_AUTO:WAN_CONNECTION_DNS_ACHIEVE_DHCPV6;
                    }

                    if (wan_cfg.wan_ipv6_address_mode==VS_WAN_IPV6_ADDRESS_MODE_DHCP)
                    {
                        data.ucIPv6AddressOrigin = WAN_CONNECTION_IPV6_ADDR_ORIGIN_DHCPV6;
                        data.ucIPv6PrefixDelegationEnabled = wan_cfg.wan_dhcpv6_client_request_prefix_enable;/*DHCP PD*/
                        data.ucIPv6PrefixOrigin = wan_cfg.wan_dhcpv6_client_request_prefix_enable?WAN_CONNECTION_IPV6_PREFIX_ORIGIN_PREFIX_DELEGATION:WAN_CONNECTION_IPV6_PREFIX_ORIGIN_NONE;
                    }
                    else
                    {
                        data.ucIPv6AddressOrigin = WAN_CONNECTION_IPV6_ADDR_ORIGIN_AUTO_CONFIGURED;
                        data.ucIPv6PrefixDelegationEnabled = wan_cfg.wan_dhcpv6_client_request_prefix_enable;/*DHCP PD*/
                        data.ucIPv6PrefixOrigin = wan_cfg.wan_dhcpv6_client_request_prefix_enable?WAN_CONNECTION_IPV6_PREFIX_ORIGIN_RA:WAN_CONNECTION_IPV6_PREFIX_ORIGIN_NONE;
                    }
                }
                break;
            case VS_WAN_CONNECTION_ADDRESSING_TYPE_PPPOE:
                data.ucAddressingType = WAN_CONNECTION_ADDRESSING_TYPE_PPPOE;
                data.ucProxyEnable = wan_cfg.wan_pppoe_proxy_enable;
                data.ulDialDemend = 1200;
                data.ucPPPAuthProtocol = WAN_CONNECTION_PPP_AUTH_PROTOCOL_AUTO;
                if (wan_cfg.wan_pppoe_mode == VS_WAN_PPPOE_DIAL_MODE_ALWAYS_ON)
                {
                    data.ucConnectionTrigger = WAN_CONNECTION_TRIGGER_ALWAYS_ON;
                }
                else
                {
                    data.ucConnectionTrigger = WAN_CONNECTION_TRIGGER_ON_COMMAND;
                }
                HI_OS_MEMCPY_S(data.aucUsername, sizeof(data.aucUsername), wan_cfg.wan_pppoe_username, sizeof(wan_cfg.wan_pppoe_username));
                HI_OS_MEMCPY_S(data.aucPassword, sizeof(data.aucPassword), wan_cfg.wan_pppoe_password, sizeof(wan_cfg.wan_pppoe_password));
                HI_OS_MEMCPY_S(data.aucPppoeServiceName, sizeof(data.aucPppoeServiceName), wan_cfg.wan_pppoe_servicename, sizeof(wan_cfg.wan_pppoe_servicename));
                data.aucUsername[sizeof(data.aucUsername)-1] = '\0';
                data.aucPassword[sizeof(data.aucPassword)-1] = '\0';
                data.aucPppoeServiceName[sizeof(data.aucPppoeServiceName)-1] = '\0';

                if ((data.ucIPMode & WAN_CONNECTION_IP_MODE_V6) == WAN_CONNECTION_IP_MODE_V6)
                {
                    if (wan_cfg.wan_ipv6_address_mode==VS_WAN_IPV6_ADDRESS_MODE_DHCP)
                    {
                        data.ucDNSAchieve = WAN_CONNECTION_DNS_ACHIEVE_DHCPV6;
                        data.ucIPv6AddressOrigin = WAN_CONNECTION_IPV6_ADDR_ORIGIN_DHCPV6;
                        data.ucIPv6PrefixDelegationEnabled = wan_cfg.wan_dhcpv6_client_request_prefix_enable;/*DHCP PD*/
                        data.ucIPv6PrefixOrigin = wan_cfg.wan_dhcpv6_client_request_prefix_enable?WAN_CONNECTION_IPV6_PREFIX_ORIGIN_PREFIX_DELEGATION:WAN_CONNECTION_IPV6_PREFIX_ORIGIN_NONE;
                    }
                    else
                    {
                        data.ucIPv6AddressOrigin = WAN_CONNECTION_IPV6_ADDR_ORIGIN_AUTO_CONFIGURED;
                        data.ucDNSAchieve = WAN_CONNECTION_DNS_ACHIEVE_AUTO;
                        data.ucIPv6PrefixDelegationEnabled = wan_cfg.wan_dhcpv6_client_request_prefix_enable;/*DHCP PD*/
                        data.ucIPv6PrefixOrigin = wan_cfg.wan_dhcpv6_client_request_prefix_enable?WAN_CONNECTION_IPV6_PREFIX_ORIGIN_RA:WAN_CONNECTION_IPV6_PREFIX_ORIGIN_NONE;
                    }
                }
                data.ucDNSEnabled = 0;
                data.ucDNSOverrideAllowed = 0;
                snprintf(data.aucDNSServers, sizeof(data.aucDNSServers), ",,");
                break;
            default:    
                printf("wan_cfg.wan_connection_mode[%d], unknown address type\n", wan_cfg.wan_connection_mode);             
                break; 
        }
    }
    else //bridge
    {
        data.ucIPv6PrefixOrigin = WAN_CONNECTION_IPV6_PREFIX_ORIGIN_NONE;
        data.ucAddressingType = WAN_CONNECTION_ADDRESSING_TYPE_DHCP;
        data.ucLanInterfaceDHCPEnable = WAN_CONNECTION_LAN_INTERFACE_DISABLE;
    }

    if (data.ucAddressingType == WAN_CONNECTION_ADDRESSING_TYPE_PPPOE &&
        data.ucConnectionType == WAN_CONNECTION_INDEX_TYPE_IP_ROUTED) 
    {
        data.ulMaxMRUSize = wan_cfg.MTU;
    } 
    else 
    {
        data.ulMaxMTUSize = wan_cfg.MTU;
    }

#if defined(CONFIG_PLATFORM_OPENWRT)
    ret = 0;
	ret = HI_IPC_CALL("hi_sml_wan_connection_attr_tab_add", &data);
#else
    ret = igdCmConfAdd(IGD_WAN_CONNECTION_ATTR_TAB,(unsigned char *)&data,sizeof(data));
#endif

    if(ret != 0)
    {
        printf("igdCmConfAdd IGD_WAN_CONNECTION_ATTR_TAB ret : %d\n", ret);
        return HI_RET_FAIL;
    }

    return HI_RET_SUCC;
}

hi_void *wan_add_func(hi_void *arg)
{
    int ret = 0, i = 0;
    vs_ctcoam_wan_dual_stack_configuration_s *wan_config = (vs_ctcoam_wan_dual_stack_configuration_s *)arg;

    vs_oam_delete_all_wan();

    for(i = 0; i < global_wan_num && i <= VS_OAM_WAN_MAX; i++)
    {
        printf("\n[oam] Prepare to add wan_index_%d ..\n", wan_config[i].wan_index);
        ret = vs_oam_wan_add(wan_config[i]);
        if(ret != 0)
        {
            printf("vs_oam_wan_add fail..\n");
        }
    }
#if 1
	ret = HI_IPC_CALL("hi_sml_wan_connection_attr_commit");
    if(ret != 0)
    {
        printf("hi_sml_wan_connection_attr_commit fail..\n");
    }
#endif
    wan_config_running = 0;
    return NULL;
}

hi_int32 vs_oam_wan_cfg_set(unsigned char set_wan_num, unsigned char *p_data, unsigned int data_len, unsigned char branch, unsigned short leaf)
{
    hi_uchar8 uc_change = 0;
    int ret = 0, i = 0;
    unsigned char packet_slicing = 0;
    vs_ctcoam_wan_dual_stack_configuration_s ram_wan_config[VS_OAM_WAN_MAX];
    hi_ushort16 ram_wan_total_num = 0;

    /* read the mib to ram */
    ret = vs_oam_wan_cfg_to_ram(&ram_wan_total_num, ram_wan_config);
    if(ret != HI_RET_SUCC)
    {
        printf("vs_oam_wan_cfg_to_ram error\n");
        wan_config_running = 0;
        return HI_RET_FAIL;
    }

    for(i = 0; i < VS_OAM_WAN_MAX; i++)
        HI_OS_MEMSET_S(&wan_config[i], sizeof(wan_config[i]), 0, sizeof(wan_config[i]));
    
    /* combine the wan data */
    for(i = 0; i < set_wan_num; i++)
    {
        packet_slicing = packet_slicing_for_receive(&wan_config[i], sizeof(wan_config[i]), p_data, data_len, branch, leaf);
        if(packet_slicing == 0)
        {
            HI_OS_MEMSET_S(&wan_config[i], sizeof(wan_config[i]), 0, sizeof(wan_config[i]));
            HI_OS_MEMCPY_S(&wan_config[i], sizeof(wan_config[i]), p_data, sizeof(wan_config[i]));
        }
        //printf("packet_slicing = %d\n", packet_slicing);
        p_data = p_data + sizeof(wan_config[i]) + ((packet_slicing > 0) ? (packet_slicing - 1)*4 : 0);
        data_len = data_len - sizeof(wan_config[i]) - ((packet_slicing > 0) ? (packet_slicing - 1)*4 : 0);

        if(wan_config[i].wan_type == VS_WAN_BRIDGE)//olt bug, NAT should be disabled in brigde mode
            wan_config[i].wan_NAT_enable = 0;

        wan_config[i].wan_dhcpv6_client_request_address_enable = 0;//functional conflict, set to 0

        wan_config[i].wan_index = (hi_ushort16)ntohs(wan_config[i].wan_index);
        wan_config[i].wan_service_mode = (hi_ushort16)ntohs(wan_config[i].wan_service_mode);
        wan_config[i].wan_type = (hi_ushort16)ntohs(wan_config[i].wan_type);
        wan_config[i].wan_vlan_id = (hi_ushort16)ntohs(wan_config[i].wan_vlan_id);
        wan_config[i].wan_cos_8021p = (hi_ushort16)ntohs(wan_config[i].wan_cos_8021p);
        wan_config[i].wan_ip_protocol = (hi_ushort16)ntohs(wan_config[i].wan_ip_protocol);
        wan_config[i].wan_connection_mode = (hi_ushort16)ntohs(wan_config[i].wan_connection_mode);
        wan_config[i].wan_ipv4_mask = ntohl(wan_config[i].wan_ipv4_mask);
        wan_config[i].wan_ipv6_mask = ntohl(wan_config[i].wan_ipv6_mask);
        wan_config[i].wan_pppoe_mode = (hi_ushort16)ntohs(wan_config[i].wan_pppoe_mode);
        wan_config[i].wan_6rd_ipv4_mask_len = ntohl(wan_config[i].wan_6rd_ipv4_mask_len);
        wan_config[i].wan_6rd_ipv6_prefix_len = ntohl(wan_config[i].wan_6rd_ipv6_prefix_len);
        wan_config[i].vlan_mode = (hi_ushort16)ntohs(wan_config[i].vlan_mode);
        wan_config[i].translation_vlan = (hi_ushort16)ntohs(wan_config[i].translation_vlan);
        wan_config[i].translation_cos = (hi_ushort16)ntohs(wan_config[i].translation_cos);
        wan_config[i].TPID = (hi_ushort16)ntohs(wan_config[i].TPID);
        wan_config[i].wan_svlan_id = (hi_ushort16)ntohs(wan_config[i].wan_svlan_id);
        wan_config[i].wan_svlan_cos = (hi_ushort16)ntohs(wan_config[i].wan_svlan_cos);
        wan_config[i].wan_state = (hi_ushort16)ntohs(wan_config[i].wan_state);
        wan_config[i].MTU = (hi_ushort16)ntohs(wan_config[i].MTU);

        wan_config[i].wan_pppoe_username[sizeof(wan_config[i].wan_pppoe_username)-1] = '\0';
        wan_config[i].wan_pppoe_password[sizeof(wan_config[i].wan_pppoe_password)-1] = '\0';
        wan_config[i].wan_pppoe_servicename[sizeof(wan_config[i].wan_pppoe_servicename)-1] = '\0';

        printf_oam_wandata(&wan_config[i]);
    }

    /* compare the data */
    if(set_wan_num != ram_wan_total_num)
    {
        printf("WAN number change\n");
        uc_change = 1;
    }
    else
    {
        /* Compare ram cfg and olt cfg*/
        for(i = 0; i < set_wan_num; i++)
        {
            if(memcmp(&wan_config[i], &ram_wan_config[i], sizeof(vs_ctcoam_wan_dual_stack_configuration_s)) != 0)
            {
                printf("\nwan_index_%d cfg has change\n", wan_config[i].wan_index);
                printf_oam_wandata(&ram_wan_config[i]);
                printf_oam_wandata(&wan_config[i]);
                uc_change = 1;
            }
        }
    }

    if(uc_change)
    {
        global_wan_num = set_wan_num;
        printf("\n[oam] Prepare to del all WAN ..\n");
        ret = hi_os_pthread_create(&wan_add_thread, NULL, wan_add_func, (hi_void *)wan_config);
        if(ret != 0)
        {
            printf("hi_os_pthread_create fail..\n");
            wan_config_running = 0;
            return HI_RET_FAIL;
        }
    }
    else
    {
        wan_config_running = 0;
        printf("\nSame wan config, not apply\n");
    }

	return HI_RET_SUCC;
}

void uncompress_data(Bytef *in_compress_data, uLong in_compress_len, Bytef *out_uncompress_data, uLongf *out_uncompress_len)
{
    int err = 0;

    if (in_compress_data)
    {
        err = uncompress(out_uncompress_data, out_uncompress_len, in_compress_data, in_compress_len);
        if (err)
        {
            printf("[%s,%d] uncompress wan data failed!!! err code = %d\n", __FUNCTION__, __LINE__, err);

        }
        else
        {
            printf("[%s,%d] uncompress wan data success!!!\n", __FUNCTION__, __LINE__);
        }
    }
    else
    {
        printf("[%s,%d] error, input_compress_len = 0\n", __FUNCTION__, __LINE__);
    }
}

uint32_t vs_ctcoam_set_wan_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    uLong data_len = 10*1024;
    unsigned int p_data_len = 0;
    unsigned char uncompressed_data[10*1024];
    unsigned char *p_data = uncompressed_data, *p_data_offect = NULL;
    unsigned char wan_total_num = 0;
    unsigned short uncompress_tlv_leaf = 0, compress_tlv_length = 0;
    unsigned char compressed_data[1024];
    unsigned char packet_slicing = 0;
    unsigned short tlv_leaf = 0;
    hi_ctcoam_tlv_s *tlv_head = (hi_ctcoam_tlv_s *)(pv_inmsg - 4);

    if(pv_inmsg == NULL)
        return HI_RET_INVALID_PARA;

    wan_config_running = 1;

    if(tlv_head != NULL)
    {
        tlv_leaf = tlv_head->us_leaf;
        tlv_leaf = (hi_ushort16)ntohs(tlv_leaf);
    }
    else
    {
        printf("[%s,%d]error, tlv_head is null\n", __FUNCTION__, __LINE__);
        goto VS_OAM_WAN_FAIL;
    }

    /* 1. calculated the compressed packet length */
    printf_oam_hex(pv_inmsg, 1496);
    if(tlv_head->uc_length == 0)//the compressed packet is sliced
        compress_tlv_length = calculate_packet_length(pv_inmsg, 1496, tlv_head->uc_branch, tlv_leaf);
    else
        compress_tlv_length = tlv_head->uc_length;

    /* 2. copy the compressed data  */
    HI_OS_MEMSET_S(&compressed_data, sizeof(compressed_data), 0, sizeof(compressed_data));
    packet_slicing = packet_slicing_for_receive(&compressed_data, sizeof(compressed_data), pv_inmsg, 1496, tlv_head->uc_branch, tlv_leaf);
    if(packet_slicing == 0)//doesn't has the tlv header
    {
        HI_OS_MEMSET_S(&compressed_data, sizeof(compressed_data), 0, sizeof(compressed_data));
        HI_OS_MEMCPY_S(&compressed_data, sizeof(compressed_data), pv_inmsg, compress_tlv_length);
    }
    printf_oam_hex(&compressed_data, 1496);

    /* 3. uncompress the compressed data, remove the tlv header and wan number bytes (remove 6 bytes, e.g. c7 22 ff 00 00 03) */
    uncompress_data(compressed_data, compress_tlv_length, p_data, &data_len);

    printf_oam_hex(p_data, data_len);
    if(data_len > 0 && data_len <= 128 + 4)//only has one packet
    {
        p_data_len = *(p_data + 3) - 2;//tlv container length - wan number bytes(2 bytes)
        data_len = p_data_len;
        wan_total_num = *(p_data + 5);//total wan number
    }
    else if(data_len > 128 + 4)
    {
        data_len = data_len - 6;
        wan_total_num = *(p_data + 5);//total wan number
    }
    else
    {
        printf("[%s,%d]error, data_len = %lu\n", __FUNCTION__, __LINE__, data_len);
        goto VS_OAM_WAN_FAIL;
    }
    p_data_offect = p_data + 6;//the pos after wan_total_num
    uncompress_tlv_leaf = (*(p_data + 1) << 8) | *(p_data + 2);
    printf_oam_hex(p_data_offect, data_len);

    /* 4. combine the data and set to mib */
    vs_oam_wan_cfg_set(wan_total_num, p_data_offect, data_len, tlv_head->uc_branch, uncompress_tlv_leaf);

    return HI_RET_SUCC;
/*Add by fyy for bug#16254*/
VS_OAM_WAN_FAIL:
    wan_config_running = 0;
    return HI_RET_FAIL;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

