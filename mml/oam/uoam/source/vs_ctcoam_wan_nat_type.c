#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "vs_ctcoam_wan_nat_type.h"

uint32_t vs_ctcoam_get_wan_nat_type(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
	IgdWanNatTypeTab IgdWanNatType;
	vs_ctcoam_wan_nat_type_s *object = HI_NULL;
    int ret;
		
	if (pv_outmsg == HI_NULL)
		return HI_RET_INVALID_PARA;
	object = (vs_ctcoam_wan_nat_type_s *)pv_outmsg;

	(void)memset_s((int8_t *)&IgdWanNatType, sizeof(IgdWanNatTypeTab), 0, sizeof(IgdWanNatTypeTab));
	IgdWanNatType.ulBitmap |= WAN_NAT_TYPE_ATTR_MASK_BIT0_NAT_TYPE;
    
	ret = igdCmConfGet(IGD_WAN_NAT_TYPE_ATTR_TAB, (uint8_t *)&IgdWanNatType, sizeof(IgdWanNatType));
	if (ret != HI_RET_SUCC) {
		printf("igdCmConfGet IGD_WAN_NAT_TYPE_ATTR_TAB ret : %d \r\n", ret);
		return HI_RET_INVALID_PARA;
	} 

    switch (IgdWanNatType.aucNatType)
    {
    case WAN_NAT_TYPE1:
        object->nat_type = (hi_ushort16)htons(VS_OAM_WAN_NAT_TYPE_NAT1);
        break;
    case WAN_NAT_TYPE2:
        object->nat_type = (hi_ushort16)htons(VS_OAM_WAN_NAT_TYPE_NAT2);
        break;
    case WAN_NAT_TYPE4:
        object->nat_type = (hi_ushort16)htons(VS_OAM_WAN_NAT_TYPE_NAT4);
        break;
    }
    return HI_RET_SUCC;
}

uint32_t vs_ctcoam_set_wan_nat_type(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
	vs_ctcoam_wan_nat_type_s *data_cfg = HI_NULL;
    IgdWanNatTypeTab IgdWanNatType;
    int ret;
    uint32_t entry_num = 0;

	if(pv_inmsg == HI_NULL)
		return HI_RET_INVALID_PARA;
	
	data_cfg = (vs_ctcoam_wan_nat_type_s *)pv_inmsg;

	(void)memset_s((int8_t *)&IgdWanNatType, sizeof(IgdWanNatTypeTab), 0, sizeof(IgdWanNatTypeTab));
	IgdWanNatType.ulBitmap |= WAN_NAT_TYPE_ATTR_MASK_BIT0_NAT_TYPE;
    
    switch (ntohs(data_cfg->nat_type))
    {
    case VS_OAM_WAN_NAT_TYPE_NAT1:
        IgdWanNatType.aucNatType = WAN_NAT_TYPE1;
        break;
    case VS_OAM_WAN_NAT_TYPE_NAT2:
        IgdWanNatType.aucNatType = WAN_NAT_TYPE2;
        break;
    case VS_OAM_WAN_NAT_TYPE_NAT3:
    case VS_OAM_WAN_NAT_TYPE_NAT4:
        IgdWanNatType.aucNatType = WAN_NAT_TYPE4;
        break;
    }

	ret = igdCmConfGetEntryNum(IGD_WAN_NAT_TYPE_ATTR_TAB, &entry_num);
	if (ret != HI_RET_SUCC) {
		printf("igdCmConfGetEntryNum fial with ret: %d\n", ret);
		return HI_RET_INVALID_PARA;
	}
    
	if (entry_num == 0) {
		ret = igdCmConfAdd(IGD_WAN_NAT_TYPE_ATTR_TAB, (uint8_t *)&IgdWanNatType, sizeof(IgdWanNatTypeTab));
		printf("igdCmConfAdd with ret [%d], index [0].", ret);
        return HI_RET_INVALID_PARA;
	} else {
		ret = igdCmConfSet(IGD_WAN_NAT_TYPE_ATTR_TAB, (uint8_t *)&IgdWanNatType, sizeof(IgdWanNatTypeTab));
	}
    
	if (ret != HI_RET_SUCC) {
		printf("igdCmConfSet IGD_WAN_NAT_TYPE_ATTR_TAB ret: %d\r\n", ret);
		return HI_RET_INVALID_PARA;
	} 
    return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

