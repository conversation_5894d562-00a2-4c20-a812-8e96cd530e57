#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "vs_ctcoam_sip_parameter_configuration.h"


uint32_t vs_ctcoam_get_sip_parameter_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    hi_uint32 u32Val = 0;
    IgdVoiceSipBasicAttrConfTab data;
    IgdVoiceAdvancedAttrConfTab data2;
    vs_ctcoam_sip_parameter_configuration_s *cfg = NULL; 

    if (pv_outmsg == NULL)
        return HI_RET_INVALID_PARA;
    
    cfg = (vs_ctcoam_sip_parameter_configuration_s *)pv_outmsg;

    HI_OS_MEMSET_S(cfg, sizeof(vs_ctcoam_sip_parameter_configuration_s), 0, sizeof(vs_ctcoam_sip_parameter_configuration_s));
    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));
    HI_OS_MEMSET_S(&data2, sizeof(data2), 0, sizeof(data2));

    data.ulBitmap  |= SIP_BASIC_ATTR_MASK_ALL;
    data.ulBitmap1 |= SIP_BASIC_ATTR_MASK1_ALL;
    ret = igdCmConfGet(IGD_VOICE_SIP_BASIC_ATTR_TAB, (unsigned char *)&data, sizeof(data));
    if(ret != 0)
    {
        printf("[%s,%d] igdCmConfGet IGD_VOICE_SIP_BASIC_ATTR_TAB ret : %d \r\n", __FUNCTION__, __LINE__, ret);
        return ret;
    }

    data2.ulBitmap  |= VOICE_ADV_ATTR_MASK_ALL;
    ret = igdCmConfGet(IGD_VOICE_ADVANCED_ATTR_TAB, (unsigned char *)&data2, sizeof(data2));
    if(ret != 0)
    {
        printf("[%s,%d] igdCmConfGet IGD_VOICE_ADVANCED_ATTR_TAB ret : %d \r\n", __FUNCTION__, __LINE__, ret);
        return ret;
    }

    cfg->sip_local_port = data2.ulSipLocalPort;

    u32Val = igdCmApiChartoIp(data.aucProxyServer);
    HI_OS_MEMCPY_S(&cfg->primary_sip_proxy_service_ip, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
    cfg->primary_sip_proxy_service_port = data.ulProxyServerPort;

    u32Val = igdCmApiChartoIp(data.aucStandbyProxyServer);
    HI_OS_MEMCPY_S(&cfg->backup_sip_proxy_service_ip, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
    cfg->backup_sip_proxy_service_port = data.ulStandbyProxyServerPort;

    u32Val = igdCmApiChartoIp(data.aucRegistrarServer);
    HI_OS_MEMCPY_S(&cfg->primary_sip_register_service_ip, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
    cfg->primary_sip_register_service_port = data.ulRegistrarServerPort;

    u32Val = igdCmApiChartoIp(data.aucStandbyRegistrarServer);
    HI_OS_MEMCPY_S(&cfg->backup_sip_register_service_ip, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
    cfg->backup_sip_register_service_port = data.ulStandbyRegistrarServerPort;

    u32Val = igdCmApiChartoIp(data.aucOutBoundProxyServer);
    HI_OS_MEMCPY_S(&cfg->primary_outbound_proxy_service_ip, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
    cfg->primary_outbound_proxy_service_port = data.ulOutBoundProxyServerPort;

    cfg->sip_register_expire = data2.ulRegisterExpireTime;
    cfg->heartbeat_switch = data.ucHeartbeatSwitch;
    cfg->heartbeat_cycle = data.lHeartbeatCycle;
    cfg->heartbeat_count = data.llHeartbeatCount;

    /* net address change */
    cfg->sip_local_port = (hi_ushort16)htons(cfg->sip_local_port);
    cfg->primary_sip_proxy_service_port = (hi_ushort16)htons(cfg->primary_sip_proxy_service_port);
    cfg->backup_sip_proxy_service_port = (hi_ushort16)htons(cfg->backup_sip_proxy_service_port);
    cfg->primary_sip_register_service_port = (hi_ushort16)htons(cfg->primary_sip_register_service_port);
    cfg->backup_sip_register_service_port = (hi_ushort16)htons(cfg->backup_sip_register_service_port);
    cfg->primary_outbound_proxy_service_port = (hi_ushort16)htons(cfg->primary_outbound_proxy_service_port);
    cfg->sip_register_expire = htonl(cfg->sip_register_expire);
    cfg->heartbeat_cycle = (hi_ushort16)htons(cfg->heartbeat_cycle);
    cfg->heartbeat_count = (hi_ushort16)htons(cfg->heartbeat_count);


    return HI_RET_SUCC;
}

//use branch=0x02 leaf=0x000e to instead above
uint32_t vs_ctcoam_set_sip_parameter_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    hi_uint32 u32Val = 0;
    char ip_address[4];
    IgdVoiceSipBasicAttrConfTab data;
    IgdVoiceAdvancedAttrConfTab data2;
    vs_ctcoam_sip_parameter_configuration_s *cfg = NULL; 

    if (pv_inmsg == NULL)
        return HI_RET_INVALID_PARA;
    
    cfg = (vs_ctcoam_sip_parameter_configuration_s *)pv_inmsg;

    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));
    HI_OS_MEMSET_S(&data2, sizeof(data2), 0, sizeof(data2));

    data.ulBitmap  |= SIP_BASIC_ATTR_MASK_BIT0_PROXY_SERVER|SIP_BASIC_ATTR_MASK_BIT1_PROXY_SERVER_PORT|
                      SIP_BASIC_ATTR_MASK_BIT3_REG_SERVER|SIP_BASIC_ATTR_MASK_BIT4_REG_SERVER_PORT|
                      SIP_BASIC_ATTR_MASK_BIT6_OUTBOUND_PROXY_SERVER|SIP_BASIC_ATTR_MASK_BIT7_OUTBOUND_PROXY_SERVER_PORT|
                      SIP_BASIC_ATTR_MASK_BIT9_STANDBY_PROXY_SERVER|SIP_BASIC_ATTR_MASK_BIT10_STANDBY_PROXY_SERVER_PORT|
                      SIP_BASIC_ATTR_MASK_BIT12_STANDBY_REG_SERVER|SIP_BASIC_ATTR_MASK_BIT13_STANDBY_REG_SERVER_PORT|
                      SIP_BASIC_ATTR_MASK_BIT24_HEARTBEAT_SWITCH|SIP_BASIC_ATTR_MASK_BIT25_HEARTBEAT_CYCLE|
                      SIP_BASIC_ATTR_MASK_BIT26_HEARTBEAT_COUNT;
    data2.ulBitmap |= VOICE_ADV_ATTR_MASK_BIT4_SIP_LOCAL_PORT|VOICE_ADV_ATTR_MASK_BIT5_REG_EXPIRE_TIME;


    /* net address change */
    cfg->sip_local_port = (hi_ushort16)htons(cfg->sip_local_port);
    cfg->primary_sip_proxy_service_port = (hi_ushort16)htons(cfg->primary_sip_proxy_service_port);
    cfg->backup_sip_proxy_service_port = (hi_ushort16)htons(cfg->backup_sip_proxy_service_port);
    cfg->primary_sip_register_service_port = (hi_ushort16)htons(cfg->primary_sip_register_service_port);
    cfg->backup_sip_register_service_port = (hi_ushort16)htons(cfg->backup_sip_register_service_port);
    cfg->primary_outbound_proxy_service_port = (hi_ushort16)htons(cfg->primary_outbound_proxy_service_port);
    cfg->sip_register_expire = htonl(cfg->sip_register_expire);
    cfg->heartbeat_cycle = (hi_ushort16)htons(cfg->heartbeat_cycle);
    cfg->heartbeat_count = (hi_ushort16)htons(cfg->heartbeat_count);


    data2.ulSipLocalPort = cfg->sip_local_port;

    HI_OS_MEMCPY_S(ip_address, CM_IP_ADDR_LEN, &cfg->primary_sip_proxy_service_ip, CM_IP_ADDR_LEN);
    u32Val = igdCmApiChartoIp(ip_address);
    HI_OS_MEMCPY_S(&data.aucProxyServer, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
    data.ulProxyServerPort = cfg->primary_sip_proxy_service_port;

    HI_OS_MEMCPY_S(ip_address, CM_IP_ADDR_LEN, &cfg->backup_sip_proxy_service_ip, CM_IP_ADDR_LEN);
    u32Val = igdCmApiChartoIp(ip_address);
    HI_OS_MEMCPY_S(&data.aucStandbyProxyServer, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
    data.ulStandbyProxyServerPort = cfg->backup_sip_proxy_service_port;

    HI_OS_MEMCPY_S(ip_address, CM_IP_ADDR_LEN, &cfg->primary_sip_register_service_ip, CM_IP_ADDR_LEN);
    u32Val = igdCmApiChartoIp(ip_address);
    HI_OS_MEMCPY_S(&data.aucRegistrarServer, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
    data.ulRegistrarServerPort = cfg->primary_sip_register_service_port;

    HI_OS_MEMCPY_S(ip_address, CM_IP_ADDR_LEN, &cfg->backup_sip_register_service_ip, CM_IP_ADDR_LEN);
    u32Val = igdCmApiChartoIp(ip_address);
    HI_OS_MEMCPY_S(&data.aucStandbyRegistrarServer, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
    data.ulStandbyRegistrarServerPort = cfg->backup_sip_register_service_port;

    HI_OS_MEMCPY_S(ip_address, CM_IP_ADDR_LEN, &cfg->primary_outbound_proxy_service_ip, CM_IP_ADDR_LEN);
    u32Val = igdCmApiChartoIp(ip_address);
    HI_OS_MEMCPY_S(&data.aucOutBoundProxyServer, CM_IP_ADDR_LEN, &u32Val, CM_IP_ADDR_LEN);
    data.ulOutBoundProxyServerPort = cfg->primary_outbound_proxy_service_port;

    data2.ulRegisterExpireTime = cfg->sip_register_expire;
    data.ucHeartbeatSwitch = cfg->heartbeat_switch;
    data.lHeartbeatCycle = cfg->heartbeat_cycle;
    data.llHeartbeatCount = cfg->heartbeat_count;

    ret = igdCmConfSet(IGD_VOICE_SIP_BASIC_ATTR_TAB, (unsigned char *)&data, sizeof(data));
    if(ret != 0)
    {
        printf("[%s,%d] igdCmConfSet IGD_VOICE_SIP_BASIC_ATTR_TAB ret : %d \r\n", __FUNCTION__, __LINE__, ret);
        return ret;
    }

    
    ret = igdCmConfSet(IGD_VOICE_ADVANCED_ATTR_TAB, (unsigned char *)&data2, sizeof(data2));
    if(ret != 0)
    {
        printf("[%s,%d] igdCmConfSet IGD_VOICE_ADVANCED_ATTR_TAB ret : %d \r\n", __FUNCTION__, __LINE__, ret);
        return ret;
    }

    return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

