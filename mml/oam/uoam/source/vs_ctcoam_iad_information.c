#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "vs_ctcoam_iad_information.h"


uint32_t vs_ctcoam_get_iad_information(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    vs_ctcoam_iad_information_s *cfg = NULL;
    IgdVoiceIadInfoTab data;
    
    if (pv_outmsg == NULL)
        return HI_RET_INVALID_PARA;

    cfg = (vs_ctcoam_iad_information_s *)pv_outmsg;

    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));
    ret = igdCmConfGet(IGD_VOICE_IAD_INFO_TAB,(unsigned char *)&data,sizeof(data));
    if(ret != 0)
    {
        printf("igdCmConfGet IGD_VOICE_IAD_INFO_TAB ret : %d \r\n", ret);
        return HI_RET_FAIL;
    }

    HI_OS_MEMCPY_S((void *)cfg->mac, sizeof(cfg->mac), data.aucMACAddress, sizeof(data.aucMACAddress));
    cfg->iad_protocal = data.ulProtocolSupported;
    HI_OS_MEMCPY_S((void *)cfg->iad_software_version, sizeof(cfg->iad_software_version), data.ucIadSoftwareVersion, sizeof(data.ucIadSoftwareVersion));
    HI_OS_MEMCPY_S((void *)cfg->iad_software_version_time, sizeof(cfg->iad_software_version_time), data.ucIadSoftwareTime, sizeof(data.ucIadSoftwareTime));
    cfg->pots_num = data.ulVoipUserCount;

    return HI_RET_SUCC;
}



#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

