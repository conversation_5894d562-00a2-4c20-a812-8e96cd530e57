/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  File Name     : hi_ctcoam_manage.c
  Version       : Initial Draft
  Created       : 2013/6/19
  Last Modified :
  Description   : ctcoam适配层接口
******************************************************************************/
#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */


#include "hi_uspace.h"
#include "hi_timer.h"

#include "hi_sysinfo.h"
#include "hi_oam_common.h"

#include "hi_oam_api.h"
#include "hi_oam_adapter.h"
#include "hi_ctcoam_proc.h"
#include "hi_ctcoam_tree.h"
#include "hi_ctcoam_manage.h"
#include "hi_ctcoam_performance.h"
#include "hi_ctcoam_alarm.h"
#include "hi_ipc.h"
//#include "hi_kernel_pmu_epon.h"

#include "hi_oam_logger.h"

#include <math.h>


#define PON_FUNC_CLASSIFICATION(szComment)  1


/*----------------------------------------------*
 * external variables                           *
 *----------------------------------------------*/
extern hi_oam_monitoring_data_s g_st_acc_statistics_data[HI_OAM_STAT_PORT_NUM];
extern hi_oam_monitoring_data_s g_st_pre_statistics_data[HI_OAM_STAT_PORT_NUM];
/*----------------------------------------------*
 * internal routine prototypes                  *
 *----------------------------------------------*/
/*刷新特定帧长测试使能模式下Report上报值*/

static hi_uint32 hi_ctcoam_tapi_dba_refresh_report(hi_void);
static hi_uint32 hi_ctcoam_dba_set_para_chk(hi_ctcoam_dba_msg_s *pst_dba_msg, hi_uchar8 uc_inmsglen);
static hi_void   hi_ctcoam_flow_list_one_container(hi_ctcoam_container_hdr_s *pst_container,
        hi_uchar8 uc_len, hi_uchar8 uc_rule_num);
static hi_void   hi_ctcoam_rule_get(oam_clasmark_rulebody_t *pst_src,
                                    oam_clasmark_fieldbody_t *pst_src_field,
                                    hi_ctcoam_classify_rule_connect_s *pst_dst);
static hi_void   hi_ctcoam_rule_set(hi_ctcoam_classify_rule_connect_s *pst_src,
                                    oam_clasmark_rulebody_t *pst_dst,
                                    oam_clasmark_fieldbody_t *pst_dst_field);
static hi_uint32 hi_ctcoam_dba_threshold_get_response(hi_uint32 ui_llid, hi_uint32 ui_exoam_type, hi_ctcoam_dba_capability_s *pst_cap);
static hi_void   hi_ctcoam_set_qset(hi_void *pv_dbamsg, hi_ctcoam_dba_capability_s *pst_cap);
static hi_uint32 hi_ctcoam_dba_threshold_set_response(hi_uint32 ui_llid, hi_uchar8 uc_setack, hi_int32 ui_exoam_type, hi_void *pv_inmsg, hi_uint32 ui_inmsglen);

static hi_uint32 hi_ctcoam_get_vlan_transparent_cfg(hi_void *pv_outmsg, portAllService *pst_cfg, hi_uchar8 *puc_len);
static hi_uint32 hi_ctcoam_get_vlan_tag_cfg(hi_void *pv_outmsg, portAllService *pst_cfg, hi_uchar8 *puc_len);
static hi_uint32 hi_ctcoam_get_vlan_translation_cfg(hi_void *pv_outmsg, portAllService *pst_cfg, hi_uchar8 *puc_len);
static hi_uint32 hi_ctcoam_get_vlan_aggr_cfg(hi_void *pv_outmsg, portAllService *pst_cfg, hi_uchar8 *puc_len);
static hi_uint32 hi_ctcoam_get_vlan_trunk_cfg(hi_void *pv_outmsg, portAllService *pst_cfg, hi_uchar8 *puc_len);
static hi_uint32 hi_ctcoam_set_vlan_transparent_cfg(hi_void *pv_inmsg, hi_uchar8 uc_paralen, portAllService *pst_cfg);
static hi_uint32 hi_ctcoam_set_vlan_tag_cfg(hi_void *pv_inmsg, hi_uchar8 uc_paralen, portAllService *pst_cfg);
static hi_uint32 hi_ctcoam_set_vlan_translation_cfg(hi_void *pv_inmsg, hi_uchar8 uc_paralen, portAllService *pst_cfg);
static hi_uint32 hi_ctcoam_set_vlan_aggr_cfg(hi_void *pv_inmsg, hi_uchar8 uc_paralen, portAllService *pst_cfg);
static hi_uint32 hi_ctcoam_set_vlan_trunk_cfg(hi_void *pv_inmsg, hi_uchar8 uc_paralen, portAllService *pst_cfg);


static hi_uint32 hi_ctcoam_list_multi_vlan(hi_void *pv_outmsg, portAllService *pst_cfg, hi_uchar8 *puc_len);
static hi_uint32 hi_ctcoam_clr_multi_vlan(hi_void *pv_inmsg, hi_uchar8 uc_paralen, portAllService *pst_cfg);
static hi_uint32 hi_ctcoam_del_multi_vlan(hi_void *pv_inmsg, hi_uchar8 uc_paralen, portAllService *pst_cfg);
static hi_uint32 hi_ctcoam_add_multi_vlan(hi_void *pv_inmsg, hi_uchar8 uc_paralen, portAllService *pst_cfg);

/*----------------------------------------------*
* module-wide global variables                 *
*----------------------------------------------*/
/* EMAC的一些能力,以备EXOAM查询 */

hi_ctcoam_eth_port_global_cfg_s g_st_eth_port_cfg;

hi_ctcoam_onu_loid_s   g_st_ctcoam_onu_loid;

hi_ctcoam_monitoring_cfg_s g_us_port_monitor_cfg[HI_OAM_STAT_PORT_NUM];

static uint32_t g_config_counter = 0;

static hi_uint32 g_ui_llid_num = 1;


/*----------------------------------------------*
 * routines' implementations                    *
 *----------------------------------------------*/



/*****************************************************************************
 Prototype    : hi_ctcoam_init
 Description  : 扩展OAM处理模块初始化
 Input        : hi_uint32 ui_llid
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_init(hi_uint32 ui_llid)
{
    /* 初始化EMAC能力全局变量 */
    HI_OS_MEMSET_S(&g_st_eth_port_cfg, sizeof(g_st_eth_port_cfg), 0, sizeof(g_st_eth_port_cfg));

    hi_oam_init_capability(ui_llid);

    return HI_RET_SUCC;
}


#if PON_FUNC_CLASSIFICATION("标准OAM属性")

/*****************************************************************************
 Prototype    : hi_ctcoam_get_eth_phy_state
 Description  : 查询以太网物理端口的状态
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                const hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_eth_phy_state(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                      const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                      hi_void *pv_outmsg)
{
    hi_uint32 ui_ret;
    hi_uchar8 uc_flag;
    hi_uint32 *pui_state = (hi_uint32 *)pv_outmsg;
    hi_uint32 ui_port = pst_instance->un_value.ui_value & 0xff;

    if (HI_NULL == pv_outmsg) {
        return HI_RET_NULLPTR;
    }

    ui_ret = hi_oam_get_onu_port_phystate(ui_port, &uc_flag);
    if (HI_RET_SUCC != ui_ret) {
        return HI_RET_NOTSUPPORT;
    }

    *pui_state = (uc_flag == HI_CTCOAM_ETH_LINK_UP_E) ? HI_OAM_ETH_ACTIVATE_E : HI_OAM_ETH_DEACTIVATE_E;

    *pui_state = htonl(*pui_state);
    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_auto_neg_state
 Description  : 查询以太网端口的自协商状态
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                const hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_auto_neg_state(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                       const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                       hi_void *pv_outmsg)
{
    hi_uint32 ui_port = pst_instance->un_value.ui_value & 0xff;
    hi_uint32 ui_flag;

    hi_oam_get_autoneg_admincontrol(ui_port, &ui_flag);

    if (HI_TRUE== ui_flag) {
        *(hi_uint32 *)pv_outmsg = htonl(HI_ADMIN_STATE_ENABLE_E); /*lint !e572*/
    } else {
        *(hi_uint32 *)pv_outmsg = htonl(HI_ADMIN_STATE_DISABLE_E);/*lint !e572*/
    }

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_auto_neg_tech
 Description  : 查询actual port capabilities属性
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                const hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_auto_neg_tech(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                      const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                      hi_void *pv_outmsg)
{
    hi_uint32 *pui_msg = (hi_uint32 *)pv_outmsg;
    /*值为0x00000028(40)，表示支持IEEE 802.3 Clause 40中规定的1000BASE-T UTP PHY
    值为0x00000192(402)，表示支持IEEE 802.3 Clause 40中规定的Full duplex 1000BASE-T UTP PHY
    值为0x00000142(322)，表示支持IEEE 802.3 Clause 31 和32中规定的Full duplex 100BASE-T2*/
    FUNC_LOG;


    *pui_msg = htonl(3);/*lint !e572*/
    *(pui_msg + 1) = htonl(0x28);/*lint !e572*/
    *(pui_msg + 2) = htonl(0x192);/*lint !e572 !e648*/
    *(pui_msg + 3) = htonl(0x142);/*lint !e572 !e648*/
    *puc_changingmsglen = 16;

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_neg_advert
 Description  : 端口自协商能力通告
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                const hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_neg_advert(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                   const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                   hi_void *pv_outmsg)
{
    hi_uint32 *pui_msg = (hi_uint32 *)pv_outmsg;

    FUNC_LOG;


    *pui_msg = htonl(3);/*lint !e572*/
    *(pui_msg + 1) = htonl(0x28);/*lint !e572 !e648*/
    *(pui_msg + 2) = htonl(0x192);/*lint !e572 !e648*/
    *(pui_msg + 3) = htonl(0x142);/*lint !e572 !e648*/
    *puc_changingmsglen = 16;

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_fec_ability
 Description  : 获取FEC能力
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                const hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_fec_ability(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance, const hi_void *pv_inmsg,
                                    hi_uchar8 *puc_changingmsglen, hi_void *pv_outmsg)
{
    hi_uint32 ui_ret;
    hi_uint32 *pui_enable;

    /* 参数检查,由于不涉及端口号,则不解析也不检查pstInstance */
    if ((ui_llidindex >= HI_MAX_LLID_NUM)
        || (HI_NULL == pv_outmsg)) {
        return HI_RET_INVALID_PARA;
    }

    pui_enable = (hi_uint32 *)pv_outmsg;
    ui_ret = hi_oam_get_fec_enable(pui_enable);
    if (HI_RET_SUCC != ui_ret) {
        return HI_RET_NOTSUPPORT;
    }
    *pui_enable = htonl(*pui_enable);

    (hi_void)pst_instance;
    (hi_void)puc_changingmsglen;


    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_fec_mode
 Description  : 查询FEC打开与关闭功能
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                const hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_fec_mode(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                 const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                 hi_void *pv_outmsg)
{
    hi_uint32 ui_ret;
    hi_uint32 *pui_fecmode = (hi_uint32 *)pv_outmsg;

    /* 检查输入指针是否为空 */
    if (HI_NULL == pv_outmsg) {
        return HI_RET_INVALID_PARA;
    }

    ui_ret = hi_oam_get_fec_mode(pui_fecmode);
    if (HI_RET_SUCC != ui_ret) {
        return HI_RET_FAIL;
    }

    *pui_fecmode = htonl(*pui_fecmode);

    (hi_void)pv_inmsg;
    (hi_void)ui_llidindex;
    (hi_void)pst_instance;
    (hi_void)puc_changingmsglen;

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_set_fec_mode
 Description  : 设置FEC打开与关闭
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_fec_mode(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                 const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                 hi_void *pv_outmsg)
{
    hi_uint32 ui_ret = HI_RET_SUCC;
    hi_uint32 ui_mode;

    /* 检查输入指针是否为空 */
    if (HI_NULL == pv_inmsg) {
        return HI_RET_INVALID_PARA;
    }

    ui_mode = ntohl(*(hi_uint32 *)pv_inmsg);
    ui_ret  = hi_oam_set_fec_mode(ui_mode);

    /*刷新Report配置*/
    (hi_void)hi_ctcoam_tapi_dba_refresh_report();

    (hi_void)pv_outmsg;
    (hi_void)pst_instance;
    (hi_void)ui_llidindex;
    (hi_void)puc_changingmsglen;

    return ui_ret;
}

#endif


#if PON_FUNC_CLASSIFICATION("标准OAM操作")

/*****************************************************************************
 Prototype    : hi_ctcoam_set_eth_phy_ctrl
 Description  : 设置或更改以太网物理端口的状态
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                const hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_eth_phy_ctrl(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                     const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                     hi_void *pv_outmsg)
{
    hi_uint32 ui_ret = HI_RET_SUCC;
    hi_uint32 ui_active = *(hi_uint32 *)pv_inmsg;
    hi_uint32 ui_port = pst_instance->un_value.ui_value & 0xff;

    ui_active = ntohl(ui_active);
    if (HI_OAM_ETH_DEACTIVATE_E == ui_active) {
        ui_ret = hi_oam_set_onu_port_phystate(ui_port, HI_FALSE);
    } else if (HI_OAM_ETH_ACTIVATE_E == ui_active) {
        ui_ret = hi_oam_set_onu_port_phystate(ui_port, HI_TRUE);
    } else {
        return HI_RET_FAIL;
    }

    return ui_ret;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_eth_phy_ctrl
 Description  : 获取以太网物理端口的状态
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                const hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_eth_phy_ctrl(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                     const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                     hi_void *pv_outmsg)
{
    hi_uint32 ui_ret;
    hi_uint32 ui_port = pst_instance->un_value.ui_value & 0xff;
    hi_uint32 *pui_active = (hi_uint32 *)pv_outmsg;
    hi_uchar8 uc_flag;

    if (HI_NULL == pv_outmsg) {
        return HI_RET_NULLPTR;
    }

    ui_ret = hi_oam_get_onu_port_phystate(ui_port, &uc_flag);
    if (HI_RET_SUCC != ui_ret) {
        return HI_RET_NOTSUPPORT;
    }

    if (HI_FALSE == uc_flag) {
        *pui_active = htonl(HI_OAM_ETH_DEACTIVATE_E); /*lint !e572*/
    } else {
        *pui_active = htonl(HI_OAM_ETH_ACTIVATE_E);/*lint !e572*/
    }
    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_set_neg_restart
 Description  : 强制以太网UNI端口开始链路的再协商
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_neg_restart(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                    const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                    hi_void *pv_outmsg)
{
    hi_uint32 ui_port = pst_instance->un_value.ui_value & 0xff;

    return hi_oam_set_autoneg_restart_autoconfig(ui_port);
}


/*****************************************************************************
 Prototype    : hi_ctcoam_set_neg_ctrl
 Description  : 打开或者关闭PHY端口的自协商功能
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                const hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_neg_ctrl(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                 const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                 hi_void *pv_outmsg)
{
    hi_uint32 ui_port = pst_instance->un_value.ui_value & 0xff;
    hi_uint32 *pui_active = (hi_uint32 *)pv_inmsg;
    hi_uint32 ui_flag;

    if (HI_NULL == pv_inmsg) {
        return HI_RET_NULLPTR;
    }
    if (HI_OAM_ETH_ACTIVATE_E == ntohl(*pui_active)) {
        ui_flag = HI_TRUE;
    } else if (HI_OAM_ETH_DEACTIVATE_E == ntohl(*pui_active)) {
        ui_flag = HI_FALSE;
    } else {
        return HI_RET_INVALID_PARA;
    }

    return hi_oam_set_autoneg_admincontrol(ui_port, ui_flag);
}

#endif


#if PON_FUNC_CLASSIFICATION("ONU扩展属性")

/*****************************************************************************
 Prototype    : hi_ctcoam_get_onu_sn
 Description  :  获取ONU的SN
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_onu_sn(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                               const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                               hi_void *pv_outmsg)
{
    hi_uint32 ui_ret;
    hi_ctcoam_onu_sn_s *pst_onusn = HI_NULL;
    hi_epon_llid_get_exoam_gloabal_data_ctrl st_ctrl;

    /* 检查输入指针是否为空 */
    if (HI_NULL == pv_outmsg) {
        return HI_RET_INVALID_PARA;
    }

    pst_onusn = (hi_ctcoam_onu_sn_s *)pv_outmsg;

    hi_oam_get_onu_sn(pst_onusn->auc_onuvendor, pst_onusn->auc_onumodel, pst_onusn->auc_ontmac,
                      pst_onusn->auc_hardwarever, pst_onusn->auc_softwarever, pst_onusn->auc_extonumodel);

    st_ctrl.ui_llidindex = ui_llidindex;

    ui_ret = hi_ctcoam_get_global_data(&st_ctrl);
    if (HI_RET_SUCC != ui_ret) {
        return HI_RET_FAIL;
    }

    if ((st_ctrl.st_data.uc_negotiatever == 0x30) || (st_ctrl.st_data.uc_negotiatever == HI_EPON_CUOAM_VER_C4_BYTE)
		|| (st_ctrl.st_data.uc_negotiatever == HI_EPON_CUOAM_VER_C5_BYTE)) {
        *puc_changingmsglen = HI_CTCOAM_ONTSN_30_MSG_LEN;
    } else {
        *puc_changingmsglen = HI_CTCOAM_ONTSN_MSG_LEN;
    }

    (hi_void)pst_instance;
    (hi_void)pv_inmsg;

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_firmware_verison
 Description  : 获取ONU的固件版本
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_firmware_verison(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    /* 检查输入指针是否为空 */
    if ((HI_NULL == pv_outmsg) || (HI_NULL == puc_changingmsglen)) {
        return HI_RET_INVALID_PARA;
    }

    hi_oam_get_firmwarever((hi_uchar8 *)pv_outmsg, puc_changingmsglen);
    //*puc_changingmsglen = (hi_uchar8)strlen(pv_outmsg);

    (hi_void)pst_instance;
    (hi_void)pv_inmsg;
    (hi_void)ui_llidindex;

    return HI_RET_SUCC;
}

hi_void hi_ctcoam_tx_power_offtime(hi_void *pv_data)
{
    hi_oam_set_tx_power(HI_OAM_ROUGE_OPEN_TX_E);
}

hi_uint32 hi_ctcoam_set_tx_power_control(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    hi_uchar8 uc_onuid[HI_MAC_LEN] = {0};
    hi_uchar8 uc_onuiddef[HI_MAC_LEN] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
    hi_ctcoam_tx_power_control_s *pst_powerctrl = (hi_ctcoam_tx_power_control_s *)pv_inmsg;
    hi_char8 buf[HI_MAC_LEN * 2 + 1] = {0};
    hi_uchar8 i;
    static hi_uint32 ui_set_tx_timerid = 0;
    static hi_uint32 ui_set_tx_timerid_enable = HI_FALSE;

    hi_oam_get_onu_id(uc_onuid);

    for (i = 0; i < 6; i++) {
        HI_OS_SPRINTF_S(&buf[i * 2], sizeof(buf) - i * 2, "%02x", uc_onuid[i]);
    }

    LOG(all, ERROR, "hi_oam_get_onu_id is %s\n", buf);

    for (i = 0; i < 6; i++) {
        HI_OS_SPRINTF_S(&buf[i * 2], sizeof(buf) - i * 2, "%02x", pst_powerctrl->uc_onuid[i]);
    }

    LOG(all, ERROR, "onuid in msg is %s\n", buf);

    pst_powerctrl->us_action = (hi_ushort16)htons(pst_powerctrl->us_action);
    pst_powerctrl->ui_transmitid = htonl(pst_powerctrl->ui_transmitid);

    if ((0 != hi_os_memcmp(uc_onuid, pst_powerctrl->uc_onuid, HI_MAC_LEN))
        && (0 != hi_os_memcmp(uc_onuiddef, pst_powerctrl->uc_onuid, HI_MAC_LEN))) {
        LOG(all, ERROR, "onuId DO NOT match AND onuId is NOT 0xFFFFFFFFFFFF\n");
        return HI_RET_FAIL;
    }

    if (pst_powerctrl->us_action == 0) {
        LOG(all, ERROR, "open Tx power\n");
        if (ui_set_tx_timerid_enable == HI_TRUE) {
            hi_timer_destroy(ui_set_tx_timerid);
            ui_set_tx_timerid_enable = HI_FALSE;
        }
        hi_oam_set_tx_power(HI_OAM_ROUGE_OPEN_TX_E);
    } else if (pst_powerctrl->us_action == 0xffff) {
        LOG(all, ERROR, "close Tx power\n");
        if (ui_set_tx_timerid_enable == HI_TRUE) {
            hi_timer_destroy(ui_set_tx_timerid);
            ui_set_tx_timerid_enable = HI_FALSE;
        }
        hi_oam_set_tx_power(HI_OAM_ROUGE_CLOSE_TX_E);
    } else {
        LOG(all, ERROR, "close Tx power %d sec ui_set_tx_timerid_enable %d\n", pst_powerctrl->us_action, ui_set_tx_timerid_enable);
        hi_oam_set_tx_power(HI_OAM_ROUGE_CHECK_E);
        if (ui_set_tx_timerid_enable == HI_FALSE) {
            if (HI_RET_SUCC != hi_timer_create(hi_ctcoam_tx_power_offtime, 0, &ui_set_tx_timerid)) {
                LOG(all, ERROR, "hi_timer_create failed\n");
                return HI_RET_FAIL;
            }
        }

        ui_set_tx_timerid_enable = HI_TRUE;
        hi_timer_mod(ui_set_tx_timerid, pst_powerctrl->us_action * 1000);
    }

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_onu_chipset
 Description  : 查询ONU所采用的PON芯片
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_onu_chipset(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                    const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                    hi_void *pv_outmsg)
{
    hi_uint32 ui_ret;
    hi_ctcoam_onu_chipset_s *pst_onuchipset = HI_NULL;

    /* 检查输入指针是否为空 */
    if (HI_NULL == pv_outmsg) {
        return HI_RET_INVALID_PARA;
    }

    pst_onuchipset = (hi_ctcoam_onu_chipset_s *)pv_outmsg;
    ui_ret =  hi_oam_get_chipsetid((hi_uchar8 *)&pst_onuchipset->us_chip_vendorid,
                                   (hi_uchar8 *)&pst_onuchipset->us_model,
                                   &pst_onuchipset->uc_revision,
                                   pst_onuchipset->auc_ic_version);
    if (HI_RET_SUCC != ui_ret) {
        return HI_RET_FAIL;
    }
    pst_onuchipset->us_chip_vendorid = (hi_ushort16)htons(pst_onuchipset->us_chip_vendorid);
    pst_onuchipset->us_model         = (hi_ushort16)htons(pst_onuchipset->us_model);
    *puc_changingmsglen = HI_CTCOAM_ONT_CHIPSET_MSG_LEN;

    (hi_void)pst_instance;
    (hi_void)pv_inmsg;
    (hi_void)ui_llidindex;

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_optical_diagnose
 Description  : ONU光模块的重要参数检测和链路诊断。
                OLT利用本属性查询ONU光收发机的重要的诊断参数的值。
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_optical_diagnose(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    hi_uint32 ui_ret = HI_RET_SUCC;

    hi_oam_optical_transceiver_diag_s *pst_optical_diagnose = (hi_oam_optical_transceiver_diag_s *)pv_outmsg;

    ui_ret = hi_oam_get_optical_param(pst_optical_diagnose);

    pst_optical_diagnose->temperature     = (hi_short16)htons(pst_optical_diagnose->temperature);
    pst_optical_diagnose->supply_vcc      = (hi_ushort16)htons(pst_optical_diagnose->supply_vcc);
    pst_optical_diagnose->tx_bias_current = (hi_ushort16)htons(pst_optical_diagnose->tx_bias_current);
    pst_optical_diagnose->tx_power        = (hi_ushort16)htons(pst_optical_diagnose->tx_power);
    pst_optical_diagnose->rx_power        = (hi_ushort16)htons(pst_optical_diagnose->rx_power);

    (hi_void)puc_changingmsglen;
    (hi_void)pst_instance;
    (hi_void)pv_inmsg;
    (hi_void)ui_llidindex;

    return ui_ret;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_service_sla
 Description  : 获取服务SLA队列参数
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_service_sla(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                    const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                    hi_void *pv_outmsg)
{
    hi_ctcoam_service_sla_s *pst_sla;
    hi_ushort16  us_len;
    /* 检查输入指针是否为空 */
    if ((HI_NULL == pv_outmsg) || (HI_NULL == puc_changingmsglen)) {
        return HI_RET_INVALID_PARA;
    }

    pst_sla = (hi_ctcoam_service_sla_s *)pv_outmsg;

    if (HI_RET_SUCC != hi_oam_get_service_sla(pst_sla, &us_len)) {
        return HI_RET_FAIL;
    }

    *puc_changingmsglen = (hi_uchar8)us_len;
    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_set_service_sla
 Description  : 设置服务SLA队列参数
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_service_sla(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                    const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                    hi_void *pv_outmsg)
{
    hi_ctcoam_service_sla_s *pst_sla = (hi_ctcoam_service_sla_s *)pv_inmsg;

    FUNC_LOG;

    /* 检查输入指针是否为空 */
    if (HI_NULL == pv_inmsg) {
        return HI_RET_NULLPTR;
    }
    return hi_oam_set_service_sla(pst_sla);
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_onu_capability1
 Description  : 描述ONU支持的主要功能，包括端口数量、端口和业务类型、上行队列数
             量、上行端口最大队列数、上行队列分配步长、下行队列数量、下行端
             口最大队列数、是否具备备用电池等。
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_onu_capability1(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                        hi_void *pv_outmsg)
{
    hi_uint32 ui_ret;
    hi_oam_onu_capabilites_s *pst_onucap = HI_NULL ;

    /* 检查输入指针是否为空 */
    if (HI_NULL == pv_outmsg) {
        return HI_RET_INVALID_PARA;
    }

    pst_onucap = (hi_oam_onu_capabilites_s *)pv_outmsg;
    ui_ret = hi_oam_get_capabilities1(pst_onucap);
    if (HI_RET_SUCC != ui_ret) {
        return HI_RET_FAIL;
    }
    *puc_changingmsglen = HI_CTCOAM_ONT_CAPABILITY_MSG_LEN;

    (hi_void)pst_instance;
    (hi_void)pv_inmsg;
    (hi_void)ui_llidindex;

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_onu_capability2
 Description  :
 Input        :
 Output       :
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_onu_capability2(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                        hi_void *pv_outmsg)
{
    hi_oam_onu_capabilites2_s *pst_onucap = HI_NULL ;

    /* 检查输入指针是否为空 */
    if (HI_NULL == pv_outmsg) {
        return HI_RET_INVALID_PARA;
    }

    *puc_changingmsglen = sizeof(hi_oam_onu_capabilites2_s);
    pst_onucap = (hi_oam_onu_capabilites2_s *)pv_outmsg;
    hi_oam_get_capabilities2(pst_onucap);

    pst_onucap->onu_type = htonl(pst_onucap->onu_type);
    pst_onucap->ge_interface_type = htonl(pst_onucap->ge_interface_type);
    pst_onucap->number_of_ge_port = (hi_ushort16)htons(pst_onucap->number_of_ge_port);

    pst_onucap->fe_interface_type = htonl(pst_onucap->fe_interface_type);
    pst_onucap->number_of_fe_port = (hi_ushort16)htons(pst_onucap->number_of_fe_port);

    pst_onucap->voip_interface_type = htonl(pst_onucap->voip_interface_type);
    pst_onucap->number_of_voip_port = (hi_ushort16)htons(pst_onucap->number_of_voip_port);

    pst_onucap->wlan_interface_type = htonl(pst_onucap->wlan_interface_type);
    pst_onucap->number_of_wlan_port = (hi_ushort16)htons(pst_onucap->number_of_wlan_port);

    pst_onucap->usb_interface_type = htonl(pst_onucap->usb_interface_type);
    pst_onucap->number_of_usb_port = (hi_ushort16)htons(pst_onucap->number_of_usb_port);

    pst_onucap->catv_interface_type = htonl(pst_onucap->catv_interface_type);
    pst_onucap->number_of_catv_port = (hi_ushort16)htons(pst_onucap->number_of_catv_port);

    pst_onucap->number_of_PONIf = 1;

    return HI_RET_SUCC;
}

/*****************************************************************************
 Prototype    : hi_ctcoam_get_onu_holdovercfg
 Description  :
 Input        :
 Output       :
 Return Value :
*****************************************************************************/
uint32_t hi_ctcoam_get_onu_holdovercfg(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                       const void *pv_inmsg, uint8_t *puc_changingmsglen,
                                       void *pv_outmsg)
{
	uint32_t ui_ret;
	uint32_t holdstate;
	uint32_t time;
	hi_ctcoam_onu_holdovercfg_s *pst_onucap = HI_NULL ;

	/* 检查输入指针是否为空 */
	if (HI_NULL == pv_outmsg) {
		return HI_RET_INVALID_PARA;
	}

	pst_onucap = (hi_ctcoam_onu_holdovercfg_s *)pv_outmsg;
	ui_ret = hi_oam_get_onu_holdover(&holdstate, &time);
	if (HI_RET_SUCC != ui_ret) {
		return HI_RET_FAIL;
	}

	pst_onucap->ui_enable = htonl(holdstate);
	pst_onucap->ui_time   = htonl(time);
	*puc_changingmsglen   = sizeof(hi_ctcoam_onu_holdovercfg_s);

	return HI_RET_SUCC;
}

/*****************************************************************************
 Prototype    : hi_ctcoam_set_onu_holdovercfg
 Description  :
 Input        :
 Output       :
 Return Value :
*****************************************************************************/
uint32_t hi_ctcoam_set_onu_holdovercfg(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                        const void *pv_inmsg, uint8_t *puc_changingmsglen,
                                        void *pv_outmsg)
{
	hi_ctcoam_onu_holdovercfg_s *pst_cfg = HI_NULL ;

	/* 检查输入指针是否为空 */
	if (HI_NULL == pv_inmsg)
		return HI_RET_INVALID_PARA;

	pst_cfg = (hi_ctcoam_onu_holdovercfg_s *)pv_inmsg;
	return hi_oam_set_onu_holdover(ntohl(pst_cfg->ui_enable), ntohl(pst_cfg->ui_time));
}

uint32_t hi_ctcoam_get_onu_protection_param(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                            const void *pv_inmsg, uint8_t *puc_changingmsglen,
                                            void *pv_outmsg)
{
	uint16_t los_optical;
	uint16_t los_mac;
	hi_ctcoam_onu_protection_param_s *param = HI_NULL ;

	if (pv_outmsg == HI_NULL)
		return HI_RET_INVALID_PARA;

	param = (hi_ctcoam_onu_protection_param_s *)pv_outmsg;
	if (HI_RET_SUCC != hi_oam_get_onu_protection_param(&los_optical, &los_mac))
		return HI_RET_FAIL;

	param->los_optical = htons(los_optical);
	param->los_mac = htons(los_mac);
	*puc_changingmsglen = sizeof(hi_ctcoam_onu_protection_param_s);
	return HI_RET_SUCC;
}

uint32_t hi_ctcoam_set_onu_protection_param(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                            const void *pv_inmsg, uint8_t *puc_changingmsglen,
                                            void *pv_outmsg)
{
	hi_ctcoam_onu_protection_param_s *param = HI_NULL ;

	if (HI_NULL == pv_inmsg)
		return HI_RET_INVALID_PARA;

	param = (hi_ctcoam_onu_protection_param_s *)pv_inmsg;
	return hi_oam_set_onu_protection_param(ntohs(param->los_optical), ntohs(param->los_mac));
}

/*****************************************************************************
 Prototype    : hi_ctcoam_get_onu_hodeover
 Description  : 定义：用于光链路保护中，对ONU的“状态保持”（holdover）功能的管理
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_onu_hodeover(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                     const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                     hi_void *pv_outmsg)
{

    return HI_RET_SUCC;
}

/*****************************************************************************
 Prototype    : hi_ctcoam_set_onu_hodeover
 Description  : 定义：用于光链路保护中，对ONU的“状态保持”（holdover）功能的管理
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_onu_hodeover(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                     const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                     hi_void *pv_outmsg)
{

    return HI_RET_SUCC;
}

/*****************************************************************************
 Prototype    : hi_ctcoam_set_onu_pon_if
 Description  : 定义：用于配置/查询ONU的主用的PON接口号。
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_onu_pon_if(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                   const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                   hi_void *pv_outmsg)
{

    return HI_RET_NOTSUPPORT;
}

/*****************************************************************************
 Prototype    : hi_ctcoam_get_onu_pon_if
 Description  : 定义：用于配置/查询ONU的主用的PON接口号。
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_onu_pon_if(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                   const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                   hi_void *pv_outmsg)
{

    return HI_RET_NOTSUPPORT;
}

/*****************************************************************************
 Prototype    : hi_ctcoam_get_onu_capability3
 Description  : 描述ONU支持的主要功能，包括端口数量、端口和业务类型、上行队列数
             量、上行端口最大队列数、上行队列分配步长、下行队列数量、下行端
             口最大队列数、是否具备备用电池等。
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_onu_capability3(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                        hi_void *pv_outmsg)
{
    hi_oam_onu_capabilities3_s *pst_capability;

    if (HI_NULL == pv_outmsg) {
        return HI_RET_INVALID_PARA;
    }
    pst_capability = (hi_oam_onu_capabilities3_s *)pv_outmsg;
    return hi_oam_get_capabilities3(pst_capability);
}

/*****************************************************************************
 Prototype    : hi_ctcoam_get_onu_power_saving_capabilities
 Description  : 描述ONU是否支持节能模式和提前醒来机制。
 Input        :
 Output       :
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_onu_power_saving_capabilities(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    /*
        hi_pmu_epon_cfg_s st_pmu_cfg;
        hi_ctcoam_onu_power_saving_capabilities_s *pst_pmu_cap = (hi_ctcoam_onu_power_saving_capabilities_s *)pv_outmsg;
        hi_uint32 ui_ret;

        FUNC_LOG;
        if ( HI_NULL == pv_outmsg )
        {
            return HI_RET_INVALID_PARA;
        }

        HI_OS_MEMSET_S(&st_pmu_cfg, sizeof(st_pmu_cfg), 0, sizeof(st_pmu_cfg));
        st_pmu_cfg.uc_type = HI_PMU_PS_CAP_GET_E;

        ui_ret = HI_IPC_CALL("hi_kernel_pmu_epon_cfg_get", &st_pmu_cfg, sizeof(st_pmu_cfg));

        if (HI_RET_SUCC != ui_ret)
        {
            return ui_ret;
        }

        HI_OS_MEMCPY_S(pst_pmu_cap, sizeof(hi_ctcoam_onu_power_saving_capabilities_s), st_pmu_cfg.uc_data, sizeof(hi_ctcoam_onu_power_saving_capabilities_s));
        *puc_changingmsglen = sizeof(hi_ctcoam_onu_power_saving_capabilities_s);
    */
    return HI_RET_SUCC;
}

/*****************************************************************************
 Prototype    : hi_ctcoam_set_onu_power_saving_capabilities
 Description  : 设置ONU是否支持节能模式和提前醒来机制。
 Input        :
 Output       :
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_onu_power_saving_capabilities(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
#if 0
    hi_pmu_epon_cfg_s st_pmu_cfg;
    hi_uint32 ui_ret;

    FUNC_LOG;

    /* 检查输入指针是否为空 */
    if (HI_NULL == pv_inmsg) {
        return HI_RET_INVALID_PARA;
    }

    HI_OS_MEMSET_S(&st_pmu_cfg, sizeof(st_pmu_cfg), 0, sizeof(st_pmu_cfg));
    st_pmu_cfg.uc_type = HI_PMU_PS_CAP_SET_E;
    HI_OS_MEMCPY_S(st_pmu_cfg.uc_data, sizeof(st_pmu_cfg.uc_data), pv_inmsg, sizeof(st_pmu_cfg.uc_data));

    ui_ret = HI_IPC_CALL("hi_kernel_pmu_epon_cfg_set", &st_pmu_cfg, sizeof(st_pmu_cfg));

    hi_ctcoam_dbg_print("ERR CODE 0x%x\n", ui_ret);
#endif
    return 0;
}

/*****************************************************************************
 Prototype    : hi_ctcoam_get_onu_power_saving_cfg
 Description  : 获取ONU节能相关配置
 Input        :
 Output       :
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_onu_power_saving_cfg(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
#if 0
    hi_pmu_epon_cfg_s st_pmu_cfg;
    hi_ctcoam_onu_power_saving_cfg_s *pst_pmu_save = (hi_ctcoam_onu_power_saving_cfg_s *)pv_outmsg;
    hi_uint32 ui_ret;

    FUNC_LOG;

    if (HI_NULL == pv_outmsg) {
        return HI_RET_INVALID_PARA;
    }

    HI_OS_MEMSET_S(&st_pmu_cfg, sizeof(st_pmu_cfg), 0, sizeof(st_pmu_cfg));
    st_pmu_cfg.uc_type = HI_PMU_PS_CFG_GET_E;

    ui_ret = HI_IPC_CALL("hi_kernel_pmu_epon_cfg_get", &st_pmu_cfg, sizeof(st_pmu_cfg));

    if (HI_RET_SUCC != ui_ret) {
        return ui_ret;
    }

    HI_OS_MEMCPY_S(pst_pmu_save, sizeof(hi_ctcoam_onu_power_saving_cfg_s), st_pmu_cfg.uc_data, sizeof(hi_ctcoam_onu_power_saving_cfg_s));
    *puc_changingmsglen = sizeof(hi_ctcoam_onu_power_saving_cfg_s);
#endif
    return HI_RET_SUCC;
}

/*****************************************************************************
 Prototype    : hi_ctcoam_set_onu_power_saving_cfg
 Description  : 设置ONU节能相关配置
 Input        :
 Output       :
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_onu_power_saving_cfg(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
#if 0
    hi_pmu_epon_cfg_s st_pmu_cfg;
    hi_uint32 ui_ret;

    FUNC_LOG;
    if (HI_NULL == pv_inmsg) {
        return HI_RET_INVALID_PARA;
    }

    HI_OS_MEMSET_S(&st_pmu_cfg, sizeof(st_pmu_cfg), 0, sizeof(st_pmu_cfg));
    st_pmu_cfg.uc_type = HI_PMU_PS_CFG_SET_E;
    HI_OS_MEMCPY_S(st_pmu_cfg.uc_data, sizeof(st_pmu_cfg.uc_data), pv_inmsg, sizeof(st_pmu_cfg.uc_data));

    ui_ret = HI_IPC_CALL("hi_kernel_pmu_epon_cfg_set", &st_pmu_cfg, sizeof(st_pmu_cfg));

    hi_ctcoam_dbg_print("ERR CODE 0x%x\n", ui_ret);
#endif
    return 0;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_onu_eth_link_state
 Description  : 查询以太网端口的链路运行状态
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_onu_eth_link_state(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    hi_uint32 ui_port = pst_instance->un_value.ui_value & 0xff;

    if (HI_NULL == pv_outmsg) {
        return HI_RET_NULLPTR;
    }

    return hi_oam_get_eth_port_linkstate(ui_port, (hi_uchar8 *)pv_outmsg);
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_onu_eth_pause
 Description  : 查询以太网端口的流控功能
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_onu_eth_pause(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                      const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                      hi_void *pv_outmsg)
{
    if ((HI_NULL == pst_instance) || (HI_NULL == pv_outmsg)) {
        return HI_RET_FAIL;
    }

    hi_uint32 ui_port = pst_instance->un_value.ui_value & 0xff;
    hi_uchar8 *puc_pause_flg = (hi_uchar8 *)pv_outmsg;

    return hi_oam_get_flow_ctl_mode((hi_ushort16)ui_port, puc_pause_flg);
}


/*****************************************************************************
 Prototype    : hi_ctcoam_set_onu_eth_pause
 Description  : 设置以太网端口的流控功能
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_onu_eth_pause(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                      const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                      hi_void *pv_outmsg)
{
    if ((HI_NULL == pst_instance) || (HI_NULL == pv_inmsg)) {
        return HI_RET_FAIL;
    }

    hi_uint32 ui_port = pst_instance->un_value.ui_value & 0xff;
    hi_uchar8 uc_pause_flg = *((hi_uchar8 *)pv_inmsg);

    return hi_oam_set_flow_ctll((hi_ushort16)ui_port, uc_pause_flg);
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_eth_us_policy
 Description  : 获取以太网端口的上行业务的入口管制信息
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_eth_us_policy(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                      const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                      hi_void *pv_outmsg)
{
    if ((HI_NULL == pst_instance) || (HI_NULL == pv_outmsg)) {
        return HI_RET_NULLPTR;
    }

    hi_lsw_port_e em_port = (hi_lsw_port_e)pst_instance->un_value.uc_value;
    hi_ctcoam_eth_us_policy_s *pst_policy = (hi_ctcoam_eth_us_policy_s *)pv_outmsg;
    hi_uint32 ui_ret;
    hi_uint32 ui_cir;
    hi_uint32 ui_cbs;
    hi_uint32 ui_ebs;

    ui_ret = hi_oam_get_eth_port_up_flow_ratelimit(em_port, &ui_cir, &ui_cbs, &ui_ebs);
    if (HI_RET_SUCC != ui_ret) {
        pst_policy->uc_enable = HI_FALSE;
        *puc_changingmsglen = sizeof(pst_policy->uc_enable);
    } else {
        pst_policy->uc_enable = HI_TRUE;
        //ui_cir = HI_CTCOAM_64KBPS_TO_KBPS(ui_cir);
        pst_policy->cir_hi8   = (ui_cir >> 16) & 0xff;
        pst_policy->cir_lo16  = (hi_ushort16)htons(ui_cir & 0xffff);

        pst_policy->cbs_hi8   = (ui_cbs >> 16) & 0xff;
        pst_policy->cbs_lo16  = (hi_ushort16)htons(ui_cbs & 0xffff);

        pst_policy->ebs_hi8   = (ui_ebs >> 16) & 0xff;
        pst_policy->ebs_lo16  = (hi_ushort16)htons(ui_ebs & 0xffff);

        *puc_changingmsglen = sizeof(*pst_policy);
    }

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_set_eth_us_policy
 Description  : 配置以太网端口的上行业务的入口管制信息
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_eth_us_policy(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                      const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                      hi_void *pv_outmsg)
{
    hi_lsw_port_e em_port = (hi_lsw_port_e)pst_instance->un_value.uc_value;
    hi_ctcoam_eth_us_policy_s *pst_policy = (hi_ctcoam_eth_us_policy_s *)pv_inmsg;
    hi_uint32 ui_cir;
    hi_uint32 ui_cbs;
    hi_uint32 ui_ebs;

    if (HI_TRUE == pst_policy->uc_enable) {
        ui_cir = ntohs(pst_policy->cir_lo16) + (pst_policy->cir_hi8 << 16);
        ui_cbs = ntohs(pst_policy->cbs_lo16) + (pst_policy->cbs_hi8 << 16);
        ui_ebs = ntohs(pst_policy->ebs_lo16) + (pst_policy->ebs_hi8 << 16);
    } else {
        ui_cir = (hi_uint32) - 1;
        ui_cbs = ui_cir;
        ui_ebs = ui_cir;
    }

    return hi_oam_set_eth_port_up_flow_ratelimit(em_port, ui_cir, ui_cbs, ui_ebs);
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_eth_ds_rate_limit
 Description  : 查询以太网端口的下行速率限制功能
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                const hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_eth_ds_rate_limit(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    if ((HI_NULL == pst_instance) || (HI_NULL == pv_outmsg)) {
        return HI_RET_NULLPTR;
    }

    hi_lsw_port_e em_port = (hi_lsw_port_e)pst_instance->un_value.uc_value;
    hi_ctcoam_eth_ds_policy_s *pst_policy = (hi_ctcoam_eth_ds_policy_s *)pv_outmsg;
    hi_uint32 ui_cir;
    hi_uint32 ui_pir;
    hi_uint32 ui_ret;

    ui_ret = hi_oam_get_eth_port_ds_flow_ratelimit(em_port, &ui_cir, &ui_pir);
    if (HI_RET_SUCC != ui_ret) {
        pst_policy->uc_enable = HI_FALSE;
        *puc_changingmsglen   = sizeof(pst_policy->uc_enable);
    } else {
        pst_policy->uc_enable = HI_TRUE;
        pst_policy->cir_hi8   = (ui_cir >> 16) & 0xff;
        pst_policy->cir_lo16  = (hi_ushort16)htons(ui_cir & 0xffff);

        pst_policy->pir_hi8   = (ui_pir >> 16) & 0xff;
        pst_policy->pir_lo16  = (hi_ushort16)htons(ui_pir & 0xffff);

        *puc_changingmsglen = sizeof(*pst_policy);
    }


    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_set_eth_ds_rate_limit
 Description  : 设置以太网端口的下行速率限制功能
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                const hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_eth_ds_rate_limit(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    if ((HI_NULL == pst_instance) || (HI_NULL == pv_inmsg)) {
        return HI_RET_NULLPTR;
    }

    hi_lsw_port_e em_port = (hi_lsw_port_e)pst_instance->un_value.uc_value;
    hi_ctcoam_eth_ds_policy_s *pst_policy = (hi_ctcoam_eth_ds_policy_s *)pv_inmsg;
    hi_uint32 ui_cir;
    hi_uint32 ui_pir;

    if (HI_TRUE == pst_policy->uc_enable) {
        ui_cir = (ntohs(pst_policy->cir_lo16) + (pst_policy->cir_hi8 << 16));
        ui_pir = (ntohs(pst_policy->pir_lo16) + (pst_policy->pir_hi8 << 16));
    } else {
        ui_cir = (hi_uint32) - 1;
        ui_pir = ui_cir;
    }

    return hi_oam_set_eth_port_ds_flow_ratelimit(em_port, ui_cir, ui_pir);
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_uni_port_loop_detect
 Description  : 查询以太网端口环路检测使能标志
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                const hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_uni_port_loop_detect(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    if ((HI_NULL == pv_outmsg) || (HI_NULL == pst_instance)) {
        return HI_RET_FAIL;
    }

    hi_uint32  ui_ret;
    hi_uint32  *pui_mode = (hi_uint32 *)pv_outmsg;
    hi_uint32  ui_port = pst_instance->un_value.ui_value & 0xffff;

    ui_ret = hi_oam_get_loopdetect(ui_port, pui_mode);
    if (HI_RET_SUCC != ui_ret) {
        return HI_RET_FAIL;
    }
    *pui_mode = htonl(*pui_mode);
    return ui_ret;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_set_uni_port_loop_detect
 Description  : 设置以太网端口环路检测使能标志
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                const hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_uni_port_loop_detect(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    if ((HI_NULL == pv_inmsg) || (HI_NULL == pst_instance)) {
        return HI_RET_NULLPTR;
    }

    hi_uint32  ui_lock = *(hi_uint32 *)pv_inmsg;
    hi_uint32 ui_port = pst_instance->un_value.ui_value & 0xffff;

    ui_lock = ntohl(ui_lock);
    return hi_oam_set_loopdetect(ui_port, ui_lock);
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_uni_port_disable_looped
 Description  : 查询以太网端口检测出环路后允许自动关闭
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                const hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_uni_port_disable_looped(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    if ((HI_NULL == pv_outmsg) || (HI_NULL == pst_instance)) {
        return HI_RET_FAIL;
    }

    hi_uint32  ui_ret;
    hi_uint32  *pui_mode = (hi_uint32 *)pv_outmsg;
    hi_uint32  ui_port = pst_instance->un_value.ui_value & 0xff;

    ui_ret = hi_oam_get_loopdetect_close(ui_port, pui_mode);
    if (HI_RET_SUCC != ui_ret) {
        return HI_RET_FAIL;
    }
    *pui_mode = htonl(*pui_mode);
    return ui_ret;
}

/*****************************************************************************
 Prototype    : hi_ctcoam_set_uni_port_disable_looped
 Description  : 设置以太网端口检测出环路后允许自动关闭
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                const hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_uni_port_disable_looped(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    if ((HI_NULL == pv_inmsg) || (HI_NULL == pst_instance)) {
        return HI_RET_NULLPTR;
    }

    hi_uint32  ui_lock = *(hi_uint32 *)pv_inmsg;
    hi_lsw_port_e em_port = (hi_lsw_port_e)pst_instance->un_value.uc_value;

    ui_lock = ntohl(ui_lock);
    return hi_oam_set_loopdetect_close(em_port, ui_lock);
}

/*****************************************************************************
 Prototype    : hi_ctcoam_get_port_mode_status
 Description  : 获取端口工作模式
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                const hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_port_mode_status(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    hi_uint32 ui_port = pst_instance->un_value.ui_value & 0xff;

    return hi_oam_get_port_mode(ui_port, (hi_uchar8 *)pv_outmsg);
}

/*****************************************************************************
 Prototype    : hi_ctcoam_get_port_mac_aging_time
 Description  : 查询mac地址老化时间
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                const hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_port_mac_aging_time(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    hi_uint32 ui_ret;
    hi_uint32 *pui_agingtime;

    FUNC_LOG;

    if (HI_NULL == pv_outmsg) {
        return HI_RET_INVALID_PARA;
    }

    pui_agingtime = (hi_uint32 *)pv_outmsg;
    ui_ret = hi_oam_get_onu_agetime(pui_agingtime);
    if (HI_RET_SUCC != ui_ret) {
        return HI_RET_FAIL;
    }

    *pui_agingtime = htonl(*pui_agingtime);
    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_set_port_mac_aging_time
 Description  : 设置mac地址老化时间
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                const hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_port_mac_aging_time(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    hi_uint32 ui_agingtime;


    if (HI_NULL == pv_inmsg) {
        return HI_RET_INVALID_PARA;
    }
    ui_agingtime = ntohl(*(hi_uint32 *)pv_inmsg);

    return hi_oam_set_onu_agetime(ui_agingtime);
}


hi_uint32 hi_ctcoam_get_voip_port_act(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                      const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                      hi_void *pv_outmsg)
{
    return HI_RET_SUCC;
}


hi_uint32 hi_ctcoam_set_voip_port_act(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                      const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                      hi_void *pv_outmsg)
{
    return HI_RET_SUCC;
}


hi_uint32 hi_ctcoam_get_tdm_port_cfg(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                     const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                     hi_void *pv_outmsg)
{
    return HI_RET_NOTSUPPORT;
}

hi_uint32 hi_ctcoam_set_tdm_port_cfg(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                     const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                     hi_void *pv_outmsg)
{
    return HI_RET_NOTSUPPORT;
}


hi_uint32 hi_ctcoam_get_vlan_cfg(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                 const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                 hi_void *pv_outmsg)
{
    if ((HI_NULL == pst_instance) || (HI_NULL == pv_outmsg) || (HI_NULL == puc_changingmsglen)) {
        return HI_RET_NULLPTR;
    }

    hi_uint32             ui_ret;
    portAllService        st_cfg;
    hi_uchar8             uc_mode;
    hi_lsw_port_e         em_port  = (hi_lsw_port_e)pst_instance->un_value.uc_value;
    static pfn_vlancfg_get pfunc[] = {hi_ctcoam_get_vlan_transparent_cfg,
                                      hi_ctcoam_get_vlan_tag_cfg,
                                      hi_ctcoam_get_vlan_translation_cfg,
                                      hi_ctcoam_get_vlan_aggr_cfg,
                                      hi_ctcoam_get_vlan_trunk_cfg
                                     };

    HI_OS_MEMSET_S(&st_cfg, sizeof(st_cfg), 0, sizeof(st_cfg));
    st_cfg.port_index = em_port;
    ui_ret = hi_oam_ctc_get_eth_port_vlan(&st_cfg);
    if (HI_RET_SUCC != ui_ret) {
        hi_ctcoam_dbg_err_print("get eth port vlan fail ret = %08x\n", ui_ret);
        return HI_RET_FAIL;
    }

    uc_mode = (hi_uchar8)st_cfg.vlanMode;

    if (uc_mode > HI_OAM_VLAN_TRUNK_E) {
        hi_ctcoam_dbg_err_print("vlan mode = %d is invalid\n", uc_mode);
        return HI_RET_NOTSUPPORT;
    }
    hi_ctcoam_dbg_print("port = %d, vlanmod = %d\n", em_port, uc_mode);

    return pfunc[uc_mode](pv_outmsg, &st_cfg, puc_changingmsglen);
}

hi_uint32 hi_ctcoam_set_vlan_cfg(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                 const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                 hi_void *pv_outmsg)
{
    if ((HI_NULL == pv_inmsg) || (HI_NULL == pst_instance)) {
        return HI_RET_NULLPTR;
    }

    hi_lsw_port_e         em_port    = (hi_lsw_port_e)pst_instance->un_value.uc_value;
    HI_OAM_VLAN_MODE_E    em_vlanmod = (HI_OAM_VLAN_MODE_E)(*(hi_uchar8 *)pv_inmsg);
    hi_uchar8             uc_paralen  =  *((hi_uchar8 *)(((hi_uchar8 *)pv_inmsg) - 1));
    hi_void               *pst_vlanpara = (hi_void *)(((hi_uchar8 *)pv_inmsg) + 1);
    portAllService        st_cfg;
    hi_uint32             ui_ret;
    static pfn_vlancfg_set pfunc[] = {hi_ctcoam_set_vlan_transparent_cfg,
                                      hi_ctcoam_set_vlan_tag_cfg,
                                      hi_ctcoam_set_vlan_translation_cfg,
                                      hi_ctcoam_set_vlan_aggr_cfg,
                                      hi_ctcoam_set_vlan_trunk_cfg
                                     };

    if (em_vlanmod > HI_OAM_VLAN_TRUNK_E) {
        hi_ctcoam_dbg_err_print("vlanmod = %d is invalid!\n", em_vlanmod);
        return HI_RET_NOTSUPPORT;
    }
    hi_ctcoam_dbg_print("port = %d, vlanmod = %d\n", em_port, em_vlanmod);

    HI_OS_MEMSET_S(&st_cfg, sizeof(st_cfg), 0, sizeof(st_cfg));
    st_cfg.port_index = em_port;
    hi_oam_ctc_get_eth_port_vlan(&st_cfg);
    st_cfg.vlanMode   = em_vlanmod;
    st_cfg.port_index = em_port;

    ui_ret = pfunc[em_vlanmod]((hi_void *)pst_vlanpara, uc_paralen, &st_cfg);
    if (HI_RET_SUCC != ui_ret) {
        hi_ctcoam_dbg_err_print("proc fail ret = 0x%08x!\n", ui_ret);
    }
    return hi_oam_ctc_set_eth_port_vlan(&st_cfg);
}

hi_uint32 hi_ctcoam_get_flow_class_and_mark(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    if ((HI_NULL == pst_instance) || (HI_NULL == pv_outmsg) || (HI_NULL == puc_changingmsglen)) {
        return HI_RET_NULLPTR;
    }

    hi_ctcoam_container_hdr_s *pst_container = HI_NULL;
    hi_ctcoam_classify_rule_connect_s *pst_dst_rule = HI_NULL;
    hi_uchar8 *puc_container;
    hi_uchar8 *puc_rule;
    hi_uchar8 uc_rule_num = 0;
    hi_uchar8 uc_rulelen = 0;
    hi_uchar8 uc_containerlen = 0;
    hi_ushort16 *pus_outlen = (hi_ushort16 *)(puc_changingmsglen + 1);;
    portClass st_cfg;
    hi_uint32 i;

    *puc_changingmsglen = 0; //tlv中长度
    *pus_outlen = 0;         //上报消息实际长度

    HI_OS_MEMSET_S(&st_cfg, sizeof(st_cfg), 0, sizeof(st_cfg));
    st_cfg.portId = pst_instance->un_value.uc_value;
    hi_oam_get_class_cfg(&st_cfg);

    puc_container = (hi_uchar8 *)pv_outmsg;
    puc_container -= sizeof(hi_ctcoam_tlv_s);  //为了统一多个container,将puc_container指向tlv的开始位置
    pst_container = (hi_ctcoam_container_hdr_s *)puc_container;

    puc_rule  = (hi_uchar8 *)&pst_container->st_classify_mark.stRuleContent[0];
    uc_containerlen = sizeof(hi_ctcoam_classify_marking_s);

    uc_rule_num = 0;
    for (i = 0; i < st_cfg.ruleNum; ++i) {
        pst_dst_rule = (hi_ctcoam_classify_rule_connect_s *)puc_rule;
        uc_rulelen   = HI_OAM_GET_RULE_LEN(st_cfg.ruleBody[i]);

        //当几个rule的总长度超过container的最大长度128时，需要组织多个container上报
        if ((uc_containerlen + uc_rulelen) > 128) {
            *pus_outlen += uc_containerlen + sizeof(hi_ctcoam_tlv_s);
            hi_ctcoam_flow_list_one_container(pst_container, uc_containerlen, uc_rule_num);
            if (*puc_changingmsglen == 0) {
                *puc_changingmsglen = uc_containerlen;
            }
            pst_container = (hi_ctcoam_container_hdr_s *)puc_rule;
            puc_rule += sizeof(hi_ctcoam_container_hdr_s);
            uc_containerlen = sizeof(hi_ctcoam_classify_marking_s);
            uc_rule_num = 0;
        }

        hi_ctcoam_rule_get(&st_cfg.ruleBody[i],
                           &st_cfg.ruleField[i][0],
                           pst_dst_rule);
        puc_rule        += uc_rulelen;
        uc_containerlen += uc_rulelen;
        uc_rule_num++;
    }

    *pus_outlen += uc_containerlen;

    hi_ctcoam_flow_list_one_container(pst_container, uc_containerlen, uc_rule_num);
    if (*puc_changingmsglen == 0) {
        *puc_changingmsglen = uc_containerlen;
    }

    return HI_RET_SUCC;
}

hi_uint32 hi_ctcoam_set_flow_class_and_mark(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    if ((HI_NULL == pst_instance) || (HI_NULL == pv_inmsg)) {
        return HI_RET_NULLPTR;
    }

    hi_lsw_port_e em_port  = (hi_lsw_port_e)pst_instance->un_value.uc_value;
    hi_ctcoam_classify_marking_s *pst_mark;
    hi_ctcoam_classify_rule_connect_s *pst_rule;
    hi_uchar8 *puc_rule;
    portClass st_cfg;
    hi_uint32 ui_ret;
    hi_uint32 i;

    if (em_port > HI_LSW_PORT_NNI_PON_E) {
        return HI_RET_SUCC;
    }
    pst_mark = (hi_ctcoam_classify_marking_s *)pv_inmsg;
    puc_rule = (hi_uchar8 *)&pst_mark->stRuleContent[0];

    HI_OS_MEMSET_S(&st_cfg, sizeof(st_cfg), 0, sizeof(st_cfg));
    st_cfg.portId = em_port;
    st_cfg.ruleNum = pst_mark->uc_rulesnum;

    hi_ctcoam_dbg_print("port = %d, action = %d rulenum = %d\n", em_port, pst_mark->uc_action, pst_mark->uc_rulesnum);

    /*
       action:0x00 删除流规则，该帧只带优先级
              0x01 添加流规则，严格按照协议规定解析数据帧
              0x02 删除全部流规则，该帧只有action，之后没有其他数据
    */
    switch (pst_mark->uc_action) {
        case HI_FLOW_DEL_E:
            for (i = 0; i < st_cfg.ruleNum; ++i) {
                st_cfg.ruleBody[i].precedenceOfRule = *puc_rule;
                puc_rule++;
            }
            ui_ret = hi_oam_del_class_cfg(&st_cfg);
            break;

        case HI_FLOW_ADD_E:
            for (i = 0; i < st_cfg.ruleNum; ++i) {
                pst_rule = (hi_ctcoam_classify_rule_connect_s *)puc_rule;
                hi_ctcoam_rule_set(pst_rule, &st_cfg.ruleBody[i], &st_cfg.ruleField[i][0]);
                puc_rule += pst_rule->uc_length + sizeof(pst_rule->uc_precedence) + sizeof(pst_rule->uc_length);
            }
            ui_ret = hi_oam_set_class_cfg(&st_cfg);
            break;

        case HI_FLOW_DEL_ALL_E:
            ui_ret = hi_oam_clear_class_cfg(&st_cfg);
            break;

        default:
            return HI_RET_FAIL;
    }

    return ui_ret;
}
hi_uint32 hi_ctcoam_get_multi_vlan_cfg(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                       const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                       hi_void *pv_outmsg)
{
    if ((HI_NULL == pst_instance) || (HI_NULL == pv_outmsg) || (HI_NULL == puc_changingmsglen)) {
        return HI_RET_NULLPTR;
    }

    hi_uint32             ui_ret;
    portAllService        st_cfg;
    hi_lsw_port_e         em_port  = (hi_lsw_port_e)pst_instance->un_value.uc_value;
    HI_OAM_VLAN_MULTICAST_MODE_E em_vlanmod = (HI_OAM_VLAN_MULTICAST_MODE_E)(*(hi_uchar8 *)pv_inmsg);
    static pfn_vlancfg_get pfunc[] = {HI_NULL,
                                      HI_NULL,
                                      HI_NULL,
                                      hi_ctcoam_list_multi_vlan
                                     };

    if (em_vlanmod != HI_OAM_VLAN_MULTICAST_LIST_E) {
        hi_ctcoam_dbg_err_print("vlanmod = %d is invalid!\n", em_vlanmod);
        return HI_RET_NOTSUPPORT;
    }

    HI_OS_MEMSET_S(&st_cfg, sizeof(st_cfg), 0, sizeof(st_cfg));
    st_cfg.port_index = em_port;
    ui_ret = hi_oam_ctc_get_eth_port_vlan(&st_cfg);
    if (HI_RET_SUCC != ui_ret) {
        hi_ctcoam_dbg_err_print("get eth port vlan fail ret = %08x\n", ui_ret);
        return HI_RET_FAIL;
    }

    hi_ctcoam_dbg_print("port = %d, vlanmod = %d\n", em_port, em_vlanmod);
    return pfunc[em_vlanmod](pv_outmsg, &st_cfg, puc_changingmsglen);
}


hi_uint32 hi_ctcoam_set_multi_vlan_cfg(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                       const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                       hi_void *pv_outmsg)
{
    if ((HI_NULL == pv_inmsg) || (HI_NULL == pst_instance)) {
        return HI_RET_NULLPTR;
    }

    hi_lsw_port_e         em_port    = (hi_lsw_port_e)pst_instance->un_value.uc_value;
    HI_OAM_VLAN_MULTICAST_MODE_E em_vlanmod = (HI_OAM_VLAN_MULTICAST_MODE_E)(*(hi_uchar8 *)pv_inmsg);
    hi_uchar8             uc_paralen  =  *((hi_uchar8 *)(((hi_uchar8 *)pv_inmsg) - 1));
    hi_void               *pst_vlanpara = (hi_void *)(((hi_uchar8 *)pv_inmsg) + 1);
    portAllService        st_cfg;
    hi_uint32             ui_ret;
    static pfn_vlancfg_set pfunc[] = {hi_ctcoam_del_multi_vlan,
                                      hi_ctcoam_add_multi_vlan,
                                      hi_ctcoam_clr_multi_vlan,
                                      HI_NULL
                                     };

    if (em_vlanmod > HI_OAM_VLAN_MULTICAST_CLR_E) {
        hi_ctcoam_dbg_err_print("vlanmod = %d is invalid!\n", em_vlanmod);
        return HI_RET_NOTSUPPORT;
    }

    hi_ctcoam_dbg_print("port = %d, vlanmod = %d\n", em_port, em_vlanmod);

    HI_OS_MEMSET_S(&st_cfg, sizeof(st_cfg), 0, sizeof(st_cfg));
    st_cfg.port_index = em_port;
    RET_CHK(hi_oam_ctc_get_eth_port_vlan(&st_cfg));

    ui_ret = pfunc[em_vlanmod]((hi_void *)pst_vlanpara, uc_paralen, &st_cfg);
    if (HI_RET_SUCC != ui_ret) {
        hi_ctcoam_dbg_err_print("proc fail ret = 0x%08x!\n", ui_ret);
    }

    return hi_oam_ctc_set_eth_port_vlan(&st_cfg);
}

hi_uint32 hi_ctcoam_get_multi_tag_strip(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                        hi_void *pv_outmsg)
{
    hi_lsw_port_e         em_port    = (hi_lsw_port_e)pst_instance->un_value.uc_value;
    *puc_changingmsglen = 1;

    RET_CHK(hi_oam_multicast_tag_strip_get(em_port, (hi_uchar8 *)pv_outmsg));
    return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_ctcoam_set_multi_tag_strip
 Description : 配置端口组播VLAN动作
               TagStriped:
               0x00：不剥除组播业务报文和通用查询报文的VLAN Tag
               0x01：剥除组播业务报文和通用查询报文的的VLAN Tag
               0x02：切换组播数据报文和通用查询报文的VLAN到用户的IPTV VLAN
 Input Parm  : hi_uint32 ui_llidindex,
               hi_ctcoam_instance_s *pst_instance,
               const hi_void *pv_inmsg,
               hi_uchar8 *puc_changingmsglen,
 Output Parm : hi_uchar8 *puc_changingmsglen,
               hi_void *pv_outmsg
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_ctcoam_set_multi_tag_strip(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                        hi_void *pv_outmsg)
{
    hi_uchar8 *pv_tmp         = (hi_uchar8 *)pv_inmsg;
    hi_lsw_port_e em_port   = (hi_lsw_port_e)pst_instance->un_value.uc_value;
    portAllService st_cfg;
    hi_uchar8 uc_datalen    = *puc_changingmsglen;
    hi_uchar8 uc_tagstriped = GETCHAR(pv_tmp);
    hi_uchar8 uc_vlannum    = GETCHAR(pv_tmp);
    hi_ushort16 us_multivlan;
    hi_ushort16 us_iptvvlan;
    hi_uint32 ui_index;
    hi_uint32 ui_item;

    /*消息长度为为1或者2＋4×N。为1时，表示不删除或删除组播vlan，
      为2＋4×N时，表示做vlan切换*/
    if (uc_datalen != (2 + 4 * uc_vlannum)  &&
        uc_datalen != 1) {
        return HI_RET_FAIL;
    }

    HI_OS_MEMSET_S(&st_cfg, sizeof(st_cfg), 0, sizeof(st_cfg));
    st_cfg.port_index = em_port;
    RET_CHK(hi_oam_ctc_get_eth_port_vlan(&st_cfg));

    hi_ctcoam_dbg_print("\r\n uc_tagstriped %d\n", uc_tagstriped);


    if (0x02 == uc_tagstriped) {
        for (ui_index = 0; ui_index < uc_vlannum; ui_index++) {
            RET_CHK(hi_ctcoam_add_multi_vlan(pv_tmp, sizeof(hi_ushort16), &st_cfg));

            /*添加接口，以太端口号为ui_portid，动作切换组播数据报文
              和通用查询报文的VLAN到用户的IPTV VLAN*/
            us_multivlan = (hi_ushort16)GETSHORT(pv_tmp);
            us_iptvvlan  = (hi_ushort16)GETSHORT(pv_tmp);

            for (ui_item = 0; ui_item < st_cfg.multiNum; ui_item++) {
                if (st_cfg.multiVlanInfo[ui_item].inVlan == us_multivlan) {
                    st_cfg.multiVlanInfo[ui_item].outVlan = us_iptvvlan;
                    break;
                }
            }

        }
    } else if (0x0 == uc_tagstriped) {
        //RET_CHK(hi_oam_ctc_clr_vlan_multicast(&st_cfg));
        if (st_cfg.multiVlanStrip == uc_tagstriped) {
            return HI_RET_SUCC;
        }

        /*如果之前是组播切换业务，则清空之前配置*/
        if (0x2 == st_cfg.multiVlanStrip) {
            hi_ctcoam_clr_multi_vlan(pv_tmp, sizeof(hi_ushort16), &st_cfg);
        }
    }

    st_cfg.multiVlanStrip = uc_tagstriped;
    st_cfg.multiMode = HI_TRUE;

    return hi_oam_ctc_set_eth_port_vlan(&st_cfg);
}

hi_uint32 hi_ctcoam_get_multi_switch(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                     const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                     hi_void *pv_outmsg)
{
    hi_uchar8 *puc_switch = (hi_uchar8 *)pv_outmsg;
    hi_uint32 ui_enable;

    RET_CHK(hi_lsw_mc_ctrl_get(&ui_enable));

    *puc_switch = (hi_uchar8)ui_enable;
    *puc_changingmsglen = 0x1;

    return HI_RET_SUCC;
}

hi_uint32 hi_ctcoam_set_multi_switch(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                     const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                     hi_void *pv_outmsg)
{
    hi_uchar8 uc_switch = *(hi_uchar8 *)pv_inmsg;
    return hi_lsw_mc_ctrl_set(uc_switch);
}

/******************************************************************************
 Function    : hi_ctcoam_get_multi_ctrl
 Description : 获取组播控制列表
                Action
                0x03：列出该ONU所有的组播控制条目（用于Extended Get Request和
                Extended Get Response消息)。当本container用于Extended Get Request
                时，本字节后面没有其他数据，均为padding；当本container用于Extended
                Get Response时，本字节后为该ONU的全部组播控制条目
 Input Parm  : hi_uint32 ui_llidindex,
               hi_ctcoam_instance_s *pst_instance,
               const hi_void *pv_inmsg,
               hi_uchar8 *puc_changingmsglen,
 Output Parm : hi_uchar8 *puc_changingmsglen,
               hi_void *pv_outmsg
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_ctcoam_get_multi_ctrl(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                   const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                   hi_void *pv_outmsg)
{
    hi_ctcoam_multicastControl_s *pst_head = (hi_ctcoam_multicastControl_s *)pv_outmsg;
    hi_uchar8 uc_action = *(hi_uchar8 *)pv_inmsg;
    hi_uchar8 uc_controltype;
    hi_uchar8 uc_entry_num;
    hi_uchar8 *puc_str = HI_NULL;

    if (uc_action != 0x3) {
        return HI_RET_FAIL;
    }

    puc_str = (hi_uchar8 *)pv_outmsg + sizeof(hi_ctcoam_multicastControl_s);
    RET_CHK(hi_oam_get_multi_ctrl_item(puc_changingmsglen, puc_str, &uc_controltype, &uc_entry_num));

    pst_head->uc_action = uc_action;
    pst_head->uc_control = uc_controltype;
    pst_head->uc_num = uc_entry_num;

    *puc_changingmsglen += sizeof(hi_ctcoam_multicastControl_s);

    return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_ctcoam_set_multi_ctrl
 Description : 组播控制列表管理
                Action
                0x00：删除下述的组播控制条目（用于Extended Set Request消息）
                0x01：增加下述的组播控制条目（用于Extended Set Request消息）
                0x02：清除ONU的组播控制表（即删除该ONU所有的组播控制条目）；该操
                作类型仅用于Extended Set Request消息。当本container为此操作类型
                时，本字节后面没有其他数据，均为padding
 Input Parm  : hi_uint32 ui_llidindex,
               hi_ctcoam_instance_s *pst_instance,
               const hi_void *pv_inmsg,
               hi_uchar8 *puc_changingmsglen,
 Output Parm : hi_uchar8 *puc_changingmsglen,
               hi_void *pv_outmsg
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_ctcoam_set_multi_ctrl(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                   const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                   hi_void *pv_outmsg)
{
    hi_uchar8 uc_action = *(hi_uchar8 *)pv_inmsg;
    hi_uchar8 uc_controltype = *(hi_uchar8 *)((hi_uchar8 *)pv_inmsg + 1);
    hi_uchar8 uc_entrynum = *(hi_uchar8 *)((hi_uchar8 *)pv_inmsg + 2);
    hi_ctcoam_gda_info_s st_gdainfo;
    portAllService st_cfg;
    hi_uint32 i;
    HI_OS_MEMSET_S(&st_gdainfo, sizeof(st_gdainfo), 0, sizeof(st_gdainfo));
    /*检查消息数据是否合法*/
    if ((uc_action != 0x00) && (uc_action != 0x01) && (uc_action != 0x02)) {
        return HI_RET_FAIL;
    }

    /*检查消息数据是否合法*/
    if (uc_controltype > 0x05) {
        return HI_RET_FAIL;
    }

    hi_ctcoam_dbg_print("\r\nuc_controltype %d uc_action %d\n", uc_controltype, uc_action);

    /*action 0x02单独处理*/
    if (0x02 == uc_action) {
        return hi_oam_set_multi_ctrl_item(&st_gdainfo, uc_controltype, uc_action);
    }

    /*处理每一个表项*/
    for (i = 0; i < uc_entrynum; i++) {
        /*获取每一个表项*/
        switch (uc_controltype) {
            case 0x00:
            case 0x01:
            case 0x03:
                HI_OS_MEMCPY_S(&st_gdainfo.st_mac, sizeof(st_gdainfo.st_mac), ((hi_uchar8 *)pv_inmsg + 3 + (sizeof(hi_ctcoam_gda_mac_s) * i)), sizeof(hi_ctcoam_gda_mac_s));
                //HI_PRINT_MEM(sizeof(hi_ctcoam_gda_mac_s), &st_gdainfo.st_mac);
                break;

            case 0x02:
                HI_OS_MEMCPY_S(&st_gdainfo.st_mac_ipv4, sizeof(st_gdainfo.st_mac_ipv4), ((hi_uchar8 *)pv_inmsg + 3 + (sizeof(hi_ctcoam_gda_mac_ipv4_s) * i)), sizeof(hi_ctcoam_gda_mac_ipv4_s));
                break;

            case 0x04:
                HI_OS_MEMCPY_S(&st_gdainfo.st_ipv6, sizeof(st_gdainfo.st_ipv6), ((hi_uchar8 *)pv_inmsg + 3 + (sizeof(hi_ctcoam_gda_ipv6_s) * i)), sizeof(hi_ctcoam_gda_ipv6_s));
                break;

            case 0x05:
                HI_OS_MEMCPY_S(&st_gdainfo.st_mac_ipv6, sizeof(st_gdainfo.st_mac_ipv6), ((hi_uchar8 *)pv_inmsg + 3 + (sizeof(hi_ctcoam_gda_mac_ipv6_s) * i)), sizeof(hi_ctcoam_gda_mac_ipv6_s));
                break;

            default:
                break;
        }

        if (uc_controltype == 0x1 || uc_controltype == 0x3 || uc_controltype == 0x04) {
            st_cfg.port_index = (hi_ushort16)ntohs(st_gdainfo.st_mac.us_userid);
            RET_CHK(hi_oam_ctc_get_eth_port_vlan(&st_cfg));

            if (uc_action) {
                RET_CHK(hi_ctcoam_add_multi_vlan(&st_gdainfo.st_mac.us_vlanid,
                                                 sizeof(st_gdainfo.st_mac.us_vlanid), &st_cfg));
            }

            RET_CHK(hi_oam_ctc_set_eth_port_vlan(&st_cfg));
        }

        /*删除或添加每一个组播地址表项*/
        RET_CHK(hi_oam_set_multi_ctrl_item(&st_gdainfo, uc_controltype, uc_action));
    }

    return HI_RET_SUCC;
}

hi_uint32 hi_ctcoam_get_multi_group_num_max(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    return HI_RET_SUCC;
}

hi_uint32 hi_ctcoam_set_multi_group_num_max(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    hi_uchar8 *pv_tmp         = (hi_uchar8 *)pv_inmsg;
    hi_uint32 ui_portid     = pst_instance->un_value.uc_value;
    hi_uchar8 uc_groupnum   = GETCHAR(pv_tmp);
    hi_uchar8 uc_datalen    = *puc_changingmsglen;

    /*对datalen 进行判断*/
    if (0x01 != uc_datalen) {
        return HI_RET_FAIL;
    }

    /*添加接口,以太端口号为ui_portid, 该端口最大组播数为uc_groupnum*/
    return hi_lsw_max_group_set((hi_lsw_port_e)ui_portid, uc_groupnum);
}

hi_uint32 hi_ctcoam_get_fast_leave_ability(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    hi_ctcoam_mc_fast_leave_ability_s *pst_cfg;

    if ((HI_NULL == pv_outmsg) || (HI_NULL == puc_changingmsglen)) {
        return HI_RET_INVALID_PARA;
    }
    pst_cfg = (hi_ctcoam_mc_fast_leave_ability_s *)pv_outmsg;

    pst_cfg->ui_num_enum = htonl(4); /*lint !e572*/
    pst_cfg->ui_igmp_not_fast_leave_ability = htonl(0x0);/*lint !e572*/
    pst_cfg->ui_igmp_fast_leave_ability = htonl(0x1);/*lint !e572*/
    pst_cfg->ui_ctc_ctrl_not_fast_leave_ability = htonl(0x10);/*lint !e572*/
    pst_cfg->ui_ctc_ctrl_fast_leave_ability = htonl(0x11);/*lint !e572*/
    *puc_changingmsglen = sizeof(pst_cfg->ui_num_enum) + ntohl(pst_cfg->ui_num_enum) * sizeof(hi_uint32);

    return HI_RET_SUCC;
}

hi_uint32 hi_ctcoam_get_fast_leave_state(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    hi_uint32 *pui_en = (hi_uint32 *)pv_outmsg;
    hi_uint32 ui_enable;

    RET_CHK(hi_lsw_fast_get(&ui_enable));

    if (ui_enable) {
        ui_enable = 0x2;
    } else {
        ui_enable = 0x1;
    }

    *pui_en = htonl(ui_enable);
    *puc_changingmsglen = 0x4;

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_llid_queue
 Description  : 获取指定llid的队列
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                const hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_llid_queue(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                   const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                   hi_void *pv_outmsg)
{
    return HI_RET_NOTSUPPORT;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_set_llid_queue
 Description  : 给指定llid分配队列
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                const hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_llid_queue(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                   const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                   hi_void *pv_outmsg)
{
    return HI_RET_NOTSUPPORT;
}

hi_uint32 hi_ctcoam_get_empty(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                              const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                              hi_void *pv_outmsg)
{
    return HI_RET_NOTSUPPORT;
}
#if 0
/*****************************************************************************
 Prototype    : hi_ctcoam_set_onu_optical_tx_alarm_enable
 Description  : 查询和设置ONU光收发机的重要参数越限告警和警示功能（打开/关闭）
 Input        : hi_ulong32 ui_llidindex
                HI_KERNEL_CTCOAM_INSTANCE_T *pst_instance
                const hi_void *pv_inmsg
                const hi_void *pv_outmsg
 Output       : None
*****************************************************************************/
hi_ulong32 hi_ctcoam_set_onu_optical_tx_alarm_enable(hi_ulong32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_void *pv_outmsg)
{
    return HI_RET_NOTSUPPORT;
}
#endif
#endif

#if PON_FUNC_CLASSIFICATION("ONU扩展操作")

/*****************************************************************************
 Prototype    : hi_ctcoam_set_onu_reset
 Description  : 重启ONU
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_onu_reset(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                  const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                  hi_void *pv_outmsg)
{
    hi_oam_onu_reset();

    return HI_RET_SUCC;
}

/*****************************************************************************
 Prototype    : hi_ctcoam_get_onu_sleep
 Description  : 获取onu睡眠配置
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_onu_sleep(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                  const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                  hi_void *pv_outmsg)
{
#if 0
    hi_pmu_epon_cfg_s st_pmu_cfg;
    hi_uint32 ui_ret;

    FUNC_LOG;

    if (HI_NULL == pv_outmsg) {
        return HI_RET_INVALID_PARA;
    }

    HI_OS_MEMSET_S(&st_pmu_cfg, sizeof(st_pmu_cfg), 0, sizeof(st_pmu_cfg));
    st_pmu_cfg.uc_type = HI_PMU_PS_SLEEP_GET_E;

    ui_ret = HI_IPC_CALL("hi_kernel_pmu_epon_cfg_get", &st_pmu_cfg, sizeof(st_pmu_cfg));

    if (HI_RET_SUCC != ui_ret) {
        return ui_ret;
    }

    HI_OS_MEMCPY_S(pv_outmsg, sizeof(st_pmu_cfg.uc_data), st_pmu_cfg.uc_data, sizeof(st_pmu_cfg.uc_data));
#endif
    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_set_onu_sleep
 Description  : 设置onu睡眠
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_onu_sleep(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                  const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                  hi_void *pv_outmsg)
{
#if 0
    hi_pmu_epon_cfg_s st_pmu_cfg;
    hi_uint32 ui_ret;

    FUNC_LOG;
    if (HI_NULL == pv_inmsg) {
        return HI_RET_INVALID_PARA;
    }

    HI_OS_MEMSET_S(&st_pmu_cfg, sizeof(st_pmu_cfg), 0, sizeof(st_pmu_cfg));
    st_pmu_cfg.uc_type = HI_PMU_PS_SLEEP_SET_E;

    HI_OS_MEMCPY_S(st_pmu_cfg.uc_data, sizeof(st_pmu_cfg.uc_data), pv_inmsg, sizeof(st_pmu_cfg.uc_data));

    ui_ret = HI_IPC_CALL("hi_kernel_pmu_epon_cfg_set", &st_pmu_cfg, sizeof(st_pmu_cfg));

    hi_ctcoam_dbg_print("ERR CODE 0x%x\n", ui_ret);
#endif
    return 0;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_onu_fast_leave_admin_ctrl
 Description  : 查询组播快速离开
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_onu_fast_leave_admin_ctrl(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    hi_uint32 *pui_act = (hi_uint32 *)pv_outmsg;
    *pui_act = 1;
    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_set_onu_fast_leave_admin_ctrl
 Description  : 设置组播快速离开
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_onu_fast_leave_admin_ctrl(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    hi_uchar8 *pv_tmp = (hi_uchar8 *)pv_inmsg;
    hi_uint32 ui_value = GETINT(pv_tmp);

    switch (ui_value) {
        case 0x1:
            return hi_lsw_fast_set(HI_FALSE);

        case 0x2:
            return hi_lsw_fast_set(HI_TRUE);

        default:
            return HI_RET_FAIL;
    }
}



/*****************************************************************************
 Prototype    : hi_ctcoam_get_multi_llid_cfg
 Description  : 获取onu多LLID配置
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_multi_llid_cfg(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                       const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                       hi_void *pv_outmsg)
{
    if (HI_NULL == pv_outmsg) {
        return HI_RET_FAIL;
    }
    *(hi_uint32 *)pv_outmsg = htonl(g_ui_llid_num);

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_set_multi_llid_cfg
 Description  : 设置onu多LLID
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_multi_llid_cfg(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                       const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                       hi_void *pv_outmsg)
{
    hi_uint32 ui_llid_num;
    hi_uint32 ui_max_llidnum;
    hi_uchar8 uc_id;

    if (HI_NULL == pv_inmsg) {
        return HI_RET_FAIL;
    }

    ui_llid_num = *((hi_uint32 *)pv_inmsg);
    ui_llid_num = ntohl(ui_llid_num);

    if (0 == ui_llid_num) { //切换到单LLID模式，注销LLID1~LLID7
        for (uc_id = 1; uc_id < g_ui_llid_num; ++uc_id) {
            HI_IPC_CALL("hi_epon_dereg_multillid", &uc_id);
        }
        hi_oam_map_set(HI_LLID_INDEX_0);
        g_ui_llid_num = 1;
    } else {
        ui_max_llidnum = hi_oam_get_llid_num();
        if (ui_llid_num > ui_max_llidnum) {
            return HI_RET_INVALID_PARA;
        }
        if (g_ui_llid_num == ui_max_llidnum) {
            return HI_RET_SUCC;
        }
        for (uc_id = 1; uc_id < g_ui_llid_num; ++uc_id) {
            HI_IPC_CALL("hi_epon_reg_multillid", &uc_id);
            hi_oam_map_set(uc_id);
        }
        g_ui_llid_num = ui_llid_num;
    }
    return HI_RET_SUCC;
}

#endif


#if PON_FUNC_CLASSIFICATION("DBA相关")
/*****************************************************************************
 Prototype    : hi_ctcoam_get_dba
 Description  : 获取DBA参数
 Input        : hi_uint32 ui_llidindex
                hi_void *pv_inmsg
                hi_uint32 ui_inmsglen
                hi_uint32 ui_exoam_type
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_dba(hi_uint32 ui_llidindex, hi_void *pv_inmsg,
                            hi_uint32 ui_inmsglen, hi_uint32 ui_exoam_type)
{
    hi_uint32  ui_ret;
    hi_ctcoam_dba_capability_s st_dba_cap;

    /* 参数检查,LLID的正确性由上层保证 */
    if ((HI_NULL == pv_inmsg) || (HI_EXOAM_TYPE_CTC_E != ui_exoam_type)) {
        hi_ctcoam_dbg_err_print("Invalid Para.");
        return HI_RET_INVALID_PARA;
    }

    ui_ret = hi_oam_get_dba(ui_llidindex, &st_dba_cap);
    if (HI_RET_SUCC != ui_ret) {
        return HI_RET_FAIL;
    }

    return hi_ctcoam_dba_threshold_get_response(ui_llidindex, ui_exoam_type, &st_dba_cap);
}


/*****************************************************************************
 Prototype    : hi_ctcoam_set_dba
 Description  : 设置DBA参数
 Input        : hi_uint32 ui_llidindex
                hi_void *pv_inmsg
                hi_uint32 ui_inmsglen
                hi_uint32 ui_exoam_type
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_dba(hi_uint32 ui_llidindex, hi_void *pv_inmsg,
                            hi_uint32 ui_inmsglen, hi_uint32 ui_exoam_type)
{
    hi_uint32  ui_ret;
    hi_uchar8  uc_setack = HI_DBA_RESULT_NACK_E;
    hi_uint32   ui_chkmsglen;
    hi_ctcoam_dba_msg_s *pst_chk_dba_msg = HI_NULL;
    hi_ctcoam_dba_capability_s st_dba_threshold;

    if ((HI_NULL == pv_inmsg) || (HI_EXOAM_TYPE_CTC_E != ui_exoam_type)) {
        hi_ctcoam_dbg_err_print("Invalid Para.");
        return HI_RET_INVALID_PARA;
    }

    /* 对dba设置参数进行检查,pv_inmsg指向hi_ctcoam_dba_msg_s的uc_queue_set_num，需要回退一个字节 */
    pst_chk_dba_msg = (hi_ctcoam_dba_msg_s *)(((char *)pv_inmsg) - 1);
    ui_chkmsglen = ui_inmsglen + 1;
    ui_ret = hi_ctcoam_dba_set_para_chk(pst_chk_dba_msg, (hi_uchar8)ui_chkmsglen);

    if (HI_RET_SUCC == ui_ret) {
        hi_ctcoam_set_qset(pv_inmsg, &st_dba_threshold);
        ui_ret = hi_oam_set_dba(ui_llidindex, &st_dba_threshold);
        if (HI_RET_SUCC != ui_ret) {
            hi_ctcoam_dbg_err_print("Set DBA Fail(%x)", ui_ret);
        } else {
            uc_setack = HI_DBA_RESULT_ACK_E;
        }
    } else {
        hi_ctcoam_dbg_err_print("dba threshold value error\n");
    }

    return hi_ctcoam_dba_threshold_set_response(ui_llidindex, uc_setack, ui_exoam_type, pv_inmsg, ui_inmsglen);
}


/*****************************************************************************
 Prototype    : hi_ctcoam_dba_set_para_chk
 Description  : 设置DBA参数检查
 Input        : hi_ctcoam_dba_msg_s *pst_dba_msg
                hi_uchar8 uc_inmsglen
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_dba_set_para_chk(hi_ctcoam_dba_msg_s *pst_dba_msg, hi_uchar8 uc_inmsglen)
{
    hi_uchar8 uc_queue_set_num = pst_dba_msg->uc_queue_set_num;
    hi_uchar8 *puc_receive = (hi_uchar8 *)pst_dba_msg;
    hi_uchar8 uc_msglen  = 0;
    hi_uchar8 uc_num  = 0;
    hi_uchar8 uc_use_queue_num = 0;
    hi_uchar8 uc_queue_num = 0xff; /*上行的队列数*/
    hi_uint32 ui_ret = HI_RET_INVALID_PARA;
    hi_uchar8 uc_bitmap = 0;
    hi_uchar8 i;
    hi_ushort16 us_thre_min[HI_CTCOAM_THRESHOLD_QUEUE_NUM];
    hi_ctcoam_threshold_s *pst_threshold;

    pst_threshold = (hi_ctcoam_threshold_s *)(puc_receive + sizeof(hi_ctcoam_dba_msg_s));
    if ((uc_queue_set_num > HI_CTCOAM_DBA_MAX_QUEUE_SET_NUM)
        || (uc_queue_set_num < HI_CTCOAM_DBA_MIN_QUEUE_SET_NUM)) {
        hi_ctcoam_dbg_err_print("queue set num is over than 4  or less than 2\n");
        return ui_ret;
    }

    puc_receive += sizeof(hi_ctcoam_dba_msg_s);
    uc_msglen = (hi_uchar8)(sizeof(hi_ctcoam_dba_msg_s));

    HI_OS_MEMSET_S(us_thre_min, sizeof(us_thre_min), 0, sizeof(us_thre_min));

    /*CTC标准规定Report帧格式中最后一个Queue Set用于填写所有队列的全部长度，
      因此，set_DBA_request帧中的Queue＃n Threshold的数量为（Number of Queue Sets-1）*/
    for (uc_num = 0; uc_num < uc_queue_set_num - 1; ++uc_num) {
        pst_threshold = (hi_ctcoam_threshold_s *)((hi_void *)puc_receive);
        uc_bitmap = pst_threshold->uc_report_bitmap;

        /* 获取当前queue set使用的队列数 */
        HI_PON_BITMAP_TO_NUM(uc_bitmap, uc_use_queue_num);

        if (uc_queue_num == 0xff) {
            uc_queue_num = uc_use_queue_num;
        } else {
            if (uc_queue_num != uc_use_queue_num) {
                hi_ctcoam_dbg_err_print("queue_set[%u]'s used_queue_num[%u] isn't equal to queue_set[0]'s used_queue_num[%u]\n",
                                        uc_num, uc_use_queue_num, uc_queue_num);
                return ui_ret;
            }
        }

        /* Threshold使用的总队列应该不超过SD5115芯片支持的总队列数 */
        if (uc_use_queue_num > HI_CTCOAM_DBA_THRES_Q_NUM) {
            hi_ctcoam_dbg_err_print("used queue num [%d] > hw support max queue num[%d]\n",
                                    uc_use_queue_num, HI_CTCOAM_DBA_THRES_Q_NUM);
            return ui_ret;
        }

        /*按照中国电信标准，当前队列集(a)的队列N的threshold应小于下一个队列集
          (a+1)对应队列N的threshold*/
        for (i = 0; i < uc_use_queue_num; ++i) {
            if (us_thre_min[i] > ntohs(pst_threshold->aus_threshold[i])) {
                hi_ctcoam_dbg_err_print("queue_set(%u).queue(%u) threshold[%d] less than que_set(%u).queue(%u) threshold[%d]\n",
                                        uc_num, i, ntohs(pst_threshold->aus_threshold[i]), uc_num - 1, i, us_thre_min[i]);
                return ui_ret;
            }

            us_thre_min[i] = (hi_ushort16)ntohs(pst_threshold->aus_threshold[i]);
        }

        /*该queue set使用了uc_use_queue_num个队列,则跳过uc_use_queue_num*2+1 */
        puc_receive  += sizeof(pst_threshold->uc_report_bitmap)
                        + (sizeof(pst_threshold->aus_threshold[0]) * HI_CTCOAM_THRESHOLD_QUEUE_NUM);
        uc_msglen    += sizeof(pst_threshold->uc_report_bitmap)
                        + (sizeof(pst_threshold->aus_threshold[0]) * HI_CTCOAM_THRESHOLD_QUEUE_NUM);

        if (uc_msglen > uc_inmsglen) {
            hi_ctcoam_dbg_err_print("inmsglen error %d\n", uc_msglen);
            return ui_ret;
        }

    }

    return HI_RET_SUCC;
}


#endif


#if PON_FUNC_CLASSIFICATION("EVENT相关")

hi_void hi_ctcoam_get_port_by_obj_type_instance(hi_ushort16 us_type, hi_uint32 ui_instance,
        hi_uchar8 *puc_port_begin, hi_uchar8 *puc_port_end)
{
    hi_uchar8 uc_port;
    FUNC_LOG;
    if (us_type == HI_CTC_INSTANCE_PORT_E) {
        uc_port = ui_instance & 0xff;
        if (uc_port == 0xff) {
            *puc_port_begin = HI_LSW_PORT_UNI_ETH1_E;
            *puc_port_end   = HI_LSW_PORT_UNI_ETH8_E;
        } else {
            *puc_port_begin = uc_port;
            *puc_port_end   = uc_port;
        }
    } else if (us_type == HI_CTC_INSTANCE_PON_IF_E) {
        *puc_port_begin = HI_LSW_PORT_NNI_PON_E;
        *puc_port_end   = HI_LSW_PORT_NNI_PON_E;
    }
    return;
}

hi_uint32 hi_ctcoam_get_instance(hi_ushort16 us_type, hi_uint32 ui_instance, hi_uchar8 uc_port)
{
    hi_uint32 ui_retval = ui_instance;

    if (us_type == HI_CTC_INSTANCE_PORT_E) {
        ui_retval &= 0xffff0000;
        ui_retval += uc_port;
    }
    return ui_retval;
}

hi_uint32 hi_ctcoam_get_event_status(hi_uint32 ui_llidindex, hi_void *pv_inmsg,
                                     hi_uint32 ui_inmsglen,  hi_void *pv_outmsg,
                                     hi_uint32 *pui_outmsglen, hi_uint32 ui_exoam_type)
{
    hi_ctcoam_event_get_req_s *pst_req;
    hi_ctcoam_event_status_s  *pst_resp;
    hi_ctcoam_alarm_value_s   *pst_alarm_val;

    hi_ushort16 i;
    hi_uchar8   uc_port = 0;
    hi_ushort16 us_objtype;
    hi_uchar8   uc_port_begin = 0;
    hi_uchar8   uc_port_end   = 0;
    hi_ushort16 us_alarmid;
    hi_ushort16 us_entrycount;
    hi_uint32   ui_objinstance;
    hi_uint32 ui_tmp = 0;

    pst_req  = (hi_ctcoam_event_get_req_s *)pv_inmsg;
    pst_resp = (hi_ctcoam_event_status_s *)pv_outmsg;
    for (i = 0, us_entrycount = 0; i < ntohs(pst_req->us_entrycount); ++i) {
        us_objtype     = (hi_ushort16)ntohs(pst_req->st_entry[i].us_objtype);
        us_alarmid     = (hi_ushort16)ntohs(pst_req->st_entry[i].us_alarmid);
        ui_objinstance = pst_req->st_entry[i].ui_objinstance;
        hi_ctcoam_get_port_by_obj_type_instance(us_objtype,  ui_objinstance,
                                                &uc_port_begin, &uc_port_end);

        for (uc_port = uc_port_begin; uc_port <= uc_port_end; uc_port++) {
            if (HI_RET_SUCC != hi_ctcoam_get_alarm_cfg(us_objtype, uc_port, us_alarmid, &pst_alarm_val)) {
                continue;
            }
            pst_resp->st_entry[us_entrycount].us_objtype     = (hi_ushort16)htons(us_objtype);
            ui_tmp = hi_ctcoam_get_instance(us_objtype, ui_objinstance, uc_port);
            pst_resp->st_entry[us_entrycount].ui_objinstance = ui_tmp;
            pst_resp->st_entry[us_entrycount].us_alarmid     = (hi_ushort16)htons(us_alarmid);
            ui_tmp = ALARM_TO_EVENT_STATUS(pst_alarm_val->ui_alarm_enable);
            pst_resp->st_entry[us_entrycount].ui_eventstatus = htonl(ui_tmp);
            us_entrycount++;
        }
    }

    pst_resp->us_entrycount = (hi_ushort16)htons(us_entrycount);

    *pui_outmsglen = sizeof(pst_resp->us_entrycount) + sizeof(pst_resp->st_entry[0]) * us_entrycount;

    return HI_RET_SUCC;
}


hi_uint32 hi_ctcoam_set_event_status(hi_uint32 ui_llidindex, hi_void *pv_inmsg,
                                     hi_uint32 ui_inmsglen,  hi_void *pv_outmsg,
                                     hi_uint32 *pui_outmsglen, hi_uint32 ui_exoam_type)
{
    hi_ctcoam_event_status_s  *pst_req;
    hi_ctcoam_event_status_s  *pst_resp;
    hi_ctcoam_alarm_value_s   *pst_alarm_val;

    hi_ushort16 i;
    hi_uchar8   uc_port = 0;
    hi_ushort16 us_objtype;
    hi_uchar8   uc_port_begin = 0;
    hi_uchar8   uc_port_end   = 0;
    hi_ushort16 us_alarmid;
    hi_ushort16 us_entrycount;
    hi_uint32   ui_objinstance;
    hi_int32 ui_tmp = 0;

    pst_req  = (hi_ctcoam_event_status_s *)pv_inmsg;
    pst_resp = (hi_ctcoam_event_status_s *)pv_outmsg;
    for (i = 0, us_entrycount = 0; i < ntohs(pst_req->us_entrycount); ++i) {
        us_objtype     = (hi_ushort16)ntohs(pst_req->st_entry[i].us_objtype);
        us_alarmid     = (hi_ushort16)ntohs(pst_req->st_entry[i].us_alarmid);
        ui_objinstance = pst_req->st_entry[i].ui_objinstance;
        hi_ctcoam_get_port_by_obj_type_instance(us_objtype,  ui_objinstance,
                                                &uc_port_begin, &uc_port_end);

        for (uc_port = uc_port_begin; uc_port <= uc_port_end; uc_port++) {
            if (HI_RET_SUCC != hi_ctcoam_get_alarm_cfg(us_objtype, uc_port, us_alarmid, &pst_alarm_val)) {
                continue;
            }
            pst_alarm_val->ui_alarm_enable = EVENT_TO_ALARM_STATUS(ntohl(pst_req->st_entry[i].ui_eventstatus));

            pst_resp->st_entry[us_entrycount].us_objtype     = pst_req->st_entry[i].us_objtype;
            ui_tmp = hi_ctcoam_get_instance(us_objtype, ui_objinstance, uc_port);
            pst_resp->st_entry[us_entrycount].ui_objinstance = ui_tmp;
            pst_resp->st_entry[us_entrycount].us_alarmid     = pst_req->st_entry[i].us_alarmid;
            pst_resp->st_entry[us_entrycount].ui_eventstatus = pst_req->st_entry[i].ui_eventstatus;
            us_entrycount++;
        }
    }

    pst_resp->us_entrycount = (hi_ushort16)htons(us_entrycount);

    *pui_outmsglen = sizeof(pst_resp->us_entrycount) + sizeof(pst_resp->st_entry[0]) * us_entrycount;

    return HI_RET_SUCC;
}


hi_uint32 hi_ctcoam_get_event_threshold(hi_uint32 ui_llidindex, hi_void *pv_inmsg,
                                        hi_uint32 ui_inmsglen,  hi_void *pv_outmsg,
                                        hi_uint32 *pui_outmsglen, hi_uint32 ui_exoam_type)
{
    hi_ctcoam_event_get_req_s    *pst_req;
    hi_ctcoam_event_threshold_s  *pst_resp;
    hi_ctcoam_alarm_value_s      *pst_alarm_val;

    hi_ushort16 i;
    hi_uchar8   uc_port = 0;
    hi_ushort16 us_objtype;
    hi_uchar8   uc_port_begin = 0;
    hi_uchar8   uc_port_end   = 0;
    hi_ushort16 us_alarmid;
    hi_ushort16 us_entrycount;
    hi_uint32   ui_objinstance;
    hi_uint32 ui_tmp = 0;

    pst_req  = (hi_ctcoam_event_get_req_s *)pv_inmsg;
    pst_resp = (hi_ctcoam_event_threshold_s *)pv_outmsg;
    for (i = 0, us_entrycount = 0; i < ntohs(pst_req->us_entrycount); ++i) {
        us_objtype     = (hi_ushort16)ntohs(pst_req->st_entry[i].us_objtype);
        us_alarmid     = (hi_ushort16)ntohs(pst_req->st_entry[i].us_alarmid);
        ui_objinstance = pst_req->st_entry[i].ui_objinstance;
        hi_ctcoam_get_port_by_obj_type_instance(us_objtype,  ui_objinstance,
                                                &uc_port_begin, &uc_port_end);

        for (uc_port = uc_port_begin; uc_port <= uc_port_end; uc_port++) {
            if (HI_RET_SUCC != hi_ctcoam_get_alarm_cfg(us_objtype, uc_port, us_alarmid, &pst_alarm_val)) {
                continue;
            }
            pst_resp->st_entry[us_entrycount].us_objtype         = pst_req->st_entry[i].us_objtype;
            ui_tmp = hi_ctcoam_get_instance(us_objtype, ui_objinstance, uc_port);
            pst_resp->st_entry[us_entrycount].ui_objinstance     = ui_tmp;
            pst_resp->st_entry[us_entrycount].us_alarmid         = pst_req->st_entry[i].us_alarmid;
            pst_resp->st_entry[us_entrycount].ui_set_threshold   = htonl(pst_alarm_val->ui_alarm_set_threshold);
            pst_resp->st_entry[us_entrycount].ui_clear_threshold = htonl(pst_alarm_val->ui_alarm_clear_threshold);
            us_entrycount++;
        }
    }

    pst_resp->us_entrycount = (hi_ushort16)htons(us_entrycount);

    *pui_outmsglen = sizeof(pst_resp->us_entrycount) + sizeof(pst_resp->st_entry[0]) * us_entrycount;

    return HI_RET_SUCC;
}


hi_uint32 hi_ctcoam_set_event_threshold(hi_uint32 ui_llidindex, hi_void *pv_inmsg,
                                        hi_uint32 ui_inmsglen,  hi_void *pv_outmsg,
                                        hi_uint32 *pui_outmsglen, hi_uint32 ui_exoam_type)
{
    hi_ctcoam_event_threshold_s  *pst_req;
    hi_ctcoam_event_threshold_s  *pst_resp;
    hi_ctcoam_alarm_value_s      *pst_alarm_val;

    hi_ushort16 i;
    hi_uchar8   uc_port = 0;
    hi_ushort16 us_objtype;
    hi_uchar8   uc_port_begin = 0;
    hi_uchar8   uc_port_end   = 0;
    hi_ushort16 us_alarmid;
    hi_ushort16 us_entrycount;
    hi_uint32   ui_objinstance;
    hi_uint32 ui_tmp = 0;

    pst_req  = (hi_ctcoam_event_threshold_s *)pv_inmsg;
    pst_resp = (hi_ctcoam_event_threshold_s *)pv_outmsg;
    for (i = 0, us_entrycount = 0; i < ntohs(pst_req->us_entrycount); ++i) {
        us_objtype     = (hi_ushort16)ntohs(pst_req->st_entry[i].us_objtype);
        us_alarmid     = (hi_ushort16)ntohs(pst_req->st_entry[i].us_alarmid);
        ui_objinstance = pst_req->st_entry[i].ui_objinstance;
        hi_ctcoam_get_port_by_obj_type_instance(us_objtype,  ui_objinstance,
                                                &uc_port_begin, &uc_port_end);

        for (uc_port = uc_port_begin; uc_port <= uc_port_end; uc_port++) {
            if (HI_RET_SUCC != hi_ctcoam_get_alarm_cfg(us_objtype, uc_port, us_alarmid, &pst_alarm_val)) {
                continue;
            }

            pst_alarm_val->ui_alarm_set_threshold   = ntohl(pst_req->st_entry[i].ui_set_threshold);
            pst_alarm_val->ui_alarm_clear_threshold = ntohl(pst_req->st_entry[i].ui_clear_threshold);

            if (pst_alarm_val->ui_alarm_set_threshold < pst_alarm_val->ui_alarm_clear_threshold) {
                continue;
            }
            pst_resp->st_entry[us_entrycount].us_objtype         = pst_req->st_entry[i].us_objtype;
            ui_tmp = hi_ctcoam_get_instance(us_objtype, ui_objinstance, uc_port);
            pst_resp->st_entry[us_entrycount].ui_objinstance     = ui_tmp;
            pst_resp->st_entry[us_entrycount].us_alarmid         = pst_req->st_entry[i].us_alarmid;
            pst_resp->st_entry[us_entrycount].ui_set_threshold   = pst_req->st_entry[i].ui_set_threshold;
            pst_resp->st_entry[us_entrycount].ui_clear_threshold = pst_req->st_entry[i].ui_clear_threshold;
            us_entrycount++;
        }
    }

    pst_resp->us_entrycount = (hi_ushort16)htons(us_entrycount);

    *pui_outmsglen = sizeof(pst_resp->us_entrycount) + sizeof(pst_resp->st_entry[0]) * us_entrycount;

    return HI_RET_SUCC;
}
#endif


#if PON_FUNC_CLASSIFICATION("适配层内部调用接口")
/*****************************************************************************
 Prototype    : hi_ctcoam_add_classify_rule
 Description  : add control rules for classification, queuing and marking
 Input        : hi_uint32 ui_llidindex
                hi_uint32 ui_port
                hi_uchar8 uc_rulenum
                hi_void *pv_data
 Output       : None
 Return Value : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_add_classify_rule(hi_uint32 ui_llidindex, hi_uint32 ui_port,
                                      hi_uchar8 uc_rulenum, const hi_void *pv_data)
{
    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_del_classify_rule
 Description  : delete control rules for classification, queuing and marking
 Input        : hi_uint32 ui_llidindex
                hi_uint32 ui_port
                hi_uchar8 uc_rulenum
                hi_void *pv_data
 Output       : None
 Return Value : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_del_classify_rule(hi_uint32 ui_llidindex, hi_uint32 ui_port,
                                      hi_uchar8 uc_rulenum, hi_void *pv_data)
{
    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_clr_classify_rule
 Description  : clear all control rules for classification, queuing and marking
 Input        : hi_uint32 ui_llidindex,
                hi_uint32 ui_port
 Output       : None
 Return Value : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_clr_classify_rule(hi_uint32 ui_llidindex, hi_uint32 ui_port)
{
    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_list_classify_rule
 Description  : list all control rules for classification, queuing and marking
 Input        : hi_void *pv_data
 Output       : None
 Return Value : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_list_classify_rule(hi_uint32 ui_llidindex, hi_uint32 ui_port, hi_void *pv_data, hi_uchar8 *puc_len)
{
    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_set_transparent_mode
 Description  : set transparent mode for vlan
 Input        : hi_void *pv_data
                hi_uchar8 uc_length
                hi_uint32 ui_port
 Output       : None
 Return Value : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_set_transparent_mode(hi_void *pv_data, hi_uchar8 uc_length, hi_uint32 ui_port, hi_uint32 ui_llidindex)
{
    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_set_tag_mode
 Description  : set tag mode for vlan
 Input        : hi_void *pv_data
                hi_uchar8 uc_length
                hi_uint32 ui_port
 Output       : None
 Return Value : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_set_tag_mode(hi_void *pv_data, hi_uchar8 uc_length, hi_uint32 ui_port, hi_uint32 ui_llidindex)
{
    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_del_tag_mode
 Description  : delete tag mode for assigned port
 Input        : hi_uint32 ui_port
 Output       : None
 Return Value : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_del_tag_mode(hi_uint32 ui_port)
{
    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_set_translation_mode
 Description  : set translation mode for vlan
 Input        : hi_void *pv_data
                hi_uchar8 uc_length
                hi_uint32 ui_port
                hi_uint32 ui_llidindex
 Output       : None
 Return Value : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_set_translation_mode(hi_void *pv_data, hi_uchar8 uc_length, hi_uint32 ui_port, hi_uint32 ui_llidindex)
{
    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_del_translation_mode
 Description  : delete vlan translation mode configure
 Input        : hi_uint32 ui_port
 Output       : None
 Return Value : hi_uint32
****************************************************************************/
hi_uint32 hi_ctcoam_del_translation_mode(hi_uint32 ui_port)
{
    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_set_nto1_aggregation_mode
 Description  : n:1 vlan aggregation configure
 Input        : hi_void *pv_data
                hi_uchar8 uc_length
                hi_uint32 ui_port
 Output       : None
*****************************************************************************/
hi_uint32 hi_ctcoam_set_nto1_aggregation_mode(hi_void *pv_data, hi_uchar8 uc_length,
        hi_uint32 ui_port, hi_uint32 ui_llidindex)
{
    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_set_trunk_mode
 Description  : set trunk mode
 Input        : hi_void *pv_data
                hi_uchar8 uc_length
                hi_uint32 ui_port
 Output       : None
*****************************************************************************/
hi_uint32 hi_ctcoam_set_trunk_mode(hi_void *pv_data, hi_uchar8 uc_length,
                                   hi_uint32 ui_port, hi_uint32 ui_llidindex)
{
    return HI_RET_SUCC;
}


/*****************************************************************************
 函 数 名  : hi_ctcoam_tapi_dba_refresh_report
 功能描述  : 刷新特定帧长测试使能模式下Report上报值
 输入参数  : void
 输出参数  : 无
 返 回 值  : static hi_uint32
*****************************************************************************/
static hi_uint32 hi_ctcoam_tapi_dba_refresh_report(hi_void)
{
    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_alarm_admin_state
 Description  : 获取告警状态
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_alarm_admin_state(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    hi_ctcoam_alarm_admin_state_s *pst_state    = (hi_ctcoam_alarm_admin_state_s *)pv_inmsg;
    hi_ctcoam_alarm_admin_state_s *pst_outstate = (hi_ctcoam_alarm_admin_state_s *)pv_outmsg;
    hi_ctcoam_alarm_value_s        *pst_value;

    /* 检查输入指针是否为空 */
    if ((HI_NULL == pv_inmsg) || (HI_NULL == pv_outmsg) || (HI_NULL == pst_instance)) {
        return HI_RET_INVALID_PARA;
    }

    if (HI_RET_SUCC != hi_ctcoam_get_alarm_cfg(pst_instance->us_instancetype,
            pst_instance->un_value.uc_value,
            (hi_ushort16)ntohs(pst_state->us_alarm_id),
            &pst_value)) {
        return HI_RET_FAIL;
    }

    pst_outstate->us_alarm_id          = pst_state->us_alarm_id;
    pst_outstate->ui_alarm_enable_flag = htonl(pst_value->ui_alarm_enable);

    (hi_void)ui_llidindex;

    return HI_RET_SUCC;
}

/*****************************************************************************
 Prototype    : hi_ctcoam_set_alarm_admin_state
 Description  : 设置告警状态
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_alarm_admin_state(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    hi_ctcoam_alarm_admin_state_s *pst_state = (hi_ctcoam_alarm_admin_state_s *)pv_inmsg;
    hi_ctcoam_alarm_value_s        *pst_value;
    hi_uint32   ui_enable_flag;
    hi_ushort16 us_alarmid;

    /* 检查输入指针是否为空 */
    if ((HI_NULL == pv_inmsg) || (HI_NULL == pst_instance)) {
        return HI_RET_INVALID_PARA;
    }
    ui_enable_flag = ntohl(pst_state->ui_alarm_enable_flag);
    us_alarmid     = (hi_ushort16)ntohs(pst_state->us_alarm_id);

    if ((ui_enable_flag < HI_ADMIN_STATE_DISABLE_E)
        || (ui_enable_flag > HI_ADMIN_STATE_ENABLE_E)) {
        return HI_RET_INVALID_PARA;
    }

    if (HI_RET_SUCC != hi_ctcoam_get_alarm_cfg(pst_instance->us_instancetype,
            pst_instance->un_value.uc_value,
            us_alarmid,
            &pst_value)) {
        return HI_RET_FAIL;
    }

    pst_value->ui_alarm_enable = ui_enable_flag;

    (hi_void)ui_llidindex;

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_alarm_threshold
 Description  : 获取告警阈值
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_alarm_threshold(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                        hi_void *pv_outmsg)
{
    const hi_ctcoam_alarm_threshold_s *pst_threshold;
    hi_ctcoam_alarm_threshold_s *pst_outthreshold;
    hi_ctcoam_alarm_value_s      *pst_value;

    /* 检查输入指针是否为空 */
    if ((HI_NULL == pv_inmsg) || (HI_NULL == pv_outmsg) || (HI_NULL == pst_instance)) {
        return HI_RET_INVALID_PARA;
    }

    pst_threshold = (hi_ctcoam_alarm_threshold_s *)pv_inmsg;
    pst_outthreshold = (hi_ctcoam_alarm_threshold_s *)pv_outmsg;
    if (HI_RET_SUCC != hi_ctcoam_get_alarm_cfg(pst_instance->us_instancetype,
            pst_instance->un_value.uc_value,
            (hi_ushort16)htons(pst_threshold->us_alarm_id),
            &pst_value)) {
        return HI_RET_FAIL;
    }

    pst_outthreshold->us_alarm_id        = pst_threshold->us_alarm_id;
    pst_outthreshold->ui_set_threshold   = htonl(pst_value->ui_alarm_set_threshold);
    pst_outthreshold->ui_clear_threshold = htonl(pst_value->ui_alarm_clear_threshold);

    (hi_void)ui_llidindex;
    (hi_void)puc_changingmsglen;

    return HI_RET_SUCC;
}

/*****************************************************************************
 Prototype    : hi_ctcoam_set_alarm_threshold
 Description  : 设置告警阈值
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_alarm_threshold(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
                                        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
                                        hi_void *pv_outmsg)
{
    hi_ctcoam_alarm_threshold_s *pst_threshold;
    hi_ctcoam_alarm_value_s      *pst_value;
    hi_ushort16 alarm_id = 0;

    /* 检查输入指针是否为空 */
    if ((HI_NULL == pv_inmsg) || (HI_NULL == pst_instance)) {
        hi_ctcoam_dbg_err_print("set alarm threshold para is null pointer\n");
        return HI_RET_INVALID_PARA;
    }

    pst_threshold = (hi_ctcoam_alarm_threshold_s *)pv_inmsg;
    alarm_id = (hi_ushort16)ntohs(pst_threshold->us_alarm_id);

    /* modified by hwz for bug#16139 */
    if (alarm_id == HI_NNI_PORT_RX_OPTICAL_POWER_HIGH_ALARM_E || alarm_id == HI_NNI_PORT_TX_OPTICAL_POWER_HIGH_ALARM_E
        || alarm_id == HI_NNI_PORT_TX_BIAS_HIGH_ALARM_E || alarm_id == HI_NNI_PORT_VCC_HIGH_ALARM_E
        || alarm_id == HI_NNI_PORT_TEMP_HIGH_ALARM || alarm_id == HI_NNI_PORT_RX_POWER_HIGH_WARNING
        || alarm_id == HI_NNI_PORT_TX_POWER_HIGH_WARNING || alarm_id == HI_NNI_PORT_TX_BIAS_HIGH_WARNING
        || alarm_id == HI_NNI_PORT_VCC_HIGH_WARNING || alarm_id == HI_NNI_PORT_TEMP_HIGH_WARNING
        || alarm_id == HI_ONU_TEMP_HIGH_ALARM)
    {
        if (ntohl(pst_threshold->ui_set_threshold) <=  ntohl(pst_threshold->ui_clear_threshold)) {
            printf("[%s]return HI_RET_INVALID_PARA, because set_threshold <= clear_threshold: alarm_id = 0x%04X, set_threshold = 0x%x clear_threshold = 0x%x\n",
                 __FUNCTION__, alarm_id, ntohl(pst_threshold->ui_set_threshold), ntohl(pst_threshold->ui_clear_threshold));
            return HI_RET_INVALID_PARA;
        }
    }

    if (HI_RET_SUCC != hi_ctcoam_get_alarm_cfg(pst_instance->us_instancetype,
            (hi_lsw_port_e)pst_instance->un_value.uc_value,
            (hi_ushort16)ntohs(pst_threshold->us_alarm_id),
            &pst_value)) {
        return HI_RET_FAIL;
    }

    pst_value->ui_alarm_set_threshold   = ntohl(pst_threshold->ui_set_threshold);
    pst_value->ui_alarm_clear_threshold = ntohl(pst_threshold->ui_clear_threshold);

    (hi_void)ui_llidindex;

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_get_statistics_state
 Description  : 获取统计使能状态
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_statistics_state(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    hi_lsw_port_e em_port = (hi_lsw_port_e)pst_instance->un_value.uc_value;
    hi_ctcoam_monitoring_cfg_s st_cfg;

    /* 检查输入指针是否为空 */
    if ((HI_NULL == pv_outmsg) || (HI_NULL == puc_changingmsglen)) {
        return HI_RET_INVALID_PARA;
    }

    st_cfg.us_monitoring_status = (hi_ushort16)htons(g_us_port_monitor_cfg[PORT(em_port)].us_monitoring_status);
    st_cfg.ui_monitoring_period = htons(g_us_port_monitor_cfg[PORT(em_port)].ui_monitoring_period);

    HI_OS_MEMCPY_S(pv_outmsg, sizeof(st_cfg), &st_cfg, sizeof(st_cfg));

    (hi_void)pst_instance;
    (hi_void)pv_inmsg;
    (hi_void)ui_llidindex;

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_set_statistics_state
 Description  : 设置统计使能状态
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_statistics_state(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    /* 检查输入指针是否为空 */
    if ((HI_NULL == pv_inmsg) || (HI_NULL == pst_instance)) {
        return HI_RET_INVALID_PARA;
    }

    hi_lsw_port_e em_port = (hi_lsw_port_e)pst_instance->un_value.uc_value;
    hi_ctcoam_monitoring_cfg_s *pst_cfg = (hi_ctcoam_monitoring_cfg_s *)pv_inmsg;
    hi_ctcoam_monitoring_cfg_s st_local_cfg;

    st_local_cfg.us_monitoring_status = (hi_ushort16)ntohs(pst_cfg->us_monitoring_status);
    st_local_cfg.ui_monitoring_period = ntohl(pst_cfg->ui_monitoring_period);
    if ((st_local_cfg.us_monitoring_status != (hi_ushort16)HI_STATISTICS_DISABLE_E)
        && (st_local_cfg.us_monitoring_status != (hi_ushort16)HI_STATISTICS_ENABLE_E)) {
        return HI_RET_INVALID_PARA;
    }

    HI_OS_MEMCPY_S(&g_us_port_monitor_cfg[PORT(em_port)], sizeof(g_us_port_monitor_cfg[PORT(em_port)]), &st_local_cfg, sizeof(hi_ctcoam_monitoring_cfg_s));

    HI_OS_MEMSET_S(&g_st_pre_statistics_data[PORT(em_port)], sizeof(g_st_pre_statistics_data[0]), 0, sizeof(g_st_pre_statistics_data[0]));

    /*启动性能监控定时器*/
    hi_ctcoam_performance_monitoring_thread_proc((hi_uint32)em_port);

    (hi_void)ui_llidindex;
    (hi_void)puc_changingmsglen;
    (hi_void)pv_outmsg;

    return HI_RET_SUCC;
}



/*****************************************************************************
 Prototype    : hi_ctcoam_get_statistics_cur_data
 Description  : 获取当前统计数据
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_statistics_cur_data(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    hi_lsw_port_e  em_port;
    hi_oam_monitoring_data_s st_delta;
    hi_ctcoam_monitoring_report_data_s st_report_data;
    hi_ushort16 *pus_msglen = (hi_ushort16 *)(puc_changingmsglen  + 1);

    memset((void *)&st_delta, 0, sizeof(hi_oam_monitoring_data_s));
    /* 检查输入指针是否为空 */
    if ((HI_NULL == pst_instance) || (HI_NULL == pv_outmsg)) {
        return HI_RET_INVALID_PARA;
    }

    em_port = (hi_lsw_port_e)pst_instance->un_value.uc_value;
    if (HI_STATISTICS_ENABLE_E != g_us_port_monitor_cfg[PORT(em_port)].us_monitoring_status) {
        return HI_RET_FAIL;
    }

    hi_ctcoam_get_statistics_realtime_data((hi_uint32)em_port, &st_delta);
    hi_ctcoam_stat_data_to_report_data(&st_delta, &st_report_data, HI_CTCOAM_LEAF_STATISTICS_CUR_DATA);
    HI_OS_MEMCPY_S((void *)pv_outmsg, sizeof(st_report_data), (void *)&st_report_data, sizeof(st_report_data));

    *puc_changingmsglen = HI_CTCOAM_STATISTICS_TLV_LEN;
    *pus_msglen = HI_CTCOAM_STATISTICS_MSG_LEN;

    return HI_RET_SUCC;
}


/*****************************************************************************
 Prototype    : hi_ctcoam_set_statistics_cur_data
 Description  : 清除PON口统计数据,设置数据必须为0
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_set_statistics_cur_data(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    hi_lsw_port_e  em_port;
    hi_oam_monitoring_data_s st_acc_data;

    /* 检查输入指针是否为空 */
    if ((HI_NULL == pv_inmsg) || (HI_NULL == pst_instance)
        || (HI_NULL == puc_changingmsglen)) {
        return HI_RET_INVALID_PARA;
    }
    if (*puc_changingmsglen == 0x8) {
        return HI_RET_SUCC;
    }

    em_port = (hi_lsw_port_e)pst_instance->un_value.uc_value;

    if (HI_STATISTICS_ENABLE_E != g_us_port_monitor_cfg[PORT(em_port)].us_monitoring_status) {
        return HI_RET_FAIL;
    }

    /*获取该端口累计统计信息*/
    HI_OS_MEMSET_S(&st_acc_data, sizeof(st_acc_data), 0, sizeof(st_acc_data));
    (hi_void)hi_ctcoam_get_cur_acc_statistics_data(em_port, &st_acc_data);

    /*更新累计统计信息*/
    HI_OS_MEMCPY_S(&g_st_acc_statistics_data[PORT(em_port)], sizeof(g_st_acc_statistics_data[PORT(em_port)]), &st_acc_data, sizeof(hi_oam_monitoring_data_s));

    *puc_changingmsglen = HI_CTCOAM_STATISTICS_TLV_LEN;

    return HI_RET_SUCC;
}

/*****************************************************************************
 Prototype    : hi_ctcoam_get_statistics_pre_data
 Description  : 获取pon口统计数据
 Input        : hi_uint32 ui_llidindex
                hi_ctcoam_instance_s *pst_instance
                const hi_void *pv_inmsg
                hi_uchar8 *puc_changingmsglen
                hi_void *pv_outmsg
 Output       : None
 Return Value :
*****************************************************************************/
hi_uint32 hi_ctcoam_get_statistics_pre_data(hi_uint32 ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const hi_void *pv_inmsg, hi_uchar8 *puc_changingmsglen,
        hi_void *pv_outmsg)
{
    /* 检查输入指针是否为空 */
    if ((HI_NULL == pst_instance) || (HI_NULL == pv_outmsg) || (HI_NULL == puc_changingmsglen)) {
        return HI_RET_INVALID_PARA;
    }

    hi_lsw_port_e em_port;
    hi_oam_monitoring_data_s         st_delta;
    hi_ctcoam_monitoring_report_data_s  st_report_data;
    hi_ushort16 *pus_msglen = (hi_ushort16 *)(puc_changingmsglen  + 1);

    em_port = (hi_lsw_port_e)pst_instance->un_value.uc_value;

    if (HI_STATISTICS_ENABLE_E != g_us_port_monitor_cfg[PORT(em_port)].us_monitoring_status) {
        return HI_RET_FAIL;
    }

    HI_OS_MEMCPY_S((void *)&st_delta, sizeof(st_delta), (void *)&g_st_pre_statistics_data[PORT(em_port)], sizeof(st_delta));
    hi_ctcoam_stat_data_to_report_data(&st_delta, &st_report_data, HI_CTCOAM_LEAF_STATISTICS_PRE_DATA);
    HI_OS_MEMCPY_S((void *)pv_outmsg, sizeof(st_report_data), (void *)&st_report_data, sizeof(st_report_data));

    *puc_changingmsglen = HI_CTCOAM_STATISTICS_TLV_LEN;
    *pus_msglen = HI_CTCOAM_STATISTICS_MSG_LEN;

    return HI_RET_SUCC;
}

#endif


//============================================================================
//                              内部函数
//============================================================================

/*****************************************************************************
 函 数 名  : hi_ctcoam_flow_list_one_container
 功能描述  : 显示一个流分类container
 输入参数  : hi_ctcoam_container_hdr_s *pst_container,
             hi_uchar8 uc_len
             hi_uchar8 uc_rule_num
 输出参数  :
 返 回 值  : hi_uint32
*****************************************************************************/
hi_void hi_ctcoam_flow_list_one_container(hi_ctcoam_container_hdr_s *pst_container,
        hi_uchar8 uc_len, hi_uchar8 uc_rule_num)
{
    if (uc_len == 128) {
        uc_len = 0; //按照Variable Container格式，用0表示128
    }
    pst_container->st_tlv.uc_branch = HI_CTCOAM_BRANCH_EXT_ATTR;
    pst_container->st_tlv.us_leaf   = (hi_ushort16)htons(HI_CTCOAM_LEAF_FLOW_CLASS_AND_MARK); /*lint !e572*/
    pst_container->st_tlv.uc_length = uc_len;
    pst_container->st_classify_mark.uc_action = HI_FLOW_LIST_E;
    pst_container->st_classify_mark.uc_rulesnum = uc_rule_num;
}

/*****************************************************************************
 函 数 名  : hi_ctcoam_rule_hdr_get
 功能描述  : 获取rule头
 输入参数  : oam_clasmark_rulebody_t *pst_src
             hi_ctcoam_classify_rule_connect_s *pst_dst
 输出参数  :
 返 回 值  : hi_uint32
*****************************************************************************/
hi_void hi_ctcoam_rule_hdr_get(oam_clasmark_rulebody_t *pst_src, hi_ctcoam_classify_rule_connect_s *pst_dst)
{
    pst_dst->uc_precedence  = pst_src->precedenceOfRule;
    pst_dst->uc_length      = pst_src->lenOfRule;
    pst_dst->uc_queuemapped = pst_src->queueMapped;
    pst_dst->uc_primark     = pst_src->ethPriMark;
    pst_dst->uc_entrynum    = pst_src->numOfField;
}

/*****************************************************************************
 函 数 名  : hi_ctcoam_rule_field_get
 功能描述  : 获取rule field
 输入参数  : oam_clasmark_fieldbody_t *pst_src
             hi_ctcoam_classify_field_value_oper_s *pst_dst
             hi_uchar8 *puc_len
 输出参数  :
 返 回 值  : hi_uint32
*****************************************************************************/
hi_void hi_ctcoam_rule_field_get(oam_clasmark_fieldbody_t *pst_src, hi_ctcoam_classify_field_value_oper_s *pst_dst, hi_uchar8 *puc_len)
{
    hi_ctcoam_classify_value_oper_ipv4_s *pst_oper4;
    hi_ctcoam_classify_value_oper_ipv6_s *pst_oper6;

    pst_dst->uc_select = pst_src->fieldSelect;

    if (pst_dst->uc_select < HI_IP_VER_E) {
        pst_oper4 = &pst_dst->un_value_oper.st_oper4;
        pst_oper4->uc_operator = pst_src->operator;
        HI_OS_MEMCPY_S(pst_oper4->auc_matchvalue, sizeof(pst_oper4->auc_matchvalue),
                       pst_src->matchValue,
                       sizeof(pst_oper4->auc_matchvalue));
        *puc_len = sizeof(pst_dst->uc_select) + sizeof(*pst_oper4);
    } else {
        pst_oper6 = &pst_dst->un_value_oper.st_oper6;
        pst_oper6->uc_operator = pst_src->operator;
        HI_OS_MEMCPY_S(pst_oper6->auc_matchvalue, sizeof(pst_oper6->auc_matchvalue),
                       pst_src->matchValue,
                       sizeof(pst_oper6->auc_matchvalue));
        *puc_len = sizeof(pst_dst->uc_select) + sizeof(*pst_oper6);
    }
}

/*****************************************************************************
 函 数 名  : hi_ctcoam_rule_get
 功能描述  : 获取rule
 输入参数  : oam_clasmark_rulebody_t *pst_src,
             oam_clasmark_fieldbody_t *pst_src_field,
             hi_ctcoam_classify_rule_connect_s *pst_dst
 输出参数  :
 返 回 值  : hi_uint32
*****************************************************************************/
hi_void hi_ctcoam_rule_get(oam_clasmark_rulebody_t *pst_src,
                           oam_clasmark_fieldbody_t *pst_src_field,
                           hi_ctcoam_classify_rule_connect_s *pst_dst)
{
    hi_uint32 i;
    hi_uchar8 *puc_dst_field;
    hi_uchar8 uc_field_len;
    hi_ctcoam_classify_field_value_oper_s *pst_dst_field;

    hi_ctcoam_rule_hdr_get(pst_src, pst_dst);

    puc_dst_field = (hi_uchar8 *)&pst_dst->st_entry[0];
    for (i = 0; i < pst_dst->uc_entrynum; ++i) {
        pst_dst_field = (hi_ctcoam_classify_field_value_oper_s *)puc_dst_field;
        hi_ctcoam_rule_field_get(&pst_src_field[i], pst_dst_field, &uc_field_len);
        puc_dst_field += uc_field_len;
    }
}


/*****************************************************************************
 函 数 名  : hi_ctcoam_rule_hdr_set
 功能描述  : 设置rule头
 输入参数  : oam_clasmark_rulebody_t *pst_src,
             oam_clasmark_fieldbody_t *pst_src_field,
             hi_ctcoam_classify_rule_connect_s *pst_dst
 输出参数  :
 返 回 值  : hi_uint32
*****************************************************************************/
hi_void hi_ctcoam_rule_hdr_set(hi_ctcoam_classify_rule_connect_s *pst_src,
                               oam_clasmark_rulebody_t *pst_dst)
{
    pst_dst->precedenceOfRule = pst_src->uc_precedence;
    pst_dst->lenOfRule        = pst_src->uc_length;
    pst_dst->queueMapped      = pst_src->uc_queuemapped;
    pst_dst->ethPriMark       = pst_src->uc_primark;
    pst_dst->numOfField       = pst_src->uc_entrynum;

    hi_ctcoam_dbg_print("rule pri = %d, len = %d qmap = %d, mark = %d, fieldnum = %d\n",
                        pst_dst->precedenceOfRule, pst_dst->lenOfRule, pst_dst->queueMapped,
                        pst_dst->ethPriMark, pst_dst->numOfField);

}

/*****************************************************************************
 函 数 名  : hi_ctcoam_rule_field_set
 功能描述  : 设置rule field
 输入参数  : oam_clasmark_rulebody_t *pst_src,
             oam_clasmark_fieldbody_t *pst_src_field,
             hi_ctcoam_classify_rule_connect_s *pst_dst
 输出参数  :
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_rule_field_set(hi_ctcoam_classify_field_value_oper_s *pst_src,
                                   oam_clasmark_fieldbody_t *pst_dst,
                                   hi_uchar8 *puc_field_len)
{
    hi_ctcoam_classify_value_oper_ipv4_s *pst_oper4;
    hi_ctcoam_classify_value_oper_ipv6_s *pst_oper6;
    hi_uint32 i;
    hi_uint32 ui_len;
    hi_uchar8 *puc_val;

    pst_dst->fieldSelect = pst_src->uc_select;

    if (HI_IP_VER_E > pst_dst->fieldSelect) {
        pst_oper4         = &pst_src->un_value_oper.st_oper4;
        pst_dst->operator = pst_oper4->uc_operator;

        HI_OS_MEMCPY_S(pst_dst->matchValue, sizeof(pst_dst->matchValue),
                       pst_oper4->auc_matchvalue,
                       sizeof(pst_oper4->auc_matchvalue));

        *puc_field_len = sizeof(pst_src->uc_select) + sizeof(*pst_oper4);

        ui_len  = sizeof(pst_oper4->auc_matchvalue);
        puc_val = pst_oper4->auc_matchvalue;
    } else {
        pst_oper6 = &pst_src->un_value_oper.st_oper6;
        pst_dst->operator = pst_oper6->uc_operator;
        HI_OS_MEMCPY_S(pst_dst->matchValue, sizeof(pst_dst->matchValue),
                       pst_oper6->auc_matchvalue,
                       sizeof(pst_oper6->auc_matchvalue));

        *puc_field_len += sizeof(pst_src->uc_select) + sizeof(*pst_oper6);

        ui_len  = sizeof(pst_oper6->auc_matchvalue);
        puc_val = pst_oper6->auc_matchvalue;
    }
    (void)puc_val;
    hi_ctcoam_dbg_print("field select = %d, oper = %d\n", pst_dst->fieldSelect, pst_dst->operator);
    for (i = 0; i < ui_len; ++i) {
        hi_ctcoam_dbg_print("%02x ", puc_val[i]);
    }
    hi_ctcoam_dbg_print("\n");

    return HI_RET_SUCC;
}


/*****************************************************************************
 函 数 名  : hi_ctcoam_rule_set
 功能描述  : 设置rule
 输入参数  : oam_clasmark_rulebody_t *pst_src,
             oam_clasmark_fieldbody_t *pst_src_field,
             hi_ctcoam_classify_rule_connect_s *pst_dst
 输出参数  :
 返 回 值  : hi_uint32
*****************************************************************************/
hi_void hi_ctcoam_rule_set(hi_ctcoam_classify_rule_connect_s *pst_src,
                           oam_clasmark_rulebody_t *pst_dst,
                           oam_clasmark_fieldbody_t *pst_dst_field)
{
    hi_uint32 i;
    hi_uchar8 *puc_field;
    hi_uchar8 uc_field_len;
    hi_ctcoam_classify_field_value_oper_s *pst_src_field;

    hi_ctcoam_rule_hdr_set(pst_src, pst_dst);
    puc_field = (hi_uchar8 *)&pst_src->st_entry[0];
    for (i = 0; i < pst_dst->numOfField; ++i) {
        pst_src_field = (hi_ctcoam_classify_field_value_oper_s *)puc_field;
        hi_ctcoam_rule_field_set(pst_src_field, &pst_dst_field[i], &uc_field_len);
        puc_field += uc_field_len;
    }
}




/*****************************************************************************
 函 数 名  : hi_ctcoam_dba_get_qset
 功能描述  : 获取qset
 输入参数  : hi_ctcoam_dba_capability_s *pst_src
             hi_uchar8 *puc_dst
 输出参数  :
 返 回 值  : hi_uint32
*****************************************************************************/
hi_void hi_ctcoam_dba_get_qset(hi_ctcoam_dba_capability_s *pst_src, hi_uchar8 *puc_dst, hi_uint32 *ui_response_length)
{
    hi_uchar8   uc_qsetid;
    hi_uchar8   uc_qid;
    hi_uchar8   uc_inmsg_qid;
    hi_ushort16 us_threshold;
    hi_uchar8   *puc_data;
    hi_ctcoam_threshold_s *pst_thre;
    hi_ctcoam_dba_capability_s *pst_dst;

    pst_dst = (hi_ctcoam_dba_capability_s *)puc_dst;
    pst_dst->uc_queue_set_num = (hi_uchar8)pst_src->uc_queue_set_num;
    *ui_response_length += sizeof(hi_uchar8);

    puc_data = (hi_uchar8 *)&pst_dst->st_threshold[0];
    for (uc_qsetid = 0; uc_qsetid < pst_src->uc_queue_set_num - 1; ++uc_qsetid) {
        pst_thre = (hi_ctcoam_threshold_s *)puc_data;
        pst_thre->uc_report_bitmap = pst_src->st_threshold[uc_qsetid].uc_report_bitmap;

        for (uc_qid = 0, uc_inmsg_qid = 0; uc_qid < HI_CTCOAM_THRESHOLD_QUEUE_NUM; ++uc_qid) {
            us_threshold = pst_src->st_threshold[uc_qsetid].aus_threshold[uc_qid];
            us_threshold = (hi_ushort16)htons(us_threshold);
            if (0 != us_threshold) {
                pst_thre->aus_threshold[uc_inmsg_qid] = us_threshold;
                uc_inmsg_qid++;
            }
        }
        puc_data += sizeof(pst_thre->uc_report_bitmap) + (sizeof(pst_thre->aus_threshold[0]) * uc_inmsg_qid);
        *ui_response_length += sizeof(pst_thre->uc_report_bitmap) + (sizeof(pst_thre->aus_threshold[0]) * uc_inmsg_qid);
    }

    return;
}

/*****************************************************************************
 函 数 名  : hi_ctcoam_dba_threshold_get_response
 功能描述  : dba get response
 输入参数  : hi_uint32 ui_llid
             hi_uint32 ui_exoam_type
             hi_ctcoam_dba_capability_s *pst_cap
 输出参数  :
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_dba_threshold_get_response(hi_uint32 ui_llid, hi_uint32 ui_exoam_type, hi_ctcoam_dba_capability_s *pst_cap)
{
    hi_uint32 ui_ret;
    hi_uchar8 *puc_response;
    hi_uchar8 *puc_responsetmp;
    hi_uint32 ui_response_length = 0;
    hi_ctcoam_msg_head_s *pst_ctcoamhead;
    hi_ctcoam_dba_msg_s   *pst_dbamsg = HI_NULL;

    puc_response = (hi_uchar8 *)hi_os_malloc(HI_EPON_MAX_OAM_LEN);
    if (HI_NULL == puc_response) {
        hi_ctcoam_dbg_err_print("Malloc Failed.");
        return HI_RET_MALLOC_FAIL;
    }
    HI_OS_MEMSET_S(puc_response, HI_EPON_MAX_OAM_LEN, 0, HI_EPON_MAX_OAM_LEN);

    /* 跳到填充位置,从OUI开始由本函数填充 */
    puc_responsetmp = puc_response;
    puc_responsetmp += sizeof(hi_epon_oam_s); /* 跳过以太头 */
    ui_response_length += sizeof(hi_epon_oam_s);
    pst_ctcoamhead = (hi_ctcoam_msg_head_s *)((hi_void *)puc_responsetmp);

    /* 填充CTC OUI和Get回应标志 */
    ui_ret = hi_ctcoam_filloui(ui_exoam_type, pst_ctcoamhead->auc_oui);
    if (HI_RET_SUCC != ui_ret) {
        hi_ctcoam_dbg_err_print("Fill OUI Failed(%d).", ui_ret);
        hi_os_free(puc_response);
        puc_response = HI_NULL;
        return ui_ret;
    }

    pst_ctcoamhead->uc_opercode = HI_CTC_OPER_DBA_E;
    puc_responsetmp += sizeof(hi_ctcoam_msg_head_s);
    ui_response_length += sizeof(hi_ctcoam_msg_head_s);

    pst_dbamsg = (hi_ctcoam_dba_msg_s *)((hi_void *)puc_responsetmp);
    pst_dbamsg->uc_dba_opercode = HI_DBA_OPERCODE_GET_DBA_RESPONSE_E;
    ui_response_length += sizeof(hi_uchar8);

    hi_ctcoam_dba_get_qset(pst_cap, &pst_dbamsg->uc_queue_set_num, &ui_response_length);

    if (ui_response_length <= HI_CTCOAM_MSG_MAX_LEN) {
        ui_response_length = HI_CTCOAM_MSG_MAX_LEN;
    }

    hi_ctcoam_dbg_print("\r\n get_response response_length[%d] \n", ui_response_length);
    ui_ret = hi_oam_api_txmsgproc(ui_llid, (hi_void *)puc_response, ui_response_length, HI_OAM_CODE_ORGANIZATION_SPEC_E);
    if (HI_RET_SUCC != ui_ret) {
        hi_ctcoam_dbg_err_print("Tx ExtOam_TLV Fail(%d).", ui_ret);
        hi_os_free(puc_response);
        puc_response = HI_NULL;
        return ui_ret;
    }

    hi_os_free(puc_response);
    puc_response = HI_NULL;
    return HI_RET_SUCC;

}


/*****************************************************************************
 函 数 名  : hi_ctcoam_dba_threshold_report_fill
 功能描述  : 填写dba set 报告
 输入参数  : hi_uchar8 *puc_response
             hi_uchar8 uc_setack
             hi_uint32 ui_exoam_type
             hi_void *pv_inmsg
             hi_uint32 ui_inmsglen
 输出参数  :
 返 回 值  : hi_uint32
*****************************************************************************/

hi_uint32 hi_ctcoam_dba_threshold_report_fill(hi_uchar8 *puc_response, hi_uint32 *ui_response_length,
    hi_uchar8 uc_setack, hi_uint32 ui_exoam_type, hi_void *pv_inmsg, hi_uint32 ui_inmsglen)
{
    hi_uint32  ui_ret;
    hi_uchar8  *puc_responsetmp;
    hi_ctcoam_dba_set_response_s *pst_dbamsg;
    hi_ctcoam_msg_head_s *pst_ctcoamhead;

    HI_OS_MEMSET_S(puc_response, HI_EPON_MAX_OAM_LEN, 0, HI_EPON_MAX_OAM_LEN);

    /* 跳到填充位置,从OUI开始由本函数填充 */
    puc_responsetmp = puc_response;
    puc_responsetmp += sizeof(hi_epon_oam_s); /* 跳过以太头 */
    *ui_response_length += sizeof(hi_epon_oam_s);
    pst_ctcoamhead = (hi_ctcoam_msg_head_s *)((hi_void *)puc_responsetmp);

    /* 填充CTC OUI和Set回应标志 */
    ui_ret = hi_ctcoam_filloui(ui_exoam_type, pst_ctcoamhead->auc_oui);
    if (HI_RET_SUCC != ui_ret) {
        hi_ctcoam_dbg_err_print("Fill OUI Failed(%d).", ui_ret);
        hi_os_free(puc_response);
        puc_response = HI_NULL;
        return ui_ret;
    }

    pst_ctcoamhead->uc_opercode = HI_CTC_OPER_DBA_E;
    puc_responsetmp += sizeof(hi_ctcoam_msg_head_s);
    *ui_response_length += sizeof(hi_ctcoam_msg_head_s);
    pst_dbamsg = (hi_ctcoam_dba_set_response_s *)((hi_void *)puc_responsetmp);
    pst_dbamsg->uc_dba_opercode = HI_DBA_OPERCODE_SET_DBA_RESPONSE_E;
    pst_dbamsg->uc_setack = uc_setack;

    /* 长度包括ucDbaOperCode和ucSetAck */
    puc_responsetmp += (sizeof(hi_uchar8) * 2);
    *ui_response_length += (sizeof(hi_uchar8) * 2);

    /* 其他内容拷贝,最后填充成功失败 */
    HI_OS_MEMCPY_S(puc_responsetmp,
                   HI_EPON_MAX_OAM_LEN - sizeof(hi_epon_oam_s) - sizeof(hi_ctcoam_msg_head_s) - (sizeof(hi_uchar8) * 2),
                   pv_inmsg, ui_inmsglen);
    *ui_response_length += ui_inmsglen;
    return HI_RET_SUCC;
}


/*****************************************************************************
 函 数 名  : hi_ctcoam_dba_threshold_set_response
 功能描述  : 填写dba set 报告
 输入参数  : hi_uint32 ui_llid
             hi_uchar8 uc_setack
             hi_int32 ui_exoam_type
             hi_void *pv_inmsg
             hi_uint32 ui_inmsglen
 输出参数  :
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_dba_threshold_set_response(hi_uint32 ui_llid, hi_uchar8 uc_setack, hi_int32 ui_exoam_type, hi_void *pv_inmsg, hi_uint32 ui_inmsglen)
{
    hi_uint32 ui_ret;
    hi_uchar8 *puc_response;
    hi_uint32 ui_response_length = 0;

    puc_response = (hi_uchar8 *)hi_os_malloc(HI_EPON_MAX_OAM_LEN);
    if (HI_NULL == puc_response) {
        hi_ctcoam_dbg_err_print("Malloc Failed.");
        return HI_RET_MALLOC_FAIL;
    }
    ui_ret = hi_ctcoam_dba_threshold_report_fill(puc_response, &ui_response_length, uc_setack, ui_exoam_type, pv_inmsg, ui_inmsglen);
    if (HI_RET_SUCC != ui_ret) {
        return ui_ret;
    }

    if (ui_inmsglen <= (HI_CTCOAM_DBA_SET_MSG_BODY_MAX_LEN - 1))
        ui_response_length = HI_CTCOAM_MSG_MAX_LEN;

    hi_ctcoam_dbg_print("\r\n set_response response_length[%d] \n", ui_response_length);
    /* 发送扩展OAM消息 */
    ui_ret = hi_oam_api_txmsgproc(ui_llid, (hi_void *)puc_response, ui_response_length, HI_OAM_CODE_ORGANIZATION_SPEC_E);
    if (HI_RET_SUCC != ui_ret) {
        hi_ctcoam_dbg_err_print("Tx ExtOam_TLV Fail(%d).", ui_ret);
    }

    hi_os_free(puc_response);
    puc_response = HI_NULL;

    return HI_RET_SUCC;
}


/*****************************************************************************
 函 数 名  : hi_ctcoam_set_one_qset
 功能描述  : 填写一个qset
 输入参数  : hi_uint32 ui_llid
             hi_uchar8 uc_setack
             hi_int32 ui_exoam_type
             hi_void *pv_inmsg
             hi_uint32 ui_inmsglen
 输出参数  :
 返 回 值  : hi_uint32
*****************************************************************************/
hi_void hi_ctcoam_set_one_qset(hi_ctcoam_threshold_s *pst_src, hi_ctcoam_threshold_s *pst_dst, hi_uchar8 *puc_len)
{
    hi_uchar8 i;
    hi_uchar8 uc_thresnum;
    hi_uchar8 uc_bitmap;
    hi_uchar8 uc_maxnum;

    uc_bitmap = pst_src->uc_report_bitmap;
    pst_dst->uc_report_bitmap = uc_bitmap;

    uc_maxnum = sizeof(pst_src->aus_threshold) / sizeof(pst_src->aus_threshold[0]);
    for (i = 0, uc_thresnum = 0; i < uc_maxnum; ++i, ++uc_thresnum) {
        if (uc_bitmap & (1 << i)) {
            pst_dst->aus_threshold[i] = (hi_ushort16)ntohs(pst_src->aus_threshold[uc_thresnum]);
            hi_ctcoam_dbg_print("\r\n qid=%d threshold=%d\n", i, pst_dst->aus_threshold[i]);
        }
    }
    *puc_len = sizeof(pst_src->uc_report_bitmap) + sizeof(pst_src->aus_threshold[0]) * HI_CTCOAM_THRESHOLD_QUEUE_NUM;

    return;
}

/*****************************************************************************
 函 数 名  : hi_ctcoam_set_qset
 功能描述  : 填写所有qset
 输入参数  : hi_uint32 ui_llid
             hi_uchar8 uc_setack
             hi_int32 ui_exoam_type
             hi_void *pv_inmsg
             hi_uint32 ui_inmsglen
 输出参数  :
 返 回 值  : hi_uint32
*****************************************************************************/
hi_void hi_ctcoam_set_qset(hi_void *pv_dbamsg, hi_ctcoam_dba_capability_s *pst_cap)
{
    int i;
    hi_uchar8 *puc_thres;
    hi_uchar8 uc_len;
    hi_uchar8 uc_qsetnum;
    hi_ctcoam_threshold_s *pst_thres;

    uc_qsetnum = *((hi_uchar8 *)pv_dbamsg);
    uc_qsetnum -= 1; //实际下发num = OLT下发qsetnum - 1
    pst_thres  = (hi_ctcoam_threshold_s *)((hi_uchar8 *)pv_dbamsg + 1);
    HI_OS_MEMSET_S(pst_cap, sizeof(*pst_cap), 0, sizeof(*pst_cap));

    pst_cap->uc_queue_set_num = uc_qsetnum;
    puc_thres = (hi_uchar8 *)pst_thres;

    for (i = 0; i < uc_qsetnum; ++i) {
        pst_thres = (hi_ctcoam_threshold_s *)puc_thres;
        hi_ctcoam_set_one_qset(pst_thres, &pst_cap->st_threshold[i], &uc_len);
        puc_thres += uc_len;
    }

    return;
}

/*****************************************************************************
 函 数 名  : hi_ctcoam_get_vlan_transparent_cfg
 功能描述  : 获取vlan透传数据
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_get_vlan_transparent_cfg(hi_void *pv_outmsg, portAllService *pst_cfg, hi_uchar8 *puc_len)
{
    hi_uchar8 *puc_mode = (hi_uchar8 *)pv_outmsg;

    *puc_mode = HI_OAM_VLAN_TRANSPARENT_E;
    *puc_len = sizeof(hi_uchar8);

    return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_ctcoam_get_vlan_tag_cfg
 功能描述  : 获取vlan tag数据
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_get_vlan_tag_cfg(hi_void *pv_outmsg, portAllService *pst_cfg, hi_uchar8 *puc_len)
{
    hi_uchar8 *puc_data = (hi_uchar8 *)pv_outmsg;
    hi_ctcoam_vlan_tag_s *pst_defvlan;

    *puc_data = HI_OAM_VLAN_TAG_E;
    pst_defvlan = (hi_ctcoam_vlan_tag_s *)(puc_data + sizeof(*puc_data));
    pst_defvlan->us_vlan = (hi_ushort16)htons(pst_cfg->vlanInfo[0].inVlan & 0xffff);
    pst_defvlan->us_tpid = (hi_ushort16)htons(pst_cfg->vlanInfo[0].inVlan >> 16);

    *puc_len = sizeof(hi_uchar8) + sizeof(*pst_defvlan);

    hi_ctcoam_dbg_print("get def_tpid=%04x, def_vlan=%04x\n", pst_defvlan->us_tpid, pst_defvlan->us_vlan);
    return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_ctcoam_get_vlan_translation_cfg
 功能描述  : 获取vlan 1:1翻译数据
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_get_vlan_translation_cfg(hi_void *pv_outmsg, portAllService *pst_cfg, hi_uchar8 *puc_len)
{
    hi_uint32 i;
    hi_ushort16 us_vlan;
    hi_ushort16 us_tpid;
    hi_uchar8   *puc_data = (hi_uchar8 *)pv_outmsg;
    hi_ctcoam_vlan_translate_s      *pst_trans;
    hi_ctcoam_vlan_translate_list_s *pst_list;

    *puc_data = HI_OAM_VLAN_TRANSLATION_E;
    pst_trans = (hi_ctcoam_vlan_translate_s *)(puc_data + sizeof(*puc_data));

    RET_CHK(hi_oam_get_port_default_tag((hi_uchar8)pst_cfg->port_index, &us_tpid, &us_vlan));
    pst_trans->st_defvlan.us_vlan = (hi_ushort16)htons(us_vlan);
    pst_trans->st_defvlan.us_tpid = (hi_ushort16)htons(us_tpid);

    hi_ctcoam_dbg_print("get def_tpid=%04x, def_vlan=%04x\n", us_tpid, us_vlan);

    for (i = 0; i < pst_cfg->serviceNum; ++i) {
        pst_list = &pst_trans->st_list[i];
        pst_list->st_src_tag.us_tpid = (hi_ushort16)htons(pst_cfg->vlanInfo[i].inVlan >> 16);
        pst_list->st_src_tag.us_vlan = (hi_ushort16)htons(pst_cfg->vlanInfo[i].inVlan & 0xffff);
        pst_list->st_dst_tag.us_tpid = (hi_ushort16)htons(pst_cfg->vlanInfo[i].outVlan >> 16);
        pst_list->st_dst_tag.us_vlan = (hi_ushort16)htons(pst_cfg->vlanInfo[i].outVlan & 0xffff);

        hi_ctcoam_dbg_print("get invlan = 0x%08x outvlan = 0x%08x\n",
                            pst_cfg->vlanInfo[i].inVlan,
                            pst_cfg->vlanInfo[i].outVlan);
    }
    *puc_len  = sizeof(hi_uchar8) + sizeof(pst_trans->st_defvlan);
    *puc_len += (hi_uchar8)(sizeof(*pst_list) * pst_cfg->serviceNum);

    return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_ctcoam_get_vlan_aggr_cfg
 功能描述  : 获取vlan N:1翻译数据
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_get_vlan_aggr_cfg(hi_void *pv_outmsg, portAllService *pst_cfg, hi_uchar8 *puc_len)
{
    hi_uint32 i;
    hi_ushort16 us_vlan;
    hi_ushort16 us_tpid;
    hi_ctcoam_vlan_aggr_s *pst_aggr;
    hi_uchar8  *puc_aggr_tbl;
    hi_ctcoam_vlan_aggr_tbl_s *pst_aggr_tbl;
    hi_ushort16 us_tblnum  = 0;
    hi_ushort16 us_vlannum = 0;
    hi_uchar8   *puc_data  = (hi_uchar8 *)pv_outmsg;

    *puc_data = HI_OAM_N_TO_ONE_AGGREGATION_E;
    pst_aggr = (hi_ctcoam_vlan_aggr_s *)(puc_data + sizeof(*puc_data));

    RET_CHK(hi_oam_get_port_default_tag((hi_uchar8)pst_cfg->port_index, &us_tpid, &us_vlan));
    pst_aggr->st_defvlan.us_vlan = (hi_ushort16)htons(us_vlan);
    pst_aggr->st_defvlan.us_tpid = (hi_ushort16)htons(us_tpid);
    hi_ctcoam_dbg_print("get def_tpid=%04x, def_vlan=%04x\n", us_tpid, us_vlan);

    puc_aggr_tbl = (hi_uchar8 *)&pst_aggr->st_aggr_tbl[0];
    pst_aggr_tbl = (hi_ctcoam_vlan_aggr_tbl_s *)puc_aggr_tbl;
    pst_aggr_tbl->st_dst_vlan.us_tpid = (hi_ushort16)htons(pst_cfg->vlanInfo[0].outVlan >> 16); ;
    pst_aggr_tbl->st_dst_vlan.us_vlan = (hi_ushort16)htons(pst_cfg->vlanInfo[0].outVlan & 0xffff);
    us_tblnum  = 1;
    us_vlannum = 0;
    hi_ctcoam_dbg_print("servicenum=%d aggr vlan = %04x %04x\n", pst_cfg->serviceNum,
                        (pst_cfg->vlanInfo[0].outVlan >> 16),
                        (pst_cfg->vlanInfo[0].outVlan & 0xffff));

    for (i = 0; i < pst_cfg->serviceNum; ++i) {
        if (htons(pst_aggr_tbl->st_dst_vlan.us_vlan) != (pst_cfg->vlanInfo[i].outVlan & 0xffff)) {
            pst_aggr_tbl->us_aggr_vlan_num = (hi_ushort16)htons(us_vlannum);

            puc_aggr_tbl += sizeof(*pst_aggr_tbl) + sizeof(pst_aggr_tbl->st_src_vlan[0]) * us_vlannum;
            pst_aggr_tbl = (hi_ctcoam_vlan_aggr_tbl_s *)puc_aggr_tbl;
            pst_aggr_tbl->st_dst_vlan.us_tpid = (hi_ushort16)htons(pst_cfg->vlanInfo[i].outVlan >> 16);
            pst_aggr_tbl->st_dst_vlan.us_vlan = (hi_ushort16)htons(pst_cfg->vlanInfo[i].outVlan & 0xffff);
            us_vlannum = 0;
            us_tblnum++;

            hi_ctcoam_dbg_print("get aggr vlan = 0x%08x\n", pst_cfg->vlanInfo[i].outVlan);
        }
        pst_aggr_tbl->st_src_vlan[us_vlannum].us_vlan = (hi_ushort16)htons(pst_cfg->vlanInfo[i].inVlan & 0xffff);
        pst_aggr_tbl->st_src_vlan[us_vlannum].us_tpid = (hi_ushort16)htons(pst_cfg->vlanInfo[i].inVlan >> 16);
        us_vlannum++;
    }
    pst_aggr_tbl->us_aggr_vlan_num = (hi_ushort16)htons(us_vlannum);
    puc_aggr_tbl += sizeof(*pst_aggr_tbl) + sizeof(pst_aggr_tbl->st_src_vlan[0]) * us_vlannum;

    pst_aggr->us_aggr_tbl_num = (hi_ushort16)htons(us_tblnum);

    *puc_len = sizeof(hi_uchar8) + sizeof(*pst_aggr);
    *puc_len += (hi_uchar8)((hi_uint32)puc_aggr_tbl - (hi_uint32)((hi_uchar8 *)&pst_aggr->st_aggr_tbl[0]));

    return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_ctcoam_get_vlan_trunk_cfg
 功能描述  : 获取vlan trunk数据
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_get_vlan_trunk_cfg(hi_void *pv_outmsg, portAllService *pst_cfg, hi_uchar8 *puc_len)
{
    hi_uint32 i;
    hi_ushort16 us_vlan;
    hi_ushort16 us_tpid;
    hi_ctcoam_vlan_trunk_s *pst_trunk;
    hi_ctcoam_vlan_tag_s *pst_vlan;
    hi_uchar8   *puc_data  = (hi_uchar8 *)pv_outmsg;

    *puc_data = HI_OAM_VLAN_TRUNK_E;
    pst_trunk = (hi_ctcoam_vlan_trunk_s *)(puc_data + sizeof(*puc_data));

    RET_CHK(hi_oam_get_port_default_tag((hi_uchar8)pst_cfg->port_index, &us_tpid, &us_vlan));
    pst_trunk->st_defvlan.us_vlan = (hi_ushort16)htons(us_vlan);
    pst_trunk->st_defvlan.us_tpid = (hi_ushort16)htons(us_tpid);
    hi_ctcoam_dbg_print("get def_tpid=%04x, def_vlan=%04x\n", us_tpid, us_vlan);

    for (i = 0; i < pst_cfg->serviceNum; ++i) {
        pst_vlan = &pst_trunk->st_vlan_in_whitelist[i];
        pst_vlan->us_vlan = (hi_ushort16)htons(pst_cfg->vlanInfo[i].inVlan & 0xffff);
        pst_vlan->us_tpid = (hi_ushort16)htons(pst_cfg->vlanInfo[i].inVlan >> 16);

        hi_ctcoam_dbg_print("get invlan = 0x%08x\n", pst_cfg->vlanInfo[i].inVlan);
    }

    *puc_len  = sizeof(hi_uchar8) + sizeof(*pst_trunk);
    *puc_len += (hi_uchar8)(sizeof(pst_trunk->st_vlan_in_whitelist[0]) * pst_cfg->serviceNum);

    return HI_RET_SUCC;
}


/*****************************************************************************
 函 数 名  : hi_ctcoam_set_vlan_transparent_cfg
 功能描述  : 设置vlan透传参数
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_set_vlan_transparent_cfg(hi_void *pv_inmsg, hi_uchar8 uc_paralen, portAllService *pst_cfg)
{
    pst_cfg->serviceNum = 0;

    return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_ctcoam_set_vlan_tag_cfg
 功能描述  : 设置vlan tag参数
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_set_vlan_tag_cfg(hi_void *pv_inmsg, hi_uchar8 uc_paralen, portAllService *pst_cfg)
{
    hi_ctcoam_vlan_tag_s *pst_vlan;

    pst_vlan = (hi_ctcoam_vlan_tag_s *)pv_inmsg;
    pst_cfg->serviceNum = 1;
    pst_cfg->vlanInfo[0].inVlan  = HI_NTOH_VLANTAG(*pst_vlan);
    pst_cfg->vlanInfo[0].outVlan = pst_cfg->vlanInfo[0].inVlan;

    hi_ctcoam_dbg_print("set vlan_tag: 0x%08x\n", pst_cfg->vlanInfo[0].inVlan);

    return HI_RET_SUCC;
}
/*****************************************************************************
 函 数 名  : hi_ctcoam_set_vlan_translation_cfg
 功能描述  : 设置vlan translation参数
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_set_vlan_translation_cfg(hi_void *pv_inmsg, hi_uchar8 uc_paralen, portAllService *pst_cfg)
{
    hi_uint32   ui_maxvlannum;
    hi_ushort16 us_tpid;
    hi_ushort16 us_vlan;
    hi_uchar8   uc_len;
    hi_uint32   i;
    hi_ctcoam_vlan_translate_s *pst_trans = (hi_ctcoam_vlan_translate_s *)pv_inmsg;

    us_tpid = (hi_ushort16)ntohs(pst_trans->st_defvlan.us_tpid);
    us_vlan = (hi_ushort16)ntohs(pst_trans->st_defvlan.us_vlan);
    hi_ctcoam_dbg_print("set def_tpid=%04x, def_vlan=%04x\n", us_tpid, us_vlan);
    RET_CHK(hi_oam_set_port_default_tag((hi_uchar8)pst_cfg->port_index, us_tpid, us_vlan));

    uc_paralen -= sizeof(hi_uchar8) + sizeof(hi_ctcoam_vlan_tag_s);

    ui_maxvlannum = sizeof(pst_cfg->vlanInfo) / sizeof(pst_cfg->vlanInfo[0]);
    uc_len = sizeof(hi_uchar8) + sizeof(hi_ctcoam_vlan_tag_s);
    for (i = 0; i < ui_maxvlannum; ++i) {
        if (uc_len >= uc_paralen) {
            break;
        }
        pst_cfg->vlanInfo[i].inVlan  = HI_NTOH_VLANTAG(pst_trans->st_list[i].st_src_tag);
        pst_cfg->vlanInfo[i].outVlan = HI_NTOH_VLANTAG(pst_trans->st_list[i].st_dst_tag);
        uc_len += sizeof(pst_trans->st_list[0]);

        hi_ctcoam_dbg_print("set invlan = 0x%08x outvlan = 0x%08x\n",
                            pst_cfg->vlanInfo[i].inVlan,
                            pst_cfg->vlanInfo[i].outVlan);
    }
    pst_cfg->serviceNum = (hi_ushort16)i;
    hi_ctcoam_dbg_print("1:1 vlan translation number = %d\n", i);

    return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_ctcoam_set_vlan_aggr_cfg
 功能描述  : 设置vlan n:1 translation参数
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_set_vlan_aggr_cfg(hi_void *pv_inmsg, hi_uchar8 uc_paralen, portAllService *pst_cfg)
{
    hi_ctcoam_vlan_aggr_s *pst_aggr = (hi_ctcoam_vlan_aggr_s *)pv_inmsg;
    hi_ctcoam_vlan_aggr_tbl_s *pst_aggr_tbl;
    hi_uchar8  *puc_tbl;
    hi_ushort16 us_tbl_id;
    hi_ushort16 us_tpid;
    hi_ushort16 us_vlan;
    hi_uint32   i;
    hi_uint32   j;
    hi_uint32   ui_maxvlan;

    us_tpid = (hi_ushort16)ntohs(pst_aggr->st_defvlan.us_tpid);
    us_vlan = (hi_ushort16)ntohs(pst_aggr->st_defvlan.us_vlan);
    hi_ctcoam_dbg_print("set def_tpid=%04x, def_vlan=%04x\n", us_tpid, us_vlan);
    RET_CHK(hi_oam_set_port_default_tag((hi_uchar8)pst_cfg->port_index, us_tpid, us_vlan));

    ui_maxvlan = sizeof(pst_cfg->vlanInfo) / sizeof(pst_cfg->vlanInfo[0]);

    puc_tbl = (hi_uchar8 *)&pst_aggr->st_aggr_tbl[0];
    for (us_tbl_id = 0, j = 0; us_tbl_id < ntohs(pst_aggr->us_aggr_tbl_num); ++us_tbl_id) {
        pst_aggr_tbl = (hi_ctcoam_vlan_aggr_tbl_s *)puc_tbl;

        for (i = 0; i < ntohs(pst_aggr_tbl->us_aggr_vlan_num); ++i) {
            if (j >= ui_maxvlan) {
                hi_ctcoam_dbg_err_print(" translation vlan number is more than %d\n.", ui_maxvlan);
                goto proc_end;
            }
            pst_cfg->vlanInfo[j].inVlan  = HI_NTOH_VLANTAG(pst_aggr_tbl->st_src_vlan[i]);
            pst_cfg->vlanInfo[j].outVlan = HI_NTOH_VLANTAG(pst_aggr_tbl->st_dst_vlan);

            hi_ctcoam_dbg_print("invlan = 0x%08x outvlan = 0x%08x\n",
                                pst_cfg->vlanInfo[j].inVlan,
                                pst_cfg->vlanInfo[j].outVlan);
            puc_tbl += sizeof(hi_ctcoam_vlan_tag_s);
            j++;
        }
        puc_tbl += sizeof(hi_ctcoam_vlan_aggr_tbl_s);
    }

proc_end:
    pst_cfg->serviceNum = (hi_ushort16)j;
    hi_ctcoam_dbg_print("n:1 vlan translation number = %d\n", j);

    return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_ctcoam_set_vlan_trunk_cfg
 功能描述  : 设置vlan trunk 参数
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_set_vlan_trunk_cfg(hi_void *pv_inmsg, hi_uchar8 uc_paralen, portAllService *pst_cfg)
{
    hi_ctcoam_vlan_trunk_s   *pst_trunk = (hi_ctcoam_vlan_trunk_s *)pv_inmsg;
    hi_uchar8 uc_vlan_num;
    hi_uchar8 uc_maxvlannum;
    hi_ushort16 us_tpid;
    hi_ushort16 us_vlan;
    hi_uchar8 i;

    us_tpid = (hi_ushort16)ntohs(pst_trunk->st_defvlan.us_tpid);
    us_vlan = (hi_ushort16)ntohs(pst_trunk->st_defvlan.us_vlan);
    hi_ctcoam_dbg_print("set def_tpid=%04x, def_vlan=%04x\n", us_tpid, us_vlan);
    RET_CHK(hi_oam_set_port_default_tag((hi_uchar8)pst_cfg->port_index, us_tpid, us_vlan));

    uc_vlan_num   = (uc_paralen - sizeof(pst_trunk->st_defvlan)) / sizeof(hi_ctcoam_vlan_tag_s);
    uc_maxvlannum = sizeof(pst_cfg->vlanInfo) / sizeof(pst_cfg->vlanInfo[0]);
    if (uc_vlan_num > uc_maxvlannum) {
        uc_vlan_num = uc_maxvlannum;
    }
    for (i = 0; i < uc_vlan_num; ++i) {
        pst_cfg->vlanInfo[i].inVlan  = HI_NTOH_VLANTAG(pst_trunk->st_vlan_in_whitelist[i]);
        pst_cfg->vlanInfo[i].outVlan = pst_cfg->vlanInfo[i].inVlan;
        hi_ctcoam_dbg_print("invlan = %u\n", pst_cfg->vlanInfo[i].inVlan);
    }
    pst_cfg->serviceNum = uc_vlan_num;
    hi_ctcoam_dbg_print("1:1 vlan translation number = %u\n", i);

    return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_ctcoam_add_multi_vlan
 功能描述  : 添加组播VLAN
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_add_multi_vlan(hi_void *pv_inmsg, hi_uchar8 uc_paralen, portAllService *pst_cfg)
{
    hi_ushort16 *pst_multi_vlan = (hi_ushort16 *)pv_inmsg;
    hi_uchar8 uc_vlan_num;
    hi_uchar8 uc_maxvlannum;
    hi_uchar8 i, j;
    hi_uint32 ui_service;
    hi_uint32 ui_exsit;

    pst_cfg->multiMode = HI_TRUE;

    ui_service = pst_cfg->multiNum;
    uc_vlan_num   = uc_paralen / sizeof(hi_ushort16);
    uc_maxvlannum = sizeof(pst_cfg->multiVlanInfo) / sizeof(pst_cfg->multiVlanInfo[0]);
    if (uc_vlan_num > (uc_maxvlannum - ui_service)) {
        uc_vlan_num = (hi_uchar8)(uc_maxvlannum - ui_service);
    }

    for (j = 0; j < uc_vlan_num; j++) {
        ui_exsit = HI_FALSE;

        /* VLAN是否已经存在了 */
        for (i = 0; i < ui_service; i++) {
            if (pst_cfg->multiVlanInfo[i].inVlan == ntohs(pst_multi_vlan[j])) {
                hi_ctcoam_dbg_print("multicast vlan[%u][%u] exsit\n",
                                    i, pst_cfg->multiVlanInfo[i].inVlan);

                ui_exsit = HI_TRUE;
                break;
            }
        }

        if (HI_TRUE == ui_exsit) {
            continue;
        }

        pst_cfg->multiVlanInfo[pst_cfg->multiNum].inVlan  = ntohs(pst_multi_vlan[j]);
        pst_cfg->multiVlanInfo[pst_cfg->multiNum].outVlan = pst_cfg->multiVlanInfo[pst_cfg->multiNum].inVlan;
        hi_ctcoam_dbg_print("add multicast invlan = %u\n", pst_cfg->multiVlanInfo[pst_cfg->multiNum].inVlan);
        pst_cfg->multiNum++;
    }

    hi_ctcoam_dbg_print("add multicast vlan number = %u\n", (pst_cfg->multiNum - ui_service));
    return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_ctcoam_del_multi_vlan
 功能描述  : 删除组播VLAN
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_del_multi_vlan(hi_void *pv_inmsg, hi_uchar8 uc_paralen, portAllService *pst_cfg)
{
    hi_ushort16 *pst_multi_vlan = (hi_ushort16 *)pv_inmsg;
    hi_uchar8 uc_vlan_num;
    hi_uchar8 i, j;
    hi_uint32 ui_service;

    ui_service = pst_cfg->multiNum;
    uc_vlan_num = uc_paralen / sizeof(hi_ushort16);
    if (uc_vlan_num > ui_service) {
        uc_vlan_num = (hi_uchar8)ui_service;
    }

    for (i = 0; i < ui_service; ++i) {
        for (j = 0; j < uc_vlan_num; ++j) {
            if (ntohs(pst_multi_vlan[j]) == pst_cfg->multiVlanInfo[i].inVlan) {
                HI_OS_MEMSET_S(&pst_cfg->multiVlanInfo[i], sizeof(pst_cfg->multiVlanInfo[i]), 0, sizeof(pst_cfg->multiVlanInfo[i]));
                pst_cfg->multiNum--;

                for (; i < ui_service; ++i) {
                    if (i == (ui_service - 1)) {
                        HI_OS_MEMSET_S(&pst_cfg->multiVlanInfo[i], sizeof(pst_cfg->multiVlanInfo[i]), 0, sizeof(pst_cfg->multiVlanInfo[i]));
                    } else {
                        HI_OS_MEMCPY_S(&pst_cfg->multiVlanInfo[i], sizeof(pst_cfg->multiVlanInfo[i]), &pst_cfg->multiVlanInfo[i + 1], sizeof(pst_cfg->multiVlanInfo[i]));
                    }
                }
            }
        }
    }

    hi_ctcoam_dbg_print("delete %u multicast vlan\n", (ui_service - pst_cfg->multiNum));
    return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_ctcoam_clr_multi_vlan
 功能描述  : 删除组播VLAN
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_clr_multi_vlan(hi_void *pv_inmsg, hi_uchar8 uc_paralen, portAllService *pst_cfg)
{
    HI_OS_MEMSET_S(pst_cfg->multiVlanInfo, sizeof(pst_cfg->multiVlanInfo), 0, sizeof(pst_cfg->multiVlanInfo));
    pst_cfg->multiNum = 0;

    hi_ctcoam_dbg_print("clean multicast vlan \n");
    return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_ctcoam_get_vlan_trunk_cfg
 功能描述  : 获取vlan trunk数据
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_ctcoam_list_multi_vlan(hi_void *pv_outmsg, portAllService *pst_cfg, hi_uchar8 *puc_len)
{
    hi_uchar8 i;
    hi_ushort16 *pst_trunk;
    hi_uchar8   *puc_data  = (hi_uchar8 *)pv_outmsg;

    *puc_data = HI_OAM_VLAN_MULTICAST_LIST_E;
    pst_trunk = (hi_ushort16 *)(puc_data + sizeof(*puc_data));
    *puc_len += sizeof(*puc_data);

    for (i = 0; i < pst_cfg->multiNum; ++i) {
        *pst_trunk = (hi_ushort16)pst_cfg->multiVlanInfo[i].inVlan;
        *puc_len += sizeof(*pst_trunk);
        pst_trunk++;
    }

    return HI_RET_SUCC;
}

uint32_t hi_ctcoam_get_opt_trans_info(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
		const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
	int32_t ret;
	struct hi_oam_optical_info *opt_info = NULL;

	if (pv_outmsg == NULL)
		return HI_RET_INVALID_PARA;

	opt_info = (struct hi_oam_optical_info *)pv_outmsg;
	ret = hi_oam_get_optical_info(opt_info);
	if (ret != HI_RET_SUCC)
		return HI_RET_FAIL;

	opt_info->tx_wavelength = htons(opt_info->tx_wavelength);
	opt_info->rx_wavelength = htons(opt_info->rx_wavelength);
	return HI_RET_SUCC;
}

uint32_t hi_ctcoam_get_conf_cnt(uint32_t llidindex, hi_ctcoam_instance_s *instance,
        const void *inmsg, uint8_t *changingmsglen, void *outmsg)
{
    uint32_t *cnt = NULL;

    if (outmsg == NULL) {
        return HI_RET_INVALID_PARA;
    }
    cnt = (uint32_t *)outmsg;
    *cnt = htonl(g_config_counter);
    return HI_RET_SUCC;
}

uint32_t hi_ctcoam_set_conf_cnt(uint32_t llidindex, hi_ctcoam_instance_s *instance,
        const void *inmsg, uint8_t *changingmsglen, void *outmsg)
{
    if (inmsg == NULL) {
        return HI_RET_INVALID_PARA;
    }
    g_config_counter = ntohl(*(uint32_t *)inmsg);
    return HI_RET_SUCC;
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
