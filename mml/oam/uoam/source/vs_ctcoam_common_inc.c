#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus

#include "vs_ctcoam_common_inc.h"

int packet_slicing_for_send(void *input_data, unsigned int max_size, unsigned char branch, unsigned short leaf) 
{
    int interval = 128;
    int i = 0;
    int header_pos = 0;
    int move_pos = 0;
    unsigned int move_size = 0;
    int packet_num = (max_size + (interval - 1)) / interval;//the number of packets the input_date will divided
    
    if(max_size <= interval)
        return 0;

    for (i = 1; i < packet_num; i++) 
    {
        header_pos = i * (interval + 4);
        move_pos = i * interval + (i - 1) * 4;
        move_size = max_size - i * interval;

        memmove(((unsigned char *)input_data) + header_pos, ((unsigned char *)input_data) + move_pos, move_size);
        memset(((unsigned char *)input_data) + move_pos, 0, 4);

        ((unsigned char *)input_data)[move_pos] = branch;
        ((unsigned char *)input_data)[move_pos + 1] = (leaf >> 8) & 0xFF; 
        ((unsigned char *)input_data)[move_pos + 2] = leaf & 0xFF; 
        if (i == packet_num - 1) //add length identification to the last packets
        {
            ((unsigned char *)input_data)[move_pos + 3] = move_size;
        }
        else
        {
            ((unsigned char *)input_data)[move_pos + 3] = 0;
        }
    }
    return packet_num;
}

int packet_slicing_for_receive(void *save_date, unsigned int save_date_size, const void *msg, unsigned int max_size, unsigned char branch, unsigned short leaf) 
{
    int i = 0;
    const unsigned char *input_data = (const unsigned char *)msg;
    unsigned char *date = (unsigned char *)save_date;
    unsigned int offset = 0;
    unsigned int packet_num = 0;//the number of packets the input_date will divided
    unsigned int copy_size = 0 , have_copy_size = 0;
    unsigned int remaining_size = 0;
    unsigned int last_size = 0;
    unsigned char leaf_high = (unsigned char)(leaf >> 8);
    unsigned char leaf_low = (unsigned char)leaf;
    unsigned int buf_size = 0;
    unsigned int buf_start = 0;
    unsigned int buf_end = 0;
    
    while(offset + 1 < max_size) 
    {
        if(input_data[offset] == branch && input_data[offset + 1] == leaf_high && input_data[offset + 2] == leaf_low && input_data[offset + 3] == 0)
        {
            buf_end = offset;
            if(have_copy_size < save_date_size) 
            {
                buf_size = buf_end - buf_start;
                remaining_size = save_date_size - have_copy_size;

                if(buf_size < remaining_size) 
                    copy_size = buf_size;
                else 
                    copy_size = remaining_size;

                for(i = 0; i < copy_size; i++) 
                {
                    date[have_copy_size + i] = input_data[buf_start + i];
                }
                packet_num++;
                buf_start = offset + 4;
                have_copy_size += copy_size;
            } 
            else 
            {
                break; 
            }
        } 
        else if (input_data[offset] == branch && input_data[offset + 1] == leaf_high && input_data[offset + 2] == leaf_low && input_data[offset + 3] != 0)//last packets
        {
            last_size = input_data[offset + 3];
            buf_end = offset;
            if(have_copy_size < save_date_size) 
            {
                buf_size = buf_end - buf_start;
                remaining_size = save_date_size - have_copy_size;
                if(buf_size < remaining_size) 
                    copy_size = buf_size;
                else 
                    copy_size = remaining_size;

                for(i = 0; i < copy_size; i++)
                {
                    date[have_copy_size + i] = input_data[buf_start + i];
                }

                packet_num++;
                buf_start = offset + 4;
                have_copy_size += copy_size;

                for(i = 0; i < last_size; i++)//the last part of data
                {
                    date[have_copy_size + i] = input_data[buf_start + i];
                }
                
                packet_num++;
                have_copy_size += last_size;
            } 
            else 
            {
                break; 
            }
        }
        offset++;
    }
    
    return packet_num;
}

int packet_slicing_for_receive_digital_map(void *save_data, const void *msg, unsigned int max_size, unsigned char branch, unsigned short leaf) 
{
    int i = 0;
    const unsigned char *input_data = (const unsigned char *)msg;
    unsigned char *data = (unsigned char *)save_data;
    unsigned int offset = 0;
    unsigned int packet_num = 0;
    unsigned int copy_size = 0 , have_copy_size = 0;
    unsigned char leaf_high = (unsigned char)(leaf >> 8);
    unsigned char leaf_low = (unsigned char)leaf;
    unsigned char loop_first = 0;
    

    if(input_data[0] > 1 && input_data[1] == 1)//first packets head
        packet_num = input_data[0];
    else
        return -1;
    
    while(offset + 1 < max_size) 
    {
        if(packet_num > 0)
        {
            if(loop_first == 0)
                loop_first = 1;
            else
                offset += 4;

            offset += 2;//jump the first two data
            copy_size = 125;
            for(i = 0; i < copy_size; i++)
                data[have_copy_size + i] = input_data[offset + i];

            have_copy_size += copy_size;
            offset += copy_size;
            packet_num--;

            if(input_data[offset] == branch && input_data[offset + 1] == leaf_high && input_data[offset + 2] == leaf_low && input_data[offset + 3] != 0x7f)//the last packets head
            {
                copy_size = input_data[offset + 3] - 2;
                offset += 4;//jump the packet header
                offset += 2;//jump the first two data
                for(i = 0; i < copy_size; i++)
                    data[have_copy_size + i] = input_data[offset + i];

                have_copy_size += copy_size;
                packet_num = 0;
                break;
            }
            else if(input_data[offset] == branch && input_data[offset + 1] == leaf_high && input_data[offset + 2] == leaf_low && input_data[offset + 3] == 0x7f)
            {
                if(packet_num == 1)//the last packets
                {
                    copy_size = 125;
                    offset += 4;//jump the packet header
                    offset += 2;//jump the first two data
                    for(i = 0; i < copy_size; i++)
                        data[have_copy_size + i] = input_data[offset + i];

                    have_copy_size += copy_size;
                    offset += copy_size;
                    packet_num = 0;
                    break;
                }
            }
        }
    }
    
    return have_copy_size;
}


int calculate_packet_length(const void *msg, unsigned int max_size, unsigned char branch, unsigned short leaf) 
{
    const unsigned char *input_data = (const unsigned char *)msg;
    unsigned int offset = 0;
    unsigned int packet_length = 0;
    unsigned char leaf_high = (unsigned char)(leaf >> 8);
    unsigned char leaf_low = (unsigned char)leaf;

    while(offset + 1 < max_size) 
    {
        if(input_data[offset] == branch && input_data[offset + 1] == leaf_high && input_data[offset + 2] == leaf_low && input_data[offset + 3] == 0)
        {
            offset += 4;
        }
        else if(input_data[offset] == branch && input_data[offset + 1] == leaf_high && input_data[offset + 2] == leaf_low && input_data[offset + 3] != 0)
        {
            packet_length += input_data[offset + 3];
            break;
        }
        else
        {
            packet_length++;
            offset++;
        }
    }
    return packet_length;
}

int calculate_mask_bits(unsigned int u32Val) 
{
    int i = 0, mask_bit = 0;
    for(i = 0; i < 32; i++)
    {
        if(u32Val & (1 << i))
        {
            mask_bit++;
        }
    }
    return mask_bit;
}

void printf_oam_hex(const void *msg, size_t length) 
{
    unsigned int i = 0, printf_enable = 0;
    const unsigned char *ptr = (const unsigned char *)msg;

    if(printf_enable)
    {
        printf("\r\n====================================================\r\n\r\n");
        for (i = 0; i < length; i++) 
        {
            printf("%02x ", ptr[i]);
            if ((i + 1) % 16 == 0) 
            {
                printf("\n");
            }
        }
        printf("\n");
        printf("====================================================\r\n");
    }
}

#if 1

int igdCmApiChartoHex(char srcchar)
{
    switch(srcchar)
    {
    case '0':
        return 0;
    case '1':
        return 1;
    case '2':
        return 2;
    case '3':
        return 3;
    case '4':
        return 4;
    case '5':
        return 5;
    case '6':
        return 6;
    case '7':
        return 7;
    case '8':
        return 8;
    case '9':
        return 9;
    case 'A':
    case 'a':
        return 10;
    case 'B':
    case 'b':
        return 11;
    case 'C':
    case 'c':
        return 12;
    case 'D':
    case 'd':
        return 13;
    case 'E':
    case 'e':
        return 14;
    case 'F':
    case 'f':
        return 15;
    default:
        return 16;
    }
}

char igdCmApiHextoChar(unsigned char srchex)
{
    switch(srchex)
    {
    case 0x0:
        return '0';
    case 0x1:
        return '1';
    case 0x2:
        return '2';
    case 0x3:
        return '3';
    case 0x4:
        return '4';
    case 0x5:
        return '5';
    case 0x6:
        return '6';
    case 0x7:
        return '7';
    case 0x8:
        return '8';
    case 0x9:
        return '9';
    case 0xa:
        return 'A';
    case 0xb:
        return 'B';
    case 0xc:
        return 'C';
    case 0xd:
        return 'D';
    case 0xe:
        return 'E';
    case 0xf:
        return 'F';
    default:
        return ' ';/* input hex error */
    }
}

int igdCmApiChartoMac(const char *mac_char, unsigned char *mac_digital)
{
    unsigned int i;
    int p0,p1;

    if((mac_char == NULL)||(mac_digital == NULL))
        return(2);

    if(strlen(mac_char) != 17)
        return(1);

    for(i=0; i<17; i=i+3)
    {
        if((p0 = igdCmApiChartoHex(mac_char[i])) >= 16)
            return(3);
        if((p1 = igdCmApiChartoHex(mac_char[i+1])) >= 16)
            return(3);
        mac_digital[i/3] = p0*16 + p1;
        if(((i+2)<17) && (mac_char[i+2] != ':' && mac_char[i+2] != '-'))
            return(4);
    }

    return(0);
}

int igdCmApiMactoChar(const unsigned char *mac_digital,char *mac_char)
{
    unsigned int i;

    if((mac_char == NULL)||(mac_digital == NULL))
        return(1);

    for(i=0; i<6; i++)
    {
        mac_char[(i*3)+0] = igdCmApiHextoChar(mac_digital[i]/0x10);
        mac_char[(i*3)+1] = igdCmApiHextoChar(mac_digital[i]%0x10);
        if(((i*3)+2) < 17)
            mac_char[(i*3)+2] = ':';
        else
            mac_char[(i*3)+2] = '\0';
    }

    return(0);
}

unsigned int igdCmApiChartoIp(const char* pszIp)
{
    unsigned int  dwIp = 0;

    if(pszIp == NULL|| strlen(pszIp) == 0)
    {
        return dwIp;
    }

    dwIp = inet_addr(pszIp);

    return (dwIp);
}

int igdCmApiIptoChar(const unsigned char *ip_digital, char *ip_char, unsigned int size)
{
    if((ip_char == NULL) || (ip_digital == NULL))
        return(1);

    HI_OS_SPRINTF_S(ip_char, size, "%d.%d.%d.%d",*ip_digital,*(ip_digital+1),*(ip_digital+2),*(ip_digital+3));
    ip_char[15]='\0';

    return(0);
}
int igdCmApiIpv6toChar(const void *ipv6_digital,char *ipv6_char)
{
    if((ipv6_char == NULL) || (ipv6_digital == NULL))
        return(1);

    inet_ntop(PF_INET6, (struct in6_addr *)ipv6_digital, ipv6_char, 48);

    return(0);
}

int igdCmApiChartoIpv6(const void *ipv6_digital,char *ipv6_char)
{
    if((ipv6_char == NULL) || (ipv6_digital == NULL))
        return(1);

    if (inet_pton(PF_INET6, ipv6_char, (struct in6_addr *)ipv6_digital) != 1) {
        return 1;
    }

    return(0);
}

int igdCmApiIpv4Valid(char *pc_addr)
{
    hi_int32 i;
    hi_char8 ch[4];
    hi_int32 n[4];

    if (7 != HI_OS_SSCANF_S(pc_addr, "%d%c%d%c%d%c%d%c",
            &n[0],&ch[0],sizeof(hi_char8),&n[1],&ch[1],sizeof(hi_char8),
            &n[2],&ch[2],sizeof(hi_char8),&n[3],&ch[3],sizeof(hi_char8))) {
        return 0;
    }

    for (i = 0; i < 3; i++) {
        if (ch[i] != '.') {
            return 0;
        }
    }

    for (i = 0; i < 4; i++) {
        if (n[i] < 0 || n[i] > 255) {
            return 0;
        }
    }

    return 1;
}



int igdCmApiULtoChar(unsigned int num, char *des, int size)
{
    char auc_num[16];
    int i = 0;

    while (num && i < 16) {
        auc_num[i++] = (num % 10) + '0';
        num /= 10;
    }

    if (i >= size) {
        return -1;
    }

    for (; i > 0;) {
        *des++ = auc_num[--i];
    }
    *des = 0;
    return 0;
}

#define IGD_CM_VAL_SEP  ","
#define IGD_CM_VAL_NULL " "
int32_t igd_cm_api_split_string(char *arry1d, uint32_t arry1d_size, char *arry2d, uint32_t rows, uint32_t cols)
{
	if (arry2d == NULL || arry1d == NULL) {
		printf("%s():%d arry1d or arry2d is NULL!\n",
				__FUNCTION__, __LINE__);
		return -1;
	}
	if (strlen(arry1d) >= arry1d_size) {
		printf("%s():%d arry1d string length out of arry1d size(%d)!\n",
				__FUNCTION__, __LINE__, arry1d_size);
		return -1;
	}

	char *token = NULL;
	char *next_token = NULL;
	uint32_t index, rows_num = 0;

	token = strtok_s(arry1d, IGD_CM_VAL_SEP, &next_token);
	while (token != NULL)
	{
		if (strlen(token) >= cols) {
	 		printf("%s():%d %s out of array cols size(%d)!\n",
				__FUNCTION__, __LINE__, token, cols);
			return -1;
		}
		if (rows_num >= rows) {
			printf("%s():%d Out of array rows size(%d)!\n",
				__FUNCTION__, __LINE__, rows);
			return -1;
		}

		index = rows_num * cols;
		if (strcmp(token, IGD_CM_VAL_NULL) != 0) {
			if (strcpy_s(arry2d + index, cols, token) != EOK) {
				printf("%s():%d strcpy_s error!\n", __FUNCTION__, __LINE__);
				return -1;
			}
		}
		token = strtok_s(NULL, IGD_CM_VAL_SEP, &next_token);
		rows_num++;
	}

	return rows_num;
}

int32_t igd_cm_api_splice_string(char *arry2d, uint32_t rows, uint32_t cols, char *arry1d, uint32_t arry1d_size)
{
	if (arry2d == NULL || arry1d == NULL) {
		printf("%s():%d arry1d or arry2d is NULL!\n",
				__FUNCTION__, __LINE__);
		return -1;
	}

	uint32_t i, index, str_len = 0;

	if (memset_s(arry1d, arry1d_size, 0 , arry1d_size) != EOK) {
		printf("%s():%d memset_s error!\n", __FUNCTION__, __LINE__);
		return -1;
	}
	for (i = 0; i < rows; i++) {
		index = i * cols;

		if (strlen(arry2d + index) >= cols) {
			printf("%s():%d %s out of array cols size(%d)!\n",
				__FUNCTION__, __LINE__, arry2d + index, cols);
			return -1;
		}
		if ((str_len + strlen(arry2d + index)) >= arry1d_size) {
			printf("%s():%d %s + %s len will be out of arry1d size(%d)!\n",
				__FUNCTION__, __LINE__, arry1d, arry2d + index, arry1d_size);
			return -1;
		}

		if (strlen(arry2d + index) == 0) {
			if (strcat_s(arry1d, arry1d_size, IGD_CM_VAL_NULL) != EOK) {
				printf("%s():%d strcat_s error!\n", __FUNCTION__, __LINE__);
				return -1;
			}
			str_len += strlen(IGD_CM_VAL_NULL);
		} else {
			if (strcat_s(arry1d, arry1d_size, arry2d + index) != EOK) {
				printf("%s():%d strcat_s error!\n", __FUNCTION__, __LINE__);
				return -1;
			}
			str_len += strlen(arry2d + index);
		}
		if (strcat_s(arry1d, arry1d_size, IGD_CM_VAL_SEP) != EOK) {
			printf("%s():%d strcat_s error!\n", __FUNCTION__, __LINE__);
			return -1;
		}
		str_len += strlen(IGD_CM_VAL_SEP);
	}

	return 0;
}


int igd_cm_write_file(const char *file_name, const void *data, unsigned int count)
{
	int fd = 0;
	int ret = 0;

	/* 写之前清空文件 */
	fd = hi_os_open(file_name, HI_O_CREAT | HI_O_RDWR | HI_O_TRUNC, 0666);
	if (-1 == fd) {
		printf("create file %s err[%d] \n", file_name, fd);
		return HI_RET_FAIL;
	}

	ret = hi_os_write(fd, data, count);
	if (-1 == ret) {
		printf("write file %s err[%d], size %d \n", file_name, fd, count);
		hi_os_close(fd);
		return HI_RET_FAIL;
	}

	ret = hi_os_close(fd);
	if (-1 == ret) {
		printf("close file %s err[%d] \n", file_name, fd);
		return HI_RET_FAIL;
	}
	return HI_RET_SUCC;
}

int igd_cm_read_file(const char *file_name, const void *data, unsigned int count)
{
	int fd = 0;
	int ret = 0;

	fd = hi_os_open(file_name, HI_O_RDONLY, 0666);
	if (-1 == fd) {
		printf("open file %s err[%d] \n", file_name, fd);
		return HI_RET_FAIL;
	}

	ret = hi_os_read(fd, (void *)data, count);
	if (-1 == ret) {
		printf("read file %s err[%d], size %d \n", file_name, fd, count);
		hi_os_close(fd);
		return HI_RET_FAIL;
	}

	ret = hi_os_close(fd);
	if (-1 == ret) {
		printf("close file %s err[%d] \n", file_name, fd);
		return HI_RET_FAIL;
	}

	return HI_RET_SUCC;
}

int igd_cm_del_file(const char *file_name)
{
	int ret = 0;
	if (file_name == NULL)
		return HI_RET_FAIL;
	ret = remove(file_name);
	if (ret != 0) {
		printf("remove file %s fail, ret is [%d] \n", file_name, ret);
		return HI_RET_FAIL;
	}

	return HI_RET_SUCC;
}

long igd_cm_get_file_size(const char *file_name)
{
	FILE* fd;
	long size = 0;
	fd = fopen(file_name, "r");
	if (fd == NULL) {
		printf("open file %s \n", file_name);
		return HI_RET_FAIL;
	}
	fseek(fd, 0, SEEK_END);
	size = ftell(fd);
	fseek(fd, 0, SEEK_SET);
	fclose(fd);
	return size;
}

#endif
#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus
