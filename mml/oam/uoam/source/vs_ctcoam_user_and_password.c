#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "vs_ctcoam_user_and_password.h"


uint32_t vs_ctcoam_get_user_and_password(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{

    hi_uint32 ret = 0;
    vs_ctcoam_user_and_password_s *date_cfg = NULL;
    IgdSysmngAccountAttrConfTab data;

    if (pv_outmsg == NULL)
		return HI_RET_INVALID_PARA;

    date_cfg = (vs_ctcoam_user_and_password_s *)pv_outmsg;

    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));

    data.ulBitmap |= ACCOUNT_ATTR_MASK_ALL;
    //ret = igdCmConfGet(IGD_SYSMNG_ACCOUNT_ATTR_TAB,(unsigned char *)&data,sizeof(data));
    ret = HI_IPC_CALL("hi_sml_login_attr_get", &data);
    if(ret != 0)
    {
        printf("hi_sml_login_attr_get fail, ret : %d \r\n", ret);
        return HI_RET_FAIL;
    }

    date_cfg->admin_change = 0;
    date_cfg->user_change = 0;

    HI_OS_MEMCPY_S(date_cfg->admin_name, 30, data.aucTeleAccountName, 30);
    HI_OS_MEMCPY_S(date_cfg->admin_password, 30, data.aucTeleAccountPassword, 30);
    HI_OS_MEMCPY_S(date_cfg->user_name, 30, data.aucUserAccountName, 30);
    HI_OS_MEMCPY_S(date_cfg->user_password, 30, data.aucUserAccountPassword, 30);

    return HI_RET_SUCC;
}

uint32_t vs_ctcoam_set_user_and_password(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    vs_ctcoam_user_and_password_s *date_cfg = NULL;
    IgdSysmngAccountAttrConfTab data;

    //Added by zfh for change pw
    IgdCmVsCustomConfTab cus_data = { 0 };
    ret = igdCmConfGet(IGD_VS_CUSTOM_ATTR_TAB,(unsigned char *)&cus_data,sizeof(cus_data));
    if(ret != 0)
    {
        return HI_RET_FAIL; 
    }

    if (pv_inmsg == NULL)
        return HI_RET_INVALID_PARA;

    date_cfg = (vs_ctcoam_user_and_password_s *) pv_inmsg;

    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));

    if(date_cfg->admin_change == 1)
    {
        data.ulBitmap |= ACCOUNT_ATTR_MASK_BIT1_TELE_ACCOUNT_NAME|ACCOUNT_ATTR_MASK_BIT2_TELE_ACCOUNT_PASSWORD;
        if (strlen((const char*)date_cfg->admin_password) < 8)
        {
            printf("Admin password length less than 8\n");
            return HI_RET_FAIL;            
        }
        HI_OS_MEMCPY_S(data.aucTeleAccountName, 30, date_cfg->admin_name, 30);
        HI_OS_MEMCPY_S(data.aucTeleAccountPassword, 30, date_cfg->admin_password, 30);
        if(cus_data.uc_cf_user_changepw_admin){
            cus_data.uc_cf_user_changepw_admin = 0;
            cus_data.ulBitmap |= VS_CUSTOM_ATTR_MASK_BIT0_CHANGEPW_ADMIN;
        }
    }
    if(date_cfg->user_change == 1)
    {
        data.ulBitmap |= ACCOUNT_ATTR_MASK_BIT3_USER_ACCOUNT_NAME|ACCOUNT_ATTR_MASK_BIT4_USER_ACCOUNT_PASSWORD;
        if (strlen((const char*)date_cfg->user_password) < 8)
        {
            printf("User password length less than 8\n");
            return HI_RET_FAIL;            
        }
        HI_OS_MEMCPY_S(data.aucUserAccountName, 30, date_cfg->user_name, 30);
        HI_OS_MEMCPY_S(data.aucUserAccountPassword, 30, date_cfg->user_password, 30);
        if(cus_data.uc_cf_user_changepw_user){
            cus_data.uc_cf_user_changepw_user = 0;
            cus_data.ulBitmap |= VS_CUSTOM_ATTR_MASK_BIT1_CHANGEPW_USER;
        }
    }

    //ret = igdCmConfSet(IGD_SYSMNG_ACCOUNT_ATTR_TAB,(unsigned char *)&data,sizeof(data));
    ret = HI_IPC_CALL("hi_sml_login_attr_set", &data);
    if(ret != 0)
    {
        printf("ihi_sml_login_attr_set fail, ret : %d \r\n", ret);
        return HI_RET_FAIL;
    }

    ret = igdCmConfSet(IGD_VS_CUSTOM_ATTR_TAB, (unsigned char *)&cus_data, sizeof(cus_data));
    if(ret != 0)
    {
        return HI_RET_FAIL;
    }

    return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

