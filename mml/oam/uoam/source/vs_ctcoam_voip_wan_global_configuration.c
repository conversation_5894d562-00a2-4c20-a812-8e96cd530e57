#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "vs_ctcoam_voip_wan_global_configuration.h"
#include "vs_ctcoam_wan_configuration.h"

vs_ctcoam_voip_wan_global_configuration_s voip_wan_config;
HI_PTHREAD_T voip_wan_add_thread;
int voip_wan_num = 0;

void printf_voip_wan_cfg(vs_ctcoam_voip_wan_global_configuration_s *cfg)
{
    int i = 0;
    unsigned char printf_enable = 0;
    if(printf_enable == 1)
    {
        printf("voice_ip_mode = %d\n", cfg->voice_ip_mode);
        printf("ip_address = 0x%x,0x%x,0x%x,0x%x\n", cfg->ip_address[0], cfg->ip_address[1], cfg->ip_address[2], cfg->ip_address[3]);
        printf("mask = 0x%x,0x%x,0x%x,0x%x\n", cfg->mask[0], cfg->mask[1], cfg->mask[2], cfg->mask[3]);
        printf("gateway = 0x%x,0x%x,0x%x,0x%x\n", cfg->gateway[0], cfg->gateway[1], cfg->gateway[2], cfg->gateway[3]);
        printf("ppoe_mode = %d\n", cfg->ppoe_mode);
        printf("pppoe_username =");
        for(i = 0; i < 32; i++)
        {
            printf(" 0x%x,", cfg->pppoe_username[i]);
        }printf("\n");
        printf("pppoe_password =");
        for(i = 0; i < 32; i++)
        {
            printf(" 0x%x,", cfg->pppoe_password[i]);
        }printf("\n");
        printf("vlan_mode = %d\n", cfg->vlan_mode);
        printf("cvlan = %d\n", cfg->cvlan);
        printf("svlan = %d\n", cfg->svlan);
        printf("priority = %d\n", cfg->priority);
    }
}

int vs_oam_delete_all_voip_wan()
{
    IgdWanConnectionAttrConfTab data, delete_wan_data[10];
    IgdWanConnectionIndexAttrTab wan_conn_indexobj[IGD_WAN_CONNECTION_RECORD_NUM];
    hi_uint32 listnum = 0, delete_wan_num = 0;
    int ret = 0, i = 0;

    igdCmConfGetEntryNum(IGD_WAN_CONNECTION_ATTR_TAB, &listnum);

    HI_OS_MEMSET_S(wan_conn_indexobj, sizeof(wan_conn_indexobj), 0, sizeof(wan_conn_indexobj));
    ret = igdCmConfGetAllEntry(IGD_WAN_CONNECTION_INDEX_ATTR_TAB, (unsigned char *)wan_conn_indexobj, sizeof(wan_conn_indexobj));
    if(ret != 0)
    {
        printf("igdCmConfGetAllEntry IGD_WAN_CONNECTION_INDEX_ATTR_TAB ret : %d \r\n", ret);
        return HI_RET_FAIL;
    }

    for(i = 0; i < listnum; i++)
    {
        HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));
        data.ulBitmap |= WAN_CONNECTION_ATTR_MASK_ALL;
        data.ulBitmap1 |= WAN_CONNECTION_ATTR_MASK1_ALL;

        data.ucGlobalIndex = wan_conn_indexobj[i].ucGlobalIndex;
        data.ucConDevIndex = wan_conn_indexobj[i].ucConDevIndex;
        data.ucXPConIndex = wan_conn_indexobj[i].ucXPConIndex;
        HI_OS_STRCPY_S((char *)data.aucWanName, sizeof(data.aucWanName), wan_conn_indexobj[i].aucWanName);

        ret = igdCmConfGet(IGD_WAN_CONNECTION_ATTR_TAB, (unsigned char *)&data, sizeof(data));
        if(ret != 0)
        {
            printf("igdCmConfGet IGD_WAN_CONNECTION_ATTR_TAB ret : %d \n", ret);
            continue;
        }

        if(data.ucServiceList & WAN_CONNECTION_INDEX_SERVICE_VOIP)//voip wan
        {
            HI_OS_MEMCPY_S(&delete_wan_data[delete_wan_num], sizeof(IgdWanConnectionAttrConfTab), &data, sizeof(IgdWanConnectionAttrConfTab));
            delete_wan_num++;
        }
    }

    for(i = 0; i < delete_wan_num; i++)
    {
        if(delete_wan_data[i].ucServiceList & WAN_CONNECTION_INDEX_SERVICE_VOIP)//voip wan
        {
            ret = igdCmConfDel(IGD_WAN_CONNECTION_ATTR_TAB, (unsigned char *)&delete_wan_data[i], sizeof(IgdWanConnectionAttrConfTab));
            if(ret != 0)
            {
                printf("igdCmConfDel IGD_WAN_CONNECTION_ATTR_TAB ret : %d\n", ret);
                return HI_RET_FAIL;
            }
            printf("delete voip wan(%s) success\n", delete_wan_data[i].aucWanName);
        }
    }

    return HI_RET_SUCC;
}

int vs_oam_voip_wan_cfg_to_ram(int *p_voip_wan_num, vs_ctcoam_voip_wan_global_configuration_s *cfg)
{
    int ret = 0, i = 0;
    hi_uint32 listnum = 0;
    unsigned char voip_wan_num = 0;
    IgdWanConnectionAttrConfTab data;
    IgdWanConnectionStateInfoTab wanStateData;

    HI_OS_MEMSET_S(cfg, sizeof(vs_ctcoam_voip_wan_global_configuration_s), 0, sizeof(vs_ctcoam_voip_wan_global_configuration_s));

    igdCmConfGetEntryNum(IGD_WAN_CONNECTION_ATTR_TAB, &listnum);

    if((0 < listnum) && (listnum <= 18))
    {
        IgdWanConnectionIndexAttrTab wan_conn_indexobj[IGD_WAN_CONNECTION_RECORD_NUM];
        HI_OS_MEMSET_S(wan_conn_indexobj,sizeof(wan_conn_indexobj),0,sizeof(wan_conn_indexobj));

        ret = igdCmConfGetAllEntry(IGD_WAN_CONNECTION_INDEX_ATTR_TAB,(unsigned char *)wan_conn_indexobj,sizeof(wan_conn_indexobj));
        if(ret != 0)
        {
            printf("igdCmConfGetAllEntry IGD_WAN_CONNECTION_INDEX_ATTR_TAB ret : %d \r\n", ret);
            return HI_RET_FAIL;
        }

        for(i = 0; i < listnum; i++)
        {
            HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));
            data.ulBitmap |= WAN_CONNECTION_ATTR_MASK_ALL;
            data.ulBitmap1 |= WAN_CONNECTION_ATTR_MASK1_ALL;

            data.ucGlobalIndex = wan_conn_indexobj[i].ucGlobalIndex;
            data.ucConDevIndex = wan_conn_indexobj[i].ucConDevIndex;
            data.ucXPConIndex = wan_conn_indexobj[i].ucXPConIndex;
            HI_OS_STRCPY_S((char *)data.aucWanName, sizeof(data.aucWanName),wan_conn_indexobj[i].aucWanName);

            ret = igdCmConfGet(IGD_WAN_CONNECTION_ATTR_TAB, (unsigned char *)&data, sizeof(data));
            if(ret != 0)
            {
                printf("igdCmConfGet IGD_WAN_CONNECTION_ATTR_TAB ret : %d \n", ret);
                continue;
            }

            HI_OS_MEMSET_S(&wanStateData, sizeof(wanStateData), 0, sizeof(wanStateData));
            wanStateData.ucGlobalIndex = wan_conn_indexobj[i].ucGlobalIndex;
            HI_OS_STRCPY_S((char *)wanStateData.aucWanName, sizeof(wanStateData.aucWanName),wan_conn_indexobj[i].aucWanName);
            wanStateData.ulBitmap |= WAN_CONNECTION_STATE_MASK_ALL;
            ret = igdCmConfGet(IGD_WAN_CONNECTION_STATE_TAB,(unsigned char *)&wanStateData,sizeof(wanStateData));
            if(ret != 0)
            {
                printf("igdCmConfGet IGD_WAN_CONNECTION_STATE_TAB ret : %d \n", ret);
                continue;
            }

            if(data.ucServiceList & WAN_CONNECTION_INDEX_SERVICE_VOIP)//only get the first voip wan
            {
                HI_OS_MEMSET_S(cfg, sizeof(vs_ctcoam_voip_wan_global_configuration_s), 0, sizeof(vs_ctcoam_voip_wan_global_configuration_s));
                voip_wan_num++;

                /*vlan info*/
                if (data.ucVlanMode == WAN_CONNECTION_VLAN_OVERRIDE)//tag mode
                {
                    cfg->vlan_mode = 1;
                    cfg->cvlan = data.ulVlanIdMark?data.ulVlanIdMark:0;
                    if(data.uc8021pMarkEnable)
                        cfg->priority = data.uc8021pMark;
                    else
                        cfg->priority = 0;
                } 
                else if (data.ucVlanMode == WAN_CONNECTION_VLAN_DISABLE)//vlan disable
                {
                    cfg->vlan_mode = 0;
                    cfg->cvlan = 0;
                    cfg->priority = 0;
                }

                /*ip info*/
                if (data.ucConnectionType == WAN_CONNECTION_TYPE_IP_ROUTED)//only in ip route mode
                {
                    switch (data.ucAddressingType)
                    {
                        case WAN_CONNECTION_ADDRESSING_TYPE_STATIC: 
                            cfg->voice_ip_mode = 0;
                            if ((data.ucIPMode & WAN_CONNECTION_IP_MODE_V4) == WAN_CONNECTION_IP_MODE_V4)
                            {
                                HI_OS_MEMCPY_S(cfg->ip_address, CM_IP_ADDR_LEN, data.aucExternalIPAddress, CM_IP_ADDR_LEN);
                                HI_OS_MEMCPY_S(cfg->mask, CM_IP_ADDR_LEN, data.aucSubnetMask, CM_IP_ADDR_LEN);
                                HI_OS_MEMCPY_S(cfg->gateway, CM_IP_ADDR_LEN, data.aucDefaultGateway, CM_IP_ADDR_LEN);
                            }
                            break;
                        case WAN_CONNECTION_ADDRESSING_TYPE_DHCP:
                            cfg->voice_ip_mode = 1;
                            break;
                        case WAN_CONNECTION_ADDRESSING_TYPE_PPPOE:
                            cfg->voice_ip_mode = 2;

                            if(data.ucPPPAuthProtocol == WAN_CONNECTION_PPP_AUTH_PROTOCOL_AUTO)
                                cfg->ppoe_mode = 0;
                            else if(data.ucPPPAuthProtocol == WAN_CONNECTION_PPP_AUTH_PROTOCOL_CHAP)
                                cfg->ppoe_mode = 1;
                            else if(data.ucPPPAuthProtocol == WAN_CONNECTION_PPP_AUTH_PROTOCOL_PAP)
                                cfg->ppoe_mode = 2;
                            
                            HI_OS_MEMCPY_S(cfg->pppoe_username, sizeof(cfg->pppoe_username), data.aucUsername, sizeof(data.aucUsername));
                            HI_OS_MEMCPY_S(cfg->pppoe_password, sizeof(cfg->pppoe_password), data.aucPassword, sizeof(data.aucPassword));
                            break;
                        default:    
                            printf("data.ucAddressingType=%d, unknown address type\n", data.ucAddressingType);             
                            break;
                    }
                }
            }
        }
    }
    *p_voip_wan_num = voip_wan_num;
    return HI_RET_SUCC;
}

int vs_oam_voip_wan_add(vs_ctcoam_voip_wan_global_configuration_s *cfg)
{
    int ret = 0;
    IgdWanConnectionAttrConfTab data;

    HI_OS_MEMSET_S(&data,sizeof(data),0,sizeof(data));

    data.ulBitmap |= WAN_CONNECTION_ATTR_MASK_BIT1_VLAN_MODE|WAN_CONNECTION_ATTR_MASK_BIT2_8021P_MARK
                |WAN_CONNECTION_ATTR_MASK_BIT5_VLANID_MARK|WAN_CONNECTION_ATTR_MASK_BIT6_ENABLE
                |WAN_CONNECTION_ATTR_MASK_BIT7_CONNECTION_TYPE|WAN_CONNECTION_ATTR_MASK_BIT8_ADDRESSING_TYPE
                |WAN_CONNECTION_ATTR_MASK_BIT9_NAT_ENABLE|WAN_CONNECTION_ATTR_MASK_BIT10_SERVERLIST
                |WAN_CONNECTION_ATTR_MASK_BIT13_BIND_PORT|WAN_CONNECTION_ATTR_MASK_BIT17_USERNAME
                |WAN_CONNECTION_ATTR_MASK_BIT18_PASSWORD|WAN_CONNECTION_ATTR_MASK_BIT20_PPPOE_SERVICE_NAME
                |WAN_CONNECTION_ATTR_MASK_BIT22_DIAL_DEMAND|WAN_CONNECTION_ATTR_MASK_BIT23_SUBNET_MASK
                |WAN_CONNECTION_ATTR_MASK_BIT24_DEFAULT_GATEWAY|WAN_CONNECTION_ATTR_MASK_BIT25_EXTERNAL_IP_ADDR
                |WAN_CONNECTION_ATTR_MASK_BIT27_DNS_ENABLED|WAN_CONNECTION_ATTR_MASK_BIT22_DIAL_DEMAND
                |WAN_CONNECTION_ATTR_MASK_BIT30_DNS_SERVERS|WAN_CONNECTION_ATTR_MASK_BIT26_MAX_MTU
                |WAN_CONNECTION_ATTR_MASK_BIT21_PPPOE_AUTH_PROTOCOL|WAN_CONNECTION_ATTR_MASK_BIT28_DNS_OVERRIDE_ALLOWED;

    data.ulBitmap1 |= WAN_CONNECTION_ATTR_MASK1_BIT6_IP_MODE|WAN_CONNECTION_ATTR_MASK1_BIT7_PROXY_ENABLE
                |WAN_CONNECTION_ATTR_MASK1_BIT10_IPV6_ENABLE|WAN_CONNECTION_ATTR_MASK1_BIT12_DNS_ACHIEVE
                |WAN_CONNECTION_ATTR_MASK1_BIT13_IPV6_ADDR_ORIGIN|WAN_CONNECTION_ATTR_MASK1_BIT14_IPV6_PREFIX_DELEGATION_ENABLED
                |WAN_CONNECTION_ATTR_MASK1_BIT16_AFTR_MODE|WAN_CONNECTION_ATTR_MASK1_BIT17_IPV6_ADDRESS
                |WAN_CONNECTION_ATTR_MASK1_BIT19_IPV6_DNS_SERVERS|WAN_CONNECTION_ATTR_MASK1_BIT21_IPV6_PREFIX
                |WAN_CONNECTION_ATTR_MASK1_BIT22_DEFAULT_IPV6_GATEWAY|WAN_CONNECTION_ATTR_MASK1_BIT24_AFTR
                |WAN_CONNECTION_ATTR_MASK1_BIT5_LAN_INTERFACE_DHCP_ENABLE|WAN_CONNECTION_ATTR_MASK1_BIT28_MAX_MRU_SIZE
                |WAN_CONNECTION_ATTR_MASK1_BIT0_CONNECTION_TRIGGER|WAN_CONNECTION_ATTR_MASK1_BIT20_IPV6_PREFIX_ORIGIN
                |WAN_CONNECTION_ATTR_MASK1_BIT29_8021P_MARK_ENABLE;

    data.ulBitmap2 |= WAN_CONNECTION_ATTR_MASK2_BIT0_NPTV6_ENABLE|WAN_CONNECTION_ATTR_MASK2_IPV6_NAT_ENABLE;
    
    data.ucEnable = 1;
    data.uc8021pMarkEnable = WAN_8021P_MARK_ENABLE;
    data.ucConnectionType = WAN_CONNECTION_TYPE_IP_ROUTED;
    data.ucServiceList = WAN_CONNECTION_SERVICE_VOIP;
    data.ucIPMode = WAN_CONNECTION_IP_MODE_V4;

    if((cfg->cvlan != 0) && (cfg->vlan_mode == 1))
    {
        data.ucVlanMode = WAN_CONNECTION_VLAN_OVERRIDE;
        data.ulVlanIdMark = cfg->cvlan;
        if (cfg->priority >= 0 && cfg->priority <= 7)
        {
            data.uc8021pMarkEnable = WAN_8021P_MARK_ENABLE;
            data.uc8021pMark = cfg->priority;
        }
    }
    else
    {
        data.ucVlanMode = WAN_CONNECTION_VLAN_DISABLE;
        data.uc8021pMarkEnable = WAN_8021P_MARK_DISABLE;
    }

    switch (cfg->voice_ip_mode)
    {
        case 0://static
            data.ucAddressingType = WAN_CONNECTION_ADDRESSING_TYPE_STATIC;
            data.ulMaxMTUSize = 1500;
            HI_OS_MEMCPY_S(data.aucExternalIPAddress, CM_IP_ADDR_LEN, cfg->ip_address, CM_IP_ADDR_LEN);
            HI_OS_MEMCPY_S(data.aucSubnetMask, CM_IP_ADDR_LEN, cfg->mask, CM_IP_ADDR_LEN);
            HI_OS_MEMCPY_S(data.aucDefaultGateway, CM_IP_ADDR_LEN, cfg->gateway, CM_IP_ADDR_LEN);
            break;
        case 1://dhcp
            data.ucAddressingType = WAN_CONNECTION_ADDRESSING_TYPE_DHCP;
            data.ulMaxMTUSize = 1500;
            break;
        case 2://pppoe
            data.ucAddressingType = WAN_CONNECTION_ADDRESSING_TYPE_PPPOE;
            data.ulMaxMRUSize = 1492;
            data.ucProxyEnable = WAN_CONNECTION_PROXY_ENABLE;
            data.ulDialDemend = 1200;
            data.ucConnectionTrigger = WAN_CONNECTION_TRIGGER_ALWAYS_ON;

            if(cfg->ppoe_mode == 0)
                data.ucPPPAuthProtocol = WAN_CONNECTION_PPP_AUTH_PROTOCOL_AUTO;
            else if(cfg->ppoe_mode == 1)
                data.ucPPPAuthProtocol = WAN_CONNECTION_PPP_AUTH_PROTOCOL_CHAP;
            else if(cfg->ppoe_mode == 2)
                data.ucPPPAuthProtocol = WAN_CONNECTION_PPP_AUTH_PROTOCOL_PAP;

            HI_OS_MEMCPY_S(data.aucUsername, sizeof(data.aucUsername), cfg->pppoe_username, sizeof(cfg->pppoe_username));
            HI_OS_MEMCPY_S(data.aucPassword, sizeof(data.aucPassword), cfg->pppoe_password, sizeof(cfg->pppoe_password));
            data.aucUsername[sizeof(data.aucUsername)-1] = '\0';
            data.aucPassword[sizeof(data.aucPassword)-1] = '\0';
            break;
    }

    ret = igdCmConfAdd(IGD_WAN_CONNECTION_ATTR_TAB,(unsigned char *)&data,sizeof(data));
    if(ret != 0)
    {
        printf("igdCmConfAdd IGD_WAN_CONNECTION_ATTR_TAB ret : %d\n", ret);
        return HI_RET_FAIL;
    }
    return HI_RET_SUCC;
}

hi_void *voip_wan_add_func(hi_void *arg)
{
    vs_ctcoam_voip_wan_global_configuration_s *wan_config = (vs_ctcoam_voip_wan_global_configuration_s *)arg;

    vs_oam_delete_all_voip_wan();
    printf("\nready to add voip wan\n");
    vs_oam_voip_wan_add(wan_config);
    wan_config_running = 0;

    return NULL;
}


uint32_t vs_ctcoam_get_voip_wan_global_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0, i = 0;
    hi_uint32 listnum = 0;
    IgdWanConnectionAttrConfTab data;
    IgdWanConnectionStateInfoTab wanStateData;
    vs_ctcoam_voip_wan_global_configuration_s *cfg = NULL; 

    if (pv_outmsg == NULL)
        return HI_RET_INVALID_PARA;

    /* After OLT set wan cfg , OLT will get wan cfg immediately . 
        In this moment , we can't  refresh WanNumber before ONU set wan cfg into flash*/
    if(wan_config_running)
        return HI_RET_SUCC;

    cfg = (vs_ctcoam_voip_wan_global_configuration_s *)pv_outmsg;
    HI_OS_MEMSET_S(cfg, sizeof(vs_ctcoam_voip_wan_global_configuration_s), 0, sizeof(vs_ctcoam_voip_wan_global_configuration_s));

    igdCmConfGetEntryNum(IGD_WAN_CONNECTION_ATTR_TAB, &listnum);

    if((0 < listnum) && (listnum <= 18))
    {
        IgdWanConnectionIndexAttrTab wan_conn_indexobj[IGD_WAN_CONNECTION_RECORD_NUM];
        HI_OS_MEMSET_S(wan_conn_indexobj,sizeof(wan_conn_indexobj),0,sizeof(wan_conn_indexobj));

        ret = igdCmConfGetAllEntry(IGD_WAN_CONNECTION_INDEX_ATTR_TAB,(unsigned char *)wan_conn_indexobj,sizeof(wan_conn_indexobj));
        if(ret != 0)
        {
            printf("igdCmConfGetAllEntry IGD_WAN_CONNECTION_INDEX_ATTR_TAB ret : %d \r\n", ret);
            return HI_RET_FAIL;
        }
        for(i = 0; i < listnum; i++)
        {
            HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));
            data.ulBitmap |= WAN_CONNECTION_ATTR_MASK_ALL;
            data.ulBitmap1 |= WAN_CONNECTION_ATTR_MASK1_ALL;

            data.ucGlobalIndex = wan_conn_indexobj[i].ucGlobalIndex;
            data.ucConDevIndex = wan_conn_indexobj[i].ucConDevIndex;
            data.ucXPConIndex = wan_conn_indexobj[i].ucXPConIndex;
            HI_OS_STRCPY_S((char *)data.aucWanName, sizeof(data.aucWanName),wan_conn_indexobj[i].aucWanName);

            ret = igdCmConfGet(IGD_WAN_CONNECTION_ATTR_TAB, (unsigned char *)&data, sizeof(data));
            if(ret != 0)
            {
                printf("igdCmConfGet IGD_WAN_CONNECTION_ATTR_TAB ret : %d \n", ret);
                continue;
            }

            HI_OS_MEMSET_S(&wanStateData, sizeof(wanStateData), 0, sizeof(wanStateData));
            wanStateData.ucGlobalIndex = wan_conn_indexobj[i].ucGlobalIndex;
            HI_OS_STRCPY_S((char *)wanStateData.aucWanName, sizeof(wanStateData.aucWanName),wan_conn_indexobj[i].aucWanName);
            wanStateData.ulBitmap |= WAN_CONNECTION_STATE_MASK_ALL;
            ret = igdCmConfGet(IGD_WAN_CONNECTION_STATE_TAB,(unsigned char *)&wanStateData,sizeof(wanStateData));
            if(ret != 0)
            {
                printf("igdCmConfGet IGD_WAN_CONNECTION_STATE_TAB ret : %d \n", ret);
                continue;
            }

            if(data.ucServiceList & WAN_CONNECTION_INDEX_SERVICE_VOIP)//only get the first voip wan
            {
                /*vlan info*/
                if (data.ucVlanMode == WAN_CONNECTION_VLAN_OVERRIDE)//tag mode
                {
                    cfg->vlan_mode = 1;
                    cfg->cvlan = data.ulVlanIdMark?data.ulVlanIdMark:0;
                    if(data.uc8021pMarkEnable)
                        cfg->priority = data.uc8021pMark;
                    else
                        cfg->priority = 0;
                } 
                else if (data.ucVlanMode == WAN_CONNECTION_VLAN_DISABLE)//vlan disable
                {
                    cfg->vlan_mode = 0;
                    cfg->cvlan = 0;
                    cfg->priority = 0;
                }

                /*ip info*/
                if (data.ucConnectionType == WAN_CONNECTION_TYPE_IP_ROUTED)//only in ip route mode
                {
                    switch (data.ucAddressingType)
                    {
                        case WAN_CONNECTION_ADDRESSING_TYPE_STATIC: 
                            cfg->voice_ip_mode = 0;
                            if ((data.ucIPMode & WAN_CONNECTION_IP_MODE_V4) == WAN_CONNECTION_IP_MODE_V4)
                            {
                                HI_OS_MEMCPY_S(cfg->ip_address, CM_IP_ADDR_LEN, data.aucExternalIPAddress, CM_IP_ADDR_LEN);
                                HI_OS_MEMCPY_S(cfg->mask, CM_IP_ADDR_LEN, data.aucSubnetMask, CM_IP_ADDR_LEN);
                                HI_OS_MEMCPY_S(cfg->gateway, CM_IP_ADDR_LEN, data.aucDefaultGateway, CM_IP_ADDR_LEN);
                            }
                            break;
                        case WAN_CONNECTION_ADDRESSING_TYPE_DHCP:
                            cfg->voice_ip_mode = 1;
                            break;
                        case WAN_CONNECTION_ADDRESSING_TYPE_PPPOE:
                            cfg->voice_ip_mode = 2;

                            if(data.ucPPPAuthProtocol == WAN_CONNECTION_PPP_AUTH_PROTOCOL_AUTO)
                                cfg->ppoe_mode = 0;
                            else if(data.ucPPPAuthProtocol == WAN_CONNECTION_PPP_AUTH_PROTOCOL_CHAP)
                                cfg->ppoe_mode = 1;
                            else if(data.ucPPPAuthProtocol == WAN_CONNECTION_PPP_AUTH_PROTOCOL_PAP)
                                cfg->ppoe_mode = 2;
                            
                            HI_OS_MEMCPY_S(cfg->pppoe_username, sizeof(cfg->pppoe_username), data.aucUsername, sizeof(data.aucUsername));
                            HI_OS_MEMCPY_S(cfg->pppoe_password, sizeof(cfg->pppoe_password), data.aucPassword, sizeof(data.aucPassword));
                            break;
                        default:    
                            printf("data.ucAddressingType=%d, unknown address type\n", data.ucAddressingType);             
                            break;
                    }
                }
                printf_voip_wan_cfg(cfg);
                /* net address change */
                cfg->cvlan = (hi_ushort16)htons(cfg->cvlan);
                cfg->svlan = (hi_ushort16)htons(cfg->svlan);
                break;
            }
        }
    }
    return HI_RET_SUCC;
}

uint32_t vs_ctcoam_set_voip_wan_global_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    vs_ctcoam_voip_wan_global_configuration_s *cfg = NULL;
    vs_ctcoam_voip_wan_global_configuration_s ram_cfg;

    if (pv_inmsg == NULL)
        return HI_RET_INVALID_PARA;

    wan_config_running = 1;

    cfg = (vs_ctcoam_voip_wan_global_configuration_s *)pv_inmsg;

    /* net address change */
    cfg->cvlan = (hi_ushort16)ntohs(cfg->cvlan);
    cfg->svlan = (hi_ushort16)ntohs(cfg->svlan);

    vs_oam_voip_wan_cfg_to_ram(&voip_wan_num, &ram_cfg);

    if(voip_wan_num == 0)
    {
        goto VS_OAM_VOIP_WAN_ADD;
    }
    else if(voip_wan_num == 1)
    {
        if(memcmp(&ram_cfg, cfg, sizeof(vs_ctcoam_voip_wan_global_configuration_s)) != 0)
        {
            printf_voip_wan_cfg(&ram_cfg);
            printf_voip_wan_cfg(cfg);
            goto VS_OAM_VOIP_WAN_ADD;
        }
        else
        {
            printf("\nSame voip wan config, not apply\n");
            wan_config_running = 0;
            return HI_RET_SUCC;
        }
    }
    else if(voip_wan_num > 1)
    {
        goto VS_OAM_VOIP_WAN_ADD;
    }

    return HI_RET_SUCC;

VS_OAM_VOIP_WAN_ADD:
    HI_OS_MEMCPY_S(&voip_wan_config, sizeof(vs_ctcoam_voip_wan_global_configuration_s), cfg, sizeof(vs_ctcoam_voip_wan_global_configuration_s));
    ret = hi_os_pthread_create(&voip_wan_add_thread, NULL, voip_wan_add_func, (hi_void *)&voip_wan_config);
    if(ret != 0)
    {
        printf("hi_os_pthread_create fail..\n");
        wan_config_running = 0;
        return HI_RET_FAIL;
    }
    else
    {
        return HI_RET_SUCC;
    }
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

