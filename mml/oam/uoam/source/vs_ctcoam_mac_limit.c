#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "vs_ctcoam_mac_limit.h"
#include "hi_sml_br.h"
#include "hi_sml_common.h"

uint32_t vs_ctcoam_get_mac_limit(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    hi_uint32 ui_port = pst_instance->un_value.ui_value & 0xff;
    vs_ctcoam_mac_limit_s *cfg = NULL;
    hi_sml_br_learn_s st_lrn = {0};

    if (pv_outmsg == NULL)
        return HI_RET_INVALID_PARA;

    cfg = (vs_ctcoam_mac_limit_s *)pv_outmsg;

    switch(ui_port)
    {
        case 1:
            st_lrn.em_port = HI_SML_ETH0_E;
            break;
        case 2:
            st_lrn.em_port = HI_SML_ETH1_E;
            break;
        case 3:
            st_lrn.em_port = HI_SML_ETH2_E;
            break;
        case 4:
            st_lrn.em_port = HI_SML_ETH3_E;
            break;    
        default:
            st_lrn.em_port = HI_SML_MAX_E;
            break;    
    }

    ret = HI_IPC_CALL("hi_sml_br_learn_get", &st_lrn);
    if(ret != HI_RET_SUCC)
    {
        printf("hi_sml_br_learn_get error\n");
        return HI_RET_FAIL;
    }

    cfg->enable = st_lrn.ui_enable;
    cfg->number = st_lrn.ui_num;
    cfg->number = (hi_ushort16)htons(cfg->number);

    return HI_RET_SUCC;
}

uint32_t vs_ctcoam_set_mac_limit(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    hi_uint32 ui_port = pst_instance->un_value.ui_value & 0xff;
    vs_ctcoam_mac_limit_s *cfg = NULL;
    hi_sml_br_learn_s st_lrn = {0};

    if (pv_inmsg == NULL)
        return HI_RET_INVALID_PARA;

    cfg = (vs_ctcoam_mac_limit_s *)pv_inmsg;

    switch(ui_port)
    {
        case 1:
            st_lrn.em_port = HI_SML_ETH0_E;
            break;
        case 2:
            st_lrn.em_port = HI_SML_ETH1_E;
            break;
        case 3:
            st_lrn.em_port = HI_SML_ETH2_E;
            break;
        case 4:
            st_lrn.em_port = HI_SML_ETH3_E;
            break;    
        default:
            st_lrn.em_port = HI_SML_MAX_E;
            break;    
    }

    cfg->number = (hi_ushort16)ntohs(cfg->number);
    if(cfg->enable == 1 && cfg->number > 0)
    {
        st_lrn.ui_enable = cfg->enable;
        st_lrn.ui_num = cfg->number;
    }
    else
    {
        st_lrn.ui_enable = 0;
        st_lrn.ui_num = 0;
    }

    ret = HI_IPC_CALL("hi_sml_br_learn_set", &st_lrn);
    if(ret != HI_RET_SUCC)
        printf("hi_sml_br_learn_set error\n");

    return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

