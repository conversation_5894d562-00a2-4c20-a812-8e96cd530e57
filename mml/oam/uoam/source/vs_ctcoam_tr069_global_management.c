#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "vs_ctcoam_tr069_global_management.h"
// #include "vs_ctcoam_common.h"
#include "vs_ctcoam_wan_configuration.h"
#include "igdCmModulePub.h"
#include "hi_ipc.h"
#include "hi_odl_tab_all_structs.h"


uint32_t vs_ctcoam_get_tr069_global_management(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{

    printf("\r\n wwzx vs_ctcoam_get_tr069_global_management \n");
    int ret = 0;
    unsigned int struct_size = 0;
    unsigned char add_len = 0;
    hi_ushort16 *pus_msglen = NULL;
    hi_ushort16 tlv_leaf = 0;
    hi_ctcoam_tlv_s *tlv_head = (hi_ctcoam_tlv_s *)(pv_inmsg - 4);
    IgdRmtMgtTr069AttrConfTab data;
    vs_ctcoam_tr069_global_management_s *tr069_global_cfg = NULL;

    if (pv_outmsg == NULL)
		return HI_RET_INVALID_PARA;

    tr069_global_cfg = (vs_ctcoam_tr069_global_management_s *)pv_outmsg;

    memset_s(&data, sizeof(IgdRmtMgtTr069AttrConfTab), 0, sizeof(IgdRmtMgtTr069AttrConfTab));
    data.ulBitmap |= TR069_ATTR_MASK_ALL;

#if defined(CONFIG_PLATFORM_OPENWRT)//Add by wzx for bug#18674
    // OpenWRT平台：使用IPC调用获取TR069配置
    ret = HI_IPC_CALL("hi_sml_tr069_global_management_get", &data);
    if (ret != HI_RET_SUCC) {
        printf("HI_IPC_CALL hi_sml_tr069_global_management_get ret : %d \r\n", ret);
        return ret;
    }
#else
    IgdCmVsCustomConfTab customValue = {0};
    ret = igdCmConfGet(IGD_REMOTEMGT_TR069_ATTR_TAB,(unsigned char *)&data,sizeof(data));
    if(ret != 0)
    {
        printf("igdCmConfGet IGD_REMOTEMGT_TR069_ATTR_TAB ret : %d \r\n", ret);
        return HI_RET_FAIL;
    }

    ret = igdCmConfGet(IGD_VS_CUSTOM_ATTR_TAB, (unsigned char *)&customValue, sizeof(customValue));
    if (ret) {
        printf("ret igdCmConfGet IGD_VS_CUSTOM_ATTR_TAB %d \r\n", ret);
        return HI_RET_FAIL;
    }
#endif//End of Add by wzx for bug#18674
    tr069_global_cfg->tr069_enable = data.cwmp_enable;
    tr069_global_cfg->inform_enable = data.ucPeriodicInformEnable;
    tr069_global_cfg->inform_interval = data.ulPeriodicInformInterval;
    tr069_global_cfg->inform_interval = htonl(tr069_global_cfg->inform_interval);
    tr069_global_cfg->certificate_enable = data.aucCertEnable;
    HI_OS_MEMCPY_S(tr069_global_cfg->project_id, sizeof(data.aucProjectId), data.aucProjectId, sizeof(data.aucProjectId));

    HI_OS_MEMCPY_S(tr069_global_cfg->acs_server_url, 128, data.aucAcsUrl, 128);
    HI_OS_MEMCPY_S(tr069_global_cfg->acs_server_username, 64, data.aucUsername, 64);
    HI_OS_MEMCPY_S(tr069_global_cfg->acs_server_password, 64, data.aucPassword, 64);

    HI_OS_MEMCPY_S(tr069_global_cfg->connect_request_username, 64, data.aucConnReqUsername, 64);
    HI_OS_MEMCPY_S(tr069_global_cfg->connect_request_password, 64, data.aucConnReqPassword, 64);

    /* slicing the packets and add tlv header to the data */
    tlv_leaf = tlv_head->us_leaf;
    tlv_leaf = (hi_ushort16)htons(tlv_leaf);
	struct_size = sizeof(vs_ctcoam_tr069_global_management_s);
    if(struct_size > 0 && struct_size <= 128)
    {
        *puc_changingmsglen = struct_size;
        HI_OS_MEMCPY_S(pv_outmsg, sizeof(tr069_global_cfg), tr069_global_cfg, *puc_changingmsglen);
    }
	else if(struct_size > 128 && struct_size <= (255 - 4))//langer than 128, need to slicing the packets
	{
        add_len = packet_slicing_for_send(tr069_global_cfg, sizeof(vs_ctcoam_tr069_global_management_s), tlv_head->uc_branch, tlv_leaf);
        add_len = (add_len - 1) * 4;
		pus_msglen = (hi_ushort16 *)(puc_changingmsglen + 1);
		*pus_msglen = 0;
		*puc_changingmsglen = struct_size + add_len;//send msg will use this len value
	}
	else if(struct_size > (255 - 4) && struct_size <= 1496)
	{
        add_len = packet_slicing_for_send(tr069_global_cfg, sizeof(vs_ctcoam_tr069_global_management_s), tlv_head->uc_branch, tlv_leaf);
        add_len = (add_len - 1) * 4;
		pus_msglen = (hi_ushort16 *)(puc_changingmsglen + 1);
		*pus_msglen = struct_size + add_len;//send msg will use this len value
		*puc_changingmsglen = 0;
	}
	else
	{
		return HI_RET_FAIL;
	}

    return HI_RET_SUCC;
}

uint32_t vs_ctcoam_set_tr069_global_management(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    vs_ctcoam_tr069_global_management_s tr069_global_cfg_date;
    vs_ctcoam_tr069_global_management_s *tr069_global_cfg = &tr069_global_cfg_date;
    IgdRmtMgtTr069AttrConfTab data;

    if (pv_inmsg == NULL)
        return HI_RET_INVALID_PARA;
    
    packet_slicing_for_receive(&tr069_global_cfg_date, sizeof(vs_ctcoam_tr069_global_management_s), pv_inmsg, 1496, 0xc7, 0x2029);

    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));

#if defined(CONFIG_PLATFORM_OPENWRT)//Add by wzx for bug#18674
    data.cwmp_enable = tr069_global_cfg->tr069_enable;
    if (tr069_global_cfg->tr069_enable == 1)
    {
        HI_OS_MEMCPY_S(data.aucProjectId, 20, tr069_global_cfg->project_id, 20);
        HI_OS_MEMCPY_S(data.aucAcsUrl, 128, tr069_global_cfg->acs_server_url, 128);
        HI_OS_MEMCPY_S(data.aucUsername, 64, tr069_global_cfg->acs_server_username, 64);
        HI_OS_MEMCPY_S(data.aucPassword, 64, tr069_global_cfg->acs_server_password, 64);
        HI_OS_MEMCPY_S(data.aucConnReqUsername, 64, tr069_global_cfg->connect_request_username, 64);
        HI_OS_MEMCPY_S(data.aucConnReqPassword, 64, tr069_global_cfg->connect_request_password, 64);
        data.ucPeriodicInformEnable = tr069_global_cfg->inform_enable;
        tr069_global_cfg->inform_interval = ntohl(tr069_global_cfg->inform_interval);
        data.ulPeriodicInformInterval = tr069_global_cfg->inform_interval;
        printf("wwzx Set Periodic Interval = %d\n", data.ulPeriodicInformInterval);
        data.aucCertEnable = tr069_global_cfg->certificate_enable;
        
        data.aucAcsUrl[sizeof(data.aucAcsUrl)-1] = '\0';
        data.aucUsername[sizeof(data.aucUsername)-1] = '\0';
        data.aucPassword[sizeof(data.aucPassword)-1] = '\0';
        data.aucProjectId[sizeof(data.aucProjectId)-1] = '\0';
        data.aucConnReqUsername[sizeof(data.aucConnReqUsername)-1] = '\0';
        data.aucConnReqPassword[sizeof(data.aucConnReqPassword)-1] = '\0';  

    }   
    ret = HI_IPC_CALL("hi_sml_tr069_global_management_set", &data);
    if (ret != HI_RET_SUCC) {
        printf("HI_IPC_CALL hi_sml_tr069_global_management_set ret : %d \r\n", ret);
        return ret;
    }
#else
    IgdCmVsCustomConfTab customValue = {0};

    ret = igdCmConfGet(IGD_VS_CUSTOM_ATTR_TAB, (unsigned char *)&customValue, sizeof(customValue));
    if (ret) {
        printf("igdCmConfGet IGD_VS_CUSTOM_ATTR_TAB %d \r\n", ret);
        return HI_RET_FAIL;
    }

    customValue.uc_cf_tr069_wan_en = tr069_global_cfg->tr069_enable;
    HI_OS_MEMCPY_S(customValue.auc_vince_project_id, sizeof(customValue.auc_vince_project_id), tr069_global_cfg->project_id, sizeof(customValue.auc_vince_project_id));
    customValue.auc_vince_project_id[sizeof(customValue.auc_vince_project_id)-1] = '\0';
    ret = igdCmConfSet(IGD_VS_CUSTOM_ATTR_TAB, (unsigned char *)&customValue, sizeof(customValue));
    if (ret) {
        printf("igdCmConfGet IGD_VS_CUSTOM_ATTR_TAB %d \r\n", ret);
        return HI_RET_FAIL;
    }

    if (tr069_global_cfg->tr069_enable == 1)
    {
        data.ulBitmap |= TR069_ATTR_MASK_BIT2_PEROID_INFORM_ENABLE|TR069_ATTR_MASK_BIT6_ACS_URL\
                        |TR069_ATTR_MASK_BIT8_ACS_USERNAME|TR069_ATTR_MASK_BIT9_ACS_PASSWORD\
                        |TR069_ATTR_MASK_BIT10_CONNREQ_USERNAME|TR069_ATTR_MASK_BIT11_CONNREQ_PASSWORD;
        data.ulBitmap1 |= TR069_ATTR_MASK1_BIT12_CERT_ENABLE;

        data.aucCertEnable = tr069_global_cfg->certificate_enable;
        data.ucPeriodicInformEnable = tr069_global_cfg->inform_enable;
        if (tr069_global_cfg->inform_enable)
        {
            data.ulBitmap |= TR069_ATTR_MASK_BIT12_INFORM_INTERNAL;
            tr069_global_cfg->inform_interval = ntohl(tr069_global_cfg->inform_interval);
            if ((tr069_global_cfg->inform_interval < TR069_ATTR_PERIOD_INFORM_INTERVAL_MIN) || \
                (tr069_global_cfg->inform_interval > TR069_ATTR_PERIOD_INFORM_INTERVAL_MAX))
            {
                printf("[Error] Tr069 inform interval time: [%d, %d]\n", TR069_ATTR_PERIOD_INFORM_INTERVAL_MIN, TR069_ATTR_PERIOD_INFORM_INTERVAL_MAX);
                return HI_RET_FAIL;
            }
            data.ulPeriodicInformInterval = tr069_global_cfg->inform_interval;
        }
        HI_OS_MEMCPY_S(data.aucAcsUrl, 128, tr069_global_cfg->acs_server_url, 128);
        HI_OS_MEMCPY_S(data.aucUsername, 64, tr069_global_cfg->acs_server_username, 64);
        HI_OS_MEMCPY_S(data.aucPassword, 64, tr069_global_cfg->acs_server_password, 64);
        HI_OS_MEMCPY_S(data.aucConnReqUsername, 64, tr069_global_cfg->connect_request_username, 64);
        HI_OS_MEMCPY_S(data.aucConnReqPassword, 64, tr069_global_cfg->connect_request_password, 64);
        data.aucAcsUrl[sizeof(data.aucAcsUrl)-1] = '\0';
        data.aucUsername[sizeof(data.aucUsername)-1] = '\0';
        data.aucPassword[sizeof(data.aucPassword)-1] = '\0';
        data.aucConnReqUsername[sizeof(data.aucConnReqUsername)-1] = '\0';
        data.aucConnReqPassword[sizeof(data.aucConnReqPassword)-1] = '\0';
    }

    ret = igdCmConfSet(IGD_REMOTEMGT_TR069_ATTR_TAB,(unsigned char *)&data,sizeof(data));
    if(ret != 0)
    {
        printf("igdCmConfSet IGD_REMOTEMGT_TR069_ATTR_TAB ret : %d \r\n", ret);
        return HI_RET_FAIL;
    }
#endif
    return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

