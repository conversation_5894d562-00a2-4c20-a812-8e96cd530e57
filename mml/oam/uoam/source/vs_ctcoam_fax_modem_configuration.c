#ifdef __cplusplus
#if __cplusplus
extern C {
#endif
#endif //__cplusplus


#include "vs_ctcoam_fax_modem_configuration.h"

int vs_oam_fax_modem_cfg_to_ram(vs_ctcoam_fax_modem_configuration_s *cfg)
{
    int ret = 0;
    IgdVoiceFaxAttrConfTab data;

    if (cfg == NULL)
        return HI_RET_INVALID_PARA;

    HI_OS_MEMSET_S(cfg, sizeof(vs_ctcoam_fax_modem_configuration_s), 0, sizeof(vs_ctcoam_fax_modem_configuration_s));
    HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));

    data.ulBitmap |= VOICE_FAX_ATTR_MASK_BIT0_FAX_MODE | VOICE_FAX_ATTR_MASK_BIT2_FAX_CONTROL_TYPE;

    ret = igdCmConfGet(IGD_VOICE_FAX_ATTR_TAB,(unsigned char *)&data,sizeof(data));
    if(ret != 0)
    {
        printf("igdCmConfGet IGD_VOICE_FAX_ATTR_TAB ret : %d \r\n", ret);
    }

    cfg->voice_T38_enable = data.ucFaxMode;
    cfg->fax_control_type = data.ucFaxControlType;

    return HI_RET_SUCC;
}

uint32_t vs_ctcoam_get_fax_modem_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    IgdVoiceFaxAttrConfTab data;
    vs_ctcoam_fax_modem_configuration_s *cfg = NULL; 

    if (pv_outmsg == NULL)
        return HI_RET_INVALID_PARA;

    cfg = (vs_ctcoam_fax_modem_configuration_s *)pv_outmsg;
    
    HI_OS_MEMSET_S(&data,sizeof(data),0,sizeof(data));

    data.ulBitmap |= VOICE_FAX_ATTR_MASK_BIT0_FAX_MODE | VOICE_FAX_ATTR_MASK_BIT2_FAX_CONTROL_TYPE;

    ret = igdCmConfGet(IGD_VOICE_FAX_ATTR_TAB,(unsigned char *)&data,sizeof(data));
    if(ret != 0)
    {
        printf("igdCmConfGet IGD_VOICE_FAX_ATTR_TAB ret : %d \r\n", ret);
    }

    cfg->voice_T38_enable = data.ucFaxMode;
    cfg->fax_control_type = data.ucFaxControlType;

    return HI_RET_SUCC;
}

uint32_t vs_ctcoam_set_fax_modem_configuration(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance,
        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)
{
    int ret = 0;
    unsigned char cfg_change = 0;
    IgdVoiceFaxAttrConfTab data;
    vs_ctcoam_fax_modem_configuration_s ram_cfg;
    vs_ctcoam_fax_modem_configuration_s *cfg = NULL; 

    if (pv_inmsg == NULL)
        return HI_RET_INVALID_PARA;

    cfg = (vs_ctcoam_fax_modem_configuration_s *)pv_inmsg;
    
    HI_OS_MEMSET_S(&data,sizeof(data),0,sizeof(data));

    /* compare cfg */
    vs_oam_fax_modem_cfg_to_ram(&ram_cfg);
    if(memcmp(&ram_cfg, cfg, sizeof(vs_ctcoam_fax_modem_configuration_s)) != 0)
        cfg_change = 1;

    /* if cfg change, set to mib */
    if(cfg_change == 1)
    {
        data.ulBitmap |= VOICE_FAX_ATTR_MASK_BIT0_FAX_MODE | VOICE_FAX_ATTR_MASK_BIT2_FAX_CONTROL_TYPE;

        if(cfg->voice_T38_enable == VOICE_FAX_MODE_T30)
            data.ucFaxMode = VOICE_FAX_MODE_T30;
        else if(cfg->voice_T38_enable == VOICE_FAX_MODE_T38)
            data.ucFaxMode = VOICE_FAX_MODE_T38;
        else
            return HI_RET_INVALID_PARA;
        
        if(cfg->fax_control_type == 0)
            data.ucFaxControlType = VOICE_FAX_T30_CONTROL_TYPE_OTHER;
        else if(cfg->fax_control_type == 1)
            data.ucFaxControlType = VOICE_FAX_T30_CONTROL_TYPE_ALL;
        else
            return HI_RET_INVALID_PARA;

        ret = igdCmConfSet(IGD_VOICE_FAX_ATTR_TAB,(unsigned char *)&data,sizeof(data));
        if(ret != 0)
        {
            printf("igdCmConfSet IGD_VOICE_FAX_ATTR_TAB ret : %d \r\n", ret);
        }
    }

    return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif // __cplusplus

