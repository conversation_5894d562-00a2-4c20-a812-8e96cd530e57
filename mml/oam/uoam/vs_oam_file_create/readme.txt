一、脚本说明
1、简介：运行脚本auto_oam_file.sh自动添加私有oam的c文件和h文件。chmod +x auto_oam_file.sh;./auto_oam_file.sh
2、脚本首先读取config文件的第1行，用于生成文件名和函数名。
3、脚本读取config文件的第2行，根据GET和SET来生成私有oam的get和set函数。
4、脚本读取config文件的第3行，用于在h文件生成define宏定义（表示私有的leaf），为了不破坏原有代码，这个宏定义需要手动复制到hi_ctcoam_tree.h对应位置。
5、运行脚本后，最终效果如下：
1）gateway/mml/oam/uoam/include目录创建了一个h文件。
2）gateway/mml/oam/uoam/source目录创建了一个c文件。
3）gateway/mml/oam/uoam/include/vs_ctcoam_common_inc.h文件添加了头文件。

二、添加私有oam说明
1、运行脚本生成文件。
2、在gateway/mml/oam/uoam/include/hi_ctcoam_tree.h添加私有leaf的宏定义
3、在gateway/mml/oam/uoam/source/hi_ctcoam_tree.c添加私有oam的回调函数。
4、对于收发包不定长或者包的长度大于255字节的的私有，需要在gateway/mml/oam/uoam/source/hi_ctcoam_proc.c添加case分支，并回调函数中修改包长度指针的值。

