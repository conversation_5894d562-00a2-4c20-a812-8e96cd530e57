#!/bin/bash

# 读取配置文件
config_file="config"

# 检查配置文件是否包含至少三行
if [ $(wc -l < $config_file) -lt 3 ]; then
    echo "配置文件config至少需要包含三行字符(最后一行需要回车)。"
    exit 1
fi

# 读取第一行文件名
file_name=$(head -n 1 $config_file)

echo $file_name

# 检查第一行字符是否由数字、英文或空格组成
if ! [[ $file_name =~ ^[0-9a-zA-Z[:space:]]*$ ]]; then
    echo "配置文件config的第一行字符必须由数字、英文或空格组成。"
    exit 1
fi

# 转换大写文件名并保存
file_name_upper=$(echo "${file_name// /_}" | tr '[:lower:]' '[:upper:]')

# 读取第二行GET|SET标志
flags=$(sed -n '2p' $config_file)

# 检查第二行字符是否包含GET或者SET
if [[ $flags != *"GET"* && $flags != *"SET"* ]]; then
    echo "配置文件config的第二行字符必须包含GET或者SET。"
    exit 1
fi

# 读取第三行十六进制字符
hex_value=$(sed -n '3p' $config_file)

# 检查第三行字符是否由数字组成
if ! [[ $hex_value =~ ^[0-9a-fA-F]+$ ]]; then
    echo "配置文件config的第三行字符必须由数字组成。"
    exit 1
fi

# 根据文件名生成C文件和H文件的名称
source_dir="../source"
include_dir="../include"
vs_common_inc_file="${include_dir}/vs_ctcoam_common_inc.h"
#oam_tree_inc_file="${include_dir}/hi_ctcoam_tree.h"
if [ ! -d "$source_dir" ] || [ ! -d "$include_dir" ]; then
    echo "上一级目录中的source或include文件夹不存在，请先创建这些文件夹。"
    exit 1
fi

if [ ! -f "$vs_common_inc_file" ]; then
    echo "在include文件夹中找不到vs_ctcoam_common_inc.h文件，请先创建这个文件。"
    exit 1
fi

#if [ ! -f "$oam_tree_inc_file" ]; then
#    echo "在include文件夹中找不到hi_ctcoam_tree.h文件，请先创建这个文件。"
#    exit 1
#fi

c_file="${source_dir}/vs_ctcoam_${file_name// /_}.c"
h_file="${include_dir}/vs_ctcoam_${file_name// /_}.h"
#c_file_name="vs_ctcoam_${file_name// /_}.c"
h_file_name="vs_ctcoam_${file_name// /_}.h"

# 检查是否已经存在要生成的C文件或H文件
if [ -f "$c_file" ] || [ -f "$h_file" ]; then
    echo "要生成的C文件或H文件已经存在，请先删除这些文件后再运行该脚本。"
    exit 1
fi

# 在vs_ctcoam_common_inc.h中添加#include "$h_file_name"
sed -i "/\/\/automatic tool identification marks, do not delete this line/i #include \"$h_file_name\"" $vs_common_inc_file

# 在hi_ctcoam_tree.h中添加宏定义
#sed -i "/\/\/automatic tool identification marks, do not delete this line/i #define VS_CTCOAM_LEAF_${file_name_upper}  0x$hex_value" $oam_tree_inc_file


# 创建C文件并写入内容
echo "#ifdef __cplusplus" > $c_file
echo "#if __cplusplus" >> $c_file
echo "extern "C" {" >> $c_file
echo "#endif" >> $c_file
echo "#endif //__cplusplus" >> $c_file
echo "" >> $c_file
echo "" >> $c_file
echo "#include \"$h_file_name\"" >> $c_file
echo "" >> $c_file
echo "" >> $c_file


if [[ $flags == *GET* ]]; then
    echo "uint32_t vs_ctcoam_get_${file_name// /_}(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance," >> $c_file
    echo "        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)" >> $c_file
    echo "{" >> $c_file
    echo "" >> $c_file
    echo "    return HI_RET_SUCC;" >> $c_file
    echo "}" >> $c_file
fi

echo "" >> $c_file

if [[ $flags == *SET* ]]; then
    echo "uint32_t vs_ctcoam_set_${file_name// /_}(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance," >> $c_file
    echo "        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg)" >> $c_file
    echo "{" >> $c_file
    echo "" >> $c_file
    echo "    return HI_RET_SUCC;" >> $c_file
    echo "}" >> $c_file
fi
echo "" >> $c_file
echo "" >> $c_file
echo "#ifdef __cplusplus" >> $c_file
echo "#if __cplusplus" >> $c_file
echo "}" >> $c_file
echo "#endif" >> $c_file
echo "#endif // __cplusplus" >> $c_file
echo "" >> $c_file




# 创建H文件并写入内容
echo "#ifndef __VS_CTCOAM_${file_name_upper}_H__" > $h_file
echo "#define __VS_CTCOAM_${file_name_upper}_H__" >> $h_file
echo "" >> $h_file
echo "" >> $h_file
echo "#ifdef __cplusplus" >> $h_file
echo "#if __cplusplus" >> $h_file
echo "extern "C" {" >> $h_file
echo "#endif" >> $h_file
echo "#endif //__cplusplus" >> $h_file
echo "" >> $h_file
echo "" >> $h_file
echo "#include \"hi_uspace.h\"" >> $h_file
echo "#include \"hi_timer.h\"" >> $h_file
echo "#include \"hi_sysinfo.h\"" >> $h_file
echo "#include \"hi_oam_common.h\"" >> $h_file
echo "#include \"hi_oam_api.h\"" >> $h_file
echo "#include \"hi_oam_adapter.h\"" >> $h_file
echo "#include \"hi_ctcoam_proc.h\"" >> $h_file
echo "#include \"hi_ctcoam_tree.h\"" >> $h_file
echo "#include \"hi_ctcoam_manage.h\"" >> $h_file
echo "#include \"hi_ctcoam_performance.h\"" >> $h_file
echo "#include \"hi_ctcoam_alarm.h\"" >> $h_file
echo "#include \"hi_ipc.h\"" >> $h_file
echo "#include \"hi_oam_logger.h\"" >> $h_file
echo "#include \"igdCmModulePub.h\"" >> $h_file
echo "#include \"vs_ctcoam_common_inc.h\"" >> $h_file
echo "#include <math.h>" >> $h_file
echo "" >> $h_file
echo "" >> $h_file

echo "// Copy the following macro definition to the appropriate location for hi_ctcoam_tree.h" >> $h_file
echo "// #define VS_CTCOAM_LEAF_${file_name_upper}  0x$hex_value" >> $h_file
echo "" >> $h_file
echo "" >> $h_file

echo "typedef struct {" >> $h_file
echo "    unsigned char example;" >> $h_file
echo "} __attribute__((__packed__)) vs_ctcoam_${file_name// /_}_s;" >> $h_file
echo "" >> $h_file
echo "" >> $h_file

if [[ $flags == *GET* ]]; then
    echo "uint32_t vs_ctcoam_get_${file_name// /_}(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance," >> $h_file
    echo "        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg);" >> $h_file
fi
if [[ $flags == *SET* ]]; then
    echo "uint32_t vs_ctcoam_set_${file_name// /_}(uint32_t ui_llidindex, hi_ctcoam_instance_s *pst_instance," >> $h_file
    echo "        const void *pv_inmsg, uint8_t *puc_changingmsglen, void *pv_outmsg);" >> $h_file
fi

echo "" >> $h_file
echo "" >> $h_file
echo "#ifdef __cplusplus" >> $h_file
echo "#if __cplusplus" >> $h_file
echo "}" >> $h_file
echo "#endif" >> $h_file
echo "#endif // __cplusplus" >> $h_file
echo "" >> $h_file

echo "#endif //__VS_CTCOAM_${file_name_upper}_H__" >> $h_file

# 输出结果
echo "生成文件：$c_file 和 $h_file"


