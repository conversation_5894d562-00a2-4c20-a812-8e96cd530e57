include(${CONFIG_CMAKE_DIR}/toolchains/${CONFIG_TOOLCHAIN_LINUX_APP}.cmake)

# 组件名称
set(USERAPP_NAME hi_oam)

# 组件类型
set(USERAPP_TARGET_TYPE so)

# 依赖的源文件
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/source OAM_SOURCE_DIR)
set(USERAPP_PRIVATE_SRC
    ${OAM_SOURCE_DIR}
)
# 依赖的头文件
set(USERAPP_PRIVATE_INC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${HGW_BASIC_DIR}/include
    ${HGW_FWK_DIR}/include
    ${HGW_FWK_DIR}/util/include
    ${HGW_FWK_DIR}/timer/include
    ${HGW_FWK_DIR}/ipc/include
    ${HGW_FWK_DIR}/notifier/include
    ${HGW_FWK_DIR}/ioreactor/include
    ${HGW_MML_DIR}/oam/oam_service/include
    ${HGW_SERVICE_DIR}/dms/upgrade/include
    ${HGW_SERVICE_DIR}/dms/sysinfo/include
    ${HGW_SERVICE_DIR}/pon/sml/include
    ${HGW_SERVICE_DIR}/network/common/include
    ${HGW_SERVICE_DIR}/pon/api/include
    ${CONFIG_LINUX_DRV_DIR}/net/pon/include
    ${HGW_CML_DIR}/odlapi/include
    ${HGW_CML_DIR}/odl/include
    ${HGW_SERVICE_DIR}/network/easymesh/include
    ${HGW_SERVICE_DIR}/network/emu/u_space/include
    ${HGW_SERVICE_DIR}/network/easymesh/controller_api
    ${HGW_SERVICE_DIR}/network/easymesh/libs/include
    ${HGW_SERVICE_DIR}/network/easymesh/agent_api
    ${CONFIG_BUILD_DIR}/open_source/local/zlib
    ${CONFIG_BUILD_DIR}/open_source/local/zlib/zlib-1.2.11
    ${HGW_VSOL_LIB_DIR}/libvsbcrypt/include
)
set(USERAPP_PRIVATE_LIB
    hi_ipc hi_oam_service
    hi_basic hi_board hi_sysinfo hi_upgrade
    hi_timer hi_notifier hi_util hi_ioreactor   
    hi_owal_ssf openwrt_lib
)

set(USERAPP_PRIVATE_STATIC_LIB vsbcrypt)

set(USERAPP_PRIVATE_STATIC_LIBDIR
        ${HGW_VSOL_LIB_DIR}/libvsbcrypt
)

# 自定义宏
set(USERAPP_PRIVATE_DEFINE)

# 自定义编译选项
set(USERAPP_PRIVATE_COMPILE)

build_app_feature()
