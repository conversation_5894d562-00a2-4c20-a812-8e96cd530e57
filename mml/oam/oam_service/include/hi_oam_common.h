/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  File Name     : hi_oam_common.h
  Version       : Initial Draft
  Created       : 2013/7/1
  Last Modified :
  Description   : oam common header file
  Function List :
******************************************************************************/
#ifndef __HI_OAM_COMMON_H__
#define __HI_OAM_COMMON_H__

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#include "hi_typedef.h"

#define HI_MAC_LEN 6

#define HI_PON_BYTE_BITMASK    0xff   /*单字节掩码*/
#define HI_PON_MAX_USHORT      0xffff
#define HI_PON_MAX_ULONG       0xffffffff

/************************************************
* 位移操作                                      *
*************************************************/
#define HI_PON_BITSHF_HB       2      /* 位数<->半字节数转换需要移位的位数 */
#define HI_PON_BITSHF_B        3      /* 位数<->字节数转换需要移位的位数   */
#define HI_PON_BITSHF_W        4      /* 位数<->字数转换需要移位的位数     */
#define HI_PON_BITSHF_D        5      /* 位数<->双字数转换需要移位的位数   */

#define HI_PON_BITS_HB         (1 << HI_PON_BITSHF_HB)    /* 每半个字节包含的位数 */
#define HI_PON_BITS_B          (1 << HI_PON_BITSHF_B)     /* 每个字节包含的位数   */
#define HI_PON_BITS_W          (1 << HI_PON_BITSHF_W)     /* 每个字包含的位数     */
#define HI_PON_BITS_D          (1 << HI_PON_BITSHF_D)     /* 每个双字包含的位数   */

#define HI_PON_BYTES_W         (1 << (HI_PON_BITSHF_W - HI_PON_BITSHF_B))    /* 每个字包含的字节数   */
#define HI_PON_BYTES_D         (1 << (HI_PON_BITSHF_D - HI_PON_BITSHF_B))    /* 每个双字包含的字节数 */


/* EMAC支持的最大llid数目*/
#define HI_MAX_LLID_NUM      8
#define HI_LLID_INDEX_0      0
#define HI_LLID_INDEX_1      1
#define HI_LLID_INDEX_2      2
#define HI_LLID_INDEX_3      3
#define HI_LLID_INDEX_4      4
#define HI_LLID_INDEX_5      5
#define HI_LLID_INDEX_6      6
#define HI_LLID_INDEX_7      7

/* OUI标识长度 */
#define HI_EPON_OUI_LEN  3 /* OUI标识为3byte */

/* 逻辑所支持的最大OAM消息长度,2000字节 */
#define HI_EPON_MAX_OAM_LEN       2000

/* OAM消息的最小长度64字节,这里扣除了4字节的FCS */
#define HI_EPON_OAM_MIN_LEN      60

/* Teknovus的OUI为0x000DB6 */
#define HI_EPON_TEKNOVUS_OUI_1ST 0x00
#define HI_EPON_TEKNOVUS_OUI_2ND 0x0D
#define HI_EPON_TEKNOVUS_OUI_3RD 0xB6

/* CTC的OUI为0x111111 */
#define HI_EPON_CTC_OUI_1ST      0x11
#define HI_EPON_CTC_OUI_2ND      0x11
#define HI_EPON_CTC_OUI_3RD      0x11

/* Dasan的OUI为0x00D0CB */
#define HI_EPON_DASAN_OUI_1ST    0x00
#define HI_EPON_DASAN_OUI_2ND    0xD0
#define HI_EPON_DASAN_OUI_3RD    0xCB

/* KT的OUI为0xAAAAAA */
#define HI_EPON_KT_OUI_1ST       0xAA
#define HI_EPON_KT_OUI_2ND       0xAA
#define HI_EPON_KT_OUI_3RD       0xAA

/* PMC的OUI为0x000CD5 */
#define HI_EPON_PMC_OUI_1ST      0x00
#define HI_EPON_PMC_OUI_2ND      0x0C
#define HI_EPON_PMC_OUI_3RD      0xD5

/* 华为的OUI为0xBC614E */
#define HI_EPON_HW_OUI_1ST      0xBC
#define HI_EPON_HW_OUI_2ND      0x61
#define HI_EPON_HW_OUI_3RD      0x4E

/* VSOL OUI is 0x8014A8 */
#define HI_EPON_VSOL_OUI_1ST      0x80
#define HI_EPON_VSOL_OUI_2ND      0x14
#define HI_EPON_VSOL_OUI_3RD      0xA8

#define HI_XGEPON_UPCAP_FILEPATH "/config/worka/hi_xgepon_upcap"

typedef enum {
    HI_XGEPON_UP_UNKNOW_E = 0,
    HI_XGEPON_UP_1G,
    HI_XGEPON_UP_10G,
    HI_XGEPON_UP_1G10G,
} hi_xgepon_upcap_e;

/* 扩展OAM类型 */
typedef enum {
    HI_EXOAM_TYPE_TEKNOVUS_E = 0, /* Teknovus 扩展OAM */
    HI_EXOAM_TYPE_CTC_E      = 1, /* CTC 扩展OAM */
    HI_EXOAM_TYPE_DASAN_E    = 2, /* DASAN 扩展OAM */
    HI_EXOAM_TYPE_KT_E       = 3, /* 韩国电信 扩展OAM */
    HI_EXOAM_TYPE_PMC_E      = 4, /* PMC 扩展OAM */
    HI_EXOAM_TYPE_HW_E       = 5, /*华为扩展OAM,  added by w00180499, 2011/3/26*/
    HI_EXOAM_TYPE_VSOL_E     = 6, /*VSOL OAM*/
    HI_EXOAM_TYPE_END_E,
} HI_EPON_EXOAM_TYPE_E;

/* OAM消息code字段内容 */
typedef enum {
    HI_OAM_CODE_INFO_E                = 0x00,  /* 标准OAM注册使用 */
    HI_OAM_CODE_EVENT_NOTIFICATION_E  = 0x01,  /* 事件通知 */
    HI_OAM_CODE_VARIABLE_REQ_E        = 0x02,  /* 变量请求 */
    HI_OAM_CODE_VARIABLE_RESPONSE_E   = 0x03,  /* 变量回应 */
    HI_OAM_CODE_LOOPBACK_CONTROL_E    = 0x04,  /* OAM环回 */
    HI_OAM_CODE_ORGANIZATION_SPEC_E   = 0xFE,  /* 扩展OAM */
} HI_EPON_OAM_CODE_E;


typedef enum {
    HI_OAM_MPCPREG_STATE_E,         //mpcp注册状态上报
    HI_OAM_MPCPREG_DISINFO_E,       //mpcp discovery information上报，仅10G EPON有效
    HI_OAM_ORGANIZE_SPECIFIC_E,     //组织特定消息
    HI_OAM_EVENT_NOTIFY_E,          //事件上报
    HI_OAM_OAMREG_STATE_E,          //OAM注册状态上报
    HI_OAM_PMU_ALARM_E              //节能模块告警上报
} HI_OAM_MSGTYPE_E;

typedef enum {
    HI_EPON_OAM_ALARM_SET_E           = 0,
    HI_EPON_OAM_ALARM_CLEAR_E         = 1,
} HI_EPON_OAM_ALARM_STATE_E;

/* OAM消息格式 */
typedef struct {
    hi_uchar8   uc_dmac[HI_MAC_LEN];
    hi_uchar8   uc_smac[HI_MAC_LEN];
    hi_ushort16 us_typeorlength;    /* OAM消息填充EPON_SLOW_PROTOCOL_TYPE */
    hi_uchar8   uc_subtype;         /* OAM消息操作码,使用E_SLOW_PROTL_OAM解析 */
    hi_ushort16 us_flag;            /* 使用EPON_OAM_FLAG_STRU解析 */
    hi_uchar8   uc_code;            /* 使用EPON_OAM_CODE_ENUM解析 */
    /* 以下消息变长 */
    /* OAM消息最后的4字节FCS软件不关心 */
} __attribute__((packed))hi_epon_oam_s;


/* CTCOAM头 */
typedef struct {
    hi_uchar8 auc_oui[HI_EPON_OUI_LEN];
    hi_uchar8 uc_opercode; /* 使用EPONDRV_CTC_OPER_CODE_ENUM解析 */

    /* 下面消息变长 */
    /* 1.Data */
} __attribute__((packed)) hi_ctcoam_msg_head_s;

/* CTCOAM事件头 */
typedef struct {
    hi_uchar8 auc_oui[HI_EPON_OUI_LEN];
    hi_uchar8 uc_opercode; /* 使用EPONDRV_CTC_OPER_CODE_ENUM解析 */
    hi_uchar8 uc_subtype;

    /* 下面消息变长 */
    /* 1.Data */
} __attribute__((packed)) hi_ctcoam_event_head_s;


/* TLV */
typedef struct {
    hi_uchar8 uc_branch;
    hi_ushort16 us_leaf;
    hi_uchar8 uc_length;

    /* 下面消息变长 */
    /* 1.Value值,长度为uc_length字节 */
} __attribute__((packed)) hi_ctcoam_tlv_s;


typedef struct {
    hi_uint32   ui_llidindex;
    hi_uint32   ui_msglen;
    hi_uint32   ui_exoam_type;
    hi_uchar8   uc_oper;
    hi_uchar8   uc_rsv;
    hi_ushort16 us_rsv;
    hi_ctcoam_tlv_s st_msg; /*ctcoam消息*/
    /*以下数据变长...*/
} __attribute__((packed))hi_ctcoam_msg_s;

/*oam，mpcp状态报告消息*/
typedef struct {
    hi_uchar8 uc_llid;
    hi_uchar8 uc_state;
} hi_oam_status_reports_s;

typedef struct {
    hi_uchar8 uc_llid;
    hi_ushort16 us_disinfo;
} hi_oam_disinfo_reports_s;

typedef struct {
    hi_uchar8 uc_alarm_state;
    hi_uint32 ui_alarminfo;
} hi_oam_alarm_report_s;

typedef struct {
    hi_uint32 ui_llid;
    hi_uint32 ui_msglen;
    hi_uint32 ui_msgtype;
    void      *pv_msg;
} __attribute__((packed))hi_ctcoam_send_ctrl_s;

/* LLID 扩展OAM注册相关数据结构 */
typedef struct {
    hi_uchar8 uc_llidstate;    /* 使用HI_KERNEL_CTCOAM_STATE_E解析 */
    hi_uchar8 uc_negotiatever; /* 协商后决定采用的版本 */
    hi_uchar8 auc_rsvd[2];

    /* CTC三层搅动测试模式,即每次都使用相同的密钥 */
    hi_uchar8 uc_churning_test;
    hi_uchar8 auc_churning_key[3];

    /* 上一次请求的密钥,如果连续多次请求统一密钥,则后面的请求不响应 */
    hi_uint32 ui_lastkeyindex;

} __attribute__((packed)) hi_epon_llid_exoam_gloabal_data_s;

typedef struct {
    hi_uint32   ui_llidindex;
    hi_epon_llid_exoam_gloabal_data_s st_data;
} __attribute__((packed)) hi_epon_llid_get_exoam_gloabal_data_ctrl;

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_OAM_COMMON_H__ */
