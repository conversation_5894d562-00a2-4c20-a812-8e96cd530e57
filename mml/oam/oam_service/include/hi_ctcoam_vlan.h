/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  File Name     : hi_ctcoam_vlan.h
  Version       : Initial Draft
  Created       : 2013/9/30
  Last Modified :
  Description   : ctcoam vlan header
  Function List :
******************************************************************************/
#ifndef __HI_CTCOAM_VLAN_H__
#define __HI_CTCOAM_VLAN_H__

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#define HI_CTCOAM_MAX_FID_NUM 960

typedef struct {
    hi_ushort16  us_fid_num;
    hi_ushort16  rsv;
    hi_uint32    bitmap[(HI_CTCOAM_MAX_FID_NUM + 31) / 32];
} hi_ctcoam_vlan_fid_ctrl_s;

typedef struct {
    hi_ushort16 us_tpid;
    hi_ushort16 us_vlan;
} hi_oam_vlan_tag_s;

hi_uint32  hi_ctcoam_set_tag_act(hi_lsw_port_e em_port, hi_lsw_tag_act_e em_igr_tag_act, hi_lsw_tag_act_e em_igr_untag_act,
                                 hi_lsw_tag_act_e em_egr_tag_act, hi_lsw_tag_act_e em_egr_untag_act);

hi_uint32 hi_ctcoam_set_vlan_transparent(portAllService *newService);
hi_uint32 hi_ctcoam_set_vlan_tag(portAllService *newService, hi_uint32 ui_ring_check);
hi_uint32 hi_ctcoam_set_vlan_translation(portAllService *newService, hi_uint32 ui_ring_check);
hi_uint32 hi_ctcoam_set_vlan_n_to_one_aggregation(portAllService *newService, hi_uint32 ui_ring_check);
hi_uint32 hi_ctcoam_set_vlan_trunk(portAllService *newService, hi_uint32 ui_ring_check);
hi_uint32 hi_ctcoam_set_vlan_multicast(portAllService *newService);
hi_uint32 hi_ctcoam_clr_vlan_multicast(portAllService *newService);

hi_uint32 hi_ctcoam_set_port_default_tag(hi_uchar8 em_port, hi_ushort16 us_tpid, hi_ushort16 us_vlan);
hi_uint32 hi_ctcoam_get_port_default_tag(hi_uchar8 em_port, hi_ushort16 *pus_tpid, hi_ushort16 *pus_vlan);

hi_void hi_ctcoam_vlan_init(hi_void);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_CTCOAM_VLAN_H__ */

