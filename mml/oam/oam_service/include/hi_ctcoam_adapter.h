/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  File Name     : hi_ctcoam_adapter.h
  Version       : Initial Draft
  Created       : 2013/12/20
  Last Modified :
  Description   : ctcoam adapter header
  Function List :
******************************************************************************/
#ifndef __HI_CTCOAM_ADAPTER_H__
#define __HI_CTCOAM_ADAPTER_H__

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#define PORT_CONVERT_AND_CHK(em_port, em_sml_port) \
    do {\
        hi_int32 _i_ret;\
        _i_ret = hi_ctcoam_port_convert((em_port), &(em_sml_port));\
        RET_CHK(_i_ret);\
    }while(0);

#define NULL_PTR_CHK(x)  if (HI_NULL == (x)) return HI_RET_FAIL;

typedef enum {
    HI_VLAN_PORT_E      = 0,
    HI_VLAN_NNI_PORT_E  = 1,
    HI_VLAN_TRANSLATE_E = 2,
    HI_VLAN_CPU_E = 3,
    HI_VLAN_PROC_TYPE_NUM = 4,
} hi_adapter_vlan_proc_type_e ;

#define HI_EPON_CHURNING_KEY_LEN 3
#define HI_EPON_CHURNING_KEY_XEPON_LEN 9  /* 10G epon 9 */

#define HI_EPON_AES_KEY_LEN      16


typedef struct {
    hi_ushort16   us_vlan;
    hi_ushort16   us_uni_bitmap;
    hi_list_head  st_list;
} hi_adapter_vlan_manage_s;

hi_int32 hi_ctcoam_port_convert(hi_lsw_port_e em_src_port, hi_sml_port_e *pem_dst_port);
hi_int32 hi_ctcoam_tag_type_convert(hi_lsw_tag_type_e em_type, hi_sml_tag_type_e *pem_tagtype);
hi_int32 hi_ctcoam_tag_act_convert(hi_lsw_tag_act_e em_tagact, hi_sml_tag_act_e *pem_tagtype);
hi_int32 hi_ctcoam_egr_type_convert(hi_lsw_qos_egr_e em_egr, hi_sml_egress_type_e *pem_sml_egr);
hi_int32 hi_adapter_vlan_list_add(hi_adapter_vlan_proc_type_e em_type, hi_sml_port_e em_port, hi_ushort16 us_vlan);
hi_uint32 hi_lsw_port_get_onu_type(hi_uint32 *ui_type);
hi_uint32 hi_lsw_pon_llid_encipherkey_set(hi_uint32 ui_llid, hi_uchar8 uc_keyindex, hi_uchar8 *puc_key);
uint32_t hi_lsw_holdover_set(uint32_t enable, uint32_t time);
uint32_t hi_lsw_holdover_get(uint32_t *enable, uint32_t *time);
uint32_t hi_lsw_protect_param_set(uint32_t los_optical, uint32_t los_mac);
uint32_t hi_lsw_protect_param_get(uint32_t *los_optical, uint32_t *los_mac);
#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_CTCOAM_ADAPTER_H__ */
