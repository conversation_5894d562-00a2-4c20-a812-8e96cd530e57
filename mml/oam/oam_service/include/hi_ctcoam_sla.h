/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  File Name     : hi_ctcoam_sla.h
  Version       : Initial Draft
  Created       : 2013/9/30
  Last Modified :
  Description   : ctcoam sla header
  Function List :
******************************************************************************/
#ifndef __HI_CTCOAM_SLA_H__
#define __HI_CTCOAM_SLA_H__

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */


typedef enum {
    HI_CTCOAM_SP_E          = 0x00,
    HI_CTCOAM_WRR_E         = 0x01,
    HI_CTCOAM_SP_WRR_E      = 0x02,
} HI_CTCOAM_SLA_BE_SCH_E;

#define HI_256KBPS_TO_1KBPS(x) ((x)<<8)
#define HI_1KBPS_TO_256KBPS(x) ((x)>>8)


#define HI_TOTAL_WEIGHT    100
#define HI_MAX_QUEUE_NUM   8

hi_uint32 hi_ctcoam_sla_service_activate(hi_ctcoam_service_sla_s *pst_sla);

hi_uint32 hi_ctcoam_sla_service_deactivate(hi_void);

hi_uint32 hi_ctcoam_service_sla_get(hi_void *pv_outmsg, hi_ushort16 *pus_changingmsglen);

hi_void   hi_ctcoam_sla_ctrl_get(hi_ctcoam_service_sla_s *pst_sla);



#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_CTCOAM_SLA_H__ */

