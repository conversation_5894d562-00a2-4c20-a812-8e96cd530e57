/******************************************************************************

                  ???? (C), 2009-2019, ?????????

 ******************************************************************************
  ? ? ?   : hi_oam_tapi.h
  ? ? ?   : ??
  ?    ?   : h66243
  ????   : D2014_01_26

******************************************************************************/
#ifndef __HI_FH_OAM_ADAPTER_H__
#define __HI_FH_OAM_ADAPTER_H__


#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#include "hi_util_log.h"

#define CLSMAXRULENUM 12
#define FIELDMAXNUM   12
#define HI_MAC_LEN 6

#define HI_OAM_ONU_VENDORID_LENGTH     4

#define HI_OAM_ONUMODEL_LENGTH     4

/* ????????,???? */
#define HI_OAM_HARDWARE_LENGTH     8

/* ????????,???? */
#define HI_OAM_SOFTWARE_LENGTH     16

#define HI_OAM_EXT_ONU_MODEL_LENGTH     16

/* ?????(??)?? */
#define HI_OAM_IC_VER_LENGTH        3

/* ???????? */
#define HI_OAM_BITMAP_LENGTH        8

/* ONU?? */
#define HI_OAM_ONU_MODEL           "5116"

/* ????? */
#define HI_OAM_ONU_HW_VER          "SD5116"

/* ????? */
#define HI_OAM_ONU_SW_VER          "V200R009C03"

/* firmware??,????(0xff- 1)?? */
#define HI_OAM_ONU_FIRMWARE_VER    "V1.2.3"

/* ONU?????*/
#define HI_OAM_ONU_VENDOR_ID       0x48575443//0x68776877

/* ??????*/
#define HI_OAM_CHIP_VENDOR_ID      0x4857//0x6877

/* ???? */
#define HI_OAM_CHIP_MODEL          0x3133

/* ?????? */
#define HI_OAM_CHIP_REVISION       0x01

/* ????(YY/MM/DD) */
#define HI_OAM_CHIP_VER1           0x09
#define HI_OAM_CHIP_VER2           0x09
#define HI_OAM_CHIP_VER3           0x13

#define HI_OAM_INTERFACE_TYPE_NUM   6 //GE FE POTS WLAN USB CATV

/* ONU??????? */
#define HI_OAM_DS_QUEUE_NUM        1
#define HI_OAM_US_QUEUE_NUM        8
#define HI_OAM_E1_PORT_NUM         0
#define HI_OAM_POTS_PORT_NUM       2
#define HI_OAM_MAX_POTS_PORT_NUM     2
#define HI_OAM_MAX_WLAN_PORT_NUM     1

/* eth????FE?GE???? */
#define HI_OAM_MAX_ETH_PORT_NUM  8
#define HI_OAM_FE_PORT_0GE4FE_MODE  4  /*MODE 1*/
#define HI_OAM_GE_PORT_0GE4FE_MODE  0

#define HI_OAM_FE_PORT_2GE0FE_MODE  0  /*MODE 2*/
#define HI_OAM_GE_PORT_2GE0FE_MODE  2

#define HI_OAM_FE_PORT_1GE0FE_MODE  0  /*MODE 3*/
#define HI_OAM_GE_PORT_1GE0FE_MODE  1

#define HI_OAM_FE_PORT_1GE3FE_MODE  3  /*MODE 3*/
#define HI_OAM_GE_PORT_1GE3FE_MODE  1

#define HI_OAM_FE_PORT_DEFULT_MODE  HI_OAM_FE_PORT_0GE4FE_MODE
#define HI_OAM_GE_PORT_DEFULT_MODE  HI_OAM_GE_PORT_0GE4FE_MODE

#define HI_OAM_ETH_PORT_NUM  HI_OAM_MAX_ETH_PORT_NUM
#define HI_OAM_STAT_PORT_NUM (HI_OAM_ETH_PORT_NUM + 1)
#define HI_OAM_ETH_ID_MAX_NUM  (HI_OAM_ETH_PORT_NUM + 1)

#define hi_ctcoam_dbg_err_print(fmt, args...) \
    hi_debug(HI_SRCMODULE_CMS_OAM_U, "[CTCOAM ERR:%s(%d)]" fmt "\r\n", __func__, __LINE__, ##args)

#define hi_ctcoam_dbg_print(fmt, args...) \
    hi_debug(HI_SRCMODULE_CMS_OAM_U, "[CTCOAM INFO:%s(%d)]" fmt "\r\n", __func__, __LINE__, ##args)

hi_void hi_oam_print_time(hi_uchar8 *puc_func, hi_uint32 ui_line, hi_uint32 ui_msgtype);
#define RET_CHK(x) \
    do {\
        if ((hi_uint32)HI_RET_SUCC != (x)) { \
            hi_ctcoam_dbg_err_print("i_ret = (%u)\n", x);\
            return (hi_uint32)HI_RET_FAIL; \
        } \
    } while(0);

/* ??????? */
typedef enum {
    HI_OAM_NO_BATTERY_BACKUP_E = 0x00,
    HI_OAM_BATTERY_BACKUP_E    = 0x01,
} HI_OAM_BATTERY_BACKUP_ENUM;


typedef enum {
    HI_OAM_INTERFACE_GE_E        = 0x00,
    HI_OAM_INTERFACE_FE_E        = 0x01,
    HI_OAM_INTERFACE_VOIP_E      = 0x02,
    HI_OAM_INTERFACE_TDM_E       = 0x03,
    HI_OAM_INTERFACE_ADSL_E      = 0x04,
    HI_OAM_INTERFACE_VDSL_E      = 0x05,
    HI_OAM_INTERFACE_WLAN_E      = 0x06,
    HI_OAM_INTERFACE_USB_E       = 0x07,
    HI_OAM_INTERFACE_CATV_E      = 0x08,
} HI_OAM_INTERFACE_TYPE_E;

/* ?????????? */
#define HI_OAM_QUEUE_MAX_PER_US_PORT 8
#define HI_OAM_QUEUE_MAX_PER_DS_PORT 8

#define HI_CTCOAM_THRESHOLD_QUEUE_NUM  8

/*ctc oam ?? queue set num */
#define HI_CTCOAM_DBA_MAX_QUEUE_SET_NUM   4
#define HI_CTCOAM_DBA_MIN_QUEUE_SET_NUM   2


#define HI_OAM_LLID_NUM 1

/* Threshold??? */
typedef struct {
    hi_uchar8 uc_report_bitmap;
    hi_ushort16 aus_threshold[HI_CTCOAM_THRESHOLD_QUEUE_NUM];
} __attribute__((packed)) hi_ctcoam_threshold_s;


typedef struct {
    hi_uchar8 uc_queue_set_num;
    hi_ctcoam_threshold_s  st_threshold[HI_CTCOAM_DBA_MAX_QUEUE_SET_NUM];
} __attribute__((packed)) hi_ctcoam_dba_capability_s;

/*流分类条件对应的域*/
typedef enum {
    HI_DA_MAC_E          = 0x00,
    HI_SA_MAC_E          = 0x01,
    HI_ETH_PRI_E         = 0x02,
    HI_VLANID_E          = 0x03,
    HI_ETH_TYPE_E        = 0x04,
    HI_DIP_V4_E          = 0x05,
    HI_SIP_V4_E          = 0x06,
    HI_IPV4_PROTO_E      = 0x07,
    HI_IPV4_DSCP_E       = 0x08,
    HI_IPV6_DSCP_E       = 0x09,
    HI_SPORT_E           = 0x0a,
    HI_DPORT_E           = 0x0b,
    HI_IP_VER_E          = 0x0c,
    HI_IPV6_FLOW_LABEL_E = 0x0d,
    HI_DIP_V6_E          = 0x0e,
    HI_SIP_V6_E          = 0x0f,
    HI_DIP_V6_PREFIX_E   = 0x10,
    HI_SIP_V6_PREFIX_E   = 0x11,
    HI_NEXT_HEADER       = 0x12,
    INVALID_FIELD        = 0xff,
} HI_CTCOAM_CLASSIFY_FIELD_E;

typedef struct {
    hi_uchar8 auc_matchvalue[6]; /*该条件对应的匹配值*/
    hi_uchar8 uc_operator;     /*该条件使用的运算符*/
} __attribute__((packed)) hi_ctcoam_classify_value_oper_ipv4_s;

typedef struct {
    hi_uchar8 auc_matchvalue[16]; /*该条件对应的匹配值*/
    hi_uchar8 uc_operator;     /*该条件使用的运算符*/
} __attribute__((packed)) hi_ctcoam_classify_value_oper_ipv6_s;

typedef union {
    hi_ctcoam_classify_value_oper_ipv4_s  st_oper4;
    hi_ctcoam_classify_value_oper_ipv6_s  st_oper6;
} __attribute__((packed)) hi_ctcoam_classify_value_oper_u;

/*规则匹配内容结构体*/
typedef struct {
    hi_uchar8 uc_select;  /*该条件对应的域*/
    hi_ctcoam_classify_value_oper_u un_value_oper;
} __attribute__((packed)) hi_ctcoam_classify_field_value_oper_s;

/*流分类及标记的规则内容*/
typedef struct {
    hi_uchar8 uc_precedence;      /*优先级排序*/
    hi_uchar8 uc_length;          /*规则长度*/
    hi_uchar8 uc_queuemapped;     /*映射的队列编号*/
    hi_uchar8 uc_primark;         /*优先级标记*/
    hi_uchar8 uc_entrynum;        /*规则需满足条件的数量*/

    /*以下是变长的entry内容描述，由HI_KERNEL_CTCOAM_CLASSIFY_FIELD_VALUE_OPER_STRU解析*/
    hi_ctcoam_classify_field_value_oper_s st_entry[0];  /*需满足的条件内容，个数由ucEntryNum决定*/

} __attribute__((packed)) hi_ctcoam_classify_rule_connect_s;

/*流分类及标记的规则内容*/
typedef struct {
    hi_uchar8 uc_precedence;      /*优先级排序*/
    hi_uchar8 uc_length;          /*规则长度*/
    hi_uchar8 uc_queuemapped;     /*映射的队列编号*/
    hi_uchar8 uc_primark;         /*优先级标记*/
    hi_uchar8 uc_entrynum;        /*规则需满足条件的数量*/
    hi_ctcoam_classify_field_value_oper_s ast_entry[2]; /*用于全局变量保存，暂使用两种entry*/
    //hi_uchar8 ucClassFid;    /*对应的classfid*/
    //hi_uchar8 ucMarkFid;    /*对应的markfid*/
    hi_uchar8 uc_valid; /*规则是否生效*/
} __attribute__((packed)) hi_ctcoam_global_classify_rule_s;

/*流分类规则结构体*/
typedef struct {
    hi_uchar8 uc_action; /*操作类型:0x00-delete given rules
       0x01-add given rules
       0x02-delete all rules
       0x03-list all rules*/
    hi_uchar8 uc_rulesnum; /*规则数量*/

    /*以下为变长的rule内容，由HI_KERNEL_CTCOAM_CLASSIFY_RULE_CONTENT_STRU解析*/
    hi_ctcoam_classify_rule_connect_s stRuleContent[0]; /*规则内容*/

} __attribute__((packed)) hi_ctcoam_classify_marking_s;

/* EMAC的扩展OAM能力 */
typedef struct {
    hi_uchar8 auc_onuvendorid[HI_OAM_ONU_VENDORID_LENGTH];  /* Vendor ID为字符串，用于标识特定的ONU厂商，
                               其编码采用ANSI T1.220标准,采用ASCII/ANSI字符编码 */
    hi_uchar8 auc_onumodel[HI_OAM_ONUMODEL_LENGTH];    /* ONU型号,其编码由厂商自定义 */

    /* ONU设备的硬件版本号,多的字节用来填充字符串结束符 */
    hi_uchar8 auc_hardwarever[HI_OAM_HARDWARE_LENGTH + sizeof(hi_uint32)];

    /* ONU设备的软件版本号,多的字节用来填充字符串结束符 */
    hi_uchar8 auc_softwarever[HI_OAM_SOFTWARE_LENGTH + sizeof(hi_uint32)];

    hi_uchar8 auc_firmwarever[0xff];                      /* ONU芯片的Firmware版本标识符 */
    hi_uchar8 uc_rsvd;                                     /* 字节对齐 */
    hi_ushort16 us_chipset_vendorid;                       /* Vendor ID为字符串，用于标识特定的芯片厂商 */
    hi_ushort16 us_chipsetmodel;                           /* 芯片型号，其编码由厂商自定义 */
    hi_uchar8 uc_revision;                                 /* 芯片修订情况 */
    hi_uchar8 auc_icversionp[HI_OAM_IC_VER_LENGTH];     /* 芯片的版本(硬件) */

    hi_uchar8 auc_bitmap_ge_ports[HI_OAM_BITMAP_LENGTH]; /* 千兆以太网UNI接口分布 */
    hi_uchar8 auc_bitmap_fe_ports[HI_OAM_BITMAP_LENGTH]; /* 百兆以太网UNI接口分布 */
    hi_uchar8 uc_service_supported;                     /* ONU支持的业务类型 */
    hi_uchar8 uc_num_ge_ports;                          /* 千兆以太网UNI接口数量 */
    hi_uchar8 uc_num_fe_ports;                          /* 兆以太网UNI接口数量 */
    hi_uchar8 uc_num_pots_ports;                        /* POTS端口数量 */
    hi_uchar8 uc_num_e1_ports;                          /* E1端口数量 */
    hi_uchar8 uc_num_wlan_ports;
    hi_uchar8 uc_num_catv_ports;                        /* catv rf num */
    hi_uchar8 uc_num_us_queues;                         /* 上行队列数 */
    hi_uchar8 uc_queue_max_per_us_port;                 /* 上行端口最大队列数 */
    hi_uchar8 uc_num_ds_queues;                         /* 下行队列数 */
    hi_uchar8 uc_queue_max_per_ds_port;                 /* 下行端口最大队列数 */
    hi_uchar8 uc_batterybackup;                         /* ONU是否有备用电池 */
    hi_uchar8 uc_fec_ability;
    hi_uint32 ui_onutype;
    hi_uchar8 uc_multillid;
    hi_uchar8 uc_protectiontype;
    hi_uchar8 uc_ponifnum;
    hi_uchar8 uc_slotnumber;
    hi_uchar8 uc_interfacenum;
    uint8_t num_usb_ports;
    uint16_t resv1;
    hi_uint32   ui_interface_type;
    hi_ushort16 us_portnum;
    hi_ushort16 us_voipnum;
    hi_uint32   ui_voip_interface_type;
    hi_uchar8 auc_ext_onumodel[HI_OAM_EXT_ONU_MODEL_LENGTH];    /* ONU型号,其编码由厂商自定义 */
} hi_ctcoam_emac_capability_s;

hi_uint32 hi_oam_init_capability(hi_uint32 ui_llid);

#define HI_OAM_GET_RULE_LEN(x) ((x).lenOfRule + 2)
//port :  disable, enable
hi_uint32 hi_oam_set_onu_port_phystate(hi_uint32 port_index, hi_uchar8 flag);
hi_uint32 hi_oam_get_onu_port_phystate(hi_uint32 port_index, hi_uchar8 *flag);

//port ???
hi_uint32 hi_oam_set_autoneg_admincontrol(hi_uint32 port_index, hi_uint32 flag);

/* ????GE,FE,VOIP,TDM?? */
typedef struct {
    hi_uchar8 uc_ge : 1;
    hi_uchar8 uc_fe : 1;
    hi_uchar8 uc_voip : 1;
    hi_uchar8 uc_tdm : 1;
    hi_uchar8 uc_adsl : 1;
    hi_uchar8 uc_vdsl : 1;
    hi_uchar8 uc_rsvd : 1;
    hi_uchar8 uc_support_private : 1;
} hi_oam_support_service_s;

struct hi_oam_onu_capabilites {
    hi_uchar8 serv_supported;
    hi_uchar8 number_GE;
    hi_uchar8 bitmap_GE[8];
    hi_uchar8 number_FE;
    hi_uchar8 bitmap_FE[8];
    hi_uchar8 number_POTS;
    hi_uchar8 number_E1;
    hi_uchar8 number_usque;
    hi_uchar8 quemax_us;
    hi_uchar8 number_dsque;
    hi_uchar8 quemax_ds;
    hi_uchar8 batteryBackup;
} __attribute__((packed));
typedef struct hi_oam_onu_capabilites hi_oam_onu_capabilites_s;
hi_uint32 hi_oam_get_capabilities1(hi_oam_onu_capabilites_s *cap1);

struct hi_oam_optical_transceiver_diag {
    hi_short16  temperature;
    hi_ushort16 supply_vcc;
    hi_ushort16 tx_bias_current;
    hi_ushort16 tx_power;
    hi_ushort16 rx_power;
} __attribute__((packed));
typedef struct hi_oam_optical_transceiver_diag hi_oam_optical_transceiver_diag_s;
hi_uint32 hi_oam_get_optical_param(hi_oam_optical_transceiver_diag_s *diag);

typedef struct {
    hi_uchar8    uc_queueid;
    hi_ushort16  us_fixpktsize;
    hi_ushort16  us_fixbandwidth;
    hi_ushort16  us_guarantebandwidth;
    hi_ushort16  us_bebandwidth;
    hi_uchar8    uc_wrrweight;
} __attribute__((packed)) hi_ctcoam_service_sla_part_s;

typedef struct {
    hi_uchar8    uc_servicedba;
    hi_uchar8    uc_besch;
    hi_uchar8    uc_highpriorityboundary;
    hi_uint32    ui_cyclelen;
    hi_uchar8    uc_servicenum;
    hi_ctcoam_service_sla_part_s st_sla_service[0];
} __attribute__((packed)) hi_ctcoam_service_sla_s;

hi_uint32 hi_oam_get_service_sla(hi_ctcoam_service_sla_s *pst_sla, hi_ushort16 *len);
hi_uint32 hi_oam_set_service_sla(hi_ctcoam_service_sla_s *pst_sla);

struct hi_oam_onu_capabilites2 {
    hi_uint32    onu_type;
    hi_uchar8    multiLlid;
    hi_uchar8    protection_type;
    hi_uchar8    number_of_PONIf;
    hi_uchar8    number_of_slot;

    hi_uchar8    number_of_interface_type;

    hi_uint32    ge_interface_type;
    hi_ushort16  number_of_ge_port;
    hi_uint32    fe_interface_type;
    hi_ushort16  number_of_fe_port;
    hi_uint32    voip_interface_type;
    hi_ushort16  number_of_voip_port;
    hi_uint32    wlan_interface_type;
    hi_ushort16  number_of_wlan_port;
    hi_uint32    usb_interface_type;
    hi_ushort16  number_of_usb_port;
    hi_uint32    catv_interface_type;
    hi_ushort16  number_of_catv_port;
    hi_uchar8    battery_backup;
} __attribute__((packed));
typedef struct hi_oam_onu_capabilites2 hi_oam_onu_capabilites2_s;
hi_uint32 hi_oam_get_capabilities2(hi_oam_onu_capabilites2_s *cap2);

struct hi_oam_onu_capabilites3 {
    hi_uchar8 uc_ipv6_support;
    hi_uchar8 uc_onu_power_supply_control;
    hi_uchar8 uc_service_sla;
} __attribute__((packed)) ;
typedef struct hi_oam_onu_capabilites3 hi_oam_onu_capabilities3_s;

struct hi_oam_onu_holdoverconfig {
	uint32_t holdover_state; /* 1-DISABLE, 2-ENABLE */
	uint32_t holdover_time; /* ms */
} __attribute__((packed)) ;
typedef struct hi_oam_onu_holdoverconfig hi_oam_onu_holdoverconfig_s;

struct hi_oam_onu_protectionparam {
	uint16_t los_optical;
	uint16_t los_mac;
} __attribute__((packed)) ;
typedef struct hi_oam_onu_protectionparam hi_oam_onu_protectionparam_s;

//port :  disable, enable
hi_uint32 hi_oam_set_onu_port_phystate(hi_uint32 port_index, hi_uchar8 flag);
hi_uint32 hi_oam_get_onu_port_phystate(hi_uint32 port_index, hi_uchar8 *flag);

//port ???
hi_uint32 hi_oam_set_autoneg_admincontrol(hi_uint32 port_index, hi_uint32 flag);

/* ??FEC????? */
typedef enum {
    HI_OAM_API_FEC_UNKOWN_E  = 0x00000001,                /* ????,????? */
    HI_OAM_API_FEC_ENABLE_E  = 0x00000002,                /* ??*/
    HI_OAM_API_FEC_DISABLE_E = 0x00000003,                /* ??? */
} HI_OAM_API_FEC_MODE_E;

hi_uint32 hi_oam_get_fec_enable(hi_uint32 *enable);
hi_uint32 hi_oam_get_fec_mode(hi_uint32 *mode);
hi_uint32 hi_oam_set_fec_mode(hi_uint32 mode);
hi_uint32 hi_oam_get_onu_id(hi_uchar8 *onu_id);
hi_uint32 hi_oam_get_onu_sn(hi_uchar8 *onu_vend_id, hi_uchar8 *onu_model, hi_uchar8 *onu_id,
                            hi_uchar8 *hardwarevesion, hi_uchar8 *softwarevesion, hi_uchar8 *ext_onu_model);
hi_uint32 hi_oam_get_firmwarever(hi_uchar8 *firewarever, hi_uchar8 *len);
hi_uint32 hi_oam_get_chipsetid(hi_uchar8 *vendor_id, hi_uchar8 *chip_model, hi_uchar8 *revision, hi_uchar8 *ic_version_date);

typedef enum {
    HI_OAM_ROUGE_CHECK_E      = 0x00,
    HI_OAM_ROUGE_OPEN_TX_E    = 0x01,
    HI_OAM_ROUGE_CLOSE_TX_E   = 0x02,
} HI_OAM_ROGUE_STATUS_ENUM;

hi_uint32 hi_oam_set_tx_power(HI_OAM_ROGUE_STATUS_ENUM em_rogue_status);

/*???????*/
hi_uint32 hi_oam_set_onu_holdover(hi_uint32 holdstate, hi_uint32 ui_time);
hi_uint32 hi_oam_get_onu_holdover(hi_uint32 *holdstate, hi_uint32 *pui_time);
uint32_t hi_oam_set_onu_protection_param(uint16_t los_optical, uint16_t los_mac);
uint32_t hi_oam_get_onu_protection_param(uint16_t *los_optical, uint16_t *los_mac);
typedef enum {
    HI_OAM_ETH_DEACTIVATE_E = 0x00000001,
    HI_OAM_ETH_ACTIVATE_E   = 0x00000002,
} HI_OAM_ETH_STATE_E;

//onu ??link ??
// port_index ????
//linkStauts 1?linkup , 0 linkdown
hi_uint32 hi_oam_get_eth_port_linkstate(hi_uint32 port_index, hi_uchar8 *link_state);

/*uni flow pause*/
hi_uint32 hi_oam_set_eth_port_pause(hi_ushort16 port_index, hi_uchar8 work_mode);
hi_uint32 hi_oam_get_eth_port_pause(hi_ushort16 port_index, hi_uchar8 *work_mode);


/*uni port rate limit*/
hi_uint32 hi_oam_set_eth_port_up_flow_ratelimit(hi_uint32 port, hi_uint32 cir, hi_uint32 cbs, hi_uint32 ebs);
hi_uint32 hi_oam_get_eth_port_up_flow_ratelimit(hi_uint32 port, hi_uint32 *cir, hi_uint32 *cbs, hi_uint32 *ebs);
hi_uint32 hi_oam_set_eth_port_ds_flow_ratelimit(hi_uint32 port, hi_uint32 cir, hi_uint32 pir);
hi_uint32 hi_oam_get_eth_port_ds_flow_ratelimit(hi_uint32 port, hi_uint32 *cir, hi_uint32 *pir);

typedef enum {
    HI_OAM_UNI_LOOP_DEACTIVATE_E = 0x00000001,
    HI_OAM_UNI_LOOP_ACTIVATE_E   = 0x00000002,
} HI_OAM_UNI_LOOP_STATE_E;
/*uni loop*/

/* ??VLAN??,leaf?0x0021 */
typedef enum {
    HI_OAM_VLAN_TRANSPARENT_E = 0x00,
    HI_OAM_VLAN_TAG_E         = 0x01,
    HI_OAM_VLAN_TRANSLATION_E = 0x02,
    HI_OAM_N_TO_ONE_AGGREGATION_E = 0x03,
    HI_OAM_VLAN_TRUNK_E = 0x04,
} HI_OAM_VLAN_MODE_E;

typedef enum {
    HI_OAM_VLAN_MULTICAST_DEL_E = 0x00,
    HI_OAM_VLAN_MULTICAST_ADD_E = 0x01,
    HI_OAM_VLAN_MULTICAST_CLR_E = 0x02,
    HI_OAM_VLAN_MULTICAST_LIST_E = 0x03,

} HI_OAM_VLAN_MULTICAST_MODE_E;

/************************************************************************/
typedef struct Vlan_Service_Info_s {
    hi_uint32 inVlan;  /*Trunk ???vlan translation?Aggregation???????vlan*/
    hi_uint32 outVlan; /*vlan translation?Aggregation???????vlan*/
} vlanServiceInfo;

typedef struct Port_Service_s {
    hi_ushort16 port_index;         /*1-8*/
    hi_ushort16 serviceNum;/*1-16*/
    hi_ushort16 vlanMode;   /*0x00:transparent mode;0x01:tag mode;0x02:translation mode;0x03:N :1 Aggregation mode;0x04:Trunk mode;???????*/
    //unsigned int defaultTag;/*tag mode?tag vlan,????????pvid,????*/
    hi_ushort16 multiMode;/*dis-multi,en-multi*/
    hi_uint32 multiNum;
    hi_uint32 multiVlanStrip;
    vlanServiceInfo vlanInfo[16]; /*???????*/
    vlanServiceInfo multiVlanInfo[16];
    //unsigned short addVid[64];  /*?????????????????vid*/
} portAllService;
hi_uint32 hi_oam_ctc_set_eth_port_vlan(portAllService *newService);
hi_uint32 hi_oam_ctc_get_eth_port_vlan(portAllService *port_service);
hi_uint32 hi_oam_ctc_clr_vlan_multicast(portAllService *newService);

/************************************************************************/
//???????????:
typedef struct oam_clasmark_rulebody {
    hi_uchar8 precedenceOfRule;
    hi_uchar8 lenOfRule;
    hi_uchar8 queueMapped;
    hi_uchar8 ethPriMark;
    hi_uchar8 numOfField;
} __attribute__((packed)) oam_clasmark_rulebody_t;

typedef struct oam_clasmark_fieldbody {
    hi_uchar8   fieldSelect;
    hi_uchar8   matchValue[16];
    hi_uchar8   operator;
} __attribute__((packed)) oam_clasmark_fieldbody_t;
typedef struct Port_Class_s {
    hi_ushort16 portId;         /*1-4*/
    hi_ushort16 ruleNum;/*0-16*/

    oam_clasmark_rulebody_t ruleBody[CLSMAXRULENUM]; /*?????cos?????*/
    oam_clasmark_fieldbody_t ruleField[CLSMAXRULENUM][FIELDMAXNUM]; /*?????key?*/
} portClass;

hi_uint32 hi_oam_set_class_cfg(portClass *p_port_class);
//??????????:
hi_uint32 hi_oam_get_class_cfg(portClass *p_port_class);
//?????????????:
hi_uint32 hi_oam_del_class_cfg(portClass *p_port_class);
//????????????:
hi_uint32 hi_oam_clear_class_cfg(portClass *p_port_class);

/*control type 0x00 0x01 0x03*/
typedef struct {
    hi_ushort16 us_userid;
    hi_ushort16 us_vlanid;
    hi_uchar8   uc_mac[HI_MAC_LEN];
} __attribute__((packed))hi_ctcoam_gda_mac_s;

/*control type 0x02*/
typedef struct {
    hi_ushort16 us_userid;
    hi_uchar8   uc_mac[HI_MAC_LEN];
    hi_uchar8   uc_ipv4[4];
} __attribute__((packed))hi_ctcoam_gda_mac_ipv4_s;

/*control type 0x04*/
typedef struct {
    hi_ushort16 us_userid;
    hi_ushort16 us_vlanid;
    hi_uchar8   uc_ipv6[16];
} __attribute__((packed))hi_ctcoam_gda_ipv6_s;

/*control type 0x05*/
typedef struct {
    hi_ushort16 us_userid;
    hi_uchar8   uc_mac[HI_MAC_LEN];
    hi_uchar8   uc_ipv6[16];
} __attribute__((packed))hi_ctcoam_gda_mac_ipv6_s;

typedef union {
    hi_ctcoam_gda_mac_s      st_mac;
    hi_ctcoam_gda_mac_ipv4_s st_mac_ipv4;
    hi_ctcoam_gda_ipv6_s     st_ipv6;
    hi_ctcoam_gda_mac_ipv6_s st_mac_ipv6;
} hi_ctcoam_gda_info_s;

typedef struct {
    hi_uint32 ui_enable;
    hi_uint32 ui_close;
} hi_ctcoam_eth_loopdetect_s;

hi_uint32 hi_oam_set_multi_ctrl_item(hi_ctcoam_gda_info_s *pst_gdainfo, hi_uchar8 uc_controltype, hi_uchar8 uc_action);
hi_int32 hi_oam_get_multi_ctrl_item(hi_uchar8 *puc_changingmsglen,
                                    hi_uchar8 *pv_outmsg,
                                    hi_uchar8 *puc_controltype,
                                    hi_uchar8 *puc_entry_num);

/***************************    Add/Del Multicast VLAN(??)*********************************************/
//????:
//portId:???(1~4)
//vlanId:??vlan(1~4095)
hi_uint32 hi_oam_multicast_vlan_add(hi_uint32 portId, hi_uint32 vlanId);
hi_uint32 hi_oam_multicast_vlan_delete(hi_uint32 portId, hi_uint32 vlanId);
hi_uint32 hi_oam_multicast_vlan_clear(hi_uint32 portId);
hi_uint32 hi_oam_multicast_vlan_get(hi_uint32 portId, hi_uint32 *vlanId, hi_uint32 *num);


//????:
//portId:???(1~4)
//vlanId:??vlan(1~4095)
//mcVid:??vlan
//userVid:??vlan
//tagOper:??
typedef enum MULTICAST_TAG_OPER_MODE {
    MC_TAG_OPER_MODE_TRANSPARENT = 0,
    MC_TAG_OPER_MODE_STRIP,
    MC_TAG_OPER_MODE_TRANSLATION
} MC_TAG_OPER_MODE;

hi_uint32 hi_oam_multicast_tag_strip_set(hi_uint32 portId, hi_uint32 tagOper);
hi_uint32 hi_oam_multicast_tag_strip_get(hi_uint32 portId, hi_uchar8 *tagOper);
hi_uint32 hi_oam_multicast_tag_translation_add(hi_uint32 portId, hi_uint32 mcVid, hi_uint32 userVid);
//hi_uint32 hi_oam_multicast_tag_translation_get(hi_uint32 portId, oam_mcast_vlan_translation_entry_t *p_tranlation_entry, hi_uchar8*num);
hi_uint32 hi_oam_multicast_vlan_translate_entry_clear(hi_uint32 portId);

/***************************    *********************************************/
hi_uint32 hi_oam_onu_reset();

//loid ??
hi_uint32 hi_oam_get_onu_logic_sn(hi_uchar8 *logicsn, hi_uint32 logicsn_len);
hi_uint32 hi_oam_get_onu_logic_passwd(hi_uchar8 *logicsn, hi_uint32 logicsn_len);
/***************************    *********************************************/

//hi_int32 PONDRV_OnuVersionGet(hi_uchar8 *hwVer, hi_uchar8 *swVer);
//?? hi_oam_get_onu_sn ????

//onu ??link ??
// portNo ????
//linkStauts 1?linkup , 0 linkdown
//hi_int32 PONDRV_PortLinkStateGet(hi_uchar8 portNo, hi_uchar8 *linkStatus);
//??hi_oam_get_eth_port_linkstate ????

//onu ?? ??
//status 0 ????,1???
hi_uint32 hi_oam_get_onu_status(hi_uchar8 *status);

//onu los 信号获取
// los 为0有光，为1时无光
hi_uint32 hi_oam_get_onu_los(hi_uchar8 *los);

//????????
//ageTime 0-630
//hi_int32 PONDRV_OnuAgeTimeSet(hi_ushort16 ageTime);
//hi_int32 PONDRV_OnuAgeTimeGet(hi_ushort16 *ageTime);
hi_uint32 hi_oam_set_onu_agetime(hi_uint32 ageTime);
hi_uint32 hi_oam_get_onu_agetime(hi_uint32 *ageTime);

//????????
// enable 1 ?? 0 ???
//hi_int32 PONDRV_OnuIsolationSet(hi_uchar8 enable);
//hi_int32 PONDRV_OnuIsolationGet(hi_uchar8 *isoState);
hi_uint32 hi_oam_set_onu_Isolation(hi_uchar8 enable);
hi_uint32 hi_oam_get_onu_Isolation(hi_uchar8 *isoState);

//??rstp ????
//enable 1 ?? 0 ???
hi_uint32 hi_oam_set_rstp_enable(hi_uchar8 enable);
hi_uint32 hi_oam_get_rstp_enable(hi_uchar8 *enable);

//????:  ONU CPU????????
//hi_int32 CMN_OnuSysInfoGet( hi_uint32 *cpu, hi_uint32 *mem);
hi_uint32 hi_oam_get_onu_sysinfo(hi_uint32 *cpu, hi_uint32 *mem);

//ONU ?IP????
//hi_int32 CMN_OnuIPAddrSet(hi_uint32 ipAddr, hi_uint32 mask);
hi_uint32 hi_oam_set_onu_ipaddr(hi_uint32 ipAddr, hi_uint32 mask);

//??????
//hi_uint32 odmSysGEPortGet(void);

//????????
//hi_uint32 PONDRV_PortDeviceTypeGet(hi_uchar8 portNo, hi_uchar8 *deviceType);

//HG??????
//hi_uint32 PONDRV_OnuDeviceHGDetectSet(hi_uchar8 enable);
//hi_uint32 PONDRV_OnuDeviceHGDetectGet(hi_uchar8 *enable);

//HG mac?????
//hi_uint32 PONDRV_OnuDeviceHGMacSectionSet(HG_MAC_st *hg, hi_uchar8 num);

/*?????????,???1Mbps,??*/
/*rate??:32, 64...32*N...1000000kbps*/
typedef enum PACKET_TYPE_t {
    PACKET_BROADCAST,
    PACKET_MULTICAST,
    PACKET_UNKNOWN_UNICAST
} PACKET_TYPE_e;
//hi_uint32 PONDRV_PortIngressSuppressionSet(hi_uchar8 portNo, PACKET_TYPE_e type, hi_uchar8 enable, hi_uint32 rate);
//hi_uint32 PONDRV_PortSuppressionGet(hi_uchar8 portNo, PACKET_TYPE_e type, hi_uchar8 *enable, hi_uint32 *rate);
hi_uint32 hi_oam_set_port_ingress_ratelimit(hi_uchar8 portNo, PACKET_TYPE_e type, hi_uchar8 enable,
        hi_uint32 rate); //?????? ?hi_uint32 hi_oam_get_port_ingress_ratelimit(hi_uchar8 portNo, PACKET_TYPE_e type, hi_uchar8 *enable, hi_uint32 *rate);

//mac????
//hi_uint32 PONDRV_OnuMacLimitSet(hi_uchar8 portNo, hi_uchar8 enable, hi_ushort16  limitNum);
//hi_uint32 PONDRV_OnuMacLimitGet(hi_uchar8 portNo, hi_uchar8 *enable, hi_ushort16 *limitNum, hi_ushort16 *curNum);
hi_uint32 hi_oam_set_onu_maclimit(hi_uchar8 portNo, hi_uchar8 enable, hi_ushort16  limitNum);
hi_uint32 hi_oam_get_onu_maclimit(hi_uchar8 portNo, hi_uchar8 *enable, hi_ushort16 *limitNum, hi_ushort16 *curNum);

//??mac?????
#if 0
hi_uint32 PONDRV_PortDynamicArlGet(hi_uchar8 portNo, hi_ushort16 maxMacNum, ARL_ENTRY_st *entry, hi_ushort16  *num);
hi_uint32 hi_oam_get_onu_macaddr(hi_uchar8 portNo, hi_ushort16 maxMacNum, ARL_ENTRY_st *entry, hi_ushort16  *num);

/*??tm???????cos->icos->queue*/
/*classify???????????, ??????:*/
/*Do not drop > Do not copy to CPU > Drop packets > Copy packets to CPU*/
//hi_uint32 PONDRV_PortAclRuleAdd(hi_uchar8 portNo, hi_uchar8 action, RULE_CLAUSE_st *clause, hi_uchar8 clauseNum, hi_uint32 *ruleId,hi_uint32 *ruleId_drop_all);
//hi_uint32 PONDRV_PortAclRuleDel(hi_uchar8 portNo, hi_uchar8 action, RULE_CLAUSE_st *clause, hi_uchar8 clauseNum, hi_uint32 *ruleId);
hi_uint32 hi_oam_add_port_acl_rule(hi_uchar8 portNo, hi_uchar8 action, RULE_CLAUSE_st *clause, hi_uchar8 clauseNum, hi_uint32 *ruleId, hi_uint32 *ruleId_drop_all);
hi_uint32 hi_oam_delete_port_acl_rule(hi_uchar8 portNo, hi_uchar8 action, RULE_CLAUSE_st *clause, hi_uchar8 clauseNum, hi_uint32 *ruleId);

#endif

/******************************************************************************
* ???: getAclFromConfig
* ????: ??acl ?????buf ?
* ????:
*               port - ONU ???
* ????:
*               buf - ?????acl ????
*               length - buf ???
* ?????:
*               0 - success
*               -1 - fail
* ??:
* ???:??
* ????:2011?2?12?
******************************************************************************/
//hi_uint32  getAclFromConfig(hi_uchar8 port, hi_uchar8 *buf, hi_ushort16 *length);


//??????
//hi_uint32 fh_set_port_service(portService *port_service);
//hi_uint32 fh_get_port_service(portService *port_service);

/***************************    *********************************************/

// add DBA config
// add 3?????

#define HI_OAM_DBA_THRES_QUE_NUM   8
#define HI_OAM_DBA_THRES_QSET_NUM  4
typedef struct {
    hi_ushort16   us_q_thre[HI_OAM_DBA_THRES_QUE_NUM];
} hi_oam_dba_threshold_qset_s;

typedef struct {
    hi_oam_dba_threshold_qset_s   st_qset_thre[HI_OAM_DBA_THRES_QSET_NUM];
} hi_oam_dba_threshold_s;

/* ???????? */
#define HI_OAM_CHURNING_KEY_LEN  3

hi_uint32 hi_oam_get_churning_key(hi_uint32 llid, hi_uchar8 keyIndex, hi_uchar8 *key);
hi_uint32 hi_oam_set_churning_key(hi_uint32 llid, hi_uchar8 keyIndex, hi_uchar8 *key);
hi_uint32 hi_oam_set_llid_key(hi_uint32 ui_llid, hi_uchar8 uc_keyindex, hi_uchar8 *puc_key);
hi_uint32 hi_oam_set_llid_encipher_key(hi_uint32 ui_llid, hi_uchar8 uc_keyindex, hi_uchar8 *puc_key);
hi_uint32 hi_oam_set_cipher_mode(hi_uint32 ui_llid, hi_uint32 ui_mode);
hi_uint32 hi_oam_set_autoneg_restart_autoconfig(hi_int32 port_index);
hi_uint32 hi_oam_get_service_sla(hi_ctcoam_service_sla_s *pst_sla, hi_ushort16 *len);
hi_uint32 hi_oam_set_service_sla(hi_ctcoam_service_sla_s *pst_sla);
hi_uint32 hi_oam_get_flow_ctl_mode(short port_index, unsigned char *work_mode);
hi_uint32 hi_oam_set_flow_ctll(hi_ushort16 port_index, hi_uchar8 work_mode);

hi_uint32 hi_oam_get_loopdetect(hi_uint32 port_index, hi_uint32 *pui_flag);
hi_uint32 hi_oam_set_loopdetect(hi_uint32 port_index, hi_uint32 flag);
hi_uint32 hi_oam_get_loopdetect_close(hi_uint32 port_index, hi_uint32 *pui_flag);
hi_uint32 hi_oam_set_loopdetect_close(hi_uint32 port_index, hi_uint32 flag);

hi_uint32 hi_oam_get_dba(hi_uint32 ui_llid, hi_ctcoam_dba_capability_s *pst_thre);
hi_uint32 hi_oam_set_dba(hi_uint32 ui_llid, hi_ctcoam_dba_capability_s *pst_thre);
hi_uint32 hi_oam_get_port_default_tag(hi_uchar8 em_port, hi_ushort16 *pus_tpid, hi_ushort16 *pus_vlan);
hi_uint32 hi_oam_set_port_default_tag(hi_uchar8 em_port, hi_ushort16 us_tpid, hi_ushort16 us_vlan);

hi_uint32 hi_oam_get_autoneg_admincontrol(hi_uint32 port_index, hi_uint32 *flag);
hi_uint32 hi_oam_get_capabilities3(hi_oam_onu_capabilities3_s *cap3);
hi_uint32 hi_oam_get_port_mode(hi_uint32 ui_port, hi_uchar8 *uc_mode);
/*????????*/
typedef struct {
    hi_ulong64    ul_ds_drop_events;
    hi_ulong64    ul_us_drop_events;
    hi_ulong64    ul_ds_octets;
    hi_ulong64    ul_us_octets;
    hi_ulong64    ul_ds_frames;
    hi_ulong64    ul_us_frames;
    hi_ulong64    ul_ds_bc_frames;
    hi_ulong64    ul_us_bc_frames;
    hi_ulong64    ul_ds_mc_frames;
    hi_ulong64    ul_us_mc_frames;
    hi_ulong64    ul_ds_crc_err_frames;
    hi_ulong64    ul_us_crc_err_frames;
    hi_ulong64    ul_ds_undersize_frames;
    hi_ulong64    ul_us_undersize_frames;
    hi_ulong64    ul_ds_oversize_frames;
    hi_ulong64    ul_us_oversize_frames;
    hi_ulong64    ul_ds_fragments;
    hi_ulong64    ul_us_fragments;
    hi_ulong64    ul_ds_jabbers;
    hi_ulong64    ul_us_jabbers;
    hi_ulong64    ul_ds_collisions;
    hi_ulong64    ul_us_collisions;
    hi_ulong64    ul_ds_frames_64;
    hi_ulong64    ul_ds_frames_65_127;
    hi_ulong64    ul_ds_frames_128_255;
    hi_ulong64    ul_ds_frames_256_511;
    hi_ulong64    ul_ds_frames_512_1023;
    hi_ulong64    ul_ds_frames_1024_1518;
    hi_ulong64    ul_us_frames_64;
    hi_ulong64    ul_us_frames_65_127;
    hi_ulong64    ul_us_frames_128_255;
    hi_ulong64    ul_us_frames_256_511;
    hi_ulong64    ul_us_frames_512_1023;
    hi_ulong64    ul_us_frames_1024_1518;
    hi_ulong64    ul_ds_discard_frames;
    hi_ulong64    ul_us_discard_frames;
    hi_ulong64    ul_ds_error_frames;
    hi_ulong64    ul_us_error_frames;
    hi_ulong64    ul_eth_port_status_change_times;

    hi_ulong64    ul_ds_pause_frames;
    hi_ulong64    ul_us_pause_frames;

    hi_ulong64    ul_us_single_col;
    hi_ulong64    ul_us_multi_col;
    hi_ulong64    ul_us_excess_col;
    hi_ulong64    ul_us_late_col;
} hi_oam_monitoring_data_s;

hi_uint32 hi_oam_get_eth_statistics_data(hi_uint32 ui_port, hi_oam_monitoring_data_s *pst_data);
hi_uint32 hi_oam_get_pon_statistics_data(hi_oam_monitoring_data_s *pst_data);

hi_uint32 hi_oam_get_eth_port_num(hi_void);
hi_uint32 hi_oam_get_llid_num(hi_void);

hi_void hi_oam_map_set(hi_uchar8 uc_llid);

hi_void hi_oam_api_init(hi_void);

hi_uint32 hi_oam_qos_tbl_dump(hi_uchar8 uc_port);

#define HI_OAM_OPT_STRLEN 16
struct hi_oam_optical_info {
	uint8_t module_type;
	uint8_t module_sub_type;
	uint8_t used_type;
	uint8_t encapsulation;
	uint16_t tx_wavelength;
	uint16_t rx_wavelength;
	uint8_t vendor_name[HI_OAM_OPT_STRLEN];
	uint8_t vendor_pn[HI_OAM_OPT_STRLEN];
	uint8_t vendor_sn[HI_OAM_OPT_STRLEN];
};

int32_t hi_oam_get_optical_info(struct hi_oam_optical_info *attr);
#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif /* __HI_FH_OAM_ADAPTER_H__ */
