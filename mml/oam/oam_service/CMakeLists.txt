include(${CONFIG_CMAKE_DIR}/toolchains/${CONFIG_TOOLCHAIN_LINUX_APP}.cmake)

# 组件名称
set(USERAPP_NAME hi_oam_service)

# 组件类型
set(USERAPP_TARGET_TYPE so)

# 依赖的源文件
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/source OAMS_SOURCE_DIR)
set(USERAPP_PRIVATE_SRC
    ${OAMS_SOURCE_DIR}
)

# 依赖的头文件
set(USERAPP_PRIVATE_INC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${HGW_BASIC_DIR}/include
    ${HGW_BASIC_DIR}/include/os
    ${HGW_FWK_DIR}/include
    ${HGW_FWK_DIR}/util/include
    ${HGW_FWK_DIR}/ipc/include
    ${HGW_SERVICE_DIR}/dms/sysinfo/include
    ${HGW_SERVICE_DIR}/dms/board/include
    ${HGW_SERVICE_DIR}/dms/upgrade/include
    ${HGW_SERVICE_DIR}/pon/sml/include
    ${HGW_SERVICE_DIR}/network/common/include
    ${HGW_SERVICE_DIR}/network/netapp/include
    ${HGW_SERVICE_DIR}/network/qos/u_space/include
    ${HGW_SERVICE_DIR}/pon/api/include
    ${HGW_SERVICE_DIR}/dms/optical
    ${CONFIG_LINUX_DRV_DIR}/net/pon/include
    ${HGW_SERVICE_DIR}/dms/cfm/cfm_lib
)

# 自定义宏
set(USERAPP_PRIVATE_DEFINE)

# 自定义编译选项
set(USERAPP_PRIVATE_COMPILE)

set(USERAPP_PRIVATE_LIB
    hi_basic hi_ipc hi_upgrade hi_util
    hi_owal_ssf openwrt_lib
)

# 定义编译日期宏
string(TIMESTAMP CURRENT_DATE "%Y%m%d")
add_compile_definitions(BUILD_DATE="${CURRENT_DATE}")
# 定义编译时间戳宏
string(TIMESTAMP CURRENT_DATE_TIME "%Y-%m-%d %H:%M:%S")
add_compile_definitions(BUILD_TIMESTAMP="${CURRENT_DATE_TIME}")

build_app_feature()
