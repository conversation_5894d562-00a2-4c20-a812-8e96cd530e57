/******************************************************************************

Copyright (c) Hi<PERSON><PERSON><PERSON> (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_oam_tapi.c
  版 本 号   : 初稿
  作    者   : h66243
  生成日期   : D2014_01_26

******************************************************************************/
#include "hi_uspace.h"
#include "hi_basic.h"
#include "hi_ipc.h"
#include "hi_board.h"
#include "hi_sysinfo.h"
#include "hi_adapter.h"

#include "hi_sml_sysinfo.h"
#include "hi_sml_epon.h"
#include "hi_sml_gpon.h"
#include "hi_sml_optical.h"
#include "hi_oam_api.h"
#include "hi_ctcoam_vlan.h"
#include "hi_ctcoam_qos.h"
#include "hi_ctcoam_sla.h"
#include "hi_oam_adapter.h"
#include "hi_sml_vlan.h"
#include "hi_sml_qos.h"

#include "hi_ctcoam_adapter.h"

#include "hi_pon_epon_api.h"

#include <math.h>

#include "hi_cfm_api.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#define HI_ROGUEFILE_LEN 32
#define HI_ROGUEFILE    HI_CONF_WORK_DIR"/rogue"

static hi_char8 g_roguefile[HI_ROGUEFILE_LEN];

portAllService g_st_vlan_cfg[HI_OAM_ETH_ID_MAX_NUM];

hi_ctcoam_emac_capability_s g_st_emac_capability;
hi_ctcoam_eth_loopdetect_s g_st_loopback_check[HI_OAM_ETH_ID_MAX_NUM];

static hi_uchar8 g_auc_ge_bitmap_2ge0fe[] = {0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3};
static hi_uchar8 g_auc_fe_bitmap_2ge0fe[] = {0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0};
static hi_uchar8 g_auc_ge_bitmap_0ge4fe[] = {0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0};
static hi_uchar8 g_auc_fe_bitmap_0ge4fe[] = {0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0f};
static hi_uchar8 g_auc_ge_bitmap_1ge0fe[] = {0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1};
static hi_uchar8 g_auc_fe_bitmap_1ge0fe[] = {0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0};
static hi_uchar8 g_auc_ge_bitmap_1ge3fe[] = {0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1};
static hi_uchar8 g_auc_fe_bitmap_1ge3fe[] = {0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0e};

static void hi_oam_init_capability_chip(void)
{
    g_st_emac_capability.us_chipset_vendorid = HI_OAM_CHIP_VENDOR_ID;
    g_st_emac_capability.us_chipsetmodel = HI_OAM_CHIP_MODEL;
    g_st_emac_capability.uc_revision = HI_OAM_CHIP_REVISION;
    g_st_emac_capability.auc_icversionp[0] = HI_OAM_CHIP_VER1;
    g_st_emac_capability.auc_icversionp[1] = HI_OAM_CHIP_VER2;
    g_st_emac_capability.auc_icversionp[2] = HI_OAM_CHIP_VER3;
}

hi_uint32 hi_oam_init_capability(hi_uint32 ui_llid)
{
    hi_uint32 ui_ret;
    hi_board_info_s st_info;
    hi_sysinfo_data_s st_sysinfo;
    hi_oam_support_service_s st_supportservice;
    hi_uint32 ui_len;

    if (ui_llid != HI_LLID_INDEX_0) { //只有llid0时才进行初始化
        return HI_RET_SUCC;
    }

    ui_ret = HI_IPC_CALL("hi_board_info_get", &st_info);
    if (HI_RET_SUCC != ui_ret) {
        return ui_ret;
    }
    ui_ret = HI_IPC_CALL("hi_sysinfo_data_get", &(st_sysinfo));
    if (HI_RET_SUCC != ui_ret) {
        return ui_ret;
    }
    HI_OS_MEMSET_S(&g_st_emac_capability, sizeof(hi_ctcoam_emac_capability_s), 0, sizeof(hi_ctcoam_emac_capability_s));

    /* SD5115 EMAC支持FEC */
    g_st_emac_capability.uc_fec_ability = HI_TRUE;

    /* ONU厂商及型号 */
    HI_OS_MEMCPY_S(g_st_emac_capability.auc_onuvendorid, sizeof(g_st_emac_capability.auc_onuvendorid), st_sysinfo.ac_vendor_id, HI_OAM_ONU_VENDORID_LENGTH);
    /*
     *GPON use equipment id as model ,LOG use product class as model ,so modelname...
     */
    ui_len =  strlen(st_sysinfo.ac_equid);
    if (ui_len > 4) {
        HI_OS_MEMCPY_S(g_st_emac_capability.auc_onumodel, sizeof(g_st_emac_capability.auc_onumodel), &st_sysinfo.ac_equid[ui_len - 4], HI_OAM_ONUMODEL_LENGTH);
    } else {
        HI_OS_MEMCPY_S(g_st_emac_capability.auc_onumodel, sizeof(g_st_emac_capability.auc_onumodel), st_sysinfo.ac_equid, HI_OAM_ONUMODEL_LENGTH);
    }
    HI_OS_MEMCPY_S(g_st_emac_capability.auc_ext_onumodel, sizeof(g_st_emac_capability.auc_ext_onumodel), st_sysinfo.ac_equid, HI_OAM_EXT_ONU_MODEL_LENGTH);
    /* ONU软硬件版本 */

    HI_OS_MEMCPY_S(g_st_emac_capability.auc_hardwarever, sizeof(g_st_emac_capability.auc_hardwarever), st_sysinfo.ac_hw_version, HI_OAM_HARDWARE_LENGTH);
    
/*Modified by fyy for bug#16079*/
#if 1
    snprintf((char *)g_st_emac_capability.auc_softwarever, sizeof(g_st_emac_capability.auc_softwarever), "%.7s-%.6s", (char *)st_sysinfo.ac_sw_version, BUILD_DATE + 2); //modify by cwz
    /* firmware版本 */
    snprintf((char *)g_st_emac_capability.auc_firmwarever, sizeof(g_st_emac_capability.auc_firmwarever), "%.6s %.32s", (char *)HI_OAM_ONU_FIRMWARE_VER, BUILD_TIMESTAMP);
#else
    HI_OS_MEMCPY_S(g_st_emac_capability.auc_softwarever, sizeof(g_st_emac_capability.auc_softwarever), st_sysinfo.ac_sw_version, HI_OAM_SOFTWARE_LENGTH);
    /* firmware版本 */
    HI_OS_MEMCPY_S(g_st_emac_capability.auc_firmwarever, sizeof(g_st_emac_capability.auc_firmwarever), HI_OAM_ONU_FIRMWARE_VER,
                   sizeof(HI_OAM_ONU_FIRMWARE_VER));
#endif
    hi_oam_init_capability_chip();

    /* 获取ONT当前eth端口数*/

    g_st_emac_capability.uc_num_ge_ports = (hi_uchar8)st_info.ui_ge_num;
    g_st_emac_capability.uc_num_fe_ports = (hi_uchar8)st_info.ui_fe_num;
    g_st_emac_capability.uc_num_pots_ports = (hi_uchar8)st_info.ui_voice_num;
    g_st_emac_capability.uc_num_wlan_ports = (hi_uchar8)(st_info.ui_wlan24 + st_info.ui_wlan58);
    g_st_emac_capability.num_usb_ports = (hi_uchar8)(st_info.ui_usb2_num + st_info.ui_usb3_num);
    g_st_emac_capability.uc_num_catv_ports = (hi_uchar8)st_info.ui_catv_num;

    /*
    ui_portnum = st_info.ui_port_num;
    if (ui_portnum == 1)
    {
        g_st_emac_capability.uc_num_ge_ports = ui_portnum;
        g_st_emac_capability.uc_num_pots_ports   = 0;
    }
    else
    {
        g_st_emac_capability.uc_num_fe_ports = ui_portnum - 1;
        g_st_emac_capability.uc_num_ge_ports = 1;
        g_st_emac_capability.uc_num_pots_ports   = HI_OAM_POTS_PORT_NUM;
    }
    */

    /* ONU能力 */
    g_st_emac_capability.uc_batterybackup    = HI_OAM_NO_BATTERY_BACKUP_E;
    g_st_emac_capability.uc_num_ds_queues    = HI_OAM_DS_QUEUE_NUM;
    g_st_emac_capability.uc_num_us_queues    = HI_OAM_US_QUEUE_NUM;
    g_st_emac_capability.uc_num_e1_ports     = HI_OAM_E1_PORT_NUM;
    g_st_emac_capability.uc_queue_max_per_ds_port = HI_OAM_QUEUE_MAX_PER_DS_PORT;
    g_st_emac_capability.uc_queue_max_per_us_port = HI_OAM_QUEUE_MAX_PER_US_PORT;

    g_st_emac_capability.uc_interfacenum     = HI_OAM_INTERFACE_TYPE_NUM;
    /* 支持哪些业务端口 */
    if (0 < g_st_emac_capability.uc_num_fe_ports) {
        st_supportservice.uc_fe = HI_TRUE;

    }

    if (0 < g_st_emac_capability.uc_num_ge_ports) {
        st_supportservice.uc_ge = HI_TRUE;
    }

    st_supportservice.uc_tdm = HI_FALSE;

    /*20151013:begin*/
    if (0 < g_st_emac_capability.uc_num_pots_ports) {
        st_supportservice.uc_voip = HI_TRUE;
    }
    //st_supportservice.uc_voip = HI_DISABLE;
    /*20151013:end*/

    /* support vsol private */
    st_supportservice.uc_support_private = HI_TRUE;

    HI_OS_MEMCPY_S(&g_st_emac_capability.uc_service_supported, sizeof(g_st_emac_capability.uc_service_supported), &st_supportservice, sizeof(st_supportservice));

    /* 端口的比特掩码 */
    if ((HI_OAM_FE_PORT_0GE4FE_MODE == g_st_emac_capability.uc_num_fe_ports)
        && (HI_OAM_GE_PORT_0GE4FE_MODE == g_st_emac_capability.uc_num_ge_ports)) {
        HI_OS_MEMCPY_S(g_st_emac_capability.auc_bitmap_ge_ports, sizeof(g_st_emac_capability.auc_bitmap_ge_ports), g_auc_ge_bitmap_0ge4fe,
                       HI_OAM_BITMAP_LENGTH);
        HI_OS_MEMCPY_S(g_st_emac_capability.auc_bitmap_fe_ports, sizeof(g_st_emac_capability.auc_bitmap_fe_ports), g_auc_fe_bitmap_0ge4fe,
                       HI_OAM_BITMAP_LENGTH);
    } else if ((HI_OAM_FE_PORT_2GE0FE_MODE == g_st_emac_capability.uc_num_fe_ports)
               && (HI_OAM_GE_PORT_2GE0FE_MODE == g_st_emac_capability.uc_num_ge_ports)) {
        HI_OS_MEMCPY_S(g_st_emac_capability.auc_bitmap_ge_ports, sizeof(g_st_emac_capability.auc_bitmap_ge_ports), g_auc_ge_bitmap_2ge0fe,
                       HI_OAM_BITMAP_LENGTH);
        HI_OS_MEMCPY_S(g_st_emac_capability.auc_bitmap_fe_ports, sizeof(g_st_emac_capability.auc_bitmap_fe_ports), g_auc_fe_bitmap_2ge0fe,
                       HI_OAM_BITMAP_LENGTH);
    } else if ((HI_OAM_FE_PORT_1GE0FE_MODE == g_st_emac_capability.uc_num_fe_ports)
               && (HI_OAM_GE_PORT_1GE0FE_MODE == g_st_emac_capability.uc_num_ge_ports)) {
        HI_OS_MEMCPY_S(g_st_emac_capability.auc_bitmap_ge_ports, sizeof(g_st_emac_capability.auc_bitmap_ge_ports), g_auc_ge_bitmap_1ge0fe,
                       HI_OAM_BITMAP_LENGTH);
        HI_OS_MEMCPY_S(g_st_emac_capability.auc_bitmap_fe_ports, sizeof(g_st_emac_capability.auc_bitmap_fe_ports), g_auc_fe_bitmap_1ge0fe,
                       HI_OAM_BITMAP_LENGTH);
    } else if ((HI_OAM_FE_PORT_1GE3FE_MODE == g_st_emac_capability.uc_num_fe_ports)
               && (HI_OAM_GE_PORT_1GE3FE_MODE == g_st_emac_capability.uc_num_ge_ports)) {
        HI_OS_MEMCPY_S(g_st_emac_capability.auc_bitmap_ge_ports, sizeof(g_st_emac_capability.auc_bitmap_ge_ports), g_auc_ge_bitmap_1ge3fe,
                       HI_OAM_BITMAP_LENGTH);
        HI_OS_MEMCPY_S(g_st_emac_capability.auc_bitmap_fe_ports, sizeof(g_st_emac_capability.auc_bitmap_fe_ports), g_auc_fe_bitmap_1ge3fe,
                       HI_OAM_BITMAP_LENGTH);
    }

    g_st_emac_capability.uc_multillid = HI_OAM_LLID_NUM;

    return HI_RET_SUCC;

}

//参数说明：port_index 端口号1-4，
//  Flag 为1 使能 ， 0为不使能；
hi_uint32 hi_oam_set_onu_port_phystate(hi_uint32 port_index, hi_uchar8 uc_flag)
{
    return hi_lsw_port_set_status((hi_lsw_port_e)port_index, uc_flag);
}

//参数说明：port_index 端口号，
//  Flag 为1 使能 ， 0为不使能；
hi_uint32 hi_oam_get_onu_port_phystate(hi_uint32 port_index, hi_uchar8 *puc_flag)
{
    hi_lsw_phy_link_sta_e em_linkstate;

    hi_lsw_phy_get_status((hi_lsw_port_e)port_index, &em_linkstate);
    *puc_flag = em_linkstate;

    return HI_RET_SUCC;

}

//GE端口自协商，参数说明：
//Port_index 端口号，
//Flag  为1表示activate   0为Deactivate
hi_uint32 hi_oam_set_autoneg_admincontrol(hi_uint32 port_index, hi_uint32 flag)
{
    hi_lsw_phy_mode_e    em_workmode;

    if (HI_TRUE == flag) {
        em_workmode = HI_LSW_PHY_MODE_AUTO;
    } else {
        em_workmode = HI_LSW_PHY_MODE_FDX_100M_E;
    }

    return hi_lsw_port_set_workmode((hi_lsw_port_e)port_index, em_workmode);
}

hi_uint32 hi_oam_get_autoneg_admincontrol(hi_uint32 port_index, hi_uint32 *flag)
{
    hi_lsw_phy_mode_e    em_workmode;

    hi_lsw_port_get_workmode((hi_lsw_port_e)port_index, &em_workmode);
    if (HI_LSW_PHY_MODE_AUTO == em_workmode) {
        *flag =  HI_TRUE;
    } else {
        *flag =  HI_FALSE;
    }

    return HI_RET_SUCC;
}


hi_uint32 hi_oam_get_port_mode(hi_uint32 ui_port, hi_uchar8 *uc_mode)
{
    hi_lsw_phy_mode_e em_workmode;

    hi_lsw_phy_get_workmode((hi_lsw_port_e)ui_port, &em_workmode);

    /*
       以太网端口双工速率状态：
       0x01：10M-Half
       0x02：10M-Full
       0x03：100M-Half
       0x04：100M-Full
       0x05：1G
    */

    switch (em_workmode) {
        case HI_LSW_PHY_MODE_AUTO:
            break;

        case HI_LSW_PHY_MODE_HDX_10M_E:
            *uc_mode = 0x01;
            break;

        case HI_LSW_PHY_MODE_HDX_100M_E:
            *uc_mode = 0x03;
            break;

        case HI_LSW_PHY_MODE_HDX_1000M_E:
            *uc_mode = 0x05;
            break;

        case HI_LSW_PHY_MODE_FDX_10M_E:
            *uc_mode = 0x02;
            break;

        case HI_LSW_PHY_MODE_FDX_100M_E:
            *uc_mode = 0x04;
            break;

        case HI_LSW_PHY_MODE_FDX_1000M_E:
            *uc_mode = 0x05;
            break;

        case HI_LSW_PHY_MODE_ETH_TEST_E:
            *uc_mode = 0x01;
            break;

        default:
            break;
    }

    return HI_RET_SUCC;
}

//参数说明：int enable 对应value的值
//  是否支持FEC功能
// 直接反馈支持FEC
hi_uint32 hi_oam_get_fec_enable(hi_uint32 *enable)
{
    if (HI_TRUE == g_st_emac_capability.uc_fec_ability) {
        *enable = HI_OAM_API_FEC_ENABLE_E;
    } else if (HI_FALSE == g_st_emac_capability.uc_fec_ability) {
        *enable = HI_OAM_API_FEC_DISABLE_E;
    } else {
        *enable = HI_OAM_API_FEC_UNKOWN_E;
    }
    return HI_RET_SUCC;
}

//参数说明：int *mode 对应value的值
//对ONU的FEC功能打开与关闭,EPON PHY接口的双向FEC功能的配置
hi_uint32 hi_oam_get_fec_mode(hi_uint32 *mode)
{
    hi_uint32 ui_enable;
    hi_uint32 ui_ret;

    ui_ret = hi_emac_get_fec(0, &ui_enable);
    if (HI_RET_SUCC != ui_ret) {
        return HI_RET_FAIL;
    }

    /* 检查查询结果是否正确 */
    switch (ui_enable) {
        case HI_TRUE:
            *mode = HI_OAM_API_FEC_ENABLE_E;
            break;

        case HI_FALSE:
            *mode = HI_OAM_API_FEC_DISABLE_E;
            break;

        default:
            return HI_RET_NOTSUPPORT;
    }
    return HI_RET_SUCC;
}

//参数说明：int *mode 对应value的值
//对ONU的FEC功能打开与关闭,EPON PHY接口的双向FEC功能的配置
hi_uint32 hi_oam_set_fec_mode(hi_uint32 mode)
{
    hi_uint32 ui_ret;

    /* 检查所需设置值是否正确 */
    if ((HI_OAM_API_FEC_UNKOWN_E != mode)
        && (HI_OAM_API_FEC_ENABLE_E != mode)
        && (HI_OAM_API_FEC_DISABLE_E != mode)) {
        return HI_RET_INVALID_PARA;
    }

    /* 将上下行FEC使能标记同时设置为该值 */
    if (HI_OAM_API_FEC_ENABLE_E == mode) {
        ui_ret = hi_emac_set_fec(0,  HI_TRUE);
        if (HI_RET_SUCC != ui_ret) {
            return HI_RET_FAIL;
        }
    } else if (HI_OAM_API_FEC_DISABLE_E == mode) {
        /*标准中规定,如果aFECMode=Disabled,ONU和OLT的接收机仍应该能够接收FEC编码的（FEC-coded）帧
        和非FEC编码（non-FEC-coded）帧*/
        ui_ret = hi_emac_set_fec(0, HI_FALSE);
        if (HI_RET_SUCC != ui_ret) {
            return HI_RET_FAIL;
        }
    } else {
        return HI_RET_SUCC;
    }
    return HI_RET_SUCC;

}

hi_uint32 hi_oam_get_onu_id(hi_uchar8 *onu_id)
{
    hi_uint32 ui_ret = HI_RET_FAIL;
    struct hi_epon_llid0mac llid0mac;

    ui_ret = HI_IPC_CALL("hi_epon_get_llid0mac", &llid0mac);
    if (HI_RET_SUCC != ui_ret) {
        return HI_RET_FAIL;
    }

    HI_OS_MEMCPY_S(onu_id, HI_MAC_LEN, llid0mac.mac, HI_MAC_LEN);
    return HI_RET_SUCC;
}

hi_uint32 hi_oam_get_onu_sn(hi_uchar8 *onu_vend_id, hi_uchar8 *onu_model, hi_uchar8 *onu_id,
                            hi_uchar8 *hardwarevesion, hi_uchar8 *softwarevesion, hi_uchar8 *ext_onu_model)
{
    hi_uint32 ui_ret;

    /* get vendorID and model para */
    HI_OS_MEMCPY_S(onu_vend_id, HI_OAM_ONU_VENDORID_LENGTH, g_st_emac_capability.auc_onuvendorid,
                   HI_OAM_ONU_VENDORID_LENGTH);
    HI_OS_MEMCPY_S(onu_model, HI_OAM_ONUMODEL_LENGTH, g_st_emac_capability.auc_onumodel,
                   HI_OAM_ONUMODEL_LENGTH);
    HI_OS_MEMCPY_S(hardwarevesion, HI_OAM_HARDWARE_LENGTH, g_st_emac_capability.auc_hardwarever,
                   HI_OAM_HARDWARE_LENGTH);
    HI_OS_MEMCPY_S(softwarevesion, HI_OAM_SOFTWARE_LENGTH, g_st_emac_capability.auc_softwarever,
                   HI_OAM_SOFTWARE_LENGTH);

    ui_ret = hi_emac_get_llid_mac(0, onu_id);
    if (HI_RET_SUCC != ui_ret) {
        return HI_RET_FAIL;
    }

    /*这个值可修改，由厂家设置*/
    HI_OS_MEMCPY_S(ext_onu_model, HI_OAM_EXT_ONU_MODEL_LENGTH, g_st_emac_capability.auc_ext_onumodel, HI_OAM_EXT_ONU_MODEL_LENGTH);

    return HI_RET_SUCC;
}

hi_uint32 hi_oam_get_firmwarever(hi_uchar8 *firewarever, hi_uchar8 *len)
{
    //参数说明：u8 *firewarever 对应version
    HI_OS_MEMCPY_S(firewarever, (hi_uchar8)strlen((hi_char8 *)g_st_emac_capability.auc_firmwarever), g_st_emac_capability.auc_firmwarever, (hi_uchar8)strlen((hi_char8 *)g_st_emac_capability.auc_firmwarever));
    *len = (hi_uchar8)strlen((hi_char8 *)g_st_emac_capability.auc_firmwarever);
    return HI_RET_SUCC;
}

hi_uint32 hi_oam_get_chipsetid(hi_uchar8 *vendor_id, hi_uchar8 *chip_model, hi_uchar8 *revision, hi_uchar8 *ic_version_date)
{
    HI_OS_MEMCPY_S(vendor_id, sizeof(hi_ushort16), &g_st_emac_capability.us_chipset_vendorid, sizeof(hi_ushort16));
    HI_OS_MEMCPY_S(chip_model, sizeof(hi_ushort16), &g_st_emac_capability.us_chipsetmodel, sizeof(hi_ushort16));
    HI_OS_MEMCPY_S(revision, sizeof(hi_uchar8), &g_st_emac_capability.uc_revision, sizeof(hi_uchar8));
    HI_OS_MEMCPY_S(ic_version_date, HI_OAM_IC_VER_LENGTH, g_st_emac_capability.auc_icversionp, HI_OAM_IC_VER_LENGTH);

    return HI_RET_SUCC;
}

hi_uint32 hi_oam_set_tx_power(HI_OAM_ROGUE_STATUS_ENUM em_rogue_status)
{
    hi_int32 ret;
    hi_uint32 ui_tx_power = HI_TRUE;
    switch (em_rogue_status) {
        case HI_OAM_ROUGE_CHECK_E:
            printf("\r\n rogue onu check \r\n");
            ui_tx_power = HI_FALSE;
            break;
        case HI_OAM_ROUGE_OPEN_TX_E:
            printf("\r\n mask as not rogue onu \r\n");
            unlink(g_roguefile);
            ui_tx_power = HI_TRUE;
            break;
        case HI_OAM_ROUGE_CLOSE_TX_E:
            printf("\r\n mask as rogue onu \r\n");
            ret = creat(g_roguefile, 0);
            if(-1 != ret) {
                close(ret);
            }
            ui_tx_power = HI_FALSE;
            break;
        default:
            break;
    }

    return hi_lsw_tx_power_set(ui_tx_power);
}


hi_uint32 hi_oam_get_capabilities1(hi_oam_onu_capabilites_s *cap1)
{
    cap1->serv_supported = g_st_emac_capability.uc_service_supported;
    cap1->number_GE =      g_st_emac_capability.uc_num_ge_ports;
    HI_OS_MEMCPY_S(cap1->bitmap_GE, sizeof(cap1->bitmap_GE), g_st_emac_capability.auc_bitmap_ge_ports, 8);
    cap1->number_FE =      g_st_emac_capability.uc_num_fe_ports;
    HI_OS_MEMCPY_S(cap1->bitmap_FE, sizeof(cap1->bitmap_FE), g_st_emac_capability.auc_bitmap_fe_ports, 8);
    cap1->number_POTS    = g_st_emac_capability.uc_num_pots_ports;
    cap1->number_E1      = g_st_emac_capability.uc_num_e1_ports;
    cap1->number_usque   = g_st_emac_capability.uc_num_us_queues;
    cap1->quemax_us      = g_st_emac_capability.uc_queue_max_per_us_port;
    cap1->number_dsque   = g_st_emac_capability.uc_num_ds_queues;
    cap1->quemax_ds      = g_st_emac_capability.uc_queue_max_per_ds_port;
    cap1->batteryBackup  = g_st_emac_capability.uc_batterybackup;

    return HI_RET_SUCC;
}

hi_uint32 hi_oam_get_optical_param(hi_oam_optical_transceiver_diag_s *diag)
{
    float s_voltage = 0;
    float s_rx = 0;
    float s_tx = 0;
    float s_bias = 0;
    float s_tep = 0;

    if (HI_NULL == diag) {
        return HI_RET_INVALID_PARA;
    }

    hi_chip_get_laser_attr(&s_voltage, &s_rx, &s_tx, &s_bias, &s_tep);

    s_voltage = s_voltage * 10000;
    diag->supply_vcc = (hi_ushort16)s_voltage;

    s_rx = s_rx * 10000;
    diag->rx_power = (hi_ushort16)s_rx;

    s_tx = s_tx * 10000;
    diag->tx_power = (hi_ushort16)s_tx;

    s_bias = s_bias * 1000 / 2;
    diag->tx_bias_current = (hi_ushort16) s_bias;

    s_tep *= 256;
    diag->temperature = (hi_ushort16)s_tep;
    return HI_RET_SUCC;
}

hi_uint32 hi_oam_get_service_sla(hi_ctcoam_service_sla_s *pst_sla, hi_ushort16 *len)
{
    return hi_ctcoam_service_sla_get((hi_void *)pst_sla, len);
}

hi_uint32 hi_oam_set_service_sla(hi_ctcoam_service_sla_s *pst_sla)
{
    if (pst_sla->uc_servicedba > HI_TRUE) {
        return HI_RET_INVALID_PARA;
    }

    if (pst_sla->uc_servicedba == HI_FALSE) {
        return hi_ctcoam_sla_service_deactivate();
    }
    return hi_ctcoam_sla_service_activate(pst_sla);
}

hi_uint32 hi_oam_get_capabilities2(hi_oam_onu_capabilites2_s *cap2)
{
    hi_lsw_port_get_onu_type(&(g_st_emac_capability.ui_onutype));
    cap2->onu_type                 = g_st_emac_capability.ui_onutype;
    cap2->multiLlid                = g_st_emac_capability.uc_multillid;
    cap2->protection_type          = g_st_emac_capability.uc_protectiontype;
    cap2->number_of_PONIf          = g_st_emac_capability.uc_ponifnum;
    cap2->number_of_slot           = g_st_emac_capability.uc_slotnumber;

    cap2->number_of_interface_type = g_st_emac_capability.uc_interfacenum;

    cap2->ge_interface_type        = HI_OAM_INTERFACE_GE_E;
    cap2->number_of_ge_port        = g_st_emac_capability.uc_num_ge_ports;

    cap2->fe_interface_type        = HI_OAM_INTERFACE_FE_E;
    cap2->number_of_fe_port        = g_st_emac_capability.uc_num_fe_ports;

    cap2->voip_interface_type      = HI_OAM_INTERFACE_VOIP_E;
    cap2->number_of_voip_port      = g_st_emac_capability.uc_num_pots_ports;

    cap2->wlan_interface_type        = HI_OAM_INTERFACE_WLAN_E;
    cap2->number_of_wlan_port        = g_st_emac_capability.uc_num_wlan_ports;

    cap2->usb_interface_type        = HI_OAM_INTERFACE_USB_E;
    cap2->number_of_usb_port        = g_st_emac_capability.num_usb_ports;

    cap2->catv_interface_type      = HI_OAM_INTERFACE_CATV_E;
    cap2->number_of_catv_port      = g_st_emac_capability.uc_num_catv_ports;

    cap2->battery_backup           = g_st_emac_capability.uc_batterybackup;

    return HI_RET_SUCC;
}

hi_uint32 hi_oam_get_capabilities3(hi_oam_onu_capabilities3_s *cap3)
{
    cap3->uc_ipv6_support             = HI_TRUE;
    cap3->uc_onu_power_supply_control = HI_FALSE;
    cap3->uc_service_sla              = 0x8;

    return HI_RET_SUCC;
}
/*用于光链路保护*/
uint32_t hi_oam_set_onu_holdover(uint32_t holdstate, uint32_t ui_time)
{
	return hi_lsw_holdover_set(holdstate, ui_time);
}
uint32_t hi_oam_get_onu_holdover(uint32_t *holdstate, uint32_t *pui_time)
{
	return hi_lsw_holdover_get(holdstate, pui_time);
}

uint32_t hi_oam_set_onu_protection_param(uint16_t los_optical, uint16_t los_mac)
{
	return hi_lsw_protect_param_set((uint32_t)los_optical, (uint32_t)los_mac);
}

uint32_t hi_oam_get_onu_protection_param(uint16_t *los_optical, uint16_t *los_mac)
{
	uint32_t ret = 0;
	uint32_t los_mac_tmp = 0;
	uint32_t los_optical_tmp = 0;

	if (los_optical == NULL || los_mac == NULL)
		return -HI_RET_NULL_PTR;

	ret = hi_lsw_protect_param_get(&los_optical_tmp, &los_mac_tmp);
	*los_optical = (uint16_t)los_optical_tmp;
	*los_mac = (uint16_t)los_mac_tmp;
	return ret;
}

hi_uint32 hi_oam_get_eth_port_linkstate(hi_uint32 port_index, hi_uchar8 *link_state)
{
    //参数说明：int port_index 端口号；
    //U8 *link_state;  端口状态UP=0x1, or DOWN=0x0
    //unsigned short port（0~3 FE1~4）
    hi_lsw_phy_link_sta_e em_linksta = HI_LSW_PHY_LINK_UP_E;
    hi_lsw_phy_get_status((hi_lsw_port_e)port_index, &em_linksta);
    *link_state = (hi_uchar8)(!em_linksta);
    return  HI_RET_SUCC;
}

hi_uint32 hi_oam_get_port_default_tag(hi_uchar8 uc_port, hi_ushort16 *pus_tpid, hi_ushort16 *pus_vlan)
{
    return hi_ctcoam_get_port_default_tag(uc_port, pus_tpid, pus_vlan);
}

hi_uint32 hi_oam_set_port_default_tag(hi_uchar8 uc_port, hi_ushort16 us_tpid, hi_ushort16 us_vlan)
{
    return hi_ctcoam_set_port_default_tag(uc_port, us_tpid, us_vlan);
}

//参数说明：port_inex 端口号；
//Work_mode 1 开启流控使能 0 关闭流控使能, 端口pause 控制
hi_uint32 hi_oam_set_flow_ctll(hi_ushort16 port_index, hi_uchar8 work_mode)
{
    return hi_lsw_port_set_pause((hi_lsw_port_e)port_index, work_mode);
}

//参数说明：port_inex 端口号；
//Work_mode 流控使能
hi_uint32 hi_oam_get_flow_ctl_mode(short port_index, unsigned char *work_mode)
{
    return hi_lsw_port_get_pause((hi_lsw_port_e)port_index, work_mode);
}


hi_uint32 hi_oam_set_eth_port_up_flow_ratelimit(hi_uint32 port, hi_uint32 cir, hi_uint32 cbs, hi_uint32 ebs)
{
    hi_lsw_igr_port_car_info_s st_carinfo;

    if ((cbs == cir) && (ebs == cir)) {
        return hi_lsw_car_set_port_car_disable((hi_lsw_port_e)port, HI_LSW_UP_E);
    }

    HI_OS_MEMSET_S(&st_carinfo, sizeof(st_carinfo), 0, sizeof(st_carinfo));
    st_carinfo.ui_cir = cir;
    st_carinfo.ui_cbs = cbs;
    st_carinfo.ui_ebs = ebs;

    return hi_lsw_car_set_port_car((hi_lsw_port_e)port, HI_LSW_UP_E, &st_carinfo);
}

hi_uint32 hi_oam_get_eth_port_up_flow_ratelimit(hi_uint32 port, hi_uint32 *cir, hi_uint32 *cbs, hi_uint32 *ebs)
{
    hi_lsw_igr_port_car_info_s st_carinfo;
    hi_uint32 ui_car_en = HI_FALSE;

    HI_OS_MEMSET_S(&st_carinfo, sizeof(st_carinfo), 0, sizeof(st_carinfo));
    hi_lsw_car_get_port_car((hi_lsw_port_e)port, HI_LSW_UP_E, &ui_car_en, &st_carinfo);

    if (HI_FALSE == ui_car_en) {
        return HI_RET_FAIL;
    }

    *cir = st_carinfo.ui_cir;
    *cbs = st_carinfo.ui_cbs;
    *ebs = st_carinfo.ui_ebs;

    return HI_RET_SUCC;
}

hi_uint32 hi_oam_set_eth_port_ds_flow_ratelimit(hi_uint32 port, hi_uint32 cir, hi_uint32 pir)
{
    hi_lsw_igr_port_car_info_s st_carinfo;

    if (pir == cir) {
        return hi_lsw_car_set_port_car_disable((hi_lsw_port_e)port, HI_LSW_DOWN_E);
    }

    HI_OS_MEMSET_S(&st_carinfo, sizeof(st_carinfo), 0, sizeof(st_carinfo));
    st_carinfo.ui_cir = cir;
    st_carinfo.ui_pir = pir;
    return hi_lsw_car_set_port_car((hi_lsw_port_e)port, HI_LSW_DOWN_E, &st_carinfo);
}

hi_uint32 hi_oam_get_eth_port_ds_flow_ratelimit(hi_uint32 port, hi_uint32 *cir, hi_uint32 *pir)
{
    hi_lsw_igr_port_car_info_s st_carinfo;
    hi_uint32 ui_car_en = HI_FALSE;

    HI_OS_MEMSET_S(&st_carinfo, sizeof(st_carinfo), 0, sizeof(st_carinfo));
    hi_lsw_car_get_port_car((hi_lsw_port_e)port, HI_LSW_DOWN_E, &ui_car_en, &st_carinfo);
    if (HI_FALSE == ui_car_en) {
        return HI_RET_FAIL;
    }

    *cir = st_carinfo.ui_cir;
    *pir = st_carinfo.ui_pir;

    return HI_RET_SUCC;
}

//ETH端口的环路检测功能
//参数说明：port_index端口号
//  hi_lsw_port_get_loopback(hi_lsw_port_e em_port, hi_lsw_port_eth_loopback_e *pem_mode);
/*需要确认flag的定义*/
hi_uint32 hi_oam_set_loopdetect(hi_uint32 port_index, hi_uint32 flag)
{
    //hi_uint32 ui_ret;
    if ((HI_OAM_UNI_LOOP_DEACTIVATE_E == flag) || (HI_OAM_UNI_LOOP_ACTIVATE_E == flag)) {
        hi_lsw_port_set_loopback(port_index, (HI_OAM_UNI_LOOP_STATE_E)flag);
    } else {
        return HI_RET_FAIL;
    }

    g_st_loopback_check[port_index].ui_enable = flag;

    return HI_RET_SUCC;
}

hi_uint32 hi_oam_get_loopdetect(hi_uint32 port_index, hi_uint32 *pui_flag)
{

    if (HI_OAM_UNI_LOOP_ACTIVATE_E == g_st_loopback_check[port_index].ui_enable) {
        *pui_flag = HI_OAM_UNI_LOOP_ACTIVATE_E;
    } else {
        *pui_flag = HI_OAM_UNI_LOOP_DEACTIVATE_E;
    }

    return HI_RET_SUCC;
}

hi_uint32 hi_oam_set_loopdetect_close(hi_uint32 port_index, hi_uint32 flag)
{

    if ((HI_TRUE== flag) || (HI_FALSE== flag)) {
        g_st_loopback_check[port_index].ui_close = flag;
        hi_lsw_vif_ring_check_port(flag);
    } else {
        return HI_RET_FAIL;
    }

    return HI_RET_SUCC;
}

hi_uint32 hi_oam_get_loopdetect_close(hi_uint32 port_index, hi_uint32 *pui_flag)
{

    if (HI_TRUE == g_st_loopback_check[port_index].ui_close) {
        *pui_flag = HI_TRUE;
    } else {
        *pui_flag = HI_FALSE;
    }

    return HI_RET_SUCC;
}

/************************************************************************/
/*UNI PORT VLAN 操作*/
hi_uint32 hi_oam_ctc_set_eth_port_vlan(portAllService *newService)
{
    hi_uint32 ui_ret = HI_RET_SUCC;
    hi_sml_onu_mode_e em_mode;

    HI_IPC_CALL("hi_sml_onu_mode_get", &em_mode);
    if (HI_SML_HGU_E == em_mode) {
        return HI_RET_SUCC;
    }

    HI_OS_MEMCPY_S(&g_st_vlan_cfg[newService->port_index], sizeof(g_st_vlan_cfg[0]), newService, sizeof(*newService));

    hi_ctcoam_dbg_print("\r\n vlanMode multiMode %d %d\n", newService->vlanMode, newService->multiMode);

    switch (newService->vlanMode) {
        case HI_OAM_VLAN_TRANSPARENT_E:
            ui_ret = hi_ctcoam_set_vlan_transparent(newService);
            break;

        case HI_OAM_VLAN_TAG_E:
            ui_ret = hi_ctcoam_set_vlan_tag(newService, g_st_loopback_check[newService->port_index].ui_enable);
            break;

        case HI_OAM_VLAN_TRANSLATION_E:
            ui_ret = hi_ctcoam_set_vlan_translation(newService, g_st_loopback_check[newService->port_index].ui_enable);
            break;

        case HI_OAM_N_TO_ONE_AGGREGATION_E:
            ui_ret = hi_ctcoam_set_vlan_n_to_one_aggregation(newService, g_st_loopback_check[newService->port_index].ui_enable);
            break;

        case HI_OAM_VLAN_TRUNK_E:
            ui_ret = hi_ctcoam_set_vlan_trunk(newService, g_st_loopback_check[newService->port_index].ui_enable);
            break;

        default:
            //hi_os_printf("port[%d] invalid vlan mode %d\n", newService->port_index, newService->vlanMode);
            //return HI_RET_INVALID_PARA;
            break;

    }

    if (ui_ret != HI_RET_SUCC) {
        hi_ctcoam_dbg_print("\r\n ui_ret %d\n", ui_ret);
        return ui_ret;
    }

    if (newService->multiMode == HI_TRUE) {
        RET_CHK(hi_ctcoam_set_vlan_multicast(newService));
    }

    return HI_RET_SUCC;
}

hi_uint32 hi_oam_ctc_clr_vlan_multicast(portAllService *newService)
{
    RET_CHK(hi_ctcoam_clr_vlan_multicast(newService));
    HI_OS_MEMSET_S(g_st_vlan_cfg[newService->port_index].multiVlanInfo, sizeof(g_st_vlan_cfg[newService->port_index].multiVlanInfo), 0,
                   sizeof(g_st_vlan_cfg[newService->port_index].multiVlanInfo));

    return HI_RET_SUCC;
}

hi_uint32 hi_oam_ctc_get_eth_port_vlan(portAllService *port_service)
{
    if ((port_service->port_index < HI_LSW_PORT_UNI_ETH1_E)
        || (port_service->port_index > HI_LSW_PORT_UNI_ETH8_E)) {
        return HI_RET_FAIL;
    }
    HI_OS_MEMCPY_S(port_service, sizeof(*port_service), &g_st_vlan_cfg[port_service->port_index], sizeof(*port_service));

    return HI_RET_SUCC;
}

hi_void hi_oam_ctc_init_eth_port_vlan()
{
    hi_uint32 i;
    HI_OS_MEMSET_S(g_st_vlan_cfg, sizeof(g_st_vlan_cfg), 0, sizeof(g_st_vlan_cfg));

    for (i = HI_LSW_PORT_UNI_ETH1_E; i <= HI_LSW_PORT_UNI_ETH8_E; i++) {
        g_st_vlan_cfg[i].port_index = (hi_ushort16)i;
        /*初始化填入无效值*/
        g_st_vlan_cfg[i].vlanMode = 0x5;
        g_st_vlan_cfg[i].multiVlanStrip = 0x3;
    }
    return;
}

int32_t hi_oam_get_optical_info(struct hi_oam_optical_info *attr)
{
	int32_t ret;
	struct hi_sml_optical_info_s opt_info = {0};

	ret = HI_IPC_CALL("hi_sml_optical_info_get", &opt_info);
	if (ret != HI_RET_SUCC) {
		printf("%s[%d] fail, ret=%d\n", __func__, __LINE__, ret);
		return ret;
	}

	ret = memcpy_s(attr, sizeof(struct hi_oam_optical_info), &opt_info, sizeof(struct hi_sml_optical_info_s));
	if (ret != HI_RET_SUCC) {
		printf("%s[%d] fail, ret=%d\n", __func__, __LINE__, ret);
		return ret;
	}

	return HI_RET_SUCC;
}

/************************************************************************/
//添加端口下的流分类接口：
hi_uint32 hi_oam_set_class_cfg(portClass *p_port_class)
{
    return hi_ctcoam_flow_add(p_port_class);
}

//获取端口下所以流分类：
hi_uint32 hi_oam_get_class_cfg(portClass *p_port_class)
{
    return hi_ctcoam_flow_list(p_port_class);
}

//删除端口下指定的流分类接口：
hi_uint32 hi_oam_del_class_cfg(portClass *p_port_class)
{
    return hi_ctcoam_flow_del(p_port_class);
}

//清除端口下所以流分类接口：
hi_uint32 hi_oam_clear_class_cfg(portClass *p_port_class)
{
    return hi_ctcoam_flow_del_all(p_port_class);
}

hi_uint32 hi_oam_set_multi_ctrl_item(hi_ctcoam_gda_info_s *pst_gdainfo, hi_uchar8 uc_controltype, hi_uchar8 uc_action)
{
    hi_sml_onu_mode_e em_mode;
    HI_IPC_CALL("hi_sml_onu_mode_get", &em_mode);
    if (HI_SML_HGU_E == em_mode) {
        return HI_RET_SUCC;
    }
    switch (uc_action) {
        case 0x00:
            return hi_lsw_mc_list_set(pst_gdainfo, uc_controltype, 0);

        case 0x01:
            return hi_lsw_mc_list_set(pst_gdainfo, uc_controltype, 1);

        case 0x02:
            return hi_lsw_mc_list_clr();

        default:
            return HI_RET_FAIL;
    }
}

hi_int32 hi_oam_get_multi_ctrl_item(hi_uchar8 *puc_changingmsglen,
                                    hi_uchar8 *pv_outmsg,
                                    hi_uchar8 *puc_controltype,
                                    hi_uchar8 *puc_entry_num)
{
    hi_ctcoam_gda_info_s st_gdainfo;
    hi_lsw_port_e em_port;
    hi_uint32 ui_index;
    hi_uchar8 uc_controltype;
    hi_uchar8 uc_entry_num = 0;

    RET_CHK(hi_lsw_mc_list_type_get(&uc_controltype));/*lint !e569*/

    for (em_port = HI_LSW_PORT_UNI_ETH0_E; em_port < HI_LSW_PORT_UNI_ETH8_E; em_port++) {
        ui_index = 0;
        HI_OS_MEMSET_S(&st_gdainfo, sizeof(st_gdainfo), 0, sizeof(st_gdainfo));
        while (HI_RET_SUCC == hi_lsw_mc_list_port_get(ui_index, em_port, &st_gdainfo)) {
            switch (uc_controltype) {
                case 0x0:
                case 0x1:
                case 0x3:
                    HI_OS_MEMCPY_S(pv_outmsg, sizeof(st_gdainfo.st_mac), &st_gdainfo.st_mac, sizeof(st_gdainfo.st_mac));
                    pv_outmsg += sizeof(st_gdainfo.st_mac);
                    *puc_changingmsglen += sizeof(st_gdainfo.st_mac);
                    break;

                case 0x2:
                    HI_OS_MEMCPY_S(pv_outmsg, sizeof(st_gdainfo.st_mac_ipv4), &st_gdainfo.st_mac_ipv4, sizeof(st_gdainfo.st_mac_ipv4));
                    pv_outmsg += sizeof(st_gdainfo.st_mac_ipv4);
                    *puc_changingmsglen += sizeof(st_gdainfo.st_mac_ipv4);
                    break;

                case 0x4:
                    HI_OS_MEMCPY_S(pv_outmsg, sizeof(st_gdainfo.st_ipv6), &st_gdainfo.st_ipv6, sizeof(st_gdainfo.st_ipv6));
                    pv_outmsg += sizeof(st_gdainfo.st_ipv6);
                    *puc_changingmsglen += sizeof(st_gdainfo.st_ipv6);
                    break;

                case 0x5:
                    HI_OS_MEMCPY_S(pv_outmsg, sizeof(st_gdainfo.st_mac_ipv6), &st_gdainfo.st_mac_ipv6, sizeof(st_gdainfo.st_mac_ipv6));
                    pv_outmsg += sizeof(st_gdainfo.st_mac_ipv6);
                    *puc_changingmsglen += sizeof(st_gdainfo.st_mac_ipv6);
                    break;

                default:
                    return (hi_int32)HI_RET_FAIL;
            }

            ui_index++;
            uc_entry_num++;
        }
    }

    *puc_controltype = uc_controltype;
    *puc_entry_num = uc_entry_num;

    return HI_RET_SUCC;
}

/***************************    Add/Del Multicast VLAN（组播）*********************************************/
//参数说明：
//portId：端口号(1~4)
//vlanId：业务vlan(1~4095)

hi_uint32 hi_oam_multicast_tag_strip_set(hi_uint32 portId, hi_uint32 tagOper)
{
    if (tagOper == 0) {
        RET_CHK(hi_ctcoam_set_tag_act((hi_lsw_port_e)portId, HI_LSW_TAG_NO_ACTION_E, HI_LSW_TAG_NO_ACTION_E,
                                      HI_LSW_TAG_NO_ACTION_E, HI_LSW_TAG_NO_ACTION_E));
    } else if (tagOper == 1) {
        RET_CHK(hi_ctcoam_set_tag_act((hi_lsw_port_e)portId, HI_LSW_TAG_DEL_E, HI_LSW_TAG_NO_ACTION_E,
                                      HI_LSW_TAG_NO_ACTION_E, HI_LSW_TAG_NO_ACTION_E));
    }
    return HI_RET_SUCC;
}
hi_uint32 hi_oam_multicast_tag_strip_get(hi_uint32 portId, hi_uchar8 *tagOper)
{
    *tagOper = 0;
    return HI_RET_SUCC;
}
hi_uint32 hi_oam_multicast_tag_translation_add(uint32_t portId, uint32_t mcVid, uint32_t userVid);
//hi_uint32 hi_oam_multicast_tag_translation_get(uint32_t portId, oam_mcast_vlan_translation_entry_t *p_tranlation_entry, uint8_t*num);
hi_uint32 hi_oam_multicast_vlan_translate_entry_clear(uint32_t portId);

/***************************    *********************************************/
hi_uint32 hi_oam_onu_reset()
{
    system("reboot");
    return HI_RET_SUCC;
}

hi_uint32 hi_oam_set_autoneg_restart_autoconfig(hi_int32 port_index)
{
    RET_CHK(hi_lsw_port_restart((hi_lsw_port_e)port_index));
    return HI_RET_NOTSUPPORT;
}
//loid 认证
hi_uint32 hi_oam_get_onu_logic_sn(hi_uchar8 *logicsn, hi_uint32 logicsn_len)
{
    //hi_emac_get_llid_mac(ui_llidindex, pst_onusn->auc_ontmac);
    return HI_RET_SUCC;
}
hi_uint32 hi_oam_get_onu_logic_passwd(hi_uchar8 *logicsn, hi_uint32 logicsn_len)
{
    return HI_RET_SUCC;
}
/***************************    *********************************************/

//onu 状态 获取
//status 0 为为注册，1为注册
hi_uint32 hi_oam_get_onu_status(hi_uchar8 *status)
{
    return HI_RET_SUCC;
}

//onu los 信号获取
// los 为0有光，为1时无光
hi_uint32 hi_oam_get_onu_los(hi_uchar8 *los)
{
    hi_uint32 ui_los;
    hi_uint32 ui_ret;

    ui_ret = hi_emac_get_dn_sta(&ui_los);
    if (HI_RET_SUCC != ui_ret) {
        return HI_RET_FAIL;
    }
    *los = (hi_uchar8)(ui_los & 0xff);
    return HI_RET_SUCC;
}

//设置端口老化时间
//ageTime 0-630
hi_uint32 hi_oam_set_onu_agetime(hi_uint32 ageTime)
{
    return hi_lsw_mac_set_agetime(ageTime);
}

hi_uint32 hi_oam_get_onu_agetime(hi_uint32 *pui_ageTime)
{
    hi_uchar8 uc_enable;
    if (HI_RET_SUCC != hi_lsw_mac_get_ageenable(&uc_enable)) {
        return HI_RET_FAIL;
    }

    if (uc_enable == HI_FALSE) {
        *pui_ageTime = 0;
    } else {
        if (HI_RET_SUCC != hi_lsw_mac_get_agetime(pui_ageTime)) {
            return HI_RET_FAIL;
        }
    }

    return HI_RET_SUCC;
}

//端口隔离使能设置
// enable 1 隔离 0 不隔离
hi_uint32 hi_oam_set_onu_Isolation(hi_uchar8 enable)
{
    // hi_lsw_mac_port_eligibility (hi_uchar8 enable);
    return HI_RET_SUCC;
}
hi_uint32 hi_oam_get_onu_Isolation(hi_uchar8 *isoState)
{
    return HI_RET_SUCC;
}

//端口rstp 使能设置
//enable 1 使能 0 不使能
hi_uint32 hi_oam_set_rstp_enable(hi_uchar8 enable)
{
    if (HI_TRUE== enable) {
        return hi_lsw_port_set_stp(HI_LSW_GLB_ETH_RSTP);
    } else {
        return hi_lsw_port_set_stp(HI_LSW_GLB_ETH_NOSTP_E);
    }
}
hi_uint32 hi_oam_get_rstp_enable(hi_uchar8 *enable)
{
    hi_eth_stp_mode_e em_stp;

    hi_lsw_port_get_stp(&em_stp);

    if (HI_LSW_GLB_ETH_NOSTP_E == em_stp) {
        *enable = HI_FALSE;
    } else {
        *enable = HI_TRUE;
    }

    return HI_RET_SUCC;
}

#if 0

//函数功能:  ONU CPU和内存利用率获取
hi_uint32 hi_oam_get_onu_sysinfo(hi_uint32 *cpu, hi_uint32 *mem);

//ONU 的IP地址分配
hi_uint32 hi_oam_set_onu_ipaddr(hi_uint32 ipAddr, hi_uint32 mask);

hi_uint32 hi_oam_set_port_ingress_ratelimit(hi_uchar8 portNo, PACKET_TYPE_e type, hi_uchar8 enable, hi_uint32 rate)
{
    //hi_lsw_car_set_simple_car(hi_lsw_simple_car_type_e em_type, hi_uchar8 uc_enable, hi_uint32 ui_cir, hi_uint32 ui_cbs);
}
hi_uint32 hi_oam_get_port_ingress_ratelimit(hi_uchar8 portNo, PACKET_TYPE_e type, hi_uchar8 *enable, hi_uint32 *rate)
{
    //hi_lsw_car_get_simple_car(hi_lsw_simple_car_type_e em_type, hi_uchar8 *puc_enable, hi_uint32 *pui_cir, hi_uint32 *pui_cbs);
}
#endif

//mac地址限制
hi_uint32 hi_oam_set_onu_maclimit(hi_uchar8 portNo, hi_uchar8 enable, hi_ushort16  limitNum)
{
    hi_lsw_mac_set_limitcount((hi_lsw_port_e)portNo, limitNum);
    hi_lsw_mac_set_limitenable((hi_lsw_port_e)portNo, enable);
    return HI_RET_SUCC;
}
hi_uint32 hi_oam_get_onu_maclimit(hi_uchar8 portNo, hi_uchar8 *enable, hi_ushort16 *limitNum, hi_ushort16 *curNum)
{
    hi_lsw_mac_get_limitcount((hi_lsw_port_e)portNo, curNum);
    hi_lsw_mac_get_limitenable((hi_lsw_port_e)portNo, enable);
    return HI_RET_SUCC;
}

//端口mac地址表获取
#if 0
hi_uint32 hi_oam_get_onu_macaddr(hi_uchar8 portNo, hi_ushort16 maxMacNum, ARL_ENTRY_st *entry, hi_ushort16  *num)
{
    //hi_lsw_mac_get_port_all_l2mac_index((hi_lsw_port_e)portNo, hi_lsw_mac_l2item_index_s *pui_index);
    return HI_RET_SUCC;
}


/*芯片tm模块映射关系为cos->icos->queue*/
/*classify表中同时匹配几条业务时, 优先级依次为:*/
/*Do not drop > Do not copy to CPU > Drop packets > Copy packets to CPU*/
hi_uint32 hi_oam_add_port_acl_rule(hi_uchar8 portNo, hi_uchar8 action, RULE_CLAUSE_st *clause, hi_uchar8 clauseNum, hi_uint32 *ruleId, hi_uint32 *ruleId_drop_all);
hi_uint32 hi_oam_delete_port_acl_rule(hi_uchar8 portNo, hi_uchar8 action, RULE_CLAUSE_st *clause, hi_uchar8 clauseNum, hi_uint32 *ruleId);


#endif
hi_uint32 hi_oam_get_dba(hi_uint32 ui_llid, hi_ctcoam_dba_capability_s *pst_cap)
{
    return hi_lsw_pon_up_report_table_item_get(ui_llid, pst_cap);
}

hi_uint32 hi_oam_set_dba(hi_uint32 ui_llid, hi_ctcoam_dba_capability_s  *pst_cap)
{
    return hi_lsw_pon_up_report_table_item_set(ui_llid, pst_cap);
}

hi_uint32 hi_oam_get_churning_key(hi_uint32 llid, hi_uchar8 keyIndex, hi_uchar8 *key)
{
    return hi_lsw_pon_llid_churning_key_get(llid, keyIndex, key);
}

hi_uint32 hi_oam_set_churning_key(hi_uint32 llid, hi_uchar8 keyIndex, hi_uchar8 *key)
{
    return hi_lsw_pon_llid_churning_key_set(llid, keyIndex, key);
}


hi_uint32 hi_oam_set_llid_key(hi_uint32 ui_llid, hi_uchar8 uc_keyindex, hi_uchar8 *puc_key)
{
    return hi_lsw_pon_llid_key_set(ui_llid, uc_keyindex, puc_key);
}

hi_uint32 hi_oam_set_llid_encipher_key(hi_uint32 ui_llid, hi_uchar8 uc_keyindex, hi_uchar8 *puc_key)
{
    return hi_lsw_pon_llid_encipherkey_set(ui_llid, uc_keyindex, puc_key);
}

hi_uint32 hi_oam_set_cipher_mode(hi_uint32 ui_llid, hi_uint32 ui_mode)
{
    return hi_lsw_pon_ciphermode_set(ui_llid, ui_mode);
}

hi_uint32 hi_oam_get_eth_statistics_data(hi_uint32 ui_port, hi_oam_monitoring_data_s *pst_data)
{
    hi_lsw_port_e em_port = (hi_lsw_port_e)ui_port;
    hi_lsw_port_statistics_data_s *pst_stat_data = (hi_lsw_port_statistics_data_s *)pst_data;

    if (pst_stat_data == HI_NULL) {
        return HI_RET_FAIL;
    }

    return hi_lsw_pm_get_uni_eth_statistics(em_port, pst_stat_data);
}

hi_uint32 hi_oam_get_pon_statistics_data(hi_oam_monitoring_data_s *pst_data)
{
    hi_lsw_port_statistics_data_s *pst_stat_data = (hi_lsw_port_statistics_data_s *)pst_data;

    if (pst_stat_data == HI_NULL) {
        return HI_RET_FAIL;
    }

    return hi_lsw_pm_get_nni_pon_statistics(pst_stat_data);
}


hi_uint32 hi_oam_get_eth_port_num(hi_void)
{
    return g_st_emac_capability.uc_num_ge_ports + g_st_emac_capability.uc_num_fe_ports;
}

hi_uint32 hi_oam_get_llid_num(hi_void)
{
    return g_st_emac_capability.uc_multillid;
}

/*****************************************************************************
 Prototype    : hi_oam_map_set
 Description  : llid map设置
 Input        : hi_uchar8 uc_llid
 Output       : None
 Return Value :
*****************************************************************************/
hi_void hi_oam_map_set(hi_uchar8 uc_llid)
{
    hi_sml_port_e em_port;
    hi_sml_epon_map_s st_map;
    hi_uchar8 uc_id;

    /* 单llid，所有eth口报文都从llid 0到OLT */
    HI_OS_MEMSET_S(&st_map, sizeof(st_map), 0, sizeof(st_map));

    st_map.ui_mask = HI_SML_MAP_IGR;

    for (uc_id = HI_LLID_INDEX_0; uc_id <= HI_LLID_INDEX_7; uc_id++) {
        for (em_port = HI_SML_ETH0_E; em_port <= HI_SML_ETH7_E; ++em_port) {
            st_map.em_port = em_port;
            st_map.ui_llid = uc_llid;
            if (HI_RET_SUCC != HI_IPC_CALL("hi_sml_epon_map_get", &st_map)) {
                continue;
            }
            (hi_void)HI_IPC_CALL("hi_sml_epon_map_del", &st_map);
        }
    }
    for (em_port = HI_SML_ETH0_E; em_port <= HI_SML_ETH7_E; ++em_port) {
        st_map.em_port = em_port;
        st_map.ui_llid = uc_llid;
        (hi_void)HI_IPC_CALL("hi_sml_epon_map_set", &st_map);
    }
}

hi_uint32 hi_oam_qos_tbl_dump(hi_uchar8 uc_port)
{
    return hi_ctcoam_qos_tbl_dump(uc_port);
}


hi_void hi_oam_api_init()
{
    hi_int32 status;
    hi_ctcoam_flow_marking_tbl_init();
    hi_oam_ctc_init_eth_port_vlan();
    hi_ctcoam_vlan_init();
    HI_OS_MEMCPY_S(g_roguefile, sizeof(g_roguefile), HI_ROGUEFILE, sizeof(HI_ROGUEFILE));
    status = access(g_roguefile, 0);
    if (0 == status) {
        status = 1;
    } else {
        status = 0;
    }
    HI_IPC_CALL("hi_epon_set_rogue_flag", &status);
}
#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
