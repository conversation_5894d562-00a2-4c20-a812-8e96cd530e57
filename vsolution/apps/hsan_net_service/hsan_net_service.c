#include <stdio.h>
#include <string.h>
#include "hi_netapp_common.h"
#include "app_netapp.h"
#include "app_dnsfilter.h"
#include "app_ipfilter.h"
#include "app_macfilter.h"
#include "app_qos.h"
#include "app_urlfilter.h"

#ifdef CONFIG_GATEWAY_SERVICE_NETWORK_WLAN_ISOLATION
#include "app_wlan_isolation.h"
#endif

static int hi_check_luofu_chip(void)
{
    FILE *fp = NULL;
    char buf[32] = {0};
    fp = fopen("/sys/firmware/devicetree/base/compatible", "r");
    if (fp)
    {
        fgets(buf, sizeof(buf), fp);
        fclose(fp);
        if (strstr(buf, "luofu"))
            return 1;
    }
    return 0;
}

int32_t hi_sec_firewall_init(void)
{
    /*Add by fyy for bug#12920*/
    int ret = 0;
    if (hi_check_luofu_chip() == 1) {
        ret = hi_netif_set_dport_flow(21,  0, 6);//ftp
        ret = hi_netif_set_dport_flow(22,  0, 6);//ssh
        ret = hi_netif_set_dport_flow(23,  0, 7);//telnet
        ret = hi_netif_set_dport_flow(69,  0, 6);//tftp
        ret = hi_netif_set_dport_flow(80,  0, 7);//http
        ret = hi_netif_set_dport_flow(443, 0, 7);//https
        if (ret)
            printf("hi_netif_set_dport_flow fail, ret=%d\n", ret);
    }
    else 
    {
        /*for web upgrade*/
        ret = hi_netif_set_dport_flow(21,  0, 6);//ftp
        ret = hi_netif_set_dport_flow(22,  0, 6);//ssh
        ret = hi_netif_set_dport_flow(23,  0, 7);//telnet
        ret = hi_netif_set_dport_flow(69,  0, 6);//tftp
        ret = hi_netif_set_dport_flow(80,  0, 7);//http
        ret = hi_netif_set_dport_flow(443, 0, 7);//https

        /* By zhh */
        ret = hi_netif_set_sport_flow(21,  0, 6);//ftp
        ret = hi_netif_set_sport_flow(22,  0, 6);//ssh
        ret = hi_netif_set_sport_flow(23,  0, 7);//telnet
        ret = hi_netif_set_sport_flow(69,  0, 6);//tftp
        ret = hi_netif_set_sport_flow(80,  0, 7);//http
        ret = hi_netif_set_sport_flow(443, 0, 7);//https

        if (ret)
            printf("hi_netif_set_d/sport_flow fail, ret=%d\n", ret);
    }
    /*End of Add*/

	return HI_RET_SUCC;
}

static void hsan_net_service_ubus_run(void)
{
    static struct ubus_context *app_ubus_ctx = NULL;
    
    uloop_init();
    app_ubus_ctx = ubus_connect(NULL);
    
    ubus_add_uloop(app_ubus_ctx);
#ifdef CONFIG_GATEWAY_SERVICE_NETWORK_QOS
    if (ubus_add_object(app_ubus_ctx, &app_qos_ubus_object))
    {
        return;
    }
#endif

#ifdef CONFIG_GATEWAY_DNSFILTER
    if (ubus_add_object(app_ubus_ctx, &app_dnsfilter_ubus_object))
    {
        return;
    }
#endif

#ifdef CONFIG_GATEWAY_SERVICE_NETWORK_SECURITY
    if (ubus_add_object(app_ubus_ctx, &app_ipfilter_ubus_object))
    {
        return;
    }
    if (ubus_add_object(app_ubus_ctx, &app_urlfilter_ubus_object))
    {
        return;
    }
#endif

#ifdef CONFIG_GATEWAY_SERVICE_NETWORK_NETAPP
    if (ubus_add_object(app_ubus_ctx, &app_netapp_ubus_object))
    {
        return;
    }
#endif

#ifdef CONFIG_GATEWAY_SERVICE_NETWORK_LANHOST
    if (ubus_add_object(app_ubus_ctx, &app_macfilter_ubus_object))
    {
        return;
    }
#endif

    uloop_run();
    
    if (app_ubus_ctx)
    {
        ubus_free(app_ubus_ctx);
    }
    uloop_done();
}

int main(int argc, char *argv[])
{
    int ret=0;

    mib_uci2mib_info_init();
#ifdef CONFIG_GATEWAY_SERVICE_NETWORK_WLAN_ISOLATION
    hi_sec_wlan_isolation_init();
#endif 

#ifdef CONFIG_GATEWAY_SERVICE_NETWORK_QOS
    app_qos_mib_init();
#endif

#ifdef CONFIG_GATEWAY_DNSFILTER
    app_dnsfilter_mib_init();

    hi_sec_dnsfilter_init();
    igdCmDnsFilterModuleInit();
#endif

#ifdef CONFIG_GATEWAY_SERVICE_NETWORK_SECURITY
    app_ipfilter_mib_init();
    app_urlfilter_mib_init();
    
    hi_sec_ipfilter_init();
    igdCmIPFilterModuleInit();
    
    hi_sec_urlfilter_init();
    igdCmSecureModuleInit();
#endif

#ifdef CONFIG_GATEWAY_SERVICE_NETWORK_NETAPP
    app_netapp_mib_init();

    hi_netapp_loopback_init();
    igdCmSysmngLoopbackAttrInit();
#endif

#ifdef CONFIG_GATEWAY_SERVICE_NETWORK_LANHOST
    app_macfilter_mib_init();

    init_macfilter();
    igdCmMacFilterModuleInit();
#endif

    hi_sec_firewall_init();
    
    hsan_net_service_ubus_run();
    
    
    return ret;
}

