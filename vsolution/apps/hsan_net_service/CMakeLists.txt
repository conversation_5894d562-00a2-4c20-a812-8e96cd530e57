include(${CONFIG_CMAKE_DIR}/toolchains/${CONFIG_TOOLCHAIN_LINUX_APP}.cmake)
# 组件名称
set(USERAPP_NAME hsan_net_service)

# 组件类型
set(USERAPP_TARGET_TYPE bin)

# 依赖的源文件
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR} HGW_HSAN_NET_SERVICE_SOURCE_DIR)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/misc HGW_HSAN_NET_SERVICE_MISC_SOURCE_DIR)

set(USERAPP_PRIVATE_SRC
    ${HGW_HSAN_NET_SERVICE_SOURCE_DIR}
    ${HGW_HSAN_NET_SERVICE_MISC_SOURCE_DIR}
)

if(CONFIG_GATEWAY_DNSFILTER STREQUAL ON)
	aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/app_dnsfilter HGW_DNS_FILTER_SOURCE_DIR)
	list(APPEND USERAPP_PRIVATE_SRC  
		${HGW_DNS_FILTER_SOURCE_DIR}
	)
	list(APPEND USERAPP_PRIVATE_DEFINE -DCONFIG_GATEWAY_DNSFILTER)
endif()

if(CONFIG_GATEWAY_SERVICE_NETWORK_SECURITY STREQUAL ON)
	aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/app_ipfilter HGW_IP_FILTER_SOURCE_DIR)
	aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/app_urlfilter HGW_URL_FILTER_SOURCE_DIR)
	list(APPEND USERAPP_PRIVATE_SRC  
		${HGW_IP_FILTER_SOURCE_DIR}
		${HGW_URL_FILTER_SOURCE_DIR}
	)
	list(APPEND USERAPP_PRIVATE_DEFINE -DCONFIG_GATEWAY_SERVICE_NETWORK_SECURITY)
endif()

if(CONFIG_GATEWAY_SERVICE_NETWORK_NETAPP STREQUAL ON)
	aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/app_netapp HGW_NETAPP_SOURCE_DIR)
	list(APPEND USERAPP_PRIVATE_SRC  
		${HGW_NETAPP_SOURCE_DIR}
	)
	list(APPEND USERAPP_PRIVATE_DEFINE -DCONFIG_GATEWAY_SERVICE_NETWORK_NETAPP)
endif()

if(CONFIG_GATEWAY_SERVICE_NETWORK_LANHOST STREQUAL ON)
	aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/app_macfilter HGW_MAC_FILTER_SOURCE_DIR)
	list(APPEND USERAPP_PRIVATE_SRC  
		${HGW_MAC_FILTER_SOURCE_DIR}
	)
	list(APPEND USERAPP_PRIVATE_DEFINE -DCONFIG_GATEWAY_SERVICE_NETWORK_LANHOST)
endif()

if(CONFIG_GATEWAY_SERVICE_NETWORK_QOS STREQUAL ON)
	aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/app_qos HGW_APPQOS_SOURCE_DIR)
	list(APPEND USERAPP_PRIVATE_SRC  
		${HGW_APPQOS_SOURCE_DIR}
	)
	list(APPEND USERAPP_PRIVATE_DEFINE -DCONFIG_GATEWAY_SERVICE_NETWORK_QOS)
endif()

if (CONFIG_GATEWAY_SERVICE_NETWORK_WLAN_ISOLATION STREQUAL ON)
# 访客网络隔离
	aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/app_qos HGW_WLAN_ISOLATION_DIR)
	list(APPEND USERAPP_PRIVATE_SRC  
		${HGW_WLAN_ISOLATION_DIR}
	)
	list(APPEND USERAPP_PRIVATE_DEFINE -DCONFIG_GATEWAY_SERVICE_NETWORK_WLAN_ISOLATION)
endif()

# 依赖的头文件
set(USERAPP_PRIVATE_INC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/app_dnsfilter
    ${CMAKE_CURRENT_SOURCE_DIR}/app_ipfilter
    ${CMAKE_CURRENT_SOURCE_DIR}/app_urlfilter
    ${CMAKE_CURRENT_SOURCE_DIR}/app_netapp
    ${CMAKE_CURRENT_SOURCE_DIR}/app_macfilter
    ${CMAKE_CURRENT_SOURCE_DIR}/app_qos
    ${CMAKE_CURRENT_SOURCE_DIR}/app_wlan_isolation
    ${CMAKE_CURRENT_SOURCE_DIR}/misc
    ${HGW_SERVICE_DIR}/dms/board/include
    ${HGW_SERVICE_DIR}/dms/sysinfo/include
    ${HGW_SERVICE_DIR}/network/common/include
    ${HGW_SERVICE_DIR}/network/lan/include
    ${HGW_SERVICE_DIR}/network/wan/include
    ${HGW_SERVICE_DIR}/network/qos/u_space/include
    ${HGW_SERVICE_DIR}/network/security/include
    ${HGW_SERVICE_DIR}/network/security/source
    ${HGW_SERVICE_DIR}/network/lanhostv2/include
    ${HGW_SERVICE_DIR}/network/lanhostv2/source
    ${HGW_SERVICE_DIR}/network/netapp/source
    ${HGW_SERVICE_DIR}/network/netapp/include
    ${HGW_SERVICE_DIR}/network/emu/u_space/include
    ${HGW_SERVICE_DIR}/network/easymesh/include
    ${HGW_FWK_DIR}/util/include
    ${HGW_FWK_DIR}/ipc/include
    ${HGW_FWK_DIR}/notifier/include
    ${HGW_FWK_DIR}/timer/include
    ${HGW_CML_DIR}/odl/include
    ${HGW_CML_DIR}/odlapi/include
    ${CONFIG_OPENSRC_INCLUDE_DIR}/libubus
    ${HGW_U_INCLUDE}
)

# 依赖的库
set(USERAPP_PRIVATE_LIB
    hi_owal_ssf hi_ioreactor openwrt_lib 
     hi_basic hi_ipc hi_util
    hi_dyingmsg ssl crypto hi_net_com
    hi_qos hi_sec hi_lanhost hi_netapp
)

# 自定义编译选项
set(USERAPP_PRIVATE_COMPILE
    -Werror
    ${HGW_COMMON_CFLAGS}
)

if(CONFIG_GATEWAY_SERVICE_NETWORK_QOS STREQUAL ON)
	list(APPEND USERAPP_PRIVATE_DEFINE -DCONFIG_WITH_DOWNLINK_QOS)
endif()

list(APPEND USERAPP_PRIVATE_COMPILE  -g)


build_app_feature()
