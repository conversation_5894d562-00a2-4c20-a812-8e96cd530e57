#include <unistd.h>
#include <string.h>
#include <netinet/in.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <features.h>
#include <stdio.h>
#include <stdlib.h>
#include <asm/types.h>
#include <linux/if_packet.h>
#include <linux/if_ether.h>
#include <errno.h>
#include <pthread.h>
#include <sys/ioctl.h>
#include <linux/if.h> 
#include <sys/stat.h>
#include <sys/wait.h>
#include <fcntl.h>
#include <linux/ip.h>
#include <linux/udp.h>
#include <sys/time.h>
#include <ifaddrs.h>
#include <dirent.h>

#include "netif_api.h"
#include "vsuci.h"

int get_vsuci_int32(const char *pkg, const char *secion, const char *option)
{
    char val[32]={0};
    int intval=0;
    int cnt=3;

    while(cnt-- >0){
        intval = vsuci_get(pkg, secion, option, val, 32);
        if(0 == intval){
            break;
        }
        sleep(1);
    }

    intval = atoi(val);

    return intval;
}

int get_uplink_cfg(igd_uplink_cfg *uplink_cfg)
{
    if(NULL == uplink_cfg){
        return 0;
    }
    
    memset(uplink_cfg, 0, sizeof(igd_uplink_cfg));
    uplink_cfg->cfg_mode = get_vsuci_int32("auto_wan", "auto_wan", "cfg_mode");
    uplink_cfg->cfg_linkid = get_vsuci_int32("auto_wan", "auto_wan", "cfg_linkid");
    uplink_cfg->detect_en = get_vsuci_int32("auto_wan", "auto_wan", "detect_en");
    uplink_cfg->detected_linkid = get_vsuci_int32("auto_wan", "auto_wan", "detected_linkid");
    uplink_cfg->current_uplinkid = get_vsuci_int32("auto_wan", "auto_wan", "current_uplinkid");
    uplink_cfg->current_uplink_type = get_vsuci_int32("auto_wan", "auto_wan", "current_uplink_type");

    return 1;
}

int update_uplink_cfg(igd_uplink_cfg *uplink_cfg)
{
    char val[32]={0};

    memset(val, 0, 32);
    snprintf(val, 32, "%d", uplink_cfg->cfg_mode);
    vsuci_set("auto_wan", "auto_wan", "cfg_mode", val);
    
    memset(val, 0, 32);
    snprintf(val, 32, "%d", uplink_cfg->cfg_linkid);
    vsuci_set("auto_wan", "auto_wan", "cfg_linkid", val);
    
    memset(val, 0, 32);
    snprintf(val, 32, "%d", uplink_cfg->detect_en);
    vsuci_set("auto_wan", "auto_wan", "detect_en", val);
    
    memset(val, 0, 32);
    snprintf(val, 32, "%d", uplink_cfg->detected_linkid);
    vsuci_set("auto_wan", "auto_wan", "detected_linkid", val);
    
    memset(val, 0, 32);
    snprintf(val, 32, "%d", uplink_cfg->current_uplinkid);
    vsuci_set("auto_wan", "auto_wan", "current_uplinkid", val);
    
    memset(val, 0, 32);
    snprintf(val, 32, "%d", uplink_cfg->current_uplink_type);
    vsuci_set("auto_wan", "auto_wan", "current_uplink_type", val);
    
    return 1;
}

int os_execcmd(char *pc_command)
{
    int  pid = 0;
    int  status = 0;
    int *argv[] = { "sh", "-c", pc_command, NULL };

    if (pc_command == NULL) {
        return -1;
    }

    pid = fork();
    if (pid < 0) {
        return -1;
    } else if (pid == 0) {
         //子进程执行分支
        execv("/bin/sh", argv);
        _exit(127);
    }

    /* wait for child process return */
    while (waitpid(pid, &status, 0) < 0) {
        if (errno != (unsigned int)EINTR) {
            return -1;
        }
    }
    
    return WIFEXITED(status) ? (0) : (-1);
    
}

int get_port_linkstatus(const char *ifname) 
{
    char path[256];
    char buff[256] = {0};
    
    snprintf(path, sizeof(path), "/sys/class/net/%s/carrier", ifname);
    int fd = open(path, O_RDONLY);
    if (fd < 0){
        return -1;
    }

    ssize_t n = read(fd, buff, sizeof(buff));
    close(fd);
    if (n <= 0){
        return -1;
    }

    return atoi(buff)>0?1:0;
}

static int is_interface_in_bridge(char *ifname, char *brname)
{
    DIR *dir;
    struct dirent *entry;
    char bridge_dir[128] = {0};

    snprintf(bridge_dir, 128, "/sys/class/net/%s/brif/", brname);

    dir = opendir(bridge_dir);
    if (!dir) {
        perror("Failed to open bridge directory");
        return 0;
    }

    while ((entry = readdir(dir)) != NULL) {
        if (entry->d_type == DT_LNK && strcmp(entry->d_name, ifname) == 0) {
            closedir(dir);
            return 1; 
        }
    }

    closedir(dir);
    return 0;
}


void del_interface_from_bridge(char *ifname, char *br_ifname)
{
    char cmdstr[256]={0};
    
    if(ifname==NULL || br_ifname==NULL)
        return;

    if(!is_interface_in_bridge(ifname, br_ifname)){
        printf("in bridge: ifname:%s, brifname:%s\n", ifname, br_ifname);
        return;
    }

    snprintf(cmdstr, 256, "ifconfig %s down", ifname);
    os_execcmd(cmdstr);
    
    snprintf(cmdstr, 256, "brctl delif  %s %s", br_ifname, ifname);
    os_execcmd(cmdstr);
    
    snprintf(cmdstr, 256, "ifconfig %s up", ifname);
    os_execcmd(cmdstr);
    
    return;
}

void add_interface_to_bridge(char *ifname, char *br_ifname)
{
    char cmdstr[256]={0};
    
    if(ifname==NULL || br_ifname==NULL)
        return;

    if(is_interface_in_bridge(ifname, br_ifname)){
        printf("not in bridge: ifname:%s, brifname:%s\n", ifname, br_ifname);
        return;
    }

    snprintf(cmdstr, 256, "ifconfig %s down", ifname);
    os_execcmd(cmdstr);
    
    snprintf(cmdstr, 256, "brctl addif  %s %s", br_ifname, ifname);
    os_execcmd(cmdstr);
    
    snprintf(cmdstr, 256, "ifconfig %s up", ifname);
    os_execcmd(cmdstr);

    return;
}

