/*
Bohannon:
auto detect wan program
based on hisi openwrt
*/
#include <stdio.h>
#include <string.h>
#include <sys/ioctl.h>
#include <pthread.h>
#include <sys/prctl.h>
#include <arpa/inet.h>

#include "auto_wan.h"
#include "netif_api.h"
#include "uci.h"
#include "auto_wan_util.h"

#ifndef FIELD_NAME_STR
#define FIELD_NAME_STR(field) #field
#endif

#define NAME_WAN_PREFIX "wan"

static int g_uplink_cfg_exist = 0;
static uint32_t g_detect_step = 0;
static uint32_t g_eth_wan_up = 0;

static pthread_t g_reportThread;

static int modify_wan_rootdev(char *wan_sname, char *wan_rootdev)
{
    char wan_vif[32]={0};
    char tmp_if[32]={0};
    int totalNum=0, i;
    struct uci_context *context = NULL;
    struct uci_element *element = NULL;
    struct uci_ptr ptr;
    char uci_option_attr[128]={0};
    int found=0;
    
    if (!(context = uci_alloc_context()))
    {
        return found;
    }

    if (UCI_OK == vsuci_get("network", wan_sname, "device", wan_vif, 32)){
        vsuci_total("network", "device", &totalNum);
        for(i=0; i<totalNum; i++){
            snprintf(uci_option_attr, 128, "network.@device[%d].name", i);
            if (uci_lookup_ptr(context, &ptr, uci_option_attr, true) != UCI_OK) {
                continue;
            }
            element = ptr.last;
            if (!(ptr.flags & UCI_LOOKUP_COMPLETE) || (element->type != UCI_TYPE_OPTION)) {
                continue;
            }
            if(ptr.o->type == UCI_TYPE_STRING && !strcmp(ptr.o->v.string, wan_vif)){
                memset(uci_option_attr, 0, 128);
                snprintf(uci_option_attr, 128, "@device[%d]", i);
                vsuci_get("network", uci_option_attr, "ifname", tmp_if, 32);
                if(strcmp(tmp_if, wan_rootdev)){
                    vsuci_set("network", uci_option_attr, "ifname", wan_rootdev);
                    uci_commit(context, &ptr.p, false);
                    found=1;
                }
                break;
            }
        }
    }
    
    uci_free_context(context);
    return found;
}

/*
get first network wan* interface section name 
*/
static int find_1st_network_wan(char *wan_sname)
{
    struct uci_context *context = NULL;
    struct uci_element *element = NULL;
    struct uci_ptr ptr;
    char uci_option_attr[128]={0};
    int totalNum=0, i;
    int found=0;
    
    if (!(context = uci_alloc_context()))
    {
        return UCI_ERR_MEM;
    }
    vsuci_total("network", "interface", &totalNum);
    for(i=0; i<totalNum; i++){
        snprintf(uci_option_attr, 128, "network.@interface[%d]", i);
        if (uci_lookup_ptr(context, &ptr, uci_option_attr, true) != UCI_OK) {
            continue;
        }
        element = ptr.last;
        if (!(ptr.flags & UCI_LOOKUP_COMPLETE) || (element->type != UCI_TYPE_SECTION)) {
            continue;
        }
        if(!strncasecmp(ptr.s->e.name, NAME_WAN_PREFIX, 3)){
            strncpy(wan_sname, ptr.s->e.name, 128);
            found=1;
            break;
        }
    }
    
    uci_free_context(context);

    return found;
}

enum hi_netif_detect_type get_wan_proto(struct hi_netif_detect_info *det_info)
{
    char proto[32]={0};
    char ipaddr[64]={0};
    char gwip[64]={0};
    char wan_sname[128]={0};

    if(NULL == det_info)
    {
        return HI_DETECT_NONE;
    }
    if(find_1st_network_wan(wan_sname) !=1 ){
        return HI_DETECT_NONE;
    }
    
    if (0 != vsuci_get("network", wan_sname, "proto", proto, 32)){
        return HI_DETECT_NONE; //no wan ,not router
    }
    
    if(!strncmp(proto, "dhcp", strlen("dhcp"))){
        return HI_DETECT_DHCP;
    }
    if(!strncmp(proto, "pppoe", strlen("pppoe"))){
        return HI_DETECT_PPPOE;
    }
    if(!strncmp(proto, "static", strlen("static"))){
        struct in_addr addr;
        vsuci_get("network", wan_sname, "ipaddr", ipaddr, 64);
        vsuci_get("network", wan_sname, "gateway", gwip, 64);
        if (inet_pton(AF_INET, ipaddr, &(addr.s_addr)) <= 0) {
            printf("ipaddr error!\n");
            return HI_DETECT_NONE;
        }
        det_info->sip = addr.s_addr;
        
        if (inet_pton(AF_INET, gwip, &(addr.s_addr)) <= 0) {
            printf("gwip error!\n");
            return HI_DETECT_NONE;
        }
        det_info->target_ip = addr.s_addr;
        
        return HI_DETECT_STATIC;
    }
    /* Bohannon for mission#00043864 */
    if(!strncmp(proto, "none", strlen("none"))){
        return HI_DETECT_BRIDGE;
    }
    
    return HI_DETECT_NONE;
}

static int check_intf_in_brlan(char * ifname)
{
    struct uci_context *context = NULL;
    struct uci_element *element = NULL;
    struct uci_element *e = NULL;
    struct uci_option *o=NULL;
    struct uci_ptr ptr;
    int found=0;
    char uci_option_attr[128]={0};
    
    if (!(context = uci_alloc_context()))
    {
        return found;
    }
    
    snprintf(uci_option_attr, 128, "network.@device[0].ports");
    if (uci_lookup_ptr(context, &ptr, uci_option_attr, true) != UCI_OK) {
        uci_free_context(context);
        return found;
    }
    element = ptr.last;
    if (!(ptr.flags & UCI_LOOKUP_COMPLETE) || (element->type != UCI_TYPE_OPTION)) {
        uci_free_context(context);
        return found;
    }
    
    o=ptr.o;
    if(o->type == UCI_TYPE_LIST){
        uci_foreach_element(&o->v.list, e)
        {
            if (!strncmp(e->name, ifname, strlen(ifname)))
            {
                found=1;
                break;
            }
        }
    }
    
    uci_free_context(context);
    return found;
}

/* bring all eth ports to bridge*/
static int select_none_eth_uplink(int flag)
{
    int i;
    char ifname[32]={0};
    char wan_sname[128]={0};
    int cfg_changed=0;
    
    for(i=0; i<HI_NETIF_DETECT_LINK_MAX; i++){
        snprintf(ifname, 32, IF_NAME_PFX"%d", i);
        /* update br-lan interfaces, add ifname from br-lan */
        if(0 ==check_intf_in_brlan(ifname)){
            vsuci_add_list("network", "@device[0]", "ports", ifname);
            cfg_changed=1;
        }
    }
    
    if(find_1st_network_wan(wan_sname) !=1 ){
        strncpy(wan_sname, "wan", 3);
    }
    
    memset(ifname, 0, 32);
    /* update wan if name to ifname */
    cfg_changed |= modify_wan_rootdev(wan_sname, ifname);
    
    /* apply changes */
    printf("cfg_changed=%d\n", cfg_changed);
    if(cfg_changed || flag){
        os_execcmd(CMD_RELOAD_NETWORK);
        os_execcmd(CMD_RELOAD_FIREWALL);
        os_execcmd(CMD_RELOAD_MINIUPNPD);
        os_execcmd(CMD_RELOAD_OMCPROXY);
    }
    
    return 0;
}

static update_qos_basic(const char *rootif, const char *wanif, int vid)
{
    char ubsCmd[512]={0};
    char aucMode[32]={0};
    int ucQosEnable, ucPlan, ucEnableForceWeight,ucEnableDscpMark;
    int ucEnable8021p, ucEnableTcMark, ulBandwidth;
    int ulTcpSession;
    char uci_option_attr[]="@IGD_QOS_BASIC_ATTR_TAB[0]";
    
    snprintf(ubsCmd, 512, "ubus call app_qos intf_init '{\"wan_root\":\"%s\", \"wan_name\":\"%s\"}'", rootif, wanif);
    os_execcmd(ubsCmd);
        
    vsuci_get("app_qos", uci_option_attr, FIELD_NAME_STR(aucMode), aucMode, 32);
    ucQosEnable = vsuci_get_int32("app_qos", uci_option_attr, FIELD_NAME_STR(ucQosEnable));
    ucPlan = vsuci_get_int32("app_qos", uci_option_attr, FIELD_NAME_STR(ucPlan));
    ucEnableForceWeight = vsuci_get_int32("app_qos", uci_option_attr, FIELD_NAME_STR(ucEnableForceWeight));
    ucEnableDscpMark = vsuci_get_int32("app_qos", uci_option_attr, FIELD_NAME_STR(ucEnableDscpMark));
    ucEnable8021p = vsuci_get_int32("app_qos", uci_option_attr, FIELD_NAME_STR(ucEnable8021p));
    ucEnableTcMark = vsuci_get_int32("app_qos", uci_option_attr, FIELD_NAME_STR(ucEnableTcMark));
    ulBandwidth = vsuci_get_int32("app_qos", uci_option_attr, FIELD_NAME_STR(ulBandwidth));
    ulTcpSession = vsuci_get_int32("app_qos", uci_option_attr, FIELD_NAME_STR(ulTcpSession));

    memset(ubsCmd, 0, 512);
    snprintf(ubsCmd, 512, "ubus call app_qos qos_basic_attr_set "
                  "'{\"aucMode\":\"%s\",\"ucQosEnable\":%d,\"ucPlan\":%d, \"ucEnableForceWeight\":%d, "
                    "\"ucEnableDscpMark\":%d,\"ucEnable8021p\":%d,\"ucEnableTcMark\":%d, \"ulBandwidth\":%d, "
                    "\"ulTcpSession\":%d}'", \
            aucMode, ucQosEnable, ucPlan, ucEnableForceWeight, \
            ucEnableDscpMark, ucEnable8021p, ucEnableTcMark, ulBandwidth, \
            ulTcpSession);
    os_execcmd(ubsCmd);
    return 0;
}

/* 
idx < 0: add all ethx to br-lan_info
0<= idx && idx <HI_NETIF_DETECT_LINK_MAX: remove ethx from br-lan, add other eths to br-lan, set ethx as wan
*/
static int select_eth_uplink(int idx)
{
    int i;
    char ifname[32]={0};
    char wan_sname[128]={0};
    int cfg_changed=0;

    if(idx >= HI_NETIF_DETECT_LINK_MAX){
        return 0;
    }
    
    for(i=0; i<HI_NETIF_DETECT_LINK_MAX; i++){
        snprintf(ifname, 32, IF_NAME_PFX"%d", i);
        if(check_intf_in_brlan(ifname) && (i == idx)){
            vsuci_del_list("network", "@device[0]", "ports", ifname);
            cfg_changed=1;
        }
        if((0==check_intf_in_brlan(ifname)) && (i != idx)){
            vsuci_add_list("network", "@device[0]", "ports", ifname);
            cfg_changed=1;
        }
    }
    
    /* update wan if name to ifname */
    if(idx < 0){
        memset(ifname, 0, 32);
    }else{
        snprintf(ifname, 32, IF_NAME_PFX"%d", idx);
    }

    if(find_1st_network_wan(wan_sname) !=1 ){
        strncpy(wan_sname, "wan_1", 3);
    }
    
    cfg_changed |= modify_wan_rootdev(wan_sname, ifname);
    
    /* apply changes */
    if(cfg_changed){
        os_execcmd(CMD_RELOAD_NETWORK);
        os_execcmd(CMD_RELOAD_FIREWALL);
        os_execcmd(CMD_RELOAD_MINIUPNPD);
        os_execcmd(CMD_RELOAD_OMCPROXY);
        /*  auto wan update qos settings
            setup qos wan netif, apply qos basic settings
        */
        update_qos_basic(ifname, ifname, 0);
    }

    
    printf("setup %s as wan\n",ifname);
    
    return 0;
}

int32_t igd_cm_uplink_cfg_init(void)
{
    uint8_t init_linkid;
    igd_uplink_cfg uplink_cfg = { 0 };
    
    get_uplink_cfg(&uplink_cfg);

    g_uplink_cfg_exist = 1;
    init_linkid = (uplink_cfg.cfg_mode == IGD_UPLINK_CFG_STATIC) ? \
                            (uplink_cfg.cfg_linkid) : (uplink_cfg.current_uplinkid);

    if (uplink_cfg.cfg_mode != IGD_UPLINK_CFG_DISABLE) {
        uplink_cfg.current_uplinkid = init_linkid;
        if (init_linkid >= CM_LINK_ETH1 && init_linkid <= CM_LINK_ETH4){
            select_eth_uplink(init_linkid - CM_LINK_ETH1);
        }
    }

    if (uplink_cfg.cfg_mode == IGD_UPLINK_CFG_AUTO_DETECT) {
         netif_wan_detect_enable(uplink_cfg.detect_en);
    } else {
        if(uplink_cfg.detect_en != 0){
            uplink_cfg.detect_en=0;
            update_uplink_cfg(&uplink_cfg);
        }
    }

     return 0;
}

void uplink_detect_step_refresh(void)
{
    g_detect_step = 0;
}

static void uplink_restart_check(igd_uplink_cfg *cfg)
{
    int32_t i;
    uint32_t up = 0, now_link_bitmap;
    static uint32_t eth_link_bitmap = 0;

    if (cfg->cfg_mode == IGD_UPLINK_CFG_STATIC || cfg->cfg_mode == IGD_UPLINK_CFG_DISABLE) {
        /*Bohannon: skip check link status when linkid is none */
        if(cfg->cfg_linkid != CM_LINK_NONE){
            netif_get_eth_linkstatus_by_idx(cfg->cfg_linkid - CM_LINK_ETH1, &g_eth_wan_up);
        }
        return;
    }

    now_link_bitmap = 0;
    for (i = CM_LINK_ETH1; i <= CM_LINK_ETH4; i++) {
        netif_get_eth_linkstatus_by_idx(i - CM_LINK_ETH1, &up);
        if (up == HI_TRUE)
            now_link_bitmap |= (0x1 << (i - CM_LINK_ETH1));
    }
    if (now_link_bitmap != eth_link_bitmap) {
       uplink_detect_step_refresh();
       if (cfg->detect_en != HI_TRUE) {
           cfg->detect_en = HI_TRUE;
           cfg->detected_linkid = CM_LINK_NONE;
           netif_wan_detect_enable(HI_TRUE);
           update_uplink_cfg(cfg);
       }
       eth_link_bitmap = now_link_bitmap;
    }
}

static uint32_t get_detect_interval(void)
{
    uint32_t interval_tab[] = {3, 3, 3, 3, 3, 15, 15, 15, 30, 30, 60 };
    uint32_t step = g_detect_step<(ARRAY_SIZE(interval_tab) - 1)?g_detect_step:(ARRAY_SIZE(interval_tab) - 1);
    return interval_tab[step];
}

static int32_t uplink_get_detect_linkid(void)
{
    int32_t link_idx;
    for (link_idx = 0; link_idx < HI_NETIF_DETECT_LINK_MAX; link_idx++) {
        if (netif_get_wan_detect_state(link_idx) == HI_DETECT_STATE_SUCC)
            return link_idx + CM_LINK_ETH1;
    }
    return CM_LINK_NONE;
}

static void uplink_detect_result_check(igd_uplink_cfg *mib_cfg)
{
    int32_t detect_linkid = uplink_get_detect_linkid();
    igd_uplink_cfg cur_cfg={0};

    get_uplink_cfg(&cur_cfg);
    if (detect_linkid == CM_LINK_NONE) {
        if (g_detect_step >= HI_UPLINK_DET_FAIL_STEP){
            g_eth_wan_up = HI_FALSE;
            mib_cfg->current_uplinkid=-1;
            update_uplink_cfg(mib_cfg);
            select_none_eth_uplink(0);
        }
        return;
    }
    
    mib_cfg->detect_en = HI_FALSE;
    netif_wan_detect_enable(mib_cfg->detect_en);
    mib_cfg->detected_linkid = detect_linkid;
      if (mib_cfg->detected_linkid != mib_cfg->current_uplinkid) {
        mib_cfg->current_uplinkid = mib_cfg->detected_linkid;
        update_uplink_cfg(mib_cfg);
        select_eth_uplink(detect_linkid - CM_LINK_ETH1);
    } else {
        if(cur_cfg.detect_en != mib_cfg->detect_en || \
         cur_cfg.detected_linkid != mib_cfg->detected_linkid || \
         cur_cfg.current_uplinkid != mib_cfg->current_uplinkid){
              update_uplink_cfg(mib_cfg);
        }
    }
    g_eth_wan_up = HI_TRUE;
}

static void uplink_detect_proc(uint32_t t)
{
    igd_uplink_cfg uplink_cfg = { 0 };
    enum hi_netif_detect_type detype=HI_DETECT_NONE;
    struct hi_netif_detect_info det_info = { 0 };

    get_uplink_cfg(&uplink_cfg);
    uplink_restart_check(&uplink_cfg);
    if (uplink_cfg.detect_en == HI_FALSE){
        return;
    }
    
    detype=get_wan_proto(&det_info);
    /* Bohannon for mission#00043864 */
    if( detype == HI_DETECT_NONE || detype == HI_DETECT_BRIDGE)
    {
        return;
    }
    det_info.type = detype;
    if ((t % get_detect_interval()) == 0) {
        netif_set_wan_detect_info(det_info.type, det_info.target_ip, det_info.sip);
        netif_wan_detect_once();
        g_detect_step += 1;
    }

    uplink_detect_result_check(&uplink_cfg);
}

static void *igdCmUplinkReport(void *arg)
{
    uint32_t t = 1;
    
    prctl(PR_SET_NAME, (unsigned long)"uplinkReport", 0, 0, 0);
    
    while (1) {
        if (g_uplink_cfg_exist == 1)
            uplink_detect_proc(t);
        sleep(1);
        t++;
    }
    
    return 0;
}

static int32_t igd_cm_uplink_stat_init(void)
{
    if (pthread_create(&g_reportThread, NULL, igdCmUplinkReport, NULL) != 0) {
        printf("Create thread uplink report failed.\r\n");
        return IGD_CM_OPERATE_FAIL;
    }
    
     return IGD_CM_OPERATE_SUCCESS;
}


int igdCmUplinkInit(void)
{
    int ret;
    struct hi_netif_detect_info det_info = { 0 };
    
    if (get_wan_proto(&det_info) != HI_DETECT_NONE) {
        ret = igd_cm_uplink_cfg_init();
        if (ret != IGD_CM_OPERATE_SUCCESS)
            printf("uplink cfig init fail\n");
    }else{
        select_none_eth_uplink(1);
    }
    ret = igd_cm_uplink_stat_init();
    if (ret != IGD_CM_OPERATE_SUCCESS)
        printf("uplink stats init fail\n");

    return IGD_CM_OPERATE_SUCCESS;
}

int main()
{

    igdCmUplinkInit();
    
    while(1)
    {
        sleep(10);
    }
    
    return 0;
}
