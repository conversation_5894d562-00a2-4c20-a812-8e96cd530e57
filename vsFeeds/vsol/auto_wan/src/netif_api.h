#ifndef HI_NETIF_WAN_DETECT_H
#define HI_NETIF_WAN_DETECT_H

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#define __PACK__ __attribute__ ((packed))

#ifndef int32_t
#define int32_t int
#endif

#ifndef uint32_t
#define uint32_t unsigned int
#endif

#ifndef uint8_t
#define uint8_t unsigned char
#endif

#define HI_NETIF_DETECT_LINK_MAX 4

#define HI_RET_FAIL -1
#define HI_RET_SUCC 0

#define HI_FALSE 0
#define HI_TRUE 1

#define IGD_CM_OPERATE_SUCCESS (0)
#define IGD_CM_OPERATE_FAIL (-1)

#define HI_UPLINK_STATS_PERIOD 3
#define HI_UPLINK_DET_FAIL_STEP 3

#define WAN_DETECT_DEV "/dev/wan_detect"
#define IF_NAME_PFX "eth"
#define BR_NAME "br-lan"

enum{
    IOC_S_WAN_ENABLE=0x1000,
    IOC_S_WAN_DET_INFO,
    IOC_G_WAN_DET_STATE,
    IOC_S_WAN_DET_ONCE,
};

enum hi_netif_detect_type {
    HI_DETECT_NONE=-1,
    HI_DETECT_DHCP,
    HI_DETECT_PPPOE,
    HI_DETECT_STATIC,
    HI_DETECT_BRIDGE,
};

enum hi_netif_detect_state {
    HI_DETECT_STATE_INIT,
    HI_DETECT_STATE_WAITING,
    HI_DETECT_STATE_SUCC,
};
    
/* IGD_UPLINK_CFG_TAB */
enum igd_cm_eth_link_id {
    CM_LINK_NONE,
    CM_LINK_ETH1 = 1,
    CM_LINK_ETH2,
    CM_LINK_ETH3,
    CM_LINK_ETH4,
    CM_LINK_MAX,
};

typedef struct {
#define IGD_UPLINK_CFG_DISABLE 0
#define IGD_UPLINK_CFG_STATIC 1
#define IGD_UPLINK_CFG_AUTO_DETECT 2
    int cfg_mode;
    int cfg_linkid;

    int detect_en;
    int detected_linkid;

    int current_uplinkid;
    int current_uplink_type;
}__PACK__ igd_uplink_cfg;

struct hi_netif_detect_info {
    enum hi_netif_detect_type type;
    unsigned int target_ip;
    unsigned int sip;
}__PACK__;

struct hi_netif_detect_result {
    int link_idx;
    enum hi_netif_detect_state state;
}__PACK__;


int netif_get_eth_linkstatus_by_idx(int idx, int *linkstatus);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif


