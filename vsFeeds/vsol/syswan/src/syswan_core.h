#ifndef _SYSWAN_CORE_H_
#define _SYSWAN_CORE_H_

// VALUE
#define WAN_MODE_BRIDGE 0
#define WAN_MODE_ROUTE 1
#define IP_PROTOCOL_V4 1
#define IP_PROTOCOL_V6 2
#define IP_PROTOCOL_V4_V6 3
#define IP_MODE_DHCP 0
#define IP_MODE_STATIC 1
#define IP_MODE_PPPOE 2
#define NAPT_MODE_DISABLE 0
#define NAPT_MODE_ENABLE 1
#define VLAN_MODE_UNTAG 0
#define VLAN_MODE_TAG 1
#define DHCP_MODE_ENABLE 0
#define DHCP_MODE_DISABLE 1
#define DNS_MODE_MANUAL 0
#define DNS_MODE_AUTO 1
#define PD_MODE_DISABLE 0
#define PD_MODE_ENABLE 1
#define DIAL_MODE_AUTO 0
#define DIAL_MODE_DEMAND 1
#define DS_LITE_DISABLE 0
#define DS_LITE_ENABLE 1
#define NAPT_DISABLE 0
#define NAPT_ENABLE 1
#define WAN_STATUS_DOWN 0
#define WAN_STATUS_UP 1
#define MTU_DEFAULT 1500

// CMD
#ifdef SYSWAN_PLATFORM_AIROHA_ONU
#define CMD_MVLAN_CLEAR "/userfs/bin/xponigmpcmd clearAll"
#define CMD_RELOAD_CALL "/etc/init.d/call reload"
#endif
#ifdef SYSWAN_FIREWALL_XTABLES
#define CMD_BRIDGE_ISOLATE_CLEAR "/etc/syswan/rule_wan_isolate.sh -r"
#endif
#define CMD_HOTPLUG_CALL "ACTION=\"ifupdate\" INTERFACE=\"wan_%u\" hotplug-call iface &"
#define CMD_RELOAD_FIREWALL "/etc/init.d/firewall reload"
#define CMD_RELOAD_MINIUPNPD "/etc/init.d/miniupnpd reload"
#define CMD_RELOAD_NETWORK "/etc/init.d/network reload"
#define CMD_RELOAD_OMCPROXY "/etc/init.d/omcproxy reload"
#define CMD_RELOAD_AUTOWAN "/etc/init.d/auto_wan reload"

#define CMD_SHEBANG "#!/bin/sh"

// NAME
#ifdef SYSWAN_PLATFORM_AIROHA_ONU
#define NAME_LAN_PREFIX "eth0."
#define NAME_WAN pon_adapt
#define NAME_WAN_DEFAULT "pon"
#define NAME_WAN_PREFIX pon_adapt_prefix
#define NAME_WLAN_2G_PREFIX "ra"
#define NAME_WLAN_5G_PREFIX "rai"
#elif defined(SYSWAN_PLATFORM_HISILICON_ONU)
#define NAME_LAN_PREFIX "eth"
#define NAME_WAN_ROOTDEV "pon"
#define NAME_WAN_PREFIX "wan"
#define NAME_WLAN_2G_PREFIX "vap"
#define NAME_WLAN_5G_PREFIX "vap"
#elif defined(SYSWAN_PLATFORM_HISILICON_ROUTER)
#define NAME_LAN_PREFIX "eth"
#define NAME_WAN_ROOTDEV "eth0"
#define NAME_WAN_PREFIX "wan"
#define NAME_WLAN_2G_PREFIX "vap"
#define NAME_WLAN_5G_PREFIX "vap"
#elif defined(SYSWAN_PLATFORM_MTK_ROUTER)
#define NAME_LAN_PREFIX "lan"
#define NAME_WAN "eth1"
#define NAME_WAN_PREFIX "eth1."
#define NAME_WLAN_2G_PREFIX "ra"
#define NAME_WLAN_5G_PREFIX "rax"
#endif

// NUM
#ifdef SYSWAN_PLATFORM_AIROHA_ONU
#define NUM_LAN num_lan
#define NUM_LAN_OFFSET 1
#define NUM_WLAN_2G 4
#define NUM_WLAN_2G_OFFSET 4
#define NUM_WLAN_5G 4
#define NUM_WLAN_5G_OFFSET 8
#define NUM_LAN_PHY num_lan_phy
#define NUM_LAN_PHY_OFFSET 8
#elif defined(SYSWAN_PLATFORM_HISILICON_ONU)
#define NUM_LAN 4
#define NUM_LAN_OFFSET 0
#define NUM_WLAN_2G 4
#define NUM_WLAN_2G_OFFSET 4
#define NUM_WLAN_5G 4
#define NUM_WLAN_5G_OFFSET 0
#define NUM_LAN_PHY 0
#define NUM_LAN_PHY_OFFSET 0
#elif defined(SYSWAN_PLATFORM_HISILICON_ROUTER)
#define NUM_LAN 4
#define NUM_LAN_OFFSET 0
#define NUM_WLAN_2G 4
#define NUM_WLAN_2G_OFFSET 4
#define NUM_WLAN_5G 4
#define NUM_WLAN_5G_OFFSET 0
#define NUM_LAN_PHY 0
#define NUM_LAN_PHY_OFFSET 0
#elif defined(SYSWAN_PLATFORM_MTK_ROUTER)
#define NUM_LAN 4
#define NUM_LAN_OFFSET 1
#define NUM_WLAN_2G 8
#define NUM_WLAN_2G_OFFSET 4
#define NUM_WLAN_5G 8
#define NUM_WLAN_5G_OFFSET 8
#endif
#define NUM_PORT (NUM_LAN + NUM_WLAN_2G + NUM_WLAN_5G)

// PATH
#ifdef SYSWAN_PLATFORM_AIROHA_ONU
#define PATH_RULE_MVLAN "/etc/syswan/rule_mvlan.sh"
#endif
#ifdef SYSWAN_FIREWALL_NFTABLES
#define PATH_RULE_BRIDGE_ISOLATE "/etc/syswan/rule_wan_isolate.nft"
#define PATH_RULE_NFTABLES "/etc/syswan/rule_wan_%u.nft"
/* Bohannon for bug#00018162 , ip stack nft rule for wan connection*/
#define PATH_RULE_IPSTACK_NFTABLES "/etc/syswan/rule_wan_%u_ipstack.nft"
#else
#define PATH_RULE_BRIDGE_ISOLATE "/etc/syswan/rule_wan_isolate.sh"
#endif
#define PATH_NET_ADDRESS "/sys/class/net/%s/address"
#define PATH_NET_ADDRESS_LAN "/sys/class/net/br-lan/address"
#define PATH_RULE_BRIDGE "/etc/syswan/rule_wan_%u.sh"
/* Bohannon for bug#00017910 , hotplug for iface wan_x wan led*/
#define PATH_ROUTE_WAN_HOTPLUG "/etc/hotplug.d/iface/98-wan_%u"
#define PATH_BRIDGE_WAN_HOTPLUG "/etc/hotplug.d/iface/99-wan_%u"
#define PATH_WAN_LIST "/tmp/wan_list"

/* ddos rule */
#define PATH_WAN_DDOS_SH_RULE "/etc/syswan/rule_wan_%u_ddos.sh"
#define WAN_DDOS_RULE_BEGIN "#!/bin/sh\n" \
    "HI_NETIF_PROTO_TYPE_ARP=1\n" \
    "HI_NETIF_PROTO_TYPE_ICMP=2\n" \
    "HI_NETIF_PROTO_TYPE_ICMP_V6=3\n" \
    "HI_NETIF_PROTO_TYPE_DHCP=4\n" \
    "HI_NETIF_PROTO_TYPE_DHCP_V6=5\n" \
    "HI_NETIF_PROTO_TYPE_IGMP=6\n" \
    "HI_NETIF_PROTO_TYPE_MLD=7\n" \
    "HI_NETIF_PROTO_TYPE_PPPOE=8\n" \
    "HI_NETIF_PROTO_TYPE_BC=9\n" \
    "HI_NETIF_PROTO_TYPE_ALL=10\n" \
    "HI_NETIF_PROTO_TYPE_ETYPE=11\n" \
    "HI_NETIF_PROTO_TYPE_UUC=12\n" \
    "HI_NETIF_PROTO_TYPE_UMC=13\n" \
    "HI_NETIF_PROTO_TYPE_TCP_SYN=14\n" \
    "HI_NETIF_PROTO_TYPE_PPPOE_DISCOVERY=15\n" \
    "HI_NETIF_PROTO_TYPE_PPP_LCP=16\n" \
    "HI_NETIF_PROTO_TYPE_UDP=17\n" \
    "#unit: 0.1Kbps\n" \
    "DDOS_RATE=100000\n" \
    "IGMP_RATE=10000\n" \
    "\n" \
    "#setup rootdev ddos rules\n" \
    "set_proto_rate() {\n" \
        "\tif [ \"$(cat /sys/firmware/devicetree/base/compatible)\" = \"hsan-emei\" ]; then\n" \
            "\t\thi_ipc /home/<USER>/pon/set_proto_rate -v devname pon type $HI_NETIF_PROTO_TYPE_ARP rate $DDOS_RATE unit 0 pq 0\n" \
            "\t\thi_ipc /home/<USER>/pon/set_proto_rate -v devname pon type $HI_NETIF_PROTO_TYPE_IGMP rate $IGMP_RATE unit 0 pq 0\n" \
            "\t\thi_ipc /home/<USER>/pon/set_proto_rate -v devname pon type $HI_NETIF_PROTO_TYPE_UUC rate $DDOS_RATE unit 0 pq 0\n" \
            "\t\thi_ipc /home/<USER>/pon/set_proto_rate -v devname pon type $HI_NETIF_PROTO_TYPE_UMC rate $IGMP_RATE unit 0 pq 0\n" \
        "\tfi\n" \
    "}\n" \
    "set_proto_rate\n\n"

/* Bohannon for bug#00017693, increase igmp limit rate */
#define WAN_DDOS_RULE "hi_ipc /home/<USER>/pon/set_proto_rate -v devname %s type %s rate %s unit 0 pq 0\n"

/* copy from hi_netif_ipc.h */
enum hi_netif_proto_type {
    HI_NETIF_PROTO_TYPE_ARP = 0x1,
    HI_NETIF_PROTO_TYPE_ICMP,
    HI_NETIF_PROTO_TYPE_ICMP_V6,
    HI_NETIF_PROTO_TYPE_DHCP,
    HI_NETIF_PROTO_TYPE_DHCP_V6,
    HI_NETIF_PROTO_TYPE_IGMP,
    HI_NETIF_PROTO_TYPE_MLD,
    HI_NETIF_PROTO_TYPE_PPPOE,
    HI_NETIF_PROTO_TYPE_BC,
    HI_NETIF_PROTO_TYPE_ALL,
    HI_NETIF_PROTO_TYPE_ETYPE,
    HI_NETIF_PROTO_TYPE_UUC,
    HI_NETIF_PROTO_TYPE_UMC,
    HI_NETIF_PROTO_TYPE_TCP_SYN,
    HI_NETIF_PROTO_TYPE_PPPOE_DISCOVERY,
    HI_NETIF_PROTO_TYPE_PPP_LCP,
    HI_NETIF_PROTO_TYPE_UDP,
    HI_NETIF_PROTO_TYPE_BUTT
}; /* correspond to hi_cfe_ddos_type */

// RULE
#ifdef SYSWAN_PLATFORM_AIROHA_ONU
#define RULE_MVLAN "/userfs/bin/xponigmpcmd port index %u\n"     \
                   "/userfs/bin/xponigmpcmd port downtagctl 4\n" \
                   "/userfs/bin/xponigmpcmd port uptci %u\n"     \
                   "/userfs/bin/xponigmpcmd port downtci %u\n"
#endif
#ifdef SYSWAN_FIREWALL_NFTABLES
#define RULE_BRIDGE "#!/bin/sh\n"                                            \
                    "FWMARK=%u\n"                                            \
                    "ip rule del from all fwmark $FWMARK/$FWMARK prohibit\n" \
                    "if [ \"$1\" != \"-r\" ]; then\n"                        \
                    "ip rule add from all fwmark $FWMARK/$FWMARK prohibit\n" \
                    "fi\n"
#define RULE_BRIDGE_ISOLATE "iifname \"%s\" oifname { %s } drop\n"
#define RULE_BRIDGE_ISOLATE_BEGIN "chain forward {\n"
#define RULE_BRIDGE_ISOLATE_END "}\n"

/* Bohannon for bug#********, bridge ports isolation */
#define RULE_BRIDGE_NFTABLES "chain forward {\n"                           \
                             "iifname { \"%s\", %s } oifname { %s } drop\n"        \
                             "iifname { %s } oifname { \"%s\", %s } drop\n"        \
                             "}\n"                                         \
                             "chain prerouting {\n"                        \
                             "iifname { %s } meta mark set meta mark | 0x%x\n" \
                             "}\n"
#define RULE_BRIDGE_NFTABLES_DHCP "chain input {\n"                                   \
                                  "iifname { %s } ip protocol udp udp dport 67-68 drop\n" \
                                  "}\n"                                               \
                                  "chain output {\n"                                  \
                                  "oif \"%s\" ip protocol udp udp dport 67-68 drop\n" \
                                  "}\n"
#define RULE_ROUTE "#!/bin/sh\n"                                                                                                                                     \
                   "DEVICE_LIST=\"%s\"\n"                                                                                                                            \
                   "FWMARK=%u\n"                                                                                                                                     \
                   "INTERFACE_WAN=\"wan_%u\"\n"                                                                                                                      \
                   "LOOKUP=%u\n"                                                                                                                                     \
                   "[ \"$1\" != \"-r\" ] && [[ \"$INTERFACE\" != \"$INTERFACE_WAN*\" ]] && return\n"                                                                 \
                   ". /lib/functions/network.sh\n"                                                                                                                   \
                   "network_get_ipaddr IP_ADDR \"$INTERFACE_WAN\"\n"                                                                                                 \
                   "network_get_subnet IP_ADDR_MASK \"$INTERFACE_WAN\"\n"                                                                                            \
                   "network_get_gateway IP_GATEWAY \"$INTERFACE_WAN\" 1\n"                                                                                           \
                   "network_get_device INTERFACE_NAME \"$INTERFACE_WAN\"\n"                                                                                          \
                   "REQUEST=$(ipcalc.sh \"$IP_ADDR_MASK\")\n"                                                                                                        \
                   "IP_ADDR_MASK=$(echo \"$REQUEST\" | awk -F '=' '$1 == \"NETWORK\" {print $2}')/$(echo \"$REQUEST\" | awk -F '=' '$1 == \"PREFIX\" {print $2}')\n" \
                   "DNS_SYNC_WITH_WAN=$(uci get dhcp.lan.dns_sync_with_wan)\n"                                                                                       \
                   "V6DNS_SYNC_WITH_WAN=$(uci get dhcp.lan.v6dns_sync_with_wan)\n"                                                                                   \
                   "add_rule() {\n"                                                                                                                                  \
                   "if [ -n \"$DEVICE_LIST\" ]; then\n"                                                                                                              \
                   "ip rule add from all fwmark $FWMARK/$FWMARK prohibit\n"                                                                                          \
                   "ip rule add from all fwmark $FWMARK/$FWMARK lookup $LOOKUP\n"                                                                                    \
                   "fi\n"                                                                                                                                            \
                   "ip rule add from \"$IP_ADDR\" lookup $LOOKUP\n"                                                                                                  \
                   "ip rule add from all oif \"$INTERFACE_NAME\" lookup $LOOKUP\n"                                                                                   \
                   "ip route add default via \"$IP_GATEWAY\" dev \"$INTERFACE_NAME\" table $LOOKUP\n"                                                                \
                   "ip route add \"$IP_ADDR_MASK\" dev \"$INTERFACE_NAME\" scope link table $LOOKUP\n"                                                               \
                   "if [ \"$DNS_SYNC_WITH_WAN\" = \"$INTERFACE_WAN\" ]; then\n"                                                                                      \
                   "DHCP_OPTION=\"6\"\n"                                                                                                                             \
                   "network_get_dnsserver DNS \"$DNS_SYNC_WITH_WAN\"\n"                                                                                              \
                   "uci delete dhcp.lan.dhcp_option\n"                                                                                                               \
                   "for addr in $DNS; do\n"                                                                                                                          \
                   "if [[ \"$addr\" != *:* ]]; then\n"                                                                                                               \
                   "DHCP_OPTION=\"$DHCP_OPTION,$addr\"\n"                                                                                                            \
                   "fi\n"                                                                                                                                            \
                   "done\n"                                                                                                                                          \
                   "if [ \"$DHCP_OPTION\" != \"6\" ]; then\n"                                                                                                        \
                   "uci add_list dhcp.lan.dhcp_option=\"$DHCP_OPTION\"\n"                                                                                            \
                   "fi\n"                                                                                                                                            \
                   "uci commit dhcp.lan\n"                                                                                                                           \
                   "fi\n"                                                                                                                                            \
                   "if [[ \"$V6DNS_SYNC_WITH_WAN\" = \"$INTERFACE_WAN*\" ]]; then\n"                                                                                 \
                   "network_get_dnsserver DNSV6 \"$V6DNS_SYNC_WITH_WAN\"\n"                                                                                          \
                   "uci delete dhcp.lan.dns\n"                                                                                                                       \
                   "for addr in $DNSV6; do\n"                                                                                                                        \
                   "if [[ \"$addr\" = *:* ]]; then\n"                                                                                                                \
                   "uci add_list dhcp.lan.dns=\"$addr\"\n"                                                                                                           \
                   "fi\n"                                                                                                                                            \
                   "done\n"                                                                                                                                          \
                   "uci commit dhcp.lan\n"                                                                                                                           \
                   "fi\n"                                                                                                                                            \
                   "}\n"                                                                                                                                             \
                   "remove_rule() {\n"                                                                                                                               \
                   "if [ -n \"$DEVICE_LIST\" ]; then\n"                                                                                                              \
                   "ip rule del from all fwmark $FWMARK/$FWMARK prohibit\n"                                                                                          \
                   "ip rule del from all fwmark $FWMARK/$FWMARK lookup $LOOKUP\n"                                                                                    \
                   "fi\n"                                                                                                                                            \
                   "ip rule flush table $LOOKUP\n"                                                                                                                   \
                   "ip route del default via \"$IP_GATEWAY\" dev \"$INTERFACE_NAME\" table $LOOKUP\n"                                                                \
                   "ip route del \"$IP_ADDR_MASK\" dev \"$INTERFACE_NAME\" scope link table $LOOKUP\n"                                                               \
                   "if [ \"$DNS_SYNC_WITH_WAN\" = \"$INTERFACE_WAN\" ]; then\n"                                                                                      \
                   "uci delete dhcp.lan.dhcp_option\n"                                                                                                               \
                   "uci commit dhcp.lan\n"                                                                                                                           \
                   "fi\n"                                                                                                                                            \
                   "if [[ \"$V6DNS_SYNC_WITH_WAN\" = \"$INTERFACE_WAN*\" ]]; then\n"                                                                                 \
                   "uci delete dhcp.lan.dns\n"                                                                                                                       \
                   "uci commit dhcp.lan\n"                                                                                                                           \
                   "fi\n"                                                                                                                                            \
                   "}\n"                                                                                                                                             \
                   "if [ \"$1\" = \"-r\" -o \"$ACTION\" = \"ifdown\" ]; then\n"                                                                                      \
                   "remove_rule\n"                                                                                                                                   \
                   "elif [ \"$ACTION\" = \"ifup\" -o \"$ACTION\" = \"ifupdate\" ]; then\n"                                                                           \
                   "remove_rule\n"                                                                                                                                   \
                   "add_rule\n"                                                                                                                                      \
                   "fi\n"
#define RULE_ROUTE_NFTABLES "chain prerouting {\n"                        \
                            "iifname { %s } meta mark set meta mark | 0x%x\n" \
                            "}\n"
#define RULE_ROUTE_NFTABLES_DHCP "chain input {\n"                                   \
                                 "iifname { %s } ip protocol udp udp dport 67-68 drop\n" \
                                 "}\n"
#else
#define RULE_BRIDGE "#!/bin/sh\n"                                                                                                         \
                    "DISABLE_DHCP=%u\n"                                                                                                   \
                    "FWMARK=%u\n"                                                                                                         \
                    "INTERFACE_WAN=\"%s\"\n"                                                                                              \
                    "for i in %s; do\n"                                                                                                   \
                    "ebtables -D FORWARD -i \"$INTERFACE_WAN\" -o \"$i\" -j DROP\n"                                                       \
                    "ebtables -D FORWARD -i \"$i\" -o \"$INTERFACE_WAN\" -j DROP\n"                                                       \
                    "if [ \"$1\" != \"-r\" ]; then\n"                                                                                     \
                    "ebtables -I FORWARD -i \"$INTERFACE_WAN\" -o \"$i\" -j DROP\n"                                                       \
                    "ebtables -I FORWARD -i \"$i\" -o \"$INTERFACE_WAN\" -j DROP\n"                                                       \
                    "fi\n"                                                                                                                \
                    "done\n"                                                                                                              \
                    "for i in %s; do\n"                                                                                                   \
                    "ebtables -t broute -D BROUTING -i \"$i\" -j mark --mark-or $FWMARK --mark-target CONTINUE\n"                         \
                    "[ \"$1\" != \"-r\" ] && ebtables -t broute -I BROUTING -i \"$i\" -j mark --mark-or $FWMARK --mark-target CONTINUE\n" \
                    "if [ $DISABLE_DHCP = 1 ]; then\n"                                                                                    \
                    "ebtables -D INPUT -i \"$i\" -p IPv4 --ip-protocol udp --ip-destination-port 67:68 -j DROP\n"                         \
                    "[ \"$1\" != \"-r\" ] && ebtables -A INPUT -i \"$i\" -p IPv4 --ip-protocol udp --ip-destination-port 67:68 -j DROP\n" \
                    "fi\n"                                                                                                                \
                    "done\n"                                                                                                              \
                    "if [ $DISABLE_DHCP = 1 ]; then\n"                                                                                    \
                    "ebtables -D OUTPUT -p IPv4 -o \"$INTERFACE_WAN\" --ip-proto udp --ip-dport 67:68 -j DROP\n"                          \
                    "[ \"$1\" != \"-r\" ] && ebtables -A OUTPUT -p IPv4 -o \"$INTERFACE_WAN\" --ip-proto udp --ip-dport 67:68 -j DROP\n"  \
                    "fi\n"                                                                                                                \
                    "ip rule del from all fwmark $FWMARK/$FWMARK prohibit\n"                                                              \
                    "if [ \"$1\" != \"-r\" ]; then\n"                                                                                     \
                    "ip rule add from all fwmark $FWMARK/$FWMARK prohibit\n"                                                              \
                    "echo 1 > /sys/class/leds/wan/brightness\n"                                                                           \
                    "else\n"                                                                                                              \
                    "echo 0 > /sys/class/leds/wan/brightness\n"                                                                           \
                    "fi\n"
#define RULE_BRIDGE_ISOLATE "ebtables -D FORWARD -i \"%s\" -o \"%s\" -j DROP\n" \
                            "[ \"$1\" != \"-r\" ] && ebtables -I FORWARD -i \"%s\" -o \"%s\" -j DROP\n"
#define RULE_ROUTE "#!/bin/sh\n"                                                                                                                                     \
                   "DEVICE_LIST=\"%s\"\n"                                                                                                                            \
                   "DISABLE_DHCP=%u\n"                                                                                                                               \
                   "FWMARK=%u\n"                                                                                                                                     \
                   "INTERFACE_WAN=\"wan_%u\"\n"                                                                                                                      \
                   "LOOKUP=%u\n"                                                                                                                                     \
                   "[ \"$1\" != \"-r\" ] && [[ \"$INTERFACE\" != \"$INTERFACE_WAN*\" ]] && return\n"                                                                 \
                   ". /lib/functions/network.sh\n"                                                                                                                   \
                   "network_get_ipaddr IP_ADDR \"$INTERFACE_WAN\"\n"                                                                                                 \
                   "network_get_subnet IP_ADDR_MASK \"$INTERFACE_WAN\"\n"                                                                                            \
                   "network_get_gateway IP_GATEWAY \"$INTERFACE_WAN\" 1\n"                                                                                           \
                   "network_get_device INTERFACE_NAME \"$INTERFACE_WAN\"\n"                                                                                          \
                   "REQUEST=$(ipcalc.sh \"$IP_ADDR_MASK\")\n"                                                                                                        \
                   "IP_ADDR_MASK=$(echo \"$REQUEST\" | awk -F '=' '$1 == \"NETWORK\" {print $2}')/$(echo \"$REQUEST\" | awk -F '=' '$1 == \"PREFIX\" {print $2}')\n" \
                   "DNS_SYNC_WITH_WAN=$(uci get dhcp.lan.dns_sync_with_wan)\n"                                                                                       \
                   "V6DNS_SYNC_WITH_WAN=$(uci get dhcp.lan.v6dns_sync_with_wan)\n"                                                                                   \
                   "add_rule() {\n"                                                                                                                                  \
                   "if [ -n \"$DEVICE_LIST\" ]; then\n"                                                                                                              \
                   "ip rule add from all fwmark $FWMARK/$FWMARK prohibit\n"                                                                                          \
                   "ip rule add from all fwmark $FWMARK/$FWMARK lookup $LOOKUP\n"                                                                                    \
                   "fi\n"                                                                                                                                            \
                   "ip rule add from \"$IP_ADDR\" lookup $LOOKUP\n"                                                                                                  \
                   "ip rule add from all oif \"$INTERFACE_NAME\" lookup $LOOKUP\n"                                                                                   \
                   "ip route add default via \"$IP_GATEWAY\" dev \"$INTERFACE_NAME\" table $LOOKUP\n"                                                                \
                   "ip route add \"$IP_ADDR_MASK\" dev \"$INTERFACE_NAME\" scope link table $LOOKUP\n"                                                               \
                   "for i in $DEVICE_LIST; do\n"                                                                                                                     \
                   "ebtables -t broute -I BROUTING -i \"$i\" -j mark --mark-or $FWMARK --mark-target CONTINUE\n"                                                     \
                   "[ $DISABLE_DHCP = 1 ] && ebtables -A INPUT -i \"$i\" -p IPv4 --ip-protocol udp --ip-destination-port 67:68 -j DROP\n"                            \
                   "done\n"                                                                                                                                          \
                   "if [ \"$DNS_SYNC_WITH_WAN\" = \"$INTERFACE_WAN\" ]; then\n"                                                                                      \
                   "DHCP_OPTION=\"6\"\n"                                                                                                                             \
                   "network_get_dnsserver DNS \"$DNS_SYNC_WITH_WAN\"\n"                                                                                              \
                   "uci delete dhcp.lan.dhcp_option\n"                                                                                                               \
                   "for addr in $DNS; do\n"                                                                                                                          \
                   "if [[ \"$addr\" != *:* ]]; then\n"                                                                                                               \
                   "DHCP_OPTION=\"$DHCP_OPTION,$addr\"\n"                                                                                                            \
                   "fi\n"                                                                                                                                            \
                   "done\n"                                                                                                                                          \
                   "if [ \"$DHCP_OPTION\" != \"6\" ]; then\n"                                                                                                        \
                   "uci add_list dhcp.lan.dhcp_option=\"$DHCP_OPTION\"\n"                                                                                            \
                   "fi\n"                                                                                                                                            \
                   "uci commit dhcp.lan\n"                                                                                                                           \
                   "fi\n"                                                                                                                                            \
                   "if [[ \"$V6DNS_SYNC_WITH_WAN\" = \"$INTERFACE_WAN*\" ]]; then\n"                                                                                 \
                   "network_get_dnsserver DNSV6 \"$V6DNS_SYNC_WITH_WAN\"\n"                                                                                          \
                   "uci delete dhcp.lan.dns\n"                                                                                                                       \
                   "for addr in $DNSV6; do\n"                                                                                                                        \
                   "if [[ \"$addr\" = *:* ]]; then\n"                                                                                                                \
                   "uci add_list dhcp.lan.dns=\"$addr\"\n"                                                                                                           \
                   "fi\n"                                                                                                                                            \
                   "done\n"                                                                                                                                          \
                   "uci commit dhcp.lan\n"                                                                                                                           \
                   "fi\n"                                                                                                                                            \
                   "}\n"                                                                                                                                             \
                   "remove_rule() {\n"                                                                                                                               \
                   "if [ -n \"$DEVICE_LIST\" ]; then\n"                                                                                                              \
                   "ip rule del from all fwmark $FWMARK/$FWMARK prohibit\n"                                                                                          \
                   "ip rule del from all fwmark $FWMARK/$FWMARK lookup $LOOKUP\n"                                                                                    \
                   "fi\n"                                                                                                                                            \
                   "ip rule flush table $LOOKUP\n"                                                                                                                   \
                   "ip route del default via \"$IP_GATEWAY\" dev \"$INTERFACE_NAME\" table $LOOKUP\n"                                                                \
                   "ip route del \"$IP_ADDR_MASK\" dev \"$INTERFACE_NAME\" scope link table $LOOKUP\n"                                                               \
                   "for i in $DEVICE_LIST; do\n"                                                                                                                     \
                   "ebtables -t broute -D BROUTING -i \"$i\" -j mark --mark-or $FWMARK --mark-target CONTINUE\n"                                                     \
                   "[ $DISABLE_DHCP = 1 ] && ebtables -D INPUT -i \"$i\" -p IPv4 --ip-protocol udp --ip-destination-port 67:68 -j DROP\n"                            \
                   "done\n"                                                                                                                                          \
                   "if [ \"$DNS_SYNC_WITH_WAN\" = \"$INTERFACE_WAN\" ]; then\n"                                                                                      \
                   "uci delete dhcp.lan.dhcp_option\n"                                                                                                               \
                   "uci commit dhcp.lan\n"                                                                                                                           \
                   "fi\n"                                                                                                                                            \
                   "if [[ \"$V6DNS_SYNC_WITH_WAN\" = \"$INTERFACE_WAN*\" ]]; then\n"                                                                                 \
                   "uci delete dhcp.lan.dns\n"                                                                                                                       \
                   "uci commit dhcp.lan\n"                                                                                                                           \
                   "fi\n"                                                                                                                                            \
                   "}\n"                                                                                                                                             \
                   "if [ \"$1\" = \"-r\" -o \"$ACTION\" = \"ifdown\" ]; then\n"                                                                                      \
                   "remove_rule\n"                                                                                                                                   \
                   "elif [ \"$ACTION\" = \"ifup\" -o \"$ACTION\" = \"ifupdate\" ]; then\n"                                                                           \
                   "remove_rule\n"                                                                                                                                   \
                   "add_rule\n"                                                                                                                                      \
                   "fi\n"
#endif

#ifdef SYSWAN_PLATFORM_HISILICON_ROUTER
/* Bohannon for bug#00017910 , hotplug for iface wan_x wan led*/
#define INTERNET_ROUTE_WAN_LED_TRIGGER    \
                "\nif [ \"$1\" = \"-r\" -o \"$ACTION\" = \"ifdown\" ]; then\n"                                                                                      \
                "echo 0 > /tmp/wan_status\n"                                                                                                                      \
                "/etc/init.d/led restart\n"                                                                                                                       \
                "elif [ \"$ACTION\" = \"ifup\" -o \"$ACTION\" = \"ifupdate\" ]; then\n"                                                                           \
                "if [ -z \"$IP_ADDR\" ]; then\n"  \
                "echo 0 > /tmp/wan_status\n"                                                                                                                      \
                "/etc/init.d/led restart\n"                                                                                                                       \
                "else\n" \
                "echo 1 > /tmp/wan_status\n"                                                                                                                      \
                "/etc/init.d/led restart\n"                                                                                                                       \
                "fi\n" \
                "fi\n"
                
#define INTERNET_BRIDGE_WAN_LED_TRIGGER    \
                    "#!/bin/sh\n"                                                                                                                                     \
                    "INTERFACE_WAN=\"wan_%u\"\n"                                                                                                                      \
                    "[ \"$1\" != \"-r\" ] && [[ \"$INTERFACE\" != \"$INTERFACE_WAN*\" ]] && return\n"                                                                 \
                    "\nif [ \"$1\" = \"-r\" -o \"$ACTION\" = \"ifdown\" ]; then\n"                                                                                      \
                    "echo 0 > /tmp/wan_status\n"                                                                                                                      \
                    "/etc/init.d/led restart\n"                                                                                                                       \
                    "elif [ \"$ACTION\" = \"ifup\" -o \"$ACTION\" = \"ifupdate\" ]; then\n"                                                                           \
                    "echo 1 > /tmp/wan_status\n"                                                                                                                      \
                    "/etc/init.d/led restart\n"                                                                                                                       \
                    "fi\n"
#else
/* Bohannon for bug#00017910 , hotplug for iface wan_x wan led*/
#define INTERNET_ROUTE_WAN_LED_TRIGGER    \
                "\nif [ \"$1\" = \"-r\" -o \"$ACTION\" = \"ifdown\" ]; then\n"                                                                                      \
                "echo 0 > /sys/class/leds/led_internet/brightness\n"                                                                                              \
                "elif [ \"$ACTION\" = \"ifup\" -o \"$ACTION\" = \"ifupdate\" ]; then\n"                                                                           \
                "if [ -z \"$IP_ADDR\" ]; then\n"  \
                "echo 0 > /sys/class/leds/led_internet/brightness\n"                                                                                              \
                "else\n" \
                "echo 1 > /sys/class/leds/led_internet/brightness\n"                                                                                              \
                "fi\n" \
                "fi\n"
                
#define INTERNET_BRIDGE_WAN_LED_TRIGGER    \
                    "#!/bin/sh\n"                                                                                                                                     \
                    "INTERFACE_WAN=\"wan_%u\"\n"                                                                                                                      \
                    "[ \"$1\" != \"-r\" ] && [[ \"$INTERFACE\" != \"$INTERFACE_WAN*\" ]] && return\n"                                                                 \
                    "\nif [ \"$1\" = \"-r\" -o \"$ACTION\" = \"ifdown\" ]; then\n"                                                                                      \
                    "echo 0 > /sys/class/leds/led_internet/brightness\n"                                                                                              \
                    "elif [ \"$ACTION\" = \"ifup\" -o \"$ACTION\" = \"ifupdate\" ]; then\n"                                                                           \
                    "echo 1 > /sys/class/leds/led_internet/brightness\n"                                                                                              \
                    "fi\n"
#endif

/* Bohannon for bug#00018162 , ip stack nft rule for wan connection*/
#define RULE_IPSTACK_NFTABLES "chain forward {\n"                           \
                                 "iifname %s ether type %s counter drop\n"        \
                                 "oifname %s ether type %s counter drop\n"        \
                                 "}\n"
                                 
// Added by Jack.Tam on 2024-03. For customization system framework.
#define SYSWAN_MAX_WAN_FILE_NUMS 8
#define SYSWAN_MAX_FILE_PATH_LEN 64
#define SYSWAN_MAX_WAN_DATA_LEN 1024
#define SYSWAN_MAX_WAN_PARA_VAL_LEN 128
#define SYSWAN_FILE_CHANNEL_MODE_BRIDGE 0
#define SYSWAN_FILE_CHANNEL_MODE_IPOE 1
#define SYSWAN_FILE_CHANNEL_MODE_PPPOE 2
#define SYSWAN_FILE_SERVICE_TYPE_TR069 1
#define SYSWAN_FILE_SERVICE_TYPE_INTERNET 2
#define SYSWAN_FILE_SERVICE_TYPE_OTHER 4
#define SYSWAN_FILE_SERVICE_TYPE_VOIP 8
#define SYSWAN_FILE_SERVICE_TYPE_TR069_INTERNET 3
#define SYSWAN_FILE_SERVICE_TYPE_TR069_VOIP 9
#define SYSWAN_FILE_SERVICE_TYPE_VOIP_INTERNET 10
#define SYSWAN_FILE_SERVICE_TYPE_TR069_VOIP_INTERNET 11

/* Bohannon for mission#00040567, device mcast router mode  */
enum brport_mrouter_mode{
    BRPORT_MROUTER_DISABLED=0,
    BRPORT_MROUTER_AUTO,
    BRPORT_MROUTER_STATIC,
};

enum syswan_service_mode
{
    TR069_INTERNET,
    INTERNET,
    TR069,
    OTHER,
    VOIP,
    TR069_VOIP,
    VOIP_INTERNET,
    TR069_VOIP_INTERNET,
};

typedef struct syswan_wan_conn_data
{
    unsigned char autoDns;
    unsigned char autoDnsV6;
    unsigned char dailMode;
    unsigned char disableLanDhcp;
    unsigned char ipMode;
    unsigned char ipProtocol;
    unsigned char ipv6DsLite;
    unsigned char ipv6PrefixProxy;
    unsigned char napt;
    unsigned char prio;
    unsigned char service;
    unsigned char status;
    unsigned char vlanMode;
    unsigned char wanMode;
    unsigned char iphostId;
    unsigned char isIptvWan;
    unsigned short mtu;
    unsigned short vid;
    unsigned short mvid;
    char bind[16];
    char gateway[16];
    char ip[16];
    char ipv6Addr[45];
    char ipv6AddrDsLite[128];
    char ipv6Gateway[40];
    char ipv6Prefix[44];
    char mac[18];
    char mask[16];
    char pppPwd[65];
    char pppServName[65];
    char pppUser[65];
    char priDns[16];
    char priDnsV6[40];
    char secDns[16];
    char secDnsV6[40];
} syswan_wan_conn_data_t;

typedef struct syswan_wan_conn
{
    unsigned char wanId;
    char wanName[34];
    syswan_wan_conn_data_t wanData;
} syswan_wan_conn_t;

extern int syswan_init(void);
extern unsigned char syswan_get_max_wan_id(void); // Added by Jack.Tam on 2024-02. For TR069 WanConnectionDevice.
extern unsigned char syswan_get_wan_nums(void);
extern int syswan_get_wan(syswan_wan_conn_t *wanConn);
extern int syswan_read_all_wan(syswan_wan_conn_t *wanConns);
extern int syswan_add_wan(syswan_wan_conn_data_t *wanData, const unsigned char wanId, int commit);
extern int syswan_modify_wan(syswan_wan_conn_t *wanConn);
extern int syswan_del_all_wan(int commit);
extern int syswan_del_wan(const unsigned char wanId, const unsigned char commit, const unsigned char isModify);
extern int syswan_add_wan_dev(void); // Added by Jack.Tam on 2024-02. For TR069 WanConnectionDevice.
extern int syswan_reconfig_all_wan(void); // Reconfig all WAN connections with new MAC addresses
extern void syswan_take_effect(void);
#endif /* !_SYSWAN_CORE_H_ */
