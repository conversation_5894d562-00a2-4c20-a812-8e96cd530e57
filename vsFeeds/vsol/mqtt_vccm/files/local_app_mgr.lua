package.path = package.path .. ";/usr/lib/lua/mqtt_vccm/?.lua;"
package.cpath = package.cpath .. ";/usr/lib/lua/vs_crypto.so;" 
package.cpath = package.cpath .. ";/usr/lib/lua/platform_call.so;"


module("luci.controller.local_app_mgr", package.seeall)
local com = require "luci.com_fun"
local uci = require("luci.model.uci").cursor()
local sys = require "luci.sys"
local vs_crypto = require "vs_crypto"
local mqtt_api = require "platform_call"
local info_process = com.safe_require("info_process")
local ems_cmd_process = com.safe_require("ems_cmd_process")
local ems_cfg_process = com.safe_require("ems_cfg_process")
local util = require("luci.util")

function index()
    entry({ "APPC", "info", "get" }, call("get_info"))
    entry({ "APPC", "cmd", "set" }, call("set_cmd"))
    entry({ "APPC", "cmd", "get" }, call("get_cmd"))
    entry({ "APPC", "cfg", "set" }, call("set_cfg"))
    entry({ "APPC", "getASPdata", "mqtt_user_mng" }, call("set_mqtt_cfg"))
    entry({ "APPC", "getASPdata", "FMask" }, call("get_fmask"))
    entry({ "APPC", "admin", "formLogin" }, call("form_login"))
    entry({ "APPC", "admin", "formLogout" }, call("form_logout"))
    entry({ "APPC", "getASPdata", "getRouterMac_json" }, call("get_route_mac"))
    entry({ "APPC", "getASPdata", "formQuickLoginApp" }, call("form_quick_login"))
    entry({ "APPC", "getASPdata", "formQuickCfgLoginAPP" }, call("form_quick_setup"))
end

local function vst_parse_http_content(content)
    local result = {}
    
    for key_value in string.gmatch(content, "([^&]+)") do
        local key, value = string.match(key_value, "([^=]+)=([^=]+)")
        if key and value then
            result[key] = value
        end
    end
    
    return result
end

function get_info()
    local request_url = luci.http.getenv("REQUEST_URI")
    local query_string = string.sub(request_url, string.find(request_url, "?") + 1)
    local decoded_query_string = luci.http.urldecode(query_string)
    local encoded_query_string = "dsfgdfs"
    local length_encode = 100 
    local length = 0
    local data = ""
    local ret

    if decoded_query_string then
	    if info_process ~= nil then
	    	length = string.len(decoded_query_string)
        	vs_crypto.decrypt(decoded_query_string,length)

	        data = info_process.get_onu_info(decoded_query_string)

            local debug = io.open("/tmp/lua_local_app_mgr","w")
            if debug then
                debug:write(decoded_query_string.."\n")
                debug:write(data.."\n")
            end

	        length = string.len(data)

	        ret = vs_crypto.encrypt(data,length,encoded_query_string,length_encode)
            
            if (debug) then
                debug:write(ret.."\n")
                debug:close()
            end

	        luci.http.write(ret)
	    else
	        luci.http.write("gGdsZQAYCjFHm5T8EiTXOmhoj/wAcBRO")
	    end
	end
end

function set_cmd()
	local encoded_query_string = "NULL"
	local data = ""
    local length_encode = 0 
    local length = 0
	local ret 
    
    local request_body = luci.http.content()
    if request_body then
        if ems_cmd_process ~= nil then
        	length = string.len(request_body)
        	vs_crypto.decrypt(request_body,length)
        	data=ems_cmd_process.cmd_set(request_body)
        	length = string.len(data)

        	ret =vs_crypto.encrypt(data,length,encoded_query_string,length_encode)
            luci.http.write(ret)
        else
            luci.http.write("gGdsZQAYCjFHm5T8EiTXOmhoj/wAcBRO")
        end
    end
end

function get_cmd()
	local encoded_query_string = "NULL"
	local data = ""
    local length_encode = 0 
    local length = 0
    local ret 
    
    if ems_cmd_process ~= nil then
    	data=ems_cmd_process.cmd_get()
    	length = string.len(data)
    	ret =vs_crypto.encrypt(data,length,encoded_query_string,length_encode)
        luci.http.write(ret)
    else
        luci.http.write("gGdsZQAYCjFHm5T8EiTXOmhoj/wAcBRO")
    end
    
end

function set_cfg()
	local encoded_query_string = "NULL"
	local data = ""
    local length_encode = 0 
    local length = 0
    local ret 

    local request_body = luci.http.content()
    if request_body then
        if ems_cfg_process ~= nil then
        	length = string.len(request_body)
        	vs_crypto.decrypt(request_body,length)

            data=ems_cfg_process.cfg_set(request_body)
            length = string.len(data)

        	ret =vs_crypto.encrypt(data,length,encoded_query_string,length_encode)
            luci.http.write(ret)
        else
            luci.http.write("gGdsZQAYCjFHm5T8EiTXOmhoj/wAcBRO")
        end
    end
end

function set_mqtt_cfg()
    local user, psd
    local mqtt_enable = 1
    local request_body = luci.http.content()
    local length = string.len(request_body)
    local ret = ""
    
    -- 创建日志文件
    local log_file = io.open("/tmp/mqtt_cfg_debug.log", "w")
    if log_file then
        log_file:write("=== set_mqtt_cfg 函数开始执行 ===\n")
        log_file:write("时间: " .. os.date("%Y-%m-%d %H:%M:%S") .. "\n")
        log_file:write("request_body长度: " .. tostring(length) .. "\n")
        log_file:flush()
    end
    
    local router_device_flag = 1;
    local device_model = util.trim(util.exec("sed -n 's/.*product name\\s*:\\s*\\(.*\\)/\\1/p' /etc/hi_version"))
    if (device_model:match("wrt_v2802ach")~=nil) then
        router_device_flag = 0;
    end
    
    if log_file then
        log_file:write("设备型号: " .. tostring(device_model) .. "\n")
        log_file:write("路由器设备标志: " .. tostring(router_device_flag) .. "\n")
        log_file:flush()
    end

    if request_body then
        if log_file then
            log_file:write("开始解密request_body...\n")
            log_file:flush()
        end
        
        vs_crypto.decrypt(request_body, length)
        
        if log_file then
            log_file:write("解密完成，开始解析参数...\n")
            log_file:flush()
        end
        
        parm = vst_parse_http_content(request_body)
        
        if log_file then
            log_file:write("参数解析完成，参数数量: " .. (parm and table.getn(parm) or 0) .. "\n")
            for k, v in pairs(parm or {}) do
                log_file:write("参数 " .. k .. " = " .. tostring(v) .. "\n")
            end
            log_file:flush()
        end
        
        if router_device_flag == 1 then
            user = com.getUsernameByRole("admin")
        else
            user = com.getUsernameByRole("user")
        end
        psd = parm["psd"] or "user"
    else
        if log_file then
            log_file:write("request_body为空，使用表单值\n")
            log_file:flush()
        end
        
        if router_device_flag == 1 then
            user = com.getUsernameByRole("admin")
        else
            user = com.getUsernameByRole("user")
        end
        psd = luci.http.formvalue("psd") or "user"
    end
    
    if log_file then
        log_file:write("用户名: " .. tostring(user) .. "\n")
        log_file:write("密码: " .. tostring(psd) .. "\n")
        log_file:flush()
    end
    
    local mqttAddr = parm["mqttAddr"] or "0"
    local mqttBindUserId = parm["mqttBindUserId"] or "0"
    local mqttBindTopic = parm["mqttBindTopic"] or "0"
    local group_name = parm["group_name"] or "0"
    local timestamp = parm["timestamp"] or "0"
    
    if log_file then
        log_file:write("MQTT配置参数:\n")
        log_file:write("  mqttAddr: " .. tostring(mqttAddr) .. "\n")
        log_file:write("  mqttBindUserId: " .. tostring(mqttBindUserId) .. "\n")
        log_file:write("  mqttBindTopic: " .. tostring(mqttBindTopic) .. "\n")
        log_file:write("  group_name: " .. tostring(group_name) .. "\n")
        log_file:write("  timestamp: " .. tostring(timestamp) .. "\n")
        log_file:flush()
    end
    
    if log_file then
        log_file:write("开始验证用户密码...\n")
        log_file:flush()
    end
    
    local auth = sys.user.checkpasswd(user, psd)
    
    if log_file then
        log_file:write("密码验证结果: " .. tostring(auth) .. "\n")
        log_file:write("开始清理临时文件...\n")
        log_file:flush()
    end
    
    os.execute("rm /tmp/bind_success -rf");
    os.execute("rm /tmp/vccm_connect_status -rf");
    os.execute("echo 1 > /tmp/binding");

    if log_file then
        log_file:write("临时文件清理完成\n")
        log_file:flush()
    end

    if auth then
        if log_file then
            log_file:write("用户验证成功，开始调用mqtt_user_mng...\n")
            log_file:flush()
        end
        
        ret = mqtt_api.mqtt_user_mng(mqtt_enable, mqttAddr, mqttBindUserId, group_name, timestamp, mqttBindTopic)
        
        if log_file then
            log_file:write("mqtt_user_mng调用完成，返回值: " .. tostring(ret) .. "\n")
            log_file:write("开始提交UCI配置...\n")
            log_file:flush()
        end
        
        uci:commit()

        if log_file then
            log_file:write("UCI配置提交完成\n")
            log_file:flush()
        end

        if ret == "success" then
            if log_file then
                log_file:write("返回成功响应\n")
                log_file:write("=== 函数执行完成 ===\n")
                log_file:close()
            end
            luci.http.write("gHJvcAAoCjm7b11/xfvGFAk8A045BAl+MKGlVNOmJdQZEsuy2QEkHA==")
        elseif ret == "bind failed" then
            if log_file then
                log_file:write("返回绑定失败响应\n")
                log_file:write("=== 函数执行完成 ===\n")
                log_file:close()
            end
            luci.http.write("gHduZAAYCqIPOsUP4w6U0ZKmx4SlJFdg")
        else
            if log_file then
                log_file:write("返回其他失败响应\n")
                log_file:write("=== 函数执行完成 ===\n")
                log_file:close()
            end
            luci.http.write("gJV0dwAYCrWfDHSAPsbM/Mm1tuX3n1bE")
        end
    else
        if log_file then
            log_file:write("用户验证失败，返回认证失败响应\n")
            log_file:write("=== 函数执行完成 ===\n")
            log_file:close()
        end
        luci.http.write("gLNfTwAoCltLeWCgsNvqtYfhZmlkbzy382GU3BJqkI+C+/NKV61cdg==")
    end
    
end

function get_fmask()
    luci.http.write("gPRjYwAYCubJyW5mV9sZrajE/F7JEjv1")
end

function form_login()
    local user, psd
    local request_body = luci.http.content()
    local length = string.len(request_body)
    
    --ZHH test
    local router_device_flag = 1;
    local device_model = util.trim(util.exec("sed -n 's/.*product name\\s*:\\s*\\(.*\\)/\\1/p' /etc/hi_version"))
    if (device_model:match("wrt_v2802ach")~=nil) then
        router_device_flag = 0;
    end

    if request_body then
        vs_crypto.decrypt(request_body, length)
        local parm = vst_parse_http_content(request_body)
        if router_device_flag == 1 then
            user = com.getUsernameByRole("admin")
        else
            user = com.getUsernameByRole("user")
        end
        psd = parm["psd"] or "user"
    else
        if router_device_flag == 1 then
            user = com.getUsernameByRole("admin")
        else
            user = com.getUsernameByRole("user")
        end
        psd = luci.http.formvalue("psd") or "user"
    end
    
    local debug = io.open("/tmp/lua_local_app_mgr","w")
    if debug then
        debug:write("user:"..tostring(user).."\n")
        debug:write("psd:"..tostring(psd).."\n")
        debug:close()
    end      

    local auth = sys.user.checkpasswd(user, psd)

    if auth then
        luci.http.write("gHJvcAAoCjm7b11/xfvGFAk8A045BAl+MKGlVNOmJdQZEsuy2QEkHA==")
    else
        luci.http.write("gLNfTwAoCltLeWCgsNvqtYfhZmlkbzy382GU3BJqkI+C+/NKV61cdg==")
    end
end

function form_logout()
    luci.http.write("gPRjYwAYCubJyW5mV9sZrajE/F7JEjv1")
end

function form_quick_login()
    luci.http.write("gPRjYwAYCubJyW5mV9sZrajE/F7JEjv1")
end

function form_quick_setup()
    luci.http.write("gPRjYwAYCubJyW5mV9sZrajE/F7JEjv1")
end
