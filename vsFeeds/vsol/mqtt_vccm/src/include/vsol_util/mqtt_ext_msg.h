#ifndef MQTT_EXT_MSG_H
#define MQTT_EXT_MSG_H

#include "mqtt_public.h"

#define MQTT_EXT_REQ_MAGIC 0x56535472
#define MQTT_EXT_RESP_MAGIC 0x56535473
#define MQTT_EXT_SOCKET_PATH "/tmp/mqtt_ext_if.sock"
#define UDP_BROADCAST_SERVER_PORT 5678

#define MQTT_BUFFER_SIZE 8192
#define MQTT_BUFFER_DATA_SIZE (MQTT_BUFFER_SIZE - sizeof(struct mqtt_ext_msg))
#define MAX_UPGRADE_DEVICE_NUM 8
#define UDP_DISCOVER_STRING "Hello, Controller!"
#define UPLOAD_IMAGE_TIMEOUT 120                 /* seconds */
#define UPGRADE_TIMEOUT 5 * UPLOAD_IMAGE_TIMEOUT /* seconds */

#define MQTT_CONNECT_STATUS_FILE "/tmp/vccm_connect_status"

struct mqtt_ext_msg
{
    INT32U magic;
    INT16U type;
    INT16U len;
    INT8U data[0];
} __attribute__((packed));

struct mqtt_ext_bind
{
    char server_addr[64];
    char user_id[64];
    char group_name[32];
    char time_stamp[64];
    char mqtt_bind_topic[64];
};

struct mqtt_ext_renew_info
{
    char topic[64];
    char info_param[512];
};

typedef struct upgrade_deivce_param
{
    INT8U mac_str[32];
    INT8U device_type[32];
    INT8U device_ip[32];
    INT8U vendor_info[48];
    INT8U md5_result[64];
    INT32U download_role;
    INT32U download_status;
    INT16U upgrade_time;
} __attribute__((packed)) UPGRADE_DEVICE_INFO;

struct upgrade_deivce_param_msg
{
    int upgrade_device_num;
    UPGRADE_DEVICE_INFO upgrade_deivce_param[MAX_UPGRADE_DEVICE_NUM];
} __attribute__((packed));

struct mqtt_ext_download_msg
{
    INT8U mac[6];
    INT16U msg_type;
    INT8U device_type[32];
    INT8U md5_result[32];
    INT8U download_path[512];
    INT16U upgrade_time;
    INT8U ftp_user[64];
    INT16U ftp_password_encrypt_len;
    INT8U ftp_password_encrypt[128];
} __attribute__((packed));

struct mqtt_ext_discover_msg
{
    INT8U udp_device_type;
    INT8U mac[6];
    INT8U device_pn_type[32];
    INT8U vendor_info[48];
    INT8U project_id[32];
    INT8U customer_model[32];
    INT8U software_version[32];
    INT8U pri_model[32];
    INT8U haware_version[32];
    INT32U info_update_time_seconds;
    INT32U run_time_seconds;
} __attribute__((packed));

typedef struct udp_device_list_msg
{
    int udp_device_num;
    struct mqtt_ext_discover_msg udp_device_info[MAX_UPGRADE_DEVICE_NUM];
} __attribute__((packed));

struct mqtt_ext_multi_wlan_enable_msg
{
    INT8U multi_wlan_enable;
} __attribute__((packed));

struct mqtt_ext_iptv_cfg
{
    INT32U iptv_enable;
    INT32U vid_val;
    INT32U bindport;
} __attribute__((packed));

struct mqtt_ext_smart_qos_cfg
{
    INT32U qos_enable;
    INT32U qos_mode;
} __attribute__((packed));

struct mqtt_ext_system_cmd
{
    char command[100];
} __attribute__((packed));

struct mqtt_ext_wan_cfg
{
    char connect_mode[16];
    char ppp_user[32];
    char ppp_pwd[32];
    char ppp_serv_name[32];
    char ip[16];
    char mask[16];
    char gateway[16];
    char pri_dns[16];
    char sec_dns[16];
    char pri_dns_v6[32];
    char sec_dns_v6[32];
    char ipv6_addr[32];
    char ipv6_gateway[32];
    char ipv6_prefix[32];
    char ipv6_addr_dslite[32];
    INT32U vid;
    INT32U mtu;
    INT32U prio;
    INT32U ip_mode;
    INT32U wan_mode;
    INT32U service;
    INT32U ip_protocol;
    INT32U auto_dns;
    INT32U auto_dns_v6;
    INT32U ipv6_prefix_proxy;
    INT32U dial_mode;
    INT32U ipv6_dslite;
    INT32U disable_lan_dhcp;
    INT32U napt;
    INT32U vlan_mode;
    char bind[16];
} __attribute__((packed));


typedef enum
{
    MQTT_EXT_MSG_TYPE_BINDING,
    MQTT_EXT_MSG_TYPE_UNBINDING,
    MQTT_EXT_MSG_TYPE_REBOOT,
    MQTT_EXT_MSG_TYPE_RESET,
    MQTT_EXT_MSG_TYPE_DEVICE_DISCOVER,
    MQTT_EXT_MSG_UDP_UPDATE_DEVICE_INFO,
    MQTT_EXT_MSG_TYPE_APP_DISCOVER_INFO,
    MQTT_EXT_MSG_TYPE_UPDATE_WLAN_TIMER = 10,
    MQTT_EXT_MSG_TYPE_UPDATE_GUEST_OPEN_DURATION,
    MQTT_EXT_MSG_TYPE_MULTI_WLAN_ENABLE,
    MQTT_EXT_MSG_PAIR_GET_ALL_AGENTS = 20,
    MQTT_EXT_MSG_PAIR_ADD_AGENT,
    MQTT_EXT_MSG_PAIR_GET_LAST_INFO,
    MQTT_EXT_MSG_PAIR_GET_WPS_STATE,
    MQTT_EXT_MSG_PAIR_DELETE_AGENT,
    MQTT_EXT_MSG_DNS_QUERY_INFO = 30,
    MQTT_EXT_MSG_UPDATE_SWITCH_L2_INFO = 31,
    MQTT_EXT_MSG_WLAN_TAKE_EFFECT = 40,
    MQTT_EXT_MSG_WLAN_GET_GUEST_OPEN_DURATION,
    MQTT_EXT_MSG_UPGRADE_EVENT = 50,
    MQTT_EXT_MSG_UPGRADE_DOWNLOAD_MSG,
    MQTT_EXT_MSG_UPGRADE_START,
    MQTT_EXT_MSG_UPGRADE_GET_STATE,
    MQTT_EXT_MSG_UDP_DEVICE_DISCOVER = 60,
    MQTT_EXT_MSG_UDP_APP_DISCOVER_INFO,
    MQTT_EXT_MSG_UDP_GET_DEVICE_INFO,
    MQTT_EXT_MSG_UDP_UPDATE_LOCAL_DEVICE_INFO,
    MQTT_EXT_MSG_PUSH_RENEW_INFO = 70,
    MQTT_EXT_MSG_TYPE_IPTV_CFG = 80,
    MQTT_EXT_MSG_TYPE_SMART_QOS_CFG = 81,
    MQTT_EXT_MSG_TYPE_WAN_CFG = 82,
    MQTT_EXT_MSG_TYPE_SYSTEM_CMD = 83,
    MQTT_EXT_MSG_UPDATE_WLAN_QUALITY = 100,
    MQTT_EXT_MSG_OPTIMIZE_WLAN_QUALITY = 101,
} MQTT_EXT_MSG;

struct mqtt_upgrade_device_type_info
{
    char device_type[32];
    char vendor_info[48];
    char md5_result[64];
    char mac_list_str[128];
    char download_url[512];
};

struct mqtt_app_upgrade_msg
{
    int device_type_num;
    struct mqtt_upgrade_device_type_info device_type_info[MAX_UPGRADE_DEVICE_NUM];
} __attribute__((packed));
;

typedef enum
{
    UDP_ROLE_CONTROLLER,
    UDP_ROLE_SUBORDINATE,
    UDP_ROLE_AUTO,
    UDP_ROLE_APP,
} UDP_ROLE;


typedef enum
{
    DOWNLOAD_CONTROLLER,
    DOWNLOAD_BY_HTTPS,
    DOWNLOAD_BY_FTP,
    DOWNLOAD_SUCCESS,
    DOWNLOAD_FAILED,
    DOWNLOAD_TIMEOUT,
    DOWNLOAD_FILE_INCORRECT,
    DOWNLOAD_ING,
    DOWNLOAD_STATUS_MAX,
} MQTT_DOWNLOAD_MSG;


#ifdef MQTT_EXT_CLIENT_SUPPORT
extern int mqtt_ext_client_send_reboot();
extern int mqtt_ext_client_send_reboot_all_devices();
extern int mqtt_ext_client_send_reset();
#endif

#ifdef MQTT_EXT_UDP_SUPPORT
extern int mqtt_ext_udp_role_refresh();
extern int mqtt_ext_udp_send_udp_discover();
extern int mqtt_ext_create_udp_server_socket();
extern int mqtt_ext_udp_recv(char *recv_buf, int recv_len, struct sockaddr *src_addr, socklen_t src_addrlen);
extern int mqtt_udp_send_delete_agent_packet(INT8U *buf, INT16U len);
extern int mqtt_ext_udp_send_upgrade_msg(char *des_ip);
extern int mqtt_upgrade_status_update(struct mqtt_ext_download_msg *download_info);
extern int mqtt_ext_client_bind_cfg(int mqtt_enable, const char *server_addr, const char *user_id, const char *group_name, const char *time_stamp, const char *mqtt_bind_topic);
#endif

#endif /* _AL_EXT_CTRL_H */
