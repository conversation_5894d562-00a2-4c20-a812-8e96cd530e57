/*Copyright (c) <2020>, <Huawei Technologies Co., Ltd>
 * All rights reserved.
 * &Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 * 1. Redistributions of source code must retain the above copyright notice, this list of
 * conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice, this list
 * of conditions and the following disclaimer in the documentation and/or other materials
 * provided with the distribution.
 * 3. Neither the name of the copyright holder nor the names of its contributors may be used
 * to endorse or promote products derived from this software without specific prior written permission.
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF
 * USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
 * LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 * THE POSSIBILITY OF SUCH DAMAGE.
 *
 * */

#include <stdio.h>
#include <signal.h>

#if defined(WIN32) || defined(WIN64)
#include "windows.h"
#endif

#include <pthread.h>
#include <sys/types.h>
#include <unistd.h>
#include "hw_type.h"
#include "iota_init.h"
#include "iota_cfg.h"
#include "log_util.h"
#include "json_util.h"
#include "string_util.h"
#include "iota_login.h"
#include "iota_datatrans.h"
#include "string.h"
#include "cJSON.h"
#include "iota_error_type.h"
#include "vsol_mqtt_core.h"
#include "vsol_ems_core.h"
#include "MQTTAsync.h"
#include "subscribe.h"
/* if you want to use syslog,you should do this:
 *
 * #include "syslog.h"
 * #define _SYS_LOG
 *
 * */

char *workPath = ".";
char *gatewayId = NULL;

char *server_addr_ = &mqtt_server_addr;
char *username_ = &mqtt_user_name;
char *password_ = &mqtt_user_passwd;
char *client_id_ = &mqtt_client_id;

int port_ = 8883; //tls MQTTS_PORT
//int port_ = 1883;

int disconnected_ = 1;
int connect_failed_times = 0;
int reconnect_flag = 0;

char *ota_version = NULL;
char *subDeviceId = "XXXX";
void Test_MessageReport(void);
void Test_PropertiesReport(void);
void Test_BatchPropertiesReport(char *deviceId);
void Test_CommandResponse(char *requestId, int result_code);
void Test_PropSetResponse(char *requestId);
void Test_PropGetResponse(char *requestId);
void Test_ReportOTAVersion(void);
void Test_ReportUpgradeStatus(int i, char *version);
void HandleConnectSuccess(EN_IOTA_MQTT_PROTOCOL_RSP *rsp);
void HandleConnectFailure(EN_IOTA_MQTT_PROTOCOL_RSP *rsp);
void HandleConnectionLost(EN_IOTA_MQTT_PROTOCOL_RSP *rsp);
void HandleDisConnectSuccess(EN_IOTA_MQTT_PROTOCOL_RSP *rsp);
void HandleDisConnectFailure(EN_IOTA_MQTT_PROTOCOL_RSP *rsp);
void HandleSubscribesuccess(EN_IOTA_MQTT_PROTOCOL_RSP *rsp);
void HandleSubscribeFailure(EN_IOTA_MQTT_PROTOCOL_RSP *rsp);
void HandlePublishSuccess(EN_IOTA_MQTT_PROTOCOL_RSP *rsp);
void HandlePublishFailure(EN_IOTA_MQTT_PROTOCOL_RSP *rsp);
void HandleMessageDown(EN_IOTA_MESSAGE *rsp);
void HandleUserTopicMessageDown(EN_IOTA_USER_TOPIC_MESSAGE *rsp);
void HandleCommandRequest(EN_IOTA_COMMAND *command);
void HandlePropertiesSet(EN_IOTA_PROPERTY_SET *rsp);
void TimeSleep(int ms);
void HandlePropertiesGet(EN_IOTA_PROPERTY_GET *rsp);
void HandleDeviceShadowRsp(EN_IOTA_DEVICE_SHADOW *rsp);
void HandleEventsDown(EN_IOTA_EVENT *message);
void MyPrintLog(int level, char *format, va_list args);
void SetAuthConfig(void);
void SetMyCallbacks(void);
void Test_ReportJson();
void Test_ReportBinary();
void Test_CmdRspV3();
void Test_UpdateSubDeviceStatus(char *deviceId);
void VSOL_HandleCommandRequest(EN_IOTA_COMMAND *command);

extern int mqtt_ext_server_init();
extern void MqttBase_set_user_properties(char *device_pn_type, unsigned char *mac, char *model, char *project_id, char *group_name, char *device_class, char *status, char *device_id, char *serial_num, char *software_ver);

void TimeSleep(int ms)
{
#if defined(WIN32) || defined(WIN64)
    Sleep(ms);
#else
    usleep(ms * 1000);
#endif
}

int get_connect_status()
{
    int connect_status = !disconnected_;
    return connect_status;
}


long get_memory_usage_kb()
{
    FILE *file = fopen("/proc/self/status", "r");
    if (file == NULL)
    {
        perror("fopen");
        return -1;
    }

    char line[256];
    long memory_usage_kb = -1;

    while (fgets(line, sizeof(line), file))
    {
        if (strncmp(line, "VmRSS:", 6) == 0)
        {
            sscanf(line, "VmRSS: %ld kB", &memory_usage_kb);
            break;
        }
    }

    fclose(file);

    return memory_usage_kb;
}

//------------------------------------------------------Test data report---------------------------------------------------------------------

void Test_MessageReport()
{
    // default topic
    int messageId = IOTA_MessageReport(NULL, "data123", "123", "hello123123123123", NULL, 0);

    // user topic
    //	int messageId = IOTA_MessageReport(NULL, "data123", "123", "hello", "devMsg", 0);
    if (messageId != 0)
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "device_demo: Test_MessageReport() failed, messageId %d\n", messageId);
    }
}

// V3 report
void Test_ReportJson()
{
    int serviceNum = 2; // reported services' totol count
    ST_IOTA_SERVICE_DATA_INFO services[serviceNum];

    //---------------the data of service1-------------------------------
    char *service1 = "{\"Load\":\"6\",\"ImbA_strVal\":\"7\"}";

    services[0].event_time = GetEventTimesStamp(); // if event_time is set to NULL, the time will be the iot-platform's time.
    services[0].service_id = "LTE";
    services[0].properties = service1;

    //---------------the data of service2-------------------------------
    char *service2 = "{\"PhV_phsA\":\"9\",\"PhV_phsB\":\"8\"}";

    services[1].event_time = NULL;
    services[1].service_id = "CPU";
    services[1].properties = service2;

    int messageId = IOTA_PropertiesReportV3(services, serviceNum);
    if (messageId != 0)
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "device_demo: Test_ReportJson() failed, messageId %d\n", messageId);
    }

    MemFree(&services[0].event_time);
}

// V3 report
void Test_ReportBinary()
{
    int messageId = IOTA_BinaryReportV3("1234567890");
    if (messageId != 0)
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "device_demo: Test_ReportBinary() failed, messageId %d\n", messageId);
    }
}

// V3 report
void Test_CmdRspV3()
{
    ST_IOTA_COMMAND_RSP_V3 *rsp = (ST_IOTA_COMMAND_RSP_V3 *)malloc(sizeof(ST_IOTA_COMMAND_RSP_V3));
    if (rsp == NULL)
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "device_demo: Test_CmdRspV3() error, Memory allocation failure\n");
        return;
    }
    rsp->body = "{\"result\":\"0\"}";
    rsp->errcode = 0;
    rsp->mid = 1;

    int messageId = IOTA_CmdRspV3(rsp);
    if (messageId != 0)
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "device_demo: Test_CmdRspV3() failed, messageId %d\n", messageId);
    }
    MemFree(&rsp);
}

void Test_PropertiesReport()
{
    int serviceNum = 2; // reported services' totol count
    ST_IOTA_SERVICE_DATA_INFO services[serviceNum];

    //---------------the data of service1-------------------------------
    char *service1 = "{\"Load\":\"6\",\"ImbA_strVal\":\"7\"}";

    services[0].event_time = GetEventTimesStamp(); // if event_time is set to NULL, the time will be the iot-platform's time.
    services[0].service_id = "parameter";
    services[0].properties = service1;

    //---------------the data of service2-------------------------------
    char *service2 = "{\"PhV_phsA\":\"9\",\"PhV_phsB\":\"8\"}";

    services[1].event_time = NULL;
    services[1].service_id = "analog";
    services[1].properties = service2;

    int messageId = IOTA_PropertiesReport(services, serviceNum, 0);
    if (messageId != 0)
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "device_demo: Test_PropertiesReport() failed, messageId %d\n", messageId);
    }

    MemFree(&services[0].event_time);
}

char vsol_test_buffer[4096] = {"Yehhhhh ha!"};
void Test_VS_PropertiesReport()
{
    int serviceNum = 1; // reported services' totol count
    ST_IOTA_SERVICE_DATA_INFO services[serviceNum];

    cJSON *root, *serviceDatas;
    root = cJSON_CreateObject();

    char *list[] = {"gateway", "ssid2g", "easyMeshDeviceName", "deviceType", "wanTotalRate", "wlanCliInfo"};
    // append_DataList2JSON(root, list, sizeof(list)/sizeof(list[0]));
    // cJSON_AddStringToObject(root, "Content", vsol_test_buffer);
    // cJSON_AddStringToObject(root, "devHwVer", "V1.0");
    // cJSON_AddStringToObject(root, "devSwVer", "V4.4.1b");
    // cJSON_AddStringToObject(root, "devBuildDate", "20210226-1630");
    // cJSON_AddStringToObject(root, "devMacAddr", "0015c5793117");

    char *properties;
    properties = cJSON_Print(root);
    PrintfLog(EN_LOG_LEVEL_INFO, "payload=%s\n", properties);
    cJSON_Delete(root);

    //---------------the data of service1-------------------------------
    // char *service1 =
    // "{devName:HG3610ACM-B,devHwVer:V1.0,devSwVer:V4.4.1a,devBuildDate:20210226-1630,devMacAddr:0015c5793117}";

    services[0].event_time = NULL; // GetEventTimesStamp(); //if event_time is set to NULL, the time will be the iot-platform's time.
    services[0].service_id = "Mytest";
    services[0].properties = properties;
    // services[0].properties = service1;

    int messageId = IOTA_PropertiesReport(services, serviceNum, 0);
    if (messageId != 0)
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "device_demo: Test_PropertiesReport() failed, messageId %d\n", messageId);
    }

    if (properties == NULL)
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "properties=NULL\n");
    }
    else
    {
        free(properties);
    }

    MemFree(&services[0].event_time);
}

void Test_BatchPropertiesReport(char *deviceId)
{
    int deviceNum = 1;                           // the number of sub devices
    ST_IOTA_DEVICE_DATA_INFO devices[deviceNum]; // Array of structures to be reported by sub devices
    int serviceList[deviceNum]; // Corresponding to the number of services to be reported for each sub device
    serviceList[0] = 2;         // device 1 reports two services
    //	serviceList[1] = 1;		  //device 2 reports one service

    char *device1_service1 = "{\"BUGUYIQI\":\"1\",\"WUYJLDFHQI\":\"3\"}"; // must be json

    char *device1_service2 = "{\"DALING\":\"2\",\"TESTV\":\"4\"}"; // must be json

    devices[0].device_id = deviceId;
    devices[0].services[0].event_time = GetEventTimesStamp();
    devices[0].services[0].service_id = "parameter";
    devices[0].services[0].properties = device1_service1;

    devices[0].services[1].event_time = GetEventTimesStamp();
    devices[0].services[1].service_id = "analog";
    devices[0].services[1].properties = device1_service2;

    //	char *device2_service1 = "{\"AA\":\"2\",\"BB\":\"4\"}";
    //	devices[1].device_id = "subDevices22222";
    //	devices[1].services[0].event_time = "d2s1";
    //	devices[1].services[0].service_id = "device2_service11111111";
    //	devices[1].services[0].properties = device2_service1;

    int messageId = IOTA_BatchPropertiesReport(devices, deviceNum, serviceList, 0);
    if (messageId != 0)
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "device_demo: Test_BatchPropertiesReport() failed, messageId %d\n", messageId);
    }

    MemFree(&devices[0].services[0].event_time);
    MemFree(&devices[0].services[1].event_time);
}

void Test_UpdateSubDeviceStatus(char *deviceId)
{
    int deviceNum = 1;
    ST_IOTA_DEVICE_STATUSES device_statuses;
    device_statuses.event_time = GetEventTimesStamp();
    device_statuses.device_statuses[0].device_id = deviceId;
    device_statuses.device_statuses[0].status = ONLINE;
    int messageId = IOTA_UpdateSubDeviceStatus(&device_statuses, deviceNum);
    if (messageId != 0)
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "device_demo: Test_UpdateSubDeviceStatus() failed, messageId %d\n", messageId);
    }
    MemFree(&device_statuses.event_time);
}

void Test_CommandResponse(char *requestId, int result_code)
{
    char *pcCommandRespense = "{\"SupWhPPP\": \"aaa\"}"; // in service accumulator
    // int result_code = 0;
    char *response_name = "CMD_PRINT_RESULT";

    int messageId = IOTA_CommandResponse(requestId, result_code, response_name, pcCommandRespense);
    if (messageId != 0)
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "device_demo: Test_CommandResponse() failed, messageId %d\n", messageId);
    }
}

void Test_PropSetResponse(char *requestId)
{
    int messageId = IOTA_PropertiesSetResponse(requestId, 0, "success");
    if (messageId != 0)
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "device_demo: Test_PropSetResponse() failed, messageId %d\n", messageId);
    }
}

void Test_PropGetResponse(char *requestId)
{
    int serviceNum = 2;
    ST_IOTA_SERVICE_DATA_INFO serviceProp[serviceNum];

    char *property = "{\"Load\":\"5\",\"ImbA_strVal\":\"6\"}";

    serviceProp[0].event_time = GetEventTimesStamp();
    serviceProp[0].service_id = "parameter";
    serviceProp[0].properties = property;

    char *property2 = "{\"PhV_phsA\":\"2\",\"PhV_phsB\":\"4\"}";

    serviceProp[1].event_time = GetEventTimesStamp();
    serviceProp[1].service_id = "analog";
    serviceProp[1].properties = property2;

    int messageId = IOTA_PropertiesGetResponse(requestId, serviceProp, serviceNum);
    if (messageId != 0)
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "device_demo: Test_PropGetResponse() failed, messageId %d\n", messageId);
    }

    MemFree(&serviceProp[0].event_time);
    MemFree(&serviceProp[1].event_time);
}

void ReportOTAVersion()
{
    ST_IOTA_OTA_VERSION_INFO otaVersion;
    size_t max_length = 100;
    char sw_version[max_length];
    char fw_version[max_length];
    memset(sw_version, 0, max_length);
    memset(fw_version, 0, max_length);

    otaVersion.event_time = NULL;
    otaVersion.sw_version = sw_version;
    otaVersion.fw_version = fw_version;
    otaVersion.object_device_id = NULL;
    // getDeviceVersion(&otaVersion, max_length);

    int messageId = IOTA_OTAVersionReport(otaVersion);
    /*if (messageId != 0) {
        PrintfLog(EN_LOG_LEVEL_ERROR, "device_demo: Test_ReportOTAVersion() failed, messageId %d\n", messageId);
    }*/
}

void Test_ReportUpgradeStatus(int i, char *version)
{
    ST_IOTA_UPGRADE_STATUS_INFO statusInfo;
    if (i == 0)
    {
        statusInfo.description = "success";
        statusInfo.progress = 100;
        statusInfo.result_code = 0;
        statusInfo.version = version;
    }
    else
    {
        statusInfo.description = "failed";
        statusInfo.result_code = 1;
        statusInfo.progress = 0;
        statusInfo.version = version;
    }

    statusInfo.event_time = NULL;
    statusInfo.object_device_id = NULL;

    int messageId = IOTA_OTAStatusReport(statusInfo);
    if (messageId != 0)
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "device_demo: Test_ReportUpgradeStatus() failed, messageId %d\n", messageId);
    }
}

//--------------------------------------------------------------------------------------------------------------------------------------------------
static void whether_restart_process()
{
    if (disconnected_ == 1)
    {
        return;
    }

    printf("!MQTT connection failed while in connected status...\n");
    return; /*add by ZhiYang on 20240809*/

    char path[PATH_MAX];
    ssize_t len = readlink("/proc/self/exe", path, sizeof(path) - 1);

    if (len == -1)
    {
        perror("readlink");
        exit(EXIT_FAILURE);
    }

    path[len] = '\0';

    char *const args[] = {path, NULL};

    if (execv(path, args) == -1)
    {
        perror("execv");
        exit(EXIT_FAILURE);
    }
}

void HandleConnectSuccess(EN_IOTA_MQTT_PROTOCOL_RSP *rsp)
{
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: handleConnectSuccess(), login success\n");
    if (vsol_mqtt_mode == VS_MQTT_EMS_MODE)
    {
        vsol_ems_subscribe_init();
    }
    else
    { // VS_MQTT_VCCM_MODE
        vsol_subscribe_init();
    }
    disconnected_ = 0;
    set_connect_status("success");
    // subscribeInit();
}

void HandleConnectFailure(EN_IOTA_MQTT_PROTOCOL_RSP *rsp)
{
    PrintfLog(EN_LOG_LEVEL_ERROR, "device_demo: HandleConnectFailure() error, messageId %d, code %d, messsage %s\n", rsp->mqtt_msg_info->messageId, rsp->mqtt_msg_info->code, rsp->message);
    // judge if the network is available etc. and login again
    //...
    PrintfLog(EN_LOG_LEVEL_ERROR, "device_demo: HandleConnectFailure() login again\n");

    whether_restart_process();
    disconnected_ = 1;
    set_connect_status("fail");

    connect_failed_times++;
    if (connect_failed_times < 10)
    {
        TimeSleep(50);
    }
    else if (connect_failed_times > 10 && connect_failed_times < 50)
    {
        TimeSleep(2500);
    }
    else
    {
        TimeSleep(10000);
    }

    mqtt_set_user_properities();
    int ret = IOTA_Connect();
    PrintfLog(EN_LOG_LEVEL_ERROR, "Connect mqtt server, memory usage: %dKB\n", get_memory_usage_kb());
    if (ret != 0)
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "device_demo: HandleAuthFailure() error, login again failed, result %d\n", ret);
    }
}

void HandleConnectionLost(EN_IOTA_MQTT_PROTOCOL_RSP *rsp)
{
    PrintfLog(EN_LOG_LEVEL_ERROR, "device_demo: HandleConnectionLost() error, messageId %d, code %d, messsage %s\n", rsp->mqtt_msg_info->messageId, rsp->mqtt_msg_info->code, rsp->message);
    // judge if the network is available etc. and login again
    //...

    whether_restart_process();
    disconnected_ = 1;
    set_connect_status("fail");

    if (connect_failed_times < 10)
    {
        TimeSleep(50);
    }
    else if (connect_failed_times > 10 && connect_failed_times < 50)
    {
        TimeSleep(2500);
    }
    else
    {
        TimeSleep(10000);
    }

    mqtt_set_user_properities();
    int ret = IOTA_Connect();
    if (ret != 0)
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "device_demo: HandleConnectionLost() error, login again failed, result %d\n", ret);
    }
}

void HandleDisConnectSuccess(EN_IOTA_MQTT_PROTOCOL_RSP *rsp)
{
    disconnected_ = 1;
    set_connect_status("fail");
    printf("device_demo: handleLogoutSuccess, login again\n");
    printf("device_demo: HandleDisConnectSuccess(), messageId %d, code %d, messsage %s\n", rsp->mqtt_msg_info->messageId, rsp->mqtt_msg_info->code, rsp->message);
}

void HandleDisConnectFailure(EN_IOTA_MQTT_PROTOCOL_RSP *rsp)
{
    PrintfLog(EN_LOG_LEVEL_WARNING, "device_demo: HandleDisConnectFailure() warning, messageId %d, code %d, messsage %s\n", rsp->mqtt_msg_info->messageId, rsp->mqtt_msg_info->code, rsp->message);
}

void HandleSubscribesuccess(EN_IOTA_MQTT_PROTOCOL_RSP *rsp)
{
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleSubscribesuccess() messageId %d\n", rsp->mqtt_msg_info->messageId);
    vsol_bind_mqtt_user(); /*Change by ZhiYang on 20240909*/
}

void HandleSubscribeFailure(EN_IOTA_MQTT_PROTOCOL_RSP *rsp)
{
    PrintfLog(EN_LOG_LEVEL_WARNING, "device_demo: HandleSubscribeFailure() warning, messageId %d, code %d, messsage %s\n", rsp->mqtt_msg_info->messageId, rsp->mqtt_msg_info->code, rsp->message);
}

void HandlePublishSuccess(EN_IOTA_MQTT_PROTOCOL_RSP *rsp)
{
    // PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandlePublishSuccess() messageId %d\n", rsp->mqtt_msg_info->messageId);
}

void HandlePublishFailure(EN_IOTA_MQTT_PROTOCOL_RSP *rsp)
{
    PrintfLog(EN_LOG_LEVEL_WARNING, "device_demo: HandlePublishFailure() warning, messageId %d, code %d, messsage %s\n", rsp->mqtt_msg_info->messageId, rsp->mqtt_msg_info->code, rsp->message);
}

//-------------------------------------------handle message arrived------------------------------------------------------------------------------

void HandleMessageDown(EN_IOTA_MESSAGE *rsp)
{
    if (rsp == NULL)
    {
        return;
    }
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleMessageDown(), content %s\n", rsp->content);
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleMessageDown(), id %s\n", rsp->id);
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleMessageDown(), name %s\n", rsp->name);
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleMessageDown(), object_device_id %s\n", rsp->object_device_id);
}

void HandlePropertiesSet(EN_IOTA_PROPERTY_SET *rsp)
{
    if (rsp == NULL)
    {
        return;
    }
    // PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandlePropertiesSet(), messageId %d \n", rsp->mqtt_msg_info->messageId); 
	// PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandlePropertiesSet(), request_id %s\n", rsp->request_id); 
	// PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandlePropertiesSet(), object_device_id %s \n", rsp->object_device_id);

    int i = 0;
    while (rsp->services_count > 0)
    {
        // PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandlePropertiesSet(), service_id %s \n", rsp->services[i].service_id);
		// PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandlePropertiesSet(), properties %s \n", rsp->services[i].properties);
        if (rsp->services[i].properties[0])
        {
            printf("\nrsp->services[i].properties = %s\n", rsp->services[i].properties);
            // parse_Data2Mib(rsp->services[i].properties);
        }
        // JSON_GetStringFromObject
        rsp->services_count--;
        i++;
    }

    Test_PropSetResponse(rsp->request_id); // response
}

void HandlePropertiesGet(EN_IOTA_PROPERTY_GET *rsp)
{
    if (rsp == NULL)
    {
        return;
    }
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandlePropertiesSet(), messageId %d \n", rsp->mqtt_msg_info->messageId);
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandlePropertiesSet(), request_id %s \n", rsp->request_id);
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandlePropertiesSet(), object_device_id %s \n", rsp->object_device_id);
    printf("Get %s context\n", rsp->mqtt_msg_info->context);
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandlePropertiesSet(), service_id %s \n", rsp->service_id);

    Test_PropGetResponse(rsp->request_id); // response
}

void HandleDeviceShadowRsp(EN_IOTA_DEVICE_SHADOW *rsp)
{
    if (rsp == NULL)
    {
        return;
    }
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleDeviceShadowRsp(), messageId %d \n", rsp->mqtt_msg_info->messageId);
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleDeviceShadowRsp(), request_id %s \n", rsp->request_id);
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleDeviceShadowRsp(), object_device_id %s \n", rsp->object_device_id);

    int i = 0;
    while (rsp->shadow_data_count > 0)
    {
        PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleDeviceShadowRsp(), service_id %s \n", rsp->shadow[i].service_id);
        PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleDeviceShadowRsp(), desired properties %s \n", rsp->shadow[i].desired_properties);
        PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleDeviceShadowRsp(), reported properties %s \n", rsp->shadow[i].reported_properties);
        PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleDeviceShadowRsp(), version    %d \n", rsp->shadow[i].version);
        rsp->shadow_data_count--;
        i++;
    }
}

void HandleUserTopicMessageDown(EN_IOTA_USER_TOPIC_MESSAGE *rsp)
{
    if (rsp == NULL || strstr(rsp->topic_para, "/response/"))
    {
        PrintfLog(EN_LOG_LEVEL_INFO, "!Drop Repeated processing\n");
        return;
    }

    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleUserTopicMessageDown(), topic_para %s\n", rsp->topic_para);
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleUserTopicMessageDown(), content %s\n", rsp->content);
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleUserTopicMessageDown(), id %s\n", rsp->id);
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleUserTopicMessageDown(), name %s\n", rsp->name);
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleUserTopicMessageDown(), object_device_id %s vsol_mqtt_mode:%d\n", rsp->object_device_id, vsol_mqtt_mode);
    if (vsol_mqtt_mode == VS_MQTT_EMS_MODE)
    {
        vsol_topic_ems_msg_process(rsp);
    }
    else
    { // VS_MQTT_VCCM_MODE
        vsol_topic_msg_process(rsp);
    }
    return;
}

void HandleCommandRequest(EN_IOTA_COMMAND *command)
{
    if (command == NULL)
    {
        return;
    }

    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleCommandRequest(), messageId %d\n", command->mqtt_msg_info->messageId);
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleCommandRequest(), object_device_id %s\n", command->object_device_id);
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleCommandRequest(), service_id %s\n", command->service_id);
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleCommandRequest(), command_name %s\n", command->command_name);
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleCommandRequest(), paras %s\n", command->paras);
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleCommandRequest(), request_id %s\n", command->request_id);

    // Test_CommandResponse(command->request_id); //response command
}

void VSOL_HandleCommandRequest(EN_IOTA_COMMAND *command)
{
    if (command == NULL)
    {
        return;
    }

    printf("Command Device ID is : %s\n", command->object_device_id);
    if (!strcmp(command->command_name, "CMD_PRINT"))
    {
        memset(vsol_test_buffer, 0, sizeof(vsol_test_buffer));
        printf("What is in command->paras : \n%s\n", command->paras);
        JSON *tmp;
        tmp = JSON_Parse(command->paras);
        printf("tmp->type = %d\n", tmp->type);
        printf("tmp->string = %s\n", tmp->string);
        printf("tmp->valuestring = %s\n", tmp->valuestring);
        printf("tmp->valueint = %d\n", tmp->valueint);
        strncpy(vsol_test_buffer, JSON_GetStringFromObject(tmp, "Content", ""), sizeof(vsol_test_buffer));
        cJSON_Delete(tmp);
        Test_CommandResponse(command->request_id, 0);
        return;
    }
    Test_CommandResponse(command->request_id, -1);
    printf("Something wrong in handle command!\n");
    return;
}


void HandleEventsDown(EN_IOTA_EVENT *message)
{
    if (message == NULL)
    {
        return;
    }

    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleEventsDown(), messageId %d\n", message->mqtt_msg_info->messageId);
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleEventsDown(), services_count %d\n", message->services_count);
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleEventsDown(), code %s\n", message->object_device_id);
    int i = 0;
    while (message->services_count > 0)
    {
        printf("servie_id: %d \n", message->services[i].servie_id);
        printf("event_time: %s \n", message->services[i].event_time);
        printf("event_type: %d \n", message->services[i].event_type);

        // sub device manager
        if (message->services[i].servie_id == EN_IOTA_EVENT_SUB_DEVICE_MANAGER)
        {
            printf("version: %lld \n", message->services[i].paras->version);

            int j = 0;
            while (message->services[i].paras->devices_count > 0)
            {
                PrintfLog(EN_LOG_LEVEL_INFO,"device_demo: HandleEventsDown(), parent_device_id: %s \n", message->services[i].paras->devices[j].parent_device_id);
                PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleEventsDown(), device_id: %s \n", message->services[i].paras->devices[j].device_id);
                PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleEventsDown(), node_id: %s \n", message->services[i].paras->devices[j].node_id);

                // add a sub device
                if (message->services[i].event_type == EN_IOTA_EVENT_ADD_SUB_DEVICE_NOTIFY)
                {
                    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleEventsDown(), name: %s \n", message->services[i].paras->devices[j].name);
                    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleEventsDown(), manufacturer_id: %s \n", message->services[i].paras->devices[j].manufacturer_id);
                    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleEventsDown(), product_id: %s \n", message->services[i].paras->devices[j].product_id);

                    Test_UpdateSubDeviceStatus(message->services[i].paras->devices[j].device_id); // report status of the sub device
                    Test_BatchPropertiesReport(message->services[i].paras->devices[j].device_id); // report data of the sub device
                }
                else if (message->services[i].event_type == EN_IOTA_EVENT_DELETE_SUB_DEVICE_NOTIFY)
                { // delete a sub device
                    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: HandleEventsDown(), the sub device is deleted: %s\n", message->services[i].paras->devices[j].device_id);
                }

                j++;
                message->services[i].paras->devices_count--;
            }
        }
        else if (message->services[i].servie_id == EN_IOTA_EVENT_OTA)
        {
            if (message->services[i].event_type == EN_IOTA_EVENT_VERSION_QUERY)
            {
                // report OTA version
                ReportOTAVersion();
            }

            if (message->services[i].event_type == EN_IOTA_EVENT_FIRMWARE_UPGRADE || message->services[i].event_type == EN_IOTA_EVENT_SOFTWARE_UPGRADE)
            {
                printf("md5 check start \n");
                usleep(100000);
                // check md5
                /*char pkg_md5 = "yourMd5"; //the md5 value of your ota package
                if (strcmp(pkg_md5, message->services[i].ota_paras->sign)) {
                        printf("md5 error in HandleEventsDown \n");
                        //report failed status
                        Test_ReportUpgradeStatus(-1, message->services[i].ota_paras->version);
                }*/

                // start to receive packages and firmware_upgrade or software_upgrade
                if (IOTA_GetOTAPackages(message->services[i].ota_paras->url, message->services[i].ota_paras->access_token, 1000) == 0)
                {
                    usleep(3000 * 1000);
                    // report successful upgrade status
                    Test_ReportUpgradeStatus(0, message->services[i].ota_paras->version);
                }
                else
                {
                    // report failed status
                    Test_ReportUpgradeStatus(-1, message->services[i].ota_paras->version);
                }
            }
        }

        i++;
        message->services_count--;
    }
}

void mqtt_set_user_properities()
{
    char device_pn_type[64] = {0};
    char model[64] = {0};
    char project_id[64] = {0};
    char group_name[64] = {0};
    char device_class[64] = {0};
    char status[64] = {0};
    char device_id[64] = {0};
    char serial_num[32] = {0};
    char software_ver[32] = {0};
    unsigned char mac[6] = {0};
    int ret = -1;

    ret = platform_get_mqtt_properties(device_pn_type, mac, model, project_id, group_name, device_class, status, device_id, serial_num, software_ver);
    PrintfLog(EN_LOG_LEVEL_INFO, "device_demo: mqtt_set_user_properities(), ret : %d\n", ret);
    if (0 == ret)
    {
        PrintfLog(EN_LOG_LEVEL_INFO, "device_pn_type : %s\n", device_pn_type);
        PrintfLog(EN_LOG_LEVEL_INFO, "mac : %02x%02x%02x%02x%02x%02x\n", mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
        PrintfLog(EN_LOG_LEVEL_INFO, "model : %s\n", model);
        PrintfLog(EN_LOG_LEVEL_INFO, "project_id : %s\n", project_id);
        PrintfLog(EN_LOG_LEVEL_INFO, "group_name : %s\n", group_name);
        PrintfLog(EN_LOG_LEVEL_INFO, "device_class : %s\n", device_class);
        PrintfLog(EN_LOG_LEVEL_INFO, "status : %s\n", status);
        PrintfLog(EN_LOG_LEVEL_INFO, "device_id : %s\n", device_id);
        PrintfLog(EN_LOG_LEVEL_INFO, "serial_num : %s\n", serial_num);
        PrintfLog(EN_LOG_LEVEL_INFO, "software_ver : %s\n", software_ver);
        MqttBase_set_user_properties(device_pn_type, mac, model, project_id, group_name, device_class, status, device_id, serial_num, software_ver);
    }
}
//----------------------------------------------------------------------------------------------------------------------------------------------

void SetAuthConfig()
{
    IOTA_ConfigSetStr(EN_IOTA_CFG_MQTT_ADDR, server_addr_);
    IOTA_ConfigSetUint(EN_IOTA_CFG_MQTT_PORT, port_);
    IOTA_ConfigSetStr(EN_IOTA_CFG_DEVICEID, username_);
    IOTA_ConfigSetStr(EN_IOTA_CFG_DEVICESECRET, password_);
    IOTA_ConfigSetUint(EN_IOTA_CFG_AUTH_MODE, EN_IOTA_CFG_AUTH_MODE_SECRET);

    IOTA_ConfigSetStr(EN_IOTA_CFG_CLIENT_ID, client_id_);

    IOTA_ConfigSetStr(EN_IOTA_CFG_RESET_SECRET_IN_PROGRESS, "0"); /*add by ZhiYang on 20240822 for reload server addr*/

    /**
     * Configuration is required in certificate mode:
     *
     * IOTA_ConfigSetUint(EN_IOTA_CFG_AUTH_MODE, EN_IOTA_CFG_AUTH_MODE_CERT);
     * IOTA_ConfigSetUint(EN_IOTA_CFG_PRIVATE_KEY_PASSWORD, "yourPassword");
     *
     */

#ifdef _SYS_LOG
    IOTA_ConfigSetUint(EN_IOTA_CFG_LOG_LOCAL_NUMBER, LOG_LOCAL7);
    IOTA_ConfigSetUint(EN_IOTA_CFG_LOG_LEVEL, LOG_INFO);
#endif

    mqtt_set_user_properities(); /*Add by ZhiYang on 20241108*/
}


void SetMyCallbacks()
{
    IOTA_SetProtocolCallback(EN_IOTA_CALLBACK_CONNECT_SUCCESS, HandleConnectSuccess);
    IOTA_SetProtocolCallback(EN_IOTA_CALLBACK_CONNECT_FAILURE, HandleConnectFailure);

    IOTA_SetProtocolCallback(EN_IOTA_CALLBACK_DISCONNECT_SUCCESS, HandleDisConnectSuccess);
    IOTA_SetProtocolCallback(EN_IOTA_CALLBACK_DISCONNECT_FAILURE, HandleDisConnectFailure);
    IOTA_SetProtocolCallback(EN_IOTA_CALLBACK_CONNECTION_LOST, HandleConnectionLost);

    IOTA_SetProtocolCallback(EN_IOTA_CALLBACK_SUBSCRIBE_SUCCESS, HandleSubscribesuccess);
    IOTA_SetProtocolCallback(EN_IOTA_CALLBACK_SUBSCRIBE_FAILURE, HandleSubscribeFailure);

    IOTA_SetProtocolCallback(EN_IOTA_CALLBACK_PUBLISH_SUCCESS, HandlePublishSuccess);
    IOTA_SetProtocolCallback(EN_IOTA_CALLBACK_PUBLISH_FAILURE, HandlePublishFailure);
    // IOTA_SetMessageCallback(HandleUserTopicMessageDown);

    IOTA_SetUserTopicMsgCallback(HandleUserTopicMessageDown);

    // IOTA_SetCmdCallback(VSOL_HandleCommandRequest);
    // IOTA_SetPropSetCallback(HandlePropertiesSet);
    // IOTA_SetPropGetCallback(HandlePropertiesGet);
    // IOTA_SetEventCallback(HandleEventsDown);
    // IOTA_SetShadowGetCallback(HandleDeviceShadowRsp);
}

unsigned char log_open = 0;      // Set whether to enable log and output directly to standard output
unsigned char log_file_open = 0; // Set whether to enable log file
static unsigned char log_level = 0;

void MyPrintLog(int level, char *format, va_list args)
{
    static FILE *log_file = NULL;
    if (level > log_level)
    {
        return;
    }
	
    if (log_open)
    {
        vprintf(format, args);
    }
	
    if (log_file_open == 0)
    {
        return;
    }

    if (log_file == NULL)
    {
        log_file = fopen(MQTT_LOG_SAVE_FILE, "a");
        if (log_file == NULL)
        {
            printf("[%s][%d] open mqtt_log.txt failed\r\n", __FUNCTION__, __LINE__);
            return;
        }
    }

    vfprintf(log_file, format, args);
    fflush(log_file);
}

void check_log_open(void)
{
    char buf[16] = {0};

    FILE *fp = fopen(MQTT_LOG_CTRL_FINE_NAME, "r");
    if (fp)
    {
        fgets(buf, sizeof(buf), fp);
        fclose(fp);
    }
    else
    {
        if (access(MQTT_LOG_CTRL_FINE_NAME, F_OK))
        {            
            fp = fopen(MQTT_LOG_CTRL_FINE_NAME, "w");
            if (fp)
            {
                fputs("0\n", fp);
                fclose(fp);
            }
            else
            {
                printf("[%s][%d] Failed to open file:%s", __FUNCTION__, __LINE__, MQTT_LOG_CTRL_FINE_NAME);
            }
        }
    }

    printf("[%s][%d] mqtt_log_status help, args:\r\n", __FUNCTION__, __LINE__);
    printf("[%s][%d]   [file]: log save to file:%s\r\n", __FUNCTION__, __LINE__, MQTT_LOG_SAVE_FILE);
    printf("[%s][%d]   [tty]:  log output to tty\r\n", __FUNCTION__, __LINE__);
    printf("[%s][%d]   [0-8]:  set loglevel, DISABLE = 0,DEBUG = 7, INFO = 6, WARNING = 4, ERROR = 3\r\n", __FUNCTION__, __LINE__);

    if (strncasecmp(buf, "file", strlen("file")) == 0)
    {
        log_file_open = 1;
        printf("MQTT log output to file\r\n");
    }
    else if (strncasecmp(buf, "tty", strlen("tty")) == 0)
    {
        log_file_open = 0;
        printf("MQTT log output to tty\r\n");
    }
    else
    {
        log_level = atoi(buf);
        if (log_level >= EN_LOG_LEVEL_ERROR)
        {
            log_open = 1;
        }
        else
        {
            log_open = 0;
        }

        printf("[%s][%d] MQTT change log level:%d\r\n", __FUNCTION__, __LINE__, log_level);
    }

    if (log_open)
    {
        MQTTAsync_setTraceLevel(MQTTASYNC_TRACE_MINIMUM);
    }
    else
    {
        MQTTAsync_setTraceLevel(MQTTASYNC_TRACE_FATAL);
    }
}

extern int authMode;

void subscribeInit()
{
    char *subscribeList[] = {
            "deviceInfo/commands/#",
            "wanTotalRate/commands/#",
#ifdef CONFIG_USER_WLAN_CLI_MGM
            "wlanCliInfo/commands/#",
            "wlanRate/set/#",
            "wlanTime/set/#",
            "wlanStatus/set/#",
#endif
            "upgrade/set/#",
            "reboot/set/#",
            "refactory/set/#",
            "adPsw/set/#",
            "wlanSsidPswd/set/#",
            "adPsw/commands/#",
            "wlanSsidPswd/commands/#",
    };
    size_t listNum = sizeof(subscribeList) / sizeof(subscribeList[0]);
    unsigned int i;
    for (i = 0; i < listNum; i++)
    {
        SubscribeUserTopicVsol(subscribeList[i]);
    }
    return;
}

void mqtt_paho_printf(enum MQTTASYNC_TRACE_LEVELS level, char *message)
{
    printf("\033[40;32;2m[level-%d]: %s\033[0m\n", level, message);
}

int mqtt_reconnect_cfg(int flag)
{
    if (!flag)
    {
        if (disconnected_ == 0)
        {
            printf("disconnect mqtt now\r\n");
            IOTA_DisConnect();
        }
    }

    reconnect_flag = flag;
    return 0;
}

int main(int argc, char **argv)
{
    int mqtt_server_cfg;

    printf("MQTT start...\n");
    mqtt_server_cfg = mqttBasicConfigInit();

#if defined(_DEBUG)
    setvbuf(stdout, NULL, _IONBF, 0); // in order to make the console log printed immediately at debug mode
#endif

    IOTA_SetPrintLogCallback(MyPrintLog); // Debug CallBack
    MQTTAsync_setTraceCallback(mqtt_paho_printf);

    check_log_open();

    if (IOTA_Init(workPath) < 0)
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "device_demo: IOTA_Init() error, init failed\n");
        return 1;
    }

    SetMyCallbacks();

    // see handleLoginSuccess and handleLoginFailure for login result
    if (mqtt_server_cfg)
    {
        SetAuthConfig();
        int ret = IOTA_Connect();
        if (ret != 0)
        {
            PrintfLog(EN_LOG_LEVEL_ERROR, "device_demo: IOTA_Auth() error, Auth failed, result %d\n", ret);
        }
    }

    // int count = 0;

    /*Add by ZhiYang on 20240724*/
    mqtt_ext_server_init();
    // ReportOTAVersion();
    // subscribeInit();

    while (1)
    {
        if (reconnect_flag)
        {
            if (mqttServerInit())
            {
                if (disconnected_ == 0)
                {
                    printf("disconnect mqtt first\r\n");
                    IOTA_DisConnect();
                    sleep(5);
                }
                SetAuthConfig();
                IOTA_Connect();
            }
            reconnect_flag = 0;
        }

        sleep(1);
    }

    return 0;
}
