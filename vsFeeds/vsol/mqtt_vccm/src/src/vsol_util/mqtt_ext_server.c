#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <sys/un.h>
#include <sys/time.h>
#include <sys/stat.h>
#include <stdio.h>
#include <stdlib.h>
#include <pthread.h>
#include <semaphore.h>
#include <arpa/inet.h>
#include <linux/netlink.h>
#include <linux/rtnetlink.h>
#include <errno.h>
#include <fcntl.h>
#include <net/if.h>
#include <sys/ioctl.h>

#define MQTT_EXT_UDP_SUPPORT
#include "mqtt_ext_msg.h"
#include "base.h"
#include "log_util.h"
#include "platform_mib_api.h"
#include "pair_handle.h"
#include "mqtt_util.h"
#include "common_util.h"

#define MQTT_MAX_CLIENTS 10
#define NETLINK_MSG_SIZE 8192

// 网络接口监控相关变量
static int netlink_monitor_fd = -1;
static char target_interface[IFNAMSIZ] = "br-lan";
static int last_interface_index = -1;
static int interface_restart_detected = 0;

typedef struct
{
    timer_t timer_id;
    void (*call_back)(void *);
    void *argv;
} info_timer_s;

static void _mqtt_timer_handle(union sigval s)
{
    info_timer_s *t;

    t = (info_timer_s *)s.sival_ptr;

    timer_delete(t->timer_id);
    (*(t->call_back))(t->argv);

    free(t);
}

int mqtt_create_timer(void (*call_back_fun)(void *), void *argv, int delay_ms)
{
    struct sigevent se;
    struct itimerspec its;
    timer_t timer_id;
    info_timer_s *t;

    t = malloc(sizeof(info_timer_s));
    if (t == NULL)
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "%s malloc failed\n", __FUNCTION__);
        return -1;
    }

    memset(&se, 0, sizeof(se));
    se.sigev_notify = SIGEV_THREAD;
    se.sigev_notify_function = _mqtt_timer_handle;
    se.sigev_value.sival_ptr = t;

    if (-1 == timer_create(CLOCK_REALTIME, &se, &timer_id))
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "%s create timer failed\r\n", __FUNCTION__);
        return 0;
    }

    t->call_back = call_back_fun;
    t->argv = argv;
    t->timer_id = timer_id;

    its.it_value.tv_sec = delay_ms / 1000;
    its.it_value.tv_nsec = (delay_ms % 1000) * 1000000;
    its.it_interval.tv_sec = 0;
    its.it_interval.tv_nsec = 0;

    if (0 != timer_settime(timer_id, 0, &its, NULL))
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "%s set timer failed\r\n", __FUNCTION__);
        timer_delete(timer_id);
        return 0;
    }

    return 1;
}

static void mqtt_reboot_timeout(void *argv)
{
    sleep(2);
    system("reboot");
}

void mqtt_reboot_handle(void)
{
    mqtt_udp_send_reboot_cmd();
    if (1 != mqtt_create_timer(mqtt_reboot_timeout, NULL, 2000))
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "Failed to create reboot timeout timer\n");
    }
}

// 获取网络接口索引
static int get_interface_index(const char *ifname)
{
    struct ifreq ifr;
    int sock = socket(AF_INET, SOCK_DGRAM, 0);
    if (sock < 0) {
        return -1;
    }
    
    memset(&ifr, 0, sizeof(ifr));
    strncpy(ifr.ifr_name, ifname, IFNAMSIZ - 1);
    
    if (ioctl(sock, SIOCGIFINDEX, &ifr) < 0) {
        close(sock);
        return -1;
    }
    
    close(sock);
    return ifr.ifr_ifindex;
}

// 创建网络接口监控套接字
static int create_netlink_monitor(void)
{
    struct sockaddr_nl addr;
    
    netlink_monitor_fd = socket(AF_NETLINK, SOCK_RAW, NETLINK_ROUTE);
    if (netlink_monitor_fd < 0) {
        PrintfLog(EN_LOG_LEVEL_ERROR, "Failed to create netlink socket: %s\n", strerror(errno));
        return -1;
    }
    
    memset(&addr, 0, sizeof(addr));
    addr.nl_family = AF_NETLINK;
    addr.nl_pid = getpid();
    addr.nl_groups = RTNLGRP_LINK;  // 监听链接状态变化
    
    if (bind(netlink_monitor_fd, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
        PrintfLog(EN_LOG_LEVEL_ERROR, "Failed to bind netlink socket: %s\n", strerror(errno));
        close(netlink_monitor_fd);
        netlink_monitor_fd = -1;
        return -1;
    }
    
    // 设置非阻塞模式
    int flags = fcntl(netlink_monitor_fd, F_GETFL, 0);
    fcntl(netlink_monitor_fd, F_SETFL, flags | O_NONBLOCK);
    
    PrintfLog(EN_LOG_LEVEL_INFO, "Netlink monitor created successfully\n");
    return 0;
}

// 处理网络接口状态变化
static void handle_interface_change(struct nlmsghdr *nlh)
{
    struct ifinfomsg *ifi = (struct ifinfomsg*)NLMSG_DATA(nlh);
    struct rtattr *tb[IFLA_MAX + 1];
    char ifname[IFNAMSIZ];
    int ifname_len = 0;
    
    // 解析接口名称
    struct rtattr *rta = IFLA_RTA(ifi);
    int len = NLMSG_PAYLOAD(nlh, sizeof(struct ifinfomsg));
    
    while (RTA_OK(rta, len)) {
        if (rta->rta_type == IFLA_IFNAME) {
            ifname_len = RTA_PAYLOAD(rta);
            if (ifname_len < IFNAMSIZ) {
                strncpy(ifname, RTA_DATA(rta), ifname_len);
                ifname[ifname_len] = '\0';
            }
        }
        rta = RTA_NEXT(rta, len);
    }
    
    // 检查是否是目标接口
    if (strcmp(ifname, target_interface) != 0) {
        return;
    }
    
    // 检查接口状态变化
    if (ifi->ifi_flags & IFF_UP) {
        // 接口启动
        int current_index = ifi->ifi_index;
        if (last_interface_index != -1 && current_index != last_interface_index) {
            PrintfLog(EN_LOG_LEVEL_INFO, "Interface %s restarted, index changed from %d to %d\n", 
                      ifname, last_interface_index, current_index);
            interface_restart_detected = 1;
        }
        last_interface_index = current_index;
    } else {
        // 接口关闭
        PrintfLog(EN_LOG_LEVEL_WARNING, "Interface %s is down\n", ifname);
    }
}

// 重新创建UDP套接字
static int recreate_udp_socket(int *udp_fd, int *max_fd)
{
    PrintfLog(EN_LOG_LEVEL_INFO, "Recreating UDP socket due to interface restart...\n");
    
    // 关闭旧套接字
    if (*udp_fd >= 0) {
        close(*udp_fd);
        *udp_fd = -1;
    }
    
    // 重新创建UDP套接字
    int new_fd = mqtt_ext_create_udp_server_socket();
    if (new_fd < 0) {
        PrintfLog(EN_LOG_LEVEL_ERROR, "Failed to recreate UDP socket\n");
        return -1;
    }
    
    *udp_fd = new_fd;
    
    // 更新最大文件描述符
    if (*udp_fd > *max_fd) {
        *max_fd = *udp_fd;
    }
    
    PrintfLog(EN_LOG_LEVEL_INFO, "UDP socket recreated successfully, fd=%d\n", *udp_fd);
    return 0;
}

static void mqtt_upgrade_timeout(void *argv)
{
    printf("[%s] mqtt_upgrade_timeout\n", __FUNCTION__);
    platform_upgrade_handle();
}

void mqtt_upgrade_handle()
{
    platform_upgrade_handle();
    // if (mqtt_create_timer(mqtt_upgrade_timeout, NULL, 2000) == 0)
    // {
    //     PrintfLog(EN_LOG_LEVEL_ERROR, "Failed to create upgrade timeout timer\n");
    // }
    // else
    // {
    //     PrintfLog(EN_LOG_LEVEL_DEBUG, "Upgrade timeout timer created successfully\n");
    // }
}

static void mqtt_reset_timeout(void *argv)
{
    platform_device_reset();
    // system("echo \"app_reset_agent_start all\" > /var/run/meshmsg.fifo &");
    // sleep(1);
}

static void mqtt_reset_handle()
{
    platform_mib_unbind_cfg();
    vsol_send_bind_mqtt_user_info();
    if (get_connect_status())
    {
        IOTA_DisConnect();
    }
    mqtt_create_timer(mqtt_reset_timeout, NULL, 2000);
}

int mqtt_ext_bind_handle(INT16U type, INT8U *buf, INT16U buf_len, INT8U *res_buf, INT16U *res_len)
{
    int ret, mqtt_enable = 0;
    struct mqtt_ext_bind *bind;

    if (buf_len < sizeof(struct mqtt_ext_bind))
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "MQTT bind msg len=%d error!!!\r\n", buf_len);
        return -1;
    }

    if (MQTT_EXT_MSG_TYPE_BINDING == type)
    {
        mqtt_enable = 1;
    }
    
    bind = (struct mqtt_ext_bind *)buf;
    ret = platform_mib_bind_cfg(mqtt_enable, bind->server_addr, bind->user_id, bind->time_stamp, bind->group_name, bind->mqtt_bind_topic);
    if (0 != ret)
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "Function platform_mib_bind_cfg failed ret = %d\r\n", ret);
        return -1;
    }
    
    mqtt_reconnect_cfg(mqtt_enable);

    return ret;
}

int mqtt_ext_iptv_cfg_handle(INT16U type, INT8U *buf, INT16U buf_len, INT8U *res_buf, INT16U *res_len)
{
    int ret = 0;
    struct mqtt_ext_iptv_cfg *iptv_cfg;

    if (buf_len < sizeof(struct mqtt_ext_iptv_cfg))
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "MQTT IPTV config msg len=%d error!!!\r\n", buf_len);
        return -1;
    }

    iptv_cfg = (struct mqtt_ext_iptv_cfg *)buf;
    PrintfLog(EN_LOG_LEVEL_INFO, "IPTV   enable=%d, vid=%d, bindport=%d", 
              iptv_cfg->iptv_enable, iptv_cfg->vid_val, iptv_cfg->bindport);
    
    ret = platform_set_iptv_cfg(iptv_cfg->iptv_enable, iptv_cfg->vid_val, iptv_cfg->bindport);
    
    if (ret == 0) {
        PrintfLog(EN_LOG_LEVEL_INFO, "IPTV配置执行成功");
    } else {
        PrintfLog(EN_LOG_LEVEL_ERROR, "IPTV配置执行失败: ret=%d", ret);
    }
    
    return ret;
}

int mqtt_ext_smart_qos_cfg_handle(INT16U type, INT8U *buf, INT16U buf_len, INT8U *res_buf, INT16U *res_len)
{
    int ret = 0;
    struct mqtt_ext_smart_qos_cfg *smart_qos_cfg;

    if (buf_len < sizeof(struct mqtt_ext_smart_qos_cfg))
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "MQTT Smart QoS config msg len=%d error!!!\r\n", buf_len);
        return -1;
    }

    smart_qos_cfg = (struct mqtt_ext_smart_qos_cfg *)buf;
    PrintfLog(EN_LOG_LEVEL_INFO, "Smart QoS enable=%d, mode=%d", 
              smart_qos_cfg->qos_enable, smart_qos_cfg->qos_mode);
    
    ret = platform_set_smart_qos_cfg(smart_qos_cfg->qos_enable, smart_qos_cfg->qos_mode);
    
    if (ret == 0) {
        PrintfLog(EN_LOG_LEVEL_INFO, "Smart QoS cfg set success");
    } else {
        PrintfLog(EN_LOG_LEVEL_ERROR, "Smart QoS cfg set failed: ret=%d", ret);
    }
    
    return ret;
}

int mqtt_ext_wan_cfg_handle(INT16U type, INT8U *buf, INT16U buf_len, INT8U *res_buf, INT16U *res_len)
{
    int ret = 0;
    struct mqtt_ext_wan_cfg *wan_cfg;

    if (buf_len < sizeof(struct mqtt_ext_wan_cfg))
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "MQTT WAN config msg len=%d error!!!\r\n", buf_len);
        return -1;
    }

    wan_cfg = (struct mqtt_ext_wan_cfg *)buf;
    PrintfLog(EN_LOG_LEVEL_INFO, "WAN config mode=%s, ppp_user=%s", 
              wan_cfg->connect_mode, wan_cfg->ppp_user);
    
    ret = platform_set_wan_cfg(wan_cfg);
    
    if (ret == 0) {
        PrintfLog(EN_LOG_LEVEL_INFO, "WAN配置执行成功");
    } else {
        PrintfLog(EN_LOG_LEVEL_ERROR, "WAN配置执行失败: ret=%d", ret);
    }
    
    return ret;
}

int mqtt_ext_system_cmd_handle(INT16U type, INT8U *buf, INT16U buf_len, INT8U *res_buf, INT16U *res_len)
{
    int ret = 0;
    struct mqtt_ext_system_cmd *system_cmd;

    if (buf_len < sizeof(struct mqtt_ext_system_cmd))
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "MQTT system command msg len=%d error!!!\r\n", buf_len);
        return -1;
    }

    system_cmd = (struct mqtt_ext_system_cmd *)buf;
    PrintfLog(EN_LOG_LEVEL_INFO, "执行系统命令: %s", system_cmd->command);
    
    ret = system(system_cmd->command);
    
    if (ret == 0) {
        PrintfLog(EN_LOG_LEVEL_INFO, "系统命令执行成功: %s", system_cmd->command);
    } else {
        PrintfLog(EN_LOG_LEVEL_ERROR, "系统命令执行失败: %s, ret=%d", system_cmd->command, ret);
    }
    
    return ret;
}

static int mqtt_ext_push_renew_info(INT8U *buf, INT16U buf_len)
{
    int ret, mqtt_enable = 0;
    struct mqtt_ext_renew_info *renew_info;
    if (!get_connect_status())
    {
        return 0;
    }

    if (buf_len < sizeof(struct mqtt_ext_renew_info))
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "MQTT renew_info msg len=%d error!!!\r\n", buf_len);
        return -1;
    }

    renew_info = (struct mqtt_ext_renew_info *)buf;

    vsol_ems_push_renew_info(renew_info->topic, renew_info->info_param, "");
    return ret;
}

static int mqtt_ext_msg_handle(INT16U type, INT8U *buf, INT16U buf_len, INT8U *res_buf, INT16U *res_len)
{
    int ret = 0;
    PrintfLog(EN_LOG_LEVEL_INFO, "[mqtt_ext_msg_handle] type = %hu", type);
    switch (type)
    {
    case MQTT_EXT_MSG_TYPE_BINDING:
    case MQTT_EXT_MSG_TYPE_UNBINDING:
        ret = mqtt_ext_bind_handle(type, buf, buf_len, res_buf, res_len);
        break;
    case MQTT_EXT_MSG_TYPE_REBOOT:
        mqtt_reboot_handle();
        break;
    case MQTT_EXT_MSG_TYPE_RESET:
        mqtt_reset_handle();
        break;
    case MQTT_EXT_MSG_PAIR_GET_ALL_AGENTS:
    case MQTT_EXT_MSG_PAIR_ADD_AGENT:
    case MQTT_EXT_MSG_PAIR_GET_LAST_INFO:
    case MQTT_EXT_MSG_PAIR_GET_WPS_STATE:
    case MQTT_EXT_MSG_PAIR_DELETE_AGENT:
        ret = pair_app_msg_handle(type, buf, buf_len, res_buf, res_len);
        break;
    case MQTT_EXT_MSG_DNS_QUERY_INFO:
        break;
    case MQTT_EXT_MSG_WLAN_TAKE_EFFECT:
        break;
    case MQTT_EXT_MSG_UPGRADE_EVENT:
    case MQTT_EXT_MSG_UPGRADE_GET_STATE:
        ret = upgrade_app_msg_handle(type, buf, buf_len, res_buf, res_len);
        break;
    case MQTT_EXT_MSG_UPGRADE_START:
        mqtt_upgrade_handle();
        break;
    case MQTT_EXT_MSG_UDP_GET_DEVICE_INFO:
        mqtt_udp_get_discover_list_info(res_buf, res_len);
        break;
    case MQTT_EXT_MSG_UDP_UPDATE_DEVICE_INFO:
        mqtt_udp_send_refresh_udp_device_info();
        break;
    case MQTT_EXT_MSG_UDP_UPDATE_LOCAL_DEVICE_INFO:
        mqtt_udp_loacl_device_info();
        break;
    case MQTT_EXT_MSG_WLAN_GET_GUEST_OPEN_DURATION:
        break;
    case MQTT_EXT_MSG_PUSH_RENEW_INFO:
        ret = mqtt_ext_push_renew_info(buf, buf_len);
        break;
    case MQTT_EXT_MSG_TYPE_IPTV_CFG:
        ret = mqtt_ext_iptv_cfg_handle(type, buf, buf_len, res_buf, res_len);
        break;
    case MQTT_EXT_MSG_TYPE_SMART_QOS_CFG:
        ret = mqtt_ext_smart_qos_cfg_handle(type, buf, buf_len, res_buf, res_len);
        break;
    case MQTT_EXT_MSG_TYPE_WAN_CFG:
        ret = mqtt_ext_wan_cfg_handle(type, buf, buf_len, res_buf, res_len);
        break;
    case MQTT_EXT_MSG_TYPE_SYSTEM_CMD:
        ret = mqtt_ext_system_cmd_handle(type, buf, buf_len, res_buf, res_len);
        break;
    case MQTT_EXT_MSG_UPDATE_WLAN_QUALITY:
        ret = update_chan_quality_thread_create();
        break;
    case MQTT_EXT_MSG_OPTIMIZE_WLAN_QUALITY:
        ret = optimize_chan_quality_thread_create();
        break;
    default:
        PrintfLog(EN_LOG_LEVEL_ERROR, "MQTT-EXT server unknow msg type=%d\n", type);
        ret = -1;
        break;
    }
    return ret;
}

static int mqtt_ext_msg_response(int client_sock, INT16U type, INT8U *buf, INT16U buf_len)
{
    struct mqtt_ext_msg *msg = (struct mqtt_ext_msg *)buf;

    if (buf_len > (MQTT_BUFFER_SIZE - sizeof(struct mqtt_ext_msg)))
    {
        PrintfLog(EN_LOG_LEVEL_ERROR, "Error buf len %d\n", buf_len);
        return -1;
    }

    msg->magic = MQTT_EXT_RESP_MAGIC;
    msg->type = type;
    msg->len = buf_len;
    buf_len += sizeof(struct mqtt_ext_msg);

    return write(client_sock, buf, buf_len);
}

static void *mqtt_ext_server_thread(void *arg)
{
    int server_fd, new_socket, valread, udp_server_fd;
    int wlan_netlink_fd, inotify_fd;
    struct sockaddr_un address;
    struct sockaddr src_addr;
    socklen_t src_addrlen;
    int addrlen = sizeof(address);
    fd_set readfds;
    int max_fd;
    char buffer[MQTT_BUFFER_SIZE] = {0};
    char res_buffer[MQTT_BUFFER_SIZE] = {0};
    INT16U res_len;
    int client_socks[MQTT_MAX_CLIENTS];
    int i, j, ret, header_len;
    struct timeval timeout;

    for (i = 0; i < MQTT_MAX_CLIENTS; i++)
    {
        client_socks[i] = -1;
    }

    // Create Unix Domain Socket
    if ((server_fd = socket(AF_UNIX, SOCK_STREAM, 0)) == -1)
    {
        return NULL;
    }

    memset(&address, 0, sizeof(address));
    address.sun_family = AF_UNIX;
    strncpy(address.sun_path, MQTT_EXT_SOCKET_PATH, sizeof(address.sun_path) - 1);

    // try to clean the socket file
    unlink(MQTT_EXT_SOCKET_PATH);

    if (bind(server_fd, (struct sockaddr *)&address, sizeof(address)) < 0)
    {
        return NULL;
    }

    chmod(MQTT_EXT_SOCKET_PATH, 0777);

    if (listen(server_fd, MQTT_MAX_CLIENTS) < 0)
    {
        return NULL;
    }

    if ((udp_server_fd = mqtt_ext_create_udp_server_socket()) == -1)
    {
        close(server_fd);
        return NULL;
    }

    // 初始化网络接口监控
    if (create_netlink_monitor() == 0) {
        // 获取当前接口索引
        last_interface_index = get_interface_index(target_interface);
        PrintfLog(EN_LOG_LEVEL_INFO, "Monitoring interface %s, current index: %d\n", 
                  target_interface, last_interface_index);
    } else {
        PrintfLog(EN_LOG_LEVEL_WARNING, "Failed to create netlink monitor, UDP socket recreation may not work\n");
    }

    max_fd = server_fd > udp_server_fd ? server_fd : udp_server_fd;
    header_len = sizeof(struct mqtt_ext_msg);

    mqtt_udp_init();
    mqtt_timer_init();
    // set timeout seconds
    timeout.tv_sec = 1;
    timeout.tv_usec = 0;


    wlan_netlink_fd = pair_wlan_netlink_create();
    ret =  netlink_socket_send_msg(wlan_netlink_fd);
    printf("netlink_socket_send_msg ret:%d netlink_fd:%d\n", ret, wlan_netlink_fd );
    if (wlan_netlink_fd > max_fd)
    {
        max_fd = wlan_netlink_fd;
    }

    inotify_fd = pair_inotify_create();
    if (inotify_fd > max_fd)
    {
        max_fd = inotify_fd;
    }


    while (1)
    {
        FD_ZERO(&readfds);
        FD_SET(server_fd, &readfds);
        FD_SET(udp_server_fd, &readfds);
        
        // 添加网络接口监控套接字到select
        if (netlink_monitor_fd >= 0) {
            FD_SET(netlink_monitor_fd, &readfds);
            if (netlink_monitor_fd > max_fd) {
                max_fd = netlink_monitor_fd;
            }
        }
        
        // reset timeout each loop
        timeout.tv_sec = 1;
        timeout.tv_usec = 0;

        if (wlan_netlink_fd > 0)
        {
            FD_SET(wlan_netlink_fd, &readfds);
        }

        if (inotify_fd > 0)
        {
            FD_SET(inotify_fd, &readfds);
        }


        for (i = 0; i < MQTT_MAX_CLIENTS; i++)
        {
            if (client_socks[i] != -1)
            {
                FD_SET(client_socks[i], &readfds);
            }
        }
        
        

        // wait for active sockets
        ret = select(max_fd + 1, &readfds, NULL, NULL, &timeout);
        if (ret == -1)
        {
            continue;
        }
        
        // 处理网络接口状态变化
        if (netlink_monitor_fd >= 0 && FD_ISSET(netlink_monitor_fd, &readfds)) {
            char buffer[NETLINK_MSG_SIZE];
            ssize_t len = recv(netlink_monitor_fd, buffer, sizeof(buffer), 0);
            
            if (len > 0) {
                struct nlmsghdr *nlh = (struct nlmsghdr*)buffer;
                
                while (NLMSG_OK(nlh, len)) {
                    if (nlh->nlmsg_type == RTM_NEWLINK || nlh->nlmsg_type == RTM_DELLINK) {
                        handle_interface_change(nlh);
                    }
                    nlh = NLMSG_NEXT(nlh, len);
                }
            }
        }
        
        // 检查是否需要重新创建UDP套接字
        if (interface_restart_detected) {
            PrintfLog(EN_LOG_LEVEL_INFO, "Interface restart detected, recreating UDP socket...\n");
            if (recreate_udp_socket(&udp_server_fd, &max_fd) == 0) {
                interface_restart_detected = 0;  // 重置标志
                PrintfLog(EN_LOG_LEVEL_INFO, "UDP socket recreated successfully after interface restart\n");
            } else {
                PrintfLog(EN_LOG_LEVEL_ERROR, "Failed to recreate UDP socket after interface restart\n");
            }
        }
        
        if (ret == 0)
        {
            // timeout
            if (timeout.tv_sec > 0 || timeout.tv_usec > 0)
            {
                PrintfLog(EN_LOG_LEVEL_WARNING, "Remaining timeout: %ld seconds, %ld microseconds\n",
                          timeout.tv_sec, timeout.tv_usec);
                PrintfLog(EN_LOG_LEVEL_DEBUG, "MQTT-ext-server select maxfd=%d, server_fd=%d udp_fd=%d netlink_fd=%d\n",\
                        max_fd, server_fd, udp_server_fd, wlan_netlink_fd);
                continue;
            }
            else
            {
                mqtt_timer_handle();
                continue;
            }
        }

        // Accept a new client
        if (FD_ISSET(server_fd, &readfds))
        {
            socklen_t client_len = sizeof(address);
            if ((new_socket = accept(server_fd, (struct sockaddr *)&address, &client_len)) < 0)
            {
                continue;
            }

            // add to cilent array
            for (i = 0; i < MQTT_MAX_CLIENTS; i++)
            {
                if (client_socks[i] == -1)
                {
                    client_socks[i] = new_socket;
                    break;
                }
            }

            if (i == MQTT_MAX_CLIENTS)
            {
                close(new_socket);
                continue;
            }

            max_fd = (new_socket > max_fd) ? new_socket : max_fd;
        }

        memset(buffer, 0, sizeof(buffer));
        
        if (wlan_netlink_fd > 0 && FD_ISSET(wlan_netlink_fd, &readfds))
        {
            // PrintfLog(EN_LOG_LEVEL_INFO,"Rec netlink msg\n");
            pair_wlan_netlink_recv(wlan_netlink_fd);
        }


        // Read data from clients
        for (i = 0; i < MQTT_MAX_CLIENTS; i++)
        {
            if (client_socks[i] == -1)
            {
                continue;
            }
            
            if (FD_ISSET(client_socks[i], &readfds))
            {
                if ((valread = read(client_socks[i], buffer, MQTT_BUFFER_SIZE)) <= 0)
                {
                    // Exception: close the socket
                    close(client_socks[i]);
                    client_socks[i] = -1;
                }
                else
                {
                    if (valread < header_len)
                    {
                        continue;
                    }
                    else
                    {
                        struct mqtt_ext_msg *e_msg = (struct mqtt_ext_msg *)buffer;
                        if (e_msg->magic == MQTT_EXT_REQ_MAGIC)
                        {
                            res_len = 0;
                            char *p = res_buffer + header_len;

                            ret = mqtt_ext_msg_handle(e_msg->type, e_msg->data, e_msg->len, p, &res_len);
                            if (ret == 0)
                            {
                                mqtt_ext_msg_response(client_socks[i], e_msg->type, res_buffer, res_len);
                            }
                        }
                        else
                        {
                            PrintfLog(EN_LOG_LEVEL_ERROR, "MQTT ext Error msg magic=0x%x\r\n", e_msg->magic);
                        }
                    }
                }
            }
        }
        
        if (FD_ISSET(udp_server_fd, &readfds))
        {
            src_addrlen = sizeof(src_addr);
            valread = recvfrom(udp_server_fd, buffer, sizeof(buffer), 0, &src_addr, &src_addrlen);
            PrintfLog(EN_LOG_LEVEL_DEBUG, "MQTT-ext-server udp server recv len=%d\n", valread);

            if (valread == -1)
            {
                PrintfLog(EN_LOG_LEVEL_ERROR, "mqtt-udp-server recv");
            }
            else if (valread > 0)
            {
                mqtt_ext_udp_recv(buffer, valread, &src_addr, src_addrlen);
            }
        }
        
        if (inotify_fd > 0 && FD_ISSET(inotify_fd, &readfds))
        {
            pair_inotify_recv(inotify_fd);
        }
    }

    return NULL;
}

static void *mqtt_probe_request_thread(void *arg) 
{    
    while (1) {
        if (device_get_mesh_role() == MESH_ROLE_AGENT && platform_judge_slaveDev_is_meshed() == 0) //slave device
        {
            PrintfLog(EN_LOG_LEVEL_INFO, "[%s] running start_ap_scan\n", __func__);
            system("iwpriv default_radio0 start_ap_scan 1");
        }
        sleep(7);
    }
}

int mqtt_create_probe_request_thread()
{
    pthread_t thread_id; // thread ID   
    int ret = 0;  

    ret = pthread_create(&thread_id, NULL, mqtt_probe_request_thread, NULL);  
    if (ret != 0) {
        printf("create mqtt_probe_request_threadfailed\r\n");
        return ret; 
    }  

    return 0;  
}

int mqtt_ext_server_init()
{
    pthread_t thread_id; // thread ID
    int ret;

    // create thread
    ret = pthread_create(&thread_id, NULL, mqtt_ext_server_thread, NULL);
    if (ret != 0)
    {
        return ret;
    }

    ret = mqtt_create_probe_request_thread();
    if (ret != 0)
    {
        return ret;
    }

    return 0;
}
