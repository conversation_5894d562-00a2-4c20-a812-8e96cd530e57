#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <arpa/inet.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/un.h>
#include <sys/inotify.h>
#include <fcntl.h>
#include <sys/types.h>
#include <errno.h>

#include "pair_handle.h"
#include "platform_pair_api.h"
#include "log_util.h"

static unsigned int wps_file_wd = 0;
static unsigned int log_ctrl_file_wd = 0;
static unsigned int disconnect_mqtt_file_wd = 0;
extern void check_log_open(void);

int watch_file(int inotify_fd, const char *filename, uint32_t mask) 
{
    int wd = inotify_add_watch(inotify_fd, filename, mask);
    if (wd == -1) {
        perror("inotify_add_watch ");
        printf("watch file %s failed\r\n", filename);
    } else {
        PrintfLog(EN_LOG_LEVEL_INFO, "Watching %s - wd:%d\n", filename, wd);
    }
    return wd;
}

static int pair_create_file(char *filename, char *txt)
{
    FILE *fp;

    fp = fopen(filename, "w");
    if (fp == NULL) {
        perror("Error opening file");
        return 1;
    }

    if (txt != NULL)
        fprintf(fp, "%s", txt);
    fclose(fp);
    return 0;
}

static void check_disconnect_mqtt_flag(void) {
    char buf[16] = {0};
    
	FILE *fp = fopen(MQTT_DISCONNECT_FLAG_FILE, "r");
	if (fp) {
		fgets(buf, sizeof(buf), fp);
		fclose(fp);
	} else {
        perror("open "MQTT_DISCONNECT_FLAG_FILE" failed");
        return;
    }

    int flag = atoi(buf);
    if (flag == 1) {
        destory();
    } else if (flag == 2) { // for master -> slave set_ie
        pair_handle_init();
        platform_mib_unbind_cfg();
    } else if (flag == 3) {
        IOTA_DisConnect();
    } else if (flag == 4) {
        IOTA_Connect();
    }

    return;
}

int pair_inotify_create()
{
    int iFdNotify = -1;
    char cCmd[128] = {0};

    iFdNotify = inotify_init();
    if (iFdNotify == -1) {
        perror("inotify_init error, iFdNotifyd\n");
        return -1;
    }

    if (access(WPS_STATUS_FILE_NAME, F_OK) != 0) {
        pair_create_file(WPS_STATUS_FILE_NAME, "success");
    }
    
    if (access(MQTT_LOG_CTRL_FINE_NAME, F_OK) != 0) {
        pair_create_file(MQTT_LOG_CTRL_FINE_NAME, "0");
    }  

    if (access(MQTT_DISCONNECT_FLAG_FILE, F_OK) != 0) {
        pair_create_file(MQTT_DISCONNECT_FLAG_FILE, "0");
    }  
    
    //watch_file(iFdNotify, TEMP_DIR, IN_CREATE | IN_DELETE);
    //watch_file(iFdNotify, MESH_CONFIG, IN_MODIFY);
    wps_file_wd = watch_file(iFdNotify, WPS_STATUS_FILE_NAME, IN_MODIFY|IN_DELETE);
    //watch_file(iFdNotify, WATCH_MESH_DIR, IN_CREATE);
    log_ctrl_file_wd = watch_file(iFdNotify, MQTT_LOG_CTRL_FINE_NAME, IN_MODIFY|IN_DELETE);

    /* Added by ZHH for web upgrade of fac disconnect mqtt */
    disconnect_mqtt_file_wd = watch_file(iFdNotify, MQTT_DISCONNECT_FLAG_FILE, IN_MODIFY|IN_DELETE);
    /* End of Addition */
    
    return iFdNotify;
}

#define EVENT_SIZE(len)  (sizeof (struct inotify_event) + len)
int pair_inotify_recv(int inotify_fd)
{
    ssize_t read_Len;
    struct inotify_event *pEvent;
    char *ptr;
    char buf[PAIR_BUF_LEN];

    PrintfLog(EN_LOG_LEVEL_INFO, "==>%s\n", __FUNCTION__);
    read_Len = read(inotify_fd, buf, PAIR_BUF_LEN);
    if (read_Len < 0) {
        perror("pair inotify read iFdNotify error\n");
        return -1;
    }
    
    for (ptr = buf; ptr < buf + read_Len; )    
    {
        pEvent = (struct inotify_event *)ptr;
#if 0
        if (strcmp(pEvent->name, WATCH_MESH_DEVICE_ROLE_FILE) == 0
            ||strcmp(pEvent->name, WATCH_MESH_DEVICE_ROLE_FILE_NAME) == 0) 
            {
            printf("File '%s' has been created.\n", pEvent->name);
        }

        else if ((pEvent->mask & IN_CREATE || pEvent->mask & IN_DELETE) && strcmp(pEvent->name, AUTO_CONTROLLER_FILE_NAME) == 0)
        {
            printf("File '%s' has been created.\n", pEvent->name);
            //HandleMeshRoleChangeEvent();
        }
        else if ((pEvent->mask & IN_CREATE || pEvent->mask & IN_DELETE) && strcmp(pEvent->name, AUTO_AGENT_FILE_NAME) == 0)
        {
            printf("File '%s' has been created.\n", pEvent->name);
            //HandleMeshRoleChangeEvent();
        }
        else
#endif
        if (wps_file_wd == pEvent->wd)
        {
            PrintfLog(EN_LOG_LEVEL_INFO, "File %s inotify event pEvent->mask=0x%x, crete=0x%x, modify=0x%x.\n", WPS_STATUS_FILE_NAME, pEvent->mask, IN_CREATE, IN_MODIFY);
            HandleWpsStatusChangeEvent();
        }
        else if (log_ctrl_file_wd == pEvent->wd) {
            check_log_open();
        } else if (disconnect_mqtt_file_wd == pEvent->wd) {
            check_disconnect_mqtt_flag();
        }
        else 
            printf("File %s change pEvent->mask=0x%x,crete=0x%x, modify=0x%x\r\n", pEvent->name, pEvent->mask, IN_CREATE, IN_MODIFY);

        ptr += EVENT_SIZE(pEvent->len);
    }

    return 0;
}


