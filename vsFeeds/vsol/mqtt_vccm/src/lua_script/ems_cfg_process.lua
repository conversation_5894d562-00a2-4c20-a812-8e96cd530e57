package.path = package.path .. ";/usr/lib/lua/mqtt_vccm/?.lua;/usr/lib/lua/luci/?.lua;"
local common_api = require("common")
local com = require "luci.com_fun"
local util = require("luci.util")
local jsonc = require "luci.jsonc"
local uci = require("luci.model.uci").cursor()
local sys = require "luci.sys"
local platform_call = require("platform_call")
local table_to_string = require("table_to_string");

local wlan_5G_txpower_map = {
    ["0"] = "10",
    ["1"] = "20",
    ["2"] = "23",
}
local wlan_24G_txpower_map = {
    ["0"] = "10",
    ["1"] = "17",
    ["2"] = "20",
}

local week_map = {
    ["0"] = "1",
    ["1"] = "2",
    ["2"] = "3",
    ["3"] = "4",
    ["4"] = "5",
    ["5"] = "6",
    ["6"] = "0",
}

local wlan_interface_map ={
    ["0"] = "default_radio0",
    ["1"] = "guest_radio1",
    ["2"] = "ra2",
    ["3"] = "ra3",
    ["4"] = "default_radio1",
    ["5"] = "guest_radio1",
    ["6"] = "rai2",
    ["7"] = "rai3",
}

local function invalid_symbol_check(str)
    if type(str) ~= "string" then
        return false
    end
    -- 开头空白字符校验（空格、制表符等）
    if str:match("^%s") ~= nil then
        return false
    end
    -- 结尾空白字符校验（空格、制表符等）
    if str:match("%s$") ~= nil then
        return false
    end
    if str:match("[\\'\"]") ~= nil then
        return false
    end
    return true
end

local function judge_router_device()
    local device_model = util.trim(util.exec("sed -n 's/.*product name\\s*:\\s*\\(.*\\)/\\1/p' /etc/hi_version"))
    
    -- 获取调试日志文件句柄
    local debug_log = io.open("/tmp/wlan_cfg_debug.log", "a")
    
    if device_model:match("wrt_v2802ach")~=nil or device_model:match("3232ax")~=nil then
        -- return false
        return true -- temp for onu guest wlan, beacause web has guest wlan
    else
        -- router need get wan port; TODO
        return true
    end
end

function convertSeconds(seconds)
    local hours = math.floor(seconds / 3600)          
    local minutes = math.floor((seconds % 3600) / 60) 

    return string.format("%02d", hours), string.format("%02d", minutes)
end

local function get_famlit_cfg_mac_list(member_list)
    local mac_list = {}

    for mac in member_list:gmatch("([^,]+)") do   
        local formatted_mac = mac:lower():gsub("(%x%x)(%x%x)(%x%x)(%x%x)(%x%x)(%x%x)", "%1:%2:%3:%4:%5:%6")  
        table.insert(mac_list, formatted_mac)  
    end

    return mac_list
end

local function get_disconnect_sta_command(mac_list)
    local command_end = ""

    for _, mac in ipairs(mac_list) do  
        command_end=  command_end .. "iwpriv ra0 set DisConnectSta=" .. mac .. " && iwpriv rai0 set DisConnectSta=" .. mac .. " && " 
    end

    return command_end:gsub(" %&%& %s*$", "")
end

local function get_current_time()
    local handle = io.popen("date +'%u %H %M %S'")  
    local result = handle:read("*a") 
    local weekday_mapping = {1, 2, 3, 4, 5, 6, 0}
    local weekday, hour, min, sec = result:match("(%d+) (%d+) (%d+) (%d+)")  
    local current_time = {}

    current_time["date"] = tostring(weekday_mapping[tonumber(weekday)]) 
    current_time["time"] = tostring((tonumber(hour) * 3600) + (tonumber(min) * 60) + tonumber(sec))  
    handle:close()
    
    return current_time
end

local function check_ip_higher(ip_start, ip_end, mask)
    local s1, s2, s3, s4 = ip_start:match("(%d+)%.(%d+)%.(%d+)%.(%d+)")
    local e1, e2, e3, e4 = ip_end:match("(%d+)%.(%d+)%.(%d+)%.(%d+)")
    local m1, m2, m3, m4 = mask:match("(%d+)%.(%d+)%.(%d+)%.(%d+)")

    if (((tonumber(s1) and tonumber(m1)) ~= (tonumber(e1) and tonumber(m1)))
            or ((tonumber(s2) and tonumber(m2)) ~= (tonumber(e2) and tonumber(m2)))
            or ((tonumber(s3) and tonumber(m3)) ~= (tonumber(e3) and tonumber(m3)))
            or ((tonumber(s4) and tonumber(m4)) ~= (tonumber(e4) and tonumber(m4)))) then
        return false
    end
    if (tonumber(e1) < tonumber(s1)
            or tonumber(e2) < tonumber(s2)
            or tonumber(e3) < tonumber(s3)
            or tonumber(e4) < tonumber(s4)) then
        return false
    end
    return true
end

local function binding_cfg_check(binding_cfg)
    if binding_cfg["timestamp"] == nil then
        return "args-'timestamp' is missing"
    end
end

local function lan_cfg_check(lan_cfg)
    if common_api.ipv4_format_check(lan_cfg["lan_ip"]) == false then
        return "lan_ip is invalid"
    end
    if common_api.ipv4_format_check(lan_cfg["subnet_mask"]) == false then
        return "subnet_mask is invalid"
    end
    if common_api.ipv4_format_check(lan_cfg["start_ip"]) == false then
        return "start_ip is invalid"
    end
    if common_api.ipv4_format_check(lan_cfg["end_ip"]) == false then
        return "end_ip is invalid"
    end
    if common_api.protect_run(check_ip_higher, lan_cfg["start_ip"], lan_cfg["end_ip"], lan_cfg["subnet_mask"]) ~= true then
        return "start_ip and end_ip is invalid"
    end
    if common_api.ipv4_format_check(lan_cfg["lan_dhcp_gateway_ip"]) == false then
        return "lan_dhcp_gateway_ip is invalid"
    end
    if tonumber(lan_cfg["lease_time_in_seconds"]) == nil or tonumber(lan_cfg["lease_time_in_seconds"]) <= 0 then
        return "lease_time_in_seconds is invalid"
    end

    if lan_cfg["lan_dns_mode"] == "static" then
        if lan_cfg["primary_dns_server"] ~= nil and lan_cfg["primary_dns_server"] ~= "" then
            if common_api.ipv4_format_check(lan_cfg["primary_dns_server"]) == false then
                return "primary_dns_server is invalid"
            end
        end
        if lan_cfg["secondary_dns_server"] ~= nil and lan_cfg["secondary_dns_server"] ~= "" then
            if common_api.ipv4_format_check(lan_cfg["secondary_dns_server"]) == false then
                return "secondary_dns_server is invalid"
            end
        end
    end

    return nil
end

local function mesh_cfg_check(mesh_cfg)
    for key, value in pairs(mesh_cfg) do
        hostname = value["hostname"]
        location = value["location"]

        if tonumber(key) == nil then
            return "invalid index"
        end
        if type(value) ~= "table" then
            return "invalid args"
        end
        if value["mac"] == nil then
            return "args-'mac' is missing"
        end
        if value["mac"]:match("%x%x%x%x%x%x%x%x%x%x%x%x") == nil then
            return "invalid args-'mac'"
        end
        if hostname ~= nil then
            if type(hostname) ~= "string" or hostname:len() == 0 or invalid_symbol_check(hostname) == false then
                return "invalid hostname"
            end
        end
        if location ~= nil then
            if type(location) ~= "string" or location:len() == 0 or invalid_symbol_check(location) == false then
                return "invalid location"
            end
        end
    end

    return nil
end

-- 规则（遵循网页）：
-- 1) 仅允许字母/数字和 ( . _ / @ ! ~ # $ % ^ * ( ) + : ? )
-- 2) 长度 ≥ 8
-- 3) 必须包含字母，且至少包含一个数字或特殊字符
local function validate_password(password)
    if type(password) ~= "string" then
        return false, "密码类型错误"
    end
    if #password < 8 then
        return false, "密码至少需要8个字符"
    end
    -- 字符白名单检查
    for ch in password:gmatch(".") do
        if not ch:match("[A-Za-z0-9%.%_/%@%!~#%$%^%*%(%)%+:%?]") then
            return false, "密码包含非法字符"
        end
    end
    local has_alpha   = password:match("%a") ~= nil
    local has_digit   = password:match("%d") ~= nil
    local has_special = password:match("[%.%_/%@%!~#%$%^%*%(%)%+:%?]") ~= nil

    if not has_alpha then
        return false, "密码必须包含字母"
    end
    if not (has_digit or has_special) then
        return false, "密码需包含数字或特殊字符"
    end
    return true
end

local function user_cfg_check(user_cfg)
    local password = user_cfg["new_password"]

    local ok, err = validate_password(password)
    if not ok then
        return "invalid password: " .. err
    end

    -- if type(password) ~= "string" or (password:len() > 0 and password:len() < 6) or invalid_symbol_check(password) == false then
    --     return "invalid password"
    -- end
    -- return nil
end

local function family_cfg_check(family_cfg)
    if family_cfg["family_index"] == nil then
        return "invalid family_index"
    end

    if family_cfg["family_name"] ~= nil then
        local family_name = family_cfg["family_name"]
        if type(family_name) ~= "string" or family_name:len() == 0 then
            return "invalid family_name"
        end
    end

    if family_cfg["member_list"] ~= nil then
        local member_list = family_cfg["member_list"]
        if type(member_list) ~= "string" or member_list:len() == 0 then
            return "invalid member_list"
        end
    end

    if (family_cfg["date_list"] ~= nil) then
        if (type(family_cfg["date_list"]) ~= "table") then
            return "invalid args date_list"
        end

        for key, value in pairs(family_cfg["date_list"]) do
            if type(value) ~= "table" then
                return "invalid date arg" 
            end

            if value["date_week"] ~= nil then
                local date_week = value["date_week"]
                if type(date_week) ~= "string" or tonumber(date_week) > 6 then
                    return "invalid date_week"
                end
            end

            if value["on_time"] ~= nil then
                local on_time = value["on_time"]
                if type(on_time) ~= "string" or tonumber(on_time) > 86400 then
                    return "invalid on_time"
                end
            end

            if value["off_time"] ~= nil then
                local off_time = value["off_time"]
                if type(off_time) ~= "string" or tonumber(off_time) > 86400 then
                    return "invalid off_time"
                end
            end
        end
    end

    if (family_cfg["url_list"] ~= nil) then
        if (type(family_cfg["url_list"]) ~= "table") then
            return "invalid args url_list"
        end

        for key, value in pairs(family_cfg["url_list"]) do
            if type(value) ~= "table" then
                return "invalid url arg"
            end

            if value["url_limit_type"] ~= nil then
                local url_limit_type = value["url_limit_type"]
                if type(url_limit_type) ~= "string" or (tonumber(url_limit_type) ~= 0 and tonumber(url_limit_type) ~= 1) then
                    return "invalid url_limit_type"
                end
            end

            if value["url"] ~= nil then
                local url = value["url"]
                if type(url) ~= "string" or url:len() == 0 then
                    return "invalid url"
                end
            end
        end
    end
end

local function client_cfg_check(client_cfg)
    for _, value in pairs(client_cfg) do
        if type(value) ~= "table" then
            return "invalid args"
        end
        if value["mac"] == nil then
            return "args-'mac' is missing"
        end
        if value["mac"]:match("%x%x%x%x%x%x%x%x%x%x%x%x") == nil then
            return "invalid args-'mac'"
        end
        if value["ban"] == nil and value["hostname"] == nil and value["client_type"] == nil then
            return "args-'ban/hostname/client_type' is missing"
        end
        if value["ban"] ~= nil then
            if tonumber(value["ban"]) ~= 1 and tonumber(value["ban"]) ~= 0 then
                return "invalid args-'ban'"
            end
        end
        if value["hostname"] ~= nil then
            if type(value["hostname"]) ~= "string" or value["hostname"]:len() == 0 then
                return "invalid hostname"
            end
        end
        if value["client_type"] ~= nil then
            if type(value["client_type"]) ~= "string" then
                return "invalid client_type"
            end
        end
    end
end

local function wlan_cfg_check(wlan_cfg)
    for key, value in pairs(wlan_cfg) do
        if tonumber(key) == nil then
            return "invalid index"
        end
        if type(value) ~= "table" then
            return "invalid args"
        end

        if value["open_duration"] ~= "0" then
            local ssid = value["ssid"]
            local password = value["password"]
            if type(ssid) ~= "string" or ssid:len() == 0 or invalid_symbol_check(ssid) == false then
                return "invalid ssid"
            end
            -- 当仅包含 ssid、password、date_list 三项时，跳过密码校验
            local only_basic_fields = true
            for k, _ in pairs(value) do
                if k ~= "ssid" and k ~= "password" and k ~= "date_list" then
                    only_basic_fields = false
                    break
                end
            end

            if not only_basic_fields then
                if type(password) ~= "string" or (password:len() > 0 and password:len() < 8)
                    -- or invalid_symbol_check(password) == false
                    or password:len() > 63 then
                    return "invalid password"
                end
            end
        end
    end

    return nil
end

local function del_family_cfg_check(del_family_cfg)
    if del_family_cfg["family_index"] == nil then
        return "args-'family_index' is missing"
    end

    if tonumber(del_family_cfg["family_index"]) == 0 then
        return "invalid family_index"
    end
end

local function del_devices_cfg_check(del_devices_cfg)
    if del_devices_cfg["mac_0"] == nil then
        return "args-'mac_0' is missing"
    end
end

local function lan_cfg_set(lan_cfg)
    local dhcp_ignore = lan_cfg["enable"] and "0" or "1"
    local old_dhcp_ip = common_api.mib_get("network", "lan", "ipaddr")
    local lan_ip = lan_cfg["lan_ip"]
    local dhcp_start_ip = lan_cfg["start_ip"]
    local dhcp_end_ip = lan_cfg["end_ip"]
    local dhcp_start = "0"
    local dhcp_limit = "0"
    local dhcp_lease_time = lan_cfg["lease_time_in_seconds"]
    local lan_dns_mode = lan_cfg["lan_dns_mode"]
    local mask = lan_cfg["subnet_mask"]
    
    if dhcp_ignore == "1" then
        common_api.mib_set("dhcp", "lan", "ignore", "1")
    else
        common_api.mib_del("dhcp", "lan", "ignore")
    end
    
    if lan_ip:find(".") then
        dhcp_start = dhcp_start_ip:match("%d+.%d+.%d+.(%d+)")
        dhcp_limit = com.countIPAddresses(dhcp_start_ip, dhcp_end_ip)
    end
    
    common_api.mib_set("dhcp", "lan", "start", dhcp_start)
    common_api.mib_set("dhcp", "lan", "limit", dhcp_limit)
    common_api.mib_set("dhcp", "lan", "leasetime", common_api.parse_second_to_string_time(tonumber(dhcp_lease_time)))
    
    if lan_dns_mode == "hgu_proxy" then
        local all_wan_data = util.ubus("syswan", "all", {})
        local wan_id = -1
    
        for _, wan_value in pairs(all_wan_data) do
            if wan_value["wanMode"] == 1 then
                if wan_value["service"] == 0 or wan_value["service"] == 1 or wan_value["service"] == 6 or wan_value["service"] == 7 then
                    wan_id = wan_value["wanId"]
                    break
                end
            end
        end
    
        if wan_id ~= -1 then
            local selected_wan_data = util.ubus("syswan", "get", { wan_id = wan_id })
            local ext_if_dns_mode = "wan_" .. wan_id
    
            common_api.mib_del("dhcp", "lan", "dhcp_option")
            common_api.mib_set("dhcp", "lan", "dns_sync_with_wan", ext_if_dns_mode)
    
            if selected_wan_data ~= nil then
                local dhcp_option = ""
    
                if selected_wan_data["priDns"] ~= "" then
                    dhcp_option = "6," .. selected_wan_data["priDns"]
                    if selected_wan_data["secDns"] ~= "" then
                        dhcp_option = dhcp_option .. "," .. selected_wan_data["secDns"]
                    end
                end
    
                if dhcp_option ~= "" then
                    common_api.mib_set_list("dhcp", "lan", "dhcp_option", dhcp_option)
                end
            end
        end
    elseif lan_dns_mode == "static" then
        local primary_dns_server = lan_cfg["primary_dns_server"]
        local secondary_dns_server = lan_cfg["secondary_dns_server"]
        local dhcp_option = "6," .. primary_dns_server
    
        if secondary_dns_server ~= "" then
            dhcp_option = dhcp_option .. "," .. secondary_dns_server
        end
        common_api.mib_set_list("dhcp", "lan", "dhcp_option", dhcp_option)
        common_api.mib_del("dhcp", "lan", "dns_sync_with_wan")
    else
        common_api.mib_del("dhcp", "lan", "dhcp_option")
        common_api.mib_del("dhcp", "lan", "dns_sync_with_wan")
    end
    common_api.mib_commit_by("dhcp")
    
    common_api.mib_set("network", "lan", "ipaddr", lan_ip)
    common_api.mib_set("network", "lan", "netmask", mask)
    common_api.mib_commit_by("network")
    
    if (old_dhcp_ip ~= "") and (old_dhcp_ip ~= lan_ip) then
        com.clean_cookie()
        com.clean_login()
    end
end

local function compare_wlan_cfg(idx, default_radio, cfg)
    local txpower0 = uci:get("wireless", "radio0", "txpower") or ""
    local txpower1 = uci:get("wireless", "radio1", "txpower") or ""
    local ssid = uci:get("wireless", default_radio, "ssid") or ""
    local key = uci:get("wireless", default_radio, "key") or ""
    local encryption = uci:get("wireless", default_radio, "encryption") or ""

    if ssid~="" and ssid ~= cfg.ssid then
        return 1
    end

    if key~="" and key ~= cfg.password then
        return 1
    end

    if encryption~="" and encryption ~= cfg.encryption then
        return 1
    end

    if cfg.transmitting_power_class ~= nil then
        if tonumber(idx) == 0 then
            if txpower0 ~= wlan_24G_txpower_map[cfg.transmitting_power_class] then
                return 1
            end
        elseif tonumber(idx) == 4 then
            if txpower1 ~= wlan_5G_txpower_map[cfg.transmitting_power_class] then
                return 1
            end
        end
    end

    return 0
end

function get_interfaces(pattern)
    local interfaces = {}
    local command = "brctl show | grep " .. pattern
    local file = io.popen(command)

    if file then
        for line in file:lines() do
            local interface = line:match("^%s*(.-)%s*$")
            if interface then
                table.insert(interfaces, interface)
            end
        end
        file:close()
    else
        print("Error executing command: " .. command)
    end

    return interfaces
end

local function get_current_ap_interface()
    -- local em_bh_interfaces = get_interfaces("em_bh")
    -- print("Interfaces matching 'em_bh':")
    -- for _, interface in ipairs(em_bh_interfaces) do
    --     print(interface)
    -- end
    local command = ""
    local command_end = ""

    local default_radio_interfaces = get_interfaces("default_radio")
    for _, interface in ipairs(default_radio_interfaces) do
        command = command.."ifconfig " .. interface .. " down"  .. ";"
        command_end = command_end .. "ifconfig " .. interface .. " up"  .. ";"
    end

    local guest_radio_interfaces = get_interfaces("guest_radio")
    for _, interface in ipairs(guest_radio_interfaces) do
        command = command.."ifconfig " .. interface .. " down"  .. ";"
        command_end = command_end .. "ifconfig " .. interface .. " up"  .. ";"
    end

    return command, command_end
end

local function wlan_cfg_set(wlan_cfg)
    -- 创建调试日志文件
    local debug_log = io.open("/tmp/wlan_cfg_debug.log", "w")
    if debug_log then
        debug_log:write("=== WLAN配置调试日志开始 ===\n")
        debug_log:write("时间: " .. os.date("%Y-%m-%d %H:%M:%S") .. "\n")
        debug_log:write("输入配置: " .. table_to_string.convert(wlan_cfg) .. "\n\n")
    end
    
    local default_radio = ""
    local date_list = {}
    local command = ""
    local command_end = ""
    local changed = 0
    local date_limit_setup_flag = 0
    for idx, cfg in pairs(wlan_cfg) do
        if debug_log then
            debug_log:write("--- 处理配置索引: " .. idx .. " ---\n")
            debug_log:write("配置内容: " .. table_to_string.convert(cfg) .. "\n")
        end
        
        local guest_flag = 0;
        if tonumber(idx) == 1 and judge_router_device() then
            default_radio = "guest_radio1"
            guest_flag = 1;
            if debug_log then
                debug_log:write("设备类型: 路由器设备，使用guest_radio1\n")
            end
        elseif tonumber(idx) >= 4 then
            default_radio = "default_radio" .. 1
            if debug_log then
                debug_log:write("设备类型: 5G设备，使用default_radio1\n")
            end
        else
            default_radio = "default_radio" .. 0
            if debug_log then
                debug_log:write("设备类型: 2.4G设备，使用default_radio0\n")
            end
        end

        if debug_log then
            debug_log:write("选择的radio接口: " .. default_radio .. "\n")
            debug_log:write("guest_flag: " .. tostring(guest_flag) .. "\n")
        end

        changed = compare_wlan_cfg(idx, default_radio, cfg)
        if debug_log then
            debug_log:write("配置是否有变化: " .. tostring(changed) .. "\n")
        end
        
        
        if date_limit_setup_flag == 0 and cfg.date_list ~= nil then
            date_list = cfg.date_list
    
            uci:foreach("cron", "task", function(s)
                if s.type:find("wifi_access_time") then
                    uci:delete("cron", s[".name"])
                end
            end)
            command, command_end = get_current_ap_interface()
    
            if next(cfg.date_list) ~= nil then
                local hour, minute = convertSeconds(tonumber(date_list['0'].on_time))
                local hour_end, minute_end = convertSeconds(tonumber(date_list['0'].off_time))
                local task = uci:add("cron", "task")

                -- 若结束时间小于起始时间，则将结束时间修正为 23:59
                if (hour_end * 60 + minute_end) < (hour * 60 + minute) then
                    hour_end = 23
                    minute_end = 59
                end
        
                uci:set("cron", task, "type", "wifi_access_time")
                uci:set("cron", task, "hour", hour)
                uci:set("cron", task, "hour_end", hour_end)
                uci:set("cron", task, "minute", minute)
                uci:set("cron", task, "minute_end", minute_end)
                uci:set("cron", task, "command", command)
                uci:set("cron", task, "command_end", command_end)
        
                local date_week={}
                for idx, date_cfg in pairs(date_list) do
                    table.insert(date_week, week_map[date_cfg.date_week])
                end
                uci:set_list("cron", task, "week", date_week)
                uci:commit("cron")
                date_limit_setup_flag = 1;
            
                -- add by zhh for cron not exec cmd
                
                -- 立即执行：若当前时间在配置的时间段内，则直接执行 command
                local function _seconds_from_midnight()
                    local _t = os.date("*t")
                    return _t.hour * 3600 + _t.min * 60 + _t.sec, _t.wday  -- wday: 1=Sunday..7=Saturday
                end

                local function _today_selected()
                    local _t = os.date("*t")
                    local _wday = _t.wday  -- 1..7
                    -- 将 Lua 的 wday(1=Sunday) 转为本配置的 0..6（0=Monday, 6=Sunday）
                    local _day_idx = (_wday == 1) and 6 or (_wday - 2)
                    for _, _date_cfg in pairs(date_list) do
                        if tonumber(_date_cfg.date_week) == _day_idx then
                            return true
                        end
                    end
                    return false
                end

                local function _in_time_range(_now, _start, _end)
                    if _start == _end then
                        return false
                    end
                    if _start < _end then
                        return _now >= _start and _now < _end
                    else
                        -- 跨午夜区间，例如 23:00-06:00
                        return _now >= _start or _now < _end
                    end
                end

                local _on_sec = tonumber(date_list['0'].on_time) or 0
                local _off_sec = tonumber(date_list['0'].off_time) or 0
                local _now_sec = _seconds_from_midnight()
                if _today_selected() and _in_time_range(_now_sec, _on_sec, _off_sec) then
                    os.execute(command)
                end

                os.execute("sleep 1;/etc/init.d/cron restart")
            else
                uci:commit("cron")
                os.execute(command_end)
            end
        end

        if cfg.ssid ~= nil and cfg.password ~=nil then
            if debug_log then
                debug_log:write("设置SSID和密码...\n")
                debug_log:write("SSID: " .. cfg.ssid .. "\n")
                debug_log:write("密码长度: " .. tostring(cfg.password:len()) .. "\n")
            end
            
            uci:set("wireless", default_radio, "ssid", cfg.ssid)
            if tonumber(idx) == 1 and guest_flag == 1 then
                if debug_log then
                    debug_log:write("处理访客WiFi配置...\n")
                end
                if cfg.password ~= "" then
                    uci:set("wireless", default_radio, "key", cfg.password)
                    uci:set("wireless", default_radio, "encryption", "psk-mixed")
                    if debug_log then
                        debug_log:write("设置加密方式: psk-mixed\n")
                    end
                else
                    uci:set("wireless", default_radio, "encryption", "none")
                    if debug_log then
                        debug_log:write("设置加密方式: none\n")
                    end
                end
            else
                uci:set("wireless", default_radio, "key", cfg.password)
                if debug_log then
                    debug_log:write("设置普通WiFi密码\n")
                end
            end
        end

        if cfg.encryption ~= nil then
            local encry = cfg.encryption;
            uci:set("wireless", default_radio, "encryption", encry)
            if debug_log then
                debug_log:write("设置加密方式: " .. encry .. "\n")
            end
        end

        if cfg.open_duration ~= nil and guest_flag == 1 then
            if debug_log then
                debug_log:write("处理open_duration配置...\n")
                debug_log:write("open_duration值: " .. cfg.open_duration .. "\n")
                debug_log:write("open_duration类型: " .. type(cfg.open_duration) .. "\n")
                debug_log:write("比较结果 (open_duration ~= 0): " .. tostring(cfg.open_duration ~= 0) .. "\n")
            end
            
            open_duration = tostring(cfg.open_duration)
            if open_duration ~= "0" then
                if debug_log then
                    debug_log:write("准备删除disabled配置...\n")
                end
                uci:delete("wireless", default_radio, "disabled")
                if debug_log then
                    debug_log:write("启用WiFi (删除disabled配置成功)\n")
                end
                if open_duration =="1" then
                    uci:set("wireless", default_radio, "duration_time", "240")
                elseif open_duration == "2" then
                    uci:set("wireless", default_radio, "duration_time", "1440")
                elseif open_duration == "3" then
                    uci:delete("wireless", default_radio, "duration_time")
                end
            else
                if debug_log then
                    debug_log:write("准备设置disabled=1..., default_radio: " .. default_radio .. "\n")
                end
                uci:set("wireless", default_radio, "disabled", "1")
                if debug_log then
                    debug_log:write("禁用WiFi (设置disabled=1成功)\n")
                end
            end
        end

        if cfg.transmitting_power_class ~= nil then
            uci:set("wireless", "radio0", "txpower", wlan_24G_txpower_map[cfg.transmitting_power_class])
            uci:set("wireless", "radio1", "txpower", wlan_5G_txpower_map[cfg.transmitting_power_class])
            if debug_log then
                debug_log:write("设置发射功率等级: " .. cfg.transmitting_power_class .. "\n")
                debug_log:write("2.4G功率: " .. wlan_24G_txpower_map[cfg.transmitting_power_class] .. "\n")
                debug_log:write("5G功率: " .. wlan_5G_txpower_map[cfg.transmitting_power_class] .. "\n")
            end
        end
    end

    if debug_log then
        debug_log:write("\n--- 开始提交配置 ---\n")
    end

    -- if changed == 1 then
    uci:commit("wireless")
    -- end
    
    if debug_log then
        debug_log:write("配置已提交到wireless\n")
    end
    
    os.execute("sleep 1")

    if debug_log then
        debug_log:write("等待1秒完成\n")
        debug_log:write("=== WLAN配置调试日志结束 ===\n")
        debug_log:close()
    end

    -- By zhh for sync wifi timer list to app
    -- local content = common_api.protect_run(table_to_string.convert, wlan_cfg);

    -- local fd_result = io.open("/var/wlan_cfg_content.lua", "w");
    -- if fd_result~=nil then
    --     fd_result:write(content);
    -- end
    -- end
end

local function client_cfg_set(client_cfg)
    util.ubus("tmgr", "set_ban", { client_cfg = client_cfg })
    util.ubus("tmgr", "set_mesh", { mesh_cfg = client_cfg })
end

local function family_cfg_set(family_cfg)
    -- local mac_list = get_famlit_cfg_mac_list(family_cfg["member_list"])
    -- local command = "true"
    -- local command_end = get_disconnect_sta_command(mac_list)
    -- local current_time = get_current_time()
    -- local grouped_weeks = {}  
    -- local current_allow = false

    -- for key, value in pairs(family_cfg["date_list"]) do
    --     if value["on_time"] ~= nil and value["off_time"] ~= nil then
    --         local time_key = value["on_time"] .. "_" .. value["off_time"] 

    --         if not grouped_weeks[time_key] then  
    --             grouped_weeks[time_key] = { on_time = value["on_time"], off_time = value["off_time"], date_weeks = {}}  
    --         end  
    --         table.insert(grouped_weeks[time_key]["date_weeks"], week_map[value["date_week"]])
    --     end
    -- end

    -- uci:foreach("cron", "task", function(s)
    --     if s.type:find("child_access_internet_time") then
    --         for _, mac in ipairs(mac_list) do  
    --             if s.command_end:find(mac) then
    --                 uci:delete("cron", s[".name"])
    --             end
    --         end
    --     end
    -- end)

    -- for key, value in pairs(grouped_weeks) do  
    --     local hour, minute = convertSeconds(tonumber(value["on_time"]))
    --     local hour_end, minute_end = convertSeconds(tonumber(value["off_time"]))
    --     local task = uci:add("cron", "task")

    --     for _, week in ipairs(value["date_weeks"]) do  
    --         if current_time["date"] == week then   
    --             if (tonumber(value["on_time"]) < tonumber(current_time["time"])) and (tonumber(current_time["time"]) < tonumber(value["off_time"])) then
    --                 current_allow = true
    --             end
    --         end  
    --     end
         
    --     uci:set("cron", task, "type", "child_access_internet_time") 
    --     uci:set("cron", task, "hour", hour)
    --     uci:set("cron", task, "hour_end", hour_end)  
    --     uci:set("cron", task, "minute", minute)
    --     uci:set("cron", task, "minute_end", minute_end) 
    --     uci:set("cron", task, "command", command)
    --     uci:set("cron", task, "command_end", command_end) 
    --     uci:set_list("cron", task, "week", value["date_weeks"])
    -- end
    -- uci:commit("cron")

    -- if family_cfg["date_list"] == nil or next(family_cfg["date_list"]) == nil then 
    --     current_allow = true
    -- end

    -- if not current_allow then
    --     util.exec(command_end)
    -- end

    util.ubus("tmgr", "set_family_dateLimit", { family_cfg = family_cfg })
end

local function del_family_cfg_set(del_family_cfg)
    util.ubus("tmgr", "del_family", { del_family_cfg = del_family_cfg })
end


local function get_product_name()
    local file = io.open("/etc/hi_version", "r")
    if not file then
        print("failed to open file :/etc/hi_version")
        return nil
    end
    
    for line in file:lines() do
        if line:match("product name%s*:%s*(.*)") then
            local product_name = line:match("product name%s*:%s*(.*)")
            file:close()
            return product_name
        end
    end
    
    -- if not find product name
    file:close()
    return nil
end


local function user_cfg_set(user_cfg)
    local product_name = get_product_name(); 
   
    local password = user_cfg["new_password"]

    -- sys.user.setpasswd(username, password)

    if product_name:match("hg5013") or product_name:match("hg5012ac") then
        -- com.set_luci_pwd("admin", password)
        sys.user.setpasswd("admin", password)
    else
        -- com.set_luci_pwd("user", password)
        sys.user.setpasswd("user", password)
    end

    if product_name:match("hg5013") or product_name:match("hg5012ac") then
        if tostring(uci:get("custom", "first_login", "first_admin_login_flag")) == "1" then
            uci:set("custom", "first_login", "first_admin_login_flag", "0")
            os.execute("sleep 1;uci commit")
        end
    else
        if tostring(uci:get("custom", "first_login", "first_user_login_flag")) == "1" then
            uci:set("custom", "first_login", "first_user_login_flag", "0")
            os.execute("sleep 1;uci commit")
        end
    end
   
end

local function binding_cfg_set(binding_cfg)
    if binding_cfg["user_id"] == nil or #binding_cfg["user_id"] == 0 then
        uci:set("mqtt_vccm", "mqtt_vccm", "mqttBindUserId", "")
        uci:set("mqtt_vccm", "mqtt_vccm", "mqttBindTopic", "")
        uci:set("mqtt_vccm", "mqtt_vccm", "bind_status", "binding")
    else
        uci:set("mqtt_vccm", "mqtt_vccm", "mqttBindUserId", binding_cfg["user_id"])
        uci:set("mqtt_vccm", "mqtt_vccm", "mqttBindTopic", binding_cfg["mqttBindTopic"])
        uci:set("mqtt_vccm", "mqtt_vccm", "bind_status", "online")
    
        if binding_cfg["group_name"] == nil and #binding_cfg["group_name"] ~= 0 then
            uci:set("mqtt_vccm", "mqtt_vccm", "groupname", binding_cfg["group_name"])
        end
    
        if binding_cfg["mqttAddr"] == nil and #binding_cfg["mqttAddr"] ~= 0 then
            uci:set("mqtt_vccm", "mqtt_vccm", "addr", binding_cfg["mqttAddr"])
        end
    end

    if binding_cfg["status"]~=nil then
        if binding_cfg["status"] == "1" then
            os.execute("rm /tmp/binding -rf"); -- zhh
            os.execute("echo 1 > /tmp/bind_success");
        end
    end

    uci:set("mqtt_vccm", "mqtt_vccm", "timestamp", binding_cfg["timestamp"])
    uci:commit()
end

local function mesh_cfg_set(mesh_cfg)
    local lan_mac_file = io.open("/sys/class/net/br-lan/address", "r")
    local self_device_mac = ""

    if lan_mac_file then
        local mac = lan_mac_file:read()

        self_device_mac = mac:gsub(":", "")
        lan_mac_file:close()
    end

    for _, cfg in pairs(mesh_cfg) do
        if cfg["location"] ~= nil then
            if cfg["mac"] == self_device_mac then
                uci:set("TMgr", "local", "self_location", cfg["location"])
                uci:commit("TMgr")
            end
        end
        
        if cfg["hostname"] ~= nil then
            if cfg["mac"] == self_device_mac then
                uci:set("TMgr", "local", "self_hostname", cfg["hostname"])
                uci:commit("TMgr")
            end
        end
    end

    util.ubus("tmgr", "set_mesh", { mesh_cfg = mesh_cfg })
end

local function del_devices_cfg_set(del_devices_cfg)
    -- for _, mac in pairs(del_devices_cfg) do
    --     platform_call.set_del_device(mac)
    -- end
    util.ubus("tmgr", "del_host", { del_devices_cfg = del_devices_cfg })
end

local function upgrade_cfg_check(upgrade_cfg)
    local log = io.open("/tmp/upgrade_cfg.log", "a")
    if log then
        log:write(string.format("[upgrade_cfg_check] time=%s input=%s\n", os.date("%Y-%m-%d %H:%M:%S"), table_to_string.convert(upgrade_cfg)))
    end
    -- if upgrade_cfg["password"] ~= nil and type(upgrade_cfg["password"]) == "string" then
    --     local user = com.getUsernameByRole("user")
    --     local password = upgrade_cfg["password"]
    --     local auth_check_resutl = sys.user.checkpasswd(user, password)
    -- 
    --     if not auth_check_resutl then
    --         if log then log:write("[upgrade_cfg_check] invalid password\n") log:close() end
    --         return "invalid password"
    --     end
    -- end
    local has_device_block = false
    for key, value in pairs(upgrade_cfg) do
        if string.match(key, "^devices_%d+$") then
            has_device_block = true
            if type(value) ~= "table" then
                if log then log:write(string.format("[upgrade_cfg_check] %s not a table\n", key)) log:close() end
                return "invalid devices block"
            end
            if value["upgrade_mac"]==nil or next(value["upgrade_mac"]) == nil then
                if log then log:write(string.format("[upgrade_cfg_check] %s missing upgrade_mac\n", key)) log:close() end
                return "args-'upgrade_mac' is missing"
            end

            if value["device_type"]==nil or type(value["device_type"]) ~= "string" then
                if log then log:write(string.format("[upgrade_cfg_check] %s missing device_type\n", key)) log:close() end
                return "args-'device_type' is missing"
            end

            if value["url"]==nil or type(value["url"]) ~= "string" then
                if log then log:write(string.format("[upgrade_cfg_check] %s missing url\n", key)) log:close() end
                return "args-'url' is missing"
            end

            if #value["url"] >= 512 then
                if log then log:write(string.format("[upgrade_cfg_check] %s url too long len=%d\n", key, #value["url"])) log:close() end
                return "args-'url' is too long (" .. #value["url"] .. " characters)"
            end
        end
    end

    if not has_device_block then
        if log then log:write("[upgrade_cfg_check] no devices_* provided\n") log:close() end
        return "no devices provided"
    end

    if log then log:write("[upgrade_cfg_check] ok\n") log:close() end
end

local function upgrade_cfg_set(upgrade_cfg)
    local log = io.open("/tmp/upgrade_cfg.log", "a")
    if log then
        log:write(string.format("[upgrade_cfg_set] time=%s input=%s\n", os.date("%Y-%m-%d %H:%M:%S"), table_to_string.convert(upgrade_cfg)))
    end
    local first_device_type = 0
    local mac_str_list = ""
    local device_type_list = ""
    local vendor_info_list = ""
    local md5_list = ""
    local url_list = ""
    local md5 = "14868ea497cdd4f7e3487285c7ee884e"
    local vendor_idx = 0

    for key, value in pairs(upgrade_cfg) do
        if string.match(key, "^devices_%d+$") then
            if first_device_type == 0 then
                first_device_type = 1
            else
                mac_str_list = mac_str_list.."|"
                device_type_list = device_type_list.."|"
                vendor_info_list = vendor_info_list.."|"
                url_list = url_list.."|"
                md5_list = md5_list.."|"
            end
            for _, mac in pairs(value["upgrade_mac"]) do
                mac_str_list = mac_str_list.." "..mac
            end
            device_type_list = device_type_list..value["device_type"]
            url_list = url_list..value["url"]

            if value["md5"]~=nil and #value["md5"] > 0 then
                md5_list = md5_list..value["md5"]
            else
                md5_list = md5_list..md5
            end

            if value["vendor_info"]~=nil and #value["vendor_info"] > 0 then
                vendor_info_list = vendor_info_list..value["vendor_info"]
            else
                vendor_info_list = vendor_info_list..tostring(vendor_idx)
                vendor_idx = vendor_idx + 1
            end
        end
    end

    if log then
        log:write(string.format("[upgrade_cfg_set] mac_list=%s\n", mac_str_list))
        log:write(string.format("[upgrade_cfg_set] device_type_list=%s\n", device_type_list))
        log:write(string.format("[upgrade_cfg_set] vendor_info_list=%s\n", vendor_info_list))
        log:write(string.format("[upgrade_cfg_set] md5_list=%s\n", md5_list))
        log:write(string.format("[upgrade_cfg_set] url_list=%s\n", url_list))
        log:flush()
    end

    platform_call.upgrade_devices(mac_str_list, device_type_list, vendor_info_list, md5_list, url_list)
    if log then log:write("[upgrade_cfg_set] dispatched to platform_call.upgrade_devices\n") log:close() end
end

local function project_cfg_check(project_cfg)
    if type(project_cfg) ~= "table" then
        return "Invalid format"
    end

    if project_cfg["project_id"] == nil then
        return "Invalid args[project_id]"
    end

    if type(project_cfg["project_id"]) ~= "string" or type(project_cfg["password"]) ~= "string" or type(project_cfg["batch"]) ~= "string" then
        return "Invalid project cfg format"
    end
    
    if project_cfg["batch"] == "0" then
        local decrypt_pwd = platform_call.decrypt_pwd(project_cfg["password"])
        local user = com.getUsernameByRole("user")
        local check_result = sys.user.checkpasswd(user, decrypt_pwd)

        if not check_result then
            return "Failed Wrong password"
        end
    end
end

local function project_cfg_set(project_cfg)
    uci:set("mqtt_vccm", "mqtt_vccm", "project_id", project_cfg["project_id"])
    os.execute("sleep 1;uci commit")
    -- platform_call.sync_project_id_to_udp_local_device()
end

-- For WAN Configuration
local function wan_cfg_check(wan_cfg)
    for key, value in pairs(wan_cfg) do
        if type(value)~="table" then
            return "invalid args";
        end
        if value["connect_mode"]==nil then
            return "args-'connect_mode' is missing"
        end

        if value["connect_mode"]== "pppoe" then
            if value["username"]==nil then
                return "args-'username' is missing"
            end

            if value["password"]==nil then
                return "args-'password' is missing"
            end

            if value["service_name"]~=nil and type(value["service_name"])~="string" then
                return "args-'service_name' is invalid"
            end
        elseif value["connect_mode"]~= "dhcp" then
            return "args-'connect_mode' is error"
        end
    end
end

local function wan_cfg_set(wan_cfg)
    for key, value in pairs(wan_cfg) do
        if value["connect_mode"]== "pppoe" then
            platform_call.set_wan_cfg(value["connect_mode"], value["username"], value["password"], value["service_name"])
        elseif value["connect_mode"]== "static" then
            local dnsv4_1, dnsv4_2 = nil, nil

            local i = 1
            for ip in string.gmatch(value["dnsv4"], "([^,]+)") do
                if i == 1 then
                    dnsv4_1 = ip
                elseif i == 2 then
                    dnsv4_2 = ip
                end
                i = i + 1
            end

            if dnsv4_1 then 
                print("dnsv4_1: " .. dnsv4_1) 
            else
                dnsv4_1 = ""
            end

            if dnsv4_2 then 
                print("dnsv4_2: " .. dnsv4_2) 
            else
                dnsv4_1 = ""
            end
            platform_call.set_wan_cfg(value["connect_mode"], value["ipv4_addr"], value["ipv4_mask"], value["ipv4_gateway"], dnsv4_1, dnsv4_2)
        elseif value["connect_mode"]== "dhcp" then
            platform_call.set_wan_cfg(value["connect_mode"])
        else
            return "args-'connect_mode' is error"
        end
    end
end
-- End of WAN


-- Add by wzx for Firewall, vpn, qos, iptv
local function firewall_cfg_check(firewall_cfg)
    if type(firewall_cfg)~="table" then
        return "invalid args";
    end
end

local function firewall_cfg_set(firewall_cfg)
    platform_call.set_firewall_cfg(firewall_cfg["level"], firewall_cfg["prevent_dos"], firewall_cfg["smart_prevent"]);
end

local function iptv_cfg_check(iptv_cfg)

    if type(iptv_cfg)~="table" then 
        return "invalid args";
    end

    if iptv_cfg["enable"]==nil then
        return "args-'enable' is missing"
    end

    if iptv_cfg["enable"] == "1" and iptv_cfg["vlan_id"]==nil then
        return "args-'vlan_id' is missing"
    end

    if iptv_cfg["enable"] == "1" and (tonumber(iptv_cfg["vlan_id"]) <= 0 or tonumber(iptv_cfg["vlan_id"]) > 4095) then
        return "args-'vlan_id' is invalid"
    end


    if iptv_cfg["enable"] == "1" and iptv_cfg["lan_port"]==nil then
        return "args-'lan_port' is missing"
    end

    local wan_port = platform_call.lua_get_wan_port()
    if tonumber(wan_port) <= 0 then
        return "args-'wan_port' is missing"
    end

    if iptv_cfg["lan_port"]~=nil and tonumber(iptv_cfg["lan_port"]) == tonumber(wan_port) then
        return "args-'lan_port' is invalid, wan_port is "..wan_port
    end 

end

local function iptv_cfg_set(iptv_cfg)
    platform_call.lua_set_iptv_cfg(iptv_cfg["enable"], iptv_cfg["vlan_id"], iptv_cfg["lan_port"]);
    os.execute("sleep 5")
end

local function qos_cfg_check(qos_cfg)
    if type(qos_cfg)~="table" then
        return "invalid args";
    end
    if qos_cfg["ai_qos"]==nil then
        return "args-'ai_qos' is missing"
    end
end

local function qos_cfg_set(qos_cfg)
    platform_call.lua_set_smart_qos_cfg(qos_cfg["ai_qos"]);
end


local function vpn_cfg_check(vpn_cfg)
    if type(vpn_cfg)~="table" then
        return "invalid args";
    end
    if vpn_cfg["enable"]==nil then
        return "args-'enable' is missing"
    end
end

local function vpn_cfg_set(vpn_cfg)
    platform_call.set_vpn_cfg(vpn_cfg["enable"]);
end

-- End of Add by wzx for Firewall, vpn, qos, iptv

local function _cfg_set(request_json)
    local cfg_tbl = common_api.protect_run(jsonc.parse, request_json)
    local ret_tbl = {
        ["result_code"] = -1,
        ["result_desc"] = "",
    }

    local debug = io.open("/tmp/lua_cfg", "w")

    local cfg_method = {
        ["lan_cfg"] = {
            ["check"] = lan_cfg_check,
            ["set"] = lan_cfg_set,
        },
        ["wlan_cfg"] = {
            ["check"] = wlan_cfg_check,
            ["set"] = wlan_cfg_set,
        },
        ["client_cfg"] = {
            ["check"] = client_cfg_check,
            ["set"] = client_cfg_set,
        },
        ["user_cfg"] = {
            ["check"] = user_cfg_check,
            ["set"] = user_cfg_set,
        },
        ["binding_cfg"] = {
            ["check"] = binding_cfg_check,
            ["set"] = binding_cfg_set,
        },
        ["mesh_cfg"] = {
            ["check"] = mesh_cfg_check,
            ["set"] = mesh_cfg_set,
        },
        ["family_cfg"] = {
            ["check"] = family_cfg_check,
            ["set"] = family_cfg_set,
        },
        ["del_family_cfg"] = {
            ["check"] = del_family_cfg_check,
            ["set"] = del_family_cfg_set,
        },
        ["del_devices_cfg"] = {
            ["check"] = del_devices_cfg_check,
            ["set"] = del_devices_cfg_set,
        },
         ["upgrade_cfg"] = {
            ["check"] = upgrade_cfg_check,
            ["set"] = upgrade_cfg_set,
        },
        ["project_cfg"] = {
            ["check"] = project_cfg_check,
            ["set"] = project_cfg_set,
        },
        ["wan_cfg"] = {
            ["check"] = wan_cfg_check,
            ["set"] = wan_cfg_set,
        },
        ["firewall_cfg"] = {
            ["check"] = firewall_cfg_check,
            ["set"] = firewall_cfg_set,
        },
        ["iptv_cfg"] = {
            ["check"] = iptv_cfg_check,
            ["set"] = iptv_cfg_set,
        },
        ["qos_cfg"] = {
            ["check"] = qos_cfg_check,
            ["set"] = qos_cfg_set,
        },
        ["vpn_cfg"] = {
            ["check"] = vpn_cfg_check,
            ["set"] = vpn_cfg_set,
        },
    }

    if type(cfg_tbl) ~= "table" then
        ret_tbl["result_desc"] = "Invalid json"
        return common_api.protect_run(jsonc.stringify, ret_tbl)
    end


    if debug then
        local str = table_to_string.convert(cfg_tbl)
        debug:write(str)
    end


    for key, value in pairs(cfg_tbl) do
        if cfg_method[key] == nil or type(value) ~= "table" then
            ret_tbl["result_desc"] = "Invalid cfg -> [" .. key .. "]"
            return common_api.protect_run(jsonc.stringify, ret_tbl)
        end
    
        local check_result = cfg_method[key]["check"](value)
        if type(check_result) == "string" then
            ret_tbl["result_desc"] = key .. " config failed, " .. check_result
            return common_api.protect_run(jsonc.stringify, ret_tbl)
        end
    end

    for key, value in pairs(cfg_tbl) do
        cfg_method[key]["set"](value)
    end

    ret_tbl["result_code"] = 0

    if debug then
        local str = table_to_string.convert(ret_tbl)
        debug:write(str)
        debug:close()
    end

    return common_api.protect_run(jsonc.stringify, ret_tbl)
end

function cfg_set(request_json)
    return common_api.protect_run(_cfg_set, request_json)
end

return {
    cfg_set = cfg_set,
}