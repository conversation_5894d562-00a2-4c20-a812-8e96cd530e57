package.path = package.path .. ";/usr/lib/lua/mqtt_vccm/?.lua;/usr/lib/lua/luci/?.lua;"
local util = require("luci.util");
local uci = require("luci.model.uci").cursor();
local table_to_string = require("table_to_string");
local common_api = require("common")

local method = {};

local throughput_type ={
    ["0"] = "wan",
    ["1"] = "wlan"
};

local wan_pon_speed = {
    upload = nil,
    download = nil
};

local wan_pon_bytes_start = {
    tx_bytes = "",
    rx_bytes = ""
};

local wan_pon_bytes_end = {
    tx_bytes = "",
    rx_bytes = ""
};

local wlan_bytes_start = {
    tx_bytes = "",
    rx_bytes = ""
};

local wlan_bytes_end = {
    tx_bytes = "",
    rx_bytes = ""
};

local time_difference = 0;

function get_product_name()
    local file = io.open("/etc/hi_version", "r")
    if not file then
        return nil
    end
    
    for line in file:lines() do
        if line:match("product name%s*:%s*(.*)") then
            local product_name = line:match("product name%s*:%s*(.*)")
            file:close()
            return product_name
        end
    end
    
    -- if not find product name
    file:close()
    return nil
end

function get_device_type()
	local product_name = get_product_name()
	if (product_name:match("v2802ach")) or (product_name:match("hg3232axt")) then
		return "ONU"
	else
		return "ROUTER"
	end
end

local function get_wan_device_name()
    --[[local device_model = util.trim(util.exec("sed -n 's/.*product name\\s*:\\s*\\(.*\\)/\\1/p' /etc/hi_version"))
    if device_model:match("wrt_v2802ach")~=nil then
        return "pon"
    else
        -- router need get wan port; TODO
        return "eth0"
    end]]--
    local wan_id = 1;
    local ip_version = 1; -- 1: ipv4, 2: ipv6, 3: ipv4/ipv6
    local syswan_info = util.ubus("syswan", "all", "all") or "";

    if syswan_info == "" then
        return ""
    end

    for _, value in pairs(syswan_info) do
        if value["service"] == 1 then
            wan_id = value["wanId"]
            ip_version = value["ipProtocol"]
        end
    end

    local wan_index= ""
    local wan_dev = "";
    if ip_version == 2 then
        wan_index = "wan_" .. wan_id .. "_v6";
        wan_dev = uci:get("network", wan_index, "device") or "pon"
    else
        wan_index = "wan_" .. wan_id;
        wan_dev = uci:get("network", wan_index, "device") or "pon"
    end

    return wan_dev
end

local function get_pon_bytes_info()
	-- local wan_dev = uci:get("network", "wan", "device") or "eth0"
	
    local wan_dev = ""
    if get_device_type() == "ONU" then
        wan_dev = "pon"
    else
        wan_dev = get_wan_device_name()
    end

    if wan_dev == "" then
        return "0", "0"
    end
    -- local network_deivces = util.ubus("luci-rpc", "getNetworkDevices", {}) or {}

    -- if  network_deivces==nil  or network_deivces[wan_dev] == nil then
    --     return "0", "0"
    -- end

    -- local network_deivce_stats_lan = network_deivces[wan_dev]["stats"]

    -- if network_deivce_stats_lan == nil then 
    --     return "0", "0"
    -- end

    -- local txbytes = network_deivce_stats_lan["tx_bytes"]
    -- local rxbytes = network_deivce_stats_lan["rx_bytes"]

    local txbytes = 0
    local rxbytes = 0

    local ftx = io.open("/sys/class/net/"..wan_dev.."/statistics/tx_bytes", "r")
    local frx = io.open("/sys/class/net/"..wan_dev.."/statistics/rx_bytes", "r")
    if ftx then
        local v = ftx:read("*l") or "0"
        txbytes = tonumber(v) or 0
        ftx:close()
    end
    if frx then
        local v = frx:read("*l") or "0"
        rxbytes = tonumber(v) or 0
        frx:close()
    end

    return txbytes, rxbytes;
end

local function get_wlan_bytes_info()
    local result = "";
    local wlan_2g;
    local wlan_5g;
    local wlan_2g_rx_bytes_sum = 0;
    local wlan_2g_tx_bytes_sum = 0;
    local wlan_5g_rx_bytes_sum = 0;
    local wlan_5g_tx_bytes_sum = 0;
    local wlan_all_rx_bytes_sum = 0;
    local wlan_all_tx_bytes_sum = 0;

    wlan_2g = tonumber(common_api.get_cmd_result("hi_cfm get board.wlan24") or "0")
    wlan_5g = tonumber(common_api.get_cmd_result("hi_cfm get board.wlan58") or "0")

    if (wlan_2g > 0) and (uci:get("wireless", "radio0", "disabled") ~= "1") then
        -- 原始通过 ubus 读取接口统计的代码（保留注释）：
        -- local network_deivces = util.ubus("luci-rpc", "getNetworkDevices", {}) or {}
        -- local network_deivce_stats_lan = nil
        -- if network_deivces["default_radio0"] ~= nil then
        --     network_deivce_stats_lan = network_deivces["default_radio0"]["stats"]
        -- -- elseif network_deivces["em_bh_2g_ap"] ~= nil then
        -- --     network_deivce_stats_lan = network_deivces["em_bh_2g_ap"]["stats"]
        -- end
        -- if network_deivce_stats_lan ~= nil then
        --     wlan_2g_tx_bytes_sum = network_deivce_stats_lan["tx_bytes"]
        --     wlan_2g_rx_bytes_sum = network_deivce_stats_lan["rx_bytes"]
        -- end

        -- 改为从 sysfs 读取统计数据
        local ftx = io.open("/sys/class/net/default_radio0/statistics/tx_bytes", "r")
        local frx = io.open("/sys/class/net/default_radio0/statistics/rx_bytes", "r")
        if ftx then
            local v = ftx:read("*l") or "0"
            wlan_2g_tx_bytes_sum = tonumber(v) or 0
            ftx:close()
        end
        if frx then
            local v = frx:read("*l") or "0"
            wlan_2g_rx_bytes_sum = tonumber(v) or 0
            frx:close()
        end
    end

    if (wlan_5g > 0) and (uci:get("wireless", "radio1", "disabled") ~= "1") then
        -- 原始通过 ubus 读取接口统计的代码（保留注释）：
        -- local network_deivces = util.ubus("luci-rpc", "getNetworkDevices", {}) or {}
        -- local network_deivce_stats_lan = nil
        -- if network_deivces["default_radio1"]~=nil then
        --     network_deivce_stats_lan = network_deivces["default_radio1"]["stats"]
        -- -- elseif network_deivces["em_bh_5g_ap"]~=nil then
        -- --     network_deivce_stats_lan = network_deivces["em_bh_5g_ap"]["stats"]
        -- end
        -- if network_deivce_stats_lan ~= nil then
        --     wlan_5g_tx_bytes_sum = network_deivce_stats_lan["tx_bytes"]
        --     wlan_5g_rx_bytes_sum = network_deivce_stats_lan["rx_bytes"]
        -- end

        -- 改为从 sysfs 读取统计数据
        local ftx = io.open("/sys/class/net/default_radio1/statistics/tx_bytes", "r")
        local frx = io.open("/sys/class/net/default_radio1/statistics/rx_bytes", "r")
        if ftx then
            local v = ftx:read("*l") or "0"
            wlan_5g_tx_bytes_sum = tonumber(v) or 0
            ftx:close()
        end
        if frx then
            local v = frx:read("*l") or "0"
            wlan_5g_rx_bytes_sum = tonumber(v) or 0
            frx:close()
        end
    end

    wlan_all_rx_bytes_sum = wlan_2g_rx_bytes_sum + wlan_5g_rx_bytes_sum;
    wlan_all_tx_bytes_sum = wlan_2g_tx_bytes_sum + wlan_5g_tx_bytes_sum;
    -- print("wlan_2g_rx_bytes_sum:"..wlan_2g_rx_bytes_sum..", wlan_5g_rx_bytes_sum:"..wlan_5g_rx_bytes_sum)
    -- print("wlan_2g_tx_bytes_sum:"..wlan_2g_tx_bytes_sum..", wlan_5g_tx_bytes_sum:"..wlan_5g_tx_bytes_sum)
    return tostring(wlan_all_tx_bytes_sum), tostring(wlan_all_rx_bytes_sum);
end

local function get_all_interface_type_bytes_start()
    wan_pon_bytes_start["tx_bytes"], wan_pon_bytes_start["rx_bytes"] = get_pon_bytes_info();
    -- print("start wan tx:"..wan_pon_bytes_start["tx_bytes"]..", rx:"..wan_pon_bytes_start["rx_bytes"])
    wlan_bytes_start["tx_bytes"], wlan_bytes_start["rx_bytes"] = get_wlan_bytes_info();
    -- print("start wlan tx:"..wlan_bytes_start["tx_bytes"]..", rx:"..wlan_bytes_start["rx_bytes"])
end

local function get_all_interface_type_bytes_end()
    wan_pon_bytes_end["tx_bytes"], wan_pon_bytes_end["rx_bytes"] = get_pon_bytes_info();
    -- print("after wan tx:"..wan_pon_bytes_end["tx_bytes"]..", rx:"..wan_pon_bytes_end["rx_bytes"])
    wlan_bytes_end["tx_bytes"], wlan_bytes_end["rx_bytes"] = get_wlan_bytes_info();
    -- print("after wlan tx:"..wlan_bytes_end["tx_bytes"]..", rx:"..wlan_bytes_end["rx_bytes"])
end

local function get_all_interface_type_bytes_info()
    local time_sleep = 0.2;

    get_all_interface_type_bytes_start();
    os.execute("sleep " .. time_sleep);
    get_all_interface_type_bytes_end();
    time_difference = time_sleep;
end

local function get_interface_type_bytes_difference(interface_type_bytes_start, interface_type_bytes_end)
    local tx_bytes_difference = 0
    local rx_bytes_difference = 0
    tx_bytes_difference = tonumber(interface_type_bytes_end["tx_bytes"]) - tonumber(interface_type_bytes_start["tx_bytes"]);
    rx_bytes_difference = tonumber(interface_type_bytes_end["rx_bytes"]) - tonumber(interface_type_bytes_start["rx_bytes"]);

    return tx_bytes_difference, rx_bytes_difference;
end

local function get_throughput_info(interface_type)
    local throughput_info_node = {
        interface = interface_type,
        upload = "",
        download = ""
    }; 

    local tx_bytes_difference = 0;
    local rx_bytes_difference = 0;

    if interface_type == "wan" then
        if wan_pon_speed["upload"] == nil or wan_pon_speed["download"] == nil then
            tx_bytes_difference, rx_bytes_difference = get_interface_type_bytes_difference(wan_pon_bytes_start, wan_pon_bytes_end);
            throughput_info_node["upload"] = string.format("%d", math.floor(tx_bytes_difference/ (1024 * (time_difference + 0.1))));
            throughput_info_node["download"] = string.format("%d", math.floor(rx_bytes_difference/ (1024 * (time_difference + 0.2))));
        else
            throughput_info_node["upload"] = wan_pon_speed["upload"];
            throughput_info_node["upload"] = wan_pon_speed["download"];
        end
        -- print("wan upload:"..throughput_info_node["upload"]..", download:"..throughput_info_node["download"])
    elseif interface_type == "wlan" then
        tx_bytes_difference, rx_bytes_difference = get_interface_type_bytes_difference(wlan_bytes_start, wlan_bytes_end);
        throughput_info_node["upload"] = string.format("%d", math.floor(rx_bytes_difference/ (1024 * (time_difference + 0.1))));
        throughput_info_node["download"] = string.format("%d", math.floor(tx_bytes_difference/ (1024 * (time_difference + 0.2))));
        -- print("tx_bytes_difference:"..tx_bytes_difference..", rx_bytes_difference:"..rx_bytes_difference..", time_difference:"..time_difference)
        -- print("(1024 * time_difference):"..(1024 * time_difference)..", math.floor(rx_bytes_difference/ (1024 * time_difference)):"..math.floor(rx_bytes_difference/ (1024 * time_difference)))
        -- print("wlan upload:"..throughput_info_node["upload"]..", download:"..throughput_info_node["download"])
    end

    return throughput_info_node;
end

function method.get()
    local info = {};

    get_all_interface_type_bytes_info();

    for key, value in pairs(throughput_type) do
        info[key] = get_throughput_info(value);
    end
    
    return info;
end

-- 导出函数供其他模块使用
method.get_pon_bytes_info = get_pon_bytes_info
method.get_wlan_bytes_info = get_wlan_bytes_info

return method;
-- method.get();
