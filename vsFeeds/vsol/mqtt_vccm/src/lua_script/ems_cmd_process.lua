package.path = package.path .. ";/usr/lib/lua/mqtt_vccm/?.lua;/usr/lib/lua/luci/?.lua;"
local jsonc = require "luci.jsonc";
local common_api = require("common");
local platform_call = require("platform_call");
local table_to_string = require("table_to_string");
local util = require "luci.util";
local uci = require("luci.model.uci").cursor();

local function ping_full_cmd(args)
    local addr = args["addr"];
    local protocol = args["protocol"];
    if (type(addr)~="string" or addr:match("[^0-9a-zA-Z%.:]")~=nil) then
        return nil;
    end

    if protocol=="ipv4/ipv6" then
        if addr:match(":")~=nil then
            protocol = "ipv6"
        else
            protocol = "ipv4"
        end
    end
    if protocol=="ipv4" then
        return "/bin/ping -c 5 "..addr;
    end
    if protocol=="ipv6" then
        return "/bin/ping6 -c 5 "..addr;
    end

    return nil
end

local function traceroute_full_cmd(args)
    local addr = args["addr"];
    local protocol = args["protocol"];
    if (type(addr)~="string" or addr:match("[^0-9a-zA-Z%.:]")~=nil) then
        return nil;
    end

    if protocol=="ipv4/ipv6" then
        if addr:match(":")~=nil then
            protocol = "ipv6"
        else
            protocol = "ipv4"
        end
    end
    if protocol=="ipv4" then
        return "/bin/traceroute "..addr;
    end
    if protocol=="ipv6" then
        return "/bin/traceroute6 "..addr;
    end

    return nil
end

local function optimize_channel_full_cmd(args)
    return "optimize_channel"
end

local function stop_full_cmd(args)
    return "stop";
end

local function cmd_reboot(args)
    return "reboot";
end

local function cmd_reset(args)
    return "restore";
end

local function self_diagnosis_full_cmd(args)
    return "self_diagnosis"
end

--neal.luo
local function extract_after_space(input_str)
    local space_pos = string.find(input_str, " ")

    if space_pos then
        local result = string.sub(input_str, space_pos + 1)

        return result
    else
        return nil, "No space found in the input string"
    end
end

local function write_cmd_to_file(cmd)
    local cmd_run_file = io.open("/tmp/ems_cmd_run", "w")

    if cmd_run_file == nil then
        cmd = "none"
        return false
    end

    cmd_run_file:write(cmd)

    cmd_run_file:close()

    return true
end

local function get_scan_result()
    local output = {}

    output["pairable_device"] = platform_call.get_scan_result_table();

    if output["pairable_device"] == nil then
        return false
    end
    local content = common_api.protect_run(jsonc.stringify, output);
    if content == nil then
        content = "{}"
        return false
    end

    local output_file = io.open("/tmp/ems_cmd_result", "w")

    if output_file == nil then
        return false
    end

    output_file:write(content)

    output_file:close()

    write_cmd_to_file("quick_pairing_scan")

    return true
end

local function quick_pairing_full_cmd(args)
    if (args["mac"] ~= nil) then
        return "quick_pairing_mac " .. tostring(args["mac"])
    else
        return "quick_pairing_scan"
    end
end
--end

local function get_weak_signal_sta_list_table()
    local ret_tbl = {
        cmd = "self_diagnosis",
        result_code = "0",
        result_desc = "",
        signal_quality = {
        }
    };

    local client_info = util.ubus("tmgr", "all", {}).client_list or {};

    for index, client in pairs(client_info) do  
        if client["online"] == "1" and client["connect_type"] == "wireless" then  
              local rssi = tonumber(client["rssi"]);

              if rssi > -110 and rssi < -75 then
                local weak_sta_node = {
                    hostname = "",
                    rssi = ""
                }

                weak_sta_node["hostname"] = client["hostname"];
                weak_sta_node["rssi"] = client["rssi"];
                table.insert(ret_tbl["signal_quality"], weak_sta_node);
              end
        end  
    end

    return ret_tbl
end

--读取 request 以获取完整的指令返回
function cmd_set(request_json)
    local fd = nil;
    local full_cmd = nil;
    local request = common_api.protect_run(jsonc.parse, request_json);
    local ret_tbl = {
        ["result_code"] = -1,
        ["result_desc"] = "",
    };
    local full_cmd_tbl = {
        ["ping"] = ping_full_cmd,
        ["traceroute"] = traceroute_full_cmd,
        ["optimize_channel"] = optimize_channel_full_cmd,
        ["stop"] = stop_full_cmd,
        ["reboot"] = cmd_reboot,
        ["restore"] = cmd_reset,
        ["self_diagnosis"] = self_diagnosis_full_cmd,
        ["quick_pairing"] = quick_pairing_full_cmd,
    }
    if (type(request)~="table") then
        ret_tbl["result_desc"] = "Invalid json";
        return common_api.protect_run(jsonc.stringify, ret_tbl);
    end
    if type(request["cmd"])~="string" or full_cmd_tbl[request["cmd"]]==nil then
        ret_tbl["result_desc"] = "Invalid cmd";
        return common_api.protect_run(jsonc.stringify, ret_tbl);
    end
    if type(request["args"])~="table" then
        ret_tbl["result_desc"] = "Invalid args";
        return common_api.protect_run(jsonc.stringify, ret_tbl);
    end
    full_cmd = full_cmd_tbl[request["cmd"]](request["args"]);
    if type(full_cmd)~="string" or full_cmd:len()==0 then
        ret_tbl["result_desc"] = "Invalid cmd or args, set cmd failed";
        return common_api.protect_run(jsonc.stringify, ret_tbl);
    end

    if full_cmd=="stop" then
        local run_pid = tonumber(common_api.get_cmd_result("cat /tmp/ems_cmd_run | awk '{print $1}' | xargs pgrep"));
        if run_pid==nil or run_pid<0 then
            ret_tbl["result_code"] = 0;
            return common_api.protect_run(jsonc.stringify, ret_tbl);
        end
        os.execute("kill -9 "..run_pid);
        run_pid = tonumber(common_api.get_cmd_result("cat /tmp/ems_cmd_run | awk '{print $1}' | xargs pgrep"));
        if run_pid==nil or run_pid<0 then
            ret_tbl["result_code"] = 0;
            ret_tbl["result_desc"] = "stop cmd success";
            return common_api.protect_run(jsonc.stringify, ret_tbl);
        end
        ret_tbl["result_desc"] = "stop cmd failed, run_pid:"..run_pid;
        return common_api.protect_run(jsonc.stringify, ret_tbl);
    elseif full_cmd=="optimize_channel" then
        -- platform_call.optimize_channel()
        -- os.execute("sleep 5")
        local channel_24g = uci:get("wireless", "radio0", "channel") or ""
        local channel_5g = uci:get("wireless", "radio1", "channel") or ""
        if (channel_24g ~= "auto") then
            uci:set("wireless", "radio0", "channel", "auto")
        end
        if (channel_5g ~= "auto") then
            uci:set("wireless", "radio1", "channel", "auto")
        end
        uci:commit("wireless")
    elseif full_cmd=="reboot" then
    	platform_call.cmd_reboot()    	
        os.execute("sleep 1")
    elseif full_cmd=="restore" then
    	platform_call.cmd_restore();
    elseif full_cmd=="self_diagnosis" then
        local debug = io.open("/tmp/cmd_set","w")
    	local args = request["args"];    	
        if args["signal_quality"] then
            ret_tbl = get_weak_signal_sta_list_table();
        else
            debug:write("cmd diagnosis conncetion speed\n")
            ret_tbl = platform_call.cmd_diagnosis(args["dns_delay"],args["connection_speed"]);
        end

        if debug then
            debug:write("ret_body"..common_api.protect_run(jsonc.stringify, ret_tbl).."\n")
            debug:close()
        end

        return common_api.protect_run(jsonc.stringify, ret_tbl);
    elseif full_cmd == "quick_pairing_scan" then
        get_scan_result();
    elseif string.find(full_cmd, "quick_pairing_mac") then
        write_cmd_to_file(full_cmd)
        local pair_mac, err = extract_after_space(full_cmd)
        platform_call.cmd_pair(tostring(pair_mac));
    else
        local run_pid = tonumber(common_api.get_cmd_result("cat /tmp/ems_cmd_run | awk '{print $1}' | xargs pgrep"));
        if (type(run_pid)=="number" and run_pid>0) then
            ret_tbl["result_desc"] = "cmd busy";
            return common_api.protect_run(jsonc.stringify, ret_tbl);
        end
        fd = io.open("/tmp/ems_cmd_run", "w");
        if fd==nil then
            ret_tbl["result_desc"] = "Set cmd failed, syscall error";
            return common_api.protect_run(jsonc.stringify, ret_tbl);
        end
        fd:write(full_cmd);
        fd:close();
        os.execute("sh /tmp/ems_cmd_run > /tmp/ems_cmd_result 2>&1 &");
    end

    ret_tbl["result_code"] = 0;
    return common_api.protect_run(jsonc.stringify, ret_tbl);
end

local function read_hole_file(file_dir)
    local file_content = "{}";
    local fd = io.open(file_dir, "r");
    if fd==nil then
        return file_content;
    end

    file_content = fd:read("*a");
    fd:close();
    return file_content;
end

function cmd_get()
    local ret_tbl = {
        ["status"]="idle",
        ["run_cmd"]="",
        ["output"]=""
    }
    local run_pid = tonumber(common_api.get_cmd_result("cat /tmp/ems_cmd_run | awk '{print $1}' | xargs pgrep"));
    if (type(run_pid)=="number" and run_pid>0) then
        ret_tbl["status"] = "busy";
    end

    ret_tbl["run_cmd"] = read_hole_file("/tmp/ems_cmd_run");

    if(ret_tbl["run_cmd"] == "self_diagnosis") then
        ret_tbl["output"] = common_api.protect_run(jsonc.parse, read_hole_file("/tmp/ems_cmd_result"));
    elseif(ret_tbl["run_cmd"] == "quick_pairing_scan") then
        ret_tbl["run_cmd"] = "quick_pairing"
        ret_tbl["output"] = common_api.protect_run(jsonc.parse, read_hole_file("/tmp/ems_cmd_result"));
    elseif string.find(ret_tbl["run_cmd"], "quick_pairing_mac") then
        ret_tbl["run_cmd"] = "quick_pairing"
        ret_tbl["output"] = platform_call.get_pair_result_table()
    else
        ret_tbl["output"] = read_hole_file("/tmp/ems_cmd_result");
    end
    return common_api.protect_run(jsonc.stringify, ret_tbl);
end

-- local test = platform_call.cmd_diagnosis(nil, "")
-- local ge_port = common_api.get_cmd_result("hi_cfm get board.ge_num") or "0"
-- local fe_port = common_api.get_cmd_result("hi_cfm get board.fe_num") or "0"
-- local port_num = tonumber(ge_port) + tonumber(fe_port)
-- for key, _ in pairs(test["connection_speed"]) do
--     if tonumber(key) > 1 then
--         test["connection_speed"][key] = nil
--     end
-- end
-- print("ZHH test:"..table_to_string.convert(test))

return {
    cmd_set = cmd_set,
    cmd_get = cmd_get
}
