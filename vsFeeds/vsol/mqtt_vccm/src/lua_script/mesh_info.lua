package.path = package.path .. ";/usr/lib/lua/mqtt_vccm/?.lua;/usr/lib/lua/luci/?.lua;"
local util = require("luci.util")
local jsonc = require "luci.jsonc"
local uci = require("luci.model.uci").cursor()
local platform_call = require("platform_call")
local table_to_string = require("table_to_string");
local throughput_info = require("throughput_info");
local method = {}
local info = {}

local debug = io.open("/tmp/lua_mesh_info","w")

local wlan_type_map_hostname = {
	["type0"] = "HG5013",
	["type1"] = "HG5013",
}

local wlan_type_map_devicetype = {
	["type0"] = "HG5013",
	["type1"] = "HG5013",
}

local mesh_enable;
local topo_error = 0;

function get_product_name()
    local file = io.open("/etc/hi_version", "r")
    if not file then
        return nil
    end
    
    for line in file:lines() do
        if line:match("product name%s*:%s*(.*)") then
            local product_name = line:match("product name%s*:%s*(.*)")
            file:close()
            return product_name
        end
    end
    
    -- if not find product name
    file:close()
    return nil
end

function get_vendor_info()
	local cust = io.open("/etc/custname", "r")
	local file = io.open("/root/vstvendor", "r")

	if cust then
		local content = cust:read("*all")
		content = string.gsub(content, "\n", "")
		cust:close()
		local vendor = uci:get("mqtt_vccm", "mqtt_vccm", "vendor_id") or "ONU"
		return vendor .. "-" .. content
	elseif file then
		local content = file:read("*all")
		content = string.gsub(content, "\n", "")
		file:close()
		return content
	else
		local vendor = uci:get("mqtt_vccm", "mqtt_vccm", "vendor_id")
		if vendor == nil then
			return "ONU-VSOLver"
		end
		return vendor
	end
end

local function deep_copy(original)
	local copy = {}

	for key, value in pairs(original) do
		if type(value) == "table" then
			copy[key] = deep_copy(value)
		else
			copy[key] = value
		end
	end

	return copy
end


local function get_wan_speed()
	local upload, download = platform_call.get_wan_speed()
	
	local upload_str = tostring(upload)
	local download_str = tostring(download)
	
	return upload_str, download_str
end

local function read_column(file_path, line_number, column_number)
	local current_line = 0

	for line in io.lines(file_path) do
		current_line = current_line + 1
		if current_line == line_number then
			local columns = {}

			for column in line:gmatch("[^%&]+") do
				table.insert(columns, column)
			end

			return columns[column_number]
		end
	end

	return nil
end

local function file_exists(filename)
	local file = io.open(filename, "r")
	if file then
		io.close(file)
		return true
	else
		return false
	end
end

local master_map_slave = {}

local function insert_child(parent_mac, new_child_mac)
	if master_map_slave[parent_mac] then
		for _, child_mac in pairs(master_map_slave[parent_mac]) do
			if child_mac == new_child_mac then
				return
			end
		end

		local child_keys = {}
		for key in pairs(master_map_slave[parent_mac]) do
			table.insert(child_keys, key)
		end

		local new_index = "mac_" .. tostring(#child_keys)
		master_map_slave[parent_mac][new_index] = new_child_mac
	else
		master_map_slave[parent_mac] = { ["mac_0"] = new_child_mac }
	end
end

local function get_online_status()
	local wan_data = util.ubus("syswan", "all", {})
	local online_status = "connected"

	if wan_data == nil then
		return online_status
	end

	for _, value in pairs(wan_data) do
		local ipv4Addr = ""
		local ipv6Addr = ""

		if value["wanMode"] == 0 then -- Bridge
			if value["status"] then
				-- status_ipv4 = "online"
				-- status_ipv6 = "online"
				online_status = "online"
			end
		else -- Route
			ipv4Addr = value["ip"]
			ipv6Addr = value["ipv6Addr"]

			if value["status"] then
				if ipv4Addr ~= "" or ipv6Addr ~= "" then
					online_status = "online"
					break
				end
			end
		end
	end

	return online_status
end

local function get_self_device_wan_status()
	local wan_status = "disconnected"
	wan_status = get_online_status()

	return wan_status
end

local g_vendor_info = get_vendor_info();
local g_device_type = uci:get("mqtt_vccm", "mqtt_vccm", "device_type") or "unknown";
if (g_device_type == "unknown") then
	local product_name = get_product_name()
	if (product_name:match("v2802ach")) then
		g_device_type = "V2802AC-H";
	elseif (product_name:match("hg3232axt")) then
		g_device_type = "HG3232AXT-H";
	elseif (product_name:match("hg5013")) then
		g_device_type = "HG5013";
	end
end

local function get_self_device_mesh_info()

	local mesh_info_node = {
		project_id = "",
		upgrade_model = "",
		vendor_info = "",
		device_pn_type = "",
		hostname = "",
		hardware_ver = "",
		mac = "",
		software_ver = "",
		device_type = "",
		lan_ip = "",
		connect_type = "",
		upload = "",
		download = "",
		uptime = "",
		location = "",
		child_devices = {},
	}

	local lan_mac_file = io.open("/sys/class/net/br-lan/address", "r")
	local system_info = util.ubus("system", "info", {})
	local device_type = ""

	if lan_mac_file then
		local mac = lan_mac_file:read()
		mesh_info_node["mac"] = mac:gsub(":", "")
		mesh_info_node["mac"] = string.lower(mesh_info_node["mac"])
		lan_mac_file:close()
	end

	device_type = g_device_type
	mesh_info_node["hostname"] = uci:get("TMgr", "local", "self_hostname") or mesh_info_node["mac"]
	mesh_info_node["project_id"] = uci:get("mqtt_vccm", "mqtt_vccm", "project_id") or "0"
	mesh_info_node["upgrade_model"] = device_type
	mesh_info_node["vendor_info"] = g_vendor_info
	mesh_info_node["device_pn_type"] = uci:get("mqtt_vccm", "mqtt_vccm", "device_pn_type") or "JetonAC1200Air-4G4A_01W"

	mesh_info_node["hardware_ver"] = util.trim(util.exec("/usr/bin/vsproduct config hwver"))
	mesh_info_node["software_ver"] = util.trim(util.exec("/usr/bin/vsproduct report version |  awk -F'=' '{print $2}'"))
	mesh_info_node["device_type"] = device_type
	mesh_info_node["lan_ip"] = uci:get("network", "lan", "ipaddr") or ""
	mesh_info_node["connect_type"] = "wireless"
	mesh_info_node["upload"], mesh_info_node["download"] = get_wan_speed()
	mesh_info_node["uptime"] = tostring(system_info["uptime"])
	mesh_info_node["location"] = uci:get("TMgr", "local", "self_location") or ""
	mesh_info_node["child_devices"] = {}

	return mesh_info_node
end

local function get_mesh_info_from_topo_file()
	local mesh_flag = false
	local slave_mesh_num = 0
	local slave_mesh_ter_num = 0
	local slave_info = {}

	if (mesh_enable ~= "0" ) then
		local topology_result = util.ubus("em.topology_show","get",{})
		slave_info = (topology_result and topology_result.result == 0 and topology_result.children) or {}

		if slave_info == nil or type(slave_info) ~= "table"  or next(slave_info) == nil then
			topo_error = 1
		else
			topo_error = 0
		end
	end

	if slave_info ~= nil and type(slave_info) == "table" then
		for _, slave_device in pairs(slave_info) do
			if slave_device["dev_role"] == 1 then
				slave_mesh_num = slave_mesh_num + 1
			else
				slave_mesh_ter_num = slave_mesh_ter_num + 1
			end
		end
	end

	if slave_mesh_num > 0 then
		mesh_flag = true
	end

	return mesh_flag, slave_mesh_num, slave_mesh_ter_num,slave_info
end

local function get_tmgr_device_number(tmgr_device_info)
	local tmgr_device_num = 0

	if tmgr_device_info == nil then
		return 0
	end

	for _ in pairs(tmgr_device_info["client_list"]) do
		tmgr_device_num = tmgr_device_num + 1
	end

	return tmgr_device_num
end

local tmgr_info =  {}
local slave_device_discover_info_list = {}
local slave_mesh_index = 1
local veth0_mac = ""
local self_lan_mac = ""

local function get_slave_device(slave_info)
	local mesh_info_node = {
		project_id = "",
		upgrade_model = "",
		vendor_info = "",
		device_pn_type = "",
		hostname = "",
		hardware_ver = "",
		mac = "",
		software_ver = "",
		device_type = "",
		lan_ip = "",
		connect_type = "",
		upload = "",
		download = "",
		uptime = "",
		location = "",
		child_devices = {},
	}

	local first_make_mesDevinfo = 1
	for _, device in pairs(slave_info) do
		if device.children ~= nil and type(device.children) == "table" then
			if device.al_mac_addr ~= nil then
				local parent_mac = device.al_mac_addr
				local child_mac = device.children[1].al_mac_addr
				insert_child(parent_mac:gsub(":",""), child_mac:gsub(":",""))

				if device.non_i1905_nb_list[1] ~= nil then
					local nb_intf_mac = device.non_i1905_nb_list[1].nb_intf_mac
					insert_child(parent_mac:gsub(":",""), nb_intf_mac:gsub(":",""))
				end
			end
			get_slave_device(device.children)
		else
			local parent_mac = device.al_mac_addr
			if device.non_i1905_nb_list[1] ~= nil and device.non_i1905_nb_list[1].nb_intf_mac ~= nil then
				local nb_intf_mac = device.non_i1905_nb_list[1].nb_intf_mac
				insert_child(parent_mac:gsub(":",""), nb_intf_mac:gsub(":",""))
			end
		end

		if device.bhtype == 2 and device.dev_role == 1 then
			
			local device_mac = device.al_mac_addr
			device_mac = device_mac:gsub(":","")
			device_mac = string.lower(device_mac)

			if tmgr_info ~= nil and type(tmgr_info) == "table" then
				local ip_addr = ""
				local location = ""
				local hostname = ""
				if tmgr_info[tostring(device_mac)] ~= nil then
					ip_addr = tmgr_info[tostring(device_mac)].ip_addr
					location = tmgr_info[tostring(device_mac)].location
					hostname = tmgr_info[tostring(device_mac)].hostname
					tmgr_info[tostring(device_mac)].found = 1
				end
				mesh_info_node["lan_ip"] = ip_addr
				mesh_info_node["location"] = location
				mesh_info_node["hostname"] = hostname
			end
			mesh_info_node["mac"] = string.lower(device_mac)
			mesh_info_node["uptime"] = tostring(device.active_time)
			mesh_info_node["upload"] = device.uplink_rate
			mesh_info_node["download"] = device.downlink_rate
			
			--[[

			From em_common_types.h
			/* ��IEEE 1905.1-2013��Table 6-12-Media type��intfType���еĶ������ */
			typedef enum {
				EM_MEDIA_IEEE_802_3U_FAST_ETH = 0x0000,  /* FastEthernet */
				EM_MEDIA_IEEE_802_3AB_GB_ETH = 0x0001,   /* GigabitEthernet */
				EM_MEDIA_IEEE_802_3AE_10GB_ETH = 0x0002, /* GigabitEthernet */
				EM_MEDIA_IEEE_802_11B_2_4G = 0x0100,     /* 2.4GHz */
				EM_MEDIA_IEEE_802_11G_2_4G = 0x0101,     /* 2.4GHz */
				EM_MEDIA_IEEE_802_11A_5G = 0x0102,       /* 5GHz */
				EM_MEDIA_IEEE_802_11N_2_4G = 0x0103,     /* 2.4GHz */
				EM_MEDIA_IEEE_802_11N_5G = 0x0104,       /* 5GHz */
				EM_MEDIA_IEEE_802_11AC_5G = 0x0105,      /* 5GHz */
				EM_MEDIA_IEEE_802_11AD_60G = 0x0106,     /* 60GHz */
				EM_MEDIA_IEEE_802_11AF = 0x0107,         /* af */
				EM_MEDIA_IEEE_802_11AX = 0x0108,         /* AX */
				EM_MEDIA_IEEE_802_11BE = 0x0109,         /* BE */
				EM_MEDIA_IEEE_1901_WAVELET = 0x0200,     /* wavelet */
				EM_MEDIA_IEEE_1901_FFT = 0x0201,         /* FFT */
				EM_MEDIA_IEEE_MOCA_V1_1 = 0x0300,        /* MoCA v1.1 */
				EM_MEDIA_IEEE_DOWN_STATE = 0x01FF,        /* down state */
				EM_MEDIA_IEEE_UNKNOWN = 0xFFFF,
			} em_media_type_e;

			]]--
			if (device.uplink_type == 0 or device.uplink_type == 1 or device.uplink_type == 2) then
				mesh_info_node["connect_type"] = "wired"
			else
				mesh_info_node["connect_type"] = "wireless"
			end

			-- / By zhh / --
			if first_make_mesDevinfo == 1 then
				runCmd = "echo -e \""..mesh_info_node["mac"].."|"..mesh_info_node["connect_type"].."|"..mesh_info_node["lan_ip"].."\" > /tmp/meshDevConnect";
				first_make_mesDevinfo = 0;
			else
				runCmd = "echo -e \""..mesh_info_node["mac"].."|"..mesh_info_node["connect_type"].."|"..mesh_info_node["lan_ip"].."\" >> /tmp/meshDevConnect";
			end
			os.execute(runCmd); 

			if nil ~= slave_device_discover_info_list then
				local find_in_device_discover_info = 0;
				for _, discover_device_info in pairs(slave_device_discover_info_list) do
					if mesh_info_node["mac"] == (discover_device_info["mac"]) then

						mesh_info_node["device_pn_type"] = discover_device_info["device_pn_type"]
						mesh_info_node["vendor_info"] = discover_device_info["vendor_info"]
						-- mesh_info_node["project_id"] = discover_device_info["project_id"]
						mesh_info_node["software_ver"] = discover_device_info["software_version"]
						mesh_info_node["pri_model"] = discover_device_info["pri_model"]
						mesh_info_node["hardware_ver"] = discover_device_info["hardware_version"]
						mesh_info_node["device_type"] = discover_device_info["customer_model"]
						mesh_info_node["upgrade_model"] = discover_device_info["pri_model"]
						mesh_info_node["project_id"] = uci:get("mqtt_vccm", "mqtt_vccm", "project_id") or "0"

						find_in_device_discover_info = 1;
						break
					end
				end
				if (find_in_device_discover_info==0) then
					platform_call.upgrade_slave_udp_device_info()
				end
			end

			info[tostring(slave_mesh_index)] = deep_copy(mesh_info_node)
			local parent_mac = device.bhalmac:gsub(":","")
			parent_mac = string.lower(parent_mac)

			if parent_mac == veth0_mac then
				parent_mac = self_lan_mac
			end
			insert_child(parent_mac, mesh_info_node["mac"])
			slave_mesh_index = slave_mesh_index + 1
			
		end
	end

end


local function get_mesh_info()

	local mesh_info_node = {
		project_id = "",
		upgrade_model = "",
		vendor_info = "",
		device_pn_type = "",
		hostname = "",
		hardware_ver = "",
		mac = "",
		software_ver = "",
		device_type = "",
		lan_ip = "",
		connect_type = "",
		upload = "",
		download = "",
		uptime = "",
		location = "",
		child_devices = {},
	}
	
	local index = 0
	local tmgr_device_info = util.ubus("tmgr", "all", { mode = 0 })
	local tmgr_device_num = 0
	local mesh_flag = false
	local slave_mesh_num = 0
	local slave_mesh_ter_num = 0

	local veth0_mac_file = io.open("/sys/class/net/veth0/address","r")
	if veth0_mac_file then
		veth0_mac = veth0_mac_file:read()
		veth0_mac = veth0_mac:gsub(":","")
		veth0_mac = string.lower(veth0_mac)
		veth0_mac_file:close()
	end

	mesh_enable = uci:get("easymesh", "easymesh_default", "mesh_switch") or "0"
	-- local self_device_role = uci:get("easymesh", "easymesh_default", "mesh_mode") or "0"
	local slave_info = {}
	slave_device_discover_info_list = platform_call.get_slave_device_discover_info_list() or nil

	if debug then
		if (slave_device_discover_info_list==nil) then
			debug:write("slave_device_discover_info_list is nil ... \n")
		else
			debug:write("slave_device_discover_info_list:"..table_to_string.convert(slave_device_discover_info_list).."\n")
		end
	end

	mesh_flag, slave_mesh_num, slave_mesh_ter_num, slave_info = get_mesh_info_from_topo_file()
	mesh_info_node = get_self_device_mesh_info()

	info["0"] = deep_copy(mesh_info_node)
	info["0"]["wan_status"] = get_self_device_wan_status()
	self_lan_mac = info["0"]["mac"]

	if mesh_enable == "0" or topo_error == 1 then -- Disable mesh or self is master mesh device, or get topology error
		for _ ,client in pairs(tmgr_device_info["client_list"]) do
			if client.online:match("1") and client.ban:match("0") then
				local insert_flag = 1;
				-- zhh add for avoid wan mac join in client list
				if client.connect_type == "wired" then
					local insert_mac = client.mac:gsub("..", "%0:"):sub(1, -2)
					local handle = io.popen("ip neigh | grep wan | grep "..insert_mac)
					local result = handle:read("*a")
					handle:close()
					if result ~= "" then
						insert_flag = 0;
					end
				end
				-- end
				if insert_flag == 1 then
					insert_child(self_lan_mac, client.mac);
				end
			end
		end
	end

	for _ ,client in pairs(tmgr_device_info["client_list"]) do
		if client.online:match("1") and client.ban:match("0") then
			tmgr_info[string.lower(client.mac)] = {}
			tmgr_info[string.lower(client.mac)]["ip_addr"]= client.ip_address
			tmgr_info[string.lower(client.mac)]["location"]= client.location
			tmgr_info[string.lower(client.mac)]["hostname"]= client.hostname
			tmgr_info[string.lower(client.mac)]["found"]= 0
		end
	end

	get_slave_device(slave_info)

	-- Get child devices
	for _, mesh_node in pairs(info) do
		local mac = mesh_node["mac"]

		local insert_mac = mac:gsub("(%w%w)", "%1:"):sub(1, -2)

		-- wirless terminal
		if mesh_enable~="0"  then
			local master_dev_client_list = util.ubus("em.client_show","get",{al_mac = insert_mac})
			if master_dev_client_list~= nil then
				local client_macs = {}
				for _, client in ipairs(master_dev_client_list.client_list) do
					local client_mac = client.client_mac
					insert_child(mac, client_mac:gsub(":",""))
				end
			end
		end

		local upload = 0
		local download = 0

		if master_map_slave[mac] and type(master_map_slave[mac]) == "table" then
			for key, value in pairs(master_map_slave[mac]) do
				mesh_node["child_devices"][key] = string.lower(value)
			end
		end

		-- Get slave mesh device speed
		if mac ~= self_lan_mac then
			mesh_node["upload"] = tostring(upload)
			mesh_node["download"] = tostring(download)
		end
	end
end

function method.get()
	get_mesh_info()

	if debug then
		debug:write("Get mesh_info:"..table_to_string.convert(info).."\n")
		debug:close()
	end
	-- print(table_to_string.convert(info))
	return info
end

return method
-- method.get()
