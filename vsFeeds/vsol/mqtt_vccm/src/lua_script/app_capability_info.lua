package.path = package.path .. ";/usr/lib/lua/mqtt_vccm/?.lua;/usr/lib/lua/luci/?.lua;"
local platform_call = require("platform_call");
local uci = require("luci.model.uci").cursor()

local info = {};
local method = {};

function get_product_name()
    local file = io.open("/etc/hi_version", "r")
    if not file then
        print("failed to open file :/etc/hi_version")
        return nil
    end
    
    for line in file:lines() do
        if line:match("product name%s*:%s*(.*)") then
            local product_name = line:match("product name%s*:%s*(.*)")
            file:close()
            return product_name
        end
    end
    
    -- if not find product name
    file:close()
    return nil
end

local function init()
    local topo_page ={};
    topo_page["speed"] = "1";                  --设备上下行速率
    topo_page["connect_type"] = "0";           --设备连接方式
    topo_page["mesh_pair"] = "1";              --设备是否有mesh组网功能
    topo_page["rtk_mesh"] = "0";               --是否支私有mesh

    local encryption_24G ={}
    local encryption_5G ={}

    local product_name = get_product_name();   -- for judging device model

    local mesh_opened = uci:get("easymesh", "easymesh_default", "mesh_switch") or "0"
    local wlan_time = {};
    -- if mesh_opened=="1" then
    --     wlan_time["supported"] = "0"
    --     encryption_24G["0"] = { ["label"] = "None", ["value"] = "none" }
    --     encryption_24G["1"] = { ["label"] = "WPA2-PSK", ["value"] = "psk2" }
    --     encryption_24G["2"] = { ["label"] = "WPA3-SAE", ["value"] = "sae" }
    --     encryption_24G["3"] = { ["label"] = "WPA2-PSK/WPA3-SAE", ["value"] = "sae-mixed" }
    --     encryption_24G["4"] = { ["label"] = "WPA-PSK/WPA2-PSK", ["value"] = "psk-mixed" }

    --     encryption_5G["0"] = { ["label"] = "None", ["value"] = "none" }
    --     encryption_5G["1"] = { ["label"] = "WPA2-PSK", ["value"] = "psk2" }
    --     encryption_5G["2"] = { ["label"] = "WPA3-SAE", ["value"] = "sae" }
    --     encryption_5G["3"] = { ["label"] = "WPA2-PSK/WPA3-SAE", ["value"] = "sae-mixed" }
    --     encryption_5G["4"] = { ["label"] = "WPA-PSK/WPA2-PSK", ["value"] = "psk-mixed" }
    if mesh_opened=="1" then
        wlan_time["supported"] = "0"
    else
        wlan_time["supported"] = "1"
    end
    encryption_24G["0"] = { ["label"] = "None", ["value"] = "none" }
    encryption_24G["1"] = { ["label"] = "WPA", ["value"] = "psk+ccmp" }
    encryption_24G["2"] = { ["label"] = "WPA2-PSK", ["value"] = "psk2+ccmp" }
    encryption_24G["3"] = { ["label"] = "WPA-PSK/WPA2-PSK", ["value"] = "psk-mixed+ccmp" }
    if (product_name:match("hg5013")) then
        encryption_24G["4"] = { ["label"] = "WPA3-SAE", ["value"] = "sae" }
        encryption_24G["5"] = { ["label"] = "WPA2-PSK/WPA3-SAE", ["value"] = "sae-mixed" }
    end

    encryption_5G["0"] = { ["label"] = "None", ["value"] = "none" }
    encryption_5G["1"] = { ["label"] = "WPA", ["value"] = "psk+ccmp" }
    encryption_5G["2"] = { ["label"] = "WPA2-PSK", ["value"] = "psk2+ccmp" }
    encryption_5G["3"] = { ["label"] = "WPA-PSK/WPA2-PSK", ["value"] = "psk-mixed+ccmp" }
    if (product_name:match("hg5013")) then
        encryption_5G["4"] = { ["label"] = "WPA3-SAE", ["value"] = "sae" }
        encryption_5G["5"] = { ["label"] = "WPA2-PSK/WPA3-SAE", ["value"] = "sae-mixed" }
    end
   
    -- end

    local wlan_setting = {};                   --WLAN设置
    wlan_setting["supported"] = "1";
    wlan_setting["multiple_ap"] = "0";         --0为单个ap，1为多个ap
    wlan_setting["supported_5G"] = "1";        --多ap时是否支持5G
    wlan_setting["24g_num"] = "1";
    wlan_setting["5g_num"] = "1";
    wlan_setting["encryption_24G"] = encryption_24G;
    wlan_setting["encryption_5G"] = encryption_5G;

    local wlan_quality = {};                   --WLAN质量
    wlan_quality["24g"] = "1";
    wlan_quality["5g"] = "1";

    local wlan_guest = {};                     --WLAN访客网络
    wlan_guest["supported"] = "1";

    local internet_setting = {};               --上网方式
    if (product_name:match("v2802ach")) or (product_name:match("hg3232axt")) then
        internet_setting["supported"] = "0";   -- ONU can't conifg wan from vst
        wlan_guest["supported"] = "1";
    elseif (product_name:match("hg5013")) or (product_name:match("hg5012ac")) then
        internet_setting["supported"] = "1";   -- Router can't conifg wan from vst
        wlan_guest["supported"] = "1";
    else
        internet_setting["supported"] = "0";
        wlan_guest["supported"] = "0";
    end

    local diagnosis_page = {};
    diagnosis_page["supported"] = "1";
    diagnosis_page["wan_speed"] = "0";         --是否有wan口速率

    local monitor_page = {};
    monitor_page["supported"] = "1";

    local upgrade_page = {};
    upgrade_page["supported"] = "1";

    local parental_control = {};
    parental_control["supported"] = "1";       --开启多条时间段（允许上网）

    local speed_test = {};                     --测速功能
    speed_test["supported"] = "1";

    local reset_function = {};
    if (product_name:match("v2802ach")) or (product_name:match("hg3232axt")) then
        reset_function["supported"] = "0";
    elseif (product_name:match("hg5013")) or (product_name:match("hg5012ac")) then
        reset_function["supported"] = "1";
    else
        reset_function["supported"] = "0";
    end

    local vpn_function = {}
    local qos_function = {}
    local firewall_function = {}

    firewall_function["supported"] = "1"
    firewall_function["smart_prevent"] = "0"
    firewall_function["prevent_dos"] = "0"

    qos_function["ai_qos"] = "1"
    qos_function["ai_bandwidth"] = "0"

    vpn_function["supported"] = "1"


    info["topo_page"] = topo_page;
    info["wlan_setting"] = wlan_setting;
    info["wlan_quality"] = wlan_quality;
    info["wlan_guest"] = wlan_guest;
    info["internet_setting"] = internet_setting;
    info["diagnosis_page"] = diagnosis_page;
    info["monitor_page"] = monitor_page;
    info["upgrade_page"] = upgrade_page;
    info["parental_control"] = parental_control;
    info["speed_test"] = speed_test;
    info["reset_function"] = reset_function;
    info["wlan_time"] = wlan_time;

    info["vpn_function"] = vpn_function;
    info["qos_function"] = qos_function;
    info["firewall_function"] = firewall_function;
end

function method.get()
    init();
    return info;
end

return method;
-- method.get();
