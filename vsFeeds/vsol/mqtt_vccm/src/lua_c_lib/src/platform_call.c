#include <stdio.h>
#include <unistd.h>
#include <sys/sysinfo.h>
#include <time.h>
#include "lua.h"
#include "lualib.h"
#include "lauxlib.h"
#include "info_common.h"
#include "platform_mib_api.h"
#include "command_api.h"
#include "platform_pair_api.h"
#include "platform_public.h"

static const luaL_Reg platform_call[] = {
        {"usleep", info_lua_usleep},
        {"time", info_lua_time},
        {"uptime", info_lua_uptime},
        {"access", info_lua_access},
        {"update_mib", update_mib},
        {"get_client_info_table", get_client_info_table},
        {"get_family_info_table", get_family_info_table},
        {"get_date_list_info_table", get_date_list_info_table},
        {"get_url_list_info_table", get_url_list_info_table},
        {"get_sys_info", get_sys_info},
        {"get_base_info", get_base_info},
        {"get_ap_info", get_ap_info},
        {"get_throughput_info_table", get_throughput_info_table},
        {"get_mesh_info", get_mesh_info_table},
        {"set_wlan_cfg_init", set_wlan_cfg_init},
        {"set_wlan_cfg", set_wlan_cfg},
        {"set_wlan_cfg_take_effect", set_wlan_cfg_take_effect},
        {"set_mesh_cfg", set_mesh_cfg},
        {"set_client_type", set_client_type},
        {"set_del_family", set_del_family},
        {"set_del_client_url_list_by_family_idx", set_del_client_url_list_by_family_idx},
        {"set_del_client_time_ctrl_by_family_idx", set_del_client_time_ctrl_by_family_idx},
        {"set_client_time_ctrl_by_mac", set_client_time_ctrl_by_mac},
        {"set_client_url_limit", set_client_url_limit},
        {"cmd_reboot", command_reboot},
        {"cmd_restore", command_restore},
        {"cmd_diagnosis", command_diagonsis},
        {"get_scan_result_table", get_scan_result_table},
        {"get_pair_result_table", get_pair_result_table},
        {"cmd_pair", cmd_pair},
        {"set_del_device", set_del_device},
        {"mqtt_user_mng", platform_lua_bind_mqtt_user_mng},
        {"get_upgrade_info", get_upgrade_info},
        {"upgrade_devices", upgrade_devices},
        {"get_slave_device_discover_info_list", get_slave_device_discover_info_list},
        {"decrypt_pwd", decrypt_pwd},
        {"sync_project_id_to_udp_local_device", sync_project_id_to_udp_local_device},
        {"wlan_update_channel_quality_info", wlan_update_channel_quality_info},
        {"set_wan_cfg", set_wan_cfg},
        {"upgrade_slave_udp_device_info", upgrade_slave_udp_device_info},
        {"optimize_channel", optimize_channel},
        {"get_firewall_info", get_firewall_info},
        {"set_firewall_cfg", set_firewall_cfg},
        {"get_iptv_info", get_iptv_info},
        {"lua_set_iptv_cfg", lua_set_iptv_cfg},
        {"get_qos_info", get_qos_info},
        {"lua_set_smart_qos_cfg", lua_set_smart_qos_cfg},
        {"get_vpn_info", get_vpn_info},
        {"set_vpn_cfg", set_vpn_cfg},
        {"lua_get_wan_port", lua_get_wan_port},
        {"get_wan_speed", platform_get_wan_speed},
        {NULL, NULL}};

int luaopen_platform_call(lua_State *L)
{
    luaL_register(L, "platform_call", platform_call);
    return 1;
}
