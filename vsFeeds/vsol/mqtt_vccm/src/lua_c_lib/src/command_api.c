#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <sys/ioctl.h>
#include <string.h>
#include <fcntl.h> /* Definition of AT_* constants */
#include <unistd.h>
#include <stdio.h>
#include <string.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include "lua.h"
#include "lualib.h"
#include "lauxlib.h"

#include "command_api.h"
#include "platform_mib_api.h"

#define MQTT_EXT_CLIENT_SUPPORT
#include "mqtt_ext_msg.h"

int command_reboot(lua_State *L)
{
    if (mqtt_ext_client_send_reboot() != 0)
    {
        printf("MQTT is disable, reboot myself.\r\n");
        system("reboot");
    }

    return 1;
}

int command_restore(lua_State *L)
{
    if (mqtt_ext_client_send_reset() != 0)
    {
        printf("MQTT is disable, restore myself.\r\n");
        system("firstboot -y");
        system("reboot");
    }
    return 1;
}

static int read_command_int_output(char *command)
{
    FILE *fp;
    char output[32];
    int value;

    fp = popen(command, "r");
    if (fp == NULL) {
        perror("Failed to run command");
        exit(1);
    }

    // 读取命令输出并转换为整数
    if (fgets(output, sizeof(output), fp) != NULL) {
        sscanf(output, "%d", &value);
    }
    
    pclose(fp);
    return value;
}

static int get_device_eth_num()
{
    int ge_num = read_command_int_output("hi_cfm get board.ge_num");
    int fe_num = read_command_int_output("hi_cfm get board.fe_num");

    return ge_num + fe_num;
}

static int command_diagonsis_connection_speed(lua_State *L)
{
    int i = 0, j = 0;
    FILE *fps;
    char fname[64]={0};
    unsigned int speed[MAX_PORT_NUM] = {0};
    int eth_num = get_device_eth_num();

    unsigned char router_flag = 1;

    // if (platform_judge_device_model() != HG5013) {
    //     router_flag = 0;
    //     speed[0] = 1000;
    // }
    for(i=0; i < eth_num; i++)
    {
        if (!router_flag) /* +1 for pon */
            j = i + 1;
        else
            j = i;
        memset(fname, 0, 64);
        /* 先检查物理链路是否接通 */
        snprintf(fname, 64, "/sys/class/net/eth%d/carrier", i);
        fps = fopen(fname, "r");
        if (fps != NULL) {
            int carrier = 0;
            if (fscanf(fps, "%d", &carrier) != 1) {
                carrier = 0;
            }
            fclose(fps);
            if (carrier != 1) {
                /* 无链路，端口速率为0，跳过读取speed */
                speed[j] = 0;
                continue;
            }
        } else {
            /* 无法读取carrier，保守认为无链路 */
            speed[j] = 0;
            continue;
        }

        /* 读取端口速率 */
        snprintf(fname, 64, "/sys/class/net/eth%d/speed", i);
        fps = fopen(fname, "r");
        if(fps != NULL){
            /* get link speed*/
            if(fscanf(fps, "%d", &speed[j]) != 1){
                speed[j]=0;
            }
        }else{
            continue;
        }
        fclose(fps);
    }

    if (!router_flag)
        add_connection_speed_to_lua(L, speed, eth_num + 1);
    else
        add_connection_speed_to_lua(L, speed, eth_num);

    return 0;
}

int command_diagonsis(lua_State *L)
{
    char *ptr = NULL;
    unsigned int value;
    
    ptr = lua_tostring(L, 1);
    if (ptr != NULL) {
        value = info_dns_query();
        add_dns_delay_to_lua(L, value);
    }

    ptr = lua_tostring(L, 2);
    if (ptr != NULL) {
        command_diagonsis_connection_speed(L);
    }

    ptr = lua_tostring(L, 3);
    if (ptr != NULL) {
        get_weak_signal_sta_list_table(L);
    }

    return 1;
}
