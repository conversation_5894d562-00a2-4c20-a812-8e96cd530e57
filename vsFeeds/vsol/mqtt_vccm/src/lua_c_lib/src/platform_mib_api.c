#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <sys/ioctl.h>
#include <string.h>
#include <fcntl.h> /* Definition of AT_* constants */
#include <unistd.h>
#include <stdio.h>
#include <string.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <sys/socket.h>
#include <sys/sysinfo.h>
#include <time.h>

#include <netinet/in.h>
#include <arpa/inet.h>
#include <openssl/md5.h>

#include "lua.h"
#include "lualib.h"
#include "lauxlib.h"
#include "platform_mib_api.h"
#include "info_common.h"
#include "info_lua_api.h"
#include "mqtt_ext_msg.h"
#include "vsuci.h"

#define UCI_OK 0

char mqtt_device_model[32] = {0};

static char *add_upgrade_info_to_string(int idx, char *device_mac_str, char *download_status, int upgrade_time);

static int parse_topo_str_element(char *src_str, char *dst_str, unsigned int index)
{
    int i = 0, j = 0;
    int ampersand_num = 0;

    for (i = 0; i < strlen(src_str); i++)
    {
        if (src_str[i] == '&')
        {
            if (ampersand_num == index)
            {
                break;
            }

            ampersand_num++;
        }
        else
        {
            if (ampersand_num == index)
            {
                dst_str[j++] = src_str[i];
            }
        }
    }

    if (j == 0)
    {
        lua_log_error("[%s][%d] Parse topo str=%s failed\r\n", __FUNCTION__, __LINE__, src_str);
    }

    dst_str[j] = '\0';
    
    return 0;
}

int device_get_mesh_role()
{
    char buf[4] = {0};
    int mesh_mode=0, mesh_switch;

    
    vsuci_get("easymesh", "easymesh_default", "mesh_switch", buf, sizeof(buf));
    mesh_switch = atoi(buf);
    if(mesh_switch == 0){
        return MESH_ROLE_DISABLED;
    }
    
    vsuci_get("easymesh", "easymesh_default", "mesh_mode", buf, sizeof(buf));
    mesh_mode = atoi(buf)+1;
    
    return mesh_mode;
}

int platform_get_udp_role(int *udp_role)
{
    if (MESH_ROLE_CONTROLLER == device_get_mesh_role())
    {
        *udp_role = UDP_ROLE_CONTROLLER;
    }
    else
    {
        *udp_role = UDP_ROLE_SUBORDINATE;
    }
    
    return 0;
}

int get_device_mac(char *mac)
{
    FILE *fp = NULL;
    char tmp_data[18] = {0};
    size_t len = 0;

    if (mac == NULL)
    {
        lua_log_error("[%s][%d] Invalid input parameters\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    fp = fopen("/sys/class/net/br-lan/address", "r");
    if (fp == NULL)
    {
        lua_log_error("[%s][%d] Failed to open file /sys/class/net/br-lan/address\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    if (fgets(tmp_data, sizeof(tmp_data), fp) == NULL)
    {
        lua_log_error("[%s][%d] Failed to read from file /sys/class/net/br-lan/address\r\n", __FUNCTION__, __LINE__);
        fclose(fp);
        return -1;
    }

    fclose(fp);

    len = strlen(tmp_data);
    if (len > 0 && tmp_data[len - 1] == '\n')
    {
        tmp_data[len - 1] = '\0';
    }

    if (strlen(tmp_data) == 0)
    {
        lua_log_error("[%s][%d] No MAC address found in file /sys/class/net/br-lan/address\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    strncpy(mac, tmp_data, sizeof(tmp_data));
    mac[sizeof(tmp_data) - 1] = '\0'; 

    return 0;
}

int platform_mib_get_dev_mac(char *mac)
{
    char value[32] = {0};

    get_device_mac(value);
    mac_str_to_bytes(value, mac);
    
    return 0;
}

int platform_get_device_mac_str(char *device_mac)
{
    get_device_mac(device_mac);
    str_to_upper(device_mac);
    
    return 0;
}

int platform_get_device_mac_str_stip(char *device_mac_stip)
{
    get_device_mac(device_mac_stip);
    str_to_lower(device_mac_stip);
    mac_str_strip_separater(device_mac_stip);

    return 0;
}

static int find_string_from_file(const char *filename, const char *search_string)
{
    char line[1024] = {0};
    FILE *file = fopen(filename, "r");

    if (!file)
    {
        lua_log_error("[%s][%d] Func find_string_from_file Failed: Error opening file\r\n", __FUNCTION__, __LINE__);
        return 0;
    }

    while (fgets(line, sizeof(line), file))
    {
        if (strstr(line, search_string) != NULL)
        {
            fclose(file);
            return 1;
        }
    }

    fclose(file);
    return 0;
}

int platform_judge_device_model(void)
{
    FILE *fp = popen("sed -n 's/.*product name\\s*:\\s*\\(.*\\)/\\1/p' /etc/hi_version", "r");
    if (NULL == fp) {
        return -1;
    }

    char buffer[64] = {0};
    fgets(buffer, sizeof(buffer), fp);
    pclose(fp);

    if (strstr(buffer, "wrt_v2802ach") != NULL) {
        return V2802AC_H;
    }
    else if (strstr(buffer, "wrt_hg3232axt") != NULL) {
        return HG3232AXT_H;
    }
    else if (strstr(buffer, "wrt_hg5013_4g") != NULL) {
        return HG5013;
    }
    else if (strstr(buffer, "wrt_hg5012ac") != NULL) {
        return HG5012AC;
    }
    else if (strstr(buffer, "wrt_hg5013_4g_h2") != NULL) {
        return HG5013_H2;
    }
    else if (strstr(buffer, "wrt_hg5013_4g_h3") != NULL) {
        return HG5013_H3;
    }
    else
        return HG5013_H2;
}

static void set_device_model(void)
{
    vsuci_get("mqtt_vccm", "mqtt_vccm", "device_type", mqtt_device_model, sizeof(mqtt_device_model));

    if (strlen(mqtt_device_model) == 0 || strcmp(mqtt_device_model, "unknown") == 0) //优先获取mib
    {
        int device_model = platform_judge_device_model();
        if (device_model == HG3232AXT_H)
            strncpy(mqtt_device_model, DEVICE_MODEL_HG3232AXTH, strlen(DEVICE_MODEL_HG3232AXTH));
        else if (device_model == HG5013)
            strncpy(mqtt_device_model, DEVICE_MODEL_JETONAX3000AIR, strlen(DEVICE_MODEL_JETONAX3000AIR));
        else if (device_model == V2802AC_H)
            strncpy(mqtt_device_model, DEVICE_MODEL_V2802ACH, strlen(DEVICE_MODEL_V2802ACH));
        else if (device_model == HG5012AC)
            strncpy(mqtt_device_model, DEVICE_MODEL_JETONAC1200AIR, strlen(DEVICE_MODEL_JETONAC1200AIR));
        else if (device_model == HG5013_H2)
            strncpy(mqtt_device_model, DEVICE_MODEL_JETONAX3000CORE, strlen(DEVICE_MODEL_JETONAX3000CORE));
        else if (device_model == HG5013_H3)
            strncpy(mqtt_device_model, DEVICE_MODEL_JETONAX3000ULTRA, strlen(DEVICE_MODEL_JETONAX3000ULTRA));
    }
}

int platform_mib_init()
{
    set_device_model();
    return 0;
}

int platform_mib_get_mqtt_info(int *mqtt_enable, char *server_addr, char *user_name, char *user_passwd, char *client_id)
{
    char buf[4] = {0};
    unsigned char mac[6];
    char mac_str_strip[16] = {0};
    char server_addr_temp[MAX_MQTT_ADDR_LEN] = {0};
    INT8U ret = 0;
    
    vsuci_get("mqtt_vccm", "mqtt_vccm", "enable", buf, sizeof(buf));
    
    if (atoi(buf) == 0)
    {
        return -1;
    }
    
    *mqtt_enable = atoi(buf);
    
    vsuci_get("mqtt_vccm", "mqtt_vccm", "addr", server_addr, sizeof(char) * 64);
    if (0 == strcmp(server_addr, "vince-sa.vsol-tech.com") || 0 == strcmp(server_addr, "ince.vsol-tech.com"))
    {
        snprintf(server_addr_temp, sizeof(server_addr_temp), "api.%s", server_addr);
        strncpy(server_addr, server_addr_temp, MAX_MQTT_ADDR_LEN - 1);
        server_addr[MAX_MQTT_ADDR_LEN - 1] = '\0';
    
        ret = vsuci_set(CONF_MQTT, "mqtt_vccm", "addr", server_addr);
        if (ret != UCI_OK)
        {
            lua_log_error("[%s][%d] vsuci_set %s %s %s failed\r\n", __FUNCTION__, __LINE__, CONF_MQTT, "addr", server_addr);
            return -1;
        }
    }
    
    platform_mib_get_dev_mac(mac);
    
#if 0
        vsuci_get("mqtt_vccm", "mqtt_vccm", "username", user_name, sizeof(char) * 32);
        vsuci_get("mqtt_vccm", "mqtt_vccm", "password", user_passwd, sizeof(char) * 32);
#else
    snprintf(user_name, MAX_MQTT_USERNAME, "%02x%02x%02x%02x%02x%02x", MAC2STR(mac));
    platform_get_device_id(user_passwd, MAX_MQTT_PASSWORD);
#endif
    
    sprintf(client_id, "%02x%02x%02x%02x%02x%02x|%s", MAC2STR(mac), mqtt_device_model);

    return 0;
}

int platform_mib_get_bind_cfg(char *user_id, char *timestamp, char *group_name)
{
    vsuci_get("mqtt_vccm", "mqtt_vccm", "mqttBindUserId", user_id, sizeof(char) * 64);
    vsuci_get("mqtt_vccm", "mqtt_vccm", "timestamp", timestamp, sizeof(char) * 64);
    vsuci_get("mqtt_vccm", "mqtt_vccm", "groupname", group_name, sizeof(char) * 64);
    
    return 0;
}

int platform_mib_unbind_cfg(void)
{
    vsuci_set("mqtt_vccm", "mqtt_vccm", "mqttBindUserId", "");
    
    return 0;
}

int platform_mib_bind_status_set(char *bind_status)
{
    INT8U ret = 0;
    
    ret = vsuci_set(CONF_MQTT, "mqtt_vccm", "bind_status", bind_status);
    if (ret != UCI_OK)
    {
        lua_log_error("[%s][%d] vsuci_set %s %s %s failed\r\n", __FUNCTION__, __LINE__, CONF_MQTT, "bind_status", bind_status);
        return -1;
    }
    
    return MQTT_OK;
}

int platform_mib_bind_cfg(int mqtt_enable, char *server_addr, char *user_id, char *timestamp, char *group_name, char* mqtt_bind_topic)
{
    INT8U ret = 0;
    char value[8] = {0};
    char server_addr_temp[MAX_MQTT_ADDR_LEN] = {0};
    
    sprintf(value, "%d", mqtt_enable ? 1 : 0);
    if (mqtt_enable)
    {
        ret = vsuci_set(CONF_MQTT, "mqtt_vccm", "enable", value);
        if (ret != UCI_OK)
        {
            lua_log_error("[%s][%d] vsuci_set %s %s %s failed\r\n", __FUNCTION__, __LINE__, CONF_MQTT, "enable", value);
            return -1;
        }
    }

    if (0 == strcmp(server_addr, "vince-sa.vsol-tech.com") || 0 == strcmp(server_addr, "ince.vsol-tech.com"))
    {
        snprintf(server_addr_temp, sizeof(server_addr_temp), "api.%s", server_addr);
        strncpy(server_addr, server_addr_temp, MAX_MQTT_ADDR_LEN - 1);
        server_addr[MAX_MQTT_ADDR_LEN - 1] = '\0';
    }
    
    ret = vsuci_set(CONF_MQTT, "mqtt_vccm", "addr", server_addr);
    if (ret != UCI_OK)
    {
        lua_log_error("[%s][%d] vsuci_set %s %s %s failed\r\n", __FUNCTION__, __LINE__, "addr", server_addr);
        return -1;
    }
    
    ret = vsuci_set(CONF_MQTT, "mqtt_vccm", "mqttBindUserId", user_id);
    if (ret != UCI_OK)
    {
        lua_log_error("[%s][%d] vsuci_set %s %s %s failed\r\n", __FUNCTION__, __LINE__, CONF_MQTT, "mqttBindUserId", user_id);
        return -1;
    }
    
    ret = vsuci_set(CONF_MQTT, "mqtt_vccm", "timestamp", timestamp);
    if (ret != UCI_OK)
    {
        lua_log_error("[%s][%d] vsuci_set %s %s %s failed\r\n", __FUNCTION__, __LINE__, CONF_MQTT, "timestamp", timestamp);
        return -1;
    }
    
    ret = vsuci_set(CONF_MQTT, "mqtt_vccm", "groupname", group_name);
    if (ret != UCI_OK)
    {
        lua_log_error("[%s][%d] vsuci_set %s %s %s failed\r\n", __FUNCTION__, __LINE__, CONF_MQTT, "groupname", group_name);
        return -1;
    }
    
    ret = vsuci_set(CONF_MQTT, "mqtt_vccm", "mqttBindTopic", mqtt_bind_topic);
    if (ret != UCI_OK)
    {
        lua_log_error("[%s][%d] vsuci_set %s %s %s failed\r\n", __FUNCTION__, __LINE__, CONF_MQTT, "groupname", group_name);
        return -1;
    }
    
    ret = platform_mib_bind_status_set(MQTT_BIND_STATUS_BINDING);
    if (ret != UCI_OK)
    {
        lua_log_error("[%s][%d] Function platform_mib_bind_status_set failed ret = %d\r\n", ret, __FUNCTION__, __LINE__);
        return -1;
    }

    return 0;
}

static int platform_mib_get_project_id(char *project_id, size_t project_id_len)
{
    INT8U ret = 0;

    if (project_id == NULL)
    {
        lua_log_error("[%s][%d] The input parameter is NULL\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    ret = vsuci_get(CONF_MQTT, "mqtt_vccm", "project_id", project_id, project_id_len);
    if (ret != UCI_OK)
    {
        lua_log_error("[%s][%d] vsuci_set %s %s %s failed\r\n", __FUNCTION__, __LINE__, CONF_MQTT, "project_id", project_id);
        return -1;
    }

    return MQTT_OK;
}

int platform_get_mqtt_properties(char *device_pn_type, unsigned char *mac, char *model, char *project_id, char *group_name, char *device_class, char *status, char *device_id, char *serial_num, char *software_ver)
{
    char bind_status[32] = {0};
    int len = 0;
    FILE *debug_fp = NULL;
    
    platform_mib_get_dev_detail_info(device_pn_type, PN_TYPE);
    platform_mib_get_dev_detail_info(project_id, PROJECT_ID);
    platform_mib_get_dev_detail_info(mac, MAC_ADDRESS);
    platform_mib_get_dev_detail_info(model, MODEL_NAME);
    platform_mib_get_dev_detail_info(device_id, DEV_ID);
    platform_mib_get_dev_detail_info(software_ver, SW_VER);
    platform_mib_get_dev_detail_info(serial_num, DEV_SN);
    

    int device_model = platform_judge_device_model();

    if (device_model == HG5013 || device_model == HG5012AC || device_model == HG5013_H2 || device_model == HG5013_H3) {
        len = strlen(MQTT_DEVICE_CLASS_ROUTER);
        strncpy(device_class, MQTT_DEVICE_CLASS_ROUTER, len);
        device_class[len] = '\0';
    } else {
        len = strlen(MQTT_DEVICE_CLASS);
        strncpy(device_class, MQTT_DEVICE_CLASS, len);
        device_class[len] = '\0';
    }

    vsuci_get_more_options(1, CONF_MQTT, "mqtt_vccm", 2 * 3, "bind_status", bind_status, sizeof(bind_status), "groupname", group_name, 64);

    if (strcmp(bind_status, MQTT_BIND_STATUS_ONLINE) != 0 && strcmp(bind_status, MQTT_BIND_STATUS_BINDING) != 0)
    {
        lua_log_error("[%s][%d] Get bind_status failed, change to online, groupe_name is %s\r\n", __FUNCTION__, __LINE__, group_name);
        strcpy(bind_status, MQTT_BIND_STATUS_ONLINE);
    }
    
    // strncpy(status, bind_status, sizeof(bind_status));
    // status[sizeof(bind_status) - 1] = '\0';
    if(access("/tmp/binding", F_OK) == 0)
    {
        strncpy(status, MQTT_BIND_STATUS_BINDING, strlen(MQTT_BIND_STATUS_BINDING));
        status[strlen(MQTT_BIND_STATUS_BINDING)] = '\0';
    }
    else
    {
        strncpy(status, MQTT_BIND_STATUS_ONLINE, strlen(MQTT_BIND_STATUS_ONLINE));
        status[strlen(MQTT_BIND_STATUS_ONLINE)] = '\0';
    }

    // 调试：将所有属性值写入文件
    debug_fp = fopen("/tmp/mqtt_properties", "w");
    if (debug_fp != NULL)
    {
        // 打印本地时间戳
        time_t now = time(NULL);
        struct tm tm_now;
        localtime_r(&now, &tm_now);
        char ts[32];
        strftime(ts, sizeof(ts), "%Y-%m-%d %H:%M:%S", &tm_now);
        fprintf(debug_fp, "=== MQTT Properties Debug Info === [%s]\n", ts);
        fprintf(debug_fp, "device_pn_type: %s\n", device_pn_type);
        fprintf(debug_fp, "mac: %02x%02x%02x%02x%02x%02x\n", mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
        fprintf(debug_fp, "model: %s\n", model);
        fprintf(debug_fp, "project_id: %s\n", project_id);
        fprintf(debug_fp, "group_name: %s\n", group_name);
        fprintf(debug_fp, "device_class: %s\n", device_class);
        fprintf(debug_fp, "status: %s\n", status);
        fprintf(debug_fp, "device_id: %s\n", device_id);
        fprintf(debug_fp, "serial_num: %s\n", serial_num);
        fprintf(debug_fp, "software_ver: %s\n", software_ver);
        fprintf(debug_fp, "bind_status: %s\n", status);
        fprintf(debug_fp, "===============================\n");
        fclose(debug_fp);
    }

    return MQTT_OK;
}

int get_all_router_mac(char *mac_list)
{
    return 0;
}

int platform_device_reset()
{
    system("/usr/bin/sysconf --factory");
    sleep(1);
    system("reboot");
    return 0;
}

int get_base_info(lua_State *L)
{
    return 0;
}

int get_ap_info(lua_State *L)
{
    return 0;
}

int get_client_info_table(lua_State *L)
{
    return 0;
}

int get_weak_signal_sta_list_table(lua_State *L)
{
    return 0;
}

int get_family_info_table(lua_State *L)
{
    return 0;
}

int get_date_list_info_table(lua_State *L)
{
    return 0;
}

int get_url_list_info_table(lua_State *L)
{
    return 0;
}

int get_throughput_info_table(lua_State *L)
{
    return 0;
}

int get_mesh_info_table(lua_State *L)
{
    return 0;
}

int set_wlan_cfg_init(lua_State *L)
{
    return 0;
}

int set_wlan_cfg(lua_State *L)
{
    return 0;
}

int set_wlan_cfg_take_effect(lua_State *L)
{
    return 0;
}

int set_mesh_cfg(lua_State *L)
{
    return 0;
}

int set_client_type(lua_State *L)
{
    return 0;
}

int set_del_client_url_list_by_family_idx(lua_State *L)
{
    return 0;
}

int set_del_client_time_ctrl_by_family_idx(lua_State *L)
{
    return 0;
}

int set_del_family(lua_State *L)
{
    return 0;
}

int set_client_time_ctrl_by_mac(lua_State *L)
{
    return 0;
}

int set_client_url_limit(lua_State *L)
{
    return 0;
}

int update_mib(lua_State *L)
{
    return 0;
}

int platform_lua_bind_mqtt_user_mng(lua_State *L)
{
    FILE *fp;
    char buf[8];
    int i, ret = MQTT_ERR;
    int mqtt_enable = 0;
    const char *server_addr = NULL;
    const char *user_id = NULL;
    const char *group_name = NULL;
    const char *time_stamp = NULL;
    const char *mqtt_bind_topic = NULL;
    
    if (lua_isnumber(L, 1))
    {
        mqtt_enable = lua_tonumber(L, 1);
    }
    else
    {
        lua_log_error("[%s][%d] Error param mqtt_state\r\n", __FUNCTION__, __LINE__);
        goto RET;
    }

    if (mqtt_enable)
    {
        server_addr = lua_tostring(L, 2);
        user_id = lua_tostring(L, 3);
        group_name = lua_tostring(L, 4);
        time_stamp = lua_tostring(L, 5);
        mqtt_bind_topic = lua_tostring(L, 6);
        if (server_addr == NULL || user_id == NULL || group_name == NULL || time_stamp == NULL || mqtt_bind_topic == NULL)
        {
            lua_log_error("[%s][%d] Error param server info\r\n", __FUNCTION__, __LINE__);
            goto RET;
        }
    }

    if (MQTT_ERR == mqtt_ext_client_bind_cfg(mqtt_enable, server_addr, user_id, group_name, time_stamp, mqtt_bind_topic))
    {
        lua_log_error("[%s][%d] MQTT bind failed\r\n", __FUNCTION__, __LINE__);
        goto RET;
    }

    for (i = 0; i < 14; i++)
    {
        sleep(1);
        fp = fopen(MQTT_CONNECT_STATUS_FILE, "r");
        if (fp != NULL)
        {
            if (fgets(buf, sizeof(buf), fp) != NULL)
            {
                if (strstr(buf, "1") != NULL)
                {
                    fclose(fp);
                    ret = MQTT_OK;
                    goto RET;
                }
            }
            fclose(fp);
        }
    }

    ret = -2;
    if (MQTT_ERR == mqtt_ext_client_bind_cfg(0, server_addr, user_id, group_name, time_stamp, mqtt_bind_topic))
    {
        lua_log_error("[%s][%d] MQTT unbind failed\r\n", __FUNCTION__, __LINE__);
        goto RET;
    }

RET:
    if (ret == MQTT_OK)
    {
        lua_pushstring(L, "success"); 
        platform_mib_bind_status_set(MQTT_BIND_STATUS_ONLINE);
    }
    else if (ret == MQTT_ERR)
    {
        lua_pushstring(L, "bind failed");
    }
    else
    {
        lua_pushstring(L, "network error");
    }
    
    return 1;
}

int get_upgrade_info(lua_State *L)
{
    int i = 0;
    char strip_mac_str[32] = {0};
    char upgrade_status_str[32] = {0};
    INT8U *res_buf;
    INT16U res_buf_len;
    struct upgrade_deivce_param_msg *upgrade_deivce_param_info = NULL;
    FILE *ulog = fopen("/tmp/upgrade_info.log", "a");
    if (ulog) {
        fprintf(ulog, "[get_upgrade_info] enter\n");
        fflush(ulog);
    }

    MQTT_BUF_MALLOC_RET(res_buf, -1);

    if (0 != mqtt_ext_client_send(MQTT_EXT_MSG_UPGRADE_GET_STATE, NULL, 0, res_buf, &res_buf_len))
    {
        if (ulog) {
            fprintf(ulog, "[get_upgrade_info] mqtt_ext_client_send failed\n");
            fclose(ulog);
        }
        lua_log_error("[%s][%d] Get all Agent routers failed\r\n", __FUNCTION__, __LINE__);
        MQTT_BUF_FREE(res_buf);
        return -1;
    }
    else
    {
        // if (res_buf_len < sizeof(struct upgrade_deivce_param_msg))
        // {
        //     if (ulog) {
        //         fprintf(ulog, "[get_upgrade_info] resp too short: %u < %zu\n", res_buf_len, sizeof(struct upgrade_deivce_param_msg));
        //         fclose(ulog);
        //     }
        //     lua_log_error("[%s][%d] Get upgrade state failed, len=%d\r\n", __FUNCTION__, __LINE__, res_buf_len);
        //     MQTT_BUF_FREE(res_buf);
        //     return -1;
        // }
        if (ulog) {
            fprintf(ulog, "[get_upgrade_info] res_buf:%s\n", res_buf);
            fflush(ulog);
        }
        upgrade_deivce_param_info = (struct upgrade_deivce_param_msg *)res_buf;
        if (ulog) {
            fprintf(ulog, "[get_upgrade_info] device_num=%u\n", upgrade_deivce_param_info->upgrade_device_num);
            fflush(ulog);
        }
        if (upgrade_deivce_param_info->upgrade_device_num > MAX_ROUTER_NUM)
        {
            if (ulog) {
                fprintf(ulog, "[get_upgrade_info] device_num overflow: %u > %u\n", upgrade_deivce_param_info->upgrade_device_num, MAX_ROUTER_NUM);
                fclose(ulog);
            }
            lua_log_error("[%s][%d] Get all upgrade routers error, upgrade_deivce_param_info.upgrade_device_num:%d\r\n",
                          __FUNCTION__, __LINE__, upgrade_deivce_param_info->upgrade_device_num);
            MQTT_BUF_FREE(res_buf);
            return -1;
        }
    }

    for (i = 0; i < upgrade_deivce_param_info->upgrade_device_num; i++)
    {
        sprintf(strip_mac_str, "%s", upgrade_deivce_param_info->upgrade_deivce_param[i].mac_str);
        mac_str_strip_separater(strip_mac_str);

        switch (upgrade_deivce_param_info->upgrade_deivce_param[i].download_status)
        {
        case DOWNLOAD_SUCCESS:
            sprintf(upgrade_status_str, "DOWNLOAD_SUCCESS");
            break;
        case DOWNLOAD_FAILED:
            sprintf(upgrade_status_str, "DOWNLOAD_FAILED");
            break;
        case DOWNLOAD_TIMEOUT:
            sprintf(upgrade_status_str, "DOWNLOAD_TIMEOUT");
            break;
        case DOWNLOAD_FILE_INCORRECT:
            sprintf(upgrade_status_str, "DOWNLOAD_FILE_INCORRECT");
            break;
        case DOWNLOAD_ING:
            sprintf(upgrade_status_str, "DOWNLOAD_ING");
            break;
        default:
            if (ulog) {
                fprintf(ulog, "[get_upgrade_info] unknown status %d for mac %s\n",
                        upgrade_deivce_param_info->upgrade_deivce_param[i].download_status,
                        upgrade_deivce_param_info->upgrade_deivce_param[i].mac_str);
                fflush(ulog);
            }
            lua_log_error("[%s][%d] Get error upgrade status:%d\n", __FUNCTION__, __LINE__,
                          upgrade_deivce_param_info->upgrade_deivce_param[i].download_status);
            continue;
            break;
        }

        if (ulog) {
            fprintf(ulog, "[get_upgrade_info] mac=%s status=%s time=%d\n",
                    strip_mac_str, upgrade_status_str,
                    upgrade_deivce_param_info->upgrade_deivce_param[i].upgrade_time);
            fflush(ulog);
        }
        add_upgrade_info_to_lua(L, !i, i, strip_mac_str, upgrade_status_str, upgrade_deivce_param_info->upgrade_deivce_param[i].upgrade_time);
    }

    if (ulog) {
        fprintf(ulog, "[get_upgrade_info] exit\n");
        fclose(ulog);
    }

    MQTT_BUF_FREE(res_buf);
    return 1;
}

static void get_upgrade_status_str(INT32U status, char *ret_str)
{
    switch (status)
    {
    case DOWNLOAD_SUCCESS:
        sprintf(ret_str, DOWNLOAD_SUCCESS_STR);
        break;
    case DOWNLOAD_FAILED:
        sprintf(ret_str, DOWNLOAD_FAILED_STR);
        break;
    case DOWNLOAD_TIMEOUT:
        sprintf(ret_str, DOWNLOAD_TIMEOUT_STR);
        break;
    case DOWNLOAD_FILE_INCORRECT:
        sprintf(ret_str, DOWNLOAD_FILE_INCORRECT_STR);
        break;
    case DOWNLOAD_ING:
        sprintf(ret_str, DOWNLOAD_ING_STR);
        break;
    default:
        lua_log_error("[%s][%d] Get error upgrade status:%d\r\n", __FUNCTION__, __LINE__, status);
        sprintf(ret_str, DOWNLOAD_FAILED_STR);
        break;
    }
}

static char *add_upgrade_info_to_string(int idx, char *device_mac_str, char *download_status, int upgrade_time)
{
    char *result = NULL;
    char *buffer = NULL;
    size_t len = 0;

    buffer = malloc(1024 * sizeof(char));
    if (!buffer) {
        fprintf(stderr, "Failed to allocate memory for buffer.\n");
        return NULL;
    }

    snprintf(buffer, 1024, "\"%d\":{\"mac\":\"%s\",\"download_status\":\"%s\",\"upgrade_time\":\"%d\"}",
             idx, device_mac_str, download_status, upgrade_time);

    len = strlen(buffer);
    result = malloc(len + 1);
    if (!result) {
        free(buffer);
        fprintf(stderr, "Failed to allocate memory for result.\n");
        return NULL;
    }

    strcpy(result, buffer);
    free(buffer);

    return result;

}

int get_upgrade_info_downloading_string(char *string, struct mqtt_app_upgrade_msg *mqtt_app_upgrade_info)
{
    int i = 0;
	char *token = NULL;
    size_t final_len = 0;
    char *final_result = NULL;

    final_result = calloc(1, 1024 * sizeof(char));
    if (!final_result) {
        fprintf(stderr, "Failed to allocate memory for final_result.\n");
        return -1;
    }

    snprintf(final_result, 1024, "{\"upgrade_info\":{");
    char tmp_mac_list[128] = {0};
    int update_device_idx = 0;
	for(i = 0; i < mqtt_app_upgrade_info->device_type_num; i++)
    {
        /* Get upgrade_cfg the mac list which need to upgrade */
        memset(tmp_mac_list, 0, sizeof(tmp_mac_list));
        strncpy(tmp_mac_list, mqtt_app_upgrade_info->device_type_info[i].mac_list_str, sizeof(tmp_mac_list));
        token = strtok(tmp_mac_list, " ");
        while (token != NULL)
        {
            str_to_lower(token);
		    char *upgrade_info_str = add_upgrade_info_to_string(update_device_idx, token, "DOWNLOAD_ING", 180);
		    if (update_device_idx > 0) {
                if (final_len > 1024)
                {
                    final_result = realloc(final_result, final_len + 1024);
                    if (!final_result) 
                    {
                        fprintf(stderr, "Failed to reallocate memory for final_result.\n");
                        return -1;
                    }
                }
                strcat(final_result, ",");
            }
            strcat(final_result, upgrade_info_str);
            free(upgrade_info_str);
            token = strtok(NULL, " ");
            ++update_device_idx;
            final_len = strlen(final_result);
        }
        
	}

    final_result = realloc(final_result, final_len + 2);
    if (!final_result) 
    {
        fprintf(stderr, "Failed to reallocate memory for final_result.\n");
        return -1;
    }
    strcat(final_result, "}");
    strcat(final_result, "}");
    final_len = strlen(final_result);

    if (final_len > 1024)
    {
        printf("[%s %d] ERROR final_len > 1024\n",__FUNCTION__, __LINE__);
        final_len = 1024;
    }
    strncpy(string, final_result, final_len);
    free(final_result);

    return 0;
}

// int get_upgrade_info_string(char *string, INT16U string_len, void *upgrade_info)
int get_upgrade_info_string(char *string, struct upgrade_deivce_param_msg *upgrade_deivce_param_info)
{
    int i = 0;
    char strip_mac_str[32] = {0};
    char upgrade_status_str[32] = {0};
    char *final_result = NULL;
    size_t final_len = 0;

    final_result = malloc(1024 * sizeof(char));
    if (!final_result) {
        fprintf(stderr, "Failed to allocate memory for final_result.\n");
        return -1;
    }

    snprintf(final_result, 1024, "{\"upgrade_info\":{");

    for (i = 0; i < upgrade_deivce_param_info->upgrade_device_num; i++) {
        sprintf(strip_mac_str, "%s", upgrade_deivce_param_info->upgrade_deivce_param[i].mac_str);
        mac_str_strip_separater(strip_mac_str);
        printf("get upgrade status, mac_str:%s, upgrade_status:%d\n",
               upgrade_deivce_param_info->upgrade_deivce_param[i].mac_str,
               upgrade_deivce_param_info->upgrade_deivce_param[i].download_status);

        switch (upgrade_deivce_param_info->upgrade_deivce_param[i].download_status)
        {
        case DOWNLOAD_SUCCESS:
            sprintf(upgrade_status_str, "DOWNLOAD_SUCCESS");
            break;
        case DOWNLOAD_FAILED:
            sprintf(upgrade_status_str, "DOWNLOAD_FAILED");
            break;
        case DOWNLOAD_TIMEOUT:
            sprintf(upgrade_status_str, "DOWNLOAD_TIMEOUT");
            break;
        case DOWNLOAD_FILE_INCORRECT:
            sprintf(upgrade_status_str, "DOWNLOAD_FILE_INCORRECT");
            break;
        case DOWNLOAD_ING:
            sprintf(upgrade_status_str, "DOWNLOAD_ING");
            break;
        default:
            printf("get error upgrade status:%d\n", upgrade_deivce_param_info->upgrade_deivce_param[i].download_status);
            continue;
            break;
        }
		str_to_lower(strip_mac_str);
		printf("[%s] mac:%s\n", __func__, strip_mac_str);
        char *upgrade_info_str = add_upgrade_info_to_string(i, strip_mac_str, upgrade_status_str, /*upgrade_deivce_param_info->upgrade_deivce_param[i].upgrade_time*/77);

        if (i > 0) {
            final_result = realloc(final_result, final_len + 1024);
            if (!final_result) {
                fprintf(stderr, "Failed to reallocate memory for final_result.\n");
                return -1;
            }
            strcat(final_result, ",");
        }
        strcat(final_result, upgrade_info_str);
        free(upgrade_info_str);
    }

    final_len = strlen(final_result);
    final_result = realloc(final_result, final_len + 2);
    if (!final_result) {
        fprintf(stderr, "Failed to reallocate memory for final_result.\n");
        return -1;
    }
    strcat(final_result, "}");
    strcat(final_result, "}");

    strcpy(string, final_result);
    free(final_result);

    return 0;
}

int upgrade_devices(lua_State *L)
{
    INT16U res_len;
    int device_type_num = 0;
    struct mqtt_app_upgrade_msg msg_upgrade_info = {0};
    char mac_token[256];
    char type_token[512];
    char vendor_token[512] = {0};
    char md5_token[64];
    char url_token[5120];
    int mac_len = 0;
    int device_len = 0;
    int vendor_len = 0;
    int md5_len = 0;
    int url_len = 0;

    const char *mac_list_str = lua_tostring(L, 1);
    const char *device_type = lua_tostring(L, 2);
    const char *vendor_info = lua_tostring(L, 3);
    const char *md5_result = lua_tostring(L, 4);
    const char *download_url = lua_tostring(L, 5);

    size_t mac_len_max = strlen(mac_list_str);
    size_t device_len_max = strlen(device_type);
    size_t vendor_len_max = strlen(vendor_info);
    size_t md5_len_max = strlen(md5_result);
    size_t url_len_max = strlen(download_url);

    if (mac_list_str == NULL || device_type == NULL || vendor_info == NULL || md5_result == NULL || download_url == NULL)
    {
        lua_log_error("[%s][%d] The input parameter is NULL\r\n", __FUNCTION__, __LINE__);
        return 1;
    }

    snprintf(mac_token, sizeof(mac_token), "%s", mac_list_str);
    snprintf(type_token, sizeof(type_token), "%s", device_type);
    snprintf(vendor_token, sizeof(vendor_token), "%s", vendor_info);
    snprintf(md5_token, sizeof(md5_token), "%s", md5_result);
    snprintf(url_token, sizeof(url_token), "%s", download_url);

    while (device_type_num < MAX_ROUTER_NUM && mac_token != NULL && type_token != NULL && vendor_token != NULL && md5_token != NULL && url_token != NULL)
    {
        if (mac_len > mac_len_max || device_len > device_len_max || vendor_len > vendor_len_max || md5_len > md5_len_max || url_len > url_len_max)
        {
            lua_log_warning("[%s][%d] Something token len expire max len\r\n", __FUNCTION__, __LINE__);
            break;
        }

        char *tmp_mac_token = NULL;
        char *tmp_type_token = NULL;
        char *tmp_vendor_token = NULL;
        char *tmp_md5_token = NULL;
        char *tmp_url_token = NULL;

        if (mac_len < mac_len_max)
        {
            tmp_mac_token = strtok(mac_token + mac_len, "|");
        }

        if (device_len < device_len_max)
        {
            tmp_type_token = strtok(type_token + device_len, "|");
        }

        if (vendor_len < vendor_len_max)
        {
            tmp_vendor_token = strtok(vendor_token + vendor_len, "|");
        }

        if (md5_len < md5_len_max)
        {
            tmp_md5_token = strtok(md5_token + md5_len, "|");
        }

        if (url_len < url_len_max)
        {
            tmp_url_token = strtok(url_token + url_len, "|");
        }

        if (tmp_mac_token == NULL || tmp_type_token == NULL || tmp_vendor_token == NULL || tmp_md5_token == NULL || tmp_url_token == NULL)
        {
            lua_log_warning("[%s][%d] something token is null\r\n", __FUNCTION__, __LINE__);
            break;
        }

        if (strlen(tmp_mac_token) > 0 && strlen(tmp_type_token) > 0 && strlen(tmp_vendor_token) > 0 && strlen(tmp_md5_token) > 0 && strlen(tmp_url_token) > 0)
        {
            snprintf(msg_upgrade_info.device_type_info[device_type_num].mac_list_str,
                     sizeof(msg_upgrade_info.device_type_info[device_type_num].mac_list_str),
                     "%s", tmp_mac_token);
            snprintf(msg_upgrade_info.device_type_info[device_type_num].device_type,
                     sizeof(msg_upgrade_info.device_type_info[device_type_num].device_type),
                     "%s", tmp_type_token);
            snprintf(msg_upgrade_info.device_type_info[device_type_num].vendor_info,
                     sizeof(msg_upgrade_info.device_type_info[device_type_num].vendor_info),
                     "%s", tmp_vendor_token);
            snprintf(msg_upgrade_info.device_type_info[device_type_num].md5_result,
                     sizeof(msg_upgrade_info.device_type_info[device_type_num].md5_result),
                     "%s", tmp_md5_token);
            snprintf(msg_upgrade_info.device_type_info[device_type_num].download_url,
                     sizeof(msg_upgrade_info.device_type_info[device_type_num].download_url),
                     "%s", tmp_url_token);

            device_type_num++;

            if (device_type_num == MAX_UPGRADE_DEVICE_NUM)
            {
                break;
            }
        }

        mac_len += strlen(tmp_mac_token) + 1;
        device_len += strlen(tmp_type_token) + 1;
        vendor_len += strlen(tmp_vendor_token) + 1;
        md5_len += strlen(tmp_md5_token) + 1;
        url_len += strlen(tmp_url_token) + 1;
    }

    msg_upgrade_info.device_type_num = device_type_num;
    if (msg_upgrade_info.device_type_num <= 0)
    {
        lua_log_error("[%s][%d] Upgrade device_type_num is 0\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    if (mqtt_ext_client_send(MQTT_EXT_MSG_UPGRADE_EVENT, (INT8U *)&msg_upgrade_info, (INT16U)sizeof(struct mqtt_app_upgrade_msg), NULL, &res_len) != 0)
    {
        lua_log_error("[%s][%d] Send MQTT_EXT_MSG_UPGRADE_EVENT error\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    return 1;
}

/**
 * @brief Universal command execution function (supports variable length lines)
 * @param command [in] Shell command to be executed
 * @param output [out] Starting address of output buffer
 * @param max_lines [in] Maximum number of supported rows
 * @param line_size [in] Storage space size per row (in bytes)
 * @return The number of successfully read rows (negative numbers indicate failures)
 */
static int execute_command(const char *command, char *output, int max_lines, int line_size)
{
    if (command == NULL)
    {
        lua_log_error("[%s][%d] Command string is NULL\n", __FUNCTION__, __LINE__);
        return -1;
    }
    if (output == NULL)
    {
        lua_log_error("[%s][%d] Output buffer is NULL\n", __FUNCTION__, __LINE__);
        return -1;
    }
    if (max_lines <= 0)
    {
        lua_log_error("[%s][%d] Invalid max_lines: %d\n", __FUNCTION__, __LINE__, max_lines);
        return -1;
    }
    if (line_size <= 0)
    {
        lua_log_error("[%s][%d] Invalid line_length: %d\n", __FUNCTION__, __LINE__, line_size);
        return -1;
    }

    FILE *fp = NULL;
    char buf[CMD_BUF_SIZE] = {0};
    int count = 0;

    if ((fp = popen(command, "r")) == NULL)
    {
        lua_log_error("[%s][%d] Execute popen failed for: %s\r\n", __FUNCTION__, __LINE__, command);
        return -1;
    }

    while (fgets(buf, sizeof(buf), fp) && count < max_lines)
    {
        char *target = output + (count * line_size);

        buf[strcspn(buf, "\n")] = '\0';
        strncpy(target, buf, line_size - 1);
        target[line_size - 1] = '\0';
        count++;
    }

    if (pclose(fp) == -1)
    {
        lua_log_error("[%s][%d] pclose failed\r\n", __FUNCTION__, __LINE__);
        return -2;
    }

    return count;
}

int platform_get_device_mac_by_ipaddr(const char *src_ip, char *terminal_mac)
{
    if (src_ip == NULL || terminal_mac == NULL)
    {
        lua_log_error("[%s][%d] The input parameter is NULL\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    char all_terminal_ips[MAX_CLIENT_NUM][IP_ADDR_STRING_LEN] = {{0}};
    char all_terminal_macs[MAX_CLIENT_NUM][MAC_ADDR_STRING_LEN] = {{0}};
    const int mac_count = execute_command("ubus call tmgr all | grep hostname | cut -d '\"' -f 4", (char *)all_terminal_macs, MAX_CLIENT_NUM, sizeof(all_terminal_macs[0]));
    const int ip_count = execute_command("ubus call tmgr all | grep ip_address | cut -d '\"' -f 4", (char *)all_terminal_ips, MAX_CLIENT_NUM, sizeof(all_terminal_ips[0]));
    int i = 0;

    if (mac_count <= 0)
    {
        lua_log_error("[%s][%d] No MAC addresses found\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    if (ip_count <= 0)
    {
        lua_log_error("[%s][%d] No IP addresses found\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    if (mac_count != ip_count)
    {
        lua_log_error("[%s][%d] MAC/IP count mismatch (%d vs %d)\r\n", __FUNCTION__, __LINE__, mac_count, ip_count);
        return -1;
    }

    for (i = 0; i < mac_count; ++i)
    {
        if (strcmp(src_ip, all_terminal_ips[i]) == 0)
        {
            strncpy(terminal_mac, all_terminal_macs[i], MAC_ADDR_STRING_LEN);
            terminal_mac[MAC_ADDR_STRING_LEN - 1] = '\0';
            return 0;
        }
    }

    lua_log_error("[%s][%d] IP %s not found\r\n", __FUNCTION__, __LINE__, src_ip);
    return -1;
}

int platform_mib_get_mqtt_status(int *mqtt_enable)
{
    INT8U ret = 0;
    char value[8] = {0};

    if (mqtt_enable == NULL)
    {
        lua_log_error("[%s][%d] The input parameter is NULL\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    *mqtt_enable = 0;
    ret = vsuci_get(CONF_MQTT, "mqtt_vccm", "enable", value, sizeof(value));
    if (ret != UCI_OK)
    {
        lua_log_error("[%s][%d] vsuci_get %s %s %s failed\r\n", __FUNCTION__, __LINE__, CONF_MQTT, "mqtt_vccm", "enable");
        return -1;
    }
    else
    {
        if (value[0] == '1')
        {
            *mqtt_enable = 1;
        }
    }
    
    return 0;
}

static int platform_get_factory_mode()
{
    int factory_mode=0;
    FILE *fp=NULL;

    if(NULL !=(fp = popen("vsproduct factory", "r"))){
        if(fscanf(fp, "%d", &factory_mode) != 1){
            factory_mode=0;
        }
        pclose(fp);
    }
    return !!factory_mode;
}

static int platform_get_quick_cfg_status()
{
    int quick_cfg_status = 0;
    char buf[3] = {0};
    vsuci_get("custom", "first_login", "first_admin_login_flag", buf, sizeof(buf));
    quick_cfg_status = atoi(buf);
    return quick_cfg_status;
}


int mqtt_ext_mib_discover_get(char *model, int *http_port, char *url_path, char *ssid, char *mac, int *mqtt_bind_status, int *quick_cfg_status, char *src_ip, char *terminal_mac, char *device_pn_type)
{

    lua_c_log("=====================> Enter %s\n", __FUNCTION__);
    char buf[128] = {0};
    int mqtt_enable = 0;

    if (model == NULL || http_port == NULL || url_path == NULL || ssid == NULL || mac == NULL || mqtt_bind_status == NULL || quick_cfg_status == NULL || src_ip == NULL || terminal_mac == NULL || device_pn_type == NULL)
    {
        lua_log_error("[%s][%d] The input parameter is NULL\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    /* TODO: Judge whether meshed */

    sprintf(model, "%s", mqtt_device_model);
    *http_port = HTTP_PORT;
    sprintf(url_path, "%s", URL_PATH);

    vsuci_get("wireless", "default_radio1", "ssid", buf, sizeof(buf));
    sprintf(ssid, "%s", buf);

    *mqtt_bind_status = MQTT_BIND_NEED;

    memset(buf, 0, sizeof(buf));
    vsuci_get("mqtt_vccm", "mqtt_vccm", "mqttBindUserId", buf, sizeof(buf));
    if (strlen(buf) > 0)
    {
        *mqtt_bind_status = MQTT_BIND_NO_NEED;
    }

    platform_get_device_mac_str(mac);
    platform_mib_get_dev_detail_info(device_pn_type, PN_TYPE);
	sprintf(terminal_mac, "%s", mac);

    /* Only Router needs quick cfg */
    int device_model = platform_judge_device_model();
    if (device_model == HG5013 || device_model == HG5012AC || device_model == HG5013_H2 || device_model == HG5013_H3) {
        if(platform_get_factory_mode() || platform_get_quick_cfg_status()){
            *quick_cfg_status = QUICK_CFG_NEED;
        }else{
            *quick_cfg_status = QUICK_CFG_NO_NEED;
        }
    } else {
        *quick_cfg_status = QUICK_CFG_NO_NEED;
    }

    lua_c_log("<==================EXIT %s\n", __FUNCTION__);
    return 0;
}

int set_del_device(lua_State *L)
{
    char *mac_tmp = lua_tostring(L, 1);
    char del_mac[32] = {0};

    if (mac_tmp == NULL || strlen(mac_tmp) < 12)
    {
        return 1;
    }
    mac_str_insert_separater(mac_tmp, del_mac);
    str_to_lower(del_mac);
    platform_del_agent(del_mac);
    return 1;
}

int platform_get_wlan_init_status(int *wlan_up_success)
{
    int uptime = 0;

    if (wlan_up_success == NULL)
    {
        lua_log_error("[%s][%d] The input parameter is NULL\n", __FUNCTION__, __LINE__);
        return -1;
    }

    uptime = get_uptime();

    if (uptime > 120)
    {
        *wlan_up_success = 1;
    }
    else
    {
        *wlan_up_success = 0;
    }
    
    return 0;
}

int platform_get_gw_ip(char *ip)
{
    if (ip == NULL)
    {
        lua_log_error("[%s][%d] The input parameter is NULL\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    get_default_gateway(ip);
    
    return MQTT_OK;
}

int platform_get_br_name(char *br_name)
{
    if (br_name == NULL)
    {
        lua_log_error("[%s][%d] The input parameter is NULL\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    strcpy(br_name, "br-lan");
    
    return 0;
}

int platform_upgrade_handle()
{
    char cmd[128] = {0};
    printf("[%s] platform_upgrade_handle\n", __FUNCTION__);

    if (access(UPGRADE_FILE_PATH, F_OK))
        return -1;

    if (access("/tmp/upgrade_flag", F_OK)) {
        system("touch /tmp/upgrade_flag");
        snprintf(cmd, sizeof(cmd), "/usr/bin/handleVINCEPktHeader -y %s", UPGRADE_FILE_PATH);
        printf("[%s] cmd: %s\n", __FUNCTION__, cmd);
        if (system(cmd)) {
            printf("[%s] Rm VINCEPktHeader failed, file incorrect\n", __FUNCTION__);
            system("rm -f /tmp/upgrade_flag");
            return -1;
        }

        memset(cmd, 0, sizeof(cmd));
        snprintf(cmd, sizeof(cmd), "/sbin/sysupgrade --test %s", UPGRADE_FILE_PATH);
        printf("[%s] cmd: %s\n", __FUNCTION__, cmd);
        if (system(cmd)) {
            printf("[%s] sysupgrade check upgrade firmware failed, file incorrect\n", __FUNCTION__);
            system("rm -f /tmp/upgrade_flag");
            return -1;
        }

        memset(cmd, 0, sizeof(cmd));
        snprintf(cmd, sizeof(cmd), "/sbin/sysupgrade %s", UPGRADE_FILE_PATH);
        printf("[%s] cmd: %s\n", __FUNCTION__, cmd);
        if (system(cmd)) {
            printf("[%s] sysupgrade start upgrade firmware failed\n", __FUNCTION__);
            system("rm -f /tmp/upgrade_flag");
            return -1;
	    }
        system("rm -f /tmp/upgrade_flag");
    }
    
    return 0;
}

int platfrom_get_download_result(char *md5_result)
{
#if 0
    FILE* fp = NULL;
    char cmd[128] = {0};

    if (access(UPGRADE_FILE_PATH, F_OK) != 0)
    {
        lua_log_error("[%s][%d] File does not exist:%s\r\n", __FUNCTION__, __LINE__, UPGRADE_FILE_PATH);
        strcpy(md5_result, "");
        return -1;
    }

    if (chmod(UPGRADE_FILE_PATH, 0777) != 0)
    {
        lua_log_error("[%s][%d] Failed to change file:%s permissions\r\n", __FUNCTION__, __LINE__, UPGRADE_FILE_PATH);
        strcpy(md5_result, "");
        return -1;
    }

    if (access("/var/tmp/md5result.txt", F_OK) == 0)
    {
        remove("/var/tmp/md5result.txt");
    }

    sprintf(cmd, "md5sum %s > /var/tmp/md5result.txt", UPGRADE_FILE_PATH);
	system(cmd);

	fp = fopen("/var/tmp/md5result.txt","r");
	if(fp != NULL)
	{
		fgets(md5_result, 33, fp);
		fclose(fp);
	}
	else
    {
        strcpy(md5_result, "");
        return -1;
	}
#endif
    return 0;
}

int platform_check_upgrade_file()
{
    return 0;
}

int platfrom_download_file_by_http(const char *download_path, double *duration)
{
    char cmd[1024] = {0};
    time_t start_time = time(NULL);
    time_t end_time = 0;
    
    if (download_path == NULL || duration == NULL)
    {
        lua_log_error("[%s][%d] The input parameter is NULL\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    if (access(UPGRADE_FILE_PATH, F_OK) == 0)
    {
        remove(UPGRADE_FILE_PATH);
    }

    // sprintf(cmd, "wget -c -o /var/tmp/download.log -O %s \"%s\" --no-check-certific", UPGRADE_FILE_PATH, download_path);
    sprintf(cmd, "curl -k -o %s \"%s\"", UPGRADE_FILE_PATH, download_path);
    system(cmd);

    end_time = time(NULL);
    *duration = difftime(end_time, start_time);
    return 0;
}

int platfrom_download_file_by_ftp(const char *download_path, double *duration, const char *ftp_user, const char *ftp_password)
{
    char cmd[1024] = {0};
    time_t start_time = time(NULL);
    time_t end_time = 0;
    
    if (download_path == NULL || duration == NULL || ftp_user == NULL || ftp_password == NULL)
    {
        lua_log_error("[%s][%d] The input parameter is NULL\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    if (access(UPGRADE_FILE_PATH, F_OK) == 0)
    {
        remove(UPGRADE_FILE_PATH);
    }

    sprintf(cmd, "wget -P /var/tmp/ -c -o /var/tmp/download.log ftp://%s:%s@%s", ftp_user, ftp_password, download_path);
    // sprintf(cmd, "wget -P /var/tmp/ -c -o /var/tmp/download.log ftp://%s:%s@%s/..%s", ftp_user, ftp_password, download_path);
    system(cmd);

    end_time = time(NULL);
    *duration = difftime(end_time, start_time);
    return 0;
}

int platfrom_get_ftp_download_path(char *ftp_path, int ftp_path_max_len)
{
    struct in_addr in;
    unsigned int uIp;

    if (ftp_path == NULL)
    {
        lua_log_error("[%s][%d] The input parameter is NULL\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    get_if_ip_addr(LAN_IF_NAME, &uIp);
    in.s_addr = uIp;

    snprintf(ftp_path, ftp_path_max_len, "%s/..%s", inet_ntoa(in), UPGRADE_FILE_PATH);
    return 0;
}

int platfrom_set_open_ftp_server()
{
    return 0;
}

int platfrom_set_close_ftp_server()
{
    return 0;
}

int platfrom_get_ftp_cfg(char *ftp_user, char *ftp_password)
{
    return 0;
}

int platform_set_ftp_server(const int ftp_enable)
{
    return 0;
}

int platform_get_upgrade_time(INT16U *upgrade_time)
{
    if (upgrade_time == NULL)
    {
        lua_log_error("[%s][%d] The input parameter is NULL\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    *upgrade_time = 180;
    return 0;
}
 
int platform_get_router_info(UPGRADE_DEVICE_INFO *router_devices_list, int *router_device_num)
{
    int count = 0;
    FILE *fp = NULL;
    char line[256] = {0};

    if (device_get_mesh_role() == MESH_ROLE_DISABLED)
    {
        platform_get_device_mac_str(router_devices_list[0].mac_str);
        *router_device_num = 1;
        return MQTT_OK;
    }

    fp = popen("ubus call em.topology_show get | grep al_mac_addr", "r");
    if (fp == NULL) {
        lua_log_error("[%s][%d] popen failed\r\n", __FUNCTION__, __LINE__);
        platform_get_device_mac_str(router_devices_list[0].mac_str);
        *router_device_num = 1;
        return -1;
    }

    unsigned char skip_first_mac = 1;
    while (fgets(line, sizeof(line), fp) != NULL) {
        // 查找冒号后第一个引号
        if (skip_first_mac)
        {
            skip_first_mac = 0;
            continue;
        }
        char *start = strchr(line, '"'); // 第一个引号
        if (start) {
            start = strchr(start + 1, '"'); // 第二个引号
            if (start) {
                start = strchr(start + 1, '"'); // 第三个引号（MAC地址前的引号）
                if (start) {
                    start++; // 跳到MAC地址第一个字符
                    char *end = strchr(start, '"'); // 找到MAC地址结尾的引号
                    if (end && (end - start) < MAC_ADDR_STRING_LEN) {
                        char mac[MAC_ADDR_STRING_LEN] = {0};
                        strncpy(mac, start, end - start);
                        mac[end - start] = '\0';
                        str_to_upper(mac);
                        snprintf(router_devices_list[count].mac_str, sizeof(router_devices_list[count].mac_str), "%s", mac);
                        // mac_str_insert_separater(mac, router_devices_list[count].mac_str);
                        count++;
                    }
                }
            }
        }
    }
    pclose(fp);


    if (count == 0) {
      
        platform_get_device_mac_str(router_devices_list[0].mac_str);
        count = 1;
    }

    *router_device_num = count;
    return MQTT_OK;
}

int platform_get_device_id(char *mac_str, int len)
{
    char processed_mac_str[DEVICE_ID_PROCESSED_MAC_STRING_LENGTH];
    unsigned char digest[MD5_DIGEST_LENGTH];
    char digest_str[2 * MD5_DIGEST_LENGTH + 1];
    INT8U mac[6] = {0};
    
    if (mac_str == NULL)
    {
        lua_log_error("[%s][%d] The input parameter is NULL\r\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    platform_get_device_mac_str(mac_str);
    
    str_to_lower(mac_str);
    mac_str_to_bytes(mac_str, mac);
    
    preprocess_mac_str(mac, processed_mac_str);
    compute_md5((const unsigned char *)processed_mac_str, strlen(processed_mac_str), digest);
    md5_to_str(digest, digest_str);

    snprintf(mac_str, len, "%s", digest_str);

    return 0;
}

int platform_mib_get_dev_detail_info(char *data, DEV_DETAIL_INFO data_type)
{
    FILE *fp = NULL;
    char tmp_data[64] = {0};
    
    if (data == NULL)
    {
        lua_log_error("[%s][%d] The input parameter is NULL\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    switch (data_type)
    {
    case MODEL_NAME:
    {
        platform_mib_get_pri_device_type(data);
    }
    break;
    case PN_TYPE:
    {
        char device_pn_type[32] = {0};
        vsuci_get("mqtt_vccm", "mqtt_vccm", "device_pn_type", device_pn_type, sizeof(device_pn_type));
        if (strlen(device_pn_type) == 0 || strcmp(device_pn_type, "unknown") == 0) 
        {
            int device_model = platform_judge_device_model();
            if (device_model == HG3232AXT_H) 
                strcpy(data, DEVICE_PN_TYPE_HG3232AXTH);
            else if (device_model == V2802AC_H) 
                strcpy(data, DEVICE_PN_TYPE_V2802ACH);
            else if (device_model == HG5013) 
                strcpy(data, DEVICE_PN_TYPE_JETONAX3000AIR);
            else if (device_model == HG5012AC) 
                strcpy(data, DEVICE_PN_TYPE_JETONAC1200AIR);
            else if (device_model == HG5013_H2)
                strcpy(data, DEVICE_PN_TYPE_JETONAX3000CORE);
            else if (device_model == HG5013_H3)
                strcpy(data, DEVICE_PN_TYPE_JETONAX3000ULTRA);
        }
        else {
            strcpy(data, device_pn_type);
        }
    }
    break;
    case DEV_ID:
    {
        platform_get_device_id(data, 2 * MD5_DIGEST_LENGTH + 1);
    }
    break;
    case PROJECT_ID:
    {
        if (MQTT_OK == platform_mib_get_project_id(tmp_data, sizeof(tmp_data)))
        {
            strcpy(data, tmp_data);
        }
        else
        {
            strcpy(data, DEF_DEVICE_PROJECT_ID);
        }
    }
    break;
    case MAC_ADDRESS:
    {
        platform_mib_get_dev_mac(data);
    }
    break;
    case SW_VER:
    {
        fp = popen("/usr/bin/vsproduct report version | awk -F'=' '{print $2}'", "r");
        if (fp == NULL) {
            perror("popen failed");
            return -1;
        }

        if (fgets(tmp_data, sizeof(tmp_data), fp) != NULL) {
            tmp_data[strcspn(tmp_data, "\n")] = 0;
            strcpy(data, tmp_data);
            printf("Command tmp_data: %s, data:%s\n", tmp_data, data);
        }
        else {
            strcpy(data, "");
        }

        pclose(fp);
    }
    break;
    case HW_VER:
    {
        fp = popen("vsproduct config hwver", "r");
        if (fp == NULL)
        {
            lua_log_error("[%s][%d] Failed to run command:vsproduct config hwver\r\n", __FUNCTION__, __LINE__);
            return -1;
        }

        if (fgets(tmp_data, sizeof(tmp_data), fp) != NULL)
        {
            size_t len = strcspn(tmp_data, "\n");

            tmp_data[len] = '\0';
            strcpy(data, tmp_data);
        }
        else
        {
            strcpy(data, "");
        }

        pclose(fp);
    }
    break;
    case VENDOR_INFO:
    {
        strcpy(data, "ROUTER-VSOLver");
    }
    break;
    case DEV_SN:
    {
        fp = popen("vsproduct config gponsn", "r");
        if (fp == NULL)
        {
            lua_log_error("[%s][%d] Failed to run command:vsproduct config gponsn\r\n", __FUNCTION__, __LINE__);
            return -1;
        }

        if (fgets(tmp_data, sizeof(tmp_data), fp) != NULL)
        {
            size_t len = strcspn(tmp_data, "\n");

            tmp_data[len] = '\0';
            strcpy(data, tmp_data);
        }
        else
        {
            strcpy(data, "");
        }

        pclose(fp);
    }
    break;
    default:
        lua_log_error("[%s][%d] Error device info data_type = %d\r\n", __FUNCTION__, __LINE__, data_type);
        return MQTT_ERR;
    }

    return MQTT_OK;
}

int platform_mib_get_pri_device_type(char *pri_device_type)
{
    if (pri_device_type == NULL)
    {
        lua_log_error("[%s][%d] The input parameter is NULL\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    strcpy(pri_device_type, mqtt_device_model);
 
    return 0;
}

int platform_get_run_time_seconds(INT32U *time_seconds)
{
    struct sysinfo info;

    if (time_seconds == NULL)
    {
        lua_log_error("[%s][%d] The input parameter is NULL\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    sysinfo(&info);

    *time_seconds = info.uptime;
    return 0;
}

int platform_get_udp_device_info(struct mqtt_ext_discover_msg *udp_device)
{
    if (udp_device == NULL)
    {
        lua_log_error("[%s][%d] The input parameter is NULL\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    platform_mib_get_dev_detail_info(udp_device->software_version, SW_VER);
    platform_mib_get_dev_detail_info(udp_device->haware_version, HW_VER);
    platform_mib_get_dev_detail_info(udp_device->mac, MAC_ADDRESS);
    platform_mib_get_dev_detail_info(udp_device->customer_model, MODEL_NAME);
    platform_mib_get_dev_detail_info(udp_device->device_pn_type, PN_TYPE);
    platform_mib_get_dev_detail_info(udp_device->vendor_info, VENDOR_INFO);
    platform_mib_get_dev_detail_info(udp_device->project_id, PROJECT_ID);

    platform_mib_get_pri_device_type(udp_device->pri_model);
    udp_device->info_update_time_seconds = 0;
    udp_device->run_time_seconds = 0;
    
    return 0;
}

int get_slave_device_discover_info_list(lua_State *L)
{
    struct udp_device_list_msg discover_info_list_msg = {0};
    unsigned char res_buf[MQTT_BUFFER_SIZE] = {0};
    unsigned short res_buf_len = 0;
    unsigned char buffer[64] = {0};
    char index_str[4] = {0};
    int index = 0;
    int ret = 0;
    int i = 0;

    ret = mqtt_ext_client_send(MQTT_EXT_MSG_UDP_GET_DEVICE_INFO, NULL, 0, res_buf, &res_buf_len);



    if (0 != ret)
    {
        lua_log_error("[%s][%d] Get udp device info failed\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    memcpy(&discover_info_list_msg, res_buf, sizeof(discover_info_list_msg));
    if (discover_info_list_msg.udp_device_num > MAX_ROUTER_NUM)
    {
        lua_log_error("[%s][%d] Udp device number over MAX_ROUTER_NUM\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    lua_newtable(L);

    if (discover_info_list_msg.udp_device_num == 1)
    {
     mqtt_ext_client_send(MQTT_EXT_MSG_UDP_UPDATE_DEVICE_INFO, NULL, 0, NULL, 0);
    }


    for (i = 0; i < discover_info_list_msg.udp_device_num; i++)
    {
        snprintf(index_str, sizeof(index_str), "%d", index);
        lua_pushstring(L, index_str);
        lua_newtable(L);
        index++;

        lua_pushstring(L, "udp_device_type");
        memset(buffer, 0, sizeof(buffer));
        snprintf(buffer, sizeof(buffer), "%d", discover_info_list_msg.udp_device_info[i].udp_device_type);
        lua_pushstring(L, buffer);
        lua_settable(L, -3);

        lua_pushstring(L, "mac");
        memset(buffer, 0, sizeof(buffer));
        snprintf(buffer, sizeof(buffer), "%02x%02x%02x%02x%02x%02x",
                 discover_info_list_msg.udp_device_info[i].mac[0], discover_info_list_msg.udp_device_info[i].mac[1],
                 discover_info_list_msg.udp_device_info[i].mac[2], discover_info_list_msg.udp_device_info[i].mac[3],
                 discover_info_list_msg.udp_device_info[i].mac[4], discover_info_list_msg.udp_device_info[i].mac[5]);
        lua_pushstring(L, buffer);
        lua_settable(L, -3);

        lua_pushstring(L, "device_pn_type");
        memset(buffer, 0, sizeof(buffer));
        strncpy(buffer, discover_info_list_msg.udp_device_info[i].device_pn_type, sizeof(discover_info_list_msg.udp_device_info[i].device_pn_type));
        lua_pushstring(L, buffer);
        lua_settable(L, -3);

        lua_pushstring(L, "vendor_info");
        memset(buffer, 0, sizeof(buffer));
        strncpy(buffer, discover_info_list_msg.udp_device_info[i].vendor_info, sizeof(discover_info_list_msg.udp_device_info[i].vendor_info));
        lua_pushstring(L, buffer);
        lua_settable(L, -3);

        lua_pushstring(L, "project_id");
        memset(buffer, 0, sizeof(buffer));
        strncpy(buffer, discover_info_list_msg.udp_device_info[i].project_id, sizeof(discover_info_list_msg.udp_device_info[i].project_id));
        lua_pushstring(L, buffer);
        lua_settable(L, -3);

        lua_pushstring(L, "customer_model");
        memset(buffer, 0, sizeof(buffer));
        strncpy(buffer, discover_info_list_msg.udp_device_info[i].customer_model, sizeof(discover_info_list_msg.udp_device_info[i].customer_model));
        lua_pushstring(L, buffer);
        lua_settable(L, -3);

        lua_pushstring(L, "software_version");
        memset(buffer, 0, sizeof(buffer));
        strncpy(buffer, discover_info_list_msg.udp_device_info[i].software_version, sizeof(discover_info_list_msg.udp_device_info[i].software_version));
        lua_pushstring(L, buffer);
        lua_settable(L, -3);

        lua_pushstring(L, "pri_model");
        memset(buffer, 0, sizeof(buffer));
        strncpy(buffer, discover_info_list_msg.udp_device_info[i].pri_model, sizeof(discover_info_list_msg.udp_device_info[i].pri_model));
        lua_pushstring(L, buffer);
        lua_settable(L, -3);

        lua_pushstring(L, "hardware_version");
        memset(buffer, 0, sizeof(buffer));
        strncpy(buffer, discover_info_list_msg.udp_device_info[i].haware_version, sizeof(discover_info_list_msg.udp_device_info[i].haware_version));
        lua_pushstring(L, buffer);
        lua_settable(L, -3);

        lua_pushstring(L, "info_update_time_seconds");
        memset(buffer, 0, sizeof(buffer));
        snprintf(buffer, sizeof(buffer), "%d", discover_info_list_msg.udp_device_info[i].info_update_time_seconds);
        lua_pushstring(L, buffer);
        lua_settable(L, -3);

        lua_pushstring(L, "run_time_seconds");
        memset(buffer, 0, sizeof(buffer));
        snprintf(buffer, sizeof(buffer), "%d", discover_info_list_msg.udp_device_info[i].run_time_seconds);
        lua_pushstring(L, buffer);
        lua_settable(L, -3);

        lua_settable(L, -3);
    }

    return 1;
}

void shift_text(char *text, int shift, char *result)
{
    int length = strlen(text);
    shift = shift % 26; // Restricted to 26 letters

    for (int i = 0; i < length; i++)
    {
        char letter = text[i];
        if (isalpha(letter))
        {
            char base = islower(letter) ? 'a' : 'A';
            result[i] = (letter - base + shift + 26) % 26 + base;
        }
        else
        {
            result[i] = letter; // Non letter characters remain unchanged
        }
    }

    result[length] = '\0';
}

int decrypt_pwd(lua_State *L)
{
    char *encrypt_pwd = lua_tostring(L, -1);
    char decrypt_pwd[1024] = {0};
    int shift = 8; // Move 8 positions

    if (encrypt_pwd)
    {
        shift_text(encrypt_pwd, -shift, decrypt_pwd);
        lua_pushstring(L, decrypt_pwd);
    }

    return 1;
}

int sync_project_id_to_udp_local_device(void)
{
    int ret = 0;
    int len = 0;

    ret = mqtt_ext_client_send(MQTT_EXT_MSG_UDP_UPDATE_LOCAL_DEVICE_INFO, NULL, 0, NULL, &len);
    if (0 != ret)
    {
        lua_log_error("[%s][%d] Synchronize project id to udp local device failed\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    return 1;
}

/* Added by ZHH */
/* or use <iwpriv vap0/8 acs "idle_scan 1"> and <iwpriv vap0/8 acs "set_dcs 1>(1 min) */
unsigned char scaning_run_flag = 0; /* for avoiding mutil pthread respeat scan */
void *platform_scan_channel_run(void *arg)
{
	int ret = -1;

    unsigned char *parm = (unsigned char*)arg;
    char bscan_2g_cmd[64] = {0};
    char bscan_5g_cmd[64] = {0};

    char cmd[128] = {0};
    snprintf(bscan_2g_cmd, sizeof(bscan_2g_cmd), "iwpriv default_radio0 acs \"bscan %hhu\"", *parm);
    snprintf(bscan_5g_cmd, sizeof(bscan_5g_cmd), "iwpriv default_radio1 acs \"bscan %hhu\"", *parm);

	scaning_run_flag = 1; /* Set Running flag */

	/* --------------- 2.4G --------------- */
	system("iwpriv default_radio0 acs \"sw 1\""); // enable acs
	// sleep(1);
    system("sleep 1");
	/* Refresh 2.4G Channel Rank Information */
	ret = system(bscan_2g_cmd); // bscan 0 means after sacn not change to best channel
	/* Add for command sw not open */
	if (ret) {
		system("iwpriv default_radio0 acs \"sw 1\"");
		// sleep(2);
        system("sleep 2");
		system(bscan_2g_cmd);
	}
	/* Sleep for waiting result */
	// sleep(3);
    system("sleep 3");
	/* Disable Acs to avoid auto change channel */
	system("iwpriv default_radio0 acs \"sw 0\"");

	/* --------------- 5G --------------- */
	/* Refresh 5G Channel Rank Information */
	system("iwpriv default_radio1 acs \"sw 1\""); // enable acs
	// sleep(1);
    system("sleep 1");

	ret = system(bscan_5g_cmd);
	/* Add for command sw not open */
	if (ret) {
		system("iwpriv default_radio1 acs \"sw 1\"");
		// sleep(2);
        system("sleep 2");
		system(bscan_5g_cmd);
	}
	// sleep(3); /* Sleep for waiting result */
    system("sleep 3");
	/* Disable Acs to avoid auto change channel */
	system("iwpriv default_radio1 acs \"sw 0\"");

	scaning_run_flag = 0; // set running finish
	return NULL;
}

int update_chan_quality_thread_create()
{
    /* Upgrade Channel Scan Result File */
	pthread_t upgrade_ch_thread;
    unsigned char param = 0;
	if (!scaning_run_flag) {
		/* If not has pthread running channel scan */
		if (pthread_create(&upgrade_ch_thread, NULL, platform_scan_channel_run, (void*)&param) != 0) { 
			perror("pthread_create failed");
            return -1;
		} 
		if (pthread_detach(upgrade_ch_thread) != 0) {
			perror("pthread_detach failed");
            return -1;
		}
	}
    return 0;
}

int optimize_chan_quality_thread_create()
{
    /* Upgrade Channel Scan Result File */
	pthread_t upgrade_ch_thread;
    
    unsigned char param = 1;
	if (!scaning_run_flag) {
		/* If not has pthread running channel scan */
		if (pthread_create(&upgrade_ch_thread, NULL, platform_scan_channel_run, (void*)&param) != 0) { 
			perror("pthread_create failed");
            return -1;
		} 
		if (pthread_detach(upgrade_ch_thread) != 0) {
			perror("pthread_detach failed");
            return -1;
		}
	}
    return 0;
}

int wlan_update_channel_quality_info(lua_State *L)
{
    if (0 != mqtt_ext_client_send(MQTT_EXT_MSG_UPDATE_WLAN_QUALITY, NULL, 0, NULL, NULL))
    {
        lua_log_error("[%s][%d] Synchronize project id to udp local device failed\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    return 0;
}
/* End of Addition */

/* Wan Connection Config */
#define WAN_JSON_PARM_STRING \
    "'{\"wanMode\": %hhu, \"ipProtocol\": %hhu, \"ipMode\": %hhu, " \
    "\"napt\": %s, \"vlanMode\": %s, \"vid\": %hhu, " \
    "\"prio\": %hhu, \"mtu\": %u, \"service\": %hhu, " \
    "\"disableLanDhcp\": %s, \"bind\": \"%s\", \"autoDns\": %s, " \
    "\"priDns\": \"%s\", \"secDns\": \"%s\", \"autoDnsV6\": %s, " \
    "\"priDnsV6\": \"%s\", \"secDnsV6\": \"%s\", \"ip\": \"%s\", " \
    "\"mask\": \"%s\", \"gateway\": \"%s\", \"ipv6PrefixProxy\": %s, " \
    "\"ipv6Addr\": \"%s\", \"ipv6Gateway\": \"%s\", \"ipv6Prefix\": \"%s\", " \
    "\"pppUser\": \"%s\", \"pppPwd\": \"%s\", \"pppServName\": \"%s\", " \
    "\"dailMode\": %s, \"ipv6DsLite\": %s, \"ipv6AddrDsLite\": \"%s\"}'"

#define WAN_PARM_VAR_STRING \
    wanMode, ipProtocol, ipMode, napt, vlanMode, vid, prio, mtu, service,\
    disableLanDhcp, bind, autoDns, priDns, secDns, autoDnsV6, priDnsV6,\
    secDnsV6, ip, mask, gateway, ipv6PrefixProxy, ipv6Addr, ipv6Gateway,\
    ipv6Prefix, pppUser, pppPwd, pppServName, dailMode, ipv6DsLite, ipv6AddrDsLite

int set_wan_cfg(lua_State *L)
{
    char* connect_mode = lua_tostring(L, 1);
    
    struct mqtt_ext_wan_cfg wan_cfg = {0};
    
    // 设置连接模式
    snprintf(wan_cfg.connect_mode, sizeof(wan_cfg.connect_mode), "%s", connect_mode);
    
    if(0 == strcmp(connect_mode, "pppoe"))
    {
        char *encodePppUserName = lua_tostring(L, 2);
        char *encodePppPassword = lua_tostring(L, 3);
        char *pppServiceName = lua_tostring(L, 4);

        /* Init value */
        wan_cfg.vid = 0;
        wan_cfg.mtu = 1492;
        wan_cfg.prio = 1;
        wan_cfg.ip_mode = 2;
        wan_cfg.wan_mode = 1;
        wan_cfg.service = 1;
        wan_cfg.ip_protocol = 3;
        wan_cfg.napt = 1;
        wan_cfg.vlan_mode = 0;
        wan_cfg.disable_lan_dhcp = 0;
        snprintf(wan_cfg.bind, sizeof(wan_cfg.bind), "000000000000");
        wan_cfg.auto_dns = 1;
        wan_cfg.auto_dns_v6 = 1;
        wan_cfg.ipv6_prefix_proxy = 1;
        snprintf(wan_cfg.ipv6_addr, sizeof(wan_cfg.ipv6_addr), "/");
        wan_cfg.dial_mode = 1;
        wan_cfg.ipv6_dslite = 0;
        snprintf(wan_cfg.ppp_pwd, sizeof(wan_cfg.ppp_pwd), "%s", encodePppPassword);
        snprintf(wan_cfg.ppp_user, sizeof(wan_cfg.ppp_user), "%s", encodePppUserName);
        if(pppServiceName != NULL){
            snprintf(wan_cfg.ppp_serv_name, sizeof(wan_cfg.ppp_serv_name), "%s", pppServiceName);
        }
    }
    else if(0 == strcmp(connect_mode, "static"))
    {
       // TODO: 实现静态IP配置
       lua_log_error("[%s][%d] Static IP mode not implemented yet\n", __FUNCTION__, __LINE__);
       return 1;
    }
    else if(0 == strcmp(connect_mode, "dhcp"))
    {
        /* Init value */
        wan_cfg.vid = 0;
        wan_cfg.mtu = 1500;
        wan_cfg.prio = 1;
        wan_cfg.ip_mode = 0;
        wan_cfg.wan_mode = 1;
        wan_cfg.service = 1;
        wan_cfg.ip_protocol = 3;
        wan_cfg.napt = 1;
        wan_cfg.vlan_mode = 0;
        wan_cfg.disable_lan_dhcp = 0;
        snprintf(wan_cfg.bind, sizeof(wan_cfg.bind), "000000000000");
        wan_cfg.auto_dns = 1;
        wan_cfg.auto_dns_v6 = 1;
        wan_cfg.ipv6_prefix_proxy = 1;
        snprintf(wan_cfg.ipv6_addr, sizeof(wan_cfg.ipv6_addr), "/");
        wan_cfg.dial_mode = 1;
        wan_cfg.ipv6_dslite = 0;
    }
    else
    {
        lua_log_error("[%s][%d] Unknown WAN type: %s\n", __FUNCTION__, __LINE__, connect_mode);
        return 1;
    }

    /* 通过MQTT发送WAN配置消息 */
    if (mqtt_ext_client_send(MQTT_EXT_MSG_TYPE_WAN_CFG, (INT8U *)&wan_cfg, 
                            sizeof(struct mqtt_ext_wan_cfg), NULL, NULL) != 0) {
        lua_log_error("[%s][%d] Error: Failed to send WAN config to MQTT process\n", __FUNCTION__, __LINE__);
        return 1;
    }
    
    return 0;
}

int platform_set_wan_cfg(struct mqtt_ext_wan_cfg *wan_cfg)
{
    //wan config
    int ret = 0;
    char wan_parm[1024] = {0};
    char syswan_cmd[1280] = {0};
    unsigned int mtu = 0;
    unsigned char vid = 0;
    unsigned char prio = 0;
    unsigned char ipMode = 0;
    unsigned char ip[16] = {0};
    unsigned char wanMode = 0;
    unsigned char service = 0;
    unsigned char napt[8] = {0};
    unsigned char mask[16] = {0};
    unsigned char bind[16] = {0};
    unsigned char ipProtocol = 0;
    unsigned char autoDns[8] = {0};
    unsigned char priDns[16] = {0};
    unsigned char secDns[16] = {0};
    unsigned char pppPwd[32] = {0};
    unsigned char vlanMode[8] = {0};
    unsigned char dailMode[8] = {0};
    unsigned char gateway[16] = {0};
    unsigned char pppUser[32] = {0};
    unsigned char autoDnsV6[8] = {0};
    unsigned char priDnsV6[32] = {0};
    unsigned char ipv6Addr[32] = {0};
    unsigned char secDnsV6[32] = {0};
    unsigned char ipv6DsLite[8] = {0};
    unsigned char ipv6Prefix[32] = {0};
    unsigned char ipv6Gateway[32] = {0};
    unsigned char pppServName[32] = {0};
    unsigned char disableLanDhcp[8] = {0};
    unsigned char ipv6PrefixProxy[8] = {0};
    unsigned char ipv6AddrDsLite[32] = {0};

    FILE *fp = NULL;
    int maxId = -1;
    char result[16] = {0};

    fp = popen("ubus call syswan maxId | grep maxId | awk -F': ' '{print \$2}'", "r");
    if (fp == NULL) {
        perror("popen");
        return 1;
    }
    if (fgets(result, sizeof(result), fp) != NULL) {
        sscanf(result, "%d", &maxId);
    }
    pclose(fp);

    if(0 == strcmp(wan_cfg->connect_mode, "pppoe"))
    {
        /* Init value */
        vid = wan_cfg->vid;
        mtu = wan_cfg->mtu;
        prio = wan_cfg->prio;
        ipMode = wan_cfg->ip_mode;
        wanMode = wan_cfg->wan_mode;
        service = wan_cfg->service;
        ipProtocol = wan_cfg->ip_protocol;
        snprintf(napt, sizeof(napt), wan_cfg->napt ? "true" : "false");
        snprintf(vlanMode, sizeof(vlanMode), wan_cfg->vlan_mode ? "true" : "false");
        snprintf(disableLanDhcp, sizeof(disableLanDhcp), wan_cfg->disable_lan_dhcp ? "true" : "false");
        snprintf(bind, sizeof(bind), "%s", wan_cfg->bind);
        snprintf(autoDns, sizeof(autoDns), wan_cfg->auto_dns ? "true" : "false");
        snprintf(autoDnsV6, sizeof(autoDnsV6), wan_cfg->auto_dns_v6 ? "true" : "false");
        snprintf(ipv6PrefixProxy, sizeof(ipv6PrefixProxy), wan_cfg->ipv6_prefix_proxy ? "true" : "false");
        snprintf(ipv6Addr, sizeof(ipv6Addr), "%s", wan_cfg->ipv6_addr);
        snprintf(dailMode, sizeof(dailMode), wan_cfg->dial_mode ? "true" : "false");
        snprintf(ipv6DsLite, sizeof(ipv6DsLite), wan_cfg->ipv6_dslite ? "true" : "false");
        snprintf(pppPwd, sizeof(pppPwd), "%s", wan_cfg->ppp_pwd);
        snprintf(pppUser, sizeof(pppUser), "%s", wan_cfg->ppp_user);
        snprintf(pppServName, sizeof(pppServName), "%s", wan_cfg->ppp_serv_name);
        snprintf(priDns, sizeof(priDns), "%s", wan_cfg->pri_dns);
        snprintf(secDns, sizeof(secDns), "%s", wan_cfg->sec_dns);
        snprintf(priDnsV6, sizeof(priDnsV6), "%s", wan_cfg->pri_dns_v6);
        snprintf(secDnsV6, sizeof(secDnsV6), "%s", wan_cfg->sec_dns_v6);
        snprintf(ip, sizeof(ip), "%s", wan_cfg->ip);
        snprintf(mask, sizeof(mask), "%s", wan_cfg->mask);
        snprintf(gateway, sizeof(gateway), "%s", wan_cfg->gateway);
        snprintf(ipv6Gateway, sizeof(ipv6Gateway), "%s", wan_cfg->ipv6_gateway);
        snprintf(ipv6Prefix, sizeof(ipv6Prefix), "%s", wan_cfg->ipv6_prefix);
        snprintf(ipv6AddrDsLite, sizeof(ipv6AddrDsLite), "%s", wan_cfg->ipv6_addr_dslite);

        snprintf(wan_parm, sizeof(wan_parm), WAN_JSON_PARM_STRING, WAN_PARM_VAR_STRING);
    }
    else if(0 == strcmp(wan_cfg->connect_mode, "static"))
    {
       // TODO
    }
    else if(0 == strcmp(wan_cfg->connect_mode, "dhcp"))
    {
        /* Init value */
        vid = wan_cfg->vid;
        mtu = wan_cfg->mtu;
        prio = wan_cfg->prio;
        ipMode = wan_cfg->ip_mode;
        wanMode = wan_cfg->wan_mode;
        service = wan_cfg->service;
        ipProtocol = wan_cfg->ip_protocol;
        snprintf(napt, sizeof(napt), wan_cfg->napt ? "true" : "false");
        snprintf(vlanMode, sizeof(vlanMode), wan_cfg->vlan_mode ? "true" : "false");
        snprintf(disableLanDhcp, sizeof(disableLanDhcp), wan_cfg->disable_lan_dhcp ? "true" : "false");
        snprintf(bind, sizeof(bind), "%s", wan_cfg->bind);
        snprintf(autoDns, sizeof(autoDns), wan_cfg->auto_dns ? "true" : "false");
        snprintf(autoDnsV6, sizeof(autoDnsV6), wan_cfg->auto_dns_v6 ? "true" : "false");
        snprintf(ipv6PrefixProxy, sizeof(ipv6PrefixProxy), wan_cfg->ipv6_prefix_proxy ? "true" : "false");
        snprintf(ipv6Addr, sizeof(ipv6Addr), "%s", wan_cfg->ipv6_addr);
        snprintf(dailMode, sizeof(dailMode), wan_cfg->dial_mode ? "true" : "false");
        snprintf(ipv6DsLite, sizeof(ipv6DsLite), wan_cfg->ipv6_dslite ? "true" : "false");
        snprintf(pppPwd, sizeof(pppPwd), "%s", wan_cfg->ppp_pwd);
        snprintf(pppUser, sizeof(pppUser), "%s", wan_cfg->ppp_user);
        snprintf(pppServName, sizeof(pppServName), "%s", wan_cfg->ppp_serv_name);
        snprintf(priDns, sizeof(priDns), "%s", wan_cfg->pri_dns);
        snprintf(secDns, sizeof(secDns), "%s", wan_cfg->sec_dns);
        snprintf(priDnsV6, sizeof(priDnsV6), "%s", wan_cfg->pri_dns_v6);
        snprintf(secDnsV6, sizeof(secDnsV6), "%s", wan_cfg->sec_dns_v6);
        snprintf(ip, sizeof(ip), "%s", wan_cfg->ip);
        snprintf(mask, sizeof(mask), "%s", wan_cfg->mask);
        snprintf(gateway, sizeof(gateway), "%s", wan_cfg->gateway);
        snprintf(ipv6Gateway, sizeof(ipv6Gateway), "%s", wan_cfg->ipv6_gateway);
        snprintf(ipv6Prefix, sizeof(ipv6Prefix), "%s", wan_cfg->ipv6_prefix);
        snprintf(ipv6AddrDsLite, sizeof(ipv6AddrDsLite), "%s", wan_cfg->ipv6_addr_dslite);

        snprintf(wan_parm, sizeof(wan_parm), WAN_JSON_PARM_STRING, WAN_PARM_VAR_STRING);
    }
    else
    {
        printf("[%s] Unknow Wan Type: %s\n", __func__, wan_cfg->connect_mode);
        return -1;
    }
	
    if (maxId >= 1)
        snprintf(syswan_cmd, sizeof(syswan_cmd), "ubus call syswan modify '{\"wanId\":1,%s", wan_parm + 2/* Skip '&{ */);
    else
        snprintf(syswan_cmd, sizeof(syswan_cmd), "ubus call syswan add %s", wan_parm);


    sleep(3);
    system(syswan_cmd);

    return 0;
}



int upgrade_slave_udp_device_info(lua_State *L)
{
    system("touch /tmp/upgrade_slave_udp_device_info");
    if (0 != mqtt_ext_client_send(MQTT_EXT_MSG_UDP_UPDATE_DEVICE_INFO, NULL, 0, NULL, NULL))
    {
        lua_log_error("[%s][%d] Synchronize project id to udp local device failed\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    return 0;
}

int platform_judge_slaveDev_is_meshed()
{
    int user_count = 0;
    char line[256] = {0};
    // Open the file for reading
    FILE* file = fopen("/proc/em_bh_5g_sta/sta_info", "r");
    if (file == NULL) {
        return 0;
    }

    // Read the file line by line
    while (fgets(line, sizeof(line), file)) {
        // Check for the line containing "Total user nums:"
        if (strncmp(line, "Total user nums: ", 17) == 0) {
            // Extract the number after the colon and space
            sscanf(line + 17, "%d", &user_count);
            break;  // Exit loop after finding the value
        }
    }

    // Close the file
    fclose(file);

    return user_count;
}

int judge_whether_is_slave_dev(char *target_mac) {
    FILE *fp;
    char command[256] = {0};
    char buffer[512] = {0};
    int found = 0;

    snprintf(command, sizeof(command), "ubus call em.topology_show get | grep al_mac_addr");

    fp = popen(command, "r");
    if (fp == NULL) {
        perror("popen failed");
        return 0;
    }

    while (fgets(buffer, sizeof(buffer), fp) != NULL) {
        char *mac_start = strstr(buffer, target_mac);
        if (mac_start) {
            found = 1;
            break;
        }
    }

    pclose(fp);
    return found;
}

int optimize_channel(lua_State *L)
{
    if (0 != mqtt_ext_client_send(MQTT_EXT_MSG_UPDATE_WLAN_QUALITY, NULL, 0, NULL, NULL))
    {
        lua_log_error("[%s][%d] Synchronize project id to udp local device failed\r\n", __FUNCTION__, __LINE__);
        return -1;
    }

    return 0;
}

//Add by wzx for Firewall, vpn, qos, iptv
// 判断防火墙级别的函数
static int get_firewall_level(void)
{
	const char *wan_rules[] = {"wan_ssh", "wan_https", "wan_http", "wan_ftp", "wan_smb", "wan_telnet"};
	char buf[64];
	int wan_drop_count = 0;
	int i, ret;
	
	// 遍历所有WAN规则，检查target值
	for (i = 0; i < sizeof(wan_rules) / sizeof(wan_rules[0]); i++) {
		memset(buf, 0, sizeof(buf));
		if (vsuci_get("firewall", wan_rules[i], "target", buf, sizeof(buf)) == UCI_OK) {
			if (strcmp(buf, "DROP") == 0) {
				wan_drop_count++;
			}
		}
	}
	
	// 判断防火墙级别：如果所有WAN端口规则都是DROP，则为高安全级别
	if (wan_drop_count == sizeof(wan_rules) / sizeof(wan_rules[0])) {
		lua_c_log("[%s][%d] Firewall level: HIGH (all %d WAN ports are DROP)\r\n", 
			__FUNCTION__, __LINE__, wan_drop_count);
		return 1;  // 高安全级别
	} else {
		lua_c_log("[%s][%d] Firewall level: NORMAL (%d/%d WAN ports are DROP)\r\n", 
			__FUNCTION__, __LINE__, wan_drop_count, sizeof(wan_rules) / sizeof(wan_rules[0]));
		return 0;  // 普通安全级别
	}
}

static void set_firewall_level(int firewall_level)
{
    const char *wan_rules[] = {"lan_telnet", "wan_telnet", "lan_ssh", "wan_ssh", "wan_http", "wan_ping", "wan_https"};
    const char *target_value = (firewall_level == 1) ? "DROP" : "ACCEPT";
    int i, ret;
    for (i = 0; i < sizeof(wan_rules) / sizeof(wan_rules[0]); i++) {
        ret = vsuci_set("firewall", wan_rules[i], "target", target_value);
        if (ret != UCI_OK) {
            lua_log_error("[%s][%d] Set %s target to %s failed\r\n", __FUNCTION__, __LINE__, wan_rules[i], target_value);
        }
    }
    
    lua_c_log("[%s][%d] Firewall level set to %s (all WAN ports set to %s)\r\n", 
        __FUNCTION__, __LINE__, 
        (firewall_level == 1) ? "HIGH" : "NORMAL", 
        target_value);
}

int get_firewall_info(lua_State *L)
{
	unsigned char firewall_level = 0;
	unsigned char prevent_dos = 0;
	int smart_prevent = 0;

	firewall_level = get_firewall_level();

	prevent_dos = 0;
    char buf[5] = {0};
    if (vsuci_get("firewall", "@defaults[0]", "synflood_protect", buf, sizeof(buf)) == UCI_OK) {
        prevent_dos = atoi(buf);
    }
    else {
        prevent_dos = 0;
    }

	smart_prevent = 0;

	add_firewall_info_to_lua(L, firewall_level, prevent_dos, smart_prevent);
	return 1;
}

int set_firewall_cfg(lua_State *L)
{
    char *firewall_level = lua_tostring(L, 1);
    char *prevent_dos = lua_tostring(L, 2);
    char *smart_prevent = lua_tostring(L, 3);

    if (firewall_level != NULL) {
        if (strstr(firewall_level, "hight")) {
            set_firewall_level(1);
        } else{
            set_firewall_level(0);
        }
    }

    if (prevent_dos != NULL) {
        if (strstr(prevent_dos, "1")) {
            vsuci_set("firewall", "@defaults[0]", "synflood_protect", "1");
        } else{
            vsuci_set("firewall", "@defaults[0]", "synflood_protect", "0");
        }
    }
    
    // 通过MQTT发送防火墙重启命令
    struct mqtt_ext_system_cmd system_cmd = {0};
    snprintf(system_cmd.command, sizeof(system_cmd.command), "/etc/init.d/firewall reload");
    
    if (mqtt_ext_client_send(MQTT_EXT_MSG_TYPE_SYSTEM_CMD, (INT8U *)&system_cmd, 
                            sizeof(struct mqtt_ext_system_cmd), NULL, NULL) != 0) {
        lua_log_error("[%s][%d] Error: Failed to send firewall reload command to MQTT process\n", __FUNCTION__, __LINE__);
        system("/etc/init.d/firewall reload");
    }
    
    return 1;
}

static int platform_get_wan_port(void)
{
    int wan_port = 0;
    char buf[32] = {0};
    char ifname[16] = {0};
    
    // 优先读取 network.@device[5].ifname
    if (vsuci_get("network", "@device[5]", "ifname", ifname, sizeof(ifname)) == UCI_OK) {
        // 解析 ifname，eth0-3 对应 wan_port 1-4
        if (strncmp(ifname, "eth", 3) == 0) {
            int eth_num = atoi(ifname + 3);
            if (eth_num >= 0 && eth_num <= 3) {
                wan_port = eth_num + 1;  // eth0->1, eth1->2, eth2->3, eth3->4
            }
        }
    }
    
    // 如果读取的wan口小于等于0，再读取detected_linkid
    if (wan_port <= 0) {
        if (vsuci_get("auto_wan", "auto_wan", "detected_linkid", buf, sizeof(buf)) == UCI_OK) {
            wan_port = atoi(buf);  // single wan port
        }
    }
    
    // 如果还是小于等于0，最后读取cfg_linkid
    if (wan_port <= 0) {
        if (vsuci_get("auto_wan", "auto_wan", "cfg_linkid", buf, sizeof(buf)) == UCI_OK) {
            wan_port = atoi(buf);  // auto wan port
        }
    }

    if (wan_port <= 0) {
        wan_port = 0;
    }
    return wan_port;
}

int lua_get_wan_port(lua_State *L)
{
    int wan_port = platform_get_wan_port();
    char buf[5] = {0};
    sprintf(buf, "%d", wan_port);
    lua_pushstring(L, buf);
    return 1;
}

/* 查找IPTV WAN是否存在，返回wanId */
static int judge_iptv_wan_exist(int *wan_id)
{
	FILE *fp;
	char buffer[8192] = {0};
	char *ptr = buffer;
	size_t total_read = 0;
	
	/* 执行ubus call syswan all命令 */
	fp = popen("ubus call syswan all", "r");
	if (fp == NULL) {
		lua_log_error("[%s][%d] Error: Failed to execute ubus command\n", __FUNCTION__, __LINE__);
		return -1;
	}
	
	/* 读取完整输出 */
	for (;;) {
		size_t cap = sizeof(buffer) - 1 - total_read;
		if (cap == 0) break;
		size_t n = fread(ptr, 1, cap, fp);
		if (n == 0) break;
		total_read += n;
		ptr += n;
	}
	buffer[total_read] = '\0';
	pclose(fp);
	
	/* 解析，定位第一条 isIptvWan 为 1 的块并取 wanId */
	char *wan_start = buffer;
	while ((wan_start = strstr(wan_start, "\"wanId\":")) != NULL) {
		char *wan_end = strchr(wan_start, '}');
		if (!wan_end) break;
		
		int wan_len = wan_end - wan_start + 1;
		if (wan_len > 0 && wan_len < 1024) {
			char wan_block[1024] = {0};
			strncpy(wan_block, wan_start, wan_len);
			wan_block[wan_len] = '\0';
			
			if (strstr(wan_block, "\"isIptvWan\": 1")) {
				char *wanid_ptr = strstr(wan_block, "\"wanId\":");
				if (wanid_ptr) {
					sscanf(wanid_ptr, "%*[^:]:%d", wan_id);
					return 0;  // 成功找到
				}
			}
		}
		wan_start = wan_end + 1;
	}
	
	*wan_id = -1;  // 未找到IPTV WAN
	return 0;
}

static int find_iptv_wan(char *enable, int *vlan_id,  int *lan_port)
{
    int wan_id = -1;
    FILE *fp;
    char buffer[8192] = {0};
    char *ptr = buffer;
    size_t total_read = 0;
    
    /* 首先检查IPTV WAN是否存在 */
    if (judge_iptv_wan_exist(&wan_id) != 0) {
        *enable = 0;
        *vlan_id = 0;
        *lan_port = 0;
        return -1;
    }
    
    /* 如果不存在IPTV WAN，设置enable为0并返回 */
    if (wan_id == -1) {
        *enable = 0;
        *vlan_id = 0;
        *lan_port = 0;
        return 0;
    }
    
    /* 如果存在IPTV WAN，使用ubus call syswan get获取详细信息 */
    char cmd[256];
    snprintf(cmd, sizeof(cmd), "ubus call syswan get '{\"wanId\": %d}'", wan_id);
    
    fp = popen(cmd, "r");
    if (fp == NULL) {
        lua_log_error("[%s][%d] Error: Failed to execute ubus get command\n", __FUNCTION__, __LINE__);
        *enable = 0;
        *vlan_id = 0;
        *lan_port = 0;
        return -1;
    }
    
    /* 读取完整输出 */
    for (;;) {
        size_t cap = sizeof(buffer) - 1 - total_read;
        if (cap == 0) break;
        size_t n = fread(ptr, 1, cap, fp);
        if (n == 0) break;
        total_read += n;
        ptr += n;
    }
    buffer[total_read] = '\0';
    pclose(fp);
    
    /* 解析输出，提取vid和bind */
    int current_vid = 0;
    char current_bind[64] = {0};
    
    /* 提取vid */
    char *vid_ptr = strstr(buffer, "\"vid\":");
    if (vid_ptr) {
        sscanf(vid_ptr, "%*[^:]:%d", &current_vid);
    }
    
    /* 提取bind */
    char *bind_ptr = strstr(buffer, "\"bind\":");
    if (bind_ptr) {
        sscanf(bind_ptr, "\"bind\": \"%63[^\"]", current_bind);
    }
    
    /* 设置返回值 */
    *enable = 1;
    *vlan_id = current_vid;
    
    *lan_port = 0;
    for (int i = 0; i < strlen(current_bind) && i < 12; i++) {
        if (current_bind[i] == '1') {
            int lan_num = i + 1;  // LAN端口号：1,2,3,4...
            *lan_port |= lan_num;  // 直接使用端口号作为掩码值
        }
    }
    
    return 0;
}

int get_iptv_info(lua_State *L)
{
    unsigned char enable = 0;
    unsigned int vlan_id = 0;
    unsigned int wan_port = 0;
    unsigned int lan_port = 0;

    enable = 0;
    vlan_id = 0;
    wan_port = platform_get_wan_port();
    lan_port = 0;

    find_iptv_wan(&enable, &vlan_id, &lan_port);
    add_iptv_info_to_lua(L, enable, vlan_id, wan_port, lan_port);
    return 1;
}

int platform_set_iptv_cfg(int iptv_enable, int vid_val, int bindport)
{
    /* 查找已存在的 IPTV WAN 的 wanId */
	int exist_id = -1;
	if (judge_iptv_wan_exist(&exist_id) != 0) {
		exist_id = -1;  // 重置为-1，表示未找到
	}

	/* 如果找到且需要关闭：直接删除 */
	if (exist_id > 0 && iptv_enable == 0) {
		char cmd[128];
		snprintf(cmd, sizeof(cmd), "ubus call syswan del '{\"wanId\": %d}'", exist_id);
		system(cmd);
		return 1;
	}
	/* 需要启用：若存在则先删后增；若不存在则直接增 */
	if (iptv_enable == 1) {
		char bind[13];
		int i;
		for (i = 0; i < 12; i++) {
			int port_num = i + 1;  // 端口1,2,3,4...
			bind[i] = (bindport == port_num) ? '1' : '0';  // 只有匹配的端口位置设为'1'
		}
		bind[12] = '\0';

		if (exist_id > 0) {
			char delcmd[128];
			snprintf(delcmd, sizeof(delcmd), "ubus call syswan del '{\"wanId\": %d}'", exist_id);
			system(delcmd);
		}

		{
			char addcmd[1024];
			snprintf(addcmd, sizeof(addcmd),
				"ubus call syswan add '{"
				"\"wanMode\": 0, "
				"\"ipProtocol\": 3, "
				"\"ipMode\": 0, "
				"\"napt\": false, "
				"\"vlanMode\": true, "
				"\"vid\": %d, "
				"\"mvid\": 0, "
				"\"prio\": 1, "
				"\"mtu\": 1500, "
				"\"service\": 3, "
				"\"disableLanDhcp\": true, "
				"\"bind\": \"%s\", "
				"\"autoDns\": true, "
				"\"priDns\": \"\", "
				"\"secDns\": \"\", "
				"\"autoDnsV6\": true, "
				"\"priDnsV6\": \"\", "
				"\"secDnsV6\": \"\", "
				"\"ip\": \"\", "
				"\"mask\": \"\", "
				"\"gateway\": \"\", "
				"\"ipv6PrefixProxy\": true, "
				"\"ipv6Addr\": \"\", "
				"\"ipv6Gateway\": \"\", "
				"\"ipv6Prefix\": \"\", "
				"\"pppUser\": \"\", "
				"\"pppPwd\": \"\", "
				"\"pppServName\": \"\", "
				"\"dailMode\": true, "
				"\"ipv6DsLite\": false, "
				"\"ipv6AddrDsLite\": \"\", "
				"\"isIptvWan\": 1}'",
				vid_val, bind);
			system(addcmd);
		}
		return 1;
	}
    return 1;
}


int lua_set_iptv_cfg(lua_State *L)
{
	int iptv_enable = lua_tonumber(L, 1);
	int vid_val = lua_tonumber(L, 2);
	int bindport = lua_tonumber(L, 3);

	struct mqtt_ext_iptv_cfg iptv_cfg = {0};
	iptv_cfg.iptv_enable = iptv_enable;
	iptv_cfg.vid_val = vid_val;
	iptv_cfg.bindport = bindport;

	/* 通过MQTT发送IPTV配置消息 */
	if (mqtt_ext_client_send(MQTT_EXT_MSG_TYPE_IPTV_CFG, (INT8U *)&iptv_cfg, 
	                        sizeof(struct mqtt_ext_iptv_cfg), NULL, NULL) != 0) {
		lua_log_error("[%s][%d] Error: Failed to send IPTV config to MQTT process\n", __FUNCTION__, __LINE__);
		return 1;
	}
	
	return 1;
}

int platform_set_smart_qos_cfg(int qos_enable, int qos_mode)
{
    char cmd[128];
    snprintf(cmd, sizeof(cmd), "ubus call smart_qos smart_qos_set '{\"enable\": %d,\"mode\": %d}'", qos_enable, qos_mode);
    int ret = system(cmd);
    return (ret == 0) ? 0 : -1;
}

int lua_set_smart_qos_cfg(lua_State *L)
{
	int qos_enable = lua_tonumber(L, 1);
	int qos_mode = 0;

	struct mqtt_ext_smart_qos_cfg smart_qos_cfg = {0};
	smart_qos_cfg.qos_enable = qos_enable;
	smart_qos_cfg.qos_mode = qos_mode;

	/* 通过MQTT发送Smart QoS配置消息 */
	if (mqtt_ext_client_send(MQTT_EXT_MSG_TYPE_SMART_QOS_CFG, (INT8U *)&smart_qos_cfg, 
	                        sizeof(struct mqtt_ext_smart_qos_cfg), NULL, NULL) != 0) {
		lua_log_error("[%s][%d] Error: Failed to send Smart QoS config to MQTT process\n", __FUNCTION__, __LINE__);
		return 1;
	}
	
	return 1;
}

int get_qos_info(lua_State *L)
{
    unsigned char qos_enable = 0;
    char buf[4] = {0};
    vsuci_get("smart_qos", "smart_qos", "enable", buf, sizeof(buf));
    qos_enable = atoi(buf);

    add_qos_info_to_lua(L, qos_enable);
    return 1;
}

int get_vpn_info(lua_State *L)
{
    return 1;
}


/*Add by wzx for WAN Speed */
/**
 * WAN速率缓存结构
 */
struct _wan_rate_cache {
    time_t last_time;
    uint64_t tx_bytes;
    uint64_t rx_bytes;
    uint32_t up_kBps;
    uint32_t down_kBps;
};

/**
 * 保存WAN速率缓存到文件
 */
static int wan_rate_save_cache(struct _wan_rate_cache *cache)
{
    int fd = open(WAN_RATE_CACHE_FILE, O_CREAT | O_WRONLY | O_TRUNC, S_IRWXU);
    if (fd == -1) {
        return -1;
    }
    
    ssize_t len = write(fd, cache, sizeof(struct _wan_rate_cache));
    close(fd);
    
    return (len == sizeof(struct _wan_rate_cache)) ? 0 : -1;
}

/**
 * 从文件加载WAN速率缓存
 */
static int wan_rate_load_cache(struct _wan_rate_cache *cache)
{
    int fd = open(WAN_RATE_CACHE_FILE, O_RDONLY);
    if (fd == -1) {
        return -1;
    }
    
    ssize_t len = read(fd, cache, sizeof(struct _wan_rate_cache));
    close(fd);
    
    return (len == sizeof(struct _wan_rate_cache)) ? 0 : -1;
}

/**
 * 获取WAN设备名称（使用ubus和vsuci接口）
 * @return: WAN设备名称，失败返回"pon"
 */
static char* platform_get_wan_device_name(void)
{
    static char wan_dev[32] = {0};

    if ((strcmp(mqtt_device_model, DEVICE_MODEL_V2802ACH) == 0)  || (strcmp(mqtt_device_model, DEVICE_MODEL_HG3232AXTH) == 0)) {
        strcpy(wan_dev, "pon");
        return wan_dev;
    }

    char buf[32] = {0};
    int wan_id = 1;
    int ip_version = 1; // 1: ipv4, 2: ipv6, 3: ipv4/ipv6
    char wan_index[32] = {0};
    FILE *fp = NULL;
    char line[256] = {0};
    int found_service = 0;
    
    // 调用ubus获取syswan信息
    char buffer[2048] = {0};
    char *ptr = buffer;
    size_t total_read = 0;
    
    fp = popen("ubus call syswan all", "r");
    if (fp != NULL) {
        // 读取完整输出
        for (;;) {
            size_t cap = sizeof(buffer) - 1 - total_read;
            if (cap == 0) break;
            size_t n = fread(ptr, 1, cap, fp);
            if (n == 0) break;
            total_read += n;
            ptr += n;
        }
        buffer[total_read] = '\0';
        pclose(fp);
        
        // 解析，定位第一条 service 为 1 的块并取 wanId 和 ipProtocol
        char *wan_start = buffer;
        while ((wan_start = strstr(wan_start, "\"wanId\":")) != NULL) {
            char *wan_end = strchr(wan_start, '}');
            if (!wan_end) break;
            
            int wan_len = wan_end - wan_start + 1;
            if (wan_len > 0 && wan_len < 1024) {
                char wan_block[1024] = {0};
                strncpy(wan_block, wan_start, wan_len);
                wan_block[wan_len] = '\0';
                
                // 查找service为1且有IP地址的WAN
                if (strstr(wan_block, "\"service\": 1")) {
                    // 检查是否有IP地址（不是空字符串）
                    char *ip_ptr = strstr(wan_block, "\"ip\":");
                    if (ip_ptr) {
                        // 跳过"ip": "，检查后面是否有实际IP
                        char *ip_value = strstr(ip_ptr, "\"ip\": \"");
                        if (ip_value) {
                            ip_value += 7; // 跳过"ip": "
                            char *ip_end = strchr(ip_value, '"');
                            if (ip_end && ip_end > ip_value) {
                                // 有IP地址，选择这个WAN
                                found_service = 1;
                                
                                // 提取wanId
                                char *wanid_ptr = strstr(wan_block, "\"wanId\":");
                                if (wanid_ptr) {
                                    sscanf(wanid_ptr, "%*[^:]:%d", &wan_id);
                                }
                                
                                // 提取ipProtocol
                                char *ipprotocol_ptr = strstr(wan_block, "\"ipProtocol\":");
                                if (ipprotocol_ptr) {
                                    sscanf(ipprotocol_ptr, "%*[^:]:%d", &ip_version);
                                }
                                
                                break; // 找到service=1且有IP的WAN后跳出循环
                            }
                        }
                    }
                }
            }
            wan_start = wan_end + 1;
        }
    }
    
    // 如果没找到service=1且有IP地址的WAN，使用默认值
    if (!found_service) {
        wan_id = 1;
        ip_version = 1;
    }
    
    // 根据ip_version确定wan_index
    if (ip_version == 2) {
        snprintf(wan_index, sizeof(wan_index), "wan_%d_v6", wan_id);
    } else {
        snprintf(wan_index, sizeof(wan_index), "wan_%d", wan_id);
    }
    
    // 使用vsuci_get获取network配置中的device
    if (vsuci_get("network", wan_index, "device", buf, sizeof(buf)) == UCI_OK) {
        if (strlen(buf) > 0) {
            strncpy(wan_dev, buf, sizeof(wan_dev) - 1);
            wan_dev[sizeof(wan_dev) - 1] = '\0';
        }
    }
    
    return wan_dev;
}

/**
 * 从文件读取网络统计字节数
 * @param file_path: 文件路径
 * @return: 字节数，失败返回0
 */
static uint64_t read_network_bytes(const char* file_path)
{
    FILE *fp = fopen(file_path, "r");
    if (fp == NULL) {
        return 0;
    }
    
    uint64_t bytes = 0;
    if (fscanf(fp, "%lu", &bytes) != 1) {
        bytes = 0;
    }
    
    fclose(fp);
    return bytes;
}

/**
 * 获取WAN速率（使用缓存机制）
 * @param L: Lua状态
 * @return: 返回upload和download速率(KB/s)
 */
int platform_get_wan_speed(lua_State *L)
{
    char* wan_dev = platform_get_wan_device_name();
    char tx_file_path[128];
    char rx_file_path[128];
    struct _wan_rate_cache cache = {0};
    time_t current_time;
    uint32_t time_interval;
    uint32_t upload_rate = 0;
    uint32_t download_rate = 0;
    
    // tx_bytes and rx_bytes file path
    snprintf(tx_file_path, sizeof(tx_file_path), "/sys/class/net/%s/statistics/tx_bytes", wan_dev);
    snprintf(rx_file_path, sizeof(rx_file_path), "/sys/class/net/%s/statistics/rx_bytes", wan_dev);
    
    // 获取当前时间
    time(&current_time);
    
    // 尝试加载缓存
    int cache_load_result = wan_rate_load_cache(&cache);
    
    if (cache_load_result == 0) {
        time_interval = current_time - cache.last_time;
        
        // 如果时间间隔小于最小间隔，直接返回缓存的值
        if (time_interval < WAN_RATE_INTERVAL) {
            upload_rate = cache.up_kBps;
            download_rate = cache.down_kBps;
            lua_pushnumber(L, upload_rate);
            lua_pushnumber(L, download_rate);
            return 2;
        }
    }
    
    // 读取当前字节数
    uint64_t tx_current = read_network_bytes(tx_file_path);
    uint64_t rx_current = read_network_bytes(rx_file_path);
    
    // 如果有缓存数据，计算速率
    if (cache.last_time > 0 && cache.tx_bytes > 0 && cache.rx_bytes > 0) {
        time_interval = current_time - cache.last_time;
        
        if (time_interval > 0) {
            // 计算字节差(本次字节数量与上次字节数量之差)
            uint64_t tx_diff = (tx_current >= cache.tx_bytes) ? (tx_current - cache.tx_bytes) : 0;
            uint64_t rx_diff = (rx_current >= cache.rx_bytes) ? (rx_current - cache.rx_bytes) : 0;
            
            // 计算速率 (KB/s)
            upload_rate = (uint32_t)(tx_diff / (1024 * time_interval));    // 上行速率
            download_rate = (uint32_t)(rx_diff / (1024 * time_interval));  // 下行速率
        }
    } else {
        // 第一次运行或缓存无效，设置初始值
        time_interval = 0;
        upload_rate = 0;
        download_rate = 0;
    }
    
    // 更新缓存
    cache.last_time = current_time;
    cache.tx_bytes = tx_current;
    cache.rx_bytes = rx_current;
    cache.up_kBps = upload_rate;
    cache.down_kBps = download_rate;
    
    // 保存缓存
    wan_rate_save_cache(&cache);
    
    // 返回结果给Lua
    lua_pushnumber(L, upload_rate);
    lua_pushnumber(L, download_rate);
    
    return 2;  // 返回2个值
}

/*End of Add by wzx for WAN Speed*/

int set_vpn_cfg(lua_State *L)
{
    return 1;
}   
//End of Add by wzx for Firewall, vpn, qos, iptv