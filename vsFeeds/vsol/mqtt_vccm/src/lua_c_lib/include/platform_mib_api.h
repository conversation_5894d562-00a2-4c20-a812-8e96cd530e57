#ifndef _PLATFORM_MIB_API_H
#define _PLATFORM_MIB_API_H

#include "platform_public.h"
#include "mqtt_ext_msg.h"

#define DEVICE_MODEL_HG5013 "HG5013-4G"
#define DEVICE_PN_TYPE_HG5013      "HG5013-4G_01W"

#define DEVICE_MODEL_JETONAX3000AIR "Jeton AX3000 Air"
#define DEVICE_PN_TYPE_JETONAX3000AIR "JetonAX3000Air-4G4A_01W"

#define DEVICE_MODEL_JETONAC1200AIR "Jeton AC1200 Air"
#define DEVICE_PN_TYPE_JETONAC1200AIR "JetonAC1200Air-4G4A_01W"


#define DEVICE_MODEL_JETONAX3000CORE "Jeton AX3000 Core"
#define DEVICE_PN_TYPE_JETONAX3000CORE "JetonAX3000Core-4G4A_01W"

#define DEVICE_MODEL_JETONAX3000ULTRA "Jeton AX3000 Ultra"
#define DEVICE_PN_TYPE_JETONAX3000ULTRA "JetonAX3000Ultra-4G4A_01W"


#define DEVICE_MODEL_HG3232AXTH "HG3232AXT-H"
#define DEVICE_PN_TYPE_HG3232AXTH      "HG3232AXT-H-4G-L_01W"

#define DEVICE_MODEL_V2802ACH "V2802AC-H"
#define DEVICE_PN_TYPE_V2802ACH      "V2802AC-H-2G2A_01W"

#define LAN_IF_NAME "br-lan"
#define MAX_PORT_NUM 4
#define UPGRADE_FILE_PATH "/var/tmp/upgrade.bin"
#define PLATFORM_PRINTF_LOG lua_log_debug

#define CONTROLLER_DEV 0
#define AGENT_DEV 1
#define STA_DEV 2

#define MAX_SSID_LEN 64
#define MAX_PSK_LEN 64
#define MAC_ADDR_STRING_LEN 18
#define IP_ADDR_STRING_LEN 16
#define TOPO_LINE_LEN 128
#define CMD_BUF_SIZE 128

#define MAX_CLIENT_NUM 256
#define MAX_FAMILY_NUM 128
#define MAX_DATE_INFO_NUM 512
#define ALL_ROUTER_INFO_FILE "/tmp/all_router_info"
#define TOPOLOGY_ROUTER_BOA_FILE "/tmp/topology_router_boa_file"
#define TOPOLOGY_CLIENT_BOA_FILE "/tmp/topology_client_boa_file"
#define TOPOLOGY_ETH_MESH_MASK_FILE "/tmp/topology_eth_mesh_mask_file"

#define MQTT_PORTS_RATE_TMP_FILE "/tmp/mqtt_ports_rate_tmp"

/*VST discover router*/
#define HTTP_PORT 8000
#define URL_PATH "/APPC"

#define MESH_ROLE_DISABLED 0
#define MESH_ROLE_CONTROLLER 1
#define MESH_ROLE_AGENT 2
#define MESH_ROLE_AUTO 3

#define MQTT_DEVICE_CLASS "ONU"
#define MQTT_DEVICE_CLASS_ROUTER "ROUTER"


#define MQTT_BIND_STATUS_BINDING "binding"
#define MQTT_BIND_STATUS_ONLINE "online"

#define MQTT_MAX_SCHEDULE_NUM 1
#define MAX_WLAN_INT_NUM 3

#define WEAK_SIGNAL_STA_RSSI (-75)

#define DOWNLOAD_SUCCESS_STR "DOWNLOAD_SUCCESS"
#define DOWNLOAD_FAILED_STR "DOWNLOAD_FAILED"
#define DOWNLOAD_TIMEOUT_STR "DOWNLOAD_TIMEOUT"
#define DOWNLOAD_FILE_INCORRECT_STR "DOWNLOAD_FILE_INCORRECT"
#define DOWNLOAD_ING_STR "DOWNLOAD_ING"

enum
{
    MQTT_BIND_NO_NEED = 0,
    MQTT_BIND_NEED = 1,
};
enum
{
    QUICK_CFG_NO_NEED = 0,
    QUICK_CFG_NEED = 1,
};
/*END VST discover router*/

enum
{
    AP_GUEST_OPEN_DURATION_DISABLE = 0,
    AP_GUEST_OPEN_DURATION_4H = 1,
    AP_GUEST_OPEN_DURATION_ONE_DAY = 2,
    AP_GUEST_OPEN_DURATION_NO_LIMIT = 3,
};

typedef enum
{
    MODEL_NAME = 0,
    PN_TYPE = 1,
    DEV_ID = 2,
    MAC_ADDRESS = 3,
    SW_VER = 4,
    HW_VER = 5,
    PROJECT_ID = 6,
    VENDOR_INFO = 7,
    DEV_SN = 8,
} DEV_DETAIL_INFO;

typedef enum
{
    HG5013 = 0,
    HG3232AXT_H = 1,
    V2802AC_H = 2,
    HG5012AC = 3,
    HG5013_H2 = 4,
    HG5013_H3 = 5,
} device_model;

typedef enum
{
    IP_INDEX = 0,
    MAC_INDEX = 1,
    CONNTYPE_INDEX = 2,
    PARENTMAC_INDEX = 3,
    DEVTYPE_INDEX = 4,
    RSSI_INDEX = 5,
    RX_INDEX = 6,
    TX_INDEX = 7,
} ROUTER_OR_CLIENT_BOA_ELE_INDEX;


typedef enum
{
    AGENT_MAC_INDEX = 0,
    AGENT_MODEL_NAME_INDEX = 1,
    AGENT_SOFTWARE_VERSION_INDEX = 2,
    AGENT_UPGRADE_TYPE_INDEX = 3,
    AGENT_HAWARE_VERSION_INDEX = 4,
    AGENT_UP_TIME_INDEX = 5,
    AGENT_UPDATE_TIME_INDEX = 6,
} ALL_ROUTER_BOA_ELE_INDEX;

typedef struct clinet_device_info
{
    char online_state;
    char connType;
    char isEnable;
    int family_idx;
    char device_type_index[64];
    char HostName[64];
    char ip_str[32];
    char rssi[8];
    char mac_str[MAC_ADDR_STRING_LEN];
    char parent_mac_str[MAC_ADDR_STRING_LEN];
    char up_KBps[16];
    char dl_KBps[16];
} CLIENT_DEV_INFO;

typedef struct wlan_cfg_param
{
    char ssid[MAX_SSID_LEN];
    char password[MAX_PSK_LEN];
    char encryption[16];
} WLAN_CFG_INFO;

typedef struct family_info_param
{
    int family_idx;
    char family_name[64];
} FAMILY_INFO;

typedef struct date_list_param
{
    int family_idx;
    int date_week;
    int on_time;
    int off_time;
} DATE_LIST_INFO;

typedef struct URLInfo
{
    char url[128];
} URLInfo;

typedef struct url_list_param
{
    int family_idx;
    int url_limit_type;
    int urls_num;
    URLInfo urls[64];
} URL_LIST_INFO;

typedef struct sch_info
{
    int sch_enable;
    int on_time_min;
    int off_time_min; /* minutes*/
    int week_day;     /* 0001111 */
} SCH_INTO_T;

typedef struct wlan_tiemr
{
    int sch_num;
    int wlan_idx;
    int sch_enable;
    int pre_enable_state;
    struct sch_info sch_info[MQTT_MAX_SCHEDULE_NUM];
} WLAN_TIMER_T;

#define WAN_RATE_CACHE_FILE "/tmp/wan_rate_cache"
#define WAN_RATE_INTERVAL 2  // 最小间隔2秒


extern char mqtt_device_model[];

extern int platform_mib_init();
extern int platform_mib_get_mqtt_info(int *mqtt_enable, char *server_addr, char *user_name, char *user_passwd, char *client_id);
extern int platform_mib_get_dev_mac(char *mac);
extern int platform_mib_get_bind_cfg(char *user_id, char *timestamp, char *group_name);
extern int platform_mib_unbind_cfg();
// extern int get_upgrade_info_string(char *string, INT16U string_len, void *upgrade_info);
extern int get_upgrade_info_string(char *string, struct upgrade_deivce_param_msg *upgrade_deivce_param_info);
extern int get_upgrade_info_downloading_string(char *string, struct mqtt_app_upgrade_msg *mqtt_app_upgrade_info);
extern int get_base_info(lua_State *L);
extern int get_ap_info(lua_State *L);
extern int get_all_router_mac(char *mac_list);
extern int get_client_info_table(lua_State *L);
extern int get_weak_signal_sta_list_table(lua_State *L);
extern int get_throughput_info_table(lua_State *L);
extern int get_mesh_info_table(lua_State *L);
extern int set_wlan_cfg_init(lua_State *L);
extern int set_wlan_cfg(lua_State *L);
extern int set_wlan_cfg_take_effect(lua_State *L);
extern int set_mesh_cfg(lua_State *L);
extern int set_client_type(lua_State *L);
extern int get_family_info_table(lua_State *L);
extern int get_date_list_info_table(lua_State *L);
extern int get_url_list_info_table(lua_State *L);
extern int update_mib(lua_State *L);
extern int set_del_family(lua_State *L);
extern int set_del_client_url_list_by_family_idx(lua_State *L);
extern int set_del_client_time_ctrl_by_family_idx(lua_State *L);
extern int set_del_client_time_ctrl_by_family_idx(lua_State *L);
extern int set_client_time_ctrl_by_mac(lua_State *L);
extern int set_client_url_limit(lua_State *L);
extern int set_del_device(lua_State *L);
extern int platform_lua_bind_mqtt_user_mng(lua_State *L);
extern int get_upgrade_info(lua_State *L);
extern int upgrade_devices(lua_State *L);
extern int platform_device_reset();
extern int platform_get_gw_ip(char *ip);
extern int platform_get_br_name(char *br_name);
extern int platform_mib_bind_cfg(int mqtt_enable, char *server_addr, char *user_id, char *timestamp, char *group_name, char *mqtt_bind_topic);
extern int platform_mib_bind_status_set(char *bind_status);
extern int platform_upgrade_handle();
extern int platfrom_get_download_result(char *md5_result);
extern int platform_check_upgrade_file();
extern int platfrom_download_file_by_http(const char *download_path, double *duration);
extern int platfrom_download_file_by_ftp(const char *download_path, double *duration, const char *ftp_user, const char *ftp_password);
extern int platfrom_set_open_ftp_server();
extern int platfrom_set_close_ftp_server();
extern int platfrom_get_ftp_cfg(char *ftp_user, char *ftp_password);
extern int platform_set_ftp_server(const int ftp_enable);
extern int platform_get_upgrade_time(INT16U *upgrade_time);
extern int platform_get_device_mac_str(char *device_mac);
extern int platform_get_run_time_seconds(INT32U *time_seconds);
extern int platform_mib_get_dev_detail_info(char *data, DEV_DETAIL_INFO data_type);
extern int platform_get_device_id(char *mac_str, int len);
extern int platform_get_device_mac_by_ipaddr(const char *src_ip, char *terminal_mac);
extern int get_slave_device_discover_info_list(lua_State *L);
extern int decrypt_pwd(lua_State *L);
extern int sync_project_id_to_udp_local_device(void);
extern int device_get_mesh_role();
extern int wlan_update_channel_quality_info(lua_State *L);
extern int update_chan_quality_thread_create();
extern int optimize_chan_quality_thread_create();
extern int platform_judge_device_model(void);

extern int set_wan_cfg(lua_State *L);
extern int upgrade_slave_udp_device_info(lua_State *L);
extern int platform_judge_slaveDev_is_meshed(void);

extern int judge_whether_is_slave_dev(char *target_mac);

extern int optimize_channel(lua_State *L);
extern int get_firewall_info(lua_State *L);
extern int set_firewall_cfg(lua_State *L);
extern int get_iptv_info(lua_State *L);
extern int lua_set_iptv_cfg(lua_State *L);
extern int get_qos_info(lua_State *L);
extern int get_vpn_info(lua_State *L);
extern int set_vpn_cfg(lua_State *L);
extern int lua_get_wan_port(lua_State *L);
extern int platform_set_iptv_cfg(int iptv_enable, int vid_val, int bindport);
extern int platform_set_smart_qos_cfg(int qos_enable, int qos_mode);
extern int platform_set_wan_cfg(struct mqtt_ext_wan_cfg *wan_cfg);
extern int lua_set_smart_qos_cfg(lua_State *L);
extern int platform_get_wan_speed(lua_State *L);
#endif
