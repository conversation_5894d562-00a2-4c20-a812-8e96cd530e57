#include "smart_qos_ubus.h"

/* Check if xt_ndpi kernel module is loaded */
static int is_xt_ndpi_loaded(void)
{
    FILE *fp;
    char line[256];
    int loaded = 0;

    fp = fopen("/proc/modules", "r");
    if (fp == NULL)
    {
        printf("Failed to open /proc/modules\n");
        return 0;
    }

    while (fgets(line, sizeof(line), fp))
    {
        if (strncmp(line, "xt_ndpi", 7) == 0)
        {
            loaded = 1;
            break;
        }
    }

    fclose(fp);
    return loaded;
}

/* Load xt_ndpi kernel module */
static int load_xt_ndpi_module(void)
{
    if (is_xt_ndpi_loaded())
    {
        return SMART_QOS_UBUS_RET_SUCC;
    }

    os_execcmd("modprobe xt_ndpi");

    return SMART_QOS_UBUS_RET_SUCC;
}

/* Unload xt_ndpi kernel module */
static int unload_xt_ndpi_module(void)
{
    if (!is_xt_ndpi_loaded())
    {
        return SMART_QOS_UBUS_RET_SUCC;
    }

    os_execcmd("rmmod xt_ndpi");

    return SMART_QOS_UBUS_RET_SUCC;
}

/* Check if module can be safely unloaded */
static int can_unload_xt_ndpi(void)
{
    FILE *fp;
    char line[256];
    int ref_count = 0;

    fp = fopen("/proc/modules", "r");
    if (!fp)
        return 1;

    while (fgets(line, sizeof(line), fp))
    {
        if (strncmp(line, "xt_ndpi", 7) == 0)
        {
            sscanf(line, "%*s %*s %d", &ref_count);
            break;
        }
    }
    fclose(fp);

    return (ref_count == 0);
}

int smart_qos_set_enable_action(int enable)
{
    int unload_check_retry = 50, module_check_retry = 50;

    if (enable == SMART_QOS_ENABLE)
    {
        load_xt_ndpi_module();

        /* Check if xt_ndpi module is actually loaded */
        while (module_check_retry > 0 && !is_xt_ndpi_loaded())
        {
            module_check_retry--;
            usleep(100000); // 100ms
        }

        if (!is_xt_ndpi_loaded())
        {
            printf("modprobe xt_ndpi fail\n");
        }

        smart_qos_iptables_init();
        printf("Smart QoS enabled\n");
    }
    else if (enable == SMART_QOS_DISABLE)
    {
        smart_qos_iptables_flush_rule();

        /* Wait for module reference count to drop */
        while (unload_check_retry > 0 && !can_unload_xt_ndpi())
        {
            unload_check_retry--;
            usleep(100000); // 100ms
        }

        if (!can_unload_xt_ndpi())
        {
            printf("smart qos iptables flush rules fail\n");
        }

        unload_xt_ndpi_module();
        printf("Smart QoS disabled\n");
    }

    return SMART_QOS_UBUS_RET_SUCC;
}

int smart_qos_set_mode_action(int mode)
{
    char protocol[2048] = {0};

    switch (mode)
    {
        case CUSTOM_PRIORITY_MODE:
            smart_qos_iptables_flush_rule();
            if (smart_qos_read_custom_protocol_uci(protocol, sizeof(protocol)) != SMART_QOS_UBUS_RET_FAIL)
            {
                smart_qos_iptables_protocol_priority_rule(protocol);
                printf("Smart QoS set to CUSTOM_PRIORITY_MODE\n");
            }
            break;
        case WEB_PRIORITY_MODE:
            smart_qos_iptables_flush_rule();
            if (smart_qos_read_web_protocol_uci(protocol, sizeof(protocol)) != SMART_QOS_UBUS_RET_FAIL)
            {
                smart_qos_iptables_protocol_priority_rule(protocol);
                printf("Smart QoS set to WEB_PRIORITY_MODE\n");
            }
            break;
        case GAME_PRIORITY_MODE:
            smart_qos_iptables_flush_rule();
            if (smart_qos_read_game_protocol_uci(protocol, sizeof(protocol)) != SMART_QOS_UBUS_RET_FAIL)
            {
                smart_qos_iptables_protocol_priority_rule(protocol);
                printf("Smart QoS set to GAME_PRIORITY_MODE\n");
            }
            break;
        case VIDEO_PRIORITY_MODE:
            smart_qos_iptables_flush_rule();
            if (smart_qos_read_video_protocol_uci(protocol, sizeof(protocol)) != SMART_QOS_UBUS_RET_FAIL)
            {
                smart_qos_iptables_protocol_priority_rule(protocol);
                printf("Smart QoS set to VIDEO_PRIORITY_MODE\n");
            }
            break;
        case SOCIAL_PRIORITY_MODE:
            smart_qos_iptables_flush_rule();
            if (smart_qos_read_social_protocol_uci(protocol, sizeof(protocol)) != SMART_QOS_UBUS_RET_FAIL)
            {
                smart_qos_iptables_protocol_priority_rule(protocol);
                printf("Smart QoS set to SOCIAL_PRIORITY_MODE\n");
            }
            break;
        default:
            printf("smart_qos_set_mode_action: Unknown mode %d\n", mode);
            return SMART_QOS_UBUS_RET_PARAM_INVALID;
    }

    return SMART_QOS_UBUS_RET_SUCC;
}

int smart_qos_apply_config(const smart_qos_config_t *config)
{
    int ret = SMART_QOS_UBUS_RET_SUCC;

    if (config == NULL)
    {
        printf("smart_qos_apply_config: config is NULL\n");
        return SMART_QOS_UBUS_RET_NULLPTR;
    }

    /* Apply enable/disable action */
    ret = smart_qos_set_enable_action(config->enable);
    if (ret != SMART_QOS_UBUS_RET_SUCC)
    {
        printf("smart_qos_apply_config: Failed to apply enable action\n");
        return ret;
    }

    /* Apply mode action only if QoS is enabled */
    if (config->enable)
    {
        ret = smart_qos_set_mode_action(config->mode);
        if (ret != SMART_QOS_UBUS_RET_SUCC)
        {
            printf("smart_qos_apply_config: Failed to apply mode action\n");
            return ret;
        }
    }

    return SMART_QOS_UBUS_RET_SUCC;
}

int smart_qos_init(struct ubus_context *ctx, struct ubus_object *obj,
                   struct ubus_request_data *req, const char *method,
                   struct blob_attr *msg)
{
    smart_qos_config_t config;
    int ret;

    /* Read current UCI configuration */
    ret = smart_qos_read_uci_config(&config);
    if (ret != SMART_QOS_UBUS_RET_SUCC)
    {
        printf("smart_qos_init: Failed to read UCI config\n");
        app_reply_ubus_fail(ctx, req, "Failed to read UCI config", ret);
        return UBUS_STATUS_OK;
    }

    /* Apply the configuration */
    ret = smart_qos_apply_config(&config);
    if (ret != SMART_QOS_UBUS_RET_SUCC)
    {
        printf("smart_qos_init: Failed to apply config\n");
        app_reply_ubus_fail(ctx, req, "Failed to apply config", ret);
        return UBUS_STATUS_OK;
    }

    app_reply_ubus_succ(ctx, req);
    return UBUS_STATUS_OK;
}

static struct blobmsg_policy smart_qos_set[] = {
    [SMART_QOS_UBUS_ATTR0] = {.name = "enable", .type = BLOBMSG_TYPE_INT32},
    [SMART_QOS_UBUS_ATTR1] = {.name = "mode", .type = BLOBMSG_TYPE_INT32},
};

int smart_qos_set_handler(struct ubus_context *ctx,
                          struct ubus_object *obj,
                          struct ubus_request_data *req,
                          const char *method,
                          struct blob_attr *msg)
{
    struct blob_attr *attr[SMART_QOS_UBUS_ATTR_MAX];
    smart_qos_config_t config;
    int ret = SMART_QOS_UBUS_RET_SUCC;

    /* Parse ubus message */
    blobmsg_parse(smart_qos_set, ARRAY_SIZE(smart_qos_set), attr, blob_data(msg), blob_len(msg));

    /* Check required parameters */
    ret = app_check_ubus_params(ctx, req, attr, ARRAY_SIZE(smart_qos_set), smart_qos_set, ARRAY_SIZE(smart_qos_set));
    if (ret != SMART_QOS_UBUS_RET_SUCC)
    {
        return UBUS_STATUS_OK;
    }

    /* Read current configuration first */
    ret = smart_qos_read_uci_config(&config);
    if (ret != SMART_QOS_UBUS_RET_SUCC)
    {
        printf("smart_qos_set_handler: Failed to read current UCI config\n");
        app_reply_ubus_fail(ctx, req, "Failed to read current UCI config", ret);
        return UBUS_STATUS_OK;
    }

    /* Update configuration with new values */
    if (attr[SMART_QOS_UBUS_ATTR0])
    {
        config.enable = blobmsg_get_u32(attr[SMART_QOS_UBUS_ATTR0]);
    }

    if (attr[SMART_QOS_UBUS_ATTR1])
    {
        config.mode = blobmsg_get_u32(attr[SMART_QOS_UBUS_ATTR1]);
    }

    /* Validate parameters */
    if (config.enable != SMART_QOS_DISABLE && config.enable != SMART_QOS_ENABLE)
    {
        printf("smart_qos_set_handler: Invalid enable value %d\n", config.enable);
        app_reply_ubus_fail(ctx, req, "Invalid enable value", SMART_QOS_UBUS_RET_PARAM_INVALID);
        return UBUS_STATUS_OK;
    }

    if (config.mode < CUSTOM_PRIORITY_MODE || config.mode > SOCIAL_PRIORITY_MODE)
    {
        printf("smart_qos_set_handler: Invalid mode value %d\n", config.mode);
        app_reply_ubus_fail(ctx, req, "Invalid mode value", SMART_QOS_UBUS_RET_PARAM_INVALID);
        return UBUS_STATUS_OK;
    }

    /* Update UCI configuration */
    ret = smart_qos_update_uci_config(&config);
    if (ret != SMART_QOS_UBUS_RET_SUCC)
    {
        printf("smart_qos_set_handler: Failed to update UCI config\n");
        app_reply_ubus_fail(ctx, req, "Failed to update UCI config", ret);
        return UBUS_STATUS_OK;
    }

    /* Apply the new configuration */
    ret = smart_qos_apply_config(&config);
    if (ret != SMART_QOS_UBUS_RET_SUCC)
    {
        printf("smart_qos_set_handler: Failed to apply config\n");
        app_reply_ubus_fail(ctx, req, "Failed to apply config", ret);
        return UBUS_STATUS_OK;
    }

    app_reply_ubus_succ(ctx, req);
    return UBUS_STATUS_OK;
}

void app_reply_ubus_fail(struct ubus_context *ctx, struct ubus_request_data *req, const char *reason,
                         uint32_t error_code)
{
    struct blob_buf result_buff = {0};
    blob_buf_init(&result_buff, 0);
    blobmsg_add_u32(&result_buff, "result", error_code);
    blobmsg_add_string(&result_buff, "fail_reason", reason);
    ubus_send_reply(ctx, req, result_buff.head);
    if (result_buff.buf != NULL)
    {
        blob_buf_free(&result_buff);
    }
}

void app_reply_ubus_succ(struct ubus_context *ctx, struct ubus_request_data *req)
{
    struct blob_buf result_buff = {0};
    blob_buf_init(&result_buff, 0);
    blobmsg_add_u32(&result_buff, "result", 0);
    ubus_send_reply(ctx, req, result_buff.head);
    if (result_buff.buf != NULL)
    {
        blob_buf_free(&result_buff);
    }
}

int32_t app_check_ubus_params(struct ubus_context *ctx, struct ubus_request_data *req, struct blob_attr **attr,
                              uint32_t attr_len, const struct blobmsg_policy *policy, uint32_t policy_len)
{
    char fail_reason[128] = {0};
    for (uint32_t i = 0; i < attr_len; i++)
    {
        if (attr[i] == NULL)
        {
            if (snprintf(fail_reason, sizeof(fail_reason), "Parameter %s is NULL. Please try again.",
                         policy[i].name) < 0)
            {
                return SMART_QOS_UBUS_RET_PARAM_INVALID;
            }
            app_reply_ubus_fail(ctx, req, fail_reason, SMART_QOS_UBUS_RET_PARAM_INVALID);
            return SMART_QOS_UBUS_RET_PARAM_INVALID;
        }
    }
    return SMART_QOS_UBUS_RET_SUCC;
}

static const struct ubus_method smart_qos_methods[] = {
    UBUS_METHOD_NOARG("smart_qos_init", smart_qos_init),
    UBUS_METHOD("smart_qos_set", smart_qos_set_handler, smart_qos_set),
};

static struct ubus_object_type smart_qos_ubus_main_object_type =
    UBUS_OBJECT_TYPE("smart_qos", smart_qos_methods);

struct ubus_object app_urlfilter_ubus_object = {
    .name = "smart_qos",
    .type = &smart_qos_ubus_main_object_type,
    .methods = smart_qos_methods,
    .n_methods = ARRAY_SIZE(smart_qos_methods),
};

static void smart_qos_ubus_run(void)
{
    static struct ubus_context *app_ubus_ctx = NULL;

    uloop_init();
    app_ubus_ctx = ubus_connect(NULL);

    ubus_add_uloop(app_ubus_ctx);

    if (ubus_add_object(app_ubus_ctx, &app_urlfilter_ubus_object))
    {
        return;
    }

    uloop_run();

    if (app_ubus_ctx)
    {
        ubus_free(app_ubus_ctx);
    }
    uloop_done();
}

int main(int argc, char *argv[])
{
    int ret = 0;
    smart_qos_config_t config = {0};

    smart_qos_iptables_init();

    /* Read and apply initial configuration */
    ret = smart_qos_read_uci_config(&config);
    if (ret == SMART_QOS_UBUS_RET_SUCC)
    {
        ret = smart_qos_apply_config(&config);
        if (ret != SMART_QOS_UBUS_RET_SUCC)
        {
            printf("Failed to apply initial Smart QoS configuration\n");
        }
    }
    else
    {
        printf("Failed to read initial Smart QoS configuration\n");
    }

    /* Start ubus service */
    smart_qos_ubus_run();

    return 0;
}
