#include "main.h"

int os_execcmd(char *pc_command)
{
    int pid = 0;
    int status = 0;
    char *argv[] = {"sh", "-c", pc_command, NULL};

    if (pc_command == NULL)
    {
        return -1;
    }

    pid = fork();
    if (pid < 0)
    {
        return -1;
    }
    else if (pid == 0)
    {
        // 子进程执行分支
        execv("/bin/sh", argv);
        _exit(127);
    }

    /* wait for child process return */
    while (waitpid(pid, &status, 0) < 0)
    {
        if (errno != (unsigned int)EINTR)
        {
            return -1;
        }
    }

    return WIFEXITED(status) ? (0) : (-1);
}

int get_device_mode(void)
{
    int mode = 0;
    mode = vsuci_get_int32("device_mode_manager", "device_mode_manager", "mode");
    return mode;
}

int get_auto_wan_cfg_mode(void)
{
    int cfg_mode = 0;
    cfg_mode = vsuci_get_int32("auto_wan", "auto_wan", "cfg_mode");
    return cfg_mode;
}

int set_device_mode(int mode)
{
    int ret = 0;

    ret = vsuci_set_int32(mode, "device_mode_manager", "device_mode_manager", "mode");
    if (ret != 0)
    {
        printf("Error: Failed to set device_mode_manager mode\n");
        return -1;
    }

    return UCI_OK;
}

int set_dhcp_forward_rules(const char *target)
{
    int ret = 0;

    ret = vsuci_set("firewall", "lan_dhcp_forward_1", "target", target);
    if (ret != UCI_OK)
    {
        printf("Error: Failed to set lan_dhcp_forward_1\n");
        return -1;
    }

    ret = vsuci_set("firewall", "lan_dhcp_forward_2", "target", target);
    if (ret != UCI_OK)
    {
        printf("Error: Failed to set lan_dhcp_forward_2\n");
        return -1;
    }

    return UCI_OK;
}

int set_auto_wan_detect_en_uci(int detect_en)
{
    int ret = 0;

    ret = vsuci_set_int32(detect_en, "auto_wan", "auto_wan", "detect_en");
    if (ret != UCI_OK)
    {
        printf("Error: Failed to set auto_wan detect_en\n");
        return -1;
    }

    return UCI_OK;
}

void reload_firewall(void)
{
    printf("Restarting firewall service...\n");
    fflush(stdout);
    os_execcmd("/etc/init.d/firewall reload >/dev/null 2>&1");
}

void reload_auto_wan(void)
{
    printf("Restarting auto_wan service...\n");
    fflush(stdout);
    os_execcmd("/etc/init.d/auto_wan reload >/dev/null 2>&1");
}

int configure_router_mode(int status)
{
    int ret = 0;

    printf("Configuring router/gateway mode\n");

    set_dhcp_forward_rules("DROP");
    if (get_auto_wan_cfg_mode == 2)//only in AUTO_DETECT cfg
    {
        set_auto_wan_detect_en_uci(1);
    }

    // TODO: Add other router mode specific configurations here
    if (status == SYSTEM_STARTUP)
    {
        reload_firewall();
    }
    else if (status == SYSTEM_RUNNING)
    {
        reload_firewall();
        reload_auto_wan();
    }
    return 0;
}

int configure_wired_bridge_mode(int status)
{
    int ret = 0;

    printf("Configuring wired bridge mode\n");

    set_dhcp_forward_rules("ACCEPT");
    set_auto_wan_detect_en_uci(0);

    // TODO: Add other wired bridge mode specific configurations here
    if (status == SYSTEM_STARTUP)
    {
        reload_firewall();
    }
    else if (status == SYSTEM_RUNNING)
    {
        reload_firewall();
        reload_auto_wan();
    }
    return 0;
}

int configure_wifi_repeater_mode(int status)
{
    int ret = 0;

    printf("Configuring WiFi repeater mode\n");

    set_dhcp_forward_rules("ACCEPT");
    set_auto_wan_detect_en_uci(0);

    // TODO: Add other WiFi repeater mode specific configurations here
    if (status == SYSTEM_STARTUP)
    {
        reload_firewall();
    }
    else if (status == SYSTEM_RUNNING)
    {
        reload_firewall();
        reload_auto_wan();
    }
    return 0;
}

// ubus reply functions
void app_reply_ubus_fail(struct ubus_context *ctx, struct ubus_request_data *req, const char *reason,
                         uint32_t error_code)
{
    struct blob_buf result_buff = {0};
    blob_buf_init(&result_buff, 0);
    blobmsg_add_u32(&result_buff, "result", error_code);
    blobmsg_add_string(&result_buff, "fail_reason", reason);
    ubus_send_reply(ctx, req, result_buff.head);
    if (result_buff.buf != NULL)
    {
        blob_buf_free(&result_buff);
    }
}

void app_reply_ubus_succ(struct ubus_context *ctx, struct ubus_request_data *req)
{
    struct blob_buf result_buff = {0};
    blob_buf_init(&result_buff, 0);
    blobmsg_add_u32(&result_buff, "result", 0);
    ubus_send_reply(ctx, req, result_buff.head);
    if (result_buff.buf != NULL)
    {
        blob_buf_free(&result_buff);
    }
}

int32_t app_check_ubus_params(struct ubus_context *ctx, struct ubus_request_data *req, struct blob_attr **attr,
                              uint32_t attr_len, const struct blobmsg_policy *policy, uint32_t policy_len)
{
    char fail_reason[128] = {0};
    for (uint32_t i = 0; i < attr_len; i++)
    {
        if (attr[i] == NULL)
        {
            if (snprintf(fail_reason, sizeof(fail_reason), "Parameter %s is NULL. Please try again.",
                         policy[i].name) < 0)
            {
                return -1;
            }
            app_reply_ubus_fail(ctx, req, fail_reason, -1);
            return -1;
        }
    }
    return 0;
}

// Apply device mode configuration based on mode value
static int apply_device_mode(int status, int mode)
{
    int ret = 0;

    switch (mode)
    {
        case MODE_ROUTER:
            ret = configure_router_mode(status);
            break;
        case MODE_WIRED_BRIDGE:
            ret = configure_wired_bridge_mode(status);
            break;
        case MODE_WIFI_REPEATER:
            ret = configure_wifi_repeater_mode(status);
            break;
        default:
            return -1;
    }

    return ret;
}

static const struct blobmsg_policy work_mode_policy[] = {
    [DEVICE_MODE_MANAGER_UBUS_ATTR0] = { .name = "status", .type = BLOBMSG_TYPE_INT32 },
    [DEVICE_MODE_MANAGER_UBUS_ATTR1] = { .name = "mode", .type = BLOBMSG_TYPE_INT32 },
};

// ubus method handler for work_mode_set
int work_mode_set_handler(struct ubus_context *ctx, struct ubus_object *obj,
                         struct ubus_request_data *req, const char *method,
                         struct blob_attr *msg)
{
    struct blob_attr *attr[DEVICE_MODE_MANAGER_UBUS_ATTR_MAX];
    int ret = 0, status = 0, mode = 0;

    // Parse ubus message
    blobmsg_parse(work_mode_policy, ARRAY_SIZE(work_mode_policy), attr, blob_data(msg), blob_len(msg));

    // Check required parameters
    ret = app_check_ubus_params(ctx, req, attr, ARRAY_SIZE(work_mode_policy), work_mode_policy, ARRAY_SIZE(work_mode_policy));
    if (ret != 0)
    {
        return UBUS_STATUS_OK;
    }

    status = blobmsg_get_u32(attr[DEVICE_MODE_MANAGER_UBUS_ATTR0]);
    mode = blobmsg_get_u32(attr[DEVICE_MODE_MANAGER_UBUS_ATTR1]);

    printf("work_mode_set called with status=%d, mode=%d\n", status, mode);

    // Update UCI configuration with new mode (status is not saved to UCI)
    ret = set_device_mode(mode);
    if (ret != 0)
    {
        printf("Failed to update UCI configuration\n");
        app_reply_ubus_fail(ctx, req, "Failed to update UCI configuration", ret);
        return UBUS_STATUS_OK;
    }

    // Apply the device mode configuration
    ret = apply_device_mode(status, mode);
    if (ret != 0)
    {
        printf("Failed to apply device mode configuration\n");
        app_reply_ubus_fail(ctx, req, "Failed to apply device mode configuration", ret);
        return UBUS_STATUS_OK;
    }

    app_reply_ubus_succ(ctx, req);
    return UBUS_STATUS_OK;
}

// ubus methods definition
static const struct ubus_method device_mode_methods[] = {
    UBUS_METHOD("work_mode_set", work_mode_set_handler, work_mode_policy),
};

// ubus object type definition
static struct ubus_object_type device_mode_object_type =
    UBUS_OBJECT_TYPE("device_mode_manager", device_mode_methods);

// ubus object definition
struct ubus_object device_mode_ubus_object = {
    .name = "device_mode_manager",
    .type = &device_mode_object_type,
    .methods = device_mode_methods,
    .n_methods = ARRAY_SIZE(device_mode_methods),
};

static void device_mode_ubus_run(void)
{
    static struct ubus_context *app_ubus_ctx = NULL;

    uloop_init();
    app_ubus_ctx = ubus_connect(NULL);

    ubus_add_uloop(app_ubus_ctx);

    if (ubus_add_object(app_ubus_ctx, &device_mode_ubus_object))
    {
        return;
    }

    uloop_run();

    if (app_ubus_ctx)
    {
        ubus_free(app_ubus_ctx);
    }
    uloop_done();
}

int main(int argc, char *argv[])
{
    printf("Starting device_mode_manager ubus service\n");

    // Start ubus service
    device_mode_ubus_run();

    return 0;
}
