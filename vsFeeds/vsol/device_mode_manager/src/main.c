#include "main.h"

// Global variables for ubus service
static struct ubus_context *ctx;
static struct blob_buf b;

int os_execcmd(char *pc_command)
{
    int pid = 0;
    int status = 0;
    char *argv[] = {"sh", "-c", pc_command, NULL};

    if (pc_command == NULL)
    {
        return -1;
    }

    pid = fork();
    if (pid < 0)
    {
        return -1;
    }
    else if (pid == 0)
    {
        // 子进程执行分支
        execv("/bin/sh", argv);
        _exit(127);
    }

    /* wait for child process return */
    while (waitpid(pid, &status, 0) < 0)
    {
        if (errno != (unsigned int)EINTR)
        {
            return -1;
        }
    }

    return WIFEXITED(status) ? (0) : (-1);
}


int get_device_mode(void)
{
    int mode = 0;
    mode = vsuci_get_int32("device_mode_manager", "device_mode_manager", "mode");
    return mode;
}

int set_device_mode(int mode)
{
    int ret = 0;
    ret = vsuci_set_int32(mode, "device_mode_manager", "device_mode_manager", "mode");
    if (ret != 0)
    {
        syslog(LOG_ERR, "Failed to set device mode to UCI: %d", mode);
        return -1;
    }
    syslog(LOG_INFO, "Device mode updated to: %d", mode);
    return 0;
}

int set_dhcp_forward_rules(const char *target)
{
    int ret = 0;

    ret = vsuci_set("firewall", "lan_dhcp_forward_1", "target", target);
    if (ret != UCI_OK)
    {
        printf("Error: Failed to set lan_dhcp_forward_1\n");
        return -1;
    }

    ret = vsuci_set("firewall", "lan_dhcp_forward_2", "target", target);
    if (ret != UCI_OK)
    {
        printf("Error: Failed to set lan_dhcp_forward_2\n");
        return -1;
    }

    return UCI_OK;
}

void reload_firewall(void)
{
    printf("Restarting firewall service...\n");
    fflush(stdout);
    os_execcmd("/etc/init.d/firewall reload >/dev/null 2>&1");
}

int configure_router_mode(void)
{
    int ret = 0;

    printf("Configuring router/gateway mode (mode 0)\n");

    // Set DHCP forward rules to DROP
    ret = set_dhcp_forward_rules("DROP");
    if (ret == UCI_OK)
    {
        reload_firewall();
    }

    // TODO: Add other router mode specific configurations here
    return 0;
}

int configure_wired_bridge_mode(void)
{
    int ret = 0;

    printf("Configuring wired bridge mode (mode 1)\n");

    // Set DHCP forward rules to ACCEPT
    ret = set_dhcp_forward_rules("ACCEPT");
    if (ret == UCI_OK)
    {
        reload_firewall();
    }

    // TODO: Add other wired bridge mode specific configurations here
    return 0;
}

int configure_wifi_repeater_mode(void)
{
    int ret = 0;

    printf("Configuring WiFi repeater mode (mode 2)\n");

    // Set DHCP forward rules to ACCEPT
    ret = set_dhcp_forward_rules("ACCEPT");
    if (ret == UCI_OK)
    {
        reload_firewall();
    }

    // TODO: Add other WiFi repeater mode specific configurations here
    return 0;
}

// Apply device mode configuration based on mode value
static int apply_device_mode(int mode)
{
    int ret = 0;

    switch (mode)
    {
        case MODE_ROUTER:
            ret = configure_router_mode();
            break;
        case MODE_WIRED_BRIDGE:
            ret = configure_wired_bridge_mode();
            break;
        case MODE_WIFI_REPEATER:
            ret = configure_wifi_repeater_mode();
            break;
        default:
            syslog(LOG_ERR, "Invalid device mode: %d", mode);
            return -1;
    }

    return ret;
}

static const struct blobmsg_policy work_mode_policy[__WORK_MODE_MAX] = {
    [DEVICE_MODE_MANAGER_UBUS_ATTR0] = { .name = "status", .type = BLOBMSG_TYPE_INT32 },
    [DEVICE_MODE_MANAGER_UBUS_ATTR1] = { .name = "mode", .type = BLOBMSG_TYPE_INT32 },
};

// ubus method handler for work_mode_set
static int work_mode_set_handler(struct ubus_context *ctx, struct ubus_object *obj,
                                struct ubus_request_data *req, const char *method,
                                struct blob_attr *msg)
{
    struct blob_attr *tb[__WORK_MODE_MAX];
    int ret = 0, status = 0, mode = 0;

    blobmsg_parse(work_mode_policy, __WORK_MODE_MAX, tb, blob_data(msg), blob_len(msg));

    if (!tb[WORK_MODE_STATUS] || !tb[WORK_MODE_MODE])
    {
        syslog(LOG_ERR, "Missing required parameters: status and mode");
        return UBUS_STATUS_INVALID_ARGUMENT;
    }

    status = blobmsg_get_u32(tb[DEVICE_MODE_MANAGER_UBUS_ATTR0]);
    mode = blobmsg_get_u32(tb[DEVICE_MODE_MANAGER_UBUS_ATTR1]);

    syslog(LOG_INFO, "work_mode_set called with status=%d, mode=%d", status, mode);

    // Update UCI configuration with new mode (status is not saved to UCI)
    ret = set_device_mode(mode);
    if (ret != 0)
    {
        syslog(LOG_ERR, "Failed to update UCI configuration");
        return UBUS_STATUS_UNKNOWN_ERROR;
    }

    // Apply the device mode configuration
    ret = apply_device_mode(mode);
    if (ret != 0)
    {
        syslog(LOG_ERR, "Failed to apply device mode configuration");
        return UBUS_STATUS_UNKNOWN_ERROR;
    }

    ubus_send_reply(ctx, req, b.head);

    return UBUS_STATUS_OK;
}

// ubus methods definition
static const struct ubus_method device_mode_methods[] = {
    UBUS_METHOD("work_mode_set", work_mode_set_handler, work_mode_policy),
};

// ubus object type definition
static struct ubus_object_type device_mode_object_type =
    UBUS_OBJECT_TYPE("device_mode_manager", device_mode_methods);

// ubus object definition
static struct ubus_object device_mode_object = {
    .name = "device_mode_manager",
    .type = &device_mode_object_type,
    .methods = device_mode_methods,
    .n_methods = ARRAY_SIZE(device_mode_methods),
};

// Signal handler for graceful shutdown
static void signal_handler(int sig)
{
    syslog(LOG_INFO, "Received signal %d, shutting down", sig);
    uloop_end();
}

// Initialize ubus service
static int init_ubus_service(void)
{
    int ret;

    ctx = ubus_connect(NULL);
    if (!ctx)
    {
        syslog(LOG_ERR, "Failed to connect to ubus");
        return -1;
    }

    ubus_add_uloop(ctx);

    ret = ubus_add_object(ctx, &device_mode_object);
    if (ret)
    {
        syslog(LOG_ERR, "Failed to add ubus object: %s", ubus_strerror(ret));
        ubus_free(ctx);
        return -1;
    }

    syslog(LOG_INFO, "device_mode_manager ubus service started");
    return 0;
}

int main(int argc, char *argv[])
{
    int ret;

    // Initialize syslog
    openlog("device_mode_manager", LOG_PID | LOG_CONS, LOG_DAEMON);
    syslog(LOG_INFO, "Starting device_mode_manager ubus service");

    // Set up signal handlers for graceful shutdown
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // Initialize uloop
    uloop_init();

    // Initialize ubus service
    ret = init_ubus_service();
    if (ret != 0)
    {
        syslog(LOG_ERR, "Failed to initialize ubus service");
        closelog();
        return -1;
    }

    // Run the main event loop
    uloop_run();

    // Cleanup
    ubus_free(ctx);
    uloop_done();
    closelog();

    syslog(LOG_INFO, "device_mode_manager service stopped");
    return 0;
}
