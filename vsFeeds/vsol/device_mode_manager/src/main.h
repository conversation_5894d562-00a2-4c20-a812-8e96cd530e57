#ifndef __DEVICE_MODE_MAIN_H__
#define __DEVICE_MODE_MAIN_H__

#include <libubox/blobmsg_json.h>
#include <libubus.h>
#include <libubox/uloop.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <unistd.h>
#include <sys/wait.h>
#include <uci.h>
#include "vsuci.h"

#define ARRAY_SIZE(x) (sizeof(x) / sizeof((x)[0]))

// Device work modes
typedef enum
{
    MODE_ROUTER = 0,       // Router/Gateway mode
    MODE_WIRED_BRIDGE = 1, // Wired bridge mode
    MODE_WIFI_REPEATER = 2 // WiFi repeater mode
} device_mode_t;

enum device_mode_manager_ubus_e{
    DEVICE_MODE_MANAGER_UBUS_ATTR0 = 0,
    DEVICE_MODE_MANAGER_UBUS_ATTR1,
    DEVICE_MODE_MANAGER_UBUS_ATTR2,
    DEVICE_MODE_MANAGER_UBUS_ATTR3,
    DEVICE_MODE_MANAGER_UBUS_ATTR4,
    DEVICE_MODE_MANAGER_UBUS_ATTR5,
    DEVICE_MODE_MANAGER_UBUS_ATTR_MAX,
};

enum running_status_e{
    SYSTEM_STARTUP = 0,     //in the early stages of system startup
    SYSTEM_RUNNING = 1,     //in the stages of system running
};

int os_execcmd(char *pc_command);
int get_device_mode(void);
int set_device_mode(int mode);
int set_dhcp_forward_rules(const char *target);
void reload_firewall(void);
int configure_router_mode(int status);
int configure_wired_bridge_mode(int status);
int configure_wifi_repeater_mode(int status);

// ubus reply functions
void app_reply_ubus_fail(struct ubus_context *ctx, struct ubus_request_data *req, const char *reason, uint32_t error_code);
void app_reply_ubus_succ(struct ubus_context *ctx, struct ubus_request_data *req);
int32_t app_check_ubus_params(struct ubus_context *ctx, struct ubus_request_data *req, struct blob_attr **attr,
                              uint32_t attr_len, const struct blobmsg_policy *policy, uint32_t policy_len);

// ubus handler
int work_mode_set_handler(struct ubus_context *ctx, struct ubus_object *obj,
                         struct ubus_request_data *req, const char *method,
                         struct blob_attr *msg);

#endif
