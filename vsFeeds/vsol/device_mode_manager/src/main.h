#ifndef __DEVICE_MODE_MAIN_H__
#define __DEVICE_MODE_MAIN_H__

#include <libubox/blobmsg_json.h>
#include <libubus.h>
#include <libubox/uloop.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <syslog.h>
#include <errno.h>
#include <signal.h>
#include <unistd.h>
#include <sys/wait.h>
#include <uci.h>
#include "vsuci.h"

#define ARRAY_SIZE(x) (sizeof(x) / sizeof((x)[0]))

// Device work modes
typedef enum
{
    MODE_ROUTER = 0,       // Router/Gateway mode
    MODE_WIRED_BRIDGE = 1, // Wired bridge mode
    MODE_WIFI_REPEATER = 2 // WiFi repeater mode
} device_mode_t;

enum device_mode_manager_ubus_e{
    DEVICE_MODE_MANAGER_UBUS_ATTR0 = 0,
    DEVICE_MODE_MANAGER_UBUS_ATTR1,
    DEVICE_MODE_MANAGER_UBUS_ATTR2,
    DEVICE_MODE_MANAGER_UBUS_ATTR3,
    DEVICE_MODE_MANAGER_UBUS_ATTR4,
    DEVICE_MODE_MANAGER_UBUS_ATTR5,
    DEVICE_MODE_MANAGER_UBUS_ATTR_MAX,
};

int os_execcmd(char *pc_command);
int get_device_mode(void);
int set_device_mode(int mode);
int set_dhcp_forward_rules(const char *target);
void reload_firewall(void);
int configure_router_mode(void);
int configure_wired_bridge_mode(void);
int configure_wifi_repeater_mode(void);

#endif
