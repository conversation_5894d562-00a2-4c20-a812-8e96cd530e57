#!/bin/sh

# Test script for device_mode_manager ubus interface

echo "Testing device_mode_manager ubus interface..."

# Check if ubus service is running
echo "1. Checking if device_mode_manager service is available..."
ubus list | grep device_mode_manager
if [ $? -eq 0 ]; then
    echo "✓ device_mode_manager service is available"
else
    echo "✗ device_mode_manager service is not available"
    echo "Please start the service first: /etc/init.d/device_mode_manager start"
    exit 1
fi

# Test work_mode_set method with different parameters
echo ""
echo "2. Testing work_mode_set method..."

# Test case 1: Router mode (mode 0) with status 0 (boot time)
echo "Test case 1: Setting router mode (mode=0) at boot time (status=0)"
ubus call device_mode_manager work_mode_set '{"status":0, "mode":0}'
if [ $? -eq 0 ]; then
    echo "✓ Router mode set successfully"
else
    echo "✗ Failed to set router mode"
fi

sleep 2

# Test case 2: Wired bridge mode (mode 1) with status 1 (system ready)
echo ""
echo "Test case 2: Setting wired bridge mode (mode=1) when system ready (status=1)"
ubus call device_mode_manager work_mode_set '{"status":1, "mode":1}'
if [ $? -eq 0 ]; then
    echo "✓ Wired bridge mode set successfully"
else
    echo "✗ Failed to set wired bridge mode"
fi

sleep 2

# Test case 3: WiFi repeater mode (mode 2) with status 1 (system ready)
echo ""
echo "Test case 3: Setting WiFi repeater mode (mode=2) when system ready (status=1)"
ubus call device_mode_manager work_mode_set '{"status":1, "mode":2}'
if [ $? -eq 0 ]; then
    echo "✓ WiFi repeater mode set successfully"
else
    echo "✗ Failed to set WiFi repeater mode"
fi

sleep 2

# Test case 4: Invalid mode (should fail)
echo ""
echo "Test case 4: Testing invalid mode (mode=5) - should fail"
ubus call device_mode_manager work_mode_set '{"status":1, "mode":5}'
if [ $? -ne 0 ]; then
    echo "✓ Invalid mode correctly rejected"
else
    echo "✗ Invalid mode was accepted (this is wrong)"
fi

# Check UCI configuration
echo ""
echo "3. Checking UCI configuration..."
current_mode=$(uci get device_mode_manager.device_mode_manager.mode 2>/dev/null)
if [ -n "$current_mode" ]; then
    echo "✓ Current mode in UCI: $current_mode"
else
    echo "✗ Failed to read mode from UCI"
fi

echo ""
echo "Test completed!"
