#!/bin/sh /etc/rc.common
# Device Mode Manager Service
# Provides ubus interface for device work mode management

START=21

USE_PROCD=1
NAME="device_mode_manager"
PROG="/usr/bin/device_mode_manager"

start_service() {
	procd_open_instance
	procd_set_param command "$PROG"
	procd_set_param respawn 3600 5 5
	procd_set_param stderr 1
	procd_set_param stdout 1
	procd_set_param pidfile /var/run/device_mode_manager.pid
	procd_close_instance
}

reload_service() {
    stop
    start
}

service_triggers() {
    procd_add_reload_trigger device_mode_manager
    procd_open_validate
    procd_close_validate
}

