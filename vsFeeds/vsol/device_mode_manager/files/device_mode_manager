#!/bin/sh /etc/rc.common
# Device Mode Manager Service
# Provides ubus interface for device work mode management

START=80

USE_PROCD=1
NAME="device_mode_manager"
PROG="/usr/bin/device_mode_manager"

start_service() {
	local status=0
	local mode

	config_load device_mode_manager
	config_get mode device_mode_manager mode

	procd_open_instance
	procd_set_param command "$PROG"
	procd_set_param respawn
	procd_set_param stderr 1
	procd_set_param stdout 1
	procd_close_instance

	sleep 5

	if [ -n "$mode" ]; then
		# 确保mode是有效数字
		mode=$(echo "$mode" | awk '{print int($1)}')
		if [ -n "$mode" ]; then
			ubus call device_mode_manager work_mode_set "{\"status\": ${status}, \"mode\": ${mode}}"
		fi
	fi
}

reload_service() {
    stop
    start
}

service_triggers() {
    procd_add_reload_trigger device_mode_manager
}

