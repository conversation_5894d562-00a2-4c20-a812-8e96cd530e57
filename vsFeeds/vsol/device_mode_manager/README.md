# Device Mode Manager

Device Mode Manager is a ubus service that manages device work modes and configures firewall rules accordingly.

## Features

- Supports three device work modes:
  - Mode 0: Router/Gateway mode
  - Mode 1: Wired bridge mode  
  - Mode 2: WiFi repeater mode
- Provides ubus interface for mode switching
- Automatically updates UCI configuration
- Configures firewall rules based on selected mode

## Installation

1. Build and install the package:
```bash
make package/vsol/device_mode_manager/compile
make package/vsol/device_mode_manager/install
```

2. Start the service:
```bash
/etc/init.d/device_mode_manager start
/etc/init.d/device_mode_manager enable
```

## Usage

### ubus Interface

The service provides a ubus interface with the following method:

#### work_mode_set

Sets the device work mode and applies corresponding configuration.

**Parameters:**
- `status` (integer): Call timing indicator
  - `0`: Called during boot time
  - `1`: Called when system is fully ready
- `mode` (integer): Device work mode
  - `0`: Router/Gateway mode
  - `1`: Wired bridge mode
  - `2`: WiFi repeater mode

**Examples:**

```bash
# Set router mode during boot
ubus call device_mode_manager work_mode_set '{"status":0, "mode":0}'

# Set wired bridge mode when system is ready
ubus call device_mode_manager work_mode_set '{"status":1, "mode":1}'

# Set WiFi repeater mode when system is ready
ubus call device_mode_manager work_mode_set '{"status":1, "mode":2}'
```

**Response:**
```json
{
    "status": 0,
    "message": "Device mode set successfully"
}
```

### UCI Configuration

The service reads and updates the UCI configuration at:
- Package: `device_mode_manager`
- Section: `device_mode_manager`
- Option: `mode`

You can also check the current mode using UCI:
```bash
uci get device_mode_manager.device_mode_manager.mode
```

### Service Management

```bash
# Start service
/etc/init.d/device_mode_manager start

# Stop service
/etc/init.d/device_mode_manager stop

# Restart service
/etc/init.d/device_mode_manager restart

# Check service status
/etc/init.d/device_mode_manager status
```

### Testing

Run the test script to verify functionality:
```bash
chmod +x /path/to/test_ubus.sh
./test_ubus.sh
```

## Mode Behaviors

### Router Mode (0)
- Sets DHCP forward rules to DROP
- Reloads firewall configuration
- Device acts as a router/gateway

### Wired Bridge Mode (1)
- Sets DHCP forward rules to ACCEPT
- Reloads firewall configuration
- Device acts as a wired bridge

### WiFi Repeater Mode (2)
- Sets DHCP forward rules to ACCEPT
- Reloads firewall configuration
- Device acts as a WiFi repeater

## Logging

The service logs to syslog with facility `LOG_DAEMON`. You can view logs using:
```bash
logread | grep device_mode_manager
```

## Troubleshooting

1. **Service not starting:**
   - Check if all dependencies are installed
   - Verify UCI configuration file exists
   - Check system logs for error messages

2. **ubus calls failing:**
   - Ensure service is running: `ubus list | grep device_mode_manager`
   - Check parameter format and values
   - Verify ubus daemon is running

3. **Mode changes not taking effect:**
   - Check firewall service status
   - Verify UCI configuration was updated
   - Check system logs for configuration errors
