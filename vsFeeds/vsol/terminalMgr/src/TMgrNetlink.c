#include <string.h>
#include <unistd.h>
#include <linux/netlink.h>
#include <sys/select.h>
#include <sys/socket.h>
#include <linux/rtnetlink.h>
#include <linux/if_addr.h>
#include <pthread.h>
#include <net/if.h>
#include <errno.h>
#include <stdlib.h>

#include <TMgrNetlink.h>
#include <TMgrInfo.h>

#include <ifaddrs.h>   
#include <linux/if_packet.h>    

/* log调试 */
static void tmgr_netlink_log_to_file(const char *fmt, ...)
{
    if (access("/tmp/tmgr_netlink.log", F_OK) != 0)
    {
        return;
    }

    FILE *fp = fopen("/tmp/tmgr_netlink.log", "a");
    if (!fp)
    {
        return;
    }

    /* 打印时间戳 */
    time_t now = time(NULL);
    struct tm *tm_now = localtime(&now);
    if (tm_now)
    {
        fprintf(fp, "%04d-%02d-%02d %02d:%02d:%02d ",
                tm_now->tm_year + 1900, tm_now->tm_mon + 1, tm_now->tm_mday,
                tm_now->tm_hour, tm_now->tm_min, tm_now->tm_sec);
    }

    va_list args;
    va_start(args, fmt);
    vfprintf(fp, fmt, args);
    va_end(args);

    fputc('\n', fp);
    fclose(fp);
}

// 全局缓存端口数量，避免重复查询
static int cached_ge_num = -1;
static int cached_fe_num = -1;
static int cached_total_ports = -1;

// 获取并缓存端口数量
static void update_port_cache(void)
{
    if (cached_total_ports >= 0) return; // 已缓存，直接返回
    
    char buffer[16] = {0};
    
    // 获取GE端口数量
    FILE *fp = popen("hi_cfm get board.ge_num", "r");
    if (fp)
    {
        if (fgets(buffer, sizeof(buffer), fp))
        {
            cached_ge_num = atoi(buffer);
        }
        pclose(fp);
    }
    
    // 获取FE端口数量
    memset(buffer, 0, sizeof(buffer));
    fp = popen("hi_cfm get board.fe_num", "r");
    if (fp)
    {
        if (fgets(buffer, sizeof(buffer), fp))
        {
            cached_fe_num = atoi(buffer);
        }
        pclose(fp);
    }
    
    cached_total_ports = cached_ge_num + cached_fe_num;
    tmgr_netlink_log_to_file("Cached ports: GE=%d FE=%d Total=%d", cached_ge_num, cached_fe_num, cached_total_ports);
}

// 统一的接口类型判断函数，只调用一次if_indextoname
int TMgrNetlinkGetInterfaceType(int index, int *is_wan, int *is_brlan, int *is_wired)
{
    char ifname[16] = {0};
    
    // 只调用一次if_indextoname
    if (!if_indextoname(index, ifname))
    {
        *is_wan = 0;
        *is_brlan = 0;
        *is_wired = 0;
        return 0;
    }
    
    tmgr_netlink_log_to_file("\n[TMgrNetlinkGetInterfaceType] Interface: %s", ifname);
    
    // 检查是否为WAN接口
    *is_wan = TMgrIsWanInterface(ifname) ? 1 : 0;
    
    // 检查是否为br-lan接口
    *is_brlan = (strcmp(ifname, "br-lan") == 0) ? 1 : 0;
    
    // 检查是否为有线接口
    *is_wired = 0;
    if (!*is_wan && !*is_brlan) // 只对非WAN非br-lan接口检查有线
    {
        update_port_cache();
        
        // 检查是否为eth0到eth(total_ports-1)中的任意一个
        for (int i = 0; i < cached_total_ports; i++)
        {
            if (strncmp(ifname, "eth", 3) == 0)
            {
                int port_num = atoi(ifname + 3);
                if (port_num == i)
                {
                    *is_wired = 1;
                    break;
                }
            }
        }
    }
    
    return 1;
}

// 保持原有接口兼容性
int TMgrNetlinkIsIfWan(int index)
{
    int is_wan, is_brlan, is_wired;
    TMgrNetlinkGetInterfaceType(index, &is_wan, &is_brlan, &is_wired);
    return is_wan;
}

int TMgrNetlinkIsIfBrLan(int index)
{
    // int is_wan, is_brlan, is_wired;
    // TMgrNetlinkGetInterfaceType(index, &is_wan, &is_brlan, &is_wired);
    // return is_brlan;

    char ifname[16] = {0};
    
    // 只调用一次if_indextoname
    if (!if_indextoname(index, ifname))
    {
        return 0;
    }
    
    return (strcmp(ifname, "br-lan") == 0) ? 1 : 0;
}

int TMgrNetlinkIsIfWired(int index)
{
    int is_wan, is_brlan, is_wired;
    TMgrNetlinkGetInterfaceType(index, &is_wan, &is_brlan, &is_wired);
    return is_wired;
}

static void TMgrNetlinkParseNdMsg(struct rtattr **tb, struct ndmsg *attr, int len)
{
    memset(tb, 0, sizeof(struct rtattr *) * (NDA_MAX + 1));
    struct rtattr *a_;
    a_ = ND_DATA(attr);
    while (RTA_OK(a_, len))
    {
        if (a_->rta_type <= NDA_MAX)
        {
            tb[a_->rta_type] = a_;
        }
        a_ = RTA_NEXT(a_, len);
    }
}

static void TMgrNetlink2IpStr(struct rtattr *dst, char *res)
{
	unsigned char *to = (unsigned char *)dst;
	sprintf(res, "%d.%d.%d.%d", to[0], to[1], to[2], to[3]);
}

static void TMgrNetlinkMac2Str(const unsigned char *addr, char *res, int addrLen)
{
	int idx = 0;

	for (idx = 0; idx < 6; idx++)
	{
		if (idx < 5)
		{
			snprintf(res, addrLen, "%02x:", addr[idx]);
		}
		else
		{
			snprintf(res, addrLen, "%02x", addr[idx]);
		}
		res += 3;
	}
}

static void TMgrNetlinkGetIfconfigWirelessMac(unsigned char (**mac_ptr)[MAC_ADDR_LEN], unsigned short *mac_num)
{
    struct ifaddrs *ifaddr = NULL;
    struct ifaddrs *ifa = NULL;
    static unsigned char wireless_mac_addr[8][MAC_ADDR_LEN] = {0};
    static unsigned short wireless_index = 0;
    unsigned char *mac = NULL;
    int i = 0;

    if (wireless_index >= 8)
    {
        *mac_ptr = wireless_mac_addr;
        *mac_num = wireless_index;
        return;
    }

    if (-1 == getifaddrs(&ifaddr))
    {
        WCPRT("[%s] Func getifaddrs Failed\n", __FUNCTION__);
        return;
    }

    for (ifa = ifaddr; ifa != NULL; ifa = ifa->ifa_next)
    {
        if (NULL == ifa->ifa_addr || AF_PACKET != ifa->ifa_addr->sa_family)
        {
            continue;
        }

        struct sockaddr_ll *s = (struct sockaddr_ll *)ifa->ifa_addr;
        mac = (unsigned char *)s->sll_addr;

        if (strstr(ifa->ifa_name, WLAN_IFNAME_PREX))
        {
            int exist = 0;

            for (i = 0; i < 8; i++)
            {
                if (0 == memcmp(wireless_mac_addr[i], mac, MAC_ADDR_LEN))
                {
                    exist = 1;
                    break;
                }
            }

            if (0 == exist && wireless_index < 8)
            {
                memcpy(wireless_mac_addr[wireless_index], mac, MAC_ADDR_LEN);
                wireless_index++;
            }
        }
    }

    *mac_ptr = wireless_mac_addr;
    *mac_num = wireless_index;
    freeifaddrs(ifaddr);
}

static int TMgrNetlinkCompareIfconfigWirelessMac(unsigned char *mac)
{
    unsigned char(*wl_mac_ptr)[MAC_ADDR_LEN] = NULL;
    unsigned short mac_num = 0;
    int i = 0;

    TMgrNetlinkGetIfconfigWirelessMac(&wl_mac_ptr, &mac_num);

    for (i = 0; i < mac_num; i++)
    {
        if (0 == memcmp(wl_mac_ptr[i], mac, MAC_ADDR_LEN))
        {
            return 1;
        }
    }

    return 0;
}

static void TMgrNetlinkNewNeighHandle(struct rtattr **tbs, NeighborState ndm_state,int addr_famliy,int index)
{
    char macAddr[64] = {0};
    char ipAddr[64] = {0};
    char ifname[32 + 1] = {0};

    // Check whether the MAC is existed
    if (tbs[NDA_LLADDR] && RTA_PAYLOAD(tbs[NDA_LLADDR]) == 6)
    {
        TMgrNetlinkMac2Str(RTA_DATA(tbs[NDA_LLADDR]), macAddr, sizeof(macAddr));
        // WCPRT("new mac : %s  %d", macAddr, ndm_state);
        tmgr_netlink_log_to_file("new mac : %s  %d", macAddr, ndm_state);
        if_indextoname(index, ifname);

        if (strstr(ifname, WLAN_IFNAME_PREX) && addr_famliy == AF_BRIDGE)
        {
            if (0 == TMgrNetlinkCompareIfconfigWirelessMac(RTA_DATA(tbs[NDA_LLADDR])))
            {
                WCPRT("Update wireless device\n");
                TMgrInfoUpdateWirelessHostNode(RTA_DATA(tbs[NDA_LLADDR]), ifname);
            }
        }        
    }
    else
    {
        WCPRT("no lladdr info or mac is error");
        return;
    }

    // Check whether the IPv4 IP is existed
    if (tbs[NDA_DST] && RTA_PAYLOAD(tbs[NDA_DST]) != 16)
    {
        TMgrNetlink2IpStr(RTA_DATA(tbs[NDA_DST]), ipAddr);
        WCPRT("new ip : %s\n", ipAddr);
        tmgr_netlink_log_to_file("new ip : %s, NDM_STALE is %d", ipAddr, NDM_STALE);
        if (ndm_state == NDM_STALE)
        {
            unsigned char *ip_ptr = RTA_DATA(tbs[NDA_DST]);
            unsigned int ip = (ip_ptr[3] << 24) | (ip_ptr[2] << 16) | (ip_ptr[1] << 8) | ip_ptr[0];
            WCPRT("Update wireled device\n");
            TMgrInfoUpdateAllHostNode(RTA_DATA(tbs[NDA_LLADDR]), ip);
        }
    }
    else
    {
        //WCPRT("no ip info or it's an IPv6 address");
    }
}

static void TMgrNetlinkDelNeighHandle(struct rtattr **tbs, int ndm_state)
{
    if (tbs[NDA_DST] == NULL)
    {
        tmgr_netlink_log_to_file("tbs[NDA_DST] is NULL");
        if (tbs[NDA_LLADDR] == NULL)
        {
            tmgr_netlink_log_to_file("tbs[NDA_LLADDR] is NULL");
            WCPRT("[%s][%d] no lladdr info", __FUNCTION__, __LINE__);
        }
        else
        {
            if (RTA_PAYLOAD(tbs[NDA_LLADDR]) != 6)
            {
                tmgr_netlink_log_to_file("tbs[NDA_LLADDR] is not 6");
                WCPRT("[%s][%d] mac is error", __FUNCTION__, __LINE__);
                return;
            }
            char macAddr[64] = {0};
            TMgrNetlinkMac2Str(RTA_DATA(tbs[NDA_LLADDR]), macAddr, sizeof(macAddr));
            tmgr_netlink_log_to_file("[TMgrNetlinkDelNeighHandle] set offline mac : %s", macAddr);
            TMgrInfoSetHostOffline(RTA_DATA(tbs[NDA_LLADDR]));
        }
        return;
    }

}

static void TMgrNetlinkProcessNeigh(char *nlBuf, int nlLen)
{
    struct nlmsghdr *nh;
    struct rtattr *tbs[NDA_MAX + 1];


    for (nh = (struct nlmsghdr *)nlBuf; NLMSG_OK(nh, nlLen); NLMSG_NEXT(nh, nlLen))
    {
        int nLen = nh->nlmsg_len - NLMSG_SPACE(sizeof(struct ndmsg));
        TMgrNetlinkParseNdMsg(tbs, NLMSG_DATA(nh), nLen);

        // 使用统一接口判断，只调用一次if_indextoname
        int is_wan, is_brlan, is_wired;
        if (TMgrNetlinkGetInterfaceType(((struct ndmsg *)NLMSG_DATA(nh))->ndm_ifindex, &is_wan, &is_brlan, &is_wired))
        {
            // 只处理非WAN接口且是br-lan或有线接口的neigh事件
            if (!is_wan && (is_brlan || is_wired))
            {
                // WCPRT("the current nlmsg_type is %d ndm_state is %d", nh->nlmsg_type,((struct ndmsg *)NLMSG_DATA(nh))->ndm_state);
                tmgr_netlink_log_to_file("the current nlmsg_type is %d ndm_state is %d", nh->nlmsg_type,((struct ndmsg *)NLMSG_DATA(nh))->ndm_state);
                switch (nh->nlmsg_type)
                {
                    case RTM_NEWNEIGH:
                    {
                        tmgr_netlink_log_to_file("This is RTM_NEWNEIGH[%d]", nh->nlmsg_type);
                        TMgrNetlinkNewNeighHandle(tbs, ((struct ndmsg *)NLMSG_DATA(nh))->ndm_state, ((struct ndmsg *)NLMSG_DATA(nh))->ndm_family, ((struct ndmsg *)NLMSG_DATA(nh))->ndm_ifindex);
                        break;
                    }

                    case RTM_DELNEIGH:
                    {
                        tmgr_netlink_log_to_file("This is RTM_DELNEIGH[%d]", nh->nlmsg_type);
                        TMgrNetlinkDelNeighHandle(tbs, ((struct ndmsg *)NLMSG_DATA(nh))->ndm_state);
                        break;
                    }
                }
            }
        }
    }

}

static int TMgrNetlinkCreatSocket() {
    struct sockaddr_nl sa;
    int sk = socket(AF_NETLINK, SOCK_RAW, NETLINK_ROUTE);
    if (sk < 0) {
        WCPRT("error in socket");
        return -1;
    }

    memset(&sa, 0, sizeof(sa));
    sa.nl_family = AF_NETLINK;
    sa.nl_groups = RTM_NEWNEIGH | RTM_DELNEIGH;

    if (bind(sk, (struct sockaddr *)&sa, sizeof(sa)) != 0) {
        WCPRT("error in bind");
        close(sk);
        return -1;
    }

    return sk;
}

void *TMgrNetlinkArpListener(void *args)
{
    char buf[1024];
    int sk = TMgrNetlinkCreatSocket();
    fd_set rd_set;
    struct timeval timeout;

    while (1)
    { 
        FD_ZERO(&rd_set);
        FD_SET(sk, &rd_set);

        timeout.tv_sec = 5;
        timeout.tv_usec = 0;

        int ret = select(sk + 1, &rd_set, NULL, NULL, &timeout);

        if (ret == -1)
        {
            if (errno == EINTR)
            {
                WCPRT("select interrupted by a signal, retrying...\n");
                continue; 
            }
            else
            {
                WCPRT("select failed");
                break;
            }
        }
        else if (ret == 0)
        {
            //WCPRT("select timeout, no message received\n");
            continue;
        }

        if (FD_ISSET(sk, &rd_set))
        {
            memset(buf, 0, sizeof(buf));
            int rlen = recv(sk, buf, sizeof(buf), 0);

            if (rlen > 0)
            {
                TMgrNetlinkProcessNeigh(buf, rlen);
            }
            else if (rlen == -1)
            {
                WCPRT("read error");
                break;
            }
            else
            {
                WCPRT("Connection closed\n");
                break;
            }
        }
    }

    close(sk);          
    pthread_exit(NULL);
}
