#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <stdint.h>
#include <sys/time.h>

#include <TMgrMain.h>

static pthread_mutex_t tmgrInfoMutex = PTHREAD_MUTEX_INITIALIZER;

#if defined(__GNUC__)
#define UNUSED_FUNC __attribute__((unused))
#else
#define UNUSED_FUNC
#endif

/**
 *  add by wzx, 2025-09-25
 * 从DHCP lease文件中获取指定MAC地址对应的hostname
 * @param mac: MAC地址（6字节数组）
 * @param hostname: 输出缓冲区，用于存储hostname
 * @param max_len: hostname缓冲区的最大长度
 * @return: 成功返回0，失败返回-1
 */
static int get_hostname_from_dhcp(const unsigned char *mac, char *hostname, int max_len)
{
    FILE *fp;
    char line[256];
    char mac_str[18];
    int found = 0;
    
    if (mac == NULL || hostname == NULL || max_len <= 0) {
        return -1;
    }
    
    // 将MAC地址转换为字符串格式（小写）
    snprintf(mac_str, sizeof(mac_str), "%02x:%02x:%02x:%02x:%02x:%02x",
             mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
    // 直接读取 /tmp/dhcp.leases 文件
    fp = fopen("/tmp/dhcp.leases", "r");
    if (fp == NULL) {
        return -1;
    }
    
    while (fgets(line, sizeof(line), fp)) {
        unsigned long lease_time;
        char client_mac[18];
        char client_ip[16];
        char client_hostname[64];
        char client_id[64];
        
        // 解析lease文件格式：timestamp mac ip hostname client_id
        // 跳过注释行
        if (line[0] == '#' || line[0] == '\n') {
            continue;
        }
        
        // 初始化hostname字段
        client_hostname[0] = '\0';
        client_id[0] = '\0';
        
        int parsed_fields = sscanf(line, "%lu %17s %15s %63s %63s", 
                                  &lease_time, client_mac, client_ip, client_hostname, client_id);
        
        // 至少需要MAC和IP，hostname是可选的
        if (parsed_fields >= 3) {
            if (strcasecmp(client_mac, mac_str) == 0) {
                // 如果有hostname且不为空且不是"*"，则使用它
                if (strlen(client_hostname) > 0 && strcmp(client_hostname, "*") != 0) {
                    strncpy(hostname, client_hostname, max_len - 1);
                    hostname[max_len - 1] = '\0';
                    found = 1;
                    break;
                } 
            }
        }
    }
    
    fclose(fp);
    
    return found ? 0 : -1;
}
/*End of add by wzx*/


static int TMgrInfoAddHost(t_terminal_mgr new_host)
{
    int host_num = 0;
    pt_terminal_mgr current;

    LIST_FOREACH(tmgrInfoList, current)
    {
        host_num++;
    }
    if (host_num > MAX_HOST_LIST_NODE_NUM)
    {
        WCPRT("Terminal count has reached the maximum of %d and cannot be increased further.", MAX_HOST_LIST_NODE_NUM);
        return STATUS_OUT_OF_RESOURCES;
    }

    pt_terminal_mgr new_node = (pt_terminal_mgr)malloc(sizeof(t_terminal_mgr));
    if (new_node == NULL)
    {
        WCPRT("Failed to allocate memory for new host");
        return STATUS_ERROR;
    }

    if (strlen(new_host.host_info.host_name) == 0)
    {
        char dhcp_hostname[MAX_HOST_NAME_LEN] = {0};
        // 首先尝试从DHCP lease文件中获取hostname
        if (get_hostname_from_dhcp(new_host.mac_addr, dhcp_hostname, sizeof(dhcp_hostname)) == 0) {
            strncpy(new_host.host_info.host_name, dhcp_hostname, MAX_HOST_NAME_LEN - 1);
            new_host.host_info.host_name[MAX_HOST_NAME_LEN - 1] = '\0';
        } else {
            // 如果无法从DHCP获取，则使用MAC地址作为fallback
            snprintf(new_host.host_info.host_name, MAX_HOST_NAME_LEN - 1, "%02X:%02X:%02X:%02X:%02X:%02X", 
                    new_host.mac_addr[0], new_host.mac_addr[1], new_host.mac_addr[2], 
                    new_host.mac_addr[3], new_host.mac_addr[4], new_host.mac_addr[5]);
        }
    }

    memcpy(new_node, &new_host, sizeof(t_terminal_mgr));
    new_node->prev = NULL;
    new_node->next = NULL;

    pthread_mutex_lock(&tmgrInfoMutex);

    new_node->next = tmgrInfoList;
    if (tmgrInfoList != NULL)
    {
        tmgrInfoList->prev = new_node;
    }
    new_node->prev = NULL;
    tmgrInfoList = new_node;

    pthread_mutex_unlock(&tmgrInfoMutex);

    return STATUS_SUCCESS;
}

/*static */ void TmgrInfoDelHost(const unsigned char *mac)
{
    pthread_mutex_lock(&tmgrInfoMutex);

    pt_terminal_mgr current;
    LIST_FOREACH(tmgrInfoList, current)
    {
        if (memcmp(current->mac_addr, mac, MAC_ADDR_LEN) == 0)
        {
            if (current->prev != NULL)
            {
                current->prev->next = current->next;
            }
            else
            {
                tmgrInfoList = current->next;
            }
            if (current->next != NULL)
            {
                current->next->prev = current->prev;
            }
            free(current);
            pthread_mutex_unlock(&tmgrInfoMutex);
            return;
        }
    }

    pthread_mutex_unlock(&tmgrInfoMutex);
}

pt_terminal_mgr TMgrInfoFindHostByMac(const unsigned char *mac)
{
    pthread_mutex_lock(&tmgrInfoMutex);

    pt_terminal_mgr current;
    LIST_FOREACH(tmgrInfoList, current)
    {
        if (memcmp(current->mac_addr, mac, sizeof(current->mac_addr)) == 0)
        {
            pthread_mutex_unlock(&tmgrInfoMutex);
            return current;
        }
    }
    pthread_mutex_unlock(&tmgrInfoMutex);
    return NULL;
}

static void TmgrInfoWirelessHostInit()
{
    #if 0  //moved by wzx
    FILE *fp;
    char ifname[16];
    char buffer[1024];
    char ssid[64] = {0};
    unsigned char mac[MAC_ADDR_LEN];
    pt_terminal_mgr pt_host;
    t_terminal_mgr t_host = {0};

    fp = popen("bridge fdb show", "r");
    if (fp == NULL)
    {
        WCPRT("popen failed");
        return;
    }

    while (fgets(buffer, sizeof(buffer), fp) != NULL)
    {
        if (strstr(buffer, "permanent"))
        {
            continue;
        }

        sscanf(buffer, "%2hhx:%2hhx:%2hhx:%2hhx:%2hhx:%2hhx dev %[^ ]", &mac[0], &mac[1], &mac[2], &mac[3], &mac[4], &mac[5], ifname);

        if (tmgrMapper.pf_ssid_get(ifname, ssid, sizeof(ssid)) == 0)
        {
            pt_host = TMgrInfoFindHostByMac(mac);

            if (pt_host != NULL)
            {
                pt_host->host_info.is_wireless_device = 1;
                strncpy(pt_host->host_info.link_ssid, ssid, sizeof(pt_host->host_info.link_ssid));
            }
            else
            {
                memcpy(t_host.mac_addr, mac, sizeof(t_host.mac_addr));
                t_host.host_info.is_wireless_device = 1;
                strncpy(t_host.host_info.link_ssid, ssid, sizeof(t_host.host_info.link_ssid));
                TMgrInfoAddHost(t_host);
            }
        }
    }
    pclose(fp);
    #endif
    return;
}

int TMgrInfoInit(void)
{
    int ret = 0;
    int cfgNums = 0, i;
    t_terminal_mgr t_cfg = {0};
    pt_terminal_mgr ptMgr = NULL;

    if (tmgrMapper.pf_host_cfg_nums_get)
    {
        cfgNums = tmgrMapper.pf_host_cfg_nums_get();

        if (cfgNums)
        {
            ptMgr = malloc(sizeof(t_terminal_mgr) * cfgNums);
            if (ptMgr == NULL)
            {
                ret = -1;
            }
            memset(ptMgr, 0, sizeof(t_terminal_mgr) * cfgNums);
        }
    }
    else
    {
        ret = -1;
    }

    if (ptMgr)
    {
        if (tmgrMapper.pf_host_cfg_get)
        {
            if (tmgrMapper.pf_host_cfg_get(ptMgr) == 0)
            {
                for (i = 0; i < cfgNums; i++)
                {
                    memcpy(&t_cfg.host_cfg, &ptMgr[i].host_cfg, sizeof(t_cfg.host_cfg));
                    memset(ptMgr[i].host_cfg.url, 0, sizeof(ptMgr[i].host_cfg.url));
                    
                    ret |= TMgrInfoAddHost(ptMgr[i]);
    
                    TMgrConfigHandler(ptMgr[i].mac_addr, URL_CONTROL, t_cfg.host_cfg.url_limit_type, t_cfg.host_cfg.url);
                }
            }
        }

        free(ptMgr);
    }

    TmgrInfoWirelessHostInit();

    return ret;
}

// 获取高精度时间（秒，包含小数部分）
static double get_current_time_precise(void)
{
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (double)tv.tv_sec + (double)tv.tv_usec / 1000000.0;
}

static void TMgrInfoSetHostOnline(pt_terminal_mgr pt_host, unsigned char *mac, unsigned int ip)
{
    t_terminal_mgr t_host = {0};
    double current_time = get_current_time_precise();

    if (pt_host != NULL) // Initialize for a cfg-exit terminal / Update list-exit terminal
    {
        pt_host->host_info.ip = ip;
        pt_host->host_info.online = 1;
    
        /* 定期尝试更新hostname（如果当前是MAC地址格式） */
        if (strlen(pt_host->host_info.host_name) > 0) {
            // 生成当前MAC地址的字符串格式
            char mac_str[18];
            snprintf(mac_str, sizeof(mac_str), "%02x:%02x:%02x:%02x:%02x:%02x",
                     pt_host->mac_addr[0], pt_host->mac_addr[1], pt_host->mac_addr[2],
                     pt_host->mac_addr[3], pt_host->mac_addr[4], pt_host->mac_addr[5]);
            
            if (strncasecmp(pt_host->host_info.host_name, mac_str, strlen(mac_str)) == 0) {
                char dhcp_hostname[MAX_HOST_NAME_LEN] = {0};
                if (get_hostname_from_dhcp(pt_host->mac_addr, dhcp_hostname, sizeof(dhcp_hostname)) == 0) {
                    strncpy(pt_host->host_info.host_name, dhcp_hostname, MAX_HOST_NAME_LEN - 1);
                    pt_host->host_info.host_name[MAX_HOST_NAME_LEN - 1] = '\0';
                }
            }
        }

        if (pt_host->host_info.link_time == 0)
        {
            pt_host->host_info.last_seen = current_time;
        }
    }
    else // Initialize for a new terminal
    {
        memcpy(t_host.mac_addr, mac, sizeof(t_host.mac_addr));
        t_host.host_info.ip = ip;
        t_host.host_info.online = 1;
        t_host.host_info.link_time = 0;
        t_host.host_info.last_seen = current_time;
        TMgrInfoAddHost(t_host);
    }
}

static void TMgrInfoUpdateWirelessHost(pt_terminal_mgr pt_host, unsigned char *mac, char *ifname)
{
    char ssid[64] = {0};
    t_terminal_mgr t_host = {0};

    if (tmgrMapper.pf_ssid_get(ifname, ssid, sizeof(ssid)) == 0)
    {
        if (pt_host != NULL)
        {
            pt_host->host_info.is_wireless_device = 1;
            strncpy(pt_host->host_info.link_ssid, ssid, sizeof(pt_host->host_info.link_ssid));
        }
        else
        {
            memcpy(t_host.mac_addr, mac, sizeof(t_host.mac_addr));
            t_host.host_info.is_wireless_device = 1;
            strncpy(t_host.host_info.link_ssid, ssid, sizeof(t_host.host_info.link_ssid));
            TMgrInfoAddHost(t_host);
        }
    }
}

static unsigned int hash_mac_to_index(unsigned char *mac)
{
    unsigned int hash = 0;

    for (int i = 0; i < MAC_ADDR_LEN; i++)
    {
        hash += mac[i];
        hash += (hash << 10);
        hash ^= (hash >> 6);
    }

    hash += (hash << 3);
    hash ^= (hash >> 11);
    hash += (hash << 15);

    return hash % MAX_HOST_LIST_NODE_NUM;
}

int TMgrInfoUpdateAllHostCurrentStatus()
{
    int i;
    int hash_key;
    int total = 0;
    double current_time = get_current_time_precise();
    // unsigned long long dif_txbytes = 0;
    // unsigned long long dif_rxbytes = 0;
    unsigned char void_mac[MAC_ADDR_LEN] = {0};
    pt_terminal_mgr pMacs = NULL;
    pt_terminal_mgr current;
    t_terminal_mgr HashMacs[MAX_HOST_LIST_NODE_NUM] = {0};
    t_wired_host_port_mapping tmgrWiredMapping[MAX_LAN_PORT_NUM] = {0};
    
    tmgrMapper.pf_get_wired_port_link_status(&tmgrWiredMapping[0]);
    
    pMacs = malloc(sizeof(t_terminal_mgr) * MAX_HOST_LIST_NODE_NUM);
    if (pMacs == NULL)
    {
        WCPRT("Memory allocation failed\n");
        return -1;
    }
    memset(pMacs, 0, sizeof(t_terminal_mgr) * MAX_HOST_LIST_NODE_NUM);

    total = tmgrMapper.pf_wlan_mac_table_get(pMacs);

    /*Add by wzx.AX3000 core 的sta_info无法读取最后4位mac地址，哈希表匹配失败，导致无法在app上显示无线客户端*/
    unsigned char is_jeton_ax3000_core = 0;
    char command[] = "hi_cfm show config | grep equipment_id | awk -F\"'\" '{print $2}'";
    char equipment_id[128] = {0};
    FILE *fp = popen(command, "r");
    if (fp != NULL) {
        if (fgets(equipment_id, sizeof(equipment_id), fp) != NULL) {
            // 移除换行符
            equipment_id[strcspn(equipment_id, "\n")] = '\0';
            if (strstr(equipment_id, "Jeton AX3000 Core") != NULL) {
                is_jeton_ax3000_core = 1;
            }
        }
        pclose(fp);
    }

    /*
     * 构建 HashMacs：使用线性探测解决碰撞，确保不会覆盖其他 MAC。
     * 这样在查找时可以按 MAC 精确匹配，避免“错用他人字节统计”。
     */
    for (i = 0; i < total; i++)
    {
        int placed = 0;
        int probe;
        hash_key = hash_mac_to_index(pMacs[i].mac_addr);
        for (probe = 0; probe < MAX_HOST_LIST_NODE_NUM; probe++)
        {
            int idx = (hash_key + probe) % MAX_HOST_LIST_NODE_NUM;
            if (memcmp(HashMacs[idx].mac_addr, void_mac, MAC_ADDR_LEN) == 0)
            {
                memcpy(HashMacs[idx].mac_addr, pMacs[i].mac_addr, MAC_ADDR_LEN);
                HashMacs[idx].host_info.rxbytes = pMacs[i].host_info.rxbytes;
                HashMacs[idx].host_info.txbytes = pMacs[i].host_info.txbytes;
                HashMacs[idx].host_info.rssi    = pMacs[i].host_info.rssi;
                placed = 1;
                break;
            }
        }
        if (!placed)
        {
            /* 表已满，放弃该条，避免覆盖 */
        }
    }

    pthread_mutex_lock(&tmgrInfoMutex);
    LIST_FOREACH(tmgrInfoList, current)
    {
        
        hash_key = hash_mac_to_index(current->mac_addr);
        if (memcmp(HashMacs[hash_key].mac_addr, void_mac, MAC_ADDR_LEN) != 0)
        {
            current->host_info.is_wireless_device = 1;
        }
        else
        {
            // 只有AX3000 Core设备才执行部分MAC地址匹配逻辑
            if (is_jeton_ax3000_core)
            {
                // 检查终端MAC后4位是否为0，如果是则计算对应的哈希键
                unsigned char temp_mac[MAC_ADDR_LEN] = {0};
                memcpy(temp_mac, current->mac_addr, MAC_ADDR_LEN); // 复制完整MAC地址
                temp_mac[4] = 0; // 将后2字节置为0
                temp_mac[5] = 0;
                hash_key = hash_mac_to_index(temp_mac);
                
                // 检查该哈希位置是否有数据且后4位也为0
                if (memcmp(HashMacs[hash_key].mac_addr, void_mac, MAC_ADDR_LEN) != 0 &&
                    HashMacs[hash_key].mac_addr[4] == 0 && HashMacs[hash_key].mac_addr[5] == 0)
                {
                    current->host_info.is_wireless_device = 1;
                }
            }
        }

        if (current->host_info.is_wireless_device)
        {
            current->host_info.rssi = -60;

            /* 精确匹配：若未在 HashMacs 中找到相同 MAC，则视为离线 */
            {
                int found = 0;
                int probe;
                for (probe = 0; probe < MAX_HOST_LIST_NODE_NUM; probe++)
                {
                    int idx = (hash_key + probe) % MAX_HOST_LIST_NODE_NUM;
                    if (memcmp(HashMacs[idx].mac_addr, void_mac, MAC_ADDR_LEN) == 0)
                    {
                        /* 探测到空位，说明不存在该 MAC */
                        break;
                    }
                    if (memcmp(HashMacs[idx].mac_addr, current->mac_addr, MAC_ADDR_LEN) == 0)
                    {
                        found = 1;
                        break;
                    }
                    
                    // 检查部分匹配：只有AX3000 Core设备才执行部分匹配逻辑
                    if (is_jeton_ax3000_core && HashMacs[idx].mac_addr[4] == 0 && HashMacs[idx].mac_addr[5] == 0 &&
                        memcmp(HashMacs[idx].mac_addr, current->mac_addr, 4) == 0)
                    {
                        found = 1;
                        break;
                    }
                }
                if (!found)
                {
                    SET_HOST_OFFLINE(current);
                }
            }
        }
        // else
        // {
        //     for (j = 0; j < MAX_LAN_PORT_NUM; j++)
        //     {
        //         if (memcmp(tmgrWiredMapping[j].mac, current->mac_addr, MAC_ADDR_LEN) == 0)
        //         {
        //             if (tmgrWiredMapping[j].link_status == 0)
        //             {
        //                 SET_HOST_OFFLINE(current);
        //             }
        //             break;
        //         }
        //     }
        // }

        current->host_info.download_rate = 0;
        current->host_info.upload_rate = 0;

        if (current->host_info.online == 1)
        {
            /*update each host link time*/
            current->host_info.link_time += (current_time - current->host_info.last_seen);

            /*update each host real-time data*/
            /* 查找与当前 MAC 完全匹配的 HashMacs 条目 */
            int match_idx = -1;
            int probe;
            for (probe = 0; probe < MAX_HOST_LIST_NODE_NUM; probe++)
            {
                int idx = (hash_key + probe) % MAX_HOST_LIST_NODE_NUM;
                if (memcmp(HashMacs[idx].mac_addr, void_mac, MAC_ADDR_LEN) == 0)
                {
                    /* 遇到空位，提前结束 */
                    break;
                }
                if (memcmp(HashMacs[idx].mac_addr, current->mac_addr, MAC_ADDR_LEN) == 0)
                {
                    match_idx = idx;
                    break;
                }
            }

            if (match_idx != -1)
            {

                if (current_time - current->host_info.last_seen > 0)
                {
                    /* Use 64-bit safe diffs and protect against counter wrap/reset */
                    {
                        uint64_t new_tx = (uint64_t)HashMacs[match_idx].host_info.txbytes;
                        uint64_t old_tx = (uint64_t)current->host_info.txbytes;
                        uint64_t new_rx = (uint64_t)HashMacs[match_idx].host_info.rxbytes;
                        uint64_t old_rx = (uint64_t)current->host_info.rxbytes;

                        uint64_t dif_tx = 0;
                        uint64_t dif_rx = 0;
                        if (new_tx >= old_tx) dif_tx = new_tx - old_tx; /* normal */
                        else dif_tx = 0; /* counter wrapped or reset */
                        if (new_rx >= old_rx) dif_rx = new_rx - old_rx;
                        else dif_rx = 0;

                        double dt = current_time - current->host_info.last_seen;
                        if (dt > 0.1)  // 最小间隔0.1秒，避免除零和过小的时间差
                        {
                            double dl_double, ul_double;
                            
                            /* 保持原有语义：下行=tx，上行=rx，使用浮点数计算提高精度 */
                            dl_double = (double)dif_tx / dt / 1024.0; /* KB/s */
                            ul_double = (double)dif_rx / dt / 1024.0; /* KB/s */

                            // 异常值检测和过滤
                            unsigned int prev_dl = current->host_info.download_rate;
                            unsigned int prev_ul = current->host_info.upload_rate;
                            
                            // 如果新计算的速率比上次大超过5倍，可能是异常值
                            if (prev_dl > 0 && dl_double > prev_dl * 5) {
                                WCPRT("Suspicious download rate jump: %.2f KB/s (prev: %u), dt=%.3f, dif_tx=%llu", 
                                      dl_double, prev_dl, dt, dif_tx);
                                dl_double = prev_dl; // 使用上次的值
                            }
                            
                            if (prev_ul > 0 && ul_double > prev_ul * 5) {
                                WCPRT("Suspicious upload rate jump: %.2f KB/s (prev: %u), dt=%.3f, dif_rx=%llu", 
                                      ul_double, prev_ul, dt, dif_rx);
                                ul_double = prev_ul; // 使用上次的值
                            }
                            
                            // 限制最大速率（防止异常峰值）
                            if (dl_double > 50000) dl_double = 50000; // 限制最大50MB/s
                            if (ul_double > 50000) ul_double = 50000; // 限制最大50MB/s

                            // 使用滑动平均平滑速率数据（权重0.7给新值，0.3给历史值）
                            if (current->host_info.download_rate_smooth == 0) {
                                current->host_info.download_rate_smooth = dl_double;
                                current->host_info.upload_rate_smooth = ul_double;
                            } else {
                                current->host_info.download_rate_smooth = 
                                    0.7 * dl_double + 0.3 * current->host_info.download_rate_smooth;
                                current->host_info.upload_rate_smooth = 
                                    0.7 * ul_double + 0.3 * current->host_info.upload_rate_smooth;
                            }

                            // 四舍五入转换为整数存储（保持数据结构兼容性）
                            current->host_info.download_rate = (unsigned int)(current->host_info.download_rate_smooth + 0.5);
                            current->host_info.upload_rate = (unsigned int)(current->host_info.upload_rate_smooth + 0.5);
                        }
                    }
                }
                /* 更新当前保存的累计值与 RSSI */
                current->host_info.rssi    = HashMacs[match_idx].host_info.rssi;
                current->host_info.rxbytes = HashMacs[match_idx].host_info.rxbytes;
                current->host_info.txbytes = HashMacs[match_idx].host_info.txbytes;
            }

            current->host_info.last_seen = current_time;
        }
    }
    free(pMacs);
    pthread_mutex_unlock(&tmgrInfoMutex);
    return 0;
}


UNUSED_FUNC void TMgrInfoUpdateAllHostNode(unsigned char *mac, unsigned int ip)
{
    pt_terminal_mgr pt_host = TMgrInfoFindHostByMac(mac);

    TMgrInfoSetHostOnline(pt_host, mac, ip);
}

UNUSED_FUNC void TMgrInfoUpdateWirelessHostNode(unsigned char *mac, char *ifname)
{
    pt_terminal_mgr pt_host = TMgrInfoFindHostByMac(mac);

    TMgrInfoUpdateWirelessHost(pt_host, mac, ifname);
}

UNUSED_FUNC void TMgrInfoSetHostOffline(unsigned char *mac)
{
    pt_terminal_mgr pt_host = TMgrInfoFindHostByMac(mac);

    if (pt_host != NULL)
    {
        SET_HOST_OFFLINE(pt_host);
    }
}

static int update_tmgr_list_flag = 1;
static int update_tmgr_list_delay = 3;

static void *TMgrDelayUpdateInfoList(void *arg)
{
    sleep(update_tmgr_list_delay);
    update_tmgr_list_flag = 1;
    return NULL;
}

UNUSED_FUNC int TMgrInfoGetSsidClientNum(char *ifname)
{
    int num = 0;
    char ssid[64] = {0};
    pt_terminal_mgr current;
    pthread_t tid_update_tmgr_list;

    if (update_tmgr_list_flag)
    {
        update_tmgr_list_flag = 0;
        TMgrInfoUpdateAllHostCurrentStatus();
        pthread_create(&tid_update_tmgr_list, NULL, TMgrDelayUpdateInfoList, NULL);
    }

    if (tmgrMapper.pf_ssid_get(ifname, ssid, sizeof(ssid)) == 0)
    {
        pthread_mutex_lock(&tmgrInfoMutex);
        LIST_FOREACH(tmgrInfoList, current)
        {
            if (current->host_info.online == 1 && current->host_info.is_wireless_device == 1)
            {
                if (strcmp(current->host_info.link_ssid, ssid) == 0)
                {
                    num++;
                }
            }
        }
        pthread_mutex_unlock(&tmgrInfoMutex);
    }
    return num;
}

UNUSED_FUNC void print_host_info_list(pt_terminal_mgr head)
{
    pt_terminal_mgr current = head;
    while (current != NULL)
    {
        WCPRT("MAC Address: ");
        for (int i = 0; i < MAC_ADDR_LEN; i++)
        {
            printf("%02X", current->mac_addr[i]);
        }
        printf("\n");
        WCPRT("IP: %u\n", current->host_info.ip);
        WCPRT("Online: %u\n", current->host_info.online);
        WCPRT("Link Time: %lld seconds\n", current->host_info.link_time);
        WCPRT("Download Rate: %u Kbps\n", current->host_info.download_rate);
        WCPRT("Upload Rate: %u Kbps\n", current->host_info.upload_rate);
        WCPRT("RSSI: %d\n", current->host_info.rssi);
        WCPRT("Host Name: %s\n", current->host_info.host_name);
        WCPRT("Link SSID: %s\n", current->host_info.link_ssid);
        WCPRT("Is Wireless Device: %u\n", current->host_info.is_wireless_device);
        WCPRT("******************************************************");

        current = current->next;
    }
}


UNUSED_FUNC int TMgrIsWanInterface(char *if_name)
{
    if (tmgrMapper.pf_is_wan_intf)
    {
        return tmgrMapper.pf_is_wan_intf(if_name);
    }
    return 0;
}