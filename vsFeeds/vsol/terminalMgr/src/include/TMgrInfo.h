#ifndef TMGR_INFO_H
#define TMGR_INFO_H

#include <stdio.h>
#include <stdarg.h>
#include <syslog.h>
#include "platform.h"

#define MAC_ADDR_LEN 6
#define MAX_URLS_NUM 10
#define MAX_URL_LEN 32
#define MAX_COMM_NAME_LEN 33
#define MAX_INTERNET_TIME_NUM 64
#define MAX_HOST_NAME_LEN 64
#define MAX_HOST_LIST_NODE_NUM 256

#if 1
#define WCPRT(...) do{printf("![%s][%d] ",__FUNCTION__,__LINE__); printf(__VA_ARGS__); printf("\n");}while(0)
#else
#define WCPRT(...) syslog(LOG_INFO,  __VA_ARGS__)
#endif
#define LIST_FOREACH(head, iterator) \
    for (iterator = (head); iterator != NULL; iterator = iterator->next)

#define MAC2STR(a) (a)[0], (a)[1], (a)[2], (a)[3], (a)[4], (a)[5]
#define MACSTR "%02x:%02x:%02x:%02x:%02x:%02x"

#define SET_HOST_OFFLINE(host) do { \
    (host)->host_info.online = 0; \
    (host)->host_info.link_time = 0; \
    (host)->host_info.rxbytes = 0; \
    (host)->host_info.txbytes = 0; \
} while(0)

typedef enum {
        STATUS_SUCCESS,
        STATUS_ERROR,
        STATUS_NOT_FOUND,
        STATUS_DUPLICATE,
        STATUS_OUT_OF_RESOURCES,
} Status;

typedef enum {
    SUNDAY = 1,
    MONDAY,
    TUESDAY,
    WEDNESDAY,
    THURSDAY,
    FRIDAY,
    SATURDAY
} DayOfWeek;

/* Structure for host infomations saved in memory */
typedef struct t_host_info {
    unsigned int ip; // IPv4
    unsigned char online;
    unsigned long long link_time; // unit : s
    double last_seen;  // 高精度时间戳（秒，包含小数部分）
    unsigned long long rxbytes;
    unsigned long long txbytes;
    unsigned int download_rate; // KBs
    unsigned int upload_rate;   // KBs
    double download_rate_smooth; // 平滑后的下载速率
    double upload_rate_smooth;   // 平滑后的上传速率
    signed char rssi;
    char host_name[MAX_HOST_NAME_LEN];
    char host_location[MAX_COMM_NAME_LEN];
    char client_type[MAX_COMM_NAME_LEN];
    char link_ssid[33];
    unsigned char is_wireless_device;
} t_host_info;

typedef struct t_wired_host_port_mapping{
    unsigned char mac[MAC_ADDR_LEN];
    unsigned char link_status;  //1:up  0:down
} t_wired_host_port_mapping, *pt_wired_host_port_mapping;

extern int TMgrInfoInit(void);
extern void TMgrInfoUpdateAllHostNode(unsigned char *mac, unsigned int ip);
extern void TMgrInfoUpdateWirelessHostNode(unsigned char *mac, char *ifname);
extern int TMgrInfoUpdateAllHostCurrentStatus(void);
extern void TMgrInfoSetHostOffline(unsigned char *mac);
extern void TmgrInfoDelHost(const unsigned char *mac);
extern int TMgrInfoGetSsidClientNum(char *ifname);
extern int TMgrIsWanInterface(char *if_name);
#endif // TMGR_INFO_H
