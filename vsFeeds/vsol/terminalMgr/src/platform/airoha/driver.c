#include <string.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <linux/wireless.h>
#include <mtk_wifi_ioctl.h>
#include <errno.h>

#include <TMgrMain.h>
#include "json-c/json.h"


#define VAP_NUMS 2
#define VAP_LEN     16
#define MAX_RSSI(A, B, C) ((A) > (B) ? ((A) > (C) ? (A) : (C)) : ((B) > (C) ? (B) : (C)))
#define WIFI_DRIVER_FILE "/proc/bus/pci/devices"
#define WIFI_CHIP_MODEL_7603 "7603"

// static int wifi_mode_select = -1;

#if 1
#define DBG(...)
#else
extern int Log2Stderr;

#define DBG(fmt, ...) \
    if (Log2Stderr)   \
    printf("<%s:%d> " fmt, __FUNCTION__, __LINE__, ##__VA_ARGS__)
#endif

int mtk_wlan_ioctl_get(int iIOCmd, char *ifname, char *szCmd, PU_IOCTL_RESULT puResult)
{
    int iSockedfd = -1;
    char szIfName[WL_SM_SIZE_MAX] = {0};
    char szData[WL_XXXXL_SIZE_MAX] = {0};
    struct iwreq wrq = {0};

    if ((NULL == puResult))
    {
        return -1;
    }

    memset(puResult, 0, sizeof(U_IOCTL_RESULT));

    iSockedfd = socket(AF_INET, SOCK_DGRAM, 0);
    if (iSockedfd < 0)
    {
        WCPRT("iSockedfd:%d\n", iSockedfd);
        return -1;
    }

    snprintf(szIfName, sizeof(szIfName), "%s", ifname);
    if (szCmd != NULL)
    {
        WCPRT("ioctl get [%d] %s %s\n", iIOCmd, szIfName, szCmd);
        strncpy(szData, szCmd, sizeof(szData) - 1);
    }
    else
    {
        WCPRT("ioctl get [%d] %s\n", iIOCmd, szIfName);
    }

    strncpy(wrq.ifr_name, szIfName, sizeof(wrq.ifr_name) - 1);

    wrq.u.data.pointer = szData;
    wrq.u.data.length = sizeof(szData);
    wrq.u.data.flags = 0;

    if (ioctl(iSockedfd, iIOCmd, &wrq) < 0)
    {
        WCPRT("ioctl failed\n");
        WCPRT("%s\n",strerror(errno));
        close(iSockedfd);
        return -1;
    }

    close(iSockedfd);

    switch (iIOCmd)
    {
    /* get channel */
    case SIOCGIWFREQ:
        puResult->dwValue = wrq.u.freq.m;
        break;

    /* get TxRate */
    case SIOCGIWRATE:
        puResult->iValue = wrq.u.bitrate.value;
        break;

    /* other use wrq.u.data.pointer */
    default:
        if (NULL == wrq.u.data.pointer)
        {
            WCPRT("mtk_wlan_ioctl_get result is NULL");
            puResult->szValue[0] = '\0';
            return 0;
        }
        // strncpy(puResult->szValue, wrq.u.data.pointer,
        //(wrq.u.data.length > WL_XXXXL_SIZE_MAX-1) ? (WL_XXXXL_SIZE_MAX-1) : wrq.u.data.length);
        if (wrq.u.data.length > WL_XXXXL_SIZE_MAX - 1)
            memcpy(puResult->szValue, wrq.u.data.pointer, WL_XXXXL_SIZE_MAX);
        else
            memcpy(puResult->szValue, wrq.u.data.pointer, wrq.u.data.length + 1);
        break;
    }

    return 0;
}

#if 0
static int toatalMacs[RADIO_NUMS] = {0};

static int get_mac_table(unsigned char radioIdx)
{
    int ret = 0,j;
    char ifName[RADIO_NUMS][32] = {"ra0", "rai0"};
    U_IOCTL_RESULT uIoctlResult = {0};
    RT_802_11_MAC_TABLE *pTemp = NULL;

    if (radioIdx < RADIO_NUMS)
    {
        toatalMacs[radioIdx] = 0;
        memset(macs[radioIdx], 0, MAX_LEN_OF_MAC_TABLE * MAC_ADDR_LEN);

        memset(&uIoctlResult, 0, sizeof(U_IOCTL_RESULT));
        ret = mtk_wlan_ioctl_get(RTPRIV_IOCTL_GET_MAC_TABLE_STRUCT, ifName[radioIdx], NULL, &uIoctlResult);
        if (ret == 0)
        {
            pTemp = (RT_802_11_MAC_TABLE *)uIoctlResult.szValue;
            toatalMacs[radioIdx] = pTemp->Num;
            
            for (j = 0; j < pTemp->Num; j++)
            {
                memcpy(&macs[radioIdx][j], pTemp->Entry[j].Addr, MAC_ADDR_LEN);
            }
        }
        else
        {
            WCPRT("%s mtk_wlan_ioctl_get failed\n", ifName[radioIdx]);
        }
    }

    return ret;
}
#endif

int pf_airoha_wlan_mac_nums_get(unsigned char radioIdx)
{
    int nums = 0;
#if 0
    if (get_mac_table(radioIdx) == 0)
    {
        nums = toatalMacs[radioIdx];
    }
#endif
    return nums;
}

// static int findStringInFile(const char *filename, const char *searchString)
// {
//     FILE *file = fopen(filename, "r");
//     if (!file)
//     {
//         perror("Error opening file");
//         return 0;
//     }

//     char line[1024];
//     while (fgets(line, sizeof(line), file))
//     {

//         if (strstr(line, searchString) != NULL)
//         {
//             fclose(file);
//             return 1;
//         }
//     }

//     fclose(file);
//     return 0;
// }

// static int copy7603MacTbl(pt_terminal_mgr pMac, RT_7603_802_11_MAC_TABLE *pTemp, int *total)
// {
//     int i, j;
//     int jump;
//     int count = 0;

//     if (pTemp->Num == 0)
//         return -1;

//     for (i = 0; i < pTemp->Num; i++)
//     {
//         jump = 0;
//         for (j = 0; j < *total; j++)
//         {
//             if (memcmp(pMac[j].mac_addr, pTemp->Entry[i].Addr, MAC_ADDR_LEN) == 0)
//             {
//                 jump = 1;
//                 break;
//             }
//         }
//         if (jump)
//             continue;

//         if (count + *total == MAX_HOST_LIST_NODE_NUM)
//             return -1;

//         memcpy(pMac[count + *total].mac_addr, pTemp->Entry[i].Addr, MAC_ADDR_LEN);
//         pMac[count + *total].host_info.rxbytes = pTemp->Entry[i].RxBytes;
//         pMac[count + *total].host_info.txbytes = pTemp->Entry[i].TxBytes;
//         pMac[count + *total].host_info.rssi = MAX_RSSI(pTemp->Entry[i].AvgRssi0, pTemp->Entry[i].AvgRssi1, pTemp->Entry[i].AvgRssi2);
//         count++;
//     }
    
//     *total += count;

//     return 0;
// }

// static int copy7623MacTbl(pt_terminal_mgr pMac, RT_7623_802_11_MAC_TABLE *pTemp, int *total)
// {
//     int i;

//     if (pTemp->Num > 0)
//     {
//         for (i = 0; i < pTemp->Num; i++)
//         {
//             if (i + *total == MAX_HOST_LIST_NODE_NUM)
//             {
//                 return -1;
//             }

//             memcpy(pMac[i + *total].mac_addr, pTemp->Entry[i].Addr, MAC_ADDR_LEN);
//             pMac[i + *total].host_info.rxbytes = pTemp->Entry[i].RxBytes;
//             pMac[i + *total].host_info.txbytes = pTemp->Entry[i].TxBytes;
//             pMac[i + *total].host_info.rssi = MAX_RSSI(pTemp->Entry[i].AvgRssi0, pTemp->Entry[i].AvgRssi1, pTemp->Entry[i].AvgRssi2);
//         }
//         *total += pTemp->Num;
//     }
//     return 0;
// }


static void str_to_mac(char *mac_str,unsigned char *mac_bytes)
{
    char token[13]; // 12字符 + 终止符
    int j = 0;
    // 移除冒号
    for (int i = 0; i < strlen(mac_str); i++) {
        if (mac_str[i] != ':') {
            token[j++] = mac_str[i];
        }
    }
    token[j] = '\0';
    // 解析为6字节数组
    sscanf(
        token,
        "%2hhx%2hhx%2hhx%2hhx%2hhx%2hhx",
        &mac_bytes[0], &mac_bytes[1], &mac_bytes[2],
        &mac_bytes[3], &mac_bytes[4], &mac_bytes[5]
    );
    
}

static void parse_wireless_client(char *ifName, pt_terminal_mgr pMac ,int *total )
{
    char filepath[128] = "";
    snprintf(filepath, sizeof(filepath), "/proc/%s/sta_info", ifName);
    
    FILE *fp = fopen(filepath, "r");
    if (!fp) {
        WCPRT("Failed to open %s: %s\n", filepath, strerror(errno));
        return;
    }
    
    char line[512];
    int current_sta = -1;
    unsigned char current_mac[MAC_ADDR_LEN] = {0};
    uint64_t tx_bytes = 0, rx_bytes = 0;
    int rssi = -60;
    
    while (fgets(line, sizeof(line), fp)) {
        // 解析STA编号行 (如 " 1: aid: 35")
        if (sscanf(line, " %d: aid:", &current_sta) == 1) {
            // 如果之前有解析到的STA数据，先保存
            if (current_sta > 1 && memcmp(current_mac, "\0\0\0\0\0\0", MAC_ADDR_LEN) != 0) {
                memcpy(pMac[*total].mac_addr, current_mac, MAC_ADDR_LEN);
                pMac[*total].host_info.rxbytes = rx_bytes;
                pMac[*total].host_info.txbytes = tx_bytes;
                pMac[*total].host_info.rssi = rssi;
                (*total)++;
            }
            // 重置当前STA的数据
            memset(current_mac, 0, MAC_ADDR_LEN);
            tx_bytes = 0;
            rx_bytes = 0;
            rssi = -60;
            continue;
        }
        
        // 解析MAC地址
        if (strstr(line, "MAC ADDR:")) {
            char mac_str[32];
            if (sscanf(line, "    MAC ADDR: %31s", mac_str) == 1) {
                str_to_mac(mac_str, current_mac);
            }
            continue;
        }
        
        // 解析RSSI
        if (strstr(line, "RSSI:")) {
            sscanf(line, "    RSSI: %d", &rssi);
            continue;
        }
        
        // 解析TX bytes
        if (strstr(line, "TX bytes:")) {
            uint64_t temp_tx = 0;
            if (sscanf(line, "    TX bytes: %llu", &temp_tx) == 1) {
                // 添加异常值检测：防止计数器异常跳跃
                if (temp_tx < 0xFFFFFFFFFFFFFFFF && temp_tx >= 0) { // 防止溢出
                    tx_bytes = temp_tx;
                } else {
                    WCPRT("Invalid TX bytes detected: %llu, keeping previous value: %llu", temp_tx, tx_bytes);
                }
            }
            continue;
        }
        
        // 解析RX bytes
        if (strstr(line, "RX bytes:")) {
            uint64_t temp_rx = 0;
            if (sscanf(line, "    RX bytes: %llu", &temp_rx) == 1) {
                // 添加异常值检测：防止计数器异常跳跃
                if (temp_rx < 0xFFFFFFFFFFFFFFFF && temp_rx >= 0) { // 防止溢出
                    rx_bytes = temp_rx;
                } else {
                    WCPRT("Invalid RX bytes detected: %llu, keeping previous value: %llu", temp_rx, rx_bytes);
                }
            }
            continue;
        }
    }
    
    // 保存最后一个STA的数据
    if (current_sta > 0 && memcmp(current_mac, "\0\0\0\0\0\0", MAC_ADDR_LEN) != 0) {
        memcpy(pMac[*total].mac_addr, current_mac, MAC_ADDR_LEN);
        pMac[*total].host_info.rxbytes = rx_bytes;
        pMac[*total].host_info.txbytes = tx_bytes;
        pMac[*total].host_info.rssi = rssi;
        (*total)++;
    }
    
    fclose(fp);
    
    WCPRT("Parsed %d wireless clients from %s\n", *total, filepath);
}

static void get_vap_interface(char ifName[][VAP_LEN], int max_vap_num)
{
    FILE *fp;
    char buffer[VAP_LEN];
    char command[] = "ip link show | grep -o 'default_radio[0-9]\\+'";
    //Bohannon for bug#00019733 guest vaps
    char command1[] = "ip link show | grep -o 'vap[0-9]\\+'";
    int vap_num = 0;

    fp = popen(command, "r");
    if (fp == NULL) {
        perror("popen failed");
        return ;
    }

    while (fgets(buffer, sizeof(buffer), fp) != NULL) {
        buffer[strcspn(buffer, "\n")] = '\0';
        strncpy(ifName[vap_num],buffer, sizeof(ifName[vap_num]));
        ++vap_num;
        if (vap_num >= max_vap_num)
        {
            break;
        }
        
    }

    pclose(fp);
    
    //Bohannon for bug#00019733 guest vaps
    fp = popen(command1, "r");
    if (fp == NULL) {
        perror("popen failed");
        return ;
    }

    while (fgets(buffer, sizeof(buffer), fp) != NULL) {
        buffer[strcspn(buffer, "\n")] = '\0';
        strncpy(ifName[vap_num],buffer, sizeof(ifName[vap_num]));
        ++vap_num;
        if (vap_num >= max_vap_num)
        {
            break;
        }
        
    }

    pclose(fp);
}

int pf_airoha_wlan_mac_table_get(pt_terminal_mgr pMac)
{ 
    int i;
    int total = 0;
    char ifName[VAP_NUMS][VAP_LEN] = {0};
    get_vap_interface(ifName, VAP_NUMS);
    // WCPRT("Get %s %s\n",ifName[0], ifName[1]);
    for (i = 0; i < VAP_NUMS; i++)
    {
        parse_wireless_client(ifName[i], pMac, &total);
    }

    return total;
}
