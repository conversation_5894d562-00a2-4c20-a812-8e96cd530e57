#!/bin/sh /etc/rc.common
# Copyright (C) 2006-2011 OpenWrt.org

# Modified by ZYB for startup earlier
# START=09
START=08
do_mount()
{
    mtd_num=$(cat /proc/mtd | grep $1 | awk '{print $1}' | tr -cd "[0-9]")
    mount -t squashfs,jffs2 /dev/mtdblock${mtd_num} $2
}

mknod_r()
{
    ret=`rm -f $1;mknod $@`

    while [ -n "$ret" ]
    do
        ret=`rm -f $1;mknod $@`
    done

    chmod 666 $1
}

dev_init() {
    usb2=$(hi_cfm get board.usb2_num)
    usb3=$(hi_cfm get board.usb3_num)

    if [ "${usb2:0:1}" != "0"  -o "${usb3:0:1}" != "0" ] ; then
        insmod /lib/hisilicon/ko/hi_kusb2phy.ko
        if [ -z "`lsmod |grep hi_kupsphy`" ]; then
            insmod /lib/hisilicon/ko/hi_kupsphy.ko
        fi
        insmod /lib/hisilicon/ko/hi_usb.ko

        if [ -f /lib/modules/`uname -r`/dwc3.ko ] ; then
            insmod /lib/modules/`uname -r`/udc-core.ko
            if [ -f /lib/modules/`uname -r`/extcon-core.ko ] ; then
                insmod /lib/modules/`uname -r`/extcon-core.ko
            fi
            insmod /lib/modules/`uname -r`/roles.ko
            insmod /lib/modules/`uname -r`/dwc3.ko p_dr_mode=1
        fi

        if [ -f /lib/modules/`uname -r`/ehci-platform.ko ] ; then
            insmod /lib/modules/`uname -r`/ehci-hcd.ko
            insmod /lib/modules/`uname -r`/ehci-platform.ko
        fi

        if [ -f /lib/modules/`uname -r`/ohci-platform.ko ] ; then
            insmod /lib/modules/`uname -r`/ohci-hcd.ko
            insmod /lib/modules/`uname -r`/ohci-platform.ko
        fi

        if [ -f /lib/modules/`uname -r`/xhci-plat-hcd.ko ] ; then
            insmod /lib/modules/`uname -r`/xhci-hcd.ko
            insmod /lib/modules/`uname -r`/xhci-plat-hcd.ko
        fi
    fi

}

histart() {
	echo "7 4 1 7" > /proc/sys/kernel/printk

	chip=`cat /sys/firmware/devicetree/base/compatible`
	chip=${chip#*,}
	[ -z $(echo $chip | grep ,) ] && chip=${chip#*-}
	[ -e /etc/rc.d/$chip ] && /etc/rc.d/$chip
	if [ ! grep -q "luofu" "/proc/device-tree/compatible"; then
		# switch adaptor for none luofu openwrt
		echo 1 > /sys/module/hi_kcfe_pub/parameters/g_intf_bypass
	fi
	insmod /lib/hisilicon/ko/hi_swa.ko
	
	if [ -f /lib/modules/`uname -r`/pstore_blk.ko ] ; then
		insmod /lib/modules/`uname -r`/pstore_zone.ko
		insmod /lib/modules/`uname -r`/pstore_blk.ko blkdev=pstore kmsg_size=64 max_reason=2
		insmod /lib/modules/`uname -r`/mtdpstore.ko
		mount -t pstore pstore /sys/fs/pstore
	fi
}

copy_firstgood_cfg()
{
	#copy default.tar.gz to /config/work/firstgood.tar.gz
    if [ -f "/mnt/framework/cfg/default.tar.gz" ]; then
        if [ ! -f "/config/work/firstgood.tar.gz" ]; then
    		cp /mnt/framework/cfg/default.tar.gz /config/work/firstgood.tar.gz
	    fi
    fi
}

start()
{
    histart

    echo "mount file system start"
    mkdir -p /var/run
    mkdir -p /var/log
    mkdir -p /var/spool/cron
    mkdir -p /dev/pts
    mkdir -p /tmp/mnt
    mkdir -p /tmp/login
    chmod 777 /tmp/login
    touch /tmp/login/login_status_log
    chmod 666 /tmp/login/login_status_log

    do_mount fac /usr/local/factory/
    do_mount cfga /config/work/
    do_mount cfgb /config/workb/
    do_mount log /log/

    mount -t devpts devpts /dev/pts

    echo "mount file system end"

    dev_init

    echo "permission adapt start"
    chmod 0771 /config/work*
    mknod_r /dev/null c 1 3
    mknod_r /dev/zero c 1 5
    mknod_r /dev/random c 1 8
    mknod_r /dev/urandom c 1 9
    echo "permission adapt end"
    
    copy_firstgood_cfg
}
