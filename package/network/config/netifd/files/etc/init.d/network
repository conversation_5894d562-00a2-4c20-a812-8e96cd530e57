#!/bin/sh /etc/rc.common

START=20
STOP=90

USE_PROCD=1

init_switch() {
	setup_switch() { return 0; }

	include /lib/network
	setup_switch
}

update_nptv6_config() {
	if [ -f "/tmp/nptv6/md5sum" ]; then
		pre_md5num=$(cat /tmp/nptv6/md5sum)
	else
		pre_md5num=0
	fi
	uci show network | grep nptv6 > /tmp/nptv6/config
	uci show network | grep ip6class >> /tmp/nptv6/config
	cur_md5num=$(md5sum /tmp/nptv6/config)

	if [ "$pre_md5num" != "$cur_md5num" ]; then
		echo $cur_md5num > /tmp/nptv6/md5sum
		/etc/init.d/firewall reload
	fi
}

create_nptv6_config() {
	if [ ! -d /tmp/nptv6/ ]; then
		mkdir /tmp/nptv6
	fi
	uci show network | grep nptv6 > /tmp/nptv6/config
	uci show network | grep ip6class >> /tmp/nptv6/config
	md5sum /tmp/nptv6/config > /tmp/nptv6/md5sum
}

update_route_config() {
	if [ -f "/tmp/route/md5sum" ]; then
		pre_md5num=$(cat /tmp/route/md5sum)
	else
		pre_md5num=0
	fi
	uci show network | grep route > /tmp/route/config
	uci show network | grep rule >> /tmp/route/config
	cur_md5num=$(md5sum /tmp/route/config)

	if [ "$pre_md5num" != "$cur_md5num" ]; then
		echo $cur_md5num > /tmp/route/md5sum
		CONNTRACK_FILE="/proc/net/nf_conntrack"
		echo f > ${CONNTRACK_FILE}
		echo 0 > /proc/hsan/cfe/lrn/lrn_flush
	fi
}

create_route_config() {
	if [ ! -d /tmp/route/ ]; then
		mkdir /tmp/route
	fi
	uci show network | grep route > /tmp/route/config
	uci show network | grep rule >> /tmp/route/config
	md5sum /tmp/route/config > /tmp/route/md5sum
}

setup_wan_port() {
	#check chip info
	if [ ! -f "/proc/device-tree/compatible" ] || ! grep -q "luofu" "/proc/device-tree/compatible"; then
		return 0
	fi
	
	# init all eth as lan
	for i in 0 1 2 3; do
		echo "eth${i} 0" > /proc/hsan/net/swa/wan_port
	done

	# get cfg_mode
	cfg_mode=$(uci get auto_wan.auto_wan.cfg_mode 2>/dev/null)

	case $cfg_mode in
		0|1)
			# static or agg wan port
			cfg_linkid=$(uci get auto_wan.auto_wan.cfg_linkid 2>/dev/null)
			if [ -n "$cfg_linkid" ] && [ "$cfg_linkid" -ge 1 ] && [ "$cfg_linkid" -le 4 ]; then
				eth_port=$((cfg_linkid - 1))
				echo "eth${eth_port} 1" > /proc/hsan/net/swa/wan_port
			fi
			;;
		2)
			# auto detect mode, compate detected_linkid and current_uplinkid
			detected_linkid=$(uci get auto_wan.auto_wan.detected_linkid 2>/dev/null)
			current_uplinkid=$(uci get auto_wan.auto_wan.current_uplinkid 2>/dev/null)
            
			if [ -n "$detected_linkid" ] && [ -n "$current_uplinkid" ] && \
				[ "$detected_linkid" -ge 1 ] && [ "$detected_linkid" -le 4 ] && \
				[ "$detected_linkid" = "$current_uplinkid" ]; then
				eth_port=$((detected_linkid - 1))
				echo "eth${eth_port} 1" > /proc/hsan/net/swa/wan_port
			fi
			;;
		*)
			echo "invalid cfg_mode: $cfg_mode"
			;;
	esac
}

start_service() {
	init_switch

	procd_open_instance
	setup_wan_port
	procd_set_param command /sbin/netifd
	procd_set_param respawn
	procd_set_param watch network.interface
	[ -e /proc/sys/kernel/core_pattern ] && {
		procd_set_param limits core="unlimited"
	}
	procd_close_instance
	# Deleted by ZYB for bad nat6 implementation
	# create_nptv6_config
}

reload_service() {
	local rv=0

	init_switch
	ubus call network reload || rv=1
	/sbin/wifi reload_legacy
	/usr/bin/wan_x_hotplug.sh up &
	setup_wan_port
	# Deleted by ZYB for bad nat6 implementation
	# update_nptv6_config
	return $rv
}

stop_service() {
	/sbin/wifi down
	ifdown -a
	sleep 1
}

validate_atm_bridge_section()
{
	uci_validate_section network "atm-bridge" "${1}" \
		'unit:uinteger:0' \
		'vci:range(32, 65535):35' \
		'vpi:range(0, 255):8' \
		'atmdev:uinteger:0' \
		'encaps:or("llc", "vc"):llc' \
		'payload:or("bridged", "routed"):bridged'
}

validate_route_section()
{
	uci_validate_section network route "${1}" \
		'interface:string' \
		'target:cidr4' \
		'netmask:netmask4' \
		'gateway:ip4addr' \
		'metric:uinteger' \
		'mtu:uinteger' \
		'table:or(range(0,65535),string)'
}

validate_route6_section()
{
	uci_validate_section network route6 "${1}" \
		'interface:string' \
		'target:cidr6' \
		'gateway:ip6addr' \
		'metric:uinteger' \
		'mtu:uinteger' \
		'table:or(range(0,65535),string)'
}

validate_rule_section()
{
	uci_validate_section network rule "${1}" \
		'in:string' \
		'out:string' \
		'src:cidr4' \
		'dest:cidr4' \
		'tos:range(0,31)' \
		'mark:string' \
		'invert:bool' \
		'lookup:or(range(0,65535),string)' \
		'goto:range(0,65535)' \
		'action:or("prohibit", "unreachable", "blackhole", "throw")'
}

validate_rule6_section()
{
	uci_validate_section network rule6 "${1}" \
		'in:string' \
		'out:string' \
		'src:cidr6' \
		'dest:cidr6' \
		'tos:range(0,31)' \
		'mark:string' \
		'invert:bool' \
		'lookup:or(range(0,65535),string)' \
		'goto:range(0,65535)' \
		'action:or("prohibit", "unreachable", "blackhole", "throw")'
}

validate_switch_section()
{
	uci_validate_section network switch "${1}" \
		'name:string' \
		'enable:bool' \
		'enable_vlan:bool' \
		'reset:bool' \
		'ar8xxx_mib_poll_interval:uinteger' \
		'ar8xxx_mib_type:range(0,1)'
}

validate_switch_vlan()
{
	uci_validate_section network switch_vlan "${1}" \
		'device:string' \
		'vlan:uinteger' \
		'ports:list(ports)'
}

service_triggers()
{
	procd_add_reload_trigger network

	procd_open_validate
	validate_atm_bridge_section
	validate_route_section
	[ -e /proc/sys/net/ipv6 ] && validate_route6_section
	validate_rule_section
	[ -e /proc/sys/net/ipv6 ] && validate_rule6_section
	validate_switch_section
	validate_switch_vlan
	procd_close_validate
}

shutdown() {
	ifdown -a
	sleep 1
}
