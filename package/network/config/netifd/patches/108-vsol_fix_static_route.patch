--- a/interface-ip.c
+++ b/interface-ip.c
@@ -95,6 +95,9 @@
 static struct device_prefix *ula_prefix = NULL;
 static struct uloop_timeout valid_until_timeout;
 
+//Add by for bug#
+static void
+interface_ip_set_route_enabled(struct interface_ip_settings *ip,struct device_route *route, bool enabled);
 
 static void
 clear_if_addr(union if_addr *a, int mask)
@@ -788,6 +791,7 @@
 	struct device *dev;
 	struct device_route *route_old, *route_new;
 	bool keep = false;
+    struct device_route *route;
 
 	dev = iface->l3_dev.dev;
 
@@ -819,6 +823,14 @@
 		route_new->iface = iface;
 		route_new->enabled = _enabled;
 	}
+
+    //Add by fyy for bug#19654
+    vlist_for_each_element(&iface->config_ip.route, route, node) {
+        if (route->failed) {
+            route->failed = false;
+            interface_ip_set_route_enabled(&iface->config_ip, route, true);
+        }
+    }
 }
 
 static void
@@ -1598,6 +1610,7 @@
 
 		if (system_add_route(dev, route))
 			route->failed = true;
+            return;
 	} else
 		system_del_route(dev, route);
 
